use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

// Core Canvas Models

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasProject {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub canvas_data: String, // JSON data for Fabric.js canvas
    pub thumbnail_url: Option<String>,
    pub width: i32,
    pub height: i32,
    pub background_color: String,
    pub version: i32,
    pub status: CanvasProjectStatus,
    
    // Metadata
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    pub tags: Vec<String>,
    pub category: String,
    
    // User and workspace info
    pub user_id: String,
    pub workspace_id: Option<String>,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_accessed_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum CanvasProjectStatus {
    Draft,
    Published,
    Archived,
}

impl Default for CanvasProjectStatus {
    fn default() -> Self {
        CanvasProjectStatus::Draft
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCanvasProjectRequest {
    pub name: String,
    pub description: Option<String>,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub background_color: Option<String>,
    pub template_id: Option<String>,
    pub category: Option<String>,
    pub tags: Option<Vec<String>>,
    pub workspace_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCanvasProjectRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub canvas_data: Option<String>,
    pub thumbnail_url: Option<String>,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub background_color: Option<String>,
    pub status: Option<CanvasProjectStatus>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    pub tags: Option<Vec<String>>,
    pub category: Option<String>,
}

// Canvas Objects

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasObject {
    pub id: String,
    pub canvas_project_id: String,
    pub object_id: String, // Fabric.js object ID
    pub object_type: String, // rectangle, circle, text, image, etc.
    pub object_data: String, // JSON data for the object
    pub layer_id: Option<String>,
    pub layer_order: i32,
    
    // Position and transform
    pub left_pos: f64,
    pub top_pos: f64,
    pub width: f64,
    pub height: f64,
    pub scale_x: f64,
    pub scale_y: f64,
    pub angle: f64,
    pub opacity: f64,
    
    // Visibility and interaction
    pub visible: bool,
    pub selectable: bool,
    pub locked: bool,
    
    // Metadata
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    pub created_by_tool: Option<String>,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCanvasObjectRequest {
    pub canvas_project_id: String,
    pub object_id: String,
    pub object_type: String,
    pub object_data: String,
    pub layer_id: Option<String>,
    pub layer_order: Option<i32>,
    pub left_pos: Option<f64>,
    pub top_pos: Option<f64>,
    pub width: Option<f64>,
    pub height: Option<f64>,
    pub scale_x: Option<f64>,
    pub scale_y: Option<f64>,
    pub angle: Option<f64>,
    pub opacity: Option<f64>,
    pub visible: Option<bool>,
    pub selectable: Option<bool>,
    pub locked: Option<bool>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    pub created_by_tool: Option<String>,
}

// Canvas Layers

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasLayer {
    pub id: String,
    pub canvas_project_id: String,
    pub layer_id: String, // Internal layer ID
    pub name: String,
    pub order_index: i32,
    pub visible: bool,
    pub locked: bool,
    pub opacity: f64,
    pub blend_mode: String,
    
    // Layer metadata
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCanvasLayerRequest {
    pub canvas_project_id: String,
    pub layer_id: String,
    pub name: String,
    pub order_index: Option<i32>,
    pub visible: Option<bool>,
    pub locked: Option<bool>,
    pub opacity: Option<f64>,
    pub blend_mode: Option<String>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCanvasLayerRequest {
    pub name: Option<String>,
    pub order_index: Option<i32>,
    pub visible: Option<bool>,
    pub locked: Option<bool>,
    pub opacity: Option<f64>,
    pub blend_mode: Option<String>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

// Canvas History

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasHistoryEntry {
    pub id: String,
    pub canvas_project_id: String,
    pub sequence_number: i32,
    pub action_type: CanvasActionType,
    pub action_data: String, // JSON data describing the action
    pub canvas_state_before: Option<String>, // Canvas state before action (for undo)
    pub canvas_state_after: Option<String>, // Canvas state after action (for redo)
    
    // Action metadata
    pub description: Option<String>,
    pub affected_objects: Vec<String>, // Array of object IDs
    
    // User info
    pub user_id: String,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum CanvasActionType {
    Add,
    Remove,
    Modify,
    Move,
    Resize,
    Rotate,
    Group,
    Ungroup,
    Layer,
    Style,
    Import,
    Export,
    Clear,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateHistoryEntryRequest {
    pub canvas_project_id: String,
    pub sequence_number: i32,
    pub action_type: CanvasActionType,
    pub action_data: String,
    pub canvas_state_before: Option<String>,
    pub canvas_state_after: Option<String>,
    pub description: Option<String>,
    pub affected_objects: Vec<String>,
    pub user_id: String,
}

// Canvas Templates

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasTemplate {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub canvas_data: String, // JSON data for template canvas
    pub thumbnail_url: Option<String>,
    pub category: String,
    pub tags: Vec<String>,
    
    // Template properties
    pub width: i32,
    pub height: i32,
    pub is_public: bool,
    pub is_featured: bool,
    pub usage_count: i32,
    
    // Metadata
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    
    // User info
    pub created_by_user_id: String,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCanvasTemplateRequest {
    pub name: String,
    pub description: Option<String>,
    pub canvas_data: String,
    pub thumbnail_url: Option<String>,
    pub category: Option<String>,
    pub tags: Option<Vec<String>>,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub is_public: Option<bool>,
    pub is_featured: Option<bool>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

// Canvas Collaborators

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasCollaborator {
    pub id: String,
    pub canvas_project_id: String,
    pub user_id: String,
    pub permission_level: CollaboratorPermission,
    
    // Collaboration metadata
    pub cursor_position: Option<CursorPosition>,
    pub active_tool: Option<String>,
    pub is_active: bool,
    pub last_activity_at: DateTime<Utc>,
    
    // Invitation info
    pub invited_by_user_id: Option<String>,
    pub invited_at: DateTime<Utc>,
    pub accepted_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum CollaboratorPermission {
    View,
    Edit,
    Admin,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CursorPosition {
    pub x: f64,
    pub y: f64,
    pub canvas_x: f64,
    pub canvas_y: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InviteCollaboratorRequest {
    pub canvas_project_id: String,
    pub user_id: String,
    pub permission_level: CollaboratorPermission,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCollaboratorRequest {
    pub permission_level: Option<CollaboratorPermission>,
    pub cursor_position: Option<CursorPosition>,
    pub active_tool: Option<String>,
    pub is_active: Option<bool>,
}

// Canvas Assets

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasAsset {
    pub id: String,
    pub canvas_project_id: Option<String>,
    pub asset_type: AssetType,
    pub original_name: String,
    pub file_name: String,
    pub file_path: String,
    pub file_size: Option<i64>,
    pub mime_type: Option<String>,
    
    // Image-specific properties
    pub width: Option<i32>,
    pub height: Option<i32>,
    
    // Usage tracking
    pub usage_count: i32,
    
    // Metadata
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    
    // User info
    pub uploaded_by_user_id: String,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum AssetType {
    Image,
    Font,
    Icon,
    Video,
    Audio,
    Document,
    Other,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadAssetRequest {
    pub canvas_project_id: Option<String>,
    pub asset_type: AssetType,
    pub original_name: String,
    pub file_data: Vec<u8>,
    pub mime_type: Option<String>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

// Canvas Comments

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasComment {
    pub id: String,
    pub canvas_project_id: String,
    pub user_id: String,
    pub content: String,
    
    // Position info (where comment is placed on canvas)
    pub position_x: Option<f64>,
    pub position_y: Option<f64>,
    
    // Thread info
    pub parent_comment_id: Option<String>,
    pub thread_id: Option<String>,
    
    // Status
    pub status: CommentStatus,
    pub resolved_by_user_id: Option<String>,
    pub resolved_at: Option<DateTime<Utc>>,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum CommentStatus {
    Open,
    Resolved,
    Archived,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCommentRequest {
    pub canvas_project_id: String,
    pub content: String,
    pub position_x: Option<f64>,
    pub position_y: Option<f64>,
    pub parent_comment_id: Option<String>,
    pub thread_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCommentRequest {
    pub content: Option<String>,
    pub position_x: Option<f64>,
    pub position_y: Option<f64>,
    pub status: Option<CommentStatus>,
}

// Canvas Exports

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CanvasExport {
    pub id: String,
    pub canvas_project_id: String,
    pub export_type: ExportType,
    pub file_name: String,
    pub file_path: String,
    pub file_size: Option<i64>,
    
    // Export settings
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub quality: Option<f64>,
    pub format_options: Option<HashMap<String, serde_json::Value>>,
    
    // User info
    pub exported_by_user_id: String,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>, // Optional expiration for temporary exports
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum ExportType {
    Png,
    Jpg,
    Svg,
    Pdf,
    Json,
    Gif,
    Webp,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateExportRequest {
    pub canvas_project_id: String,
    pub export_type: ExportType,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub quality: Option<f64>,
    pub format_options: Option<HashMap<String, serde_json::Value>>,
    pub expires_at: Option<DateTime<Utc>>,
}

// Response DTOs

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanvasProjectResponse {
    pub project: CanvasProject,
    pub layers: Vec<CanvasLayer>,
    pub objects: Vec<CanvasObject>,
    pub collaborators: Vec<CanvasCollaborator>,
    pub comments: Vec<CanvasComment>,
    pub assets: Vec<CanvasAsset>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanvasProjectListResponse {
    pub projects: Vec<CanvasProject>,
    pub total: i64,
    pub page: i32,
    pub per_page: i32,
    pub has_more: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanvasTemplateListResponse {
    pub templates: Vec<CanvasTemplate>,
    pub total: i64,
    pub page: i32,
    pub per_page: i32,
    pub has_more: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanvasStatistics {
    pub total_projects: i64,
    pub draft_projects: i64,
    pub published_projects: i64,
    pub archived_projects: i64,
    pub active_users: i64,
    pub total_objects: i64,
    pub total_collaborators: i64,
}

// Query parameters for listing

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanvasProjectQuery {
    pub page: Option<i32>,
    pub per_page: Option<i32>,
    pub status: Option<CanvasProjectStatus>,
    pub category: Option<String>,
    pub tags: Option<Vec<String>>,
    pub search: Option<String>,
    pub user_id: Option<String>,
    pub workspace_id: Option<String>,
    pub sort_by: Option<ProjectSortBy>,
    pub sort_order: Option<SortOrder>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanvasTemplateQuery {
    pub page: Option<i32>,
    pub per_page: Option<i32>,
    pub category: Option<String>,
    pub tags: Option<Vec<String>>,
    pub search: Option<String>,
    pub is_public: Option<bool>,
    pub is_featured: Option<bool>,
    pub sort_by: Option<TemplateSortBy>,
    pub sort_order: Option<SortOrder>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum ProjectSortBy {
    CreatedAt,
    UpdatedAt,
    LastAccessedAt,
    Name,
    Category,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum TemplateSortBy {
    CreatedAt,
    UpdatedAt,
    Name,
    UsageCount,
    Category,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum SortOrder {
    Asc,
    Desc,
}

// Error types

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanvasError {
    pub code: String,
    pub message: String,
    pub details: Option<HashMap<String, serde_json::Value>>,
}

impl std::fmt::Display for CanvasError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}: {}", self.code, self.message)
    }
}

impl std::error::Error for CanvasError {}

// Default implementations

impl Default for CreateCanvasProjectRequest {
    fn default() -> Self {
        Self {
            name: "Untitled Design".to_string(),
            description: None,
            width: Some(800),
            height: Some(600),
            background_color: Some("#ffffff".to_string()),
            template_id: None,
            category: Some("design".to_string()),
            tags: Some(vec![]),
            workspace_id: None,
        }
    }
}

impl Default for CanvasProjectQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
            status: None,
            category: None,
            tags: None,
            search: None,
            user_id: None,
            workspace_id: None,
            sort_by: Some(ProjectSortBy::UpdatedAt),
            sort_order: Some(SortOrder::Desc),
        }
    }
}

impl Default for CanvasTemplateQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
            category: None,
            tags: None,
            search: None,
            is_public: None,
            is_featured: None,
            sort_by: Some(TemplateSortBy::UpdatedAt),
            sort_order: Some(SortOrder::Desc),
        }
    }
}

// Utility functions

impl CanvasProject {
    pub fn is_collaborative(&self) -> bool {
        // Check if project has active collaborators (would need database query)
        true // Placeholder
    }
    
    pub fn can_edit(&self, user_id: &str) -> bool {
        // Check if user can edit (owner or has edit permissions)
        self.user_id == user_id // Simplified check
    }
    
    pub fn get_object_count(&self) -> usize {
        // Parse canvas_data to count objects (simplified)
        serde_json::from_str::<serde_json::Value>(&self.canvas_data)
            .ok()
            .and_then(|v| v.get("objects"))
            .and_then(|v| v.as_array())
            .map(|v| v.len())
            .unwrap_or(0)
    }
}

impl CanvasTemplate {
    pub fn increment_usage(&mut self) {
        self.usage_count += 1;
    }
    
    pub fn is_accessible_by(&self, user_id: &str) -> bool {
        self.is_public || self.created_by_user_id == user_id
    }
}

impl CanvasCollaborator {
    pub fn can_edit(&self) -> bool {
        matches!(self.permission_level, CollaboratorPermission::Edit | CollaboratorPermission::Admin)
    }
    
    pub fn is_admin(&self) -> bool {
        matches!(self.permission_level, CollaboratorPermission::Admin)
    }
}

// Fabric.js specific types for canvas data

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FabricCanvasData {
    pub version: String,
    pub objects: Vec<FabricObject>,
    pub background: String,
    pub backgroundImage: Option<FabricObject>,
    pub overlayImage: Option<FabricObject>,
    pub clipPath: Option<FabricObject>,
    pub width: Option<i32>,
    pub height: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FabricObject {
    #[serde(rename = "type")]
    pub object_type: String,
    pub left: Option<f64>,
    pub top: Option<f64>,
    pub width: Option<f64>,
    pub height: Option<f64>,
    #[serde(rename = "scaleX")]
    pub scale_x: Option<f64>,
    #[serde(rename = "scaleY")]
    pub scale_y: Option<f64>,
    pub angle: Option<f64>,
    pub opacity: Option<f64>,
    pub visible: Option<bool>,
    pub selectable: Option<bool>,
    pub evented: Option<bool>,
    pub id: Option<String>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    
    // Shape-specific properties
    pub fill: Option<String>,
    pub stroke: Option<String>,
    #[serde(rename = "strokeWidth")]
    pub stroke_width: Option<f64>,
    
    // Text-specific properties
    pub text: Option<String>,
    #[serde(rename = "fontFamily")]
    pub font_family: Option<String>,
    #[serde(rename = "fontSize")]
    pub font_size: Option<f64>,
    
    // Image-specific properties
    pub src: Option<String>,
    #[serde(rename = "crossOrigin")]
    pub cross_origin: Option<String>,
    
    // Path-specific properties
    pub path: Option<Vec<Vec<f64>>>,
    
    // Additional properties
    #[serde(flatten)]
    pub additional_properties: HashMap<String, serde_json::Value>,
}

impl Default for FabricCanvasData {
    fn default() -> Self {
        Self {
            version: "5.3.0".to_string(),
            objects: vec![],
            background: "#ffffff".to_string(),
            backgroundImage: None,
            overlayImage: None,
            clipPath: None,
            width: Some(800),
            height: Some(600),
        }
    }
}