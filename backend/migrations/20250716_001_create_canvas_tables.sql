-- Canvas Tables for Visual Design Studio
-- Migration: 20250716_001_create_canvas_tables.sql
-- Description: Database schema for canvas data storage and management

-- Canvas Projects table
CREATE TABLE IF NOT EXISTS canvas_projects (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    name TEXT NOT NULL,
    description TEXT,
    canvas_data TEXT NOT NULL, -- JSON data for Fabric.js canvas
    thumbnail_url TEXT,
    width INTEGER NOT NULL DEFAULT 800,
    height INTEGER NOT NULL DEFAULT 600,
    background_color TEXT DEFAULT '#ffffff',
    version INTEGER NOT NULL DEFAULT 1,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    
    -- Metadata
    metadata TEXT, -- JSON metadata for additional properties
    tags TEXT, -- JSON array of tags
    category TEXT DEFAULT 'design',
    
    -- User and workspace info
    user_id TEXT NOT NULL,
    workspace_id TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys (assuming these tables exist)
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Canvas Objects table (for individual objects within a canvas)
CREATE TABLE IF NOT EXISTS canvas_objects (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    canvas_project_id TEXT NOT NULL,
    object_id TEXT NOT NULL, -- Fabric.js object ID
    object_type TEXT NOT NULL, -- rectangle, circle, text, image, etc.
    object_data TEXT NOT NULL, -- JSON data for the object
    layer_id TEXT,
    layer_order INTEGER DEFAULT 0,
    
    -- Position and transform
    left_pos REAL DEFAULT 0,
    top_pos REAL DEFAULT 0,
    width REAL DEFAULT 0,
    height REAL DEFAULT 0,
    scale_x REAL DEFAULT 1,
    scale_y REAL DEFAULT 1,
    angle REAL DEFAULT 0,
    opacity REAL DEFAULT 1,
    
    -- Visibility and interaction
    visible BOOLEAN DEFAULT true,
    selectable BOOLEAN DEFAULT true,
    locked BOOLEAN DEFAULT false,
    
    -- Metadata
    metadata TEXT, -- JSON metadata
    created_by_tool TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (canvas_project_id) REFERENCES canvas_projects(id) ON DELETE CASCADE
);

-- Canvas Layers table
CREATE TABLE IF NOT EXISTS canvas_layers (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    canvas_project_id TEXT NOT NULL,
    layer_id TEXT NOT NULL, -- Internal layer ID
    name TEXT NOT NULL,
    order_index INTEGER NOT NULL DEFAULT 0,
    visible BOOLEAN DEFAULT true,
    locked BOOLEAN DEFAULT false,
    opacity REAL DEFAULT 1,
    blend_mode TEXT DEFAULT 'normal',
    
    -- Layer metadata
    metadata TEXT, -- JSON metadata
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (canvas_project_id) REFERENCES canvas_projects(id) ON DELETE CASCADE,
    UNIQUE(canvas_project_id, layer_id)
);

-- Canvas History table (for undo/redo functionality)
CREATE TABLE IF NOT EXISTS canvas_history (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    canvas_project_id TEXT NOT NULL,
    sequence_number INTEGER NOT NULL,
    action_type TEXT NOT NULL, -- add, remove, modify, group, ungroup, etc.
    action_data TEXT NOT NULL, -- JSON data describing the action
    canvas_state_before TEXT, -- Canvas state before action (for undo)
    canvas_state_after TEXT, -- Canvas state after action (for redo)
    
    -- Action metadata
    description TEXT,
    affected_objects TEXT, -- JSON array of object IDs
    
    -- User info
    user_id TEXT NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (canvas_project_id) REFERENCES canvas_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Canvas Templates table (reusable canvas designs)
CREATE TABLE IF NOT EXISTS canvas_templates (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    name TEXT NOT NULL,
    description TEXT,
    canvas_data TEXT NOT NULL, -- JSON data for template canvas
    thumbnail_url TEXT,
    category TEXT DEFAULT 'general',
    tags TEXT, -- JSON array of tags
    
    -- Template properties
    width INTEGER NOT NULL DEFAULT 800,
    height INTEGER NOT NULL DEFAULT 600,
    is_public BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0,
    
    -- Metadata
    metadata TEXT, -- JSON metadata
    
    -- User info
    created_by_user_id TEXT NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Canvas Collaborators table (for real-time collaboration)
CREATE TABLE IF NOT EXISTS canvas_collaborators (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    canvas_project_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    permission_level TEXT NOT NULL DEFAULT 'view' CHECK (permission_level IN ('view', 'edit', 'admin')),
    
    -- Collaboration metadata
    cursor_position TEXT, -- JSON data for cursor position
    active_tool TEXT,
    is_active BOOLEAN DEFAULT false,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Invitation info
    invited_by_user_id TEXT,
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    accepted_at TIMESTAMP,
    
    FOREIGN KEY (canvas_project_id) REFERENCES canvas_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(canvas_project_id, user_id)
);

-- Canvas Assets table (images, fonts, etc. used in canvas)
CREATE TABLE IF NOT EXISTS canvas_assets (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    canvas_project_id TEXT,
    asset_type TEXT NOT NULL, -- image, font, icon, etc.
    original_name TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    
    -- Image-specific properties
    width INTEGER,
    height INTEGER,
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    
    -- Metadata
    metadata TEXT, -- JSON metadata
    
    -- User info
    uploaded_by_user_id TEXT NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (canvas_project_id) REFERENCES canvas_projects(id) ON DELETE SET NULL,
    FOREIGN KEY (uploaded_by_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Canvas Comments table (for design feedback)
CREATE TABLE IF NOT EXISTS canvas_comments (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    canvas_project_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    content TEXT NOT NULL,
    
    -- Position info (where comment is placed on canvas)
    position_x REAL,
    position_y REAL,
    
    -- Thread info
    parent_comment_id TEXT,
    thread_id TEXT,
    
    -- Status
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'resolved', 'archived')),
    resolved_by_user_id TEXT,
    resolved_at TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (canvas_project_id) REFERENCES canvas_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_comment_id) REFERENCES canvas_comments(id) ON DELETE CASCADE,
    FOREIGN KEY (resolved_by_user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Canvas Exports table (track exported versions)
CREATE TABLE IF NOT EXISTS canvas_exports (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    canvas_project_id TEXT NOT NULL,
    export_type TEXT NOT NULL, -- png, jpg, svg, pdf, json, etc.
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    
    -- Export settings
    width INTEGER,
    height INTEGER,
    quality REAL,
    format_options TEXT, -- JSON data for format-specific options
    
    -- User info
    exported_by_user_id TEXT NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP, -- Optional expiration for temporary exports
    
    FOREIGN KEY (canvas_project_id) REFERENCES canvas_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (exported_by_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Indexes for performance optimization

-- Canvas Projects indexes
CREATE INDEX IF NOT EXISTS idx_canvas_projects_user_id ON canvas_projects(user_id);
CREATE INDEX IF NOT EXISTS idx_canvas_projects_workspace_id ON canvas_projects(workspace_id);
CREATE INDEX IF NOT EXISTS idx_canvas_projects_status ON canvas_projects(status);
CREATE INDEX IF NOT EXISTS idx_canvas_projects_created_at ON canvas_projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_canvas_projects_updated_at ON canvas_projects(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_canvas_projects_category ON canvas_projects(category);

-- Canvas Objects indexes
CREATE INDEX IF NOT EXISTS idx_canvas_objects_project_id ON canvas_objects(canvas_project_id);
CREATE INDEX IF NOT EXISTS idx_canvas_objects_object_id ON canvas_objects(object_id);
CREATE INDEX IF NOT EXISTS idx_canvas_objects_type ON canvas_objects(object_type);
CREATE INDEX IF NOT EXISTS idx_canvas_objects_layer ON canvas_objects(layer_id, layer_order);
CREATE INDEX IF NOT EXISTS idx_canvas_objects_position ON canvas_objects(left_pos, top_pos);
CREATE INDEX IF NOT EXISTS idx_canvas_objects_visible ON canvas_objects(visible);

-- Canvas Layers indexes
CREATE INDEX IF NOT EXISTS idx_canvas_layers_project_id ON canvas_layers(canvas_project_id);
CREATE INDEX IF NOT EXISTS idx_canvas_layers_order ON canvas_layers(canvas_project_id, order_index);
CREATE INDEX IF NOT EXISTS idx_canvas_layers_visible ON canvas_layers(visible);

-- Canvas History indexes
CREATE INDEX IF NOT EXISTS idx_canvas_history_project_id ON canvas_history(canvas_project_id);
CREATE INDEX IF NOT EXISTS idx_canvas_history_sequence ON canvas_history(canvas_project_id, sequence_number);
CREATE INDEX IF NOT EXISTS idx_canvas_history_user_id ON canvas_history(user_id);
CREATE INDEX IF NOT EXISTS idx_canvas_history_created_at ON canvas_history(created_at DESC);

-- Canvas Templates indexes
CREATE INDEX IF NOT EXISTS idx_canvas_templates_category ON canvas_templates(category);
CREATE INDEX IF NOT EXISTS idx_canvas_templates_public ON canvas_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_canvas_templates_featured ON canvas_templates(is_featured);
CREATE INDEX IF NOT EXISTS idx_canvas_templates_usage ON canvas_templates(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_canvas_templates_created_by ON canvas_templates(created_by_user_id);

-- Canvas Collaborators indexes
CREATE INDEX IF NOT EXISTS idx_canvas_collaborators_project_id ON canvas_collaborators(canvas_project_id);
CREATE INDEX IF NOT EXISTS idx_canvas_collaborators_user_id ON canvas_collaborators(user_id);
CREATE INDEX IF NOT EXISTS idx_canvas_collaborators_active ON canvas_collaborators(is_active);
CREATE INDEX IF NOT EXISTS idx_canvas_collaborators_activity ON canvas_collaborators(last_activity_at DESC);

-- Canvas Assets indexes
CREATE INDEX IF NOT EXISTS idx_canvas_assets_project_id ON canvas_assets(canvas_project_id);
CREATE INDEX IF NOT EXISTS idx_canvas_assets_type ON canvas_assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_canvas_assets_user_id ON canvas_assets(uploaded_by_user_id);
CREATE INDEX IF NOT EXISTS idx_canvas_assets_usage ON canvas_assets(usage_count DESC);

-- Canvas Comments indexes
CREATE INDEX IF NOT EXISTS idx_canvas_comments_project_id ON canvas_comments(canvas_project_id);
CREATE INDEX IF NOT EXISTS idx_canvas_comments_user_id ON canvas_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_canvas_comments_parent ON canvas_comments(parent_comment_id);
CREATE INDEX IF NOT EXISTS idx_canvas_comments_thread ON canvas_comments(thread_id);
CREATE INDEX IF NOT EXISTS idx_canvas_comments_status ON canvas_comments(status);
CREATE INDEX IF NOT EXISTS idx_canvas_comments_position ON canvas_comments(position_x, position_y);

-- Canvas Exports indexes
CREATE INDEX IF NOT EXISTS idx_canvas_exports_project_id ON canvas_exports(canvas_project_id);
CREATE INDEX IF NOT EXISTS idx_canvas_exports_type ON canvas_exports(export_type);
CREATE INDEX IF NOT EXISTS idx_canvas_exports_user_id ON canvas_exports(exported_by_user_id);
CREATE INDEX IF NOT EXISTS idx_canvas_exports_created_at ON canvas_exports(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_canvas_exports_expires_at ON canvas_exports(expires_at);

-- Triggers for automatically updating timestamps

-- Canvas Projects update trigger
CREATE TRIGGER IF NOT EXISTS update_canvas_projects_updated_at
    AFTER UPDATE ON canvas_projects
    FOR EACH ROW
BEGIN
    UPDATE canvas_projects 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- Canvas Objects update trigger
CREATE TRIGGER IF NOT EXISTS update_canvas_objects_updated_at
    AFTER UPDATE ON canvas_objects
    FOR EACH ROW
BEGIN
    UPDATE canvas_objects 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- Canvas Layers update trigger
CREATE TRIGGER IF NOT EXISTS update_canvas_layers_updated_at
    AFTER UPDATE ON canvas_layers
    FOR EACH ROW
BEGIN
    UPDATE canvas_layers 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- Canvas Templates update trigger
CREATE TRIGGER IF NOT EXISTS update_canvas_templates_updated_at
    AFTER UPDATE ON canvas_templates
    FOR EACH ROW
BEGIN
    UPDATE canvas_templates 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- Canvas Assets update trigger
CREATE TRIGGER IF NOT EXISTS update_canvas_assets_updated_at
    AFTER UPDATE ON canvas_assets
    FOR EACH ROW
BEGIN
    UPDATE canvas_assets 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- Canvas Comments update trigger
CREATE TRIGGER IF NOT EXISTS update_canvas_comments_updated_at
    AFTER UPDATE ON canvas_comments
    FOR EACH ROW
BEGIN
    UPDATE canvas_comments 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- Views for common queries

-- Active canvas projects view
CREATE VIEW IF NOT EXISTS active_canvas_projects AS
SELECT 
    cp.*,
    COUNT(DISTINCT co.id) as object_count,
    COUNT(DISTINCT cl.id) as layer_count,
    COUNT(DISTINCT cc.id) as collaborator_count
FROM canvas_projects cp
LEFT JOIN canvas_objects co ON cp.id = co.canvas_project_id
LEFT JOIN canvas_layers cl ON cp.id = cl.canvas_project_id
LEFT JOIN canvas_collaborators cc ON cp.id = cc.canvas_project_id AND cc.is_active = true
WHERE cp.status = 'draft' OR cp.status = 'published'
GROUP BY cp.id;

-- Recent canvas activity view
CREATE VIEW IF NOT EXISTS recent_canvas_activity AS
SELECT 
    cp.id as project_id,
    cp.name as project_name,
    cp.thumbnail_url,
    cp.updated_at as last_modified,
    u.name as last_modified_by,
    COUNT(DISTINCT cc.id) as active_collaborators
FROM canvas_projects cp
LEFT JOIN users u ON cp.user_id = u.id
LEFT JOIN canvas_collaborators cc ON cp.id = cc.canvas_project_id AND cc.is_active = true
WHERE cp.status != 'archived'
GROUP BY cp.id
ORDER BY cp.updated_at DESC;

-- Canvas statistics view
CREATE VIEW IF NOT EXISTS canvas_statistics AS
SELECT 
    COUNT(*) as total_projects,
    COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_projects,
    COUNT(CASE WHEN status = 'published' THEN 1 END) as published_projects,
    COUNT(CASE WHEN status = 'archived' THEN 1 END) as archived_projects,
    AVG(CAST(substr(canvas_data, 1, 100) AS INTEGER)) as avg_complexity, -- Simplified complexity metric
    COUNT(DISTINCT user_id) as active_users
FROM canvas_projects
WHERE created_at >= datetime('now', '-30 days');

-- Initial data for development (optional)

-- Insert default canvas template
INSERT OR IGNORE INTO canvas_templates (
    id,
    name,
    description,
    canvas_data,
    category,
    tags,
    width,
    height,
    is_public,
    is_featured,
    created_by_user_id
) VALUES (
    'default-blank-template',
    'Blank Canvas',
    'A blank canvas template for starting new designs',
    '{"version":"5.3.0","objects":[],"background":"#ffffff"}',
    'basic',
    '["blank", "starter", "basic"]',
    800,
    600,
    true,
    true,
    'system'
);

-- Performance optimization notes:
-- 1. canvas_data is stored as TEXT (JSON) for flexibility but consider BLOB for large canvases
-- 2. Consider partitioning canvas_history by date for very active systems
-- 3. Implement canvas_data compression for storage efficiency
-- 4. Use connection pooling for high concurrent access
-- 5. Consider read replicas for canvas template queries
-- 6. Implement caching layer (Redis) for frequently accessed canvas data