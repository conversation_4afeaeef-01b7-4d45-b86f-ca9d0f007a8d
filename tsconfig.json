{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@unified-assistant/types": ["./packages/types/src"], "@unified-assistant/types/*": ["./packages/types/src/*"], "@unified-assistant/ai": ["./packages/ai/src"], "@unified-assistant/ai/*": ["./packages/ai/src/*"], "@unified-assistant/events": ["./packages/events/src"], "@unified-assistant/events/*": ["./packages/events/src/*"], "@unified-assistant/ui": ["./packages/ui/src"], "@unified-assistant/ui/*": ["./packages/ui/src/*"], "@unified-assistant/foundation": ["./packages/foundation/src"], "@unified-assistant/foundation/*": ["./packages/foundation/src/*"], "@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "packages/*/src/**/*", "examples/**/*"], "exclude": ["node_modules", "**/node_modules", "**/dist", "**/.next"], "references": [{"path": "./packages/types"}, {"path": "./packages/ai"}, {"path": "./packages/events"}, {"path": "./packages/ui"}, {"path": "./packages/foundation"}]}