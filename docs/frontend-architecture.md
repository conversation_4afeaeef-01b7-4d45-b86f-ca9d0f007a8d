# Frontend Architecture - Unified AI Assistant Platform

## Design Philosophy

### Langflow-Inspired Visual Language
The frontend architecture adopts Langflow's clean, modern, and professional visual design language as the foundation for the unified AI assistant platform. Key visual principles include:

- **Clean Minimalism**: Uncluttered interfaces that focus on functionality
- **Visual Workflow Paradigm**: Drag-and-drop interactions as primary design pattern
- **Professional Aesthetics**: Enterprise-ready visual design with consumer-grade usability
- **Consistent Component Language**: Unified design system across all 8 modules

### Core Design Principles
1. **Workflow-First Design**: Every interface element supports visual workflow creation
2. **Progressive Disclosure**: Complex features revealed gradually as users need them
3. **Contextual Intelligence**: AI-powered interface that adapts to user behavior
4. **Cross-Module Consistency**: Seamless experience across all integrated modules
5. **Performance by Design**: 60 FPS interactions with <2s initial load times

## Design System Architecture

### Visual Design Language

```typescript
// Langflow-inspired design tokens
const designTokens = {
  colors: {
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      500: '#0ea5e9', // Langflow blue
      600: '#0284c7',
      700: '#0369a1',
      900: '#0c4a6e'
    },
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      500: '#737373',
      700: '#404040',
      900: '#171717'
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444'
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Menlo', 'monospace']
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem'
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem'
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem'
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
  }
}
```

### Component Architecture

```typescript
// Base component interface following Langflow patterns
interface BaseComponent {
  id: string
  variant?: 'default' | 'ghost' | 'outline' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  className?: string
}

// Workflow node component (core pattern)
interface WorkflowNode extends BaseComponent {
  type: 'input' | 'output' | 'processor' | 'ai-agent'
  position: { x: number; y: number }
  connections: Connection[]
  data: Record<string, any>
  
  // Visual properties
  icon?: React.ComponentType
  color?: string
  status?: 'idle' | 'running' | 'success' | 'error'
}

// Module container component
interface ModuleContainer extends BaseComponent {
  moduleId: string
  title: string
  description?: string
  actions?: Action[]
  
  // Layout properties
  layout?: 'full' | 'sidebar' | 'modal' | 'panel'
  resizable?: boolean
  collapsible?: boolean
}
```

### UI Component Library

```typescript
// Core UI components based on Radix UI + Tailwind CSS 4
export const UIComponents = {
  // Layout components
  Container: styled.div`
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  `,
  
  Grid: styled.div<{ cols?: number }>`
    @apply grid gap-6;
    grid-template-columns: repeat(${props => props.cols || 1}, minmax(0, 1fr));
  `,
  
  // Navigation components
  Sidebar: styled.aside`
    @apply w-64 bg-white border-r border-gray-200 h-full overflow-y-auto;
  `,
  
  TopBar: styled.header`
    @apply bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between;
  `,
  
  // Workflow components
  WorkflowCanvas: styled.div`
    @apply relative bg-gray-50 overflow-hidden;
    background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
    background-size: 20px 20px;
  `,
  
  WorkflowNode: styled.div<{ status?: string }>`
    @apply absolute bg-white rounded-lg border shadow-sm p-4 cursor-move;
    border-color: ${props => 
      props.status === 'running' ? '#3b82f6' :
      props.status === 'success' ? '#10b981' :
      props.status === 'error' ? '#ef4444' : '#d1d5db'
    };
  `,
  
  // Form components
  Input: styled.input`
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500;
  `,
  
  Button: styled.button<{ variant?: string; size?: string }>`
    @apply inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
    
    ${props => props.variant === 'primary' && `
      @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
    `}
    
    ${props => props.variant === 'ghost' && `
      @apply text-gray-600 hover:bg-gray-100 focus:ring-gray-500;
    `}
    
    ${props => props.size === 'sm' && `
      @apply px-3 py-2 text-sm;
    `}
    
    ${props => props.size === 'lg' && `
      @apply px-6 py-3 text-lg;
    `}
  `
}
```

## Module UI Integration

### Unified Module Interface

```typescript
// Standard module interface that all 8 modules implement
interface ModuleInterface {
  id: string
  name: string
  icon: React.ComponentType
  
  // UI integration
  renderSidebar(): React.ReactNode
  renderMain(): React.ReactNode
  renderToolbar(): React.ReactNode
  
  // Workflow integration
  getWorkflowComponents(): WorkflowComponent[]
  
  // State integration
  getInitialState(): any
  
  // Navigation integration
  getRoutes(): Route[]
}

// Module registry for dynamic loading
class ModuleRegistry {
  private modules = new Map<string, ModuleInterface>()
  
  register(module: ModuleInterface) {
    this.modules.set(module.id, module)
  }
  
  getModule(id: string): ModuleInterface | undefined {
    return this.modules.get(id)
  }
  
  getAllModules(): ModuleInterface[] {
    return Array.from(this.modules.values())
  }
}
```

### Module-Specific UI Adaptations

```typescript
// Agent Inbox - Chat-focused interface
const AgentInboxUI = {
  layout: 'chat',
  components: {
    ConversationList: ChatConversationList,
    MessageThread: ChatMessageThread,
    InputArea: ChatInputArea
  },
  workflowNodes: ['chat-input', 'ai-response', 'human-review']
}

// Open Canvas - Document editing interface
const OpenCanvasUI = {
  layout: 'editor',
  components: {
    DocumentTree: DocumentNavigator,
    Editor: RichTextEditor,
    Collaboration: CollaborationPanel
  },
  workflowNodes: ['document-input', 'ai-edit', 'collaboration']
}

// FotoFun - Canvas-based design interface
const FotoFunUI = {
  layout: 'canvas',
  components: {
    ToolPalette: DesignToolPalette,
    Canvas: FabricCanvas,
    LayerPanel: LayerManager
  },
  workflowNodes: ['image-input', 'ai-edit', 'filter', 'export']
}

// Vibe Kanban - Project management interface
const VibeKanbanUI = {
  layout: 'kanban',
  components: {
    KanbanBoard: TaskKanbanBoard,
    AgentPanel: AgentStatusPanel,
    Analytics: ProjectAnalytics
  },
  workflowNodes: ['task-create', 'agent-assign', 'status-update']
}
```

## Visual Workflow Interface

### Drag-and-Drop System

```typescript
// React DnD implementation for workflow builder
import { DndProvider, useDrag, useDrop } from 'react-dnd'

interface DraggableNode {
  id: string
  type: string
  data: any
}

const WorkflowNode: React.FC<{ node: DraggableNode }> = ({ node }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'workflow-node',
    item: node,
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  })
  
  const [{ isOver }, drop] = useDrop({
    accept: 'workflow-node',
    drop: (item: DraggableNode, monitor) => {
      handleNodeDrop(item, node, monitor.getDropResult())
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  })
  
  return (
    <div 
      ref={(node) => drag(drop(node))}
      className={`workflow-node ${isDragging ? 'dragging' : ''} ${isOver ? 'drop-target' : ''}`}
    >
      {/* Node content */}
    </div>
  )
}
```

### Connection System

```typescript
// Visual connection system between workflow nodes
interface Connection {
  id: string
  sourceId: string
  targetId: string
  sourceHandle: string
  targetHandle: string
  type: 'data' | 'control' | 'error'
}

const ConnectionRenderer: React.FC<{ connections: Connection[] }> = ({ connections }) => {
  return (
    <svg className="absolute inset-0 pointer-events-none">
      {connections.map(connection => (
        <path
          key={connection.id}
          d={generateConnectionPath(connection)}
          stroke={getConnectionColor(connection.type)}
          strokeWidth={2}
          fill="none"
          markerEnd="url(#arrowhead)"
        />
      ))}
    </svg>
  )
}
```

## Performance Optimization

### Code Splitting & Lazy Loading

```typescript
// Module-based code splitting
const LazyModules = {
  Chat: lazy(() => import('@modules/chat/ChatModule')),
  Content: lazy(() => import('@modules/content/ContentModule')),
  Code: lazy(() => import('@modules/code/CodeModule')),
  Design: lazy(() => import('@modules/design/DesignModule')),
  Automation: lazy(() => import('@modules/automation/AutomationModule')),
  Orchestration: lazy(() => import('@modules/orchestration/OrchestrationModule')),
  Workflows: lazy(() => import('@modules/workflows/WorkflowModule')),
  Social: lazy(() => import('@modules/social/SocialModule'))
}

// Dynamic module loading
const ModuleLoader: React.FC<{ moduleId: string }> = ({ moduleId }) => {
  const ModuleComponent = LazyModules[moduleId]
  
  return (
    <Suspense fallback={<ModuleLoadingSpinner />}>
      <ModuleComponent />
    </Suspense>
  )
}
```

### Animation Performance

```typescript
// Optimized animations using Framer Motion
const AnimationConfig = {
  // Smooth page transitions
  pageTransition: {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.3
  },
  
  // Workflow node animations
  nodeAnimation: {
    initial: { scale: 0, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0, opacity: 0 },
    transition: { type: 'spring', stiffness: 300, damping: 25 }
  },
  
  // Loading states
  loadingAnimation: {
    animate: { rotate: 360 },
    transition: { duration: 1, repeat: Infinity, ease: 'linear' }
  }
}
```

## Responsive Design Strategy

### Breakpoint System

```typescript
// Tailwind CSS 4 responsive breakpoints
const breakpoints = {
  sm: '640px',   // Mobile landscape
  md: '768px',   // Tablet portrait
  lg: '1024px',  // Tablet landscape / Desktop
  xl: '1280px',  // Desktop
  '2xl': '1536px' // Large desktop
}

// Responsive component behavior
const ResponsiveLayout: React.FC = () => {
  const { width } = useWindowSize()
  
  const isMobile = width < 768
  const isTablet = width >= 768 && width < 1024
  const isDesktop = width >= 1024
  
  return (
    <div className="app-layout">
      {isDesktop && <Sidebar />}
      <main className={`main-content ${isMobile ? 'mobile' : ''}`}>
        {isMobile && <MobileNavigation />}
        <ModuleRenderer />
      </main>
      {isTablet && <CollapsibleSidebar />}
    </div>
  )
}
```

## Theme System

### Dark/Light Mode Implementation

```typescript
// Theme provider with system preference detection
const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system')
  
  const resolvedTheme = useMemo(() => {
    if (theme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return theme
  }, [theme])
  
  useEffect(() => {
    document.documentElement.classList.toggle('dark', resolvedTheme === 'dark')
  }, [resolvedTheme])
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme, resolvedTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}
```

## Cross-Module Navigation

### Unified Navigation System

```typescript
// Navigation configuration for all 8 modules
const navigationConfig = [
  {
    id: 'chat',
    name: 'Conversations',
    icon: MessageSquare,
    path: '/chat',
    shortcut: 'cmd+1'
  },
  {
    id: 'content',
    name: 'Documents',
    icon: FileText,
    path: '/content',
    shortcut: 'cmd+2'
  },
  {
    id: 'code',
    name: 'Development',
    icon: Code,
    path: '/code',
    shortcut: 'cmd+3'
  },
  {
    id: 'design',
    name: 'Design Studio',
    icon: Palette,
    path: '/design',
    shortcut: 'cmd+4'
  },
  {
    id: 'automation',
    name: 'Automation',
    icon: Zap,
    path: '/automation',
    shortcut: 'cmd+5'
  },
  {
    id: 'orchestration',
    name: 'Projects',
    icon: Kanban,
    path: '/projects',
    shortcut: 'cmd+6'
  },
  {
    id: 'workflows',
    name: 'Workflows',
    icon: GitBranch,
    path: '/workflows',
    shortcut: 'cmd+7'
  },
  {
    id: 'social',
    name: 'Social Media',
    icon: Share,
    path: '/social',
    shortcut: 'cmd+8'
  }
]

// Command palette for quick navigation
const CommandPalette: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState('')
  
  const filteredCommands = useMemo(() => {
    return navigationConfig.filter(item =>
      item.name.toLowerCase().includes(query.toLowerCase())
    )
  }, [query])
  
  return (
    <Dialog open={isOpen} onClose={() => setIsOpen(false)}>
      <CommandInput 
        placeholder="Search modules, commands, or content..."
        value={query}
        onChange={setQuery}
      />
      <CommandList>
        {filteredCommands.map(command => (
          <CommandItem 
            key={command.id}
            onSelect={() => navigate(command.path)}
          >
            <command.icon className="w-4 h-4 mr-2" />
            {command.name}
            <kbd className="ml-auto">{command.shortcut}</kbd>
          </CommandItem>
        ))}
      </CommandList>
    </Dialog>
  )
}
```

## Accessibility Architecture

### WCAG 2.1 AA Compliance

```typescript
// Accessibility utilities and components
const AccessibilityProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [announcements, setAnnouncements] = useState<string[]>([])
  
  const announce = useCallback((message: string) => {
    setAnnouncements(prev => [...prev, message])
    setTimeout(() => {
      setAnnouncements(prev => prev.slice(1))
    }, 1000)
  }, [])
  
  return (
    <AccessibilityContext.Provider value={{ announce }}>
      {children}
      <LiveRegion aria-live="polite" aria-atomic="true">
        {announcements.map((message, index) => (
          <div key={index}>{message}</div>
        ))}
      </LiveRegion>
    </AccessibilityContext.Provider>
  )
}

// Focus management for complex interfaces
const useFocusManagement = () => {
  const trapFocus = useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault()
            lastElement.focus()
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault()
            firstElement.focus()
          }
        }
      }
    }
    
    container.addEventListener('keydown', handleKeyDown)
    firstElement.focus()
    
    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
  }, [])
  
  return { trapFocus }
}
```

## Conclusion

This frontend architecture provides a comprehensive foundation for the unified AI assistant platform, emphasizing Langflow's clean visual language while ensuring performance, accessibility, and maintainability across all 8 integrated modules. The architecture supports seamless user experiences, efficient development workflows, and enterprise-grade requirements.