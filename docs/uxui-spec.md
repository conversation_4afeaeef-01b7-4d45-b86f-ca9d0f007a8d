# UX/UI Specifications - Unified AI Assistant Platform

## Design System Specifications

### Visual Language Foundation
Based on Langflow's clean, professional aesthetic, the unified AI assistant platform adopts a workflow-centric design language that emphasizes clarity, efficiency, and visual hierarchy.

#### Core Design Principles
1. **Workflow-First**: Every interface prioritizes workflow creation and management
2. **Clean Minimalism**: Uncluttered interfaces with purposeful whitespace
3. **Visual Hierarchy**: Clear information architecture through typography and spacing
4. **Contextual Intelligence**: Smart interface adaptation based on user context
5. **Progressive Disclosure**: Complex features revealed as needed

#### Color System
```scss
// Primary color palette inspired by Langflow
$primary-50: #f0f9ff;   // Background tints
$primary-100: #e0f2fe;  // Subtle backgrounds
$primary-500: #0ea5e9;  // Primary actions (Langflow blue)
$primary-600: #0284c7;  // Hover states
$primary-700: #0369a1;  // Active states
$primary-900: #0c4a6e;  // Text on light backgrounds

// Neutral palette for structure
$neutral-50: #fafafa;   // Canvas background
$neutral-100: #f5f5f5;  // Card backgrounds
$neutral-200: #e5e5e5;  // Borders
$neutral-500: #737373;  // Secondary text
$neutral-700: #404040;  // Primary text
$neutral-900: #171717;  // Headings

// Semantic colors
$success: #10b981;      // Success states
$warning: #f59e0b;      // Warning states
$error: #ef4444;        // Error states
$info: #3b82f6;         // Information states
```

#### Typography System
```scss
// Font families
$font-sans: 'Inter', system-ui, -apple-system, sans-serif;
$font-mono: 'JetBrains Mono', 'Fira Code', monospace;

// Font scale
$text-xs: 0.75rem;      // 12px - Captions, labels
$text-sm: 0.875rem;     // 14px - Secondary text
$text-base: 1rem;       // 16px - Body text
$text-lg: 1.125rem;     // 18px - Large body text
$text-xl: 1.25rem;      // 20px - Subheadings
$text-2xl: 1.5rem;      // 24px - Headings
$text-3xl: 1.875rem;    // 30px - Large headings

// Font weights
$font-normal: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;
```

#### Spacing System
```scss
// Spacing scale (based on 4px grid)
$space-1: 0.25rem;      // 4px
$space-2: 0.5rem;       // 8px
$space-3: 0.75rem;      // 12px
$space-4: 1rem;         // 16px
$space-5: 1.25rem;      // 20px
$space-6: 1.5rem;       // 24px
$space-8: 2rem;         // 32px
$space-10: 2.5rem;      // 40px
$space-12: 3rem;        // 48px
$space-16: 4rem;        // 64px
```

## User Interface Layouts

### Main Application Layout

```jsx
// Primary application shell
const AppShell = () => (
  <div className="app-shell">
    {/* Top navigation bar */}
    <header className="top-bar">
      <div className="logo-section">
        <Logo />
        <WorkspaceSwitcher />
      </div>
      <div className="center-section">
        <ModuleNavigation />
      </div>
      <div className="right-section">
        <CommandPalette />
        <NotificationCenter />
        <UserMenu />
      </div>
    </header>
    
    {/* Main content area */}
    <div className="main-content">
      <aside className="sidebar">
        <ModuleSpecificSidebar />
      </aside>
      <main className="content-area">
        <ModuleContent />
      </main>
      <aside className="right-panel">
        <ContextualPanel />
      </aside>
    </div>
    
    {/* Bottom status bar */}
    <footer className="status-bar">
      <SystemStatus />
      <AIAgentStatus />
      <PerformanceMetrics />
    </footer>
  </div>
)
```

### Module-Specific Layouts

#### 1. Agent Inbox (Chat Interface)
```jsx
const AgentInboxLayout = () => (
  <div className="chat-layout">
    <div className="conversation-list">
      <ConversationSearch />
      <ConversationItems />
    </div>
    <div className="chat-area">
      <ConversationHeader />
      <MessageThread />
      <InputArea />
    </div>
    <div className="chat-sidebar">
      <ConversationInfo />
      <AttachedFiles />
      <AIAgentPanel />
    </div>
  </div>
)
```

#### 2. Open Canvas (Document Editor)
```jsx
const OpenCanvasLayout = () => (
  <div className="editor-layout">
    <div className="document-tree">
      <DocumentNavigation />
      <RecentDocuments />
    </div>
    <div className="editor-area">
      <EditorToolbar />
      <RichTextEditor />
    </div>
    <div className="editor-sidebar">
      <DocumentOutline />
      <CollaborationPanel />
      <CommentThread />
    </div>
  </div>
)
```

#### 3. FotoFun (Design Studio)
```jsx
const FotoFunLayout = () => (
  <div className="design-layout">
    <div className="tool-palette">
      <ToolCategories />
      <ToolOptions />
    </div>
    <div className="canvas-area">
      <CanvasToolbar />
      <DesignCanvas />
      <ZoomControls />
    </div>
    <div className="design-sidebar">
      <LayerPanel />
      <PropertyPanel />
      <AssetLibrary />
    </div>
  </div>
)
```

#### 4. Vibe Kanban (Project Management)
```jsx
const VibeKanbanLayout = () => (
  <div className="kanban-layout">
    <div className="project-sidebar">
      <ProjectSelector />
      <AgentStatus />
      <TaskFilters />
    </div>
    <div className="kanban-area">
      <KanbanHeader />
      <KanbanBoard />
    </div>
    <div className="analytics-panel">
      <ProjectMetrics />
      <AgentPerformance />
      <TaskAnalytics />
    </div>
  </div>
)
```

## Visual Workflow Interfaces

### Workflow Builder Component

```jsx
const WorkflowBuilder = () => {
  const [nodes, setNodes] = useState([])
  const [connections, setConnections] = useState([])
  
  return (
    <div className="workflow-builder">
      {/* Component palette */}
      <div className="component-palette">
        <div className="palette-header">
          <h3>Components</h3>
          <SearchInput placeholder="Search components..." />
        </div>
        <div className="component-categories">
          {componentCategories.map(category => (
            <ComponentCategory 
              key={category.id}
              category={category}
              onDragStart={handleComponentDrag}
            />
          ))}
        </div>
      </div>
      
      {/* Workflow canvas */}
      <div className="workflow-canvas">
        <div className="canvas-toolbar">
          <ZoomControls />
          <LayoutControls />
          <ExecutionControls />
        </div>
        <div className="canvas-grid">
          <WorkflowGrid>
            {nodes.map(node => (
              <WorkflowNode
                key={node.id}
                node={node}
                onUpdate={handleNodeUpdate}
                onDelete={handleNodeDelete}
              />
            ))}
            <ConnectionRenderer connections={connections} />
          </WorkflowGrid>
        </div>
      </div>
      
      {/* Properties panel */}
      <div className="properties-panel">
        <NodeProperties />
        <ConnectionProperties />
        <WorkflowSettings />
      </div>
    </div>
  )
}
```

### Workflow Node Design

```jsx
const WorkflowNode = ({ node, onUpdate, onDelete }) => {
  const [isSelected, setIsSelected] = useState(false)
  const [isExecuting, setIsExecuting] = useState(false)
  
  return (
    <div 
      className={`workflow-node ${node.type} ${isSelected ? 'selected' : ''} ${isExecuting ? 'executing' : ''}`}
      onClick={() => setIsSelected(true)}
    >
      {/* Node header */}
      <div className="node-header">
        <div className="node-icon">
          <node.icon />
        </div>
        <div className="node-title">
          <h4>{node.name}</h4>
          <span className="node-type">{node.type}</span>
        </div>
        <div className="node-actions">
          <StatusIndicator status={node.status} />
          <MoreActions onDelete={() => onDelete(node.id)} />
        </div>
      </div>
      
      {/* Input handles */}
      <div className="input-handles">
        {node.inputs.map(input => (
          <Handle
            key={input.id}
            type="input"
            position="left"
            id={input.id}
            label={input.label}
          />
        ))}
      </div>
      
      {/* Node content */}
      <div className="node-content">
        {node.type === 'ai-agent' && <AIAgentConfig />}
        {node.type === 'data-processor' && <DataProcessorConfig />}
        {node.type === 'user-input' && <UserInputConfig />}
      </div>
      
      {/* Output handles */}
      <div className="output-handles">
        {node.outputs.map(output => (
          <Handle
            key={output.id}
            type="output"
            position="right"
            id={output.id}
            label={output.label}
          />
        ))}
      </div>
    </div>
  )
}
```

## Navigation & Information Architecture

### Primary Navigation Structure

```
1. Conversations (Agent Inbox)
   ├── Active Chats
   ├── AI Agents
   ├── History
   └── Settings

2. Documents (Open Canvas)
   ├── Recent Documents
   ├── Shared Documents
   ├── Templates
   └── Collaboration

3. Development (vCode)
   ├── Projects
   ├── Files
   ├── Terminal
   └── Extensions

4. Design Studio (FotoFun)
   ├── Projects
   ├── Assets
   ├── Tools
   └── Templates

5. Automation (Computer Use)
   ├── Workflows
   ├── Scripts
   ├── Monitoring
   └── Security

6. Projects (Vibe Kanban)
   ├── Active Projects
   ├── Agent Management
   ├── Task Tracking
   └── Analytics

7. Workflows (Langflow)
   ├── Visual Workflows
   ├── Component Library
   ├── Marketplace
   └── Execution History

8. Social Media
   ├── Content Calendar
   ├── Platforms
   ├── Analytics
   └── Automation
```

### Command Palette Interface

```jsx
const CommandPalette = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  
  const commands = useMemo(() => {
    return [
      // Module navigation
      { id: 'nav-chat', name: 'Go to Conversations', icon: MessageSquare, action: () => navigate('/chat') },
      { id: 'nav-docs', name: 'Go to Documents', icon: FileText, action: () => navigate('/docs') },
      
      // Quick actions
      { id: 'new-chat', name: 'Start new conversation', icon: Plus, action: createNewChat },
      { id: 'new-doc', name: 'Create new document', icon: FileText, action: createNewDocument },
      
      // Search
      { id: 'search-all', name: 'Search everywhere', icon: Search, action: openGlobalSearch },
      
      // Settings
      { id: 'settings', name: 'Open settings', icon: Settings, action: () => navigate('/settings') }
    ].filter(cmd => cmd.name.toLowerCase().includes(query.toLowerCase()))
  }, [query])
  
  return (
    <Dialog open={isOpen} onClose={onClose}>
      <div className="command-palette">
        <div className="command-input">
          <Search className="search-icon" />
          <input
            type="text"
            placeholder="Type a command or search..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            autoFocus
          />
        </div>
        <div className="command-list">
          {commands.map((command, index) => (
            <div
              key={command.id}
              className={`command-item ${index === selectedIndex ? 'selected' : ''}`}
              onClick={command.action}
            >
              <command.icon className="command-icon" />
              <span className="command-name">{command.name}</span>
              {command.shortcut && (
                <kbd className="command-shortcut">{command.shortcut}</kbd>
              )}
            </div>
          ))}
        </div>
      </div>
    </Dialog>
  )
}
```

## Interaction Design Patterns

### Micro-Animations

```scss
// Smooth transitions for all interactive elements
.interactive-element {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// Loading animations
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loading-spin {
  animation: spin 1s linear infinite;
}
```

### Drag and Drop Interactions

```jsx
const DragDropInteractions = {
  // Workflow node dragging
  nodeDrag: {
    dragStart: (node) => {
      node.classList.add('dragging')
      document.body.style.cursor = 'grabbing'
    },
    dragEnd: (node) => {
      node.classList.remove('dragging')
      document.body.style.cursor = 'default'
    }
  },
  
  // File upload dragging
  fileUpload: {
    dragEnter: (target) => {
      target.classList.add('drag-over')
    },
    dragLeave: (target) => {
      target.classList.remove('drag-over')
    },
    drop: (target, files) => {
      target.classList.remove('drag-over')
      handleFileUpload(files)
    }
  }
}
```

## Component Specifications

### Button Components

```jsx
// Primary button component
const Button = ({ 
  variant = 'primary', 
  size = 'md', 
  loading = false, 
  disabled = false,
  icon,
  children,
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500',
    ghost: 'text-gray-600 hover:bg-gray-100 hover:text-gray-700 focus:ring-gray-500',
    destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm rounded-md',
    md: 'px-4 py-2 text-sm rounded-md',
    lg: 'px-6 py-3 text-base rounded-md'
  }
  
  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <Spinner className="w-4 h-4 mr-2" />}
      {icon && !loading && <icon className="w-4 h-4 mr-2" />}
      {children}
    </button>
  )
}
```

### Input Components

```jsx
// Text input component
const Input = ({ 
  label, 
  error, 
  helper, 
  leftIcon, 
  rightIcon, 
  className,
  ...props 
}) => {
  return (
    <div className={`input-group ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <leftIcon className="h-5 w-5 text-gray-400" />
          </div>
        )}
        <input
          className={`
            block w-full rounded-md border-gray-300 shadow-sm 
            focus:border-blue-500 focus:ring-blue-500
            ${leftIcon ? 'pl-10' : 'pl-3'}
            ${rightIcon ? 'pr-10' : 'pr-3'}
            ${error ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500' : ''}
          `}
          {...props}
        />
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <rightIcon className="h-5 w-5 text-gray-400" />
          </div>
        )}
      </div>
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      {helper && !error && (
        <p className="mt-1 text-sm text-gray-500">{helper}</p>
      )}
    </div>
  )
}
```

## Mobile & Responsive Design

### Mobile Navigation

```jsx
const MobileNavigation = () => {
  const [isOpen, setIsOpen] = useState(false)
  
  return (
    <>
      {/* Mobile menu button */}
      <button
        className="mobile-menu-btn"
        onClick={() => setIsOpen(true)}
      >
        <Menu className="w-6 h-6" />
      </button>
      
      {/* Mobile slide-out menu */}
      <Drawer open={isOpen} onClose={() => setIsOpen(false)}>
        <div className="mobile-navigation">
          <div className="nav-header">
            <Logo />
            <button onClick={() => setIsOpen(false)}>
              <X className="w-6 h-6" />
            </button>
          </div>
          <div className="nav-content">
            {navigationConfig.map(item => (
              <MobileNavItem
                key={item.id}
                item={item}
                onClick={() => setIsOpen(false)}
              />
            ))}
          </div>
        </div>
      </Drawer>
    </>
  )
}
```

### Responsive Breakpoints

```scss
// Mobile-first responsive design
.responsive-layout {
  // Mobile (default)
  display: flex;
  flex-direction: column;
  
  // Tablet portrait
  @media (min-width: 768px) {
    flex-direction: row;
    
    .sidebar {
      width: 240px;
    }
  }
  
  // Desktop
  @media (min-width: 1024px) {
    .sidebar {
      width: 280px;
    }
    
    .right-panel {
      display: block;
      width: 320px;
    }
  }
  
  // Large desktop
  @media (min-width: 1440px) {
    .content-area {
      max-width: 1200px;
    }
  }
}
```

## Data Visualization Standards

### Chart Components

```jsx
const ChartComponents = {
  // Line chart for metrics over time
  MetricsChart: ({ data, title }) => (
    <div className="chart-container">
      <h3 className="chart-title">{title}</h3>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
          <XAxis dataKey="date" stroke="#6b7280" />
          <YAxis stroke="#6b7280" />
          <Tooltip 
            contentStyle={{
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '6px'
            }}
          />
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke="#0ea5e9" 
            strokeWidth={2}
            dot={{ fill: '#0ea5e9', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  ),
  
  // Progress indicators
  ProgressRing: ({ progress, size = 120 }) => (
    <div className="progress-ring" style={{ width: size, height: size }}>
      <svg width={size} height={size}>
        <circle
          cx={size / 2}
          cy={size / 2}
          r={(size - 8) / 2}
          fill="none"
          stroke="#e5e7eb"
          strokeWidth="4"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={(size - 8) / 2}
          fill="none"
          stroke="#0ea5e9"
          strokeWidth="4"
          strokeDasharray={`${(size - 8) * Math.PI}`}
          strokeDashoffset={`${(size - 8) * Math.PI * (1 - progress / 100)}`}
          strokeLinecap="round"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </svg>
      <div className="progress-text">
        <span className="progress-value">{progress}%</span>
      </div>
    </div>
  )
}
```

## Conclusion

This UX/UI specification provides comprehensive design guidance for creating a unified AI assistant platform that embodies Langflow's clean, professional aesthetic while supporting complex multi-module workflows. The specification ensures consistency across all 8 integrated modules while maintaining the flexibility needed for module-specific optimizations.

Key design achievements:
- **Visual Workflow Paradigm**: Drag-and-drop interfaces throughout the platform
- **Consistent Design Language**: Unified aesthetic across all modules
- **Performance-Optimized**: Smooth 60 FPS interactions and fast loading
- **Accessibility-First**: WCAG 2.1 AA compliant design patterns
- **Responsive Excellence**: Mobile-first design that scales to desktop
- **Enterprise-Ready**: Professional interface suitable for business use