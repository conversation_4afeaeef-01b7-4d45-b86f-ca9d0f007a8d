# Development Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- pnpm 8.15.0+
- Git
- PostgreSQL (optional, for database features)
- Red<PERSON> (optional, for caching)

### One-Command Setup
```bash
# Clone and setup everything
git clone https://github.com/PMStander/assistant.git
cd assistant
chmod +x scripts/dev-setup.sh
./scripts/dev-setup.sh
```

### Start Development Environment
```bash
# Start all modules and packages
pnpm run dev

# Or use the development script
chmod +x scripts/dev-start.sh
./scripts/dev-start.sh start
```

## 📦 Project Structure

```
unified-ai-assistant/
├── packages/                 # Shared packages
│   ├── types/               # TypeScript definitions
│   ├── ai/                  # AI orchestration
│   ├── events/              # Event system
│   ├── ui/                  # Shared components
│   ├── foundation/          # Platform integration
│   └── state/               # State management
├── modules/                 # Individual modules
│   ├── agent-inbox/         # Communication hub
│   ├── foto-fun/            # Visual design
│   ├── vibe-kanban/         # Task management
│   ├── open-canvas/         # Content creation
│   ├── vcode/               # Code generation
│   ├── gen-ui-computer-use/ # UI automation
│   ├── social-media-agent/  # Social media
│   └── langflow/            # Visual workflows
├── scripts/                 # Development scripts
├── docs/                    # Documentation
└── examples/                # Integration examples
```

## 🛠️ Development Workflow

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env.local

# Update with your API keys
# - OPENAI_API_KEY
# - ANTHROPIC_API_KEY
# - GOOGLE_AI_API_KEY
# - DATABASE_URL (if using database features)
```

### 2. Package Development
```bash
# Build all packages
pnpm run build:packages

# Watch mode for specific package
cd packages/ai
pnpm run dev

# Test packages
pnpm run test --filter=@unified-assistant/*
```

### 3. Module Development
```bash
# Start specific module
pnpm run dev:agent-inbox
pnpm run dev:foto-fun
pnpm run dev:vibe-kanban

# Or start all modules
pnpm run dev
```

### 4. Code Quality
```bash
# Lint all code
pnpm run lint

# Fix linting issues
pnpm run lint --fix

# Type checking
pnpm run type-check

# Format code
pnpm run format
```

## 🏗️ Architecture Overview

### LEVER Approach
Our development follows the **LEVER** methodology:

- **L**everage: Use existing patterns and proven solutions
- **E**xtend: Build upon solid foundations with new capabilities
- **V**erify: Ensure quality through testing and validation
- **E**liminate: Remove duplication and unnecessary complexity
- **R**educe: Simplify through standardization and automation

### Shared Packages

#### @unified-assistant/types
Core TypeScript definitions for the entire platform.

```typescript
import { AIRequest, PlatformEvent, ModuleConfig } from '@unified-assistant/types';
```

#### @unified-assistant/ai
Unified AI orchestration with multi-provider support.

```typescript
import { AIOrchestrator, ProviderManager } from '@unified-assistant/ai';

const orchestrator = new AIOrchestrator(providerManager, contextManager);
const result = await orchestrator.processRequest(request);
```

#### @unified-assistant/events
Event-driven communication system.

```typescript
import { PlatformEventBus } from '@unified-assistant/events';

const eventBus = new PlatformEventBus();
eventBus.emit({
  type: 'module:message',
  source: 'agent-inbox',
  target: 'foto-fun',
  payload: { action: 'generate-image' }
});
```

#### @unified-assistant/ui
Shared React components with consistent design.

```typescript
import { AIChat, ModuleSwitcher, Button } from '@unified-assistant/ui';

<AIChat 
  moduleContext={{ moduleId: 'agent-inbox' }}
  onMessage={handleMessage}
/>
```

#### @unified-assistant/foundation
Platform integration layer.

```typescript
import { UnifiedPlatform, AgentInboxIntegration } from '@unified-assistant/foundation';

const platform = new UnifiedPlatform(config);
await platform.initialize();
```

#### @unified-assistant/state
Zustand-based state management.

```typescript
import { usePlatformState, useAIState } from '@unified-assistant/state';

const { config, status, updateMetrics } = usePlatformState();
const { providers, activeProvider } = useAIState();
```

## 🔧 Development Scripts

### Core Scripts
```bash
# Development
pnpm run dev                 # Start all modules
pnpm run dev:foundation      # Start foundation packages only
pnpm run dev:agent-inbox     # Start agent-inbox module
pnpm run dev:foto-fun        # Start foto-fun module

# Building
pnpm run build               # Build everything
pnpm run build:packages      # Build shared packages only
pnpm run clean               # Clean all build artifacts

# Testing
pnpm run test                # Run all tests
pnpm run test:integration    # Run integration tests
pnpm run test:watch          # Run tests in watch mode

# Code Quality
pnpm run lint                # Lint all code
pnpm run type-check          # TypeScript validation
pnpm run format              # Format code with Prettier
```

### Development Helper Scripts
```bash
# Setup and management
./scripts/dev-setup.sh       # Initial development setup
./scripts/dev-start.sh start # Start development environment
./scripts/dev-start.sh status # Check status of all modules
./scripts/dev-start.sh logs agent-inbox # View module logs
./scripts/dev-start.sh restart foto-fun # Restart specific module
./scripts/dev-start.sh health # Run health checks
```

## 🧪 Testing Strategy

### Unit Tests
Each package and module has its own test suite:

```bash
# Run tests for specific package
pnpm run test --filter=@unified-assistant/ai

# Run tests for specific module
cd agent-inbox && pnpm run test
```

### Integration Tests
Cross-module functionality testing:

```bash
# Run integration tests
pnpm run test:integration

# Test specific integration
pnpm run test:integration -- --testNamePattern="AI Orchestration"
```

### End-to-End Tests
Full workflow testing:

```bash
# Run E2E tests (requires running development environment)
pnpm run test:e2e
```

## 🔍 Debugging

### Development Tools
1. **Browser DevTools**: React DevTools, Redux DevTools
2. **VS Code Extensions**: 
   - TypeScript Importer
   - ESLint
   - Prettier
   - Turbo Console Log

### Logging
```typescript
// Use structured logging
import { logger } from '@unified-assistant/utils';

logger.info('Module started', { moduleId: 'agent-inbox', port: 3000 });
logger.error('AI request failed', { error, requestId });
```

### Event Debugging
```typescript
// Enable event debugging
const eventBus = new PlatformEventBus();
eventBus.addMiddleware(new LoggingMiddleware({ logLevel: 'debug' }));
```

## 📊 Performance Monitoring

### Bundle Analysis
```bash
# Analyze bundle size
ANALYZE=true pnpm run build

# Check package sizes
pnpm run analyze:packages
```

### Performance Metrics
- **Bundle Size Target**: <500KB initial load
- **AI Response Time**: <3 seconds
- **Module Load Time**: <2 seconds
- **Event Processing**: <100ms

## 🚀 Deployment

### Development Deployment
```bash
# Build for development
pnpm run build

# Start production server
pnpm run start
```

### Production Deployment
See [Deployment Guide](./deployment.md) for production setup.

## 🤝 Contributing

### Code Style
- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Write tests for new features
- Update documentation

### Pull Request Process
1. Create feature branch from `main`
2. Implement changes with tests
3. Run `pnpm run lint` and `pnpm run test`
4. Update documentation if needed
5. Submit pull request with clear description

### Module Integration
When adding new modules:

1. **Create Module Structure**:
   ```bash
   mkdir my-new-module
   cd my-new-module
   pnpm init
   ```

2. **Implement BaseModule Interface**:
   ```typescript
   import { BaseModule } from '@unified-assistant/foundation';
   
   export class MyNewModule implements BaseModule {
     // Implementation
   }
   ```

3. **Register with Platform**:
   ```typescript
   const registration: ModuleRegistration = {
     id: 'my-new-module',
     name: 'My New Module',
     capabilities: ['feature1', 'feature2']
   };
   
   await platform.registerModule(registration);
   ```

4. **Add to Development Scripts**:
   Update `package.json` and development scripts to include the new module.

## 📚 Additional Resources

- [Architecture Guide](./architecture.md)
- [Module Integration Guide](./module-integration.md)
- [API Reference](./api-reference.md)
- [Deployment Guide](./deployment.md)
- [Troubleshooting Guide](./troubleshooting.md)

## 🆘 Getting Help

1. **Documentation**: Check the docs/ directory
2. **Issues**: Create GitHub issue with detailed description
3. **Discussions**: Use GitHub Discussions for questions
4. **Examples**: Check examples/ directory for usage patterns

---

**Happy coding! 🎉**
