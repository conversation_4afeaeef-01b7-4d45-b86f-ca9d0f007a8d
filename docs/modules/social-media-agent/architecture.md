# Social Media Agent - LangGraph Architecture

## Product-Focused Architecture Overview

From a product management perspective, the Social Media Agent's architecture represents a sophisticated orchestration of AI agents working in harmony to transform raw web content into engaging social media posts. This document details the LangGraph-based architecture that enables scalable, reliable content automation.

## Core LangGraph Concepts

### Agent Graph Philosophy
The system is built on LangGraph's agent orchestration framework, which provides:
- **Stateful Workflows**: Persistent state across multi-step processes
- **Conditional Routing**: Dynamic decision-making based on content analysis
- **Human Interrupts**: Seamless integration of human oversight
- **Parallel Processing**: Concurrent execution of independent tasks

### Graph Composition Pattern
Each major workflow is implemented as a separate LangGraph graph, enabling:
- **Modular Development**: Independent development and testing
- **Scalable Deployment**: Selective scaling of high-traffic components
- **Maintenance Efficiency**: Isolated bug fixes and feature updates
- **Reusability**: Shared components across different workflows

## Primary Graph Architecture

### Core Graphs (Located in `/src/agents/`)

#### 1. **generate_post** - Main Content Processing Graph
**File**: `/src/agents/generate-post/generate-post-graph.ts`
**Purpose**: Primary workflow for URL-to-post transformation
**Key Nodes**:
- `generateContentReport`: Content analysis and summarization
- `generatePost`: AI-powered post creation
- `humanNode`: Human review and approval
- `schedulePost`: Publishing and scheduling
- `condensePost`: Character limit optimization

**State Management**: `GeneratePostState` contains all workflow context including links, generated content, approval status, and scheduling information.

#### 2. **verify_links** - Content Validation Subgraph
**File**: `/src/agents/verify-links/verify-links-graph.ts`
**Purpose**: Validates URL accessibility and content relevance
**Routing Logic**: Dynamically routes to specialized verification based on URL type:
- GitHub repositories → `verify_github`
- YouTube videos → `verify_youtube`
- Twitter posts → `verify_tweet`
- General websites → `verify_general`

#### 3. **find_images** - Media Processing Subgraph
**File**: `/src/agents/find-images/find-images-graph.ts`
**Purpose**: Discovers, validates, and optimizes images for social media
**Workflow**: Image discovery → relevance ranking → format optimization → storage

#### 4. **supervisor** - Content Orchestration Graph
**File**: `/src/agents/supervisor/supervisor-graph.ts`
**Purpose**: High-level content routing and batch processing
**Capabilities**: Determines post types, groups related content, manages processing queues

#### 5. **curate_data** - Content Ingestion Graph
**File**: `/src/agents/curate-data/index.ts`
**Purpose**: Automated content discovery and filtering
**Sources**: AI newsletters, trending GitHub repos, Reddit posts, Twitter threads

## Subgraph Architecture Pattern

### Specialized Verification Graphs

#### GitHub Content Verification
**File**: `/src/agents/verify-github/`
**Specialization**: Repository analysis, release notes, trending repositories
**Integration**: GitHub API for repository metadata and content extraction

#### YouTube Content Verification
**File**: `/src/agents/verify-youtube/`
**Specialization**: Video metadata, transcript analysis, thumbnail extraction
**Integration**: YouTube API and Google Vertex AI for content analysis

#### Twitter Content Verification
**File**: `/src/agents/verify-tweet/verify-tweet-graph.ts`
**Specialization**: Tweet thread analysis, engagement metrics, author validation
**Integration**: Twitter API v2 for comprehensive tweet data

#### Reddit Content Verification
**File**: `/src/agents/verify-reddit-post/verify-reddit-post-graph.ts`
**Specialization**: Subreddit analysis, post popularity, comment insights
**Integration**: Reddit API via Snoowrap wrapper

## State Management Architecture

### Core State Definitions

#### GeneratePostState
**File**: `/src/agents/generate-post/generate-post-state.ts`
**Purpose**: Central state container for post generation workflow
**Key Fields**:
```typescript
{
  links: string[],              // Input URLs
  report: string,               // Content analysis summary
  post: string,                 // Generated social media post
  scheduledDate: Date,          // Publishing schedule
  next: string,                 // Routing decision
  humanFeedback: string,        // User modifications
  images: ImageData[],          // Associated media
  platformSpecific: {          // Platform-specific formatting
    twitter: TwitterPost,
    linkedin: LinkedInPost
  }
}
```

#### Shared State Components
**File**: `/src/agents/shared/shared-state.ts`
**Purpose**: Common state elements across multiple graphs
**Benefits**: Consistency, type safety, reduced duplication

### State Persistence Strategy

#### LangGraph Store Integration
**Purpose**: Persistent storage for workflow state and content tracking
**Implementation**: Used for duplicate URL detection and content history
**Files**: `/src/agents/shared/stores/`

#### URL Tracking System
**File**: `/src/agents/shared/stores/post-subject-urls.ts`
**Purpose**: Prevents duplicate content generation
**Mechanism**: Stores processed URLs with metadata for future reference

## Human-in-the-Loop (HITL) Architecture

### Human Node Pattern
**File**: `/src/agents/shared/nodes/generate-post/human-node.ts`
**Purpose**: Standardized human interaction interface
**Capabilities**:
- Content review and approval
- Real-time editing
- Scheduling modifications
- Quality feedback

### Agent Inbox Integration
**Purpose**: Web-based interface for human interactions
**Architecture**: RESTful API connecting to LangGraph interrupts
**User Experience**: Intuitive content review and approval workflow

### Interrupt Management
**Pattern**: Conditional routing based on human responses
**Implementation**: State-based decision trees handling:
- Approval/rejection decisions
- Content modification requests
- Scheduling changes
- Platform-specific adjustments

## Routing and Conditional Logic

### Dynamic Routing Strategy
**Implementation**: Conditional edge functions determining next steps
**Examples**:
```typescript
function routeAfterGeneratingReport(state: GeneratePostState) {
  return state.report ? "generatePost" : END;
}

function rewriteOrEndConditionalEdge(state: GeneratePostState) {
  if (state.next === "rewrite") return "rewritePost";
  if (state.next === "schedule") return "schedulePost";
  return END;
}
```

### Content-Type Routing
**Purpose**: Specialized handling based on URL type
**Benefits**: Optimized processing for different content sources
**Extensibility**: Easy addition of new content type handlers

## Error Handling and Recovery

### Graph-Level Error Handling
**Strategy**: Graceful degradation with fallback options
**Implementation**: Try-catch blocks with alternative routing
**User Experience**: Transparent error recovery with human notification

### Retry Logic
**Pattern**: Exponential backoff for API failures
**Scope**: External service calls and content processing
**Configuration**: Customizable retry limits and delays

## Performance Optimization

### Parallel Processing
**Strategy**: Concurrent execution of independent tasks
**Examples**: 
- Image processing while generating text
- Multi-platform content generation
- Parallel content validation

### Caching Strategy
**Implementation**: Strategic caching of expensive operations
**Targets**: Content analysis results, image processing, API responses
**Benefits**: Reduced latency and API quota usage

## Monitoring and Observability

### LangSmith Integration
**Purpose**: Comprehensive workflow monitoring and debugging
**Features**: 
- Execution tracing
- Performance metrics
- Error tracking
- User interaction analysis

### Operational Metrics
**Key Indicators**:
- Graph execution time
- Success/failure rates
- Human interaction frequency
- Platform publishing rates

## Security Architecture

### Authentication Flow
**Pattern**: OAuth integration via Arcade API
**Security**: Token encryption and secure storage
**Compliance**: Platform-specific security requirements

### Content Validation
**Multi-layer Approach**:
- Automated content filtering
- Human review checkpoints
- Platform policy compliance
- Brand safety validation

## Scalability Design

### Horizontal Scaling
**Strategy**: Multiple graph instances with load balancing
**State Management**: Shared state store for coordination
**Resource Allocation**: Dynamic scaling based on content volume

### Resource Optimization
**API Management**: Efficient quota usage and rate limiting
**Memory Management**: Optimized state storage and cleanup
**Processing Efficiency**: Batch operations and parallel execution

## Integration Points

### External Service Integration
**Pattern**: Adapter pattern for external APIs
**Services**: Twitter, LinkedIn, GitHub, YouTube, Reddit
**Error Handling**: Service-specific retry and fallback logic

### Data Persistence
**Strategy**: Supabase for structured data, LangGraph Store for workflow state
**Backup**: Regular state snapshots and recovery procedures
**Consistency**: Transaction-based updates for critical operations

## Development and Deployment

### Graph Configuration
**File**: `/social-media-agent/langgraph.json`
**Purpose**: Graph registration and deployment configuration
**Management**: Version control and environment-specific settings

### Environment Management
**Pattern**: Environment-specific configuration
**Security**: Secure credential management
**Flexibility**: Feature flags and configurable parameters

## Testing Strategy

### Graph Testing
**Approach**: Integration tests for complete workflows
**Coverage**: End-to-end scenarios and error conditions
**Automation**: Continuous integration with test data

### Unit Testing
**Focus**: Individual node logic and state transitions
**Tools**: Jest framework with TypeScript support
**Coverage**: Critical business logic and edge cases

## Future Architecture Considerations

### Extensibility
**Design**: Plugin architecture for new content types
**Flexibility**: Configurable workflows and custom nodes
**Integration**: Easy addition of new social platforms

### Performance Enhancements
**Optimization**: Advanced caching and pre-processing
**Scalability**: Distributed processing and queue management
**Monitoring**: Real-time performance analytics

## Architecture Benefits

### Product Advantages
1. **Rapid Feature Development**: Modular graph composition
2. **Reliable Operations**: Built-in error handling and recovery
3. **Scalable Growth**: Horizontal scaling and resource optimization
4. **Quality Assurance**: Multi-layer validation and human oversight
5. **Operational Excellence**: Comprehensive monitoring and debugging

### Technical Advantages
1. **Maintainability**: Clear separation of concerns
2. **Testability**: Isolated components with defined interfaces
3. **Extensibility**: Plugin architecture for new features
4. **Reliability**: Robust error handling and state management
5. **Performance**: Optimized processing and resource usage

This architecture provides a solid foundation for building a production-ready social media automation system that balances efficiency with quality control, ensuring reliable content generation while maintaining human oversight and brand consistency.