# vCode IDE - Architecture Documentation

## Architectural Overview

vCode IDE implements a modern Electron-based architecture that separates concerns between system-level operations and user interface, enabling secure, performant, and maintainable desktop application development.

## Core Architecture Pattern

### Multi-Process Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Framework                       │
├─────────────────────────────────────────────────────────────┤
│  Main Process (Node.js Runtime)                            │
│  ├── Process Management                                    │
│  ├── Window Management                                     │
│  ├── IPC Event Handling                                   │
│  ├── System Integration                                    │
│  └── Security Context                                      │
├─────────────────────────────────────────────────────────────┤
│  Preload Script (Security Bridge)                          │
│  ├── IPC Context Exposure                                 │
│  ├── API Surface Definition                               │
│  └── Security Isolation                                    │
├─────────────────────────────────────────────────────────────┤
│  Renderer Process (Chromium Runtime)                       │
│  ├── React Application                                     │
│  ├── Monaco Editor                                         │
│  ├── UI Components                                         │
│  └── State Management                                      │
└─────────────────────────────────────────────────────────────┘
```

## Main Process Architecture

### Entry Point: `/Users/<USER>/Projects/own/assistant/vcode/src/main.ts`
```typescript
// Main process responsibilities
- Window lifecycle management
- IPC listener registration
- Extension installation (React DevTools)
- Application event handling
- Terminal cleanup on exit
```

### Window Management
```typescript
// Window configuration
const mainWindow = new BrowserWindow({
  width: 800,
  height: 600,
  webPreferences: {
    devTools: inDevelopment,
    contextIsolation: true,        // Security isolation
    nodeIntegration: true,         // Node.js access
    nodeIntegrationInSubFrames: false,
    webSecurity: false,           // For Monaco editor
    preload: preload,             // Security bridge
  },
  frame: true,
});
```

### IPC Communication Hub
Location: `/Users/<USER>/Projects/own/assistant/vcode/src/helpers/ipc/`

#### IPC Module Structure
```
ipc/
├── listeners-register.ts        # Central IPC registration
├── context-exposer.ts          # Security context exposure
├── ai/                         # AI service communication
│   ├── ai-channels.ts
│   ├── ai-context.ts
│   └── ai-listeners.ts
├── git/                        # Git integration
│   ├── git-channels.ts
│   ├── git-context.ts
│   └── git-listeners.ts
├── terminal/                   # Terminal management
│   ├── terminal-channels.ts
│   ├── terminal-context.ts
│   └── terminal-listeners.ts
└── [other modules]/
```

## Renderer Process Architecture

### React Application Structure
```
src/
├── App.tsx                     # Root application component
├── routes/                     # Application routing
│   ├── __root.tsx
│   ├── router.tsx
│   └── routes.tsx
├── pages/                      # Page components
│   ├── home/                   # Welcome screen
│   └── workspace/              # Main IDE workspace
├── components/                 # Reusable components
│   ├── ui/                     # Radix UI components
│   └── global-commands/        # Command palette
├── stores/                     # State management
│   ├── buffers/
│   ├── editor-splits/
│   ├── git/
│   ├── project/
│   ├── settings/
│   └── terminal/
└── services/                   # Business logic
```

### State Management Architecture
Using Zustand for lightweight state management:

```typescript
// Store pattern
interface StoreState {
  // State properties
  data: any;
  loading: boolean;
  error: string | null;
  
  // Actions
  setData: (data: any) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}
```

## Security Architecture

### Context Isolation
- **Enabled**: Full context isolation between main and renderer
- **Preload Script**: Secure API surface exposure
- **Node Integration**: Limited to main process only

### Electron Fuses Configuration
Location: `/Users/<USER>/Projects/own/assistant/vcode/forge.config.ts`
```typescript
new FusesPlugin({
  version: FuseVersion.V1,
  [FuseV1Options.RunAsNode]: false,
  [FuseV1Options.EnableCookieEncryption]: true,
  [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
  [FuseV1Options.EnableNodeCliInspectArguments]: false,
  [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
  [FuseV1Options.OnlyLoadAppFromAsar]: true,
})
```

### IPC Security Model
```typescript
// Secure context exposure pattern
contextBridge.exposeInMainWorld('electronAPI', {
  // AI operations
  ai: {
    sendMessage: (messages: any, requestId: string) => 
      ipcRenderer.invoke(AI_SEND_MESSAGE_CHANNEL, { messages, requestId }),
    onStreamChunk: (callback: Function) => 
      ipcRenderer.on(AI_STREAM_CHUNK_CHANNEL, callback),
    onStreamEnd: (callback: Function) => 
      ipcRenderer.on(AI_STREAM_END_CHANNEL, callback),
    onStreamError: (callback: Function) => 
      ipcRenderer.on(AI_STREAM_ERROR_CHANNEL, callback),
  },
  // Other secure APIs...
});
```

## Service Layer Architecture

### AI Service Integration
Location: `/Users/<USER>/Projects/own/assistant/vcode/src/api/ai/`

#### Grok AI Integration
```typescript
// AI service flow
Client Request → IPC Channel → Main Process → Grok API
                                    ↓
Streaming Response ← IPC Events ← Stream Processing
```

#### AI Service Components
- **System Prompt**: Defines AI behavior and capabilities
- **Tool Registry**: Extensible tool system for AI operations
- **Streaming Handler**: Real-time response processing
- **Settings Integration**: Secure API key management

### Git Service Integration
Location: `/Users/<USER>/Projects/own/assistant/vcode/src/helpers/ipc/git/`

#### Git Operations Architecture
```typescript
// Git service capabilities
- Repository status monitoring
- Branch management
- Commit operations
- Diff generation
- File watching for changes
```

### Terminal Service Integration
Location: `/Users/<USER>/Projects/own/assistant/vcode/src/helpers/ipc/terminal/`

#### Terminal Management
```typescript
// Terminal architecture
├── PTY Process Management
├── Cross-platform shell detection
├── Terminal lifecycle management
├── Data streaming via IPC
└── Cleanup on application exit
```

## Build System Architecture

### Vite Configuration
Three separate build configurations:
1. **Main Process**: `vite.main.config.ts`
2. **Preload Script**: `vite.preload.config.ts`
3. **Renderer Process**: `vite.renderer.config.mts`

### Electron Forge Integration
Location: `/Users/<USER>/Projects/own/assistant/vcode/forge.config.ts`

#### Build Pipeline
```typescript
// Build process flow
Source Code → Vite Compilation → Electron Forge → Platform Packages
                                      ↓
                                 Distribution Files
```

#### Platform Targets
- **Windows**: Squirrel installer
- **macOS**: ZIP distribution with universal binary
- **Linux**: DEB and RPM packages

## Performance Architecture

### Bundle Optimization
```typescript
// Renderer process optimizations
build: {
  rollupOptions: {
    output: {
      manualChunks: {
        monaco: ['monaco-editor']  // Separate Monaco chunk
      }
    }
  }
}
```

### Monaco Editor Integration
```typescript
// Monaco optimization
optimizeDeps: {
  include: [
    'monaco-editor/esm/vs/editor/editor.api',
    'monaco-editor/esm/vs/editor/editor.worker',
    'monaco-editor/esm/vs/language/json/json.worker',
    'monaco-editor/esm/vs/language/css/css.worker',
    'monaco-editor/esm/vs/language/html/html.worker',
    'monaco-editor/esm/vs/language/typescript/ts.worker'
  ]
}
```

### React Compiler Integration
```typescript
// Automatic React optimizations
react({
  babel: {
    plugins: [["babel-plugin-react-compiler"]],
  },
})
```

## Data Flow Architecture

### Application State Flow
```
User Action → Component → Store → IPC → Main Process → System/API
                 ↓                              ↓
            UI Update ←  Store Update  ←  IPC Response
```

### AI Communication Flow
```
User Input → Chat Component → AI Service → Grok API
                    ↓                         ↓
          UI Stream Update ←  IPC Events  ←  Stream Response
```

### File System Integration
```
File Operation → Project Service → IPC → Main Process → Node.js FS
                        ↓                              ↓
                 UI Update ←  Store Update  ←  IPC Response
```

## Error Handling Architecture

### Multi-Level Error Handling
1. **Component Level**: React error boundaries
2. **Service Level**: Try-catch with error propagation
3. **IPC Level**: Error channel communication
4. **Main Process**: Global error handling

### Error Communication Pattern
```typescript
// Error propagation
try {
  await operation();
} catch (error) {
  webContents.send(ERROR_CHANNEL, { 
    requestId, 
    error: error.message 
  });
}
```

## Extensibility Architecture

### Plugin System Foundation
- **Tool Registry**: Dynamic tool registration
- **IPC Channels**: Extensible communication
- **Context System**: Secure API exposure
- **Service Layer**: Modular service architecture

### Future Extension Points
- **Custom Tools**: AI tool extension system
- **Themes**: Visual customization system
- **Language Servers**: LSP integration
- **Workflow Automation**: Custom automation tools

## DevOps Considerations

### Development Environment
- **Hot Reload**: Full development server with live reload
- **Debug Tools**: React DevTools integration
- **Source Maps**: Full debugging support
- **Environment Variables**: Development/production configuration

### Build Optimization
- **Code Splitting**: Feature-based module separation
- **Tree Shaking**: Unused code elimination
- **Asset Optimization**: Image and resource optimization
- **Bundle Analysis**: Performance monitoring tools

### Security Hardening
- **ASAR Packaging**: Code protection
- **Integrity Validation**: Embedded ASAR validation
- **Context Isolation**: Security boundary enforcement
- **API Surface**: Minimal exposure principle

This architecture provides a robust foundation for a desktop IDE while maintaining security, performance, and extensibility requirements essential for modern development tools.