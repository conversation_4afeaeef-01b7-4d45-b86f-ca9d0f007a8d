# FotoFun - AI Integration Architecture

## AI-First Design Philosophy

FotoFun is built with AI integration as a core architectural principle, not an afterthought. Every tool in the 32+ tool suite is AI-compatible through a sophisticated adapter pattern that enables natural language control while preserving the full power of manual editing capabilities.

## AI System Architecture Overview

```mermaid
graph TB
    subgraph "User Interface Layer"
        UC[User Chat Input]
        AAD[Agent Approval Dialog]
        WD[Workflow Display]
        CI[Confidence Indicator]
    end
    
    subgraph "AI Orchestration Layer"
        MRA[Master Routing Agent]
        SEA[Sequential Editing Agent]
        WM[Workflow Memory]
        CCE[Confidence & Context Engine]
    end
    
    subgraph "Tool Adapter Layer"
        TAR[Tool Adapter Registry]
        BTA[Base Tool Adapter]
        CTA[Canvas Tool Adapter]
        FTA[Filter Tool Adapter]
    end
    
    subgraph "Canvas Execution Layer"
        CB[Canvas Bridge]
        TM[Tool Manager]
        CM[Canvas Manager]
        FC[Fabric Canvas]
    end
    
    subgraph "External AI Services"
        OAI[OpenAI GPT-4o]
        REP[Replicate API]
        SDXL[Stable Diffusion XL]
        BG[Background Removal]
        UP[Image Upscaling]
    end
    
    UC --> MRA
    MRA --> SEA
    MRA --> TAR
    SEA --> WM
    TAR --> BTA
    BTA --> CB
    CB --> TM
    TM --> FC
    MRA --> OAI
    TAR --> REP
    REP --> SDXL
    REP --> BG
    REP --> UP
    
    classDef ai fill:#10b981,stroke:#047857,color:#fff
    classDef adapter fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef canvas fill:#f59e0b,stroke:#d97706,color:#fff
    classDef external fill:#ef4444,stroke:#dc2626,color:#fff
    
    class MRA,SEA,WM,CCE ai
    class TAR,BTA,CTA,FTA adapter
    class CB,TM,CM,FC canvas
    class OAI,REP,SDXL,BG,UP external
```

## AI Agent System

### Master Routing Agent
**File Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/agents/MasterRoutingAgent.ts`
**Purpose**: Intelligent request analysis and routing to appropriate execution strategies

#### Core Responsibilities
1. **Request Analysis**: Parse natural language requests using GPT-4o
2. **Route Determination**: Decide between text responses, simple tools, or complex workflows
3. **Confidence Scoring**: Calculate execution confidence for auto-approval
4. **Context Management**: Maintain canvas state and user preferences

#### Routing Strategies
```typescript
type RouteType = 
  | 'text-only'           // Informational responses
  | 'simple-tool'         // Single tool execution
  | 'sequential-workflow' // Multi-step workflows
  | 'evaluator-optimizer' // Quality-focused workflows (future)
  | 'orchestrator-worker' // Parallel processing (future)

interface RouteAnalysis {
  requestType: RouteType
  confidence: number        // 0-1 confidence score
  reasoning: string         // Why this route was chosen
  complexity: 'trivial' | 'simple' | 'moderate' | 'complex'
  suggestedTool?: string   // For simple-tool routes
  estimatedSteps?: number  // For workflow routes
}
```

#### Example Request Analysis
```typescript
// Input: "Make the image brighter and more dramatic"
const analysis = await masterRouter.analyzeRequest(request)
// Output:
{
  requestType: 'sequential-workflow',
  confidence: 0.85,
  reasoning: 'Multi-step adjustment requiring brightness and contrast changes',
  complexity: 'simple',
  estimatedSteps: 2
}
```

### Sequential Editing Agent
**File Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/agents/SequentialEditingAgent.ts`
**Purpose**: Multi-step workflow planning and execution

#### Workflow Planning Process
1. **Step Decomposition**: Break complex requests into atomic operations
2. **Tool Selection**: Choose optimal tools for each step
3. **Parameter Inference**: Calculate appropriate parameters
4. **Dependency Analysis**: Ensure proper execution order
5. **Quality Validation**: Verify each step's success

#### Workflow Examples
```typescript
// Vintage Photo Effect Workflow
const vintageWorkflow = {
  steps: [
    {
      tool: 'saturation',
      params: { adjustment: -30 },
      description: 'Reduce color saturation for vintage look'
    },
    {
      tool: 'sepia',
      params: { intensity: 0.7 },
      description: 'Apply warm sepia tone'
    },
    {
      tool: 'brightness',
      params: { adjustment: -10 },
      description: 'Slightly darken for aged appearance'
    }
  ],
  confidence: 0.92,
  autoApprove: true
}

// Portrait Enhancement Workflow
const portraitWorkflow = {
  steps: [
    {
      tool: 'exposure',
      params: { adjustment: 0.3 },
      description: 'Brighten overall exposure'
    },
    {
      tool: 'saturation',
      params: { adjustment: 20 },
      description: 'Enhance skin tones'
    },
    {
      tool: 'sharpen',
      params: { amount: 0.5, radius: 1.0 },
      description: 'Sharpen facial details'
    }
  ],
  confidence: 0.88,
  autoApprove: false  // Lower confidence requires approval
}
```

### Workflow Memory System
**File Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/agents/WorkflowMemory.ts`
**Purpose**: Context preservation across multi-turn conversations

#### Memory Components
```typescript
interface WorkflowMemory {
  // Conversation context
  conversationHistory: ConversationTurn[]
  currentIntent: UserIntent
  
  // Canvas context
  canvasState: CanvasSnapshot
  previousOperations: OperationHistory[]
  
  // User preferences learned over time
  stylePreferences: StyleProfile
  qualityThresholds: QualityPreferences
  
  // Workflow state
  activeWorkflow?: WorkflowState
  pendingApprovals: PendingOperation[]
}

interface ConversationTurn {
  userMessage: string
  aiResponse: AgentResult
  timestamp: number
  canvasStateBefore: CanvasSnapshot
  canvasStateAfter: CanvasSnapshot
}
```

#### Context-Aware Responses
```typescript
// Memory-enhanced request processing
class WorkflowMemory {
  async processRequest(request: string): Promise<ContextualResponse> {
    const context = this.buildContext()
    const enrichedRequest = this.enrichWithContext(request, context)
    
    return {
      request: enrichedRequest,
      context,
      relevantHistory: this.getRelevantHistory(request),
      suggestedFollowUps: this.generateFollowUps(context)
    }
  }
  
  private buildContext(): RequestContext {
    return {
      canvasContent: this.analyzeCanvasContent(),
      recentOperations: this.getRecentOperations(),
      userStyle: this.inferUserStyle(),
      currentTool: this.getCurrentTool()
    }
  }
}
```

## Tool Adapter Architecture

### Base Tool Adapter System
**File Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/adapters/base.ts`
**Purpose**: Convert canvas tools to AI-compatible interfaces

#### Adapter Hierarchy
```typescript
abstract class BaseToolAdapter<TInput, TOutput> {
  // Core properties
  abstract tool: Tool                    // Canvas tool instance
  abstract aiName: string               // AI-friendly name
  abstract description: string          // AI usage description
  abstract metadata: ToolMetadata       // Categorization and performance hints
  abstract inputSchema: z.ZodType<TInput> // Type-safe input validation
  
  // Execution
  abstract execute(params: TInput, context: CanvasContext): Promise<TOutput>
  
  // AI SDK integration
  toAITool(): AITool {
    return tool({
      description: this.description,
      inputSchema: this.inputSchema,
      execute: async (args) => {
        // Server-side validation, client-side execution pattern
        return {
          clientExecutionRequired: true,
          params: args,
          metadata: this.metadata
        }
      }
    })
  }
}
```

#### Canvas Tool Adapter (Extended Base)
```typescript
abstract class CanvasToolAdapter<TInput, TOutput> extends BaseToolAdapter<TInput, TOutput> {
  // Common execution pattern for canvas tools
  protected async executeWithCommonPatterns(
    params: TInput,
    context: CanvasContext,
    toolExecution: (images: FabricImage[]) => Promise<Partial<TOutput>>
  ): Promise<TOutput> {
    
    // 1. Validate canvas state
    if (!context.canvas) {
      throw new Error('Canvas is required but not provided')
    }
    
    // 2. Get target images based on context
    const images = context.targetImages
    if (images.length === 0) {
      throw new Error(`No images found to ${this.getActionVerb()}`)
    }
    
    // 3. Execute tool-specific logic
    const result = await toolExecution(images)
    
    // 4. Return standardized result
    return {
      success: true,
      targetingMode: context.targetingMode,
      affectedObjects: images.map(img => img.get('id')),
      ...result
    } as TOutput
  }
  
  protected abstract getActionVerb(): string
}
```

#### Filter Tool Adapter (Specialized)
```typescript
abstract class FilterToolAdapter<TInput, TOutput> extends CanvasToolAdapter<TInput, TOutput> {
  // Specialized for Fabric.js filter operations
  protected async applyFilterToImages(
    images: FabricImage[],
    params: TInput,
    canvas: Canvas
  ): Promise<void> {
    const filterType = this.getFilterType()
    
    images.forEach((img) => {
      const imageWithFilters = img as FabricImageWithFilters
      
      // Initialize filters array
      if (!imageWithFilters.filters) {
        imageWithFilters.filters = []
      }
      
      // Remove existing filters of this type
      imageWithFilters.filters = imageWithFilters.filters.filter(
        (filter) => filter.type !== filterType
      )
      
      // Add new filter if parameters indicate it should be applied
      if (this.shouldApplyFilter(params)) {
        const filter = this.createFilter(params)
        imageWithFilters.filters.push(filter)
      }
      
      // Apply all filters
      imageWithFilters.applyFilters()
    })
    
    canvas.renderAll()
  }
  
  protected abstract getFilterType(): string
  protected abstract createFilter(params: TInput): Filter
  protected abstract shouldApplyFilter(params: TInput): boolean
}
```

### Tool Registry System
**File Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/adapters/registry.ts`
**Purpose**: Centralized management of all tool adapters

#### Registry Implementation
```typescript
class AdapterRegistry {
  private adapters = new Map<string, BaseToolAdapter>()
  private categoryIndex = new Map<string, string[]>()
  
  register(adapter: BaseToolAdapter): void {
    this.adapters.set(adapter.aiName, adapter)
    this.indexByCategory(adapter)
  }
  
  get(name: string): BaseToolAdapter | undefined {
    return this.adapters.get(name)
  }
  
  getToolNamesByCategory(category: ToolCategory): string[] {
    return this.categoryIndex.get(category) || []
  }
  
  getAITools(): Record<string, AITool> {
    const aiTools: Record<string, AITool> = {}
    
    for (const [name, adapter] of this.adapters) {
      aiTools[name] = adapter.toAITool()
    }
    
    return aiTools
  }
  
  private indexByCategory(adapter: BaseToolAdapter): void {
    const category = adapter.metadata.category
    if (!this.categoryIndex.has(category)) {
      this.categoryIndex.set(category, [])
    }
    this.categoryIndex.get(category)!.push(adapter.aiName)
  }
}

// Global registry instance
export const adapterRegistry = new AdapterRegistry()
```

#### Tool Categories
```typescript
interface ToolMetadata {
  category: 'canvas-editing' | 'ai-native'
  executionType: 'fast' | 'slow' | 'expensive'
  worksOn: 'existing-image' | 'new-image' | 'both'
}

// Category-based tool organization
const TOOL_CATEGORIES = {
  'canvas-editing': [
    // Fast, local operations
    'brightness', 'contrast', 'saturation', 'hue', 'exposure', 'colorTemperature',
    'blur', 'sharpen', 'grayscale', 'sepia', 'invert',
    'crop', 'rotate', 'flip', 'resize'
  ],
  'ai-native': [
    // Expensive, API-based operations
    'generateImage', 'removeBackground', 'upscaleImage', 'inpaintImage'
  ]
} as const
```

## AI-Powered Parameter Inference

### Natural Language Parameter Extraction
**Implementation**: GPT-4o powered parameter inference

```typescript
class ParameterInferenceEngine {
  async inferToolParameters(
    tool: BaseToolAdapter,
    request: string,
    context: CanvasContext
  ): Promise<ToolParameters> {
    
    const { object: params } = await generateObject({
      model: openai('gpt-4o'),
      schema: tool.inputSchema,
      prompt: `Infer parameters for the ${tool.aiName} tool based on this request: "${request}"
      
      Tool description: ${tool.description}
      
      Canvas context:
      - Dimensions: ${context.canvasAnalysis.dimensions.width}x${context.canvasAnalysis.dimensions.height}
      - Has content: ${context.canvasAnalysis.hasContent}
      - Object count: ${context.canvasAnalysis.objectCount}
      
      Provide appropriate parameters for this tool. Be conservative with values.
      Consider the user's intent and the current image state.`
    })
    
    return this.validateAndNormalizeParameters(params, tool)
  }
  
  private validateAndNormalizeParameters(
    params: unknown,
    tool: BaseToolAdapter
  ): ToolParameters {
    // Validate against tool schema
    const validatedParams = tool.inputSchema.parse(params)
    
    // Apply tool-specific normalization
    return this.normalizeForTool(validatedParams, tool)
  }
}
```

### Context-Aware Parameter Adjustment
```typescript
// Example: Brightness adjustment with context awareness
class BrightnessAdapter extends FilterToolAdapter {
  async inferParameters(request: string, context: CanvasContext): Promise<BrightnessParams> {
    // Analyze current image brightness
    const currentBrightness = await this.analyzeImageBrightness(context.targetImages[0])
    
    // Extract intent from natural language
    const intent = await this.extractBrightnessIntent(request)
    
    // Calculate optimal adjustment based on current state and intent
    const adjustment = this.calculateOptimalAdjustment(currentBrightness, intent)
    
    return {
      adjustment: this.clampValue(adjustment, -100, 100)
    }
  }
  
  private async analyzeImageBrightness(image: FabricImage): Promise<number> {
    // Analyze histogram to determine current brightness level
    const histogram = await this.getImageHistogram(image)
    return this.calculateAverageBrightness(histogram)
  }
  
  private extractBrightnessIntent(request: string): BrightnessIntent {
    // Pattern matching for brightness-related terms
    const patterns = {
      'much brighter': 40,
      'brighter': 25,
      'slightly brighter': 15,
      'darker': -25,
      'much darker': -40,
      'fix underexposure': 30,
      'fix overexposure': -20
    }
    
    for (const [pattern, value] of Object.entries(patterns)) {
      if (request.toLowerCase().includes(pattern)) {
        return { type: pattern, suggestedValue: value }
      }
    }
    
    return { type: 'neutral', suggestedValue: 0 }
  }
}
```

## Confidence Scoring & Auto-Approval

### Multi-Factor Confidence Calculation
**Implementation**: Sophisticated confidence scoring for auto-approval decisions

```typescript
interface ConfidenceFactors {
  parameterAppropriate: number    // Are parameters reasonable and safe?
  canvasContext: number          // Does canvas state support this operation?
  toolSuitability: number       // Is this the optimal tool for the request?
  riskLevel: number             // How likely is this to produce intended result?
}

interface ConfidenceScore {
  overall: number                // 0-1 overall confidence
  factors: ConfidenceFactors     // Individual factor scores
  reasoning: string              // Human-readable explanation
}

class ConfidenceEngine {
  async calculateToolConfidence(
    tool: BaseToolAdapter,
    params: ToolParameters,
    request: string,
    context: CanvasContext
  ): Promise<ConfidenceScore> {
    
    const { object: confidence } = await generateObject({
      model: openai('gpt-4o'),
      schema: confidenceSchema,
      prompt: `Calculate confidence for executing ${tool.aiName} with request: "${request}"
      
      Tool: ${tool.aiName}
      Description: ${tool.description}
      Parameters: ${JSON.stringify(params)}
      
      Canvas context:
      - Dimensions: ${context.canvasAnalysis.dimensions.width}x${context.canvasAnalysis.dimensions.height}
      - Has content: ${context.canvasAnalysis.hasContent}
      - Object count: ${context.canvasAnalysis.objectCount}
      
      Calculate confidence based on:
      1. Parameter Appropriateness: Are values reasonable and within safe ranges?
      2. Canvas Context: Does the current canvas state support this operation?
      3. Tool Suitability: Is this the best tool for what the user wants?
      4. Risk Level: How likely is this to succeed without issues?
      
      Overall confidence should be the weighted average of these factors.
      Be conservative - it's better to ask for approval than make mistakes.`
    })
    
    return {
      overall: confidence.overall,
      factors: confidence.factors,
      reasoning: confidence.reasoning
    }
  }
}
```

### Auto-Approval Thresholds
```typescript
interface AutoApprovalSettings {
  threshold: number              // 0-1 threshold for auto-approval
  categoryOverrides: Record<ToolCategory, number>
  riskToleranceLevel: 'conservative' | 'moderate' | 'aggressive'
}

const DEFAULT_THRESHOLDS = {
  conservative: {
    threshold: 0.9,
    categoryOverrides: {
      'canvas-editing': 0.85,    // Lower threshold for local operations
      'ai-native': 0.95          // Higher threshold for expensive operations
    }
  },
  moderate: {
    threshold: 0.8,
    categoryOverrides: {
      'canvas-editing': 0.75,
      'ai-native': 0.9
    }
  },
  aggressive: {
    threshold: 0.7,
    categoryOverrides: {
      'canvas-editing': 0.65,
      'ai-native': 0.8
    }
  }
} as const
```

## External AI Service Integration

### Replicate API Integration
**File Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/server/replicateClient.ts`
**Purpose**: Integration with state-of-the-art AI models

#### Supported Models
```typescript
export const REPLICATE_MODELS = {
  // Image Generation
  IMAGE_GENERATION: "stability-ai/stable-diffusion-xl-base-1.0",
  
  // Image Processing
  BACKGROUND_REMOVAL: "bria-ai/bria-remove-background",
  IMAGE_UPSCALING: "google-research/maxim",
  INPAINTING: "stability-ai/stable-diffusion-inpainting",
  
  // Future models
  STYLE_TRANSFER: "runwayml/stable-diffusion-v1-5",
  FACE_RESTORATION: "tencentarc/gfpgan",
  SUPER_RESOLUTION: "xinntao/realesrgan"
} as const

class ReplicateClient {
  async generateImage(params: ImageGenerationParams): Promise<ImageResult> {
    const result = await replicate.run(REPLICATE_MODELS.IMAGE_GENERATION, {
      input: {
        prompt: params.prompt,
        negative_prompt: params.negativePrompt,
        width: params.width,
        height: params.height,
        num_inference_steps: params.steps,
        guidance_scale: params.guidanceScale,
        seed: params.seed
      }
    })
    
    return this.processImageResult(result)
  }
  
  async removeBackground(imageData: string): Promise<BackgroundRemovalResult> {
    const result = await replicate.run(REPLICATE_MODELS.BACKGROUND_REMOVAL, {
      input: { image: imageData }
    })
    
    return {
      processedImage: result,
      maskImage: null, // Bria model doesn't provide mask
      processingTime: Date.now() - startTime
    }
  }
  
  async upscaleImage(params: UpscaleParams): Promise<UpscaleResult> {
    const result = await replicate.run(REPLICATE_MODELS.IMAGE_UPSCALING, {
      input: {
        image: params.imageData,
        scale: params.scaleFactor
      }
    })
    
    return {
      upscaledImage: result,
      originalDimensions: params.originalDimensions,
      newDimensions: {
        width: params.originalDimensions.width * params.scaleFactor,
        height: params.originalDimensions.height * params.scaleFactor
      }
    }
  }
}
```

#### API Route Implementation
```typescript
// app/api/ai/replicate/generate-image/route.ts
export async function POST(request: Request) {
  try {
    // Authentication and rate limiting
    const session = await getSession(request)
    if (!session) {
      return new Response('Unauthorized', { status: 401 })
    }
    
    // Input validation
    const body = await request.json()
    const params = imageGenerationSchema.parse(body)
    
    // Execute model
    const replicateClient = new ReplicateClient()
    const result = await replicateClient.generateImage(params)
    
    // Return result
    return Response.json({
      success: true,
      data: result,
      metadata: {
        model: REPLICATE_MODELS.IMAGE_GENERATION,
        processingTime: result.processingTime,
        cost: result.estimatedCost
      }
    })
    
  } catch (error) {
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
```

### OpenAI Integration
**Purpose**: Natural language processing and reasoning

#### GPT-4o Usage Patterns
```typescript
class OpenAIClient {
  // Request analysis and routing
  async analyzeRequest(request: string, context: RequestContext): Promise<RouteAnalysis> {
    return await generateObject({
      model: openai('gpt-4o'),
      schema: routeAnalysisSchema,
      prompt: this.buildAnalysisPrompt(request, context)
    })
  }
  
  // Multi-step workflow planning
  async planWorkflow(request: string, availableTools: string[]): Promise<WorkflowPlan> {
    return await generateObject({
      model: openai('gpt-4o'),
      schema: workflowPlanSchema,
      prompt: this.buildWorkflowPrompt(request, availableTools)
    })
  }
  
  // Parameter inference for tools
  async inferParameters(
    toolName: string,
    toolDescription: string,
    request: string,
    schema: z.ZodSchema
  ): Promise<unknown> {
    return await generateObject({
      model: openai('gpt-4o'),
      schema,
      prompt: this.buildParameterPrompt(toolName, toolDescription, request)
    })
  }
}
```

## Canvas Bridge System

### AI-Canvas Communication
**File Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/tools/canvas-bridge.ts`
**Purpose**: Bridge between AI system and canvas operations

#### Context Interface
```typescript
export interface CanvasContext {
  // Canvas instance
  canvas: Canvas
  
  // Targeting information
  targetingMode: 'all-images' | 'selected-images' | 'active-layer'
  targetImages: FabricImage[]
  
  // Selection system
  selectionManager: LayerAwareSelectionManager
  
  // Canvas analysis
  canvasAnalysis: {
    dimensions: { width: number; height: number }
    hasContent: boolean
    objectCount: number
    lastAnalyzedAt: number
  }
  
  // User preferences
  userPreferences: {
    autoApprovalThreshold: number
    qualityPreference: 'speed' | 'quality'
    defaultExportFormat: ExportFormat
  }
}
```

#### Context Builder
```typescript
class CanvasContextBuilder {
  static async build(canvasStore: CanvasStore): Promise<CanvasContext> {
    const canvas = canvasStore.fabricCanvas
    if (!canvas) {
      throw new Error('Canvas not available')
    }
    
    // Analyze current canvas state
    const analysis = await this.analyzeCanvas(canvas)
    
    // Determine targeting mode and get target images
    const { targetingMode, targetImages } = await this.getTargetingInfo(canvas)
    
    // Get selection manager
    const selectionManager = canvasStore.selectionManager
    if (!selectionManager) {
      throw new Error('Selection manager not available')
    }
    
    return {
      canvas,
      targetingMode,
      targetImages,
      selectionManager,
      canvasAnalysis: analysis,
      userPreferences: this.getUserPreferences()
    }
  }
  
  private static async analyzeCanvas(canvas: Canvas): Promise<CanvasAnalysis> {
    const objects = canvas.getObjects()
    const images = objects.filter(obj => obj.type === 'image')
    
    return {
      dimensions: {
        width: canvas.getWidth(),
        height: canvas.getHeight()
      },
      hasContent: objects.length > 0,
      objectCount: objects.length,
      imageCount: images.length,
      hasSelection: canvas.getActiveObject() !== null,
      lastAnalyzedAt: Date.now()
    }
  }
}
```

## AI Chat Interface

### Enhanced Chat Component
**File Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/components/editor/Panels/AIChat/EnhancedAIChat.tsx`
**Purpose**: User interface for AI interactions

#### Features
- **Natural Language Input**: Text input with intent recognition
- **Agent Mode Selection**: Choose between different AI agents
- **Workflow Visualization**: Display step-by-step operations
- **Confidence Indicators**: Show AI confidence levels
- **Auto-Approval Settings**: User-configurable approval thresholds
- **Operation History**: Track all AI operations

#### Chat Interface Implementation
```typescript
export function EnhancedAIChat() {
  const [input, setInput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentWorkflow, setCurrentWorkflow] = useState<WorkflowState | null>(null)
  
  const handleSubmit = async (message: string) => {
    setIsProcessing(true)
    
    try {
      // Build canvas context
      const context = await CanvasContextBuilder.build(useCanvasStore.getState())
      
      // Send to AI system
      const result = await processAIRequest(message, context)
      
      // Handle result based on type
      if (result.requiresApproval) {
        setCurrentWorkflow(result.workflow)
      } else {
        await executeWorkflow(result.workflow)
      }
      
    } catch (error) {
      console.error('AI processing error:', error)
    } finally {
      setIsProcessing(false)
    }
  }
  
  return (
    <div className="ai-chat-panel">
      <AgentModeToggle />
      <ChatHistory />
      <WorkflowDisplay workflow={currentWorkflow} />
      <ConfidenceIndicator />
      <ChatInput 
        onSubmit={handleSubmit}
        disabled={isProcessing}
        placeholder="Describe what you want to do with your image..."
      />
      <AutoApprovalSettings />
    </div>
  )
}
```

## Performance Optimization

### AI Request Caching
```typescript
class AIRequestCache {
  private cache = new Map<string, CachedResult>()
  private maxCacheSize = 100
  
  async get(key: string): Promise<CachedResult | null> {
    const cached = this.cache.get(key)
    if (cached && !this.isExpired(cached)) {
      return cached
    }
    return null
  }
  
  set(key: string, result: CachedResult): void {
    if (this.cache.size >= this.maxCacheSize) {
      this.evictOldest()
    }
    
    this.cache.set(key, {
      ...result,
      timestamp: Date.now()
    })
  }
  
  private generateCacheKey(request: string, context: CanvasContext): string {
    return crypto
      .createHash('sha256')
      .update(JSON.stringify({ request, context: this.serializeContext(context) }))
      .digest('hex')
  }
}
```

### Debounced Parameter Inference
```typescript
class DebouncedParameterInference {
  private debounceMap = new Map<string, NodeJS.Timeout>()
  
  async inferWithDebounce(
    toolName: string,
    request: string,
    context: CanvasContext,
    debounceMs = 300
  ): Promise<ToolParameters> {
    
    return new Promise((resolve) => {
      // Clear existing timeout for this tool
      const existingTimeout = this.debounceMap.get(toolName)
      if (existingTimeout) {
        clearTimeout(existingTimeout)
      }
      
      // Set new timeout
      const timeout = setTimeout(async () => {
        const params = await this.inferParameters(toolName, request, context)
        resolve(params)
        this.debounceMap.delete(toolName)
      }, debounceMs)
      
      this.debounceMap.set(toolName, timeout)
    })
  }
}
```

This comprehensive AI integration architecture enables FotoFun to provide natural language control over all 32+ tools while maintaining the precision and power of manual editing. The system's intelligence grows with usage, learning user preferences and improving confidence scoring over time.