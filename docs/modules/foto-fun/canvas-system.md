# FotoFun - Canvas System Architecture

## Overview

FotoFun's canvas system is built on **Fabric.js v6** and provides a professional-grade image editing experience with advanced selection, rendering, and manipulation capabilities. The system is designed for extensibility, performance, and seamless AI integration.

## Core Architecture

### Canvas Manager (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/canvas/CanvasManager.ts`)

The central coordinator for all canvas operations:

```typescript
class CanvasManager {
  private canvas: Canvas
  private selectionManager: LayerAwareSelectionManager
  private clipboardManager: ClipboardManager
  private performanceMonitor: PerformanceMonitor
  
  // Canvas lifecycle management
  async initialize(element: HTMLCanvasElement, options: CanvasOptions): Promise<void>
  dispose(): void
  
  // Object management
  addObject(object: FabricObject): void
  removeObject(object: FabricObject): void
  getObjects(): FabricObject[]
  
  // Rendering control
  renderAll(): void
  renderSelection(): void
  setViewport(transform: Transform): void
}
```

### Canvas Store Integration (`/Users/<USER>/Projects/own/assistant/foto-fun/store/canvasStore.ts`)

```typescript
interface CanvasStore {
  // Canvas instances
  fabricCanvas: Canvas | null
  
  // Canvas properties
  zoom: number
  isPanning: boolean
  
  // Document properties
  width: number
  height: number
  backgroundColor: string
  
  // Viewport properties
  viewportWidth: number
  viewportHeight: number
  
  // Initialization management
  isReady: boolean
  initializationError: Error | null
  initializationPromise: Promise<void> | null
  
  // Actions
  initCanvas: (element: HTMLCanvasElement, width: number, height: number) => Promise<void>
  waitForReady: () => Promise<void>
  hasContent: () => boolean
}
```

## Selection System

### Layer-Aware Selection Manager (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/selection/LayerAwareSelectionManager.ts`)

Advanced selection system with pixel-perfect accuracy:

```typescript
class LayerAwareSelectionManager {
  private canvas: Canvas
  private pixelCache: ObjectPixelCache
  private currentSelection: Selection | null
  
  // Selection creation methods
  createRectangleSelection(bounds: Rectangle): Selection
  createEllipseSelection(bounds: Rectangle): Selection
  createLassoSelection(points: Point[]): Selection
  createMagicWandSelection(point: Point, tolerance: number): Selection
  
  // Selection manipulation
  addToSelection(selection: Selection): void
  subtractFromSelection(selection: Selection): void
  intersectWithSelection(selection: Selection): void
  
  // Selection queries
  isPointInSelection(point: Point): boolean
  getSelectionBounds(): Rectangle | null
  getSelectionMask(): ImageData | null
}
```

### Selection Renderer (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/selection/SelectionRenderer.ts`)

Handles visual representation of selections:

```typescript
class SelectionRenderer {
  private canvas: Canvas
  private selectionManager: LayerAwareSelectionManager
  private marchingAnts: MarchingAntsAnimation
  
  // Rendering methods
  renderSelection(): void
  renderMarchingAnts(): void
  renderSelectionBounds(): void
  
  // Animation control
  startMarchingAnts(): void
  stopMarchingAnts(): void
  
  // Overlay management
  createSelectionOverlay(): FabricObject
  updateSelectionOverlay(): void
  clearSelectionOverlay(): void
}
```

### Object Pixel Cache (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/selection/ObjectPixelCache.ts`)

Optimized pixel-level selection operations:

```typescript
class ObjectPixelCache {
  private cache: Map<string, ImageData>
  private dirtyObjects: Set<string>
  
  // Cache management
  updatePixelMap(): void
  markDirty(objectId: string): void
  updatePixelMapIfNeeded(): void
  
  // Pixel queries
  getPixelAt(point: Point): PixelData | null
  isPixelTransparent(point: Point): boolean
  findConnectedPixels(point: Point, tolerance: number): Point[]
}
```

## Canvas Operations

### Viewport Management

```typescript
// Zoom operations
interface ZoomOperations {
  setZoom(zoom: number): void
  zoomIn(): void
  zoomOut(): void
  zoomToFit(): void
  zoomToActual(): void
}

// Pan operations
interface PanOperations {
  startPanning(e: TPointerEventInfo<MouseEvent>): void
  pan(e: TPointerEventInfo<MouseEvent>): void
  endPanning(): void
  centerContent(): void
}

// Viewport constants
const ZOOM_LEVELS = [25, 33, 50, 67, 75, 100, 150, 200, 300, 400, 500]
const MIN_ZOOM = 25
const MAX_ZOOM = 500
const DEFAULT_ZOOM = 100
```

### Event Handling

```typescript
// Mouse wheel zoom
canvas.on('mouse:wheel', (opt: TPointerEventInfo<WheelEvent>) => {
  const delta = opt.e.deltaY
  let zoom = canvas.getZoom()
  zoom *= 0.999 ** delta
  
  // Clamp zoom
  zoom = Math.max(MIN_ZOOM / 100, Math.min(MAX_ZOOM / 100, zoom))
  
  canvas.zoomToPoint(
    new Point(opt.e.offsetX, opt.e.offsetY),
    zoom
  )
  
  opt.e.preventDefault()
  opt.e.stopPropagation()
})

// Object lifecycle events
canvas.on('object:added', (e: any) => {
  const obj = e.target
  if (obj && !obj.excludeFromExport) {
    // Update object registry
    objectRegistry.updatePixelMap()
    // Update filter states
    updateFilterStates()
  }
})
```

## Performance Optimizations

### Object Registry Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/objectRegistryStore.ts`)

```typescript
interface ObjectRegistryStore {
  // Object tracking
  objects: Map<string, FabricObject>
  pixelMap: ImageData | null
  dirtyObjects: Set<string>
  
  // Performance tracking
  lastUpdateTime: number
  updateCount: number
  
  // Update methods
  updatePixelMap(): void
  markDirty(objectId: string): void
  updatePixelMapIfNeeded(): void
  
  // Cleanup
  clearCache(): void
  dispose(): void
}
```

### Debounced Updates

```typescript
// Debounced pixel map updates
canvas.on('object:modified', (e: any) => {
  const obj = e.target
  if (obj && obj.get('id') && !obj.excludeFromExport) {
    objectRegistry.markDirty(obj.get('id') as string)
    
    // Debounce modifications
    if (window.pixelMapUpdateTimeout) {
      clearTimeout(window.pixelMapUpdateTimeout)
    }
    
    window.pixelMapUpdateTimeout = setTimeout(() => {
      objectRegistry.updatePixelMapIfNeeded()
      updateFilterStates()
    }, 300) // 300ms debounce
  }
})
```

### Memory Management

```typescript
// Canvas disposal
disposeCanvas: () => {
  const { fabricCanvas, cleanupSelection } = get()
  
  // Cleanup selection system
  cleanupSelection()
  
  // Clear pending updates
  if (window.pixelMapUpdateTimeout) {
    clearTimeout(window.pixelMapUpdateTimeout)
    delete window.pixelMapUpdateTimeout
  }
  
  // Dispose canvas
  if (fabricCanvas) {
    fabricCanvas.dispose()
    set({ fabricCanvas: null })
  }
}
```

## Filter System Integration

### Filter Pipeline (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/filters/FilterPipeline.ts`)

```typescript
class FilterPipeline {
  private filters: Filter[] = []
  
  // Filter management
  addFilter(filter: Filter): void
  removeFilter(filterId: string): void
  updateFilter(filterId: string, params: FilterParams): void
  
  // Execution
  async apply(image: FabricImage): Promise<FabricImage>
  async applyToSelection(selection: Selection): Promise<void>
  
  // State management
  getFilterState(): FilterState
  setFilterState(state: FilterState): void
}
```

### Selection-Aware Filters (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/filters/SelectionAwareFilter.ts`)

```typescript
abstract class SelectionAwareFilter extends Filter {
  // Apply filter only to selected areas
  async applyToSelection(
    image: FabricImage, 
    selection: Selection, 
    params: FilterParams
  ): Promise<void> {
    const mask = selection.getMask()
    const filteredImage = await this.apply(image, params)
    
    // Blend filtered image with original using mask
    return this.blendWithMask(image, filteredImage, mask)
  }
  
  // Blend implementation
  protected blendWithMask(
    original: FabricImage,
    filtered: FabricImage,
    mask: ImageData
  ): FabricImage {
    // Pixel-level blending based on mask
    const result = original.clone()
    const originalData = original.getImageData()
    const filteredData = filtered.getImageData()
    
    // Apply mask blending
    for (let i = 0; i < mask.data.length; i += 4) {
      const alpha = mask.data[i + 3] / 255
      if (alpha > 0) {
        result.data[i] = originalData.data[i] * (1 - alpha) + filteredData.data[i] * alpha
        result.data[i + 1] = originalData.data[i + 1] * (1 - alpha) + filteredData.data[i + 1] * alpha
        result.data[i + 2] = originalData.data[i + 2] * (1 - alpha) + filteredData.data[i + 2] * alpha
      }
    }
    
    return result
  }
}
```

## Canvas-AI Integration

### Canvas Bridge (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/tools/canvas-bridge.ts`)

```typescript
interface CanvasContext {
  canvas: Canvas
  targetingMode: 'all-images' | 'selected-objects' | 'visible-selection'
  targetImages: FabricImage[]
  selection?: Selection
  metadata?: CanvasMetadata
}

class CanvasBridge {
  // Context creation
  static createContext(canvas: Canvas, options: ContextOptions): CanvasContext
  
  // Image targeting
  static getTargetImages(canvas: Canvas, mode: TargetingMode): FabricImage[]
  static getSelectedImages(canvas: Canvas): FabricImage[]
  static getAllImages(canvas: Canvas): FabricImage[]
  
  // Selection utilities
  static hasSelection(canvas: Canvas): boolean
  static getSelectionBounds(canvas: Canvas): Rectangle | null
  static getSelectionMask(canvas: Canvas): ImageData | null
  
  // Canvas analysis
  static analyzeCanvas(canvas: Canvas): CanvasAnalysis
  static getDimensions(canvas: Canvas): { width: number; height: number }
  static getObjectCount(canvas: Canvas): number
  static hasContent(canvas: Canvas): boolean
}
```

### AI Tool Integration

```typescript
// AI tools can query canvas state
interface AICanvasQueries {
  // Selection queries
  hasSelection(): boolean
  getSelectionBounds(): Rectangle | null
  getSelectionPixelCount(): number
  
  // Object queries
  getImageCount(): number
  getObjectTypes(): string[]
  getCanvasDimensions(): { width: number; height: number }
  
  // State queries
  canUndo(): boolean
  canRedo(): boolean
  hasUnsavedChanges(): boolean
}
```

## Clipboard System

### Clipboard Manager (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/clipboard/ClipboardManager.ts`)

```typescript
class ClipboardManager {
  private canvas: Canvas
  private selectionManager: LayerAwareSelectionManager
  private clipboard: ClipboardData | null = null
  
  // Clipboard operations
  async copy(): Promise<void>
  async cut(): Promise<void>
  async paste(): Promise<void>
  
  // System clipboard integration
  async copyToSystemClipboard(): Promise<void>
  async pasteFromSystemClipboard(): Promise<void>
  
  // Data formats
  async copyAsImage(): Promise<Blob>
  async copyAsJSON(): Promise<string>
  async pasteFromImage(blob: Blob): Promise<void>
  async pasteFromJSON(json: string): Promise<void>
}
```

## Text System Integration

### Text Rendering

```typescript
// Text tool integration with canvas
interface TextCanvasIntegration {
  // Text creation
  createTextObject(text: string, options: TextOptions): FabricText
  
  // Text editing
  enterTextEditMode(textObject: FabricText): void
  exitTextEditMode(): void
  
  // Text effects
  applyTextEffect(textObject: FabricText, effect: TextEffect): void
  
  // Font management
  loadFont(fontFamily: string): Promise<void>
  getFontMetrics(fontFamily: string, fontSize: number): FontMetrics
}
```

### Character Panel Integration (`/Users/<USER>/Projects/own/assistant/foto-fun/components/editor/Panels/CharacterPanel/`)

```typescript
// Character panel components
interface CharacterPanelComponents {
  FontSelector: React.FC<FontSelectorProps>
  FontSizeInput: React.FC<FontSizeInputProps>
  FontStyleButtons: React.FC<FontStyleButtonsProps>
  LetterSpacingControl: React.FC<LetterSpacingControlProps>
  LineHeightControl: React.FC<LineHeightControlProps>
  TextColorPicker: React.FC<TextColorPickerProps>
}
```

## Layer System

### Layer Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/layerStore.ts`)

```typescript
interface LayerStore {
  // Layer data
  layers: Layer[]
  activeLayerId: string | null
  
  // Layer operations
  createLayer(name: string, type: LayerType): Layer
  deleteLayer(layerId: string): void
  duplicateLayer(layerId: string): Layer
  reorderLayers(layerIds: string[]): void
  
  // Layer properties
  setLayerVisibility(layerId: string, visible: boolean): void
  setLayerOpacity(layerId: string, opacity: number): void
  setLayerBlendMode(layerId: string, blendMode: BlendMode): void
  
  // Layer selection
  setActiveLayer(layerId: string): void
  selectMultipleLayers(layerIds: string[]): void
}
```

### Layer-Canvas Synchronization

```typescript
// Keep layers in sync with canvas objects
canvas.on('object:added', (e: any) => {
  const obj = e.target
  if (obj && !obj.isTemp) {
    const layer = createLayerFromObject(obj)
    layerStore.getState().addLayer(layer)
  }
})

canvas.on('object:removed', (e: any) => {
  const obj = e.target
  if (obj && !obj.isTemp) {
    const layerId = obj.get('id')
    layerStore.getState().deleteLayer(layerId)
  }
})
```

## Performance Monitoring

### Performance Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/performanceStore.ts`)

```typescript
interface PerformanceStore {
  // Metrics
  renderTime: number
  selectionTime: number
  filterTime: number
  toolExecutionTime: number
  
  // Tracking methods
  trackRender(time: number): void
  trackSelection(time: number): void
  trackFilter(time: number): void
  trackToolExecution(time: number): void
  
  // Analysis
  getAverageRenderTime(): number
  getPerformanceReport(): PerformanceReport
  
  // Optimization
  suggestOptimizations(): OptimizationSuggestion[]
}
```

### Performance Optimization Strategies

```typescript
// Render optimization
const optimizeRendering = () => {
  // Use requestAnimationFrame for smooth updates
  let renderScheduled = false
  
  const scheduleRender = () => {
    if (!renderScheduled) {
      renderScheduled = true
      requestAnimationFrame(() => {
        canvas.renderAll()
        renderScheduled = false
      })
    }
  }
  
  // Batch multiple updates
  return scheduleRender
}

// Selection optimization
const optimizeSelection = () => {
  // Cache selection calculations
  const selectionCache = new Map<string, Selection>()
  
  const getCachedSelection = (key: string, calculator: () => Selection) => {
    if (!selectionCache.has(key)) {
      selectionCache.set(key, calculator())
    }
    return selectionCache.get(key)!
  }
  
  return getCachedSelection
}
```

## Future Enhancements

### WebGL Migration Considerations

```typescript
// Planned WebGL architecture
interface WebGLCanvasSystem {
  // WebGL context management
  initWebGL(): Promise<WebGL2RenderingContext>
  
  // Shader programs
  loadShaders(): Promise<ShaderProgram[]>
  
  // GPU-accelerated operations
  renderWithGPU(): void
  applyFiltersWithGPU(filters: Filter[]): void
  
  // Performance benefits
  // - Faster rendering for large images
  // - Parallel filter processing
  // - Better selection performance
}
```

### Real-time Collaboration

```typescript
// Planned collaboration features
interface CollaborationCanvas {
  // User cursors
  showUserCursors(users: CollaborationUser[]): void
  
  // Real-time updates
  broadcastCanvasChange(change: CanvasChange): void
  applyRemoteChange(change: CanvasChange): void
  
  // Conflict resolution
  resolveConflicts(changes: CanvasChange[]): CanvasChange[]
  
  // Operational transforms
  transformOperation(op: Operation, contextOp: Operation): Operation
}
```

### Advanced Selection Features

```typescript
// Future selection enhancements
interface AdvancedSelection {
  // AI-powered selection
  aiSmartSelect(prompt: string): Promise<Selection>
  
  // Edge detection
  edgeDetectSelection(tolerance: number): Selection
  
  // Color range selection
  selectColorRange(color: Color, tolerance: number): Selection
  
  // Shape detection
  detectShapes(): ShapeSelection[]
  
  // Selection refinement
  refineSelectionEdges(): void
  featherSelection(radius: number): void
}
```

## Canvas System Benefits

### 1. Professional-Grade Capabilities
- **Pixel-perfect selections**: Advanced algorithms for precise selection
- **Layer-aware operations**: Tools understand layer hierarchy
- **Non-destructive editing**: Preserve original image data
- **Infinite undo/redo**: Complete operation history

### 2. Performance Optimizations
- **Efficient rendering**: Optimized canvas updates
- **Smart caching**: Reduce redundant calculations
- **Memory management**: Proper resource cleanup
- **Debounced updates**: Prevent UI blocking

### 3. AI Integration
- **Context-aware tools**: AI understands canvas state
- **Natural language control**: Describe operations in plain English
- **Intelligent targeting**: Automatic object selection
- **Workflow optimization**: AI suggests best practices

### 4. Extensibility
- **Plugin architecture**: Easy to add new tools
- **Custom filters**: Extensible filter system
- **Tool adapters**: Bridge canvas tools to AI
- **Event system**: Rich event handling for extensions

This canvas system provides the foundation for a professional-grade photo editing experience while maintaining the performance and extensibility needed for modern web applications.