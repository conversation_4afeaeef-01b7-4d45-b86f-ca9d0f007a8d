# FotoFun - Extensibility Architecture

## Overview

FotoFun is designed with **extensibility** as a core principle, enabling seamless integration of new tools, AI models, plugins, and customizations. The architecture follows **plugin-based patterns**, **dependency injection**, and **event-driven design** to ensure new features can be added without modifying core systems.

## Plugin Architecture

### Core Plugin System

```typescript
// File: lib/plugins/core.ts
interface Plugin {
  id: string
  name: string
  version: string
  description: string
  author: string
  dependencies: string[]
  
  // Plugin lifecycle
  activate(): Promise<void>
  deactivate(): Promise<void>
  
  // Plugin capabilities
  provides: PluginCapability[]
  requires: PluginDependency[]
  
  // Plugin manifest
  manifest: PluginManifest
}

interface PluginCapability {
  type: 'tool' | 'filter' | 'ai-model' | 'format' | 'ui-component' | 'workflow'
  name: string
  version: string
  exports: Record<string, unknown>
}

interface PluginDependency {
  name: string
  version: string
  required: boolean
}

interface PluginManifest {
  permissions: PluginPermission[]
  resources: PluginResource[]
  hooks: PluginHook[]
  ui: PluginUI[]
}

// Plugin registry
class PluginRegistry {
  private plugins = new Map<string, Plugin>()
  private activePlugins = new Set<string>()
  private dependencyGraph = new Map<string, string[]>()
  
  public async registerPlugin(plugin: Plugin): Promise<void> {
    // Validate plugin
    await this.validatePlugin(plugin)
    
    // Check dependencies
    await this.checkDependencies(plugin)
    
    // Register plugin
    this.plugins.set(plugin.id, plugin)
    
    // Update dependency graph
    this.updateDependencyGraph(plugin)
    
    console.log(`Plugin registered: ${plugin.name} v${plugin.version}`)
  }
  
  public async activatePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) throw new Error(`Plugin not found: ${pluginId}`)
    
    // Activate dependencies first
    await this.activateDependencies(plugin)
    
    // Activate plugin
    await plugin.activate()
    this.activePlugins.add(pluginId)
    
    // Register capabilities
    await this.registerCapabilities(plugin)
    
    console.log(`Plugin activated: ${plugin.name}`)
  }
  
  public async deactivatePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) throw new Error(`Plugin not found: ${pluginId}`)
    
    // Check if other plugins depend on this one
    const dependents = this.getDependents(pluginId)
    if (dependents.length > 0) {
      throw new Error(`Cannot deactivate plugin ${pluginId}: required by ${dependents.join(', ')}`)
    }
    
    // Deactivate plugin
    await plugin.deactivate()
    this.activePlugins.delete(pluginId)
    
    // Unregister capabilities
    await this.unregisterCapabilities(plugin)
    
    console.log(`Plugin deactivated: ${plugin.name}`)
  }
  
  private async validatePlugin(plugin: Plugin): Promise<void> {
    // Validate plugin structure
    if (!plugin.id || !plugin.name || !plugin.version) {
      throw new Error('Invalid plugin: missing required fields')
    }
    
    // Validate semantic version
    if (!this.isValidSemver(plugin.version)) {
      throw new Error(`Invalid plugin version: ${plugin.version}`)
    }
    
    // Validate permissions
    await this.validatePermissions(plugin.manifest.permissions)
  }
  
  private async checkDependencies(plugin: Plugin): Promise<void> {
    for (const dep of plugin.requires) {
      const depPlugin = this.plugins.get(dep.name)
      
      if (!depPlugin) {
        if (dep.required) {
          throw new Error(`Required dependency not found: ${dep.name}`)
        }
        continue
      }
      
      // Check version compatibility
      if (!this.isVersionCompatible(depPlugin.version, dep.version)) {
        throw new Error(`Incompatible dependency version: ${dep.name}`)
      }
    }
  }
}
```

### Tool Plugin System

```typescript
// File: lib/plugins/tools.ts
abstract class ToolPlugin implements Plugin {
  abstract id: string
  abstract name: string
  abstract version: string
  abstract description: string
  abstract author: string
  
  dependencies: string[] = []
  provides: PluginCapability[] = []
  requires: PluginDependency[] = []
  
  // Tool-specific properties
  abstract toolType: ToolType
  abstract toolCategory: ToolCategory
  abstract toolIcon: string
  abstract toolShortcut?: string
  
  // Tool implementation
  abstract createTool(): BaseTool
  abstract getToolAdapter(): BaseToolAdapter
  
  // Plugin lifecycle
  public async activate(): Promise<void> {
    // Register tool in tool registry
    const toolRegistry = getToolRegistry()
    const tool = this.createTool()
    const adapter = this.getToolAdapter()
    
    await toolRegistry.registerTool(tool)
    await toolRegistry.registerAdapter(adapter)
    
    console.log(`Tool plugin activated: ${this.name}`)
  }
  
  public async deactivate(): Promise<void> {
    // Unregister tool from tool registry
    const toolRegistry = getToolRegistry()
    
    await toolRegistry.unregisterTool(this.id)
    await toolRegistry.unregisterAdapter(this.id)
    
    console.log(`Tool plugin deactivated: ${this.name}`)
  }
}

// Example tool plugin
class CustomBrushPlugin extends ToolPlugin {
  id = 'custom-brush'
  name = 'Custom Brush Tool'
  version = '1.0.0'
  description = 'Advanced brush tool with custom textures'
  author = 'FotoFun Community'
  
  toolType = 'drawing' as ToolType
  toolCategory = 'brush' as ToolCategory
  toolIcon = 'brush-custom'
  toolShortcut = 'B'
  
  manifest: PluginManifest = {
    permissions: ['canvas-access', 'file-access'],
    resources: [
      { type: 'texture', path: './textures/brush-*.png' },
      { type: 'shader', path: './shaders/brush.glsl' }
    ],
    hooks: ['canvas-render', 'tool-select'],
    ui: [
      { type: 'tool-options', component: 'BrushOptions' },
      { type: 'tool-button', component: 'BrushButton' }
    ]
  }
  
  createTool(): BaseTool {
    return new CustomBrushTool({
      id: this.id,
      name: this.name,
      icon: this.toolIcon,
      shortcut: this.toolShortcut
    })
  }
  
  getToolAdapter(): BaseToolAdapter {
    return new CustomBrushAdapter({
      toolId: this.id,
      capabilities: ['pressure-sensitive', 'texture-support']
    })
  }
}

// Tool registration
const customBrushPlugin = new CustomBrushPlugin()
await pluginRegistry.registerPlugin(customBrushPlugin)
await pluginRegistry.activatePlugin(customBrushPlugin.id)
```

### AI Model Plugin System

```typescript
// File: lib/plugins/ai-models.ts
abstract class AIModelPlugin implements Plugin {
  abstract id: string
  abstract name: string
  abstract version: string
  abstract description: string
  abstract author: string
  
  // AI model properties
  abstract modelType: AIModelType
  abstract modelCapabilities: AICapability[]
  abstract modelEndpoint: string
  abstract modelCost: ModelCost
  
  // Model implementation
  abstract createModel(): BaseAIModel
  abstract getModelAdapter(): BaseAIModelAdapter
  
  public async activate(): Promise<void> {
    // Register AI model
    const aiRegistry = getAIRegistry()
    const model = this.createModel()
    const adapter = this.getModelAdapter()
    
    await aiRegistry.registerModel(model)
    await aiRegistry.registerAdapter(adapter)
    
    console.log(`AI model plugin activated: ${this.name}`)
  }
  
  public async deactivate(): Promise<void> {
    // Unregister AI model
    const aiRegistry = getAIRegistry()
    
    await aiRegistry.unregisterModel(this.id)
    await aiRegistry.unregisterAdapter(this.id)
    
    console.log(`AI model plugin deactivated: ${this.name}`)
  }
}

// Example AI model plugin
class StableDiffusionPlugin extends AIModelPlugin {
  id = 'stable-diffusion-xl'
  name = 'Stable Diffusion XL'
  version = '1.0.0'
  description = 'High-quality image generation model'
  author = 'Stability AI'
  
  modelType = 'image-generation' as AIModelType
  modelCapabilities = ['text-to-image', 'image-to-image', 'inpainting'] as AICapability[]
  modelEndpoint = 'https://api.replicate.com/v1/predictions'
  modelCost = {
    inputTokens: 0.0001,
    outputTokens: 0.0002,
    imageGeneration: 0.012
  }
  
  manifest: PluginManifest = {
    permissions: ['network-access', 'api-access'],
    resources: [
      { type: 'model-config', path: './config/stable-diffusion.json' }
    ],
    hooks: ['ai-request', 'image-generation'],
    ui: [
      { type: 'ai-panel', component: 'StableDiffusionPanel' }
    ]
  }
  
  createModel(): BaseAIModel {
    return new StableDiffusionModel({
      id: this.id,
      name: this.name,
      endpoint: this.modelEndpoint,
      capabilities: this.modelCapabilities,
      cost: this.modelCost
    })
  }
  
  getModelAdapter(): BaseAIModelAdapter {
    return new StableDiffusionAdapter({
      modelId: this.id,
      apiKey: process.env.REPLICATE_API_TOKEN,
      rateLimits: {
        requestsPerMinute: 60,
        requestsPerHour: 1000
      }
    })
  }
}
```

## Hook System

### Core Hook Infrastructure

```typescript
// File: lib/hooks/core.ts
type HookCallback<T = any> = (data: T) => Promise<T | void> | T | void

interface Hook {
  name: string
  priority: number
  callback: HookCallback
  plugin: string
}

class HookSystem {
  private hooks = new Map<string, Hook[]>()
  
  public registerHook(
    name: string,
    callback: HookCallback,
    options: HookOptions = {}
  ): void {
    const hook: Hook = {
      name,
      callback,
      priority: options.priority || 10,
      plugin: options.plugin || 'core'
    }
    
    if (!this.hooks.has(name)) {
      this.hooks.set(name, [])
    }
    
    const hooks = this.hooks.get(name)!
    hooks.push(hook)
    
    // Sort by priority (lower number = higher priority)
    hooks.sort((a, b) => a.priority - b.priority)
  }
  
  public async executeHook<T>(name: string, data: T): Promise<T> {
    const hooks = this.hooks.get(name)
    if (!hooks) return data
    
    let result = data
    
    for (const hook of hooks) {
      try {
        const hookResult = await hook.callback(result)
        if (hookResult !== undefined) {
          result = hookResult
        }
      } catch (error) {
        console.error(`Hook error in ${hook.plugin}:${name}:`, error)
        // Continue with other hooks
      }
    }
    
    return result
  }
  
  public unregisterHook(name: string, plugin: string): void {
    const hooks = this.hooks.get(name)
    if (!hooks) return
    
    const filtered = hooks.filter(hook => hook.plugin !== plugin)
    this.hooks.set(name, filtered)
  }
}

// Global hook system
export const hookSystem = new HookSystem()

// Hook registration helper
export const registerHook = (
  name: string,
  callback: HookCallback,
  options?: HookOptions
): void => {
  hookSystem.registerHook(name, callback, options)
}

// Hook execution helper
export const executeHook = async <T>(name: string, data: T): Promise<T> => {
  return hookSystem.executeHook(name, data)
}
```

### Built-in Hooks

```typescript
// File: lib/hooks/built-in.ts
// Canvas hooks
export const CANVAS_HOOKS = {
  BEFORE_RENDER: 'canvas:before-render',
  AFTER_RENDER: 'canvas:after-render',
  OBJECT_ADDED: 'canvas:object-added',
  OBJECT_REMOVED: 'canvas:object-removed',
  SELECTION_CHANGED: 'canvas:selection-changed'
} as const

// Tool hooks
export const TOOL_HOOKS = {
  BEFORE_ACTIVATE: 'tool:before-activate',
  AFTER_ACTIVATE: 'tool:after-activate',
  BEFORE_DEACTIVATE: 'tool:before-deactivate',
  AFTER_DEACTIVATE: 'tool:after-deactivate',
  TOOL_EXECUTED: 'tool:executed'
} as const

// AI hooks
export const AI_HOOKS = {
  BEFORE_REQUEST: 'ai:before-request',
  AFTER_REQUEST: 'ai:after-request',
  REQUEST_FAILED: 'ai:request-failed',
  MODEL_LOADED: 'ai:model-loaded'
} as const

// File hooks
export const FILE_HOOKS = {
  BEFORE_LOAD: 'file:before-load',
  AFTER_LOAD: 'file:after-load',
  BEFORE_SAVE: 'file:before-save',
  AFTER_SAVE: 'file:after-save',
  FORMAT_DETECTED: 'file:format-detected'
} as const

// Example hook usage
registerHook(CANVAS_HOOKS.BEFORE_RENDER, (canvasData) => {
  // Apply performance optimizations
  return optimizeCanvasForRender(canvasData)
}, { priority: 1 })

registerHook(TOOL_HOOKS.AFTER_ACTIVATE, (toolData) => {
  // Track tool usage
  trackToolUsage(toolData.toolId)
}, { priority: 5 })

registerHook(AI_HOOKS.BEFORE_REQUEST, (requestData) => {
  // Add authentication
  requestData.headers = {
    ...requestData.headers,
    'Authorization': `Bearer ${getAPIKey()}`
  }
  return requestData
}, { priority: 1 })
```

## Component Extension System

### Custom Component Registration

```typescript
// File: lib/components/extensions.ts
interface ComponentExtension {
  id: string
  name: string
  component: React.ComponentType<any>
  slot: ComponentSlot
  priority: number
  conditions?: ComponentCondition[]
}

type ComponentSlot = 
  | 'toolbar'
  | 'sidebar'
  | 'context-menu'
  | 'tool-options'
  | 'ai-panel'
  | 'status-bar'
  | 'modal'

interface ComponentCondition {
  type: 'tool-active' | 'selection-exists' | 'file-type' | 'user-permission'
  value: string
  operator: 'equals' | 'contains' | 'not-equals'
}

class ComponentExtensionRegistry {
  private extensions = new Map<ComponentSlot, ComponentExtension[]>()
  
  public registerComponent(extension: ComponentExtension): void {
    const slot = extension.slot
    
    if (!this.extensions.has(slot)) {
      this.extensions.set(slot, [])
    }
    
    const extensions = this.extensions.get(slot)!
    extensions.push(extension)
    
    // Sort by priority
    extensions.sort((a, b) => a.priority - b.priority)
    
    console.log(`Component registered: ${extension.name} in ${slot}`)
  }
  
  public getComponents(slot: ComponentSlot): ComponentExtension[] {
    const extensions = this.extensions.get(slot) || []
    
    // Filter by conditions
    return extensions.filter(ext => this.checkConditions(ext.conditions))
  }
  
  private checkConditions(conditions?: ComponentCondition[]): boolean {
    if (!conditions) return true
    
    return conditions.every(condition => {
      switch (condition.type) {
        case 'tool-active':
          const activeTool = getActiveTool()
          return this.checkOperator(activeTool, condition.value, condition.operator)
        
        case 'selection-exists':
          const hasSelection = getSelectionExists()
          return this.checkOperator(hasSelection.toString(), condition.value, condition.operator)
        
        case 'file-type':
          const fileType = getCurrentFileType()
          return this.checkOperator(fileType, condition.value, condition.operator)
        
        case 'user-permission':
          const hasPermission = getUserPermission(condition.value)
          return hasPermission
        
        default:
          return true
      }
    })
  }
  
  private checkOperator(actual: string, expected: string, operator: string): boolean {
    switch (operator) {
      case 'equals':
        return actual === expected
      case 'contains':
        return actual.includes(expected)
      case 'not-equals':
        return actual !== expected
      default:
        return false
    }
  }
}

// Global registry
export const componentRegistry = new ComponentExtensionRegistry()

// Component registration helper
export const registerComponent = (extension: ComponentExtension): void => {
  componentRegistry.registerComponent(extension)
}
```

### Dynamic Component Loading

```typescript
// File: components/ExtensibleContainer.tsx
interface ExtensibleContainerProps {
  slot: ComponentSlot
  fallback?: React.ReactNode
  props?: Record<string, any>
}

export const ExtensibleContainer: React.FC<ExtensibleContainerProps> = ({
  slot,
  fallback,
  props = {}
}) => {
  const [components, setComponents] = useState<ComponentExtension[]>([])
  
  useEffect(() => {
    const loadComponents = () => {
      const extensions = componentRegistry.getComponents(slot)
      setComponents(extensions)
    }
    
    loadComponents()
    
    // Listen for component updates
    const unsubscribe = componentRegistry.subscribe(slot, loadComponents)
    return unsubscribe
  }, [slot])
  
  if (components.length === 0) {
    return fallback ? <>{fallback}</> : null
  }
  
  return (
    <div className={`extensible-container extensible-${slot}`}>
      {components.map(extension => {
        const Component = extension.component
        return (
          <div key={extension.id} className="extension-wrapper">
            <Component {...props} />
          </div>
        )
      })}
    </div>
  )
}

// Usage in core components
export const Toolbar: React.FC = () => {
  return (
    <div className="toolbar">
      <div className="toolbar-core">
        {/* Core toolbar items */}
      </div>
      
      <ExtensibleContainer
        slot="toolbar"
        fallback={<div>No extensions</div>}
      />
    </div>
  )
}
```

## API Extension Points

### Custom API Endpoints

```typescript
// File: lib/api/extensions.ts
interface APIExtension {
  path: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  handler: (req: Request) => Promise<Response>
  middleware?: APIMiddleware[]
  permissions?: string[]
}

interface APIMiddleware {
  name: string
  execute: (req: Request, res: Response, next: () => void) => Promise<void>
}

class APIExtensionRegistry {
  private extensions = new Map<string, APIExtension>()
  
  public registerEndpoint(extension: APIExtension): void {
    const key = `${extension.method}:${extension.path}`
    
    if (this.extensions.has(key)) {
      throw new Error(`API endpoint already registered: ${key}`)
    }
    
    this.extensions.set(key, extension)
    console.log(`API endpoint registered: ${key}`)
  }
  
  public getEndpoint(method: string, path: string): APIExtension | null {
    const key = `${method}:${path}`
    return this.extensions.get(key) || null
  }
  
  public async handleRequest(req: Request): Promise<Response | null> {
    const url = new URL(req.url)
    const path = url.pathname
    const method = req.method
    
    const extension = this.getEndpoint(method, path)
    if (!extension) return null
    
    // Execute middleware
    for (const middleware of extension.middleware || []) {
      await middleware.execute(req, new Response(), () => {})
    }
    
    // Check permissions
    if (extension.permissions) {
      const hasPermission = await this.checkPermissions(req, extension.permissions)
      if (!hasPermission) {
        return new Response('Forbidden', { status: 403 })
      }
    }
    
    // Execute handler
    return extension.handler(req)
  }
  
  private async checkPermissions(req: Request, permissions: string[]): Promise<boolean> {
    // Implementation depends on auth system
    return true
  }
}

// Example API extension
const customAPIExtension: APIExtension = {
  path: '/api/custom-filter',
  method: 'POST',
  handler: async (req: Request) => {
    const { imageData, filterParams } = await req.json()
    
    // Apply custom filter
    const result = await applyCustomFilter(imageData, filterParams)
    
    return Response.json({ success: true, result })
  },
  middleware: [
    {
      name: 'rate-limit',
      execute: async (req, res, next) => {
        // Rate limiting logic
        next()
      }
    }
  ],
  permissions: ['custom-filter-access']
}

// Register API extension
apiExtensionRegistry.registerEndpoint(customAPIExtension)
```

## Theme & Styling Extensions

### Custom Theme System

```typescript
// File: lib/themes/extensions.ts
interface ThemeExtension {
  id: string
  name: string
  description: string
  colors: ThemeColors
  typography: ThemeTypography
  spacing: ThemeSpacing
  components: ThemeComponents
}

interface ThemeColors {
  primary: string
  secondary: string
  background: string
  surface: string
  text: string
  textSecondary: string
  border: string
  success: string
  warning: string
  error: string
}

interface ThemeTypography {
  fontFamily: string
  fontSize: {
    xs: string
    sm: string
    base: string
    lg: string
    xl: string
    '2xl': string
    '3xl': string
  }
  fontWeight: {
    normal: string
    medium: string
    bold: string
  }
}

interface ThemeSpacing {
  xs: string
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
}

interface ThemeComponents {
  button: ComponentTheme
  input: ComponentTheme
  toolbar: ComponentTheme
  sidebar: ComponentTheme
  canvas: ComponentTheme
}

interface ComponentTheme {
  base: string
  variants: Record<string, string>
  states: Record<string, string>
}

class ThemeExtensionRegistry {
  private themes = new Map<string, ThemeExtension>()
  private activeTheme: string = 'default'
  
  public registerTheme(theme: ThemeExtension): void {
    this.themes.set(theme.id, theme)
    console.log(`Theme registered: ${theme.name}`)
  }
  
  public setActiveTheme(themeId: string): void {
    const theme = this.themes.get(themeId)
    if (!theme) {
      throw new Error(`Theme not found: ${themeId}`)
    }
    
    this.activeTheme = themeId
    this.applyTheme(theme)
  }
  
  public getActiveTheme(): ThemeExtension | null {
    return this.themes.get(this.activeTheme) || null
  }
  
  private applyTheme(theme: ThemeExtension): void {
    // Apply CSS custom properties
    const root = document.documentElement
    
    // Colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })
    
    // Typography
    root.style.setProperty('--font-family', theme.typography.fontFamily)
    Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--font-size-${key}`, value)
    })
    
    // Spacing
    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value)
    })
    
    // Component styles
    Object.entries(theme.components).forEach(([component, styles]) => {
      root.style.setProperty(`--${component}-base`, styles.base)
      
      Object.entries(styles.variants).forEach(([variant, css]) => {
        root.style.setProperty(`--${component}-${variant}`, css)
      })
    })
  }
}

// Example theme extension
const darkTheme: ThemeExtension = {
  id: 'dark-pro',
  name: 'Dark Pro',
  description: 'Professional dark theme for photo editing',
  colors: {
    primary: '#3b82f6',
    secondary: '#8b5cf6',
    background: '#0f172a',
    surface: '#1e293b',
    text: '#f1f5f9',
    textSecondary: '#94a3b8',
    border: '#334155',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444'
  },
  typography: {
    fontFamily: 'Inter, system-ui, sans-serif',
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem'
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      bold: '700'
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem'
  },
  components: {
    button: {
      base: 'px-4 py-2 rounded-md font-medium transition-colors',
      variants: {
        primary: 'bg-blue-600 text-white hover:bg-blue-700',
        secondary: 'bg-gray-700 text-gray-300 hover:bg-gray-600'
      },
      states: {
        disabled: 'opacity-50 cursor-not-allowed'
      }
    },
    input: {
      base: 'px-3 py-2 rounded-md border bg-gray-800 text-white',
      variants: {
        default: 'border-gray-600 focus:border-blue-500',
        error: 'border-red-500 focus:border-red-500'
      },
      states: {
        focused: 'ring-2 ring-blue-500 ring-opacity-50'
      }
    },
    toolbar: {
      base: 'bg-gray-900 border-b border-gray-700',
      variants: {
        main: 'h-12 px-4',
        secondary: 'h-10 px-3'
      },
      states: {}
    },
    sidebar: {
      base: 'bg-gray-800 border-r border-gray-700',
      variants: {
        left: 'w-64',
        right: 'w-80'
      },
      states: {
        collapsed: 'w-16'
      }
    },
    canvas: {
      base: 'bg-gray-900',
      variants: {
        checkerboard: 'bg-checkerboard-dark'
      },
      states: {}
    }
  }
}

// Register theme
themeRegistry.registerTheme(darkTheme)
```

## File Format Extensions

### Custom Format Support

```typescript
// File: lib/formats/extensions.ts
interface FormatExtension {
  id: string
  name: string
  extensions: string[]
  mimeTypes: string[]
  capabilities: FormatCapability[]
  
  // Format handlers
  detect: (file: File) => Promise<boolean>
  read: (file: File) => Promise<FormatData>
  write: (data: FormatData, options?: FormatOptions) => Promise<Blob>
  
  // Format metadata
  metadata: FormatMetadata
}

interface FormatCapability {
  type: 'read' | 'write' | 'edit' | 'layers' | 'transparency' | 'animation'
  supported: boolean
  version?: string
}

interface FormatData {
  width: number
  height: number
  channels: number
  bitDepth: number
  colorSpace: string
  layers?: Layer[]
  metadata?: Record<string, any>
  rawData: ArrayBuffer
}

interface FormatMetadata {
  description: string
  website?: string
  documentation?: string
  license?: string
  author?: string
}

class FormatExtensionRegistry {
  private formats = new Map<string, FormatExtension>()
  
  public registerFormat(format: FormatExtension): void {
    this.formats.set(format.id, format)
    
    // Register file extensions
    format.extensions.forEach(ext => {
      this.registerFileExtension(ext, format.id)
    })
    
    // Register MIME types
    format.mimeTypes.forEach(mime => {
      this.registerMimeType(mime, format.id)
    })
    
    console.log(`Format registered: ${format.name}`)
  }
  
  public async detectFormat(file: File): Promise<FormatExtension | null> {
    // Check by extension first
    const extension = this.getFileExtension(file.name)
    let formatId = this.getFormatByExtension(extension)
    
    if (formatId) {
      const format = this.formats.get(formatId)
      if (format && await format.detect(file)) {
        return format
      }
    }
    
    // Check by MIME type
    formatId = this.getFormatByMimeType(file.type)
    if (formatId) {
      const format = this.formats.get(formatId)
      if (format && await format.detect(file)) {
        return format
      }
    }
    
    // Check by content detection
    for (const [id, format] of this.formats) {
      if (await format.detect(file)) {
        return format
      }
    }
    
    return null
  }
  
  public async readFile(file: File): Promise<FormatData> {
    const format = await this.detectFormat(file)
    if (!format) {
      throw new Error(`Unsupported file format: ${file.name}`)
    }
    
    if (!format.capabilities.some(cap => cap.type === 'read' && cap.supported)) {
      throw new Error(`Format does not support reading: ${format.name}`)
    }
    
    return format.read(file)
  }
  
  public async writeFile(
    data: FormatData,
    formatId: string,
    options?: FormatOptions
  ): Promise<Blob> {
    const format = this.formats.get(formatId)
    if (!format) {
      throw new Error(`Format not found: ${formatId}`)
    }
    
    if (!format.capabilities.some(cap => cap.type === 'write' && cap.supported)) {
      throw new Error(`Format does not support writing: ${format.name}`)
    }
    
    return format.write(data, options)
  }
}

// Example format extension for PSD files
const psdFormatExtension: FormatExtension = {
  id: 'psd',
  name: 'Adobe Photoshop',
  extensions: ['.psd', '.psb'],
  mimeTypes: ['image/vnd.adobe.photoshop'],
  capabilities: [
    { type: 'read', supported: true },
    { type: 'write', supported: false },
    { type: 'layers', supported: true },
    { type: 'transparency', supported: true }
  ],
  
  metadata: {
    description: 'Adobe Photoshop Document format with layer support',
    website: 'https://www.adobe.com/products/photoshop.html',
    documentation: 'https://www.adobe.com/devnet-apps/photoshop/fileformatashtml/',
    license: 'Proprietary',
    author: 'Adobe Systems'
  },
  
  detect: async (file: File): Promise<boolean> => {
    // Check PSD signature
    const header = await file.slice(0, 4).arrayBuffer()
    const signature = new TextDecoder().decode(header)
    return signature === '8BPS'
  },
  
  read: async (file: File): Promise<FormatData> => {
    const buffer = await file.arrayBuffer()
    const psd = await parsePSDFile(buffer)
    
    return {
      width: psd.width,
      height: psd.height,
      channels: psd.channels,
      bitDepth: psd.bitDepth,
      colorSpace: psd.colorMode,
      layers: psd.layers.map(layer => ({
        name: layer.name,
        opacity: layer.opacity,
        blendMode: layer.blendMode,
        visible: layer.visible,
        bounds: layer.bounds,
        imageData: layer.imageData
      })),
      metadata: psd.metadata,
      rawData: buffer
    }
  },
  
  write: async (data: FormatData, options?: FormatOptions): Promise<Blob> => {
    throw new Error('PSD writing not supported')
  }
}

// Register format
formatRegistry.registerFormat(psdFormatExtension)
```

## Workflow Extensions

### Custom Workflow System

```typescript
// File: lib/workflows/extensions.ts
interface WorkflowExtension {
  id: string
  name: string
  description: string
  version: string
  
  // Workflow definition
  steps: WorkflowStep[]
  triggers: WorkflowTrigger[]
  conditions: WorkflowCondition[]
  
  // Workflow execution
  execute: (context: WorkflowContext) => Promise<WorkflowResult>
  validate: (context: WorkflowContext) => Promise<boolean>
  
  // Workflow metadata
  metadata: WorkflowMetadata
}

interface WorkflowStep {
  id: string
  name: string
  type: 'tool' | 'ai' | 'filter' | 'custom'
  action: string
  parameters: Record<string, any>
  conditions?: WorkflowCondition[]
  timeout?: number
}

interface WorkflowTrigger {
  type: 'manual' | 'auto' | 'event' | 'schedule'
  event?: string
  schedule?: string
  conditions?: WorkflowCondition[]
}

interface WorkflowCondition {
  type: 'file-type' | 'image-size' | 'tool-active' | 'user-permission'
  operator: 'equals' | 'contains' | 'greater-than' | 'less-than'
  value: any
}

interface WorkflowContext {
  canvas: fabric.Canvas
  activeTools: string[]
  fileInfo: FileInfo
  userPermissions: string[]
  variables: Record<string, any>
}

interface WorkflowResult {
  success: boolean
  steps: WorkflowStepResult[]
  errors: WorkflowError[]
  duration: number
}

class WorkflowExtensionRegistry {
  private workflows = new Map<string, WorkflowExtension>()
  private activeWorkflows = new Set<string>()
  
  public registerWorkflow(workflow: WorkflowExtension): void {
    this.workflows.set(workflow.id, workflow)
    
    // Register triggers
    workflow.triggers.forEach(trigger => {
      this.registerTrigger(trigger, workflow.id)
    })
    
    console.log(`Workflow registered: ${workflow.name}`)
  }
  
  public async executeWorkflow(
    workflowId: string,
    context: WorkflowContext
  ): Promise<WorkflowResult> {
    const workflow = this.workflows.get(workflowId)
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`)
    }
    
    // Validate workflow
    const isValid = await workflow.validate(context)
    if (!isValid) {
      throw new Error(`Workflow validation failed: ${workflowId}`)
    }
    
    // Execute workflow
    this.activeWorkflows.add(workflowId)
    
    try {
      const result = await workflow.execute(context)
      return result
    } finally {
      this.activeWorkflows.delete(workflowId)
    }
  }
  
  private registerTrigger(trigger: WorkflowTrigger, workflowId: string): void {
    switch (trigger.type) {
      case 'event':
        if (trigger.event) {
          hookSystem.registerHook(trigger.event, async (data) => {
            const context = this.createWorkflowContext(data)
            if (this.checkConditions(trigger.conditions, context)) {
              await this.executeWorkflow(workflowId, context)
            }
          })
        }
        break
      
      case 'schedule':
        if (trigger.schedule) {
          // Schedule workflow execution
          this.scheduleWorkflow(workflowId, trigger.schedule)
        }
        break
    }
  }
}

// Example workflow extension
const batchProcessingWorkflow: WorkflowExtension = {
  id: 'batch-processing',
  name: 'Batch Processing',
  description: 'Process multiple images with the same operations',
  version: '1.0.0',
  
  steps: [
    {
      id: 'resize',
      name: 'Resize Images',
      type: 'tool',
      action: 'resize',
      parameters: {
        width: 800,
        height: 600,
        maintainAspectRatio: true
      }
    },
    {
      id: 'enhance',
      name: 'AI Enhancement',
      type: 'ai',
      action: 'enhance-image',
      parameters: {
        model: 'real-esrgan',
        scale: 2
      }
    },
    {
      id: 'optimize',
      name: 'Optimize for Web',
      type: 'filter',
      action: 'optimize-web',
      parameters: {
        quality: 85,
        format: 'webp'
      }
    }
  ],
  
  triggers: [
    {
      type: 'manual',
      conditions: [
        {
          type: 'file-type',
          operator: 'contains',
          value: 'image'
        }
      ]
    }
  ],
  
  conditions: [
    {
      type: 'user-permission',
      operator: 'equals',
      value: 'batch-processing'
    }
  ],
  
  metadata: {
    author: 'FotoFun Team',
    category: 'Batch Processing',
    tags: ['batch', 'automation', 'optimization']
  },
  
  validate: async (context: WorkflowContext): Promise<boolean> => {
    // Check if user has permission
    return context.userPermissions.includes('batch-processing')
  },
  
  execute: async (context: WorkflowContext): Promise<WorkflowResult> => {
    const startTime = Date.now()
    const results: WorkflowStepResult[] = []
    const errors: WorkflowError[] = []
    
    for (const step of this.steps) {
      try {
        const stepResult = await this.executeStep(step, context)
        results.push(stepResult)
      } catch (error) {
        errors.push({
          step: step.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    return {
      success: errors.length === 0,
      steps: results,
      errors,
      duration: Date.now() - startTime
    }
  }
}

// Register workflow
workflowRegistry.registerWorkflow(batchProcessingWorkflow)
```

## Extension Management

### Extension Marketplace

```typescript
// File: lib/extensions/marketplace.ts
interface ExtensionMarketplace {
  // Extension discovery
  searchExtensions: (query: string, filters?: ExtensionFilter[]) => Promise<ExtensionListing[]>
  getExtensionDetails: (extensionId: string) => Promise<ExtensionDetails>
  getFeaturedExtensions: () => Promise<ExtensionListing[]>
  
  // Extension installation
  installExtension: (extensionId: string, version?: string) => Promise<void>
  updateExtension: (extensionId: string, version?: string) => Promise<void>
  uninstallExtension: (extensionId: string) => Promise<void>
  
  // Extension management
  getInstalledExtensions: () => Promise<InstalledExtension[]>
  checkForUpdates: () => Promise<ExtensionUpdate[]>
  enableExtension: (extensionId: string) => Promise<void>
  disableExtension: (extensionId: string) => Promise<void>
}

interface ExtensionListing {
  id: string
  name: string
  description: string
  version: string
  author: string
  category: string
  tags: string[]
  rating: number
  downloads: number
  screenshots: string[]
  icon: string
  price: number
  license: string
}

interface ExtensionDetails extends ExtensionListing {
  readme: string
  changelog: string
  requirements: ExtensionRequirement[]
  permissions: string[]
  dependencies: string[]
  supportedVersions: string[]
}

interface ExtensionRequirement {
  type: 'fotofun-version' | 'browser' | 'os' | 'hardware'
  value: string
  optional: boolean
}

class ExtensionMarketplaceClient implements ExtensionMarketplace {
  private baseUrl = 'https://marketplace.fotofun.app/api'
  
  async searchExtensions(
    query: string,
    filters?: ExtensionFilter[]
  ): Promise<ExtensionListing[]> {
    const params = new URLSearchParams({
      q: query,
      limit: '20'
    })
    
    filters?.forEach(filter => {
      params.append(filter.type, filter.value)
    })
    
    const response = await fetch(`${this.baseUrl}/extensions/search?${params}`)
    return response.json()
  }
  
  async installExtension(extensionId: string, version?: string): Promise<void> {
    // Download extension package
    const packageUrl = `${this.baseUrl}/extensions/${extensionId}/download`
    const response = await fetch(packageUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ version })
    })
    
    if (!response.ok) {
      throw new Error(`Failed to download extension: ${response.statusText}`)
    }
    
    const packageData = await response.arrayBuffer()
    
    // Install extension
    await this.installExtensionPackage(extensionId, packageData)
  }
  
  private async installExtensionPackage(
    extensionId: string,
    packageData: ArrayBuffer
  ): Promise<void> {
    // Extract package
    const extractedFiles = await this.extractPackage(packageData)
    
    // Validate extension
    const manifest = await this.validateExtension(extractedFiles)
    
    // Install files
    await this.installExtensionFiles(extensionId, extractedFiles)
    
    // Register extension
    await this.registerExtension(manifest)
    
    console.log(`Extension installed: ${extensionId}`)
  }
}
```

## Security & Permissions

### Extension Security Model

```typescript
// File: lib/security/extensions.ts
interface ExtensionPermission {
  id: string
  name: string
  description: string
  risk: 'low' | 'medium' | 'high'
  required: boolean
}

interface ExtensionSandbox {
  // Allowed APIs
  allowedAPIs: string[]
  
  // Resource limits
  memoryLimit: number
  cpuLimit: number
  networkLimit: number
  
  // File system access
  fileSystemAccess: FileSystemAccess
  
  // Network access
  networkAccess: NetworkAccess
}

interface FileSystemAccess {
  read: string[]
  write: string[]
  execute: string[]
}

interface NetworkAccess {
  allowedDomains: string[]
  blockedDomains: string[]
  allowedPorts: number[]
}

class ExtensionSecurityManager {
  private permissions = new Map<string, ExtensionPermission[]>()
  private sandboxes = new Map<string, ExtensionSandbox>()
  
  public async validateExtension(
    extensionId: string,
    manifest: ExtensionManifest
  ): Promise<boolean> {
    // Check permissions
    for (const permission of manifest.permissions) {
      if (!this.isPermissionAllowed(permission)) {
        throw new Error(`Permission not allowed: ${permission}`)
      }
    }
    
    // Validate code
    const codeValidation = await this.validateExtensionCode(extensionId)
    if (!codeValidation.safe) {
      throw new Error(`Code validation failed: ${codeValidation.issues.join(', ')}`)
    }
    
    return true
  }
  
  public createSandbox(extensionId: string): ExtensionSandbox {
    const permissions = this.permissions.get(extensionId) || []
    
    const sandbox: ExtensionSandbox = {
      allowedAPIs: this.getAPIPermissions(permissions),
      memoryLimit: 100 * 1024 * 1024, // 100MB
      cpuLimit: 1000, // 1 second CPU time
      networkLimit: 10 * 1024 * 1024, // 10MB network
      fileSystemAccess: this.getFileSystemAccess(permissions),
      networkAccess: this.getNetworkAccess(permissions)
    }
    
    this.sandboxes.set(extensionId, sandbox)
    return sandbox
  }
  
  private async validateExtensionCode(extensionId: string): Promise<CodeValidation> {
    // Static analysis
    const staticAnalysis = await this.performStaticAnalysis(extensionId)
    
    // Dynamic analysis
    const dynamicAnalysis = await this.performDynamicAnalysis(extensionId)
    
    const issues = [
      ...staticAnalysis.issues,
      ...dynamicAnalysis.issues
    ]
    
    return {
      safe: issues.length === 0,
      issues
    }
  }
}
```

## Extension Benefits

### 1. Developer Experience
- **Plugin architecture**: Easy to extend core functionality
- **Hook system**: Integrate with existing workflows
- **TypeScript support**: Full type safety for extensions
- **Development tools**: Testing and debugging utilities

### 2. User Experience
- **Seamless integration**: Extensions feel like native features
- **Customization**: Tailor the editor to specific needs
- **Performance**: Extensions run in optimized sandboxes
- **Security**: Comprehensive permission system

### 3. Ecosystem Growth
- **Marketplace**: Discover and install extensions
- **Community**: Share and collaborate on extensions
- **Documentation**: Comprehensive extension API docs
- **Tools**: Extension development toolkit

### 4. Business Benefits
- **Monetization**: Premium extensions and features
- **Partnerships**: Third-party integrations
- **Customization**: Enterprise-specific extensions
- **Innovation**: Community-driven feature development

This extensibility architecture ensures FotoFun can evolve and grow through community contributions while maintaining security, performance, and user experience standards.