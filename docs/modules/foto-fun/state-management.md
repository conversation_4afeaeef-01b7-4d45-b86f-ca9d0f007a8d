# FotoFun - State Management Architecture

## Overview

FotoFun uses **Zustand** as its state management solution, providing a modern, lightweight, and performant approach to handling application state. The architecture follows a **domain-driven design** with specialized stores for different areas of functionality, ensuring clean separation of concerns and optimal performance.

## Store Architecture

### Core Store Structure

```mermaid
graph TB
    subgraph "Core Stores"
        CanvasStore[Canvas Store]
        ToolStore[Tool Store]
        HistoryStore[History Store]
        DocumentStore[Document Store]
    end
    
    subgraph "Feature Stores"
        LayerStore[Layer Store]
        SelectionStore[Selection Store]
        FilterStore[Filter Store]
        ColorStore[Color Store]
    end
    
    subgraph "System Stores"
        PerformanceStore[Performance Store]
        ObjectRegistryStore[Object Registry Store]
        ToolOptionsStore[Tool Options Store]
    end
    
    subgraph "External State"
        FabricCanvas[Fabric.js Canvas]
        Supabase[Supabase DB]
        LocalStorage[Local Storage]
    end
    
    CanvasStore --> FabricCanvas
    ToolStore --> ToolOptionsStore
    HistoryStore --> DocumentStore
    LayerStore --> CanvasStore
    SelectionStore --> CanvasStore
    FilterStore --> CanvasStore
    DocumentStore --> Supabase
    PerformanceStore --> LocalStorage
    ObjectRegistryStore --> FabricCanvas
```

## Core Stores

### 1. Canvas Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/canvasStore.ts`)

The central store managing canvas state, viewport, and UI components.

```typescript
interface CanvasStore {
  // Canvas instances
  fabricCanvas: Canvas | null
  
  // Canvas properties
  zoom: number
  isPanning: boolean
  lastPosX: number
  lastPosY: number
  
  // Document properties (actual image/document dimensions)
  width: number
  height: number
  backgroundColor: string
  
  // Viewport properties (visible area dimensions)
  viewportWidth: number
  viewportHeight: number
  
  // UI state
  activeAITool: {
    type: string
    tool: any
    activationType: 'dialog' | 'panel' | 'immediate'
  } | null
  
  activeAdjustmentTool: {
    toolId: string
    toolName: string
    currentValue?: number
    anchorElement?: HTMLElement | null
  } | null
  
  reviewModal: {
    isOpen: boolean
    title: string
    originalImage: string
    processedImage: string
    onApplyInPlace: () => void
    onRejectChange: () => void
    onAcceptBoth: () => void
    maskImage?: string
  } | null
  
  // Selection management
  selectionManager: LayerAwareSelectionManager | null
  selectionRenderer: SelectionRenderer | null
  clipboardManager: ClipboardManager | null
  
  // Initialization state
  isReady: boolean
  initializationError: Error | null
  initializationPromise: Promise<void> | null
}
```

#### Key Canvas Store Methods

```typescript
// Canvas lifecycle management
const canvasStore = useCanvasStore()

// Initialize canvas with proper async handling
await canvasStore.initCanvas(canvasElement, width, height)

// Wait for canvas to be ready
await canvasStore.waitForReady()

// Check if canvas has content
const hasContent = canvasStore.hasContent()

// Zoom operations
canvasStore.setZoom(1.5)
canvasStore.zoomIn()
canvasStore.zoomOut()
canvasStore.zoomToFit()

// Pan operations
canvasStore.startPanning(event)
canvasStore.pan(event)
canvasStore.endPanning()

// Viewport management
canvasStore.resizeViewport(800, 600)
canvasStore.centerContent()
```

### 2. Tool Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/toolStore.ts`)

Manages active tools and tool state.

```typescript
interface ToolStore {
  // Active tool
  activeTool: string | null
  previousTool: string | null
  
  // Tool state
  isToolActive: boolean
  toolCursor: string
  
  // Tool categories
  toolCategories: ToolCategory[]
  
  // Tool actions
  setActiveTool: (toolId: string) => void
  deactivateTool: () => void
  toggleTool: (toolId: string) => void
  
  // Tool queries
  getToolById: (toolId: string) => Tool | null
  getToolsByCategory: (category: string) => Tool[]
  isToolAvailable: (toolId: string) => boolean
  
  // Tool history
  getRecentTools: () => Tool[]
  addToRecentTools: (toolId: string) => void
}
```

#### Tool Store Implementation

```typescript
export const useToolStore = create<ToolStore>()(
  devtools(
    (set, get) => ({
      activeTool: null,
      previousTool: null,
      isToolActive: false,
      toolCursor: 'default',
      toolCategories: TOOL_CATEGORIES,
      
      setActiveTool: (toolId) => {
        const current = get().activeTool
        
        // Store previous tool
        if (current && current !== toolId) {
          set({ previousTool: current })
        }
        
        // Activate new tool
        set({ 
          activeTool: toolId,
          isToolActive: true,
          toolCursor: getToolCursor(toolId)
        })
        
        // Update recent tools
        get().addToRecentTools(toolId)
        
        // Activate tool in canvas
        activateCanvasTool(toolId)
      },
      
      deactivateTool: () => {
        const { activeTool } = get()
        
        if (activeTool) {
          deactivateCanvasTool(activeTool)
          set({ 
            activeTool: null,
            isToolActive: false,
            toolCursor: 'default'
          })
        }
      },
      
      toggleTool: (toolId) => {
        const { activeTool } = get()
        
        if (activeTool === toolId) {
          get().deactivateTool()
        } else {
          get().setActiveTool(toolId)
        }
      }
    }),
    { name: 'tool-store' }
  )
)
```

### 3. History Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/historyStore.ts`)

Manages command history for undo/redo functionality.

```typescript
interface HistoryStore {
  // Command history
  undoStack: Command[]
  redoStack: Command[]
  
  // History state
  canUndo: boolean
  canRedo: boolean
  currentIndex: number
  maxHistorySize: number
  
  // History actions
  addCommand: (command: Command) => void
  undo: () => Promise<void>
  redo: () => Promise<void>
  clear: () => void
  
  // Batch operations
  beginBatch: () => void
  endBatch: () => void
  isBatching: boolean
  
  // History queries
  getUndoDescription: () => string | null
  getRedoDescription: () => string | null
  getHistorySize: () => number
}
```

#### Command Pattern Implementation

```typescript
// Base command interface
abstract class Command {
  abstract execute(): Promise<void>
  abstract undo(): Promise<void>
  abstract getDescription(): string
  abstract canUndo(): boolean
}

// Composite command for batching
class CompositeCommand extends Command {
  private commands: Command[] = []
  
  addCommand(command: Command): void {
    this.commands.push(command)
  }
  
  async execute(): Promise<void> {
    for (const command of this.commands) {
      await command.execute()
    }
  }
  
  async undo(): Promise<void> {
    for (const command of this.commands.reverse()) {
      await command.undo()
    }
  }
  
  getDescription(): string {
    return `Batch: ${this.commands.length} operations`
  }
  
  canUndo(): boolean {
    return this.commands.every(cmd => cmd.canUndo())
  }
}

// History store implementation
export const useHistoryStore = create<HistoryStore>()(
  devtools(
    (set, get) => ({
      undoStack: [],
      redoStack: [],
      canUndo: false,
      canRedo: false,
      currentIndex: -1,
      maxHistorySize: 100,
      isBatching: false,
      currentBatch: null,
      
      addCommand: (command) => {
        const state = get()
        
        // Add to batch if batching
        if (state.isBatching && state.currentBatch) {
          state.currentBatch.addCommand(command)
          return
        }
        
        // Execute command
        command.execute()
        
        // Add to undo stack
        const newUndoStack = [...state.undoStack, command]
        
        // Limit history size
        if (newUndoStack.length > state.maxHistorySize) {
          newUndoStack.shift()
        }
        
        set({
          undoStack: newUndoStack,
          redoStack: [], // Clear redo stack
          canUndo: true,
          canRedo: false,
          currentIndex: newUndoStack.length - 1
        })
      },
      
      undo: async () => {
        const state = get()
        if (!state.canUndo) return
        
        const command = state.undoStack[state.currentIndex]
        await command.undo()
        
        const newUndoStack = state.undoStack.slice(0, -1)
        const newRedoStack = [...state.redoStack, command]
        
        set({
          undoStack: newUndoStack,
          redoStack: newRedoStack,
          canUndo: newUndoStack.length > 0,
          canRedo: true,
          currentIndex: newUndoStack.length - 1
        })
      },
      
      redo: async () => {
        const state = get()
        if (!state.canRedo) return
        
        const command = state.redoStack[state.redoStack.length - 1]
        await command.execute()
        
        const newRedoStack = state.redoStack.slice(0, -1)
        const newUndoStack = [...state.undoStack, command]
        
        set({
          undoStack: newUndoStack,
          redoStack: newRedoStack,
          canUndo: true,
          canRedo: newRedoStack.length > 0,
          currentIndex: newUndoStack.length - 1
        })
      }
    }),
    { name: 'history-store' }
  )
)
```

## Feature Stores

### 4. Layer Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/layerStore.ts`)

Manages layer hierarchy and operations.

```typescript
interface Layer {
  id: string
  name: string
  type: LayerType
  visible: boolean
  opacity: number
  blendMode: BlendMode
  locked: boolean
  objectId?: string // Link to canvas object
  thumbnail?: string
  bounds?: Rectangle
  effects?: LayerEffect[]
  mask?: LayerMask
  parent?: string
  children: string[]
  order: number
  created: number
  modified: number
}

interface LayerStore {
  // Layer data
  layers: Layer[]
  activeLayerId: string | null
  selectedLayerIds: string[]
  
  // Layer operations
  createLayer: (name: string, type: LayerType) => Layer
  deleteLayer: (layerId: string) => void
  duplicateLayer: (layerId: string) => Layer
  mergeLayer: (layerId: string, targetId: string) => void
  
  // Layer hierarchy
  reorderLayers: (layerIds: string[]) => void
  groupLayers: (layerIds: string[]) => Layer
  ungroupLayer: (groupId: string) => void
  
  // Layer properties
  setLayerProperty: (layerId: string, property: string, value: unknown) => void
  setLayerVisibility: (layerId: string, visible: boolean) => void
  setLayerOpacity: (layerId: string, opacity: number) => void
  setLayerBlendMode: (layerId: string, blendMode: BlendMode) => void
  setLayerLocked: (layerId: string, locked: boolean) => void
  
  // Layer selection
  setActiveLayer: (layerId: string) => void
  selectLayer: (layerId: string, addToSelection?: boolean) => void
  deselectLayer: (layerId: string) => void
  selectAllLayers: () => void
  deselectAllLayers: () => void
  
  // Layer queries
  getLayer: (layerId: string) => Layer | null
  getLayersByType: (type: LayerType) => Layer[]
  getVisibleLayers: () => Layer[]
  getLayerHierarchy: () => LayerHierarchy
  findLayerByObjectId: (objectId: string) => Layer | null
}
```

#### Layer-Canvas Synchronization

```typescript
// Keep layers synchronized with canvas objects
const syncLayersWithCanvas = () => {
  const canvasStore = useCanvasStore()
  const layerStore = useLayerStore()
  
  // Listen to canvas object events
  canvasStore.fabricCanvas?.on('object:added', (e: any) => {
    const obj = e.target
    if (obj && !obj.isTemp) {
      const layer = layerStore.createLayer(obj.name || 'Layer', getLayerType(obj))
      layer.objectId = obj.id
      layerStore.addLayer(layer)
    }
  })
  
  canvasStore.fabricCanvas?.on('object:removed', (e: any) => {
    const obj = e.target
    if (obj && !obj.isTemp) {
      const layer = layerStore.findLayerByObjectId(obj.id)
      if (layer) {
        layerStore.deleteLayer(layer.id)
      }
    }
  })
  
  canvasStore.fabricCanvas?.on('object:modified', (e: any) => {
    const obj = e.target
    if (obj && !obj.isTemp) {
      const layer = layerStore.findLayerByObjectId(obj.id)
      if (layer) {
        layerStore.setLayerProperty(layer.id, 'modified', Date.now())
        layerStore.setLayerProperty(layer.id, 'bounds', obj.getBoundingRect())
      }
    }
  })
}
```

### 5. Selection Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/selectionStore.ts`)

Manages selection state and operations.

```typescript
interface SelectionStore {
  // Selection state
  hasSelection: boolean
  selectionType: SelectionType
  selectionBounds: Rectangle | null
  selectionMask: ImageData | null
  
  // Selection tools
  activeSelectionTool: string | null
  selectionMode: 'new' | 'add' | 'subtract' | 'intersect'
  
  // Selection operations
  createSelection: (type: SelectionType, data: SelectionData) => void
  addToSelection: (selection: Selection) => void
  subtractFromSelection: (selection: Selection) => void
  intersectWithSelection: (selection: Selection) => void
  invertSelection: () => void
  clearSelection: () => void
  
  // Selection queries
  isPointInSelection: (point: Point) => boolean
  getSelectionPixelCount: () => number
  getSelectionArea: () => number
  
  // Selection modification
  expandSelection: (pixels: number) => void
  contractSelection: (pixels: number) => void
  featherSelection: (radius: number) => void
  smoothSelection: (amount: number) => void
  
  // Selection save/load
  saveSelection: (name: string) => void
  loadSelection: (name: string) => void
  getSavedSelections: () => SavedSelection[]
}
```

#### Selection Integration

```typescript
// Integration with canvas selection system
const useSelectionIntegration = () => {
  const canvasStore = useCanvasStore()
  const selectionStore = useSelectionStore()
  
  // Update selection store when canvas selection changes
  useEffect(() => {
    const canvas = canvasStore.fabricCanvas
    if (!canvas) return
    
    const selectionManager = canvasStore.selectionManager
    if (!selectionManager) return
    
    // Listen to selection events
    selectionManager.on('selection:created', (selection: Selection) => {
      selectionStore.createSelection(selection.type, selection.data)
    })
    
    selectionManager.on('selection:cleared', () => {
      selectionStore.clearSelection()
    })
    
    selectionManager.on('selection:modified', (selection: Selection) => {
      selectionStore.updateSelection(selection)
    })
  }, [canvasStore.fabricCanvas, canvasStore.selectionManager])
}
```

### 6. Filter Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/filterStore.ts`)

Manages filter states and applications.

```typescript
interface FilterStore {
  // Filter states
  activeFilters: Record<string, boolean>
  filterParameters: Record<string, FilterParams>
  
  // Filter operations
  toggleFilter: (filterId: string) => void
  setFilterParameter: (filterId: string, param: string, value: unknown) => void
  resetFilter: (filterId: string) => void
  resetAllFilters: () => void
  
  // Filter queries
  isFilterActive: (filterId: string) => boolean
  getFilterParameter: (filterId: string, param: string) => unknown
  getActiveFilterCount: () => number
  
  // Filter presets
  savePreset: (name: string, filters: FilterPreset) => void
  loadPreset: (name: string) => void
  getPresets: () => FilterPreset[]
  
  // Canvas synchronization
  updateFromCanvas: (filterStates: Record<string, boolean>) => void
}
```

#### Toggle Filter Tools

```typescript
// Toggle filter tools that can be turned on/off
export const TOGGLE_FILTER_TOOLS = [
  'grayscale',
  'sepia',
  'invert'
] as const

// Filter store implementation
export const useFilterStore = create<FilterStore>()(
  devtools(
    (set, get) => ({
      activeFilters: {},
      filterParameters: {},
      
      toggleFilter: (filterId) => {
        const current = get().activeFilters[filterId] || false
        
        set(state => ({
          activeFilters: {
            ...state.activeFilters,
            [filterId]: !current
          }
        }))
        
        // Apply filter to canvas
        applyFilterToCanvas(filterId, !current)
      },
      
      setFilterParameter: (filterId, param, value) => {
        set(state => ({
          filterParameters: {
            ...state.filterParameters,
            [filterId]: {
              ...state.filterParameters[filterId],
              [param]: value
            }
          }
        }))
        
        // Update canvas filter
        updateCanvasFilter(filterId, param, value)
      },
      
      updateFromCanvas: (filterStates) => {
        set(state => ({
          activeFilters: {
            ...state.activeFilters,
            ...filterStates
          }
        }))
      }
    }),
    { name: 'filter-store' }
  )
)
```

## System Stores

### 7. Performance Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/performanceStore.ts`)

Monitors and tracks application performance.

```typescript
interface PerformanceStore {
  // Performance metrics
  renderTime: number
  selectionTime: number
  filterTime: number
  toolExecutionTime: number
  aiResponseTime: number
  
  // Memory usage
  memoryUsage: number
  canvasMemoryUsage: number
  
  // Frame rate
  currentFPS: number
  averageFPS: number
  
  // Performance tracking
  trackRender: (time: number) => void
  trackSelection: (time: number) => void
  trackFilter: (time: number) => void
  trackToolExecution: (time: number) => void
  trackAIResponse: (time: number) => void
  
  // Performance analysis
  getPerformanceReport: () => PerformanceReport
  getBottlenecks: () => PerformanceBottleneck[]
  suggestOptimizations: () => OptimizationSuggestion[]
  
  // Performance optimization
  enablePerformanceMode: () => void
  disablePerformanceMode: () => void
  setQualityLevel: (level: QualityLevel) => void
}
```

#### Performance Monitoring Implementation

```typescript
// Performance monitoring utilities
const performanceMonitor = {
  startTimer: (label: string) => {
    const startTime = performance.now()
    
    return {
      end: () => {
        const endTime = performance.now()
        const duration = endTime - startTime
        
        // Track performance based on label
        const performanceStore = usePerformanceStore.getState()
        
        switch (label) {
          case 'render':
            performanceStore.trackRender(duration)
            break
          case 'selection':
            performanceStore.trackSelection(duration)
            break
          case 'filter':
            performanceStore.trackFilter(duration)
            break
          case 'tool':
            performanceStore.trackToolExecution(duration)
            break
          case 'ai':
            performanceStore.trackAIResponse(duration)
            break
        }
        
        return duration
      }
    }
  },
  
  measureAsync: async <T>(label: string, fn: () => Promise<T>): Promise<T> => {
    const timer = performanceMonitor.startTimer(label)
    try {
      const result = await fn()
      return result
    } finally {
      timer.end()
    }
  }
}
```

### 8. Object Registry Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/objectRegistryStore.ts`)

Manages canvas object registry and pixel mapping.

```typescript
interface ObjectRegistryStore {
  // Object tracking
  objects: Map<string, FabricObject>
  pixelMap: ImageData | null
  dirtyObjects: Set<string>
  
  // Performance tracking
  lastUpdateTime: number
  updateCount: number
  
  // Registry operations
  registerObject: (object: FabricObject) => void
  unregisterObject: (objectId: string) => void
  getObject: (objectId: string) => FabricObject | null
  
  // Pixel map operations
  updatePixelMap: () => void
  markDirty: (objectId: string) => void
  updatePixelMapIfNeeded: () => void
  
  // Queries
  getObjectsAtPoint: (point: Point) => FabricObject[]
  getPixelColor: (point: Point) => Color | null
  isPointTransparent: (point: Point) => boolean
  
  // Cleanup
  clearCache: () => void
  dispose: () => void
}
```

#### Pixel Map Optimization

```typescript
// Efficient pixel map updates with debouncing
const usePixelMapOptimization = () => {
  const objectRegistry = useObjectRegistryStore()
  
  // Debounced update function
  const debouncedUpdate = useMemo(
    () => debounce(() => {
      objectRegistry.updatePixelMapIfNeeded()
    }, 300),
    []
  )
  
  // Listen to canvas changes
  useEffect(() => {
    const canvas = canvasStore.fabricCanvas
    if (!canvas) return
    
    // Object modification events
    canvas.on('object:modified', (e: any) => {
      const obj = e.target
      if (obj && obj.get('id')) {
        objectRegistry.markDirty(obj.get('id'))
        debouncedUpdate()
      }
    })
    
    // Object lifecycle events
    canvas.on('object:added', () => {
      objectRegistry.updatePixelMap()
    })
    
    canvas.on('object:removed', () => {
      objectRegistry.updatePixelMap()
    })
    
    return () => {
      canvas.off('object:modified')
      canvas.off('object:added')
      canvas.off('object:removed')
    }
  }, [canvasStore.fabricCanvas])
}
```

## State Persistence

### Local Storage Integration

```typescript
// Persist certain stores to local storage
const persistStores = () => {
  // Tool preferences
  const toolStore = useToolStore()
  toolStore.subscribe(
    (state) => state.recentTools,
    (recentTools) => {
      localStorage.setItem('foto-fun-recent-tools', JSON.stringify(recentTools))
    }
  )
  
  // Filter presets
  const filterStore = useFilterStore()
  filterStore.subscribe(
    (state) => state.presets,
    (presets) => {
      localStorage.setItem('foto-fun-filter-presets', JSON.stringify(presets))
    }
  )
  
  // Performance settings
  const performanceStore = usePerformanceStore()
  performanceStore.subscribe(
    (state) => state.qualityLevel,
    (qualityLevel) => {
      localStorage.setItem('foto-fun-quality-level', qualityLevel)
    }
  )
}
```

### Supabase Integration

```typescript
// Document persistence to Supabase
const useDocumentPersistence = () => {
  const documentStore = useDocumentStore()
  const canvasStore = useCanvasStore()
  
  // Auto-save document
  const autoSave = useCallback(
    debounce(async () => {
      const canvas = canvasStore.fabricCanvas
      if (!canvas) return
      
      const canvasData = canvas.toJSON()
      const thumbnail = canvas.toDataURL({ format: 'png', quality: 0.5 })
      
      await documentStore.saveDocument({
        canvasData,
        thumbnail,
        lastModified: new Date().toISOString()
      })
    }, 5000),
    [canvasStore.fabricCanvas]
  )
  
  // Listen to canvas changes
  useEffect(() => {
    const canvas = canvasStore.fabricCanvas
    if (!canvas) return
    
    canvas.on('object:modified', autoSave)
    canvas.on('object:added', autoSave)
    canvas.on('object:removed', autoSave)
    
    return () => {
      canvas.off('object:modified', autoSave)
      canvas.off('object:added', autoSave)
      canvas.off('object:removed', autoSave)
    }
  }, [canvasStore.fabricCanvas, autoSave])
}
```

## State Synchronization

### Cross-Store Communication

```typescript
// Coordinate state changes across stores
const useStoreCoordination = () => {
  const canvasStore = useCanvasStore()
  const toolStore = useToolStore()
  const layerStore = useLayerStore()
  const selectionStore = useSelectionStore()
  
  // Tool changes affect canvas cursor
  useEffect(() => {
    const canvas = canvasStore.fabricCanvas
    if (!canvas) return
    
    const activeTool = toolStore.activeTool
    if (activeTool) {
      canvas.defaultCursor = toolStore.toolCursor
    }
  }, [toolStore.activeTool, toolStore.toolCursor, canvasStore.fabricCanvas])
  
  // Layer selection affects canvas object selection
  useEffect(() => {
    const canvas = canvasStore.fabricCanvas
    if (!canvas) return
    
    const activeLayerId = layerStore.activeLayerId
    if (activeLayerId) {
      const layer = layerStore.getLayer(activeLayerId)
      if (layer && layer.objectId) {
        const obj = canvas.getObjectById(layer.objectId)
        if (obj) {
          canvas.setActiveObject(obj)
        }
      }
    }
  }, [layerStore.activeLayerId, canvasStore.fabricCanvas])
  
  // Selection changes affect layer selection
  useEffect(() => {
    const hasSelection = selectionStore.hasSelection
    if (hasSelection) {
      // Update UI to show selection tools
      toolStore.setSelectionMode(true)
    } else {
      toolStore.setSelectionMode(false)
    }
  }, [selectionStore.hasSelection])
}
```

### State Validation

```typescript
// Validate state consistency
const useStateValidation = () => {
  const stores = {
    canvas: useCanvasStore(),
    tool: useToolStore(),
    layer: useLayerStore(),
    selection: useSelectionStore(),
    filter: useFilterStore(),
    history: useHistoryStore()
  }
  
  // Validation checks
  const validateState = useCallback(() => {
    const issues: ValidationIssue[] = []
    
    // Check canvas-layer sync
    const canvas = stores.canvas.fabricCanvas
    if (canvas) {
      const canvasObjects = canvas.getObjects()
      const layers = stores.layer.layers
      
      if (canvasObjects.length !== layers.length) {
        issues.push({
          type: 'sync-error',
          message: 'Canvas objects and layers are out of sync',
          severity: 'warning'
        })
      }
    }
    
    // Check tool state
    const activeTool = stores.tool.activeTool
    if (activeTool && !stores.tool.isToolAvailable(activeTool)) {
      issues.push({
        type: 'tool-error',
        message: `Active tool ${activeTool} is not available`,
        severity: 'error'
      })
    }
    
    // Check history state
    if (stores.history.canUndo && stores.history.undoStack.length === 0) {
      issues.push({
        type: 'history-error',
        message: 'History state is inconsistent',
        severity: 'error'
      })
    }
    
    return issues
  }, [stores])
  
  // Run validation periodically in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(() => {
        const issues = validateState()
        if (issues.length > 0) {
          console.warn('State validation issues:', issues)
        }
      }, 10000)
      
      return () => clearInterval(interval)
    }
  }, [validateState])
}
```

## Performance Optimizations

### Selective Subscriptions

```typescript
// Only subscribe to specific state slices
const useCanvasZoom = () => {
  return useCanvasStore(state => state.zoom)
}

const useActiveTool = () => {
  return useToolStore(state => state.activeTool)
}

const useCanUndo = () => {
  return useHistoryStore(state => state.canUndo)
}
```

### Computed State

```typescript
// Derived state with useMemo
const useComputedState = () => {
  const layers = useLayerStore(state => state.layers)
  const activeTool = useToolStore(state => state.activeTool)
  const hasSelection = useSelectionStore(state => state.hasSelection)
  
  // Compute available tools based on context
  const availableTools = useMemo(() => {
    return TOOLS.filter(tool => {
      if (tool.requiresSelection && !hasSelection) return false
      if (tool.requiresLayers && layers.length === 0) return false
      return true
    })
  }, [layers.length, hasSelection])
  
  // Compute layer tree
  const layerTree = useMemo(() => {
    return buildLayerTree(layers)
  }, [layers])
  
  return {
    availableTools,
    layerTree
  }
}
```

### State Middleware

```typescript
// Custom middleware for logging and debugging
const loggerMiddleware = (config: any) => (set: any, get: any, api: any) =>
  config(
    (...args: any[]) => {
      console.log('State change:', args)
      set(...args)
    },
    get,
    api
  )

// Performance monitoring middleware
const performanceMiddleware = (config: any) => (set: any, get: any, api: any) =>
  config(
    (...args: any[]) => {
      const start = performance.now()
      set(...args)
      const end = performance.now()
      
      if (end - start > 10) {
        console.warn(`Slow state update: ${end - start}ms`)
      }
    },
    get,
    api
  )
```

## Testing Support

### Store Testing Utilities

```typescript
// Test utilities for store testing
export const createTestStore = <T>(
  storeCreator: () => T,
  initialState?: Partial<T>
): T => {
  const store = storeCreator()
  
  if (initialState) {
    // Merge initial state
    Object.assign(store, initialState)
  }
  
  return store
}

// Mock canvas for testing
export const createMockCanvas = (): Canvas => {
  return {
    width: 800,
    height: 600,
    getObjects: () => [],
    add: jest.fn(),
    remove: jest.fn(),
    renderAll: jest.fn(),
    // ... other canvas methods
  } as unknown as Canvas
}

// Test canvas store
describe('CanvasStore', () => {
  let store: CanvasStore
  
  beforeEach(() => {
    store = createTestStore(() => useCanvasStore.getState())
  })
  
  it('should initialize canvas', async () => {
    const mockElement = document.createElement('canvas')
    await store.initCanvas(mockElement, 800, 600)
    
    expect(store.fabricCanvas).toBeDefined()
    expect(store.width).toBe(800)
    expect(store.height).toBe(600)
  })
  
  it('should handle zoom operations', () => {
    store.setZoom(1.5)
    expect(store.zoom).toBe(1.5)
    
    store.zoomIn()
    expect(store.zoom).toBeGreaterThan(1.5)
  })
})
```

## Future Enhancements

### Real-time Collaboration

```typescript
// Planned real-time state synchronization
interface CollaborationStore {
  // Connected users
  connectedUsers: CollaborationUser[]
  
  // Shared state
  sharedCursor: Record<string, Point>
  sharedSelection: Record<string, Selection>
  
  // Collaboration actions
  broadcastStateChange: (change: StateChange) => void
  applyRemoteChange: (change: StateChange) => void
  
  // Conflict resolution
  resolveConflict: (localChange: StateChange, remoteChange: StateChange) => StateChange
}
```

### State Time Travel

```typescript
// Advanced debugging with state time travel
interface TimeTravel {
  // State history
  stateHistory: StateSnapshot[]
  currentIndex: number
  
  // Time travel actions
  jumpToState: (index: number) => void
  playForward: () => void
  playBackward: () => void
  
  // State diff
  getStateDiff: (fromIndex: number, toIndex: number) => StateDiff
  
  // Export/import
  exportStateHistory: () => string
  importStateHistory: (data: string) => void
}
```

## State Management Benefits

### 1. Performance
- **Selective subscriptions**: Only re-render when needed
- **Computed state**: Memoized derived values
- **Efficient updates**: Immutable updates with structural sharing
- **Lazy initialization**: Load stores on demand

### 2. Developer Experience
- **TypeScript support**: Full type safety
- **DevTools integration**: Time travel debugging
- **Hot reload**: State persists across reloads
- **Testing utilities**: Easy to test store logic

### 3. Maintainability
- **Domain separation**: Clear boundaries between stores
- **Predictable updates**: Unidirectional data flow
- **State validation**: Consistency checks
- **Documentation**: Self-documenting state shapes

### 4. Scalability
- **Modular design**: Easy to add new stores
- **Memory efficiency**: Cleanup unused state
- **Performance monitoring**: Track state update performance
- **Persistence**: Save/restore state across sessions

This state management architecture provides a solid foundation for building a complex photo editing application while maintaining performance, developer experience, and scalability.