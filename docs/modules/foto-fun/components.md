# FotoFun - Component Architecture

## Overview

FotoFun's component architecture follows a modular, feature-based design that separates concerns between marketing, editor, and shared UI components. The architecture emphasizes reusability, type safety, and performance optimization.

## Component Hierarchy

```
components/
├── dialogs/            # Global modal dialogs
├── editor/             # Editor-specific components
├── marketing/          # Landing page components
└── ui/                 # Reusable UI primitives
```

## Marketing Components

**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/components/marketing/`

### Landing Page Components

#### Header Component
- **File**: `header.tsx`
- **Purpose**: Navigation and branding for marketing pages
- **Features**:
  - Responsive navigation menu
  - Dark/light mode toggle
  - Authentication state awareness
  - Call-to-action buttons

#### Hero Section
- **File**: `hero-section.tsx`
- **Purpose**: Main value proposition and primary CTA
- **Features**:
  - Animated text and graphics
  - Video background support
  - Action buttons (Try Now, GitHub)
  - Social proof elements

#### Features Section
- **File**: `features-section.tsx`
- **Purpose**: Showcase key platform features
- **Features**:
  - Feature cards with icons
  - Interactive demonstrations
  - Benefit-focused messaging
  - Visual feature comparisons

#### Demo Section
- **File**: `demo-section.tsx`
- **Purpose**: Interactive product demonstration
- **Features**:
  - Embedded editor preview
  - Step-by-step walkthroughs
  - Live feature showcases
  - Video demonstrations

#### Pricing Section
- **File**: `pricing-section.tsx`
- **Purpose**: Pricing tiers and value proposition
- **Features**:
  - Pricing comparison table
  - Feature inclusion matrix
  - Popular plan highlighting
  - CTA buttons for each tier

#### Testimonials Section
- **File**: `testimonials-section.tsx`
- **Purpose**: Social proof and user stories
- **Features**:
  - Customer testimonials
  - User avatar and credentials
  - Quote carousel
  - Star ratings

#### FAQ Section
- **File**: `faq-section.tsx`
- **Purpose**: Address common questions
- **Features**:
  - Expandable Q&A items
  - Search functionality
  - Categorized questions
  - Contact support links

#### Footer
- **File**: `footer.tsx`
- **Purpose**: Site navigation and legal links
- **Features**:
  - Multi-column link organization
  - Social media links
  - Newsletter signup
  - Legal and policy links

## Editor Components

**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/components/editor/`

### Core Editor Layout

#### Canvas Component
- **File**: `Canvas/index.tsx`
- **Purpose**: Main editing canvas with Fabric.js integration
- **Features**:
  - Canvas initialization and lifecycle management
  - Object manipulation and rendering
  - Event handling (mouse, keyboard, touch)
  - Performance optimization with virtual scrolling

```typescript
interface CanvasProps {
  width: number
  height: number
  onCanvasReady?: (canvas: fabric.Canvas) => void
  className?: string
}

export function Canvas({ width, height, onCanvasReady, className }: CanvasProps) {
  // Canvas implementation
}
```

#### MenuBar
- **File**: `MenuBar/index.tsx`
- **Purpose**: Top application menu with file operations
- **Features**:
  - File menu (New, Open, Save, Export)
  - Edit menu (Undo, Redo, Copy, Paste)
  - View menu (Zoom, Fit, Grid)
  - Settings and preferences

**Sub-components**:
- `SettingsDialog.tsx`: Application settings modal

#### ToolPalette
- **File**: `ToolPalette/index.tsx`
- **Purpose**: Tool selection sidebar
- **Features**:
  - Categorized tool groups
  - Active tool highlighting
  - Keyboard shortcuts display
  - Tool search and filtering

```typescript
interface ToolPaletteProps {
  activeTool: string
  onToolSelect: (toolId: string) => void
  tools: Tool[]
}
```

#### ToolOptions
- **File**: `ToolOptions/index.tsx`
- **Purpose**: Context-sensitive tool options
- **Features**:
  - Dynamic option rendering based on active tool
  - Real-time parameter updates
  - Preset management
  - Option validation and constraints

**Sub-components**:
- `OptionButtonGroup.tsx`: Button group controls
- `OptionCheckbox.tsx`: Boolean option toggles
- `OptionColor.tsx`: Color picker controls
- `OptionDropdown.tsx`: Selection dropdowns
- `OptionNumber.tsx`: Numeric input controls
- `OptionSlider.tsx`: Range slider controls

#### StatusBar
- **File**: `StatusBar/index.tsx`
- **Purpose**: Bottom status information
- **Features**:
  - Document dimensions and zoom level
  - Cursor position coordinates
  - Selection information
  - Performance metrics

### Panel Components

#### AI Chat Panel
- **File**: `Panels/AIChat/index.tsx`
- **Purpose**: AI assistant interface
- **Features**:
  - Natural language input
  - Conversation history
  - Agent workflow display
  - Confidence indicators

**Sub-components**:
- `EnhancedAIChat.tsx`: Main chat interface
- `AgentModeToggle.tsx`: Agent mode selection
- `AgentStatusPart.tsx`: Agent status display
- `AgentWorkflowDisplay.tsx`: Workflow visualization

```typescript
interface AIChatProps {
  onMessageSend: (message: string) => void
  messages: ChatMessage[]
  isLoading?: boolean
  agentMode: 'simple' | 'advanced'
}
```

#### Layers Panel
- **File**: `Panels/LayersPanel/index.tsx`
- **Purpose**: Layer management interface
- **Features**:
  - Layer hierarchy visualization
  - Visibility and lock toggles
  - Opacity and blend mode controls
  - Drag-and-drop reordering

#### Character Panel
- **File**: `Panels/CharacterPanel/index.tsx`
- **Purpose**: Typography controls
- **Features**:
  - Font family selection
  - Font size and weight controls
  - Character and line spacing
  - Text color and alignment

**Sub-components**:
- `FontSelector.tsx`: Font family picker
- `FontSizeInput.tsx`: Size control
- `FontStyleButtons.tsx`: Bold, italic, underline
- `LetterSpacingControl.tsx`: Character spacing
- `LineHeightControl.tsx`: Line height adjustment
- `TextColorPicker.tsx`: Text color selection

#### Text Effects Panel
- **File**: `Panels/TextEffectsPanel/index.tsx`
- **Purpose**: Advanced text styling
- **Features**:
  - Drop shadow effects
  - Stroke and outline options
  - Gradient fills
  - Text warp and distortion
  - Effect presets

**Sub-components**:
- `DropShadowSection.tsx`: Shadow effects
- `GlowSection.tsx`: Glow and outer effects
- `GradientSection.tsx`: Gradient fills
- `StrokeSection.tsx`: Stroke and outline
- `TextPresetsSection.tsx`: Effect presets
- `TextWarpSection.tsx`: Text distortion

#### Paragraph Panel
- **File**: `Panels/ParagraphPanel/index.tsx`
- **Purpose**: Paragraph-level text controls
- **Features**:
  - Text alignment options
  - Indentation controls
  - Justification settings
  - Text direction support

**Sub-components**:
- `AlignmentButtons.tsx`: Text alignment
- `IndentControls.tsx`: Paragraph indentation
- `JustificationOptions.tsx`: Text justification
- `SpacingControls.tsx`: Paragraph spacing
- `TextDirectionControl.tsx`: LTR/RTL support

#### Glyphs Panel
- **File**: `Panels/GlyphsPanel/index.tsx`
- **Purpose**: Special character insertion
- **Features**:
  - Character grid display
  - Font-specific glyphs
  - Recently used characters
  - Unicode search

**Sub-components**:
- `GlyphGrid.tsx`: Character grid
- `RecentGlyphs.tsx`: Recent character history
- `glyphData.ts`: Character data definitions

### Adjustment Components

#### Options Bar
- **File**: `OptionsBar/index.tsx`
- **Purpose**: Selection-specific options
- **Features**:
  - Selection transformation controls
  - Boolean operation buttons
  - Selection refinement tools

**Sub-components**:
- `SelectionOptions.tsx`: Selection-specific controls

### Dialog Components

#### Adjustment Dialogs
- **Location**: `dialogs/`
- **Purpose**: Modal interfaces for image adjustments

**Available Dialogs**:
- `AdjustmentDialog.tsx`: Base adjustment dialog
- `BlurAdjustmentDialog.tsx`: Blur effect settings
- `BrightnessAdjustmentDialog.tsx`: Brightness controls
- `ColorTemperatureAdjustmentDialog.tsx`: Color temperature
- `ContrastAdjustmentDialog.tsx`: Contrast adjustment
- `ExposureAdjustmentDialog.tsx`: Exposure controls
- `HueAdjustmentDialog.tsx`: Hue adjustment
- `SaturationAdjustmentDialog.tsx`: Saturation controls
- `SharpenAdjustmentDialog.tsx`: Sharpening settings

#### AI Feature Dialogs
- `ImageGenerationDialog.tsx`: AI image generation
- `ImageTransformationDialog.tsx`: AI transformations
- `ReviewImageDialog.tsx`: Image review and approval

#### Utility Dialogs
- `NewDocumentDialog.tsx`: Create new document
- `AgentApprovalDialog.tsx`: Agent approval interface

### Comparison and Preview Components

#### ImageComparison
- **File**: `ImageComparison.tsx`
- **Purpose**: Before/after image comparison
- **Features**:
  - Side-by-side view
  - Slider-based comparison
  - Zoom and pan synchronization
  - Full-screen comparison mode

#### AlternativeGrid
- **File**: `AlternativeGrid.tsx`
- **Purpose**: Display multiple image variations
- **Features**:
  - Grid layout for alternatives
  - Hover previews
  - Selection highlighting
  - Batch operations

#### ConfidenceIndicator
- **File**: `ConfidenceIndicator.tsx`
- **Purpose**: Display AI confidence levels
- **Features**:
  - Visual confidence meters
  - Color-coded indicators
  - Tooltip explanations
  - Threshold visualization

## Shared UI Components

**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/components/ui/`

### Form Components

#### Input Controls
- `input.tsx`: Text input with validation
- `label.tsx`: Form labels with accessibility
- `checkbox.tsx`: Boolean checkbox controls
- `radio-group.tsx`: Radio button groups
- `select.tsx`: Dropdown selection
- `slider.tsx`: Range slider controls
- `switch.tsx`: Toggle switches

#### Layout Components
- `card.tsx`: Content cards with variants
- `tabs.tsx`: Tabbed interfaces
- `progress.tsx`: Progress indicators
- `badge.tsx`: Status badges
- `button.tsx`: Button component with variants

#### Navigation Components
- `dropdown-menu.tsx`: Context menus
- `popover.tsx`: Floating content
- `dialog.tsx`: Modal dialogs
- `scroll-area.tsx`: Custom scrollbars

#### Utility Components
- `feature-gate.tsx`: Feature availability gating
- `floating-panel.tsx`: Floating panels
- `toggle-group.tsx`: Toggle button groups
- `toggle.tsx`: Individual toggle buttons
- `alert.tsx`: Alert messages

## Global Dialog Components

**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/components/dialogs/`

#### NewDocumentDialog
- **Purpose**: Create new editing documents
- **Features**:
  - Preset size selection
  - Custom dimensions
  - Background color options
  - Template selection

## Component Patterns and Best Practices

### Design Patterns

#### Compound Components
Many editor panels use the compound component pattern for flexible composition:

```typescript
<CharacterPanel>
  <CharacterPanel.FontSelector />
  <CharacterPanel.FontSize />
  <CharacterPanel.FontStyle />
</CharacterPanel>
```

#### Render Props
Tool options use render props for dynamic content:

```typescript
<ToolOptions tool={activeTool}>
  {({ options, updateOption }) => (
    // Render tool-specific options
  )}
</ToolOptions>
```

#### Context Providers
Editor state is provided through React context:

```typescript
<EditorProvider>
  <CanvasProvider>
    <ToolProvider>
      <EditorLayout />
    </ToolProvider>
  </CanvasProvider>
</EditorProvider>
```

### Performance Optimizations

#### Memoization
Heavy components use React.memo and useMemo:

```typescript
const Canvas = memo(({ width, height, objects }: CanvasProps) => {
  const memoizedObjects = useMemo(() => 
    objects.map(obj => ({ ...obj, id: uuid() })), 
    [objects]
  )
  
  return <canvas>{/* implementation */}</canvas>
})
```

#### Virtualization
Large lists use virtual scrolling:

```typescript
// Layer panel with virtualized list
<VirtualizedList
  items={layers}
  renderItem={({ item }) => <LayerItem layer={item} />}
  height={400}
  itemHeight={48}
/>
```

#### Code Splitting
Heavy components are dynamically imported:

```typescript
const ImageGenerationDialog = dynamic(
  () => import('./dialogs/ImageGenerationDialog'),
  { ssr: false }
)
```

### Accessibility

#### Keyboard Navigation
All interactive components support keyboard navigation:

```typescript
const handleKeyDown = (e: KeyboardEvent) => {
  switch (e.key) {
    case 'Enter':
    case ' ':
      handleActivate()
      break
    case 'Escape':
      handleCancel()
      break
  }
}
```

#### Screen Reader Support
Components include ARIA attributes:

```typescript
<button
  aria-label="Adjust brightness"
  aria-describedby="brightness-help"
  aria-pressed={isActive}
>
  Brightness
</button>
```

#### Color Contrast
All UI elements meet WCAG AA contrast requirements with proper color schemes for light and dark modes.

### Type Safety

#### Component Props
All components use TypeScript interfaces:

```typescript
interface ToolOptionProps<T = unknown> {
  tool: Tool
  option: ToolOption<T>
  value: T
  onChange: (value: T) => void
  disabled?: boolean
}
```

#### Event Handlers
Event handlers are properly typed:

```typescript
interface CanvasEvents {
  onObjectSelected: (object: FabricObject) => void
  onObjectModified: (object: FabricObject) => void
  onSelectionCreated: (selection: Selection) => void
}
```

## Testing Strategy

### Unit Testing
Components are tested with React Testing Library:

```typescript
test('Canvas renders with correct dimensions', () => {
  render(<Canvas width={800} height={600} />)
  const canvas = screen.getByRole('img')
  expect(canvas).toHaveAttribute('width', '800')
  expect(canvas).toHaveAttribute('height', '600')
})
```

### Integration Testing
Editor workflows are tested end-to-end:

```typescript
test('brightness adjustment workflow', async () => {
  render(<Editor />)
  const brightnessTool = screen.getByRole('button', { name: /brightness/i })
  fireEvent.click(brightnessTool)
  
  const slider = screen.getByRole('slider')
  fireEvent.change(slider, { target: { value: '20' } })
  
  await waitFor(() => {
    expect(screen.getByText(/brightness adjusted/i)).toBeInTheDocument()
  })
})
```

This component architecture provides a solid foundation for building a complex photo editing interface while maintaining code quality, performance, and user experience.