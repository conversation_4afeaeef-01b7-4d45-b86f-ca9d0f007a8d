# FotoFun - Technology Stack

## Overview

FotoFun leverages cutting-edge web technologies to deliver a professional-grade photo editing experience that rivals desktop applications. The stack is carefully chosen for performance, developer experience, and future extensibility.

## Core Technology Stack

### Frontend Framework & Runtime

#### Next.js 15 (App Router)
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/next.config.ts`
- **Purpose**: Full-stack React framework with SSR, SSG, and API routes
- **Key Features**:
  - App Router for modern routing architecture
  - Built-in API routes for backend functionality
  - Optimized bundling and code splitting
  - Static generation for marketing pages
  - Server-side rendering for editor application

#### React 19
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/package.json`
- **Purpose**: UI library with latest concurrent features
- **Key Features**:
  - Concurrent rendering for smooth UI updates
  - Suspense for better loading states
  - Enhanced error boundaries
  - Improved server components

#### Bun Runtime
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/package.json` (scripts)
- **Purpose**: Fast JavaScript runtime and package manager
- **Benefits**:
  - 3x faster than npm/yarn for package installation
  - Built-in TypeScript support
  - Hot reload for rapid development
  - Optimized bundling and compilation

### Canvas & Graphics Engine

#### Fabric.js v6
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/canvas/CanvasManager.ts`
- **Purpose**: Professional-grade HTML5 canvas library
- **Capabilities**:
  - Object-based canvas manipulation
  - Advanced selection and transformation
  - Filter and effect system
  - Layer management
  - Event handling
  - Serialization/deserialization

```typescript
// Canvas initialization with Fabric.js
const canvas = new Canvas(element, {
  width,
  height,
  backgroundColor: bgColor,
  preserveObjectStacking: true,
  selection: true,
  renderOnAddRemove: true,
})
```

#### PIXI.js Integration (Planned)
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/package.json` (@pixi/react)
- **Purpose**: WebGL-accelerated rendering for performance-critical operations
- **Future Use Cases**:
  - Large image processing
  - Real-time filter previews
  - Complex selection operations

### State Management

#### Zustand
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/store/`
- **Purpose**: Lightweight, modern state management
- **Architecture**:
  ```typescript
  // Store structure with specific responsibilities
  ├── canvasStore.ts        # Canvas state and operations
  ├── toolStore.ts          # Active tool and tool state  
  ├── toolOptionsStore.ts   # Tool-specific options
  ├── layerStore.ts         # Layer management
  ├── selectionStore.ts     # Selection state
  ├── historyStore.ts       # Command history
  ├── documentStore.ts      # Document properties
  ├── colorStore.ts         # Color management
  ├── filterStore.ts        # Filter states
  └── performanceStore.ts   # Performance monitoring
  ```

**Benefits**:
- Zero boilerplate
- TypeScript-first design
- DevTools integration
- Minimal bundle size
- Excellent performance

### AI & Machine Learning

#### OpenAI SDK
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/providers.ts`
- **Purpose**: GPT-4 integration for natural language processing
- **Use Cases**:
  - Request analysis and routing
  - Tool parameter inference
  - Workflow planning
  - Confidence scoring

```typescript
import { openai } from '@ai-sdk/openai'

// AI tool analysis
const { object: analysis } = await generateObject({
  model: openai('gpt-4o'),
  schema: routeAnalysisSchema,
  prompt: `Analyze this photo editing request...`
})
```

#### AI SDK v5
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/adapters/base.ts`
- **Purpose**: Modern AI integration framework
- **Features**:
  - Tool calling support
  - Streaming responses
  - Type-safe schemas with Zod
  - Multi-provider support

#### Replicate API
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/server/replicateClient.ts`
- **Purpose**: AI model hosting platform
- **Integrated Models**:
  - **Stable Diffusion XL**: Image generation
  - **Bria RMBG**: Background removal
  - **Google Upscaler**: 2x/4x image upscaling
  - **LaMa Inpainting**: Object removal with intelligent fill

```typescript
// Replicate model integration
export const STABLE_DIFFUSION_XL = "stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b"

export async function generateImage(input: ImageGenerationInput) {
  return await replicate.run(STABLE_DIFFUSION_XL, { input })
}
```

### Database & Authentication

#### Supabase
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/db/supabase/`
- **Purpose**: Complete backend-as-a-service
- **Services Used**:
  - **PostgreSQL Database**: User data, documents, history
  - **Authentication**: JWT-based auth with OAuth providers
  - **Real-time**: Live collaboration features (planned)
  - **Storage**: Document and asset storage
  - **Row Level Security**: Data protection

```typescript
// Supabase client configuration
export const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_ANON_KEY!,
  {
    auth: {
      flowType: 'pkce'
    }
  }
)
```

#### Drizzle ORM
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/db/schema.ts`
- **Purpose**: Type-safe database operations
- **Benefits**:
  - Full TypeScript support
  - Zero runtime overhead
  - SQL-like query builder
  - Migration management

### UI & Design System

#### Tailwind CSS v4
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/tailwindcss`
- **Purpose**: Utility-first CSS framework
- **Features**:
  - JIT compilation for optimal bundle size
  - Built-in dark mode support
  - Responsive design utilities
  - Custom animation system

#### Radix UI
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/components/ui/`
- **Purpose**: Unstyled, accessible UI primitives
- **Components Used**:
  - Dialog, Popover, Select
  - Slider, Progress, Checkbox
  - Dropdown Menu, Tabs, Toggle
  - Radio Group, Switch, Scroll Area

```typescript
// Radix component example
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
```

#### Lucide React
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/package.json`
- **Purpose**: Beautiful, customizable SVG icons
- **Usage**: Tool icons, UI elements, status indicators

#### Shadcn/ui Design System
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/components.json`
- **Purpose**: Copy-paste component library built on Radix
- **Benefits**:
  - Fully customizable components
  - Accessible by default
  - TypeScript support
  - Tailwind CSS integration

### Development Tools & Quality

#### TypeScript
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/tsconfig.json`
- **Purpose**: Type-safe JavaScript development
- **Configuration**:
  - Strict mode enabled
  - Path mapping for clean imports
  - React 19 types
  - Node.js compatibility

#### ESLint
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/eslint.config.mjs`
- **Purpose**: Code quality and consistency
- **Rules**:
  - Next.js recommended rules
  - React hooks rules
  - TypeScript-specific rules
  - Custom project rules

#### Zod
- **Path**: Throughout `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/adapters/`
- **Purpose**: Schema validation and type inference
- **Use Cases**:
  - API request/response validation
  - AI tool parameter schemas
  - Form validation
  - Environment variable validation

```typescript
// Zod schema example
const brightnessSchema = z.object({
  adjustment: z.number()
    .min(-100).max(100)
    .describe('Brightness adjustment (-100 to +100)')
})
```

### Testing & Quality Assurance

#### Vitest
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/package.json`
- **Purpose**: Fast unit testing framework
- **Features**:
  - Vite-powered testing
  - TypeScript support
  - Mock support
  - Coverage reporting

#### Testing Library
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/package.json`
- **Purpose**: React component testing
- **Libraries**:
  - `@testing-library/react`
  - `@testing-library/user-event`

### Build & Bundling

#### Turbopack
- **Enabled**: `bun dev --turbopack`
- **Purpose**: Next-generation bundler for development
- **Benefits**:
  - 10x faster than Webpack
  - Incremental computation
  - Better error messages
  - Hot module replacement

#### PostCSS
- **Path**: `/Users/<USER>/Projects/own/assistant/foto-fun/postcss.config.mjs`
- **Purpose**: CSS processing and optimization
- **Plugins**:
  - Tailwind CSS processing
  - Autoprefixer for browser compatibility

### Utility Libraries

#### Class Variance Authority (CVA)
- **Purpose**: Type-safe variant-based styling
- **Usage**: Component styling with variants

#### clsx & tailwind-merge
- **Purpose**: Conditional CSS class management
- **Benefits**: Clean class name composition

#### date-fns
- **Purpose**: Modern date utility library
- **Usage**: Date formatting and manipulation

#### file-saver
- **Purpose**: Client-side file downloads
- **Usage**: Exporting edited images

#### uuid
- **Purpose**: Unique identifier generation
- **Usage**: Object IDs, session tracking

## Performance Considerations

### Bundle Optimization

**Code Splitting**:
- Next.js automatic code splitting
- Dynamic imports for heavy components
- Lazy loading for non-critical features

**Tree Shaking**:
- ES modules for optimal tree shaking
- Selective imports from large libraries
- Unused code elimination

### Runtime Performance

**Canvas Optimization**:
- Object pooling for Fabric.js objects
- Debounced canvas updates
- Incremental pixel map updates
- Dirty object tracking

**Memory Management**:
- Canvas disposal on cleanup
- Filter garbage collection
- Selection overlay optimization

### Loading Performance

**Image Optimization**:
- Next.js automatic image optimization
- WebP/AVIF format support
- Responsive image loading
- Lazy loading for non-critical images

## Security Stack

### Input Validation
- **Zod schemas**: All user inputs validated
- **File type validation**: Safe image uploads
- **Parameter sanitization**: XSS prevention

### Authentication Security
- **JWT tokens**: Secure session management
- **PKCE flow**: OAuth security enhancement
- **Row-level security**: Database-level protection

### API Security
- **Rate limiting**: Abuse prevention
- **API key validation**: Secure third-party access
- **Request size limits**: DoS protection

## Development Workflow

### Package Management
```bash
# Fast package installation
bun install

# Add dependencies
bun add <package>

# Development server (with Turbopack)
bun dev

# Production build
bun run build
```

### Code Quality
```bash
# Type checking
bun typecheck

# Linting
bun lint

# Testing
bun test
```

### Database Management
```bash
# Generate migrations
bun run db:generate

# Run migrations
bun run db:migrate

# Database studio
bun run db:studio
```

## Deployment Stack

### Hosting Options

**Vercel (Recommended)**:
- Native Next.js support
- Edge runtime for API routes
- Automatic deployments
- Global CDN

**Self-hosted**:
- Docker containerization
- Node.js/Bun runtime
- Nginx reverse proxy
- SSL/TLS termination

### Environment Variables
```bash
# AI Services
REPLICATE_API_TOKEN=
OPENAI_API_KEY=

# Database
SUPABASE_URL=
SUPABASE_ANON_KEY=

# Analytics (Optional)
NEXT_PUBLIC_ANALYTICS_ID=
```

## Future Technology Considerations

### WebGL Migration
- **Konva.js**: Better WebGL performance
- **Three.js**: 3D capabilities
- **WebGPU**: Next-generation graphics API

### Real-time Features
- **WebRTC**: Peer-to-peer collaboration
- **WebSockets**: Live cursor sharing
- **Operational transforms**: Conflict resolution

### AI Enhancements
- **Local models**: WebAssembly AI
- **Streaming**: Real-time AI responses
- **Custom models**: Fine-tuned editing models

### Mobile Support
- **React Native**: Native mobile apps
- **Capacitor**: Hybrid mobile apps
- **PWA**: Progressive web app features

This technology stack provides a solid foundation for building a modern, performant, and scalable photo editing platform while maintaining developer productivity and code quality.