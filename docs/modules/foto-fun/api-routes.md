# FotoFun - API Routes Architecture

## Overview

FotoFun's API routes are built using **Next.js 15 App Router** with a focus on AI integration, external service orchestration, and real-time communication. The architecture follows RESTful principles while providing specialized endpoints for AI-powered photo editing workflows.

## API Architecture

### Route Structure

```
app/api/
├── ai/                     # AI-powered endpoints
│   ├── chat/              # Main AI chat orchestration
│   ├── debug/             # AI debugging tools
│   └── replicate/         # External AI model integrations
│       ├── generate-image/
│       ├── inpaint/
│       ├── remove-background/
│       └── upscale-image/
├── auth/                  # Authentication endpoints
│   └── callback/          # OAuth callbacks
├── debug-saturation/      # Development debugging
└── test-exposure/         # Testing endpoints
```

### Request/Response Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Route
    participant Agent as AI Agent
    participant External as External Service
    participant DB as Database
    
    C->>API: POST /api/ai/chat
    API->>Agent: Initialize MasterRoutingAgent
    Agent->>Agent: Analyze request
    Agent->>API: Return execution plan
    API->>External: Call external AI service
    External->>API: Return AI response
    API->>DB: Log conversation
    API->>C: Stream response
```

## AI Endpoints

### 1. AI Chat Endpoint (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/ai/chat/route.ts`)

The central AI orchestration endpoint that handles natural language requests and coordinates tool execution.

```typescript
// POST /api/ai/chat
export async function POST(req: Request) {
  const { messages, canvasContext, aiSettings } = await req.json()
  
  // Initialize AI tools from adapter registry
  await initialize()
  const aiTools = adapterRegistry.getAITools()
  
  return streamText({
    model: openai('gpt-4o'),
    messages: convertToModelMessages(messages),
    tools: {
      // Cost approval for expensive operations
      requestCostApproval: tool({
        description: 'Request user approval for expensive external API operations',
        inputSchema: z.object({
          toolName: z.string(),
          operation: z.string(),
          estimatedCost: z.number(),
          details: z.string()
        }),
        execute: async ({ toolName, operation, estimatedCost, details }) => {
          return {
            type: 'cost-approval-required',
            toolName,
            operation,
            estimatedCost,
            details,
            message: `This operation will cost approximately $${estimatedCost.toFixed(3)}`
          }
        }
      }),
      
      // Multi-step workflow execution
      executeAgentWorkflow: tool({
        description: 'Execute complex multi-step photo editing workflow',
        inputSchema: z.object({
          request: z.string().describe('The user request to execute')
        }),
        execute: async ({ request }) => {
          return await executeMultiStepWorkflow(request, messages, canvasContext, aiSettings)
        }
      }),
      
      // All canvas tools
      ...aiTools
    },
    system: `You are FotoFun's AI assistant. Route requests appropriately:
    
    1. **Simple Operations**: Direct tool calls for single operations
    2. **Complex Workflows**: Use executeAgentWorkflow for multi-step operations
    3. **AI-Native Tools**: Always use requestCostApproval first
    
    Canvas: ${canvasContext.dimensions.width}x${canvasContext.dimensions.height}px
    Has content: ${canvasContext.hasContent}
    Has selection: ${canvasContext.hasSelection || false}
    
    Available tools:
    - Canvas editing: ${adapterRegistry.getToolNamesByCategory('canvas-editing').join(', ')}
    - AI-native: ${adapterRegistry.getToolNamesByCategory('ai-native').join(', ')}`
  }).toUIMessageStreamResponse()
}
```

#### Multi-Step Workflow Execution

```typescript
async function executeMultiStepWorkflow(
  request: string,
  messages: UIMessage[],
  canvasContext: CanvasContext,
  aiSettings: AISettings
): Promise<WorkflowResult> {
  // Create mock canvas for server-side operations
  const mockCanvas = createMockCanvas(canvasContext)
  
  // Create agent context
  const agentContext: AgentContext = {
    canvas: mockCanvas,
    conversation: messages,
    workflowMemory: new WorkflowMemory(mockCanvas),
    userPreferences: {
      autoApprovalThreshold: aiSettings?.autoApproveThreshold || 0.8,
      maxAutonomousSteps: 10,
      showConfidenceScores: aiSettings?.showConfidenceScores ?? true,
      showApprovalDecisions: aiSettings?.showApprovalDecisions ?? true,
    },
    canvasAnalysis: {
      dimensions: canvasContext.dimensions,
      hasContent: canvasContext.hasContent,
      objectCount: canvasContext.objectCount || 0,
      lastAnalyzedAt: Date.now()
    }
  }
  
  // Execute with master routing agent
  const masterAgent = new MasterRoutingAgent(agentContext)
  const agentResult = await masterAgent.execute(request)
  
  // Extract tool executions for client-side execution
  const toolExecutions = agentResult.results.map(stepResult => {
    const data = stepResult.data as WorkflowStepData
    if (data?.toolName && data?.params) {
      return {
        toolName: data.toolName,
        params: data.params,
        description: data.description,
        confidence: stepResult.confidence
      }
    }
    return null
  }).filter(Boolean)
  
  // Return structured workflow result
  return {
    success: agentResult.completed,
    workflow: {
      description: `Multi-step workflow: ${request}`,
      steps: toolExecutions,
      agentType: 'sequential',
      totalSteps: toolExecutions.length,
      reasoning: extractReasoning(agentResult)
    },
    agentStatus: {
      confidence: calculateOverallConfidence(agentResult),
      approvalRequired: requiresApproval(agentResult, agentContext),
      threshold: agentContext.userPreferences.autoApprovalThreshold
    },
    statusUpdates: extractStatusUpdates(agentResult),
    toolExecutions,
    message: `Planned ${toolExecutions.length} steps. Now executing each tool...`
  }
}
```

### 2. AI Debug Endpoint (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/ai/debug/route.ts`)

Development endpoint for debugging AI agent behavior and tool execution.

```typescript
// POST /api/ai/debug
export async function POST(req: Request) {
  const { request, canvasContext, debugMode } = await req.json()
  
  // Enhanced logging for debug mode
  console.log('=== AI DEBUG MODE ===')
  console.log('Request:', request)
  console.log('Canvas Context:', canvasContext)
  console.log('Debug Mode:', debugMode)
  
  // Create debug agent with enhanced logging
  const debugAgent = new DebugMasterRoutingAgent(canvasContext)
  
  try {
    const result = await debugAgent.execute(request)
    
    return NextResponse.json({
      success: true,
      result,
      debug: {
        agentType: debugAgent.name,
        executionTime: debugAgent.getExecutionTime(),
        memoryUsage: debugAgent.getMemoryUsage(),
        toolsUsed: debugAgent.getToolsUsed(),
        routingDecisions: debugAgent.getRoutingDecisions()
      }
    })
  } catch (error) {
    console.error('Debug execution error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      debug: {
        stackTrace: error instanceof Error ? error.stack : undefined,
        agentState: debugAgent.getState()
      }
    })
  }
}
```

### 3. Replicate AI Integration Endpoints

#### Image Generation (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/ai/replicate/generate-image/route.ts`)

```typescript
// POST /api/ai/replicate/generate-image
export async function POST(req: Request) {
  const { prompt, width = 512, height = 512, style = 'photographic' } = await req.json()
  
  // Validate request
  if (!prompt || prompt.trim().length === 0) {
    return NextResponse.json(
      { error: 'Prompt is required' },
      { status: 400 }
    )
  }
  
  // Cost estimation
  const estimatedCost = calculateImageGenerationCost(width, height)
  
  try {
    console.log('Generating image:', { prompt, width, height, style })
    
    // Call Replicate API
    const result = await runImageGeneration({
      prompt,
      width,
      height,
      style,
      num_outputs: 1,
      guidance_scale: 7.5,
      num_inference_steps: 20,
      scheduler: 'K_EULER'
    })
    
    if (!result || !result.length) {
      throw new Error('No image generated')
    }
    
    const imageUrl = result[0]
    console.log('Image generated successfully:', imageUrl)
    
    return NextResponse.json({
      success: true,
      imageUrl,
      dimensions: { width, height },
      cost: estimatedCost,
      metadata: {
        prompt,
        style,
        model: 'stable-diffusion-xl',
        generatedAt: new Date().toISOString()
      }
    })
    
  } catch (error) {
    console.error('Image generation error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Image generation failed',
      cost: estimatedCost
    }, { status: 500 })
  }
}

function calculateImageGenerationCost(width: number, height: number): number {
  const baseCost = 0.012 // Base cost per image
  const pixelMultiplier = (width * height) / (512 * 512)
  return baseCost * pixelMultiplier
}
```

#### Background Removal (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/ai/replicate/remove-background/route.ts`)

```typescript
// POST /api/ai/replicate/remove-background
export async function POST(req: Request) {
  const { imageUrl, returnMask = false } = await req.json()
  
  if (!imageUrl) {
    return NextResponse.json(
      { error: 'Image URL is required' },
      { status: 400 }
    )
  }
  
  try {
    console.log('Removing background from:', imageUrl)
    
    // Call Bria background removal model
    const result = await runBackgroundRemoval({
      image: imageUrl,
      return_mask: returnMask
    })
    
    const response: BackgroundRemovalResult = {
      success: true,
      processedImageUrl: result.processedImage,
      cost: 0.003 // Fixed cost for background removal
    }
    
    if (returnMask && result.mask) {
      response.maskImageUrl = result.mask
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('Background removal error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Background removal failed',
      cost: 0.003
    }, { status: 500 })
  }
}
```

#### Image Upscaling (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/ai/replicate/upscale-image/route.ts`)

```typescript
// POST /api/ai/replicate/upscale-image
export async function POST(req: Request) {
  const { imageUrl, scale = 2, face_enhance = false } = await req.json()
  
  if (!imageUrl) {
    return NextResponse.json(
      { error: 'Image URL is required' },
      { status: 400 }
    )
  }
  
  if (![2, 4].includes(scale)) {
    return NextResponse.json(
      { error: 'Scale must be 2 or 4' },
      { status: 400 }
    )
  }
  
  try {
    console.log('Upscaling image:', { imageUrl, scale, face_enhance })
    
    // Call Google's upscaler model
    const result = await runImageUpscaling({
      image: imageUrl,
      scale,
      face_enhance
    })
    
    return NextResponse.json({
      success: true,
      processedImageUrl: result.upscaledImage,
      originalDimensions: result.originalDimensions,
      newDimensions: result.newDimensions,
      scaleFactor: scale,
      cost: calculateUpscalingCost(scale)
    })
    
  } catch (error) {
    console.error('Image upscaling error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Image upscaling failed',
      cost: calculateUpscalingCost(scale)
    }, { status: 500 })
  }
}

function calculateUpscalingCost(scale: number): number {
  return scale === 2 ? 0.005 : 0.010
}
```

#### Inpainting (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/ai/replicate/inpaint/route.ts`)

```typescript
// POST /api/ai/replicate/inpaint
export async function POST(req: Request) {
  const { imageUrl, maskUrl, prompt, negativePrompt } = await req.json()
  
  if (!imageUrl || !maskUrl) {
    return NextResponse.json(
      { error: 'Image URL and mask URL are required' },
      { status: 400 }
    )
  }
  
  try {
    console.log('Inpainting image:', { imageUrl, maskUrl, prompt })
    
    // Call Stable Diffusion inpainting model
    const result = await runInpainting({
      image: imageUrl,
      mask: maskUrl,
      prompt: prompt || 'high quality, detailed',
      negative_prompt: negativePrompt || 'blurry, low quality',
      num_inference_steps: 20,
      guidance_scale: 7.5,
      strength: 0.8
    })
    
    return NextResponse.json({
      success: true,
      processedImageUrl: result.inpaintedImage,
      cost: 0.008,
      metadata: {
        prompt,
        negativePrompt,
        model: 'stable-diffusion-inpainting',
        processedAt: new Date().toISOString()
      }
    })
    
  } catch (error) {
    console.error('Inpainting error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Inpainting failed',
      cost: 0.008
    }, { status: 500 })
  }
}
```

## Authentication Endpoints

### OAuth Callback (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/auth/callback/route.ts`)

```typescript
// GET /api/auth/callback
export async function GET(req: Request) {
  const { searchParams } = new URL(req.url)
  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/'
  
  if (!code) {
    return NextResponse.redirect(new URL('/auth/auth-code-error', req.url))
  }
  
  const supabase = createRouteHandlerClient({ cookies })
  
  try {
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (error) {
      console.error('Auth callback error:', error)
      return NextResponse.redirect(new URL('/auth/auth-code-error', req.url))
    }
    
    // Update user profile
    if (data.user) {
      await updateUserProfile(data.user)
    }
    
    // Redirect to original destination
    const redirectUrl = new URL(next, req.url)
    return NextResponse.redirect(redirectUrl)
    
  } catch (error) {
    console.error('Auth callback error:', error)
    return NextResponse.redirect(new URL('/auth/auth-code-error', req.url))
  }
}

async function updateUserProfile(user: User): Promise<void> {
  const supabase = createRouteHandlerClient({ cookies })
  
  const { error } = await supabase
    .from('users')
    .upsert({
      id: user.id,
      email: user.email,
      last_sign_in: new Date().toISOString()
    })
  
  if (error) {
    console.error('Failed to update user profile:', error)
  }
}
```

## Testing & Development Endpoints

### Debug Saturation (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/debug-saturation/route.ts`)

```typescript
// POST /api/debug-saturation
export async function POST(req: Request) {
  const { imageData, adjustment } = await req.json()
  
  if (!imageData || typeof adjustment !== 'number') {
    return NextResponse.json(
      { error: 'Invalid parameters' },
      { status: 400 }
    )
  }
  
  try {
    // Simulate saturation adjustment
    const result = await debugSaturationAdjustment(imageData, adjustment)
    
    return NextResponse.json({
      success: true,
      result,
      debug: {
        originalAdjustment: adjustment,
        appliedAdjustment: result.actualAdjustment,
        processingTime: result.processingTime,
        algorithm: 'HSL_SATURATION'
      }
    })
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Debug failed'
    }, { status: 500 })
  }
}
```

### Test Exposure (`/Users/<USER>/Projects/own/assistant/foto-fun/app/api/test-exposure/route.ts`)

```typescript
// POST /api/test-exposure
export async function POST(req: Request) {
  const { imageData, exposure } = await req.json()
  
  try {
    const startTime = performance.now()
    
    // Test exposure adjustment algorithm
    const result = await testExposureAdjustment(imageData, exposure)
    
    const endTime = performance.now()
    const processingTime = endTime - startTime
    
    return NextResponse.json({
      success: true,
      result,
      performance: {
        processingTime,
        pixelsProcessed: result.pixelsProcessed,
        pixelsPerSecond: result.pixelsProcessed / (processingTime / 1000)
      }
    })
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Test failed'
    }, { status: 500 })
  }
}
```

## Error Handling & Middleware

### Global Error Handler

```typescript
// Centralized error handling
class APIError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export function handleAPIError(error: unknown): NextResponse {
  console.error('API Error:', error)
  
  if (error instanceof APIError) {
    return NextResponse.json({
      error: error.message,
      code: error.code
    }, { status: error.statusCode })
  }
  
  if (error instanceof Error) {
    return NextResponse.json({
      error: error.message
    }, { status: 500 })
  }
  
  return NextResponse.json({
    error: 'Internal server error'
  }, { status: 500 })
}
```

### Request Validation

```typescript
// Zod schemas for request validation
const ImageGenerationSchema = z.object({
  prompt: z.string().min(1).max(1000),
  width: z.number().min(256).max(1024).optional(),
  height: z.number().min(256).max(1024).optional(),
  style: z.enum(['photographic', 'artistic', 'digital-art']).optional()
})

const BackgroundRemovalSchema = z.object({
  imageUrl: z.string().url(),
  returnMask: z.boolean().optional()
})

// Validation middleware
export function validateRequest<T>(schema: z.ZodSchema<T>) {
  return async (req: Request): Promise<T> => {
    try {
      const body = await req.json()
      return schema.parse(body)
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new APIError(
          `Validation error: ${error.errors.map(e => e.message).join(', ')}`,
          400,
          'VALIDATION_ERROR'
        )
      }
      throw error
    }
  }
}
```

### Rate Limiting

```typescript
// Simple rate limiting implementation
class RateLimiter {
  private requests = new Map<string, number[]>()
  
  isAllowed(key: string, limit: number, windowMs: number): boolean {
    const now = Date.now()
    const windowStart = now - windowMs
    
    // Get or create request history
    const history = this.requests.get(key) || []
    
    // Filter out old requests
    const recentRequests = history.filter(time => time > windowStart)
    
    // Check if under limit
    if (recentRequests.length < limit) {
      recentRequests.push(now)
      this.requests.set(key, recentRequests)
      return true
    }
    
    return false
  }
}

const rateLimiter = new RateLimiter()

// Rate limiting middleware
export function rateLimit(limit: number = 100, windowMs: number = 60000) {
  return (req: Request) => {
    const key = getClientKey(req)
    
    if (!rateLimiter.isAllowed(key, limit, windowMs)) {
      throw new APIError('Rate limit exceeded', 429, 'RATE_LIMIT_EXCEEDED')
    }
  }
}

function getClientKey(req: Request): string {
  // Use IP address or user ID as key
  const forwarded = req.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : 'unknown'
  return ip
}
```

## Response Formatting

### Standard Response Format

```typescript
interface APIResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  code?: string
  timestamp?: string
}

export function createSuccessResponse<T>(data: T): APIResponse<T> {
  return {
    success: true,
    data,
    timestamp: new Date().toISOString()
  }
}

export function createErrorResponse(
  error: string,
  code?: string
): APIResponse {
  return {
    success: false,
    error,
    code,
    timestamp: new Date().toISOString()
  }
}
```

### Streaming Responses

```typescript
// Streaming response for AI chat
export function createStreamingResponse(
  stream: ReadableStream<Uint8Array>
): Response {
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
      'Transfer-Encoding': 'chunked'
    }
  })
}

// Server-sent events for real-time updates
export function createSSEResponse(): Response {
  const encoder = new TextEncoder()
  
  const stream = new ReadableStream({
    start(controller) {
      // Send initial connection event
      controller.enqueue(
        encoder.encode('data: {"type": "connected"}\n\n')
      )
    }
  })
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  })
}
```

## External Service Integration

### Replicate Client (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/server/replicateClient.ts`)

```typescript
import Replicate from 'replicate'

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN
})

// Model endpoints
export const STABLE_DIFFUSION_XL = 'stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b'
export const BACKGROUND_REMOVAL = 'briaai/bria-background-removal:3619b5d8c1e83f3b4e8f3e6c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c'
export const IMAGE_UPSCALER = 'google/upscaler:4739e8b8f3b3d7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c'

// Image generation
export async function runImageGeneration(input: ImageGenerationInput): Promise<string[]> {
  const output = await replicate.run(STABLE_DIFFUSION_XL, { input })
  return output as string[]
}

// Background removal
export async function runBackgroundRemoval(input: BackgroundRemovalInput): Promise<BackgroundRemovalOutput> {
  const output = await replicate.run(BACKGROUND_REMOVAL, { input })
  return output as BackgroundRemovalOutput
}

// Image upscaling
export async function runImageUpscaling(input: UpscalingInput): Promise<UpscalingOutput> {
  const output = await replicate.run(IMAGE_UPSCALER, { input })
  return output as UpscalingOutput
}

// Inpainting
export async function runInpainting(input: InpaintingInput): Promise<InpaintingOutput> {
  const output = await replicate.run(STABLE_DIFFUSION_XL, { input })
  return output as InpaintingOutput
}
```

### OpenAI Integration (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/providers.ts`)

```typescript
import { openai } from 'ai'

export const openaiClient = openai({
  apiKey: process.env.OPENAI_API_KEY
})

// Model configurations
export const GPT_4O = 'gpt-4o'
export const GPT_4O_MINI = 'gpt-4o-mini'

// Usage tracking
export function trackOpenAIUsage(
  model: string,
  promptTokens: number,
  completionTokens: number
): void {
  console.log('OpenAI usage:', {
    model,
    promptTokens,
    completionTokens,
    totalTokens: promptTokens + completionTokens
  })
  
  // TODO: Store usage in database for billing
}
```

## Performance Optimizations

### Caching Strategy

```typescript
// Simple in-memory cache
class APICache {
  private cache = new Map<string, CacheEntry>()
  private readonly TTL = 5 * 60 * 1000 // 5 minutes
  
  set(key: string, value: unknown): void {
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    })
  }
  
  get(key: string): unknown | null {
    const entry = this.cache.get(key)
    if (!entry) return null
    
    // Check if expired
    if (Date.now() - entry.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }
    
    return entry.value
  }
  
  clear(): void {
    this.cache.clear()
  }
}

const apiCache = new APICache()

// Cache middleware
export function cacheResponse(key: string, ttl?: number) {
  return {
    get: () => apiCache.get(key),
    set: (value: unknown) => apiCache.set(key, value)
  }
}
```

### Request Deduplication

```typescript
// Deduplicate identical requests
class RequestDeduplicator {
  private pending = new Map<string, Promise<unknown>>()
  
  async deduplicate<T>(
    key: string,
    fn: () => Promise<T>
  ): Promise<T> {
    // Check if request is already pending
    if (this.pending.has(key)) {
      return this.pending.get(key) as Promise<T>
    }
    
    // Create new request
    const promise = fn().finally(() => {
      this.pending.delete(key)
    })
    
    this.pending.set(key, promise)
    return promise
  }
}

const deduplicator = new RequestDeduplicator()

// Usage in API route
export async function POST(req: Request) {
  const { prompt } = await req.json()
  const key = `generate-${hashString(prompt)}`
  
  return deduplicator.deduplicate(key, async () => {
    return await generateImage(prompt)
  })
}
```

## Security Measures

### API Key Validation

```typescript
// Validate API keys
export function validateAPIKey(req: Request): boolean {
  const apiKey = req.headers.get('x-api-key')
  
  if (!apiKey) {
    return false
  }
  
  // Validate against allowed keys
  const allowedKeys = process.env.ALLOWED_API_KEYS?.split(',') || []
  return allowedKeys.includes(apiKey)
}
```

### CORS Configuration

```typescript
// CORS middleware
export function cors(req: Request): Response | null {
  const origin = req.headers.get('origin')
  const allowedOrigins = [
    'http://localhost:3000',
    'https://fotofun.app'
  ]
  
  if (origin && !allowedOrigins.includes(origin)) {
    return new Response('CORS error', { status: 403 })
  }
  
  return null
}
```

### Input Sanitization

```typescript
// Sanitize user inputs
export function sanitizeInput(input: string): string {
  return input
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .trim()
}
```

## Future Enhancements

### GraphQL Integration

```typescript
// Planned GraphQL endpoint
// POST /api/graphql
export async function POST(req: Request) {
  const { query, variables } = await req.json()
  
  const result = await graphqlExecute({
    schema: fotofunSchema,
    source: query,
    variableValues: variables,
    contextValue: createGraphQLContext(req)
  })
  
  return NextResponse.json(result)
}
```

### WebSocket Support

```typescript
// Real-time collaboration endpoint
// GET /api/ws
export async function GET(req: Request) {
  const { socket, response } = Deno.upgradeWebSocket(req)
  
  socket.onopen = () => {
    console.log('WebSocket connected')
  }
  
  socket.onmessage = (event) => {
    const message = JSON.parse(event.data)
    handleWebSocketMessage(socket, message)
  }
  
  return response
}
```

### Batch Processing

```typescript
// Batch API endpoint
// POST /api/batch
export async function POST(req: Request) {
  const { requests } = await req.json()
  
  const results = await Promise.allSettled(
    requests.map(async (request: BatchRequest) => {
      return await processRequest(request)
    })
  )
  
  return NextResponse.json({
    success: true,
    results: results.map(result => ({
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : undefined,
      error: result.status === 'rejected' ? result.reason : undefined
    }))
  })
}
```

## API Benefits

### 1. Performance
- **Streaming responses**: Real-time AI communication
- **Efficient caching**: Reduce redundant API calls
- **Request deduplication**: Prevent duplicate processing
- **Optimized routing**: Fast request handling

### 2. Reliability
- **Error handling**: Comprehensive error management
- **Rate limiting**: Prevent abuse and overload
- **Validation**: Input validation and sanitization
- **Monitoring**: Request tracking and analytics

### 3. Security
- **Authentication**: Secure user authentication
- **Authorization**: Role-based access control
- **CORS protection**: Cross-origin request security
- **Input sanitization**: Prevent injection attacks

### 4. Scalability
- **Modular design**: Easy to extend and maintain
- **External service integration**: Flexible AI model support
- **Async processing**: Non-blocking operations
- **Load balancing**: Horizontal scaling support

This API architecture provides a robust foundation for FotoFun's AI-powered photo editing capabilities while maintaining security, performance, and scalability.