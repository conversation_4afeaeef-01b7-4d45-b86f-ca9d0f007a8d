# FotoFun - UI Design System

## Overview

FotoFun's UI design system is built on modern web technologies with a focus on **accessibility**, **performance**, and **professional aesthetics**. The system leverages **Radix UI primitives**, **Tailwind CSS**, and **custom components** to create a cohesive, scalable interface that supports both light and dark themes.

## Design Principles

### 1. Professional First
- **Clean aesthetics**: Minimal, distraction-free interface
- **Spatial hierarchy**: Clear visual organization
- **Consistent branding**: Unified color palette and typography
- **Tool-focused**: Interface serves the creative process

### 2. Accessibility by Design
- **WCAG 2.1 AA compliance**: Full accessibility support
- **Keyboard navigation**: Complete keyboard-only operation
- **Screen reader support**: Semantic HTML and ARIA labels
- **Color contrast**: Sufficient contrast ratios for readability

### 3. Performance Optimized
- **Lazy loading**: Components load on demand
- **Efficient rendering**: Optimized re-render cycles
- **Small bundle size**: Tree-shaking and code splitting
- **Smooth animations**: 60fps animations with hardware acceleration

### 4. Responsive Design
- **Mobile-first**: Responsive design principles
- **Flexible layouts**: Adaptable to different screen sizes
- **Touch-friendly**: Appropriate touch targets
- **Progressive enhancement**: Works on all devices

## Component Architecture

### Base Components (`/Users/<USER>/Projects/own/assistant/foto-fun/components/ui/`)

#### Button Component
```typescript
// File: button.tsx
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline'
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
)
```

#### Input Component
```typescript
// File: input.tsx
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean
  helperText?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, helperText, ...props }, ref) => {
    return (
      <div className="space-y-1">
        <input
          type={type}
          className={cn(
            'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            error && 'border-destructive focus-visible:ring-destructive',
            className
          )}
          ref={ref}
          {...props}
        />
        {helperText && (
          <p className={cn(
            'text-sm',
            error ? 'text-destructive' : 'text-muted-foreground'
          )}>
            {helperText}
          </p>
        )}
      </div>
    )
  }
)
```

#### Dialog Component
```typescript
// File: dialog.tsx
import * as DialogPrimitive from '@radix-ui/react-dialog'

const Dialog = DialogPrimitive.Root
const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      'fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className
    )}
    {...props}
  />
))

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
```

### Editor-Specific Components (`/Users/<USER>/Projects/own/assistant/foto-fun/components/editor/`)

#### Canvas Component
```typescript
// File: Canvas/index.tsx
interface CanvasProps {
  width: number
  height: number
  className?: string
  onReady?: (canvas: fabric.Canvas) => void
}

export function Canvas({ width, height, className, onReady }: CanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const { fabricCanvas, initCanvas, isReady } = useCanvasStore()
  
  useEffect(() => {
    if (canvasRef.current && !fabricCanvas) {
      initCanvas(canvasRef.current, width, height)
    }
  }, [width, height])
  
  useEffect(() => {
    if (isReady && fabricCanvas && onReady) {
      onReady(fabricCanvas)
    }
  }, [isReady, fabricCanvas, onReady])
  
  return (
    <div className={cn('relative overflow-hidden', className)}>
      <canvas
        ref={canvasRef}
        className="border border-border rounded-md"
        style={{ maxWidth: '100%', maxHeight: '100%' }}
      />
      {!isReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading canvas...</p>
          </div>
        </div>
      )}\n    </div>\n  )\n}
```

#### Tool Palette Component
```typescript
// File: ToolPalette/index.tsx
interface ToolPaletteProps {
  orientation?: 'horizontal' | 'vertical'
  size?: 'sm' | 'md' | 'lg'
  showLabels?: boolean
}

export function ToolPalette({ 
  orientation = 'vertical', 
  size = 'md', 
  showLabels = false 
}: ToolPaletteProps) {
  const { activeTool, setActiveTool } = useToolStore()
  const tools = useAvailableTools()
  
  return (
    <div className={cn(
      'flex gap-1 p-2 bg-background border border-border rounded-md',
      orientation === 'horizontal' ? 'flex-row' : 'flex-col',
      size === 'sm' && 'gap-0.5 p-1',
      size === 'lg' && 'gap-2 p-3'
    )}>
      {tools.map((tool) => (
        <ToolButton
          key={tool.id}
          tool={tool}
          isActive={activeTool === tool.id}
          onClick={() => setActiveTool(tool.id)}
          size={size}
          showLabel={showLabels}
        />
      ))}\n    </div>\n  )\n}

interface ToolButtonProps {
  tool: Tool
  isActive: boolean
  onClick: () => void
  size: 'sm' | 'md' | 'lg'
  showLabel: boolean
}

function ToolButton({ tool, isActive, onClick, size, showLabel }: ToolButtonProps) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  }
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={isActive ? 'default' : 'ghost'}
            size="icon"
            className={cn(sizeClasses[size])}
            onClick={onClick}
            aria-label={tool.name}
          >
            <tool.icon className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tool.name}</p>
          {tool.shortcut && (
            <p className="text-xs text-muted-foreground">
              {tool.shortcut}
            </p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
```

#### Menu Bar Component
```typescript
// File: MenuBar/index.tsx
export function MenuBar() {
  const { canUndo, canRedo, undo, redo } = useHistoryStore()
  const { hasSelection } = useSelectionStore()
  
  return (
    <div className="flex items-center justify-between p-2 bg-background border-b border-border">
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={undo}
          disabled={!canUndo}
        >
          <Undo className="h-4 w-4 mr-1" />
          Undo
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={redo}
          disabled={!canRedo}
        >
          <Redo className="h-4 w-4 mr-1" />
          Redo
        </Button>
        <Separator orientation="vertical" className="h-6" />
        <Button
          variant="ghost"
          size="sm"
          disabled={!hasSelection}
        >
          <Copy className="h-4 w-4 mr-1" />
          Copy
        </Button>
        <Button
          variant="ghost"
          size="sm"
          disabled={!hasSelection}
        >
          <Scissors className="h-4 w-4 mr-1" />
          Cut
        </Button>
      </div>
      
      <div className="flex items-center gap-2">
        <ZoomControls />
        <Separator orientation="vertical" className="h-6" />
        <SettingsDialog />
      </div>
    </div>
  )
}
```

#### AI Chat Interface
```typescript
// File: Panels/AIChat/index.tsx
interface AIChatProps {
  className?: string
}

export function AIChat({ className }: AIChatProps) {
  const { messages, input, handleSubmit, handleInputChange, isLoading } = useChat({
    api: '/api/ai/chat',
    onResponse: (response) => {
      console.log('AI response:', response)
    }
  })
  
  return (
    <Card className={cn('flex flex-col h-full', className)}>
      <CardHeader className="flex-shrink-0 pb-2">
        <CardTitle className="text-lg">AI Assistant</CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col overflow-hidden">
        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <ChatMessage
                key={message.id}
                message={message}
                isUser={message.role === 'user'}
              />
            ))}
            {isLoading && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span className="text-sm">AI is thinking...</span>
              </div>
            )}
          </div>
        </ScrollArea>
        
        <form onSubmit={handleSubmit} className="flex gap-2 mt-4">
          <Input
            value={input}
            onChange={handleInputChange}
            placeholder="Describe what you want to do..."
            className="flex-1"
          />
          <Button type="submit" disabled={isLoading}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

interface ChatMessageProps {
  message: Message
  isUser: boolean
}

function ChatMessage({ message, isUser }: ChatMessageProps) {
  return (
    <div className={cn(
      'flex gap-3 p-3 rounded-lg',
      isUser ? 'bg-primary text-primary-foreground ml-8' : 'bg-muted mr-8'
    )}>
      <div className="flex-shrink-0">
        {isUser ? (
          <User className="h-5 w-5" />
        ) : (
          <Bot className="h-5 w-5" />
        )}
      </div>
      <div className="flex-1 prose prose-sm max-w-none">
        <ReactMarkdown>{message.content}</ReactMarkdown>
      </div>
    </div>
  )
}
```

### Panel Components (`/Users/<USER>/Projects/own/assistant/foto-fun/components/editor/Panels/`)

#### Layers Panel
```typescript
// File: LayersPanel/index.tsx
export function LayersPanel() {
  const { layers, activeLayerId, setActiveLayer, toggleLayerVisibility } = useLayerStore()
  
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Layers</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-64">
          <div className="space-y-1">
            {layers.map((layer) => (
              <LayerItem
                key={layer.id}
                layer={layer}
                isActive={activeLayerId === layer.id}
                onClick={() => setActiveLayer(layer.id)}
                onToggleVisibility={() => toggleLayerVisibility(layer.id)}
              />
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

interface LayerItemProps {
  layer: Layer
  isActive: boolean
  onClick: () => void
  onToggleVisibility: () => void
}

function LayerItem({ layer, isActive, onClick, onToggleVisibility }: LayerItemProps) {
  return (
    <div
      className={cn(
        'flex items-center gap-2 p-2 rounded-md cursor-pointer hover:bg-accent',
        isActive && 'bg-accent'
      )}
      onClick={onClick}
    >
      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6"
        onClick={(e) => {
          e.stopPropagation()
          onToggleVisibility()
        }}
      >
        {layer.visible ? (
          <Eye className="h-4 w-4" />
        ) : (
          <EyeOff className="h-4 w-4" />
        )}
      </Button>
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{layer.name}</p>
        <p className="text-xs text-muted-foreground">{layer.type}</p>
      </div>
      
      <div className="flex items-center gap-1">
        <Slider
          value={[layer.opacity]}
          onValueChange={([value]) => {
            // Update layer opacity
          }}
          max={100}
          step={1}
          className="w-16"
        />
        <span className="text-xs text-muted-foreground w-8">
          {layer.opacity}%
        </span>
      </div>
    </div>
  )
}
```

#### Character Panel
```typescript
// File: CharacterPanel/index.tsx
export function CharacterPanel() {
  const { activeObject } = useCanvasStore()
  const isTextObject = activeObject?.type === 'textbox'
  
  if (!isTextObject) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <p className="text-muted-foreground text-sm">
            Select a text object to edit character properties
          </p>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Character</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <FontSelector />
        <div className="grid grid-cols-2 gap-2">
          <FontSizeInput />
          <LineHeightControl />
        </div>
        <FontStyleButtons />
        <div className="grid grid-cols-2 gap-2">
          <LetterSpacingControl />
          <TextColorPicker />
        </div>
      </CardContent>
    </Card>
  )
}

// Font Selector Component
export function FontSelector() {
  const [fonts, setFonts] = useState<GoogleFont[]>([])
  const [selectedFont, setSelectedFont] = useState('Inter')
  const [isLoading, setIsLoading] = useState(false)
  
  useEffect(() => {
    loadGoogleFonts().then(setFonts)
  }, [])
  
  const handleFontChange = async (fontFamily: string) => {
    setIsLoading(true)
    try {
      await loadFont(fontFamily)
      setSelectedFont(fontFamily)
      // Apply to active text object
      applyFontToActiveText(fontFamily)
    } finally {
      setIsLoading(false)
    }
  }
  
  return (
    <div className="space-y-2">
      <Label>Font Family</Label>
      <Select value={selectedFont} onValueChange={handleFontChange}>
        <SelectTrigger>
          <SelectValue placeholder="Select font">
            {isLoading ? 'Loading...' : selectedFont}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {fonts.map((font) => (
            <SelectItem key={font.family} value={font.family}>
              <span style={{ fontFamily: font.family }}>
                {font.family}
              </span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
```

## Theme System

### Color Palette

```typescript
// File: app/globals.css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }
  
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
  }
}
```

### Theme Provider

```typescript
// File: app/providers.tsx
import { ThemeProvider } from 'next-themes'

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {children}
    </ThemeProvider>
  )
}
```

### Custom Theme Hook

```typescript
// File: hooks/useTheme.ts
export function useTheme() {
  const { theme, setTheme } = useNextTheme()
  
  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }
  
  const isDark = theme === 'dark'
  const isLight = theme === 'light'
  const isSystem = theme === 'system'
  
  return {
    theme,
    setTheme,
    toggleTheme,
    isDark,
    isLight,
    isSystem
  }
}
```

## Layout System

### Editor Layout

```typescript
// File: app/editor/layout.tsx
export default function EditorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="h-screen flex flex-col">
      <MenuBar />
      <div className="flex-1 flex overflow-hidden">
        <div className="w-16 flex-shrink-0">
          <ToolPalette />
        </div>
        <div className="flex-1 flex flex-col">
          <div className="flex-1 relative">
            {children}
          </div>
          <StatusBar />
        </div>
        <div className="w-80 flex-shrink-0 flex flex-col">
          <Tabs defaultValue="ai" className="h-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="ai">AI</TabsTrigger>
              <TabsTrigger value="layers">Layers</TabsTrigger>
              <TabsTrigger value="properties">Properties</TabsTrigger>
            </TabsList>
            <TabsContent value="ai" className="flex-1">
              <AIChat />
            </TabsContent>
            <TabsContent value="layers" className="flex-1">
              <LayersPanel />
            </TabsContent>
            <TabsContent value="properties" className="flex-1">
              <PropertiesPanel />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
```

### Responsive Breakpoints

```typescript
// File: tailwind.config.ts
export default {
  theme: {
    screens: {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      // Custom breakpoints for editor
      'editor-sm': '1024px',
      'editor-md': '1280px',
      'editor-lg': '1536px',
    }
  }
}
```

## Animation System

### Smooth Transitions

```typescript
// File: components/ui/animations.tsx
export const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
  transition: { duration: 0.2 }
}

export const slideIn = {
  initial: { x: -20, opacity: 0 },
  animate: { x: 0, opacity: 1 },
  exit: { x: -20, opacity: 0 },
  transition: { duration: 0.2 }
}

export const scaleIn = {
  initial: { scale: 0.95, opacity: 0 },
  animate: { scale: 1, opacity: 1 },
  exit: { scale: 0.95, opacity: 0 },
  transition: { duration: 0.2 }
}

// Usage in components
export function AnimatedPanel({ children }: { children: React.ReactNode }) {
  return (
    <motion.div {...fadeIn}>
      {children}
    </motion.div>
  )
}
```

### Loading States

```typescript
// File: components/ui/loading.tsx
export function LoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }
  
  return (
    <div className={cn(
      'animate-spin rounded-full border-2 border-primary border-t-transparent',
      sizeClasses[size]
    )} />
  )
}

export function LoadingOverlay({ message }: { message?: string }) {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center">
        <LoadingSpinner size="lg" />
        {message && (
          <p className="mt-2 text-sm text-muted-foreground">
            {message}
          </p>
        )}
      </div>
    </div>
  )
}
```

## Accessibility Features

### Focus Management

```typescript
// File: hooks/useFocusManagement.ts
export function useFocusManagement() {
  const trapFocus = (element: HTMLElement) => {
    const focusableElements = element.querySelectorAll(
      'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
    )
    
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus()
            e.preventDefault()
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus()
            e.preventDefault()
          }
        }
      }
    }
    
    element.addEventListener('keydown', handleKeyDown)
    firstElement.focus()
    
    return () => {
      element.removeEventListener('keydown', handleKeyDown)
    }
  }
  
  return { trapFocus }
}
```

### Keyboard Navigation

```typescript
// File: hooks/useKeyboardNavigation.ts
export function useKeyboardNavigation() {
  const { activeTool, setActiveTool, toolCategories } = useToolStore()
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Tool shortcuts
      if (e.key === 'v' && !e.ctrlKey && !e.metaKey) {
        setActiveTool('move')
      } else if (e.key === 'c' && !e.ctrlKey && !e.metaKey) {
        setActiveTool('crop')
      } else if (e.key === 'b' && !e.ctrlKey && !e.metaKey) {
        setActiveTool('brush')
      } else if (e.key === 't' && !e.ctrlKey && !e.metaKey) {
        setActiveTool('horizontal-type')
      }
      
      // Undo/Redo shortcuts
      if ((e.ctrlKey || e.metaKey) && e.key === 'z') {
        e.preventDefault()
        if (e.shiftKey) {
          // Redo
          useHistoryStore.getState().redo()
        } else {
          // Undo
          useHistoryStore.getState().undo()
        }
      }
    }
    
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [setActiveTool])
}
```

### Screen Reader Support

```typescript
// File: components/ui/sr-only.tsx
export function SROnly({ children }: { children: React.ReactNode }) {
  return (
    <span className="sr-only">
      {children}
    </span>
  )
}

// Usage in components
export function ToolButton({ tool, isActive, onClick }: ToolButtonProps) {
  return (
    <button
      onClick={onClick}
      aria-label={`${tool.name} tool`}
      aria-pressed={isActive}
      className={cn(
        'tool-button',
        isActive && 'active'
      )}
    >
      <tool.icon />
      <SROnly>
        {tool.name} {isActive ? '(active)' : ''}
      </SROnly>
    </button>
  )
}
```

## Performance Optimizations

### Virtualization

```typescript
// File: components/ui/virtualized-list.tsx
import { VariableSizeList as List } from 'react-window'

interface VirtualizedListProps {
  items: any[]
  itemHeight: number
  height: number
  renderItem: (props: any) => React.ReactElement
}

export function VirtualizedList({ 
  items, 
  itemHeight, 
  height, 
  renderItem 
}: VirtualizedListProps) {
  return (
    <List
      height={height}
      itemCount={items.length}
      itemSize={() => itemHeight}
      itemData={items}
    >
      {renderItem}
    </List>
  )
}
```

### Lazy Loading

```typescript
// File: components/ui/lazy-image.tsx
export function LazyImage({ src, alt, className }: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )
    
    if (imgRef.current) {
      observer.observe(imgRef.current)
    }
    
    return () => observer.disconnect()
  }, [])
  
  return (
    <div ref={imgRef} className={cn('relative', className)}>
      {isInView && (
        <img
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          className={cn(
            'transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0'
          )}
        />
      )}
      {!isLoaded && (
        <div className="absolute inset-0 bg-muted animate-pulse" />
      )}
    </div>
  )
}
```

## Testing Support

### Component Testing

```typescript
// File: components/ui/__tests__/button.test.tsx
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Button } from '../button'

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
  })
  
  it('handles click events', async () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    await userEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
  
  it('applies variant styles correctly', () => {
    const { rerender } = render(<Button variant="destructive">Delete</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-destructive')
    
    rerender(<Button variant="outline">Cancel</Button>)
    expect(screen.getByRole('button')).toHaveClass('border')
  })
})
```

### Visual Testing

```typescript
// File: components/ui/__tests__/visual.test.tsx
import { render } from '@testing-library/react'
import { Button } from '../button'

describe('Button Visual Tests', () => {
  it('matches snapshot for default variant', () => {
    const { container } = render(<Button>Default</Button>)
    expect(container.firstChild).toMatchSnapshot()
  })
  
  it('matches snapshot for all variants', () => {
    const variants = ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link']
    
    variants.forEach(variant => {
      const { container } = render(
        <Button variant={variant as any}>
          {variant}
        </Button>
      )
      expect(container.firstChild).toMatchSnapshot(`button-${variant}`)
    })
  })
})
```

## Future Enhancements

### Advanced Animations

```typescript
// Planned advanced animations
interface AnimationConfig {
  duration: number
  easing: string
  delay?: number
  repeat?: number
}

export function useSpringAnimation(config: AnimationConfig) {
  const [spring, api] = useSpring(() => ({
    opacity: 0,
    transform: 'scale(0.9)',
    config: {
      duration: config.duration,
      easing: config.easing
    }
  }))
  
  return { spring, api }
}
```

### Advanced Theming

```typescript
// Planned theme customization
interface ThemeConfig {
  colors: ColorPalette
  spacing: SpacingScale
  typography: TypographyScale
  animations: AnimationConfig
}

export function useCustomTheme(config: ThemeConfig) {
  const [theme, setTheme] = useState(config)
  
  const updateTheme = (updates: Partial<ThemeConfig>) => {
    setTheme(prev => ({ ...prev, ...updates }))
  }
  
  return { theme, updateTheme }
}
```

### Component Variants

```typescript
// Planned advanced component variants
const advancedButtonVariants = cva(
  'base-button-classes',
  {
    variants: {
      variant: {
        primary: 'primary-styles',
        secondary: 'secondary-styles',
        success: 'success-styles',
        warning: 'warning-styles',
        error: 'error-styles'
      },
      size: {
        xs: 'xs-styles',
        sm: 'sm-styles',
        md: 'md-styles',
        lg: 'lg-styles',
        xl: 'xl-styles'
      },
      shape: {
        rectangle: 'rectangle-styles',
        rounded: 'rounded-styles',
        pill: 'pill-styles',
        circle: 'circle-styles'
      }
    },
    compoundVariants: [
      {
        variant: 'primary',
        size: 'lg',
        class: 'primary-lg-styles'
      }
    ]
  }
)
```

## Design System Benefits

### 1. Consistency
- **Unified components**: Consistent behavior across the application
- **Design tokens**: Centralized design values
- **Brand compliance**: Consistent visual identity
- **Accessibility standards**: WCAG compliance built-in

### 2. Developer Experience
- **TypeScript support**: Full type safety
- **Component documentation**: Comprehensive component API
- **Hot reload**: Fast development cycles
- **Testing utilities**: Built-in testing support

### 3. Performance
- **Optimized rendering**: Efficient re-render cycles
- **Lazy loading**: Components load on demand
- **Bundle optimization**: Tree-shaking and code splitting
- **Animation performance**: 60fps animations

### 4. Maintainability
- **Modular architecture**: Easy to update and extend
- **Version control**: Tracked design system changes
- **Documentation**: Self-documenting components
- **Testing coverage**: Comprehensive test suites

This design system provides a comprehensive foundation for building FotoFun's professional photo editing interface while maintaining consistency, accessibility, and performance across all components and interactions.