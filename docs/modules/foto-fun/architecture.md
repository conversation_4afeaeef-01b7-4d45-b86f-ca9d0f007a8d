# FotoFun - System Architecture

## High-Level Architecture Overview

FotoFun implements a modern, AI-first architecture that prioritizes extensibility, performance, and user experience. The system is built around three core architectural patterns:

1. **AI-Canvas Bridge Pattern**: Every canvas tool is wrapped with an AI adapter for natural language control
2. **Command Pattern**: All operations are reversible commands for infinite undo/redo
3. **Agent-Orchestrated Workflows**: AI agents plan and execute complex editing sequences

```mermaid
graph TB
    subgraph "Client Layer"
        UI[React 19 UI]
        Canvas[Fabric.js v6 Canvas]
        Stores[Zustand Stores]
    end
    
    subgraph "AI Orchestration Layer"
        MRA[Master Routing Agent]
        SEA[Sequential Editing Agent]
        TAR[Tool Adapter Registry]
    end
    
    subgraph "Canvas Management Layer"
        CM[Canvas Manager]
        SM[Selection Manager]
        TM[Tool Manager]
        HM[History Manager]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI GPT-4]
        Replicate[Replicate AI Models]
        Supabase[Supabase Database]
    end
    
    UI <--> Stores
    UI <--> Canvas
    Stores <--> MRA
    MRA <--> SEA
    MRA <--> TAR
    TAR <--> TM
    Canvas <--> CM
    CM <--> SM
    CM <--> HM
    MRA <--> OpenAI
    MRA <--> Replicate
    Stores <--> Supabase
```

## Core Architectural Patterns

### 1. AI-Canvas Bridge Pattern

Every canvas tool is wrapped with an AI adapter that translates natural language into tool parameters:

```typescript
// File: /Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/adapters/base.ts
abstract class BaseToolAdapter<TInput, TOutput> {
  abstract tool: Tool
  abstract aiName: string
  abstract description: string
  abstract metadata: ToolMetadata
  abstract inputSchema: z.ZodType<TInput>
  abstract execute(params: TInput, context: CanvasContext): Promise<TOutput>
  
  toAITool(): unknown {
    return tool({
      description: this.description,
      inputSchema: this.inputSchema,
      execute: async (args: unknown) => {
        return {
          success: true,
          message: `Tool ${this.aiName} will be executed on the client`,
          clientExecutionRequired: true,
          params: args
        }
      }
    })
  }
}
```

### 2. Agent Orchestration System

**Master Routing Agent** (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/agents/MasterRoutingAgent.ts`):
- Analyzes user requests using GPT-4
- Routes to appropriate execution strategy
- Handles confidence scoring and auto-approval

**Sequential Editing Agent** (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/agents/SequentialEditingAgent.ts`):
- Plans multi-step workflows
- Executes canvas tools in sequence
- Provides step-by-step status updates

```mermaid
sequenceDiagram
    participant U as User
    participant MRA as Master Routing Agent
    participant SEA as Sequential Editing Agent
    participant TAR as Tool Adapter Registry
    participant C as Canvas
    
    U->>MRA: "Make this image vintage"
    MRA->>MRA: Analyze request with GPT-4
    MRA->>SEA: Route to sequential workflow
    SEA->>SEA: Plan editing steps
    SEA->>TAR: Get saturation tool
    SEA->>TAR: Get sepia tool
    SEA->>C: Execute tools
    C->>U: Show result
```

### 3. Command Pattern for History Management

All operations are implemented as reversible commands:

```typescript
// File: /Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/commands/base/Command.ts
abstract class Command {
  abstract execute(): void | Promise<void>
  abstract undo(): void | Promise<void>
  abstract canUndo(): boolean
  abstract getDescription(): string
}
```

Command categories:
- **Canvas Commands**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/commands/canvas/`
- **Layer Commands**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/commands/layer/`
- **Selection Commands**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/commands/selection/`
- **Filter Commands**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/commands/filters/`

## Layer Architecture

### 1. Presentation Layer (React 19 + Next.js 15)

**Main Editor**: `/Users/<USER>/Projects/own/assistant/foto-fun/app/editor/page.tsx`
- Canvas component integration
- Tool palette and options
- AI chat interface
- Status bars and panels

**Component Structure**:
```
components/editor/
├── Canvas/              # Main canvas component
├── MenuBar/            # Top menu bar
├── ToolPalette/        # Tool selection
├── ToolOptions/        # Tool-specific options
├── Panels/             # Side panels
│   ├── AIChat/         # AI assistant chat
│   ├── LayersPanel/    # Layer management
│   ├── CharacterPanel/ # Typography controls
│   └── TextEffectsPanel/ # Text effects
└── StatusBar/          # Bottom status bar
```

### 2. State Management Layer (Zustand)

**Store Architecture**: `/Users/<USER>/Projects/own/assistant/foto-fun/store/`

```typescript
// Core stores with specific responsibilities
- canvasStore.ts        # Canvas state and operations
- toolStore.ts          # Active tool and tool state
- toolOptionsStore.ts   # Tool-specific options
- layerStore.ts         # Layer management
- selectionStore.ts     # Selection state
- historyStore.ts       # Command history
- documentStore.ts      # Document properties
- colorStore.ts         # Color management
- filterStore.ts        # Filter states
- performanceStore.ts   # Performance monitoring
```

### 3. Business Logic Layer

**AI Orchestration**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/`
- Agent system for workflow planning
- Tool adapter registry
- Client/server AI tool execution
- Memory system for context

**Canvas Management**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/`
- Canvas manager with Fabric.js integration
- Selection system with layer awareness
- Tool system with 32+ professional tools
- Filter pipeline for image processing

### 4. Data Layer (Supabase)

**Database Schema**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/db/schema.ts`
- User authentication and profiles
- Document storage and versioning
- AI conversation history
- Usage analytics

## Tool System Architecture

### Tool Categories

1. **Selection Tools** (6 tools): `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/selection/`
   - Marquee Rectangle/Ellipse
   - Lasso and Magic Wand
   - Quick Selection
   - Move tool

2. **Transform Tools** (6 tools): `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/transform/`
   - Crop, Rotate, Flip, Resize
   - Hand and Zoom tools

3. **Drawing Tools** (2 tools): `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/drawing/`
   - Brush tool
   - Eraser tool

4. **Text Tools** (4 tools): `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/text/`
   - Horizontal/Vertical Type
   - Type Mask and Type on Path

5. **Adjustment Tools** (6 tools): `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/adjustments/`
   - Brightness, Contrast, Saturation
   - Hue, Exposure, Color Temperature

6. **Filter Tools** (5 tools): `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/filters/`
   - Blur, Sharpen, Grayscale
   - Sepia, Invert

7. **Utility Tools** (1 tool):
   - Eyedropper tool

8. **AI-Native Tools** (2 tools): `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/ai-native/`
   - Image Generation
   - Image Transformation

### Tool Adapter Pattern

Each tool has an AI adapter that bridges natural language to tool parameters:

```typescript
// Example: Brightness Tool Adapter
class BrightnessAdapter extends FilterToolAdapter<
  { adjustment: number },
  { success: boolean; message: string }
> {
  tool = brightnessTool
  aiName = 'adjustBrightness'
  description = 'Adjust image brightness. Calculate adjustment value based on user intent...'
  metadata = {
    category: 'canvas-editing' as const,
    executionType: 'fast' as const,
    worksOn: 'existing-image' as const
  }
  
  inputSchema = z.object({
    adjustment: z.number()
      .min(-100).max(100)
      .describe('Brightness adjustment (-100 to +100)')
  })
}
```

## AI Integration Architecture

### 1. Natural Language Processing

**Request Analysis Flow**:
1. User types natural language request
2. Master Routing Agent analyzes with GPT-4
3. Determines execution strategy (text/simple-tool/workflow)
4. Routes to appropriate handler

**Tool Confidence Scoring**:
```typescript
interface ToolConfidence {
  overall: number
  factors: {
    parameterAppropriate: number
    canvasContext: number
    toolSuitability: number
    riskLevel: number
  }
}
```

### 2. Execution Strategies

**Text-Only Responses**: Help, explanations, guidance
**Simple Tool Execution**: Single tool operations with auto-approval
**Sequential Workflows**: Multi-step canvas operations
**Evaluator-Optimizer**: Quality-focused workflows (future)
**Orchestrator-Worker**: Parallel processing (future)

### 3. External AI Services

**Replicate Integration**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/server/replicateClient.ts`
- Image generation (Stable Diffusion XL)
- Background removal (Bria)
- Image upscaling (Google)
- Inpainting (object removal)

**OpenAI Integration**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/providers.ts`
- GPT-4 for request analysis
- Tool parameter inference
- Workflow planning
- Confidence scoring

## Canvas System Architecture

### Fabric.js v6 Integration

**Canvas Manager**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/canvas/CanvasManager.ts`
- Canvas initialization and lifecycle
- Object management and rendering
- Event handling and coordination

**Selection System**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/selection/`
```typescript
// Layer-aware selection with pixel-perfect accuracy
class LayerAwareSelectionManager {
  canvas: Canvas
  pixelCache: ObjectPixelCache
  renderer: SelectionRenderer
  
  // Advanced selection algorithms
  createRectangleSelection(bounds: Rectangle): Selection
  createLassoSelection(points: Point[]): Selection
  magicWandSelect(point: Point, tolerance: number): Selection
}
```

### Performance Optimizations

**Object Registry**: `/Users/<USER>/Projects/own/assistant/foto-fun/store/objectRegistryStore.ts`
- Pixel map caching for selection operations
- Dirty object tracking for incremental updates
- Debounced re-computation to prevent blocking

**Memory Management**:
- Canvas disposal cleanup
- Filter garbage collection
- Selection overlay management

## API Routes Architecture

**Next.js API Routes**: `/Users/<USER>/Projects/own/assistant/foto-fun/app/api/`

```
api/
├── ai/
│   ├── chat/           # AI chat assistant
│   ├── debug/          # AI debugging tools
│   └── replicate/      # Replicate AI models
│       ├── generate-image/
│       ├── inpaint/
│       ├── remove-background/
│       └── upscale-image/
├── auth/
│   └── callback/       # Supabase auth callback
├── debug-saturation/   # Debug endpoints
└── test-exposure/      # Test endpoints
```

### AI API Workflow

1. **Request Validation**: Zod schema validation
2. **Authentication**: Supabase JWT verification
3. **Rate Limiting**: API usage tracking
4. **AI Processing**: Replicate/OpenAI integration
5. **Response Formatting**: Standardized JSON responses

## Security Architecture

### Authentication & Authorization

**Supabase Integration**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/db/supabase/`
- JWT-based authentication
- Row-level security policies
- Real-time subscriptions
- OAuth provider integration

**Middleware**: `/Users/<USER>/Projects/own/assistant/foto-fun/middleware.ts`
- Route protection
- Session management
- CSRF protection

### Data Protection

**Client-Side Security**:
- Input validation with Zod schemas
- XSS prevention in canvas rendering
- Safe HTML sanitization

**Server-Side Security**:
- API key validation
- Request size limits
- Rate limiting by user/IP

## Deployment Architecture

### Build Process

**Next.js 15 App Router**:
- Static generation for marketing pages
- Server-side rendering for editor
- API routes for backend functionality

**Bun Runtime**:
- Fast package management
- Optimized TypeScript compilation
- Development server with hot reload

### Environment Configuration

**Required Environment Variables**:
```bash
# AI Services
REPLICATE_API_TOKEN=        # Replicate AI models
OPENAI_API_KEY=            # OpenAI GPT-4

# Database
SUPABASE_URL=              # Supabase project URL
SUPABASE_ANON_KEY=         # Supabase anonymous key

# Optional
NEXT_PUBLIC_SUPABASE_URL=  # Client-side Supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=
```

### Production Considerations

**Performance**:
- WebGL optimization for canvas operations
- Image compression and caching
- CDN integration for static assets

**Scalability**:
- Horizontal scaling with load balancers
- Database connection pooling
- AI API request queuing

**Monitoring**:
- Performance tracking in store
- Error reporting and analytics
- AI usage monitoring

## Future Architecture Considerations

### WebGL Migration Path

Current Fabric.js implementation may need optimization for:
- Large image processing
- Complex filter operations
- Real-time collaboration

**Potential Migration to Konva.js**:
- Better WebGL performance
- Lower memory usage
- More efficient rendering pipeline

### Plugin Architecture

**Extension Points**:
- Custom tool adapters
- AI model integrations
- UI component extensions
- Export format plugins

**Plugin API Design**:
```typescript
interface PluginAPI {
  registerTool(adapter: ToolAdapter): void
  registerFilter(filter: FilterPlugin): void
  registerAIModel(model: AIModelPlugin): void
  registerExporter(exporter: ExportPlugin): void
}
```

### Real-Time Collaboration

**Planned Architecture**:
- Supabase real-time for state synchronization
- Operational transforms for conflict resolution
- Cursor and selection sharing
- Live commenting and annotations

This architecture provides a solid foundation for scaling FotoFun into a comprehensive, AI-powered photo editing platform while maintaining performance, extensibility, and user experience quality.