# FotoFun - Performance Architecture

## Overview

FotoFun's performance architecture is designed to handle **intensive graphics operations**, **real-time AI processing**, and **large-scale image manipulation** while maintaining smooth user experience. The system employs multiple optimization strategies across the entire application stack.

## Performance Metrics & Targets

### Key Performance Indicators

```typescript
// Performance targets and monitoring
interface PerformanceTargets {
  // Core Web Vitals
  largestContentfulPaint: number    // < 2.5s
  firstInputDelay: number           // < 100ms
  cumulativeLayoutShift: number     // < 0.1
  
  // Canvas Performance
  canvasRenderTime: number          // < 16ms (60fps)
  toolResponseTime: number          // < 50ms
  selectionTime: number             // < 100ms
  
  // AI Performance
  aiResponseTime: number            // < 3000ms
  imageProcessingTime: number       // < 5000ms
  
  // Memory Usage
  canvasMemoryLimit: number         // < 512MB
  totalMemoryLimit: number          // < 1GB
  
  // Network Performance
  bundleSize: number                // < 2MB initial
  imageLoadTime: number             // < 1000ms
  apiLatency: number                // < 500ms
}

const performanceTargets: PerformanceTargets = {
  largestContentfulPaint: 2500,
  firstInputDelay: 100,
  cumulativeLayoutShift: 0.1,
  canvasRenderTime: 16,
  toolResponseTime: 50,
  selectionTime: 100,
  aiResponseTime: 3000,
  imageProcessingTime: 5000,
  canvasMemoryLimit: 512 * 1024 * 1024,
  totalMemoryLimit: 1024 * 1024 * 1024,
  bundleSize: 2 * 1024 * 1024,
  imageLoadTime: 1000,
  apiLatency: 500
}
```

## Canvas Performance Optimization

### Fabric.js Performance Configuration

```typescript
// File: lib/canvas/performance.ts
interface CanvasPerformanceConfig {
  // Rendering optimizations
  renderOnAddRemove: boolean
  skipTargetFind: boolean
  stateful: boolean
  
  // Selection optimizations
  selectionBorder: boolean
  selectionLineWidth: number
  selectionDashArray: number[]
  
  // Performance settings
  enableRetinaScaling: boolean
  devicePixelRatio: number
  imageSmoothingEnabled: boolean
  
  // Memory management
  objectCaching: boolean
  statefullCache: boolean
  cacheProperties: string[]
}

export const createOptimizedCanvas = (
  element: HTMLCanvasElement,
  config: CanvasPerformanceConfig
): fabric.Canvas => {
  const canvas = new fabric.Canvas(element, {
    // Basic configuration
    width: config.width,
    height: config.height,
    
    // Performance optimizations
    renderOnAddRemove: config.renderOnAddRemove,
    skipTargetFind: config.skipTargetFind,
    stateful: config.stateful,
    
    // Selection styling
    selectionBorder: config.selectionBorder,
    selectionLineWidth: config.selectionLineWidth,
    selectionDashArray: config.selectionDashArray,
    
    // Rendering quality
    enableRetinaScaling: config.enableRetinaScaling,
    devicePixelRatio: config.devicePixelRatio,
    imageSmoothingEnabled: config.imageSmoothingEnabled,
    
    // Object caching
    objectCaching: config.objectCaching,
    statefullCache: config.statefullCache
  })
  
  // Configure global fabric settings
  fabric.Object.prototype.objectCaching = config.objectCaching
  fabric.Object.prototype.statefullCache = config.statefullCache
  fabric.Object.prototype.cacheProperties = config.cacheProperties
  
  return canvas
}
```

### Render Performance Optimization

```typescript
// File: lib/canvas/rendering.ts
class RenderOptimizer {
  private canvas: fabric.Canvas
  private renderQueue: RenderRequest[] = []
  private isRendering = false
  private lastRenderTime = 0
  private targetFPS = 60
  private frameTime = 1000 / this.targetFPS
  
  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas
    this.setupPerformanceMonitoring()
  }
  
  // Throttled rendering to maintain 60fps
  public requestRender(priority: 'high' | 'medium' | 'low' = 'medium'): void {
    const request: RenderRequest = {
      timestamp: performance.now(),
      priority,
      id: generateId()
    }
    
    this.renderQueue.push(request)
    this.processRenderQueue()
  }
  
  private processRenderQueue(): void {
    if (this.isRendering) return
    
    const now = performance.now()
    const timeSinceLastRender = now - this.lastRenderTime
    
    if (timeSinceLastRender < this.frameTime) {
      // Schedule next frame
      requestAnimationFrame(() => this.processRenderQueue())
      return
    }
    
    this.performRender()
  }
  
  private performRender(): void {
    this.isRendering = true
    const startTime = performance.now()
    
    try {
      // Sort queue by priority
      this.renderQueue.sort((a, b) => {
        const priorityOrder = { high: 0, medium: 1, low: 2 }
        return priorityOrder[a.priority] - priorityOrder[b.priority]
      })
      
      // Process high priority renders first
      const highPriorityRenders = this.renderQueue.filter(r => r.priority === 'high')
      if (highPriorityRenders.length > 0) {
        this.canvas.renderAll()
        this.renderQueue = this.renderQueue.filter(r => r.priority !== 'high')
      } else if (this.renderQueue.length > 0) {
        this.canvas.renderAll()
        this.renderQueue = []
      }
      
      this.lastRenderTime = performance.now()
      
      // Track performance
      const renderTime = this.lastRenderTime - startTime
      this.trackRenderPerformance(renderTime)
      
    } finally {
      this.isRendering = false
    }
  }
  
  private trackRenderPerformance(renderTime: number): void {
    const fps = Math.round(1000 / renderTime)
    
    // Send to performance monitoring
    if (typeof window !== 'undefined' && window.performance) {
      window.performance.mark('canvas-render-end')
      window.performance.measure('canvas-render', 'canvas-render-start', 'canvas-render-end')
    }
    
    // Track in performance store
    const performanceStore = usePerformanceStore.getState()
    performanceStore.trackRender(renderTime)
    performanceStore.updateFPS(fps)
    
    // Log performance warnings
    if (renderTime > 16) {
      console.warn(`Slow canvas render: ${renderTime.toFixed(2)}ms (${fps}fps)`)
    }
  }
}
```

### Object Caching Strategy

```typescript
// File: lib/canvas/caching.ts
class ObjectCacheManager {
  private cache = new Map<string, fabric.Object>()
  private maxCacheSize = 100
  private cacheHits = 0
  private cacheMisses = 0
  
  // Cache fabric objects for reuse
  public cacheObject(key: string, object: fabric.Object): void {
    // Check cache size limit
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    // Clone object to avoid mutations
    const clonedObject = fabric.util.object.clone(object)
    this.cache.set(key, clonedObject)
  }
  
  public getCachedObject(key: string): fabric.Object | null {
    const cached = this.cache.get(key)
    
    if (cached) {
      this.cacheHits++
      return fabric.util.object.clone(cached)
    }
    
    this.cacheMisses++
    return null
  }
  
  public getCacheStats(): CacheStats {
    return {
      size: this.cache.size,
      hits: this.cacheHits,
      misses: this.cacheMisses,
      hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses)
    }
  }
  
  public clearCache(): void {
    this.cache.clear()
    this.cacheHits = 0
    this.cacheMisses = 0
  }
}

// Object-level caching configuration
const optimizeObjectCaching = (object: fabric.Object): void => {
  // Enable object caching
  object.objectCaching = true
  object.statefullCache = true
  
  // Configure cache properties
  object.cacheProperties = [
    'fill',
    'stroke',
    'strokeWidth',
    'opacity',
    'shadow',
    'filters'
  ]
  
  // Set cache canvas size
  object.cacheCanvasSize = 512 // Limit cache canvas size
  
  // Configure dirty flags
  object.dirty = true
  object.needsItsOwnCache = () => true
}
```

## Memory Management

### Memory Monitoring

```typescript
// File: lib/performance/memory.ts
class MemoryMonitor {
  private observers: MemoryObserver[] = []
  private memoryThreshold = 800 * 1024 * 1024 // 800MB
  private canvasMemoryThreshold = 400 * 1024 * 1024 // 400MB
  
  public startMonitoring(): void {
    // Monitor overall memory usage
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = (performance as any).memory
        this.checkMemoryUsage(memInfo)
      }, 5000)
    }
    
    // Monitor canvas memory
    this.monitorCanvasMemory()
  }
  
  private checkMemoryUsage(memInfo: any): void {
    const usedMemory = memInfo.usedJSHeapSize
    const totalMemory = memInfo.totalJSHeapSize
    const memoryLimit = memInfo.jsHeapSizeLimit
    
    // Check if approaching memory limit
    if (usedMemory > this.memoryThreshold) {
      console.warn(`High memory usage: ${(usedMemory / 1024 / 1024).toFixed(2)}MB`)
      this.triggerMemoryCleanup()
    }
    
    // Notify observers
    this.observers.forEach(observer => {
      observer.onMemoryUpdate({
        used: usedMemory,
        total: totalMemory,
        limit: memoryLimit,
        percentage: (usedMemory / memoryLimit) * 100
      })
    })
  }
  
  private monitorCanvasMemory(): void {
    const canvasStore = useCanvasStore.getState()
    const canvas = canvasStore.fabricCanvas
    
    if (!canvas) return
    
    // Estimate canvas memory usage
    const objects = canvas.getObjects()
    let estimatedMemory = 0
    
    objects.forEach(obj => {
      // Estimate object memory based on type and properties
      if (obj.type === 'image') {
        const img = obj as fabric.Image
        estimatedMemory += (img.width || 0) * (img.height || 0) * 4 // RGBA
      } else if (obj.type === 'path') {
        const path = obj as fabric.Path
        estimatedMemory += (path.path?.length || 0) * 32 // Rough estimate
      }
    })
    
    if (estimatedMemory > this.canvasMemoryThreshold) {
      console.warn(`High canvas memory usage: ${(estimatedMemory / 1024 / 1024).toFixed(2)}MB`)
      this.triggerCanvasMemoryCleanup()
    }
  }
  
  private triggerMemoryCleanup(): void {
    // Clear caches
    this.clearImageCaches()
    this.clearObjectCaches()
    
    // Run garbage collection if available
    if ('gc' in window) {
      (window as any).gc()
    }
    
    // Trigger React cleanup
    if ('React' in window) {
      // Force component cleanup
      this.triggerComponentCleanup()
    }
  }
  
  private clearImageCaches(): void {
    // Clear fabric image cache
    fabric.Image.prototype.clearCaches?.()
    
    // Clear browser image cache for unused images
    const unusedImages = document.querySelectorAll('img[data-cached="true"]')
    unusedImages.forEach(img => {
      if (!img.offsetParent) {
        img.remove()
      }
    })
  }
  
  private clearObjectCaches(): void {
    const canvasStore = useCanvasStore.getState()
    const canvas = canvasStore.fabricCanvas
    
    if (!canvas) return
    
    // Clear object caches
    canvas.getObjects().forEach(obj => {
      if (obj.clearCache) {
        obj.clearCache()
      }
    })
  }
}
```

### Automatic Memory Cleanup

```typescript
// File: lib/performance/cleanup.ts
class AutomaticCleanup {
  private cleanupInterval: number = 60000 // 1 minute
  private lastCleanup: number = 0
  
  public startAutomaticCleanup(): void {
    setInterval(() => {
      this.performCleanup()
    }, this.cleanupInterval)
  }
  
  private performCleanup(): void {
    const now = Date.now()
    if (now - this.lastCleanup < this.cleanupInterval) return
    
    console.log('Performing automatic cleanup...')
    
    // Clean up unused objects
    this.cleanupUnusedObjects()
    
    // Clean up event listeners
    this.cleanupEventListeners()
    
    // Clean up timers
    this.cleanupTimers()
    
    // Clean up component refs
    this.cleanupComponentRefs()
    
    this.lastCleanup = now
    console.log('Automatic cleanup completed')
  }
  
  private cleanupUnusedObjects(): void {
    // Clean up Fabric.js objects that are no longer referenced
    const canvasStore = useCanvasStore.getState()
    const canvas = canvasStore.fabricCanvas
    
    if (!canvas) return
    
    const objects = canvas.getObjects()
    objects.forEach(obj => {
      // Remove objects that are marked for deletion
      if ((obj as any).markedForDeletion) {
        canvas.remove(obj)
      }
      
      // Clear caches for inactive objects
      if (obj !== canvas.getActiveObject()) {
        obj.clearCache?.()
      }
    })
  }
  
  private cleanupEventListeners(): void {
    // Remove orphaned event listeners
    const elements = document.querySelectorAll('[data-cleanup-listeners]')
    elements.forEach(element => {
      if (!element.offsetParent) {
        // Element is no longer in DOM, remove its listeners
        element.removeEventListener('click', null as any)
        element.removeEventListener('mousedown', null as any)
        element.removeEventListener('mousemove', null as any)
        element.removeEventListener('mouseup', null as any)
      }
    })
  }
  
  private cleanupTimers(): void {
    // Clean up abandoned timers and intervals
    const store = usePerformanceStore.getState()
    const activeTimers = store.getActiveTimers()
    
    activeTimers.forEach(timer => {
      if (timer.isAbandoned()) {
        clearTimeout(timer.id)
        store.removeTimer(timer.id)
      }
    })
  }
}
```

## Image Processing Optimization

### Efficient Image Loading

```typescript
// File: lib/images/optimization.ts
class ImageOptimizer {
  private loadedImages = new Map<string, HTMLImageElement>()
  private loadingPromises = new Map<string, Promise<HTMLImageElement>>()
  
  public async loadOptimizedImage(
    url: string,
    options: ImageLoadOptions = {}
  ): Promise<HTMLImageElement> {
    const cacheKey = this.getCacheKey(url, options)
    
    // Return cached image if available
    if (this.loadedImages.has(cacheKey)) {
      return this.loadedImages.get(cacheKey)!
    }
    
    // Return existing loading promise if in progress
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)!
    }
    
    // Start loading new image
    const loadPromise = this.loadImage(url, options)
    this.loadingPromises.set(cacheKey, loadPromise)
    
    try {
      const image = await loadPromise
      this.loadedImages.set(cacheKey, image)
      return image
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }
  
  private async loadImage(
    url: string,
    options: ImageLoadOptions
  ): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      
      // Configure loading options
      if (options.crossOrigin) {
        img.crossOrigin = options.crossOrigin
      }
      
      // Performance optimizations
      img.decoding = 'async'
      img.loading = 'lazy'
      
      // Handle load completion
      img.onload = () => {
        // Resize if needed
        if (options.maxWidth || options.maxHeight) {
          const resized = this.resizeImage(img, options)
          resolve(resized)
        } else {
          resolve(img)
        }
      }
      
      img.onerror = () => {
        reject(new Error(`Failed to load image: ${url}`))
      }
      
      // Start loading
      img.src = url
    })
  }
  
  private resizeImage(
    img: HTMLImageElement,
    options: ImageLoadOptions
  ): HTMLImageElement {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    // Calculate optimal dimensions
    const { width, height } = this.calculateOptimalSize(
      img.width,
      img.height,
      options.maxWidth,
      options.maxHeight
    )
    
    canvas.width = width
    canvas.height = height
    
    // Use high-quality scaling
    ctx.imageSmoothingEnabled = true
    ctx.imageSmoothingQuality = 'high'
    
    // Draw resized image
    ctx.drawImage(img, 0, 0, width, height)
    
    // Convert back to image
    const resizedImg = new Image()
    resizedImg.src = canvas.toDataURL('image/jpeg', 0.9)
    
    return resizedImg
  }
  
  private calculateOptimalSize(
    originalWidth: number,
    originalHeight: number,
    maxWidth?: number,
    maxHeight?: number
  ): { width: number; height: number } {
    if (!maxWidth && !maxHeight) {
      return { width: originalWidth, height: originalHeight }
    }
    
    const aspectRatio = originalWidth / originalHeight
    
    if (maxWidth && maxHeight) {
      const widthRatio = maxWidth / originalWidth
      const heightRatio = maxHeight / originalHeight
      const ratio = Math.min(widthRatio, heightRatio)
      
      return {
        width: Math.round(originalWidth * ratio),
        height: Math.round(originalHeight * ratio)
      }
    }
    
    if (maxWidth) {
      return {
        width: Math.min(maxWidth, originalWidth),
        height: Math.round(Math.min(maxWidth, originalWidth) / aspectRatio)
      }
    }
    
    if (maxHeight) {
      return {
        width: Math.round(Math.min(maxHeight!, originalHeight) * aspectRatio),
        height: Math.min(maxHeight!, originalHeight)
      }
    }
    
    return { width: originalWidth, height: originalHeight }
  }
}
```

### WebGL Acceleration

```typescript
// File: lib/canvas/webgl.ts
class WebGLRenderer {
  private gl: WebGLRenderingContext
  private programs: Map<string, WebGLProgram> = new Map()
  private buffers: Map<string, WebGLBuffer> = new Map()
  
  constructor(canvas: HTMLCanvasElement) {
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
    if (!gl) {
      throw new Error('WebGL not supported')
    }
    this.gl = gl
  }
  
  // Accelerated image filtering
  public applyFilter(
    imageData: ImageData,
    filterType: FilterType,
    params: FilterParams
  ): ImageData {
    const program = this.getOrCreateProgram(filterType)
    
    // Create texture from image data
    const texture = this.createTexture(imageData)
    
    // Set up render target
    const framebuffer = this.createFramebuffer(imageData.width, imageData.height)
    
    // Apply filter
    this.gl.useProgram(program)
    this.setFilterUniforms(program, params)
    this.renderToTexture(texture, framebuffer)
    
    // Read back result
    const result = this.readPixels(imageData.width, imageData.height)
    
    // Cleanup
    this.cleanup([texture, framebuffer])
    
    return result
  }
  
  private getOrCreateProgram(filterType: FilterType): WebGLProgram {
    if (this.programs.has(filterType)) {
      return this.programs.get(filterType)!
    }
    
    const program = this.createProgram(filterType)
    this.programs.set(filterType, program)
    return program
  }
  
  private createProgram(filterType: FilterType): WebGLProgram {
    const vertexShader = this.createShader(this.gl.VERTEX_SHADER, `
      attribute vec2 a_position;
      attribute vec2 a_texCoord;
      varying vec2 v_texCoord;
      
      void main() {
        gl_Position = vec4(a_position, 0, 1);
        v_texCoord = a_texCoord;
      }
    `)
    
    const fragmentShader = this.createShader(
      this.gl.FRAGMENT_SHADER,
      this.getFilterShader(filterType)
    )
    
    const program = this.gl.createProgram()!
    this.gl.attachShader(program, vertexShader)
    this.gl.attachShader(program, fragmentShader)
    this.gl.linkProgram(program)
    
    if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
      throw new Error('Program linking failed')
    }
    
    return program
  }
  
  private getFilterShader(filterType: FilterType): string {
    switch (filterType) {
      case 'blur':
        return `
          precision mediump float;
          uniform sampler2D u_texture;
          uniform float u_blurAmount;
          varying vec2 v_texCoord;
          
          void main() {
            vec4 color = vec4(0.0);
            float total = 0.0;
            
            for (float x = -4.0; x <= 4.0; x++) {
              for (float y = -4.0; y <= 4.0; y++) {
                vec2 offset = vec2(x, y) * u_blurAmount;
                color += texture2D(u_texture, v_texCoord + offset);
                total += 1.0;
              }
            }
            
            gl_FragColor = color / total;
          }
        `
      
      case 'sharpen':
        return `
          precision mediump float;
          uniform sampler2D u_texture;
          uniform float u_strength;
          varying vec2 v_texCoord;
          
          void main() {
            vec4 center = texture2D(u_texture, v_texCoord);
            vec4 blur = vec4(0.0);
            
            // Sample surrounding pixels
            blur += texture2D(u_texture, v_texCoord + vec2(-1.0, -1.0) * 0.001);
            blur += texture2D(u_texture, v_texCoord + vec2( 0.0, -1.0) * 0.001);
            blur += texture2D(u_texture, v_texCoord + vec2( 1.0, -1.0) * 0.001);
            blur += texture2D(u_texture, v_texCoord + vec2(-1.0,  0.0) * 0.001);
            blur += texture2D(u_texture, v_texCoord + vec2( 1.0,  0.0) * 0.001);
            blur += texture2D(u_texture, v_texCoord + vec2(-1.0,  1.0) * 0.001);
            blur += texture2D(u_texture, v_texCoord + vec2( 0.0,  1.0) * 0.001);
            blur += texture2D(u_texture, v_texCoord + vec2( 1.0,  1.0) * 0.001);
            
            blur /= 8.0;
            
            vec4 sharpen = center + (center - blur) * u_strength;
            gl_FragColor = sharpen;
          }
        `
      
      default:
        return `
          precision mediump float;
          uniform sampler2D u_texture;
          varying vec2 v_texCoord;
          
          void main() {
            gl_FragColor = texture2D(u_texture, v_texCoord);
          }
        `
    }
  }
}
```

## AI Performance Optimization

### Request Batching

```typescript
// File: lib/ai/batching.ts
class AIRequestBatcher {
  private batchQueue: AIRequest[] = []
  private batchSize = 5
  private batchTimeout = 1000 // 1 second
  private batchTimer: NodeJS.Timeout | null = null
  
  public async addRequest(request: AIRequest): Promise<AIResponse> {
    return new Promise((resolve, reject) => {
      const batchedRequest: BatchedAIRequest = {
        ...request,
        resolve,
        reject,
        timestamp: Date.now()
      }
      
      this.batchQueue.push(batchedRequest)
      
      // Process batch if full
      if (this.batchQueue.length >= this.batchSize) {
        this.processBatch()
      } else {
        // Set timeout for partial batch
        this.setBatchTimeout()
      }
    })
  }
  
  private setBatchTimeout(): void {
    if (this.batchTimer) return
    
    this.batchTimer = setTimeout(() => {
      this.processBatch()
    }, this.batchTimeout)
  }
  
  private async processBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return
    
    // Clear timeout
    if (this.batchTimer) {
      clearTimeout(this.batchTimer)
      this.batchTimer = null
    }
    
    // Extract current batch
    const batch = this.batchQueue.splice(0, this.batchSize)
    
    try {
      // Process batch with OpenAI
      const responses = await this.processBatchWithOpenAI(batch)
      
      // Resolve individual requests
      batch.forEach((request, index) => {
        request.resolve(responses[index])
      })
    } catch (error) {
      // Reject all requests in batch
      batch.forEach(request => {
        request.reject(error)
      })
    }
  }
  
  private async processBatchWithOpenAI(batch: BatchedAIRequest[]): Promise<AIResponse[]> {
    const batchPayload = {
      model: 'gpt-4o',
      messages: batch.map(req => ({
        role: 'user',
        content: req.prompt
      })),
      temperature: 0.7,
      max_tokens: 1000
    }
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(batchPayload)
    })
    
    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`)
    }
    
    const data = await response.json()
    
    return data.choices.map((choice: any) => ({
      text: choice.message.content,
      usage: data.usage
    }))
  }
}
```

### Response Caching

```typescript
// File: lib/ai/caching.ts
class AIResponseCache {
  private cache = new Map<string, CachedResponse>()
  private maxSize = 1000
  private ttl = 3600000 // 1 hour
  
  public async getCachedResponse(request: AIRequest): Promise<AIResponse | null> {
    const key = this.generateCacheKey(request)
    const cached = this.cache.get(key)
    
    if (!cached) return null
    
    // Check if expired
    if (Date.now() - cached.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }
    
    // Update access time
    cached.lastAccess = Date.now()
    
    return cached.response
  }
  
  public setCachedResponse(request: AIRequest, response: AIResponse): void {
    const key = this.generateCacheKey(request)
    
    // Check cache size limit
    if (this.cache.size >= this.maxSize) {
      this.evictOldest()
    }
    
    this.cache.set(key, {
      response,
      timestamp: Date.now(),
      lastAccess: Date.now()
    })
  }
  
  private generateCacheKey(request: AIRequest): string {
    // Create deterministic key from request
    const keyData = {
      prompt: request.prompt,
      model: request.model,
      temperature: request.temperature,
      maxTokens: request.maxTokens
    }
    
    return btoa(JSON.stringify(keyData))
  }
  
  private evictOldest(): void {
    let oldestKey = ''
    let oldestTime = Date.now()
    
    for (const [key, cached] of this.cache) {
      if (cached.lastAccess < oldestTime) {
        oldestTime = cached.lastAccess
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }
}
```

## Bundle Optimization

### Code Splitting Strategy

```typescript
// File: app/components/LazyComponents.tsx
import dynamic from 'next/dynamic'
import { Suspense } from 'react'

// Lazy load heavy components
export const LazyCanvasEditor = dynamic(
  () => import('./CanvasEditor'),
  {
    loading: () => <div>Loading canvas editor...</div>,
    ssr: false // Canvas doesn't work on server
  }
)

export const LazyAIChat = dynamic(
  () => import('./AIChat'),
  {
    loading: () => <div>Loading AI chat...</div>
  }
)

export const LazyToolPanel = dynamic(
  () => import('./ToolPanel'),
  {
    loading: () => <div>Loading tools...</div>
  }
)

// Lazy load fabric.js
export const useFabricCanvas = () => {
  const [fabric, setFabric] = useState(null)
  
  useEffect(() => {
    const loadFabric = async () => {
      const fabricModule = await import('fabric')
      setFabric(fabricModule.fabric)
    }
    
    loadFabric()
  }, [])
  
  return fabric
}
```

### Asset Optimization

```typescript
// File: lib/assets/optimization.ts
class AssetOptimizer {
  // Preload critical assets
  public preloadCriticalAssets(): void {
    const criticalAssets = [
      '/icons/tools-sprite.svg',
      '/fonts/inter-var.woff2',
      '/images/default-canvas.jpg'
    ]
    
    criticalAssets.forEach(asset => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = asset
      link.as = this.getAssetType(asset)
      document.head.appendChild(link)
    })
  }
  
  // Lazy load non-critical assets
  public lazyLoadAssets(): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          img.src = img.dataset.src!
          img.classList.remove('lazy')
          observer.unobserve(img)
        }
      })
    })
    
    document.querySelectorAll('img[data-src]').forEach(img => {
      observer.observe(img)
    })
  }
  
  private getAssetType(asset: string): string {
    if (asset.endsWith('.woff2') || asset.endsWith('.woff')) return 'font'
    if (asset.endsWith('.svg')) return 'image'
    if (asset.endsWith('.js')) return 'script'
    if (asset.endsWith('.css')) return 'style'
    return 'image'
  }
}
```

## Network Performance

### Request Optimization

```typescript
// File: lib/network/optimization.ts
class NetworkOptimizer {
  private requestQueue: NetworkRequest[] = []
  private activeRequests = new Set<string>()
  private maxConcurrentRequests = 6
  
  public async optimizedFetch(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    // Check if identical request is already in progress
    const requestKey = this.generateRequestKey(url, options)
    if (this.activeRequests.has(requestKey)) {
      return this.getExistingRequest(requestKey)
    }
    
    // Add to queue if at max concurrent requests
    if (this.activeRequests.size >= this.maxConcurrentRequests) {
      return this.queueRequest(url, options)
    }
    
    // Execute immediately
    return this.executeRequest(url, options)
  }
  
  private async executeRequest(
    url: string,
    options: RequestInit
  ): Promise<Response> {
    const requestKey = this.generateRequestKey(url, options)
    this.activeRequests.add(requestKey)
    
    try {
      // Add request timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000)
      
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      // Process next queued request
      this.processQueue()
      
      return response
    } finally {
      this.activeRequests.delete(requestKey)
    }
  }
  
  private generateRequestKey(url: string, options: RequestInit): string {
    return btoa(`${url}:${JSON.stringify(options)}`)
  }
  
  private async queueRequest(
    url: string,
    options: RequestInit
  ): Promise<Response> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ url, options, resolve, reject })
    })
  }
  
  private processQueue(): void {
    if (this.requestQueue.length === 0) return
    if (this.activeRequests.size >= this.maxConcurrentRequests) return
    
    const nextRequest = this.requestQueue.shift()!
    this.executeRequest(nextRequest.url, nextRequest.options)
      .then(nextRequest.resolve)
      .catch(nextRequest.reject)
  }
}
```

### Response Compression

```typescript
// File: lib/network/compression.ts
class ResponseCompressor {
  public async compressResponse(data: any): Promise<Uint8Array> {
    const jsonString = JSON.stringify(data)
    const encoder = new TextEncoder()
    const input = encoder.encode(jsonString)
    
    // Use compression stream if available
    if ('CompressionStream' in window) {
      const stream = new CompressionStream('gzip')
      const writer = stream.writable.getWriter()
      const reader = stream.readable.getReader()
      
      writer.write(input)
      writer.close()
      
      const chunks: Uint8Array[] = []
      let done = false
      
      while (!done) {
        const { value, done: readerDone } = await reader.read()
        done = readerDone
        if (value) chunks.push(value)
      }
      
      return new Uint8Array(chunks.reduce((acc, chunk) => [...acc, ...chunk], []))
    }
    
    // Fallback to manual compression
    return this.manualCompress(input)
  }
  
  private manualCompress(data: Uint8Array): Uint8Array {
    // Simple LZ77-style compression
    const compressed: number[] = []
    let i = 0
    
    while (i < data.length) {
      const windowSize = Math.min(255, i)
      const lookaheadSize = Math.min(255, data.length - i)
      
      let bestMatch = { length: 0, distance: 0 }
      
      // Find best match in sliding window
      for (let j = Math.max(0, i - windowSize); j < i; j++) {
        let matchLength = 0
        
        while (
          matchLength < lookaheadSize &&
          data[j + matchLength] === data[i + matchLength]
        ) {
          matchLength++
        }
        
        if (matchLength > bestMatch.length) {
          bestMatch = { length: matchLength, distance: i - j }
        }
      }
      
      if (bestMatch.length >= 3) {
        // Encode match
        compressed.push(0xFF) // Match flag
        compressed.push(bestMatch.distance)
        compressed.push(bestMatch.length)
        i += bestMatch.length
      } else {
        // Encode literal
        compressed.push(data[i])
        i++
      }
    }
    
    return new Uint8Array(compressed)
  }
}
```

## Performance Monitoring

### Real-time Performance Tracking

```typescript
// File: lib/performance/monitoring.ts
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private observers: PerformanceObserver[] = []
  
  public startMonitoring(): void {
    this.monitorWebVitals()
    this.monitorCustomMetrics()
    this.monitorResourceTiming()
    this.monitorLongTasks()
  }
  
  private monitorWebVitals(): void {
    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lcp = entries[entries.length - 1]
      
      this.recordMetric('lcp', lcp.startTime, {
        element: lcp.element?.tagName,
        url: lcp.url
      })
    })
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    
    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        this.recordMetric('fid', entry.processingStart - entry.startTime)
      })
    })
    fidObserver.observe({ entryTypes: ['first-input'] })
    
    // Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          this.recordMetric('cls', entry.value)
        }
      })
    })
    clsObserver.observe({ entryTypes: ['layout-shift'] })
  }
  
  private monitorCustomMetrics(): void {
    // Canvas rendering time
    const renderObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.name === 'canvas-render') {
          this.recordMetric('canvas-render', entry.duration)
        }
      })
    })
    renderObserver.observe({ entryTypes: ['measure'] })
    
    // AI response time
    const aiObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.name.startsWith('ai-request')) {
          this.recordMetric('ai-response', entry.duration)
        }
      })
    })
    aiObserver.observe({ entryTypes: ['measure'] })
  }
  
  private monitorResourceTiming(): void {
    const resourceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        const resource = entry as PerformanceResourceTiming
        
        this.recordMetric('resource-load', resource.duration, {
          name: resource.name,
          type: resource.initiatorType,
          size: resource.transferSize
        })
      })
    })
    resourceObserver.observe({ entryTypes: ['resource'] })
  }
  
  private monitorLongTasks(): void {
    const longTaskObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        this.recordMetric('long-task', entry.duration, {
          attribution: entry.attribution
        })
      })
    })
    
    try {
      longTaskObserver.observe({ entryTypes: ['longtask'] })
    } catch (e) {
      // Long task monitoring not supported
    }
  }
  
  private recordMetric(
    name: string,
    value: number,
    metadata?: any
  ): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata
    }
    
    this.metrics.push(metric)
    
    // Keep only recent metrics
    if (this.metrics.length > 1000) {
      this.metrics.shift()
    }
    
    // Send to analytics
    this.sendToAnalytics(metric)
  }
  
  private sendToAnalytics(metric: PerformanceMetric): void {
    // Send to Vercel Analytics
    if (typeof window !== 'undefined' && window.va) {
      window.va('track', 'performance', {
        name: metric.name,
        value: metric.value,
        ...metric.metadata
      })
    }
    
    // Send to custom analytics
    if (window.gtag) {
      window.gtag('event', 'performance', {
        event_category: 'performance',
        event_label: metric.name,
        value: Math.round(metric.value)
      })
    }
  }
}
```

## Performance Benefits

### 1. Rendering Performance
- **60fps canvas operations**: Smooth real-time editing
- **Efficient object caching**: Reduced computation overhead
- **WebGL acceleration**: Hardware-accelerated filters
- **Optimized re-renders**: Only render when necessary

### 2. Memory Management
- **Automatic cleanup**: Prevents memory leaks
- **Smart caching**: Efficient memory usage
- **Garbage collection**: Proactive memory management
- **Resource monitoring**: Real-time memory tracking

### 3. Network Optimization
- **Request batching**: Reduced API calls
- **Response caching**: Faster subsequent requests
- **Asset optimization**: Efficient loading strategies
- **Compression**: Reduced payload sizes

### 4. Bundle Performance
- **Code splitting**: Load only needed code
- **Lazy loading**: Defer non-critical components
- **Tree shaking**: Remove unused code
- **Asset optimization**: Efficient resource loading

### 5. AI Performance
- **Request batching**: Efficient API usage
- **Response caching**: Faster repeated operations
- **Parallel processing**: Multiple operations simultaneously
- **Cost optimization**: Reduced API costs

This comprehensive performance architecture ensures FotoFun provides a smooth, responsive experience even during intensive photo editing operations and AI processing workflows.