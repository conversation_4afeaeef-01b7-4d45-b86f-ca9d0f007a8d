# FotoFun - Tools Framework Architecture

## Overview

FotoFun's tools framework provides a comprehensive, extensible system for photo editing operations. Built around a **Tool Adapter Pattern**, the framework bridges traditional canvas tools with AI-powered natural language control, creating a unified interface for both manual and automated editing workflows.

## Architecture Overview

```mermaid
graph TB
    subgraph "AI Interface Layer"
        NL[Natural Language Input]
        AI_Chat[AI Chat Interface]
        Router[Master Routing Agent]
    end
    
    subgraph "Tool Adapter Layer"
        Registry[Adapter Registry]
        BaseAdapter[Base Tool Adapter]
        CanvasAdapter[Canvas Tool Adapter]
        FilterAdapter[Filter Tool Adapter]
    end
    
    subgraph "Canvas Tools Layer"
        Selection[Selection Tools]
        Transform[Transform Tools]
        Drawing[Drawing Tools]
        Text[Text Tools]
        Adjustments[Adjustment Tools]
        Filters[Filter Tools]
        AI_Native[AI-Native Tools]
    end
    
    subgraph "Canvas System"
        Fabric[Fabric.js Canvas]
        Commands[Command System]
        History[History Management]
    end
    
    NL --> AI_Chat
    AI_Chat --> Router
    Router --> Registry
    Registry --> BaseAdapter
    BaseAdapter --> CanvasAdapter
    BaseAdapter --> FilterAdapter
    CanvasAdapter --> Selection
    CanvasAdapter --> Transform
    CanvasAdapter --> Drawing
    CanvasAdapter --> Text
    CanvasAdapter --> Adjustments
    FilterAdapter --> Filters
    Registry --> AI_Native
    Selection --> Fabric
    Transform --> Fabric
    Drawing --> Fabric
    Text --> Fabric
    Adjustments --> Fabric
    Filters --> Fabric
    AI_Native --> Fabric
    Fabric --> Commands
    Commands --> History
```

## Tool Categories

### 1. Selection Tools (6 tools)
**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/selection/`

#### Marquee Rectangle Tool
```typescript
// File: marqueeRectTool.ts
class MarqueeRectTool extends SelectionTool {
  id = 'marquee-rect'
  name = 'Rectangular Marquee'
  description = 'Create rectangular selections'
  
  async activate(): Promise<void> {
    this.canvas.defaultCursor = 'crosshair'
    this.setupEventHandlers()
  }
  
  private setupEventHandlers(): void {
    this.canvas.on('mouse:down', this.handleMouseDown.bind(this))
    this.canvas.on('mouse:move', this.handleMouseMove.bind(this))
    this.canvas.on('mouse:up', this.handleMouseUp.bind(this))
  }
  
  private handleMouseDown(e: fabric.IEvent): void {
    this.startPoint = e.pointer
    this.isDrawing = true
  }
  
  private handleMouseMove(e: fabric.IEvent): void {
    if (!this.isDrawing) return
    
    const rect = this.createSelectionRect(this.startPoint, e.pointer)
    this.selectionManager.updatePreview(rect)
  }
  
  private handleMouseUp(): void {
    this.isDrawing = false
    this.selectionManager.commitSelection()
  }
}
```

#### Magic Wand Tool
```typescript
// File: magicWandTool.ts
class MagicWandTool extends SelectionTool {
  id = 'magic-wand'
  name = 'Magic Wand'
  description = 'Select similar colored areas'
  
  options = {
    tolerance: 20,
    contiguous: true,
    antiAlias: true
  }
  
  async activate(): Promise<void> {
    this.canvas.defaultCursor = 'crosshair'
    this.canvas.on('mouse:down', this.handleClick.bind(this))
  }
  
  private async handleClick(e: fabric.IEvent): Promise<void> {
    const point = e.pointer
    const imageData = this.getImageDataAtPoint(point)
    
    if (!imageData) return
    
    const selection = await this.selectSimilarPixels(
      point,
      imageData,
      this.options.tolerance,
      this.options.contiguous
    )
    
    this.selectionManager.setSelection(selection)
  }
  
  private async selectSimilarPixels(
    startPoint: fabric.Point,
    imageData: ImageData,
    tolerance: number,
    contiguous: boolean
  ): Promise<Selection> {
    const targetColor = this.getColorAtPoint(startPoint, imageData)
    const width = imageData.width
    const height = imageData.height
    const pixels = imageData.data
    
    const visited = new Set<string>()
    const selectedPixels = new Set<string>()
    
    if (contiguous) {
      // Flood fill algorithm
      this.floodFill(startPoint, targetColor, tolerance, pixels, width, height, visited, selectedPixels)
    } else {
      // Select all similar pixels
      this.selectAllSimilar(targetColor, tolerance, pixels, width, height, selectedPixels)
    }
    
    return this.createSelectionFromPixels(selectedPixels, width, height)
  }
}
```

### 2. Transform Tools (6 tools)
**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/transform/`

#### Crop Tool
```typescript
// File: cropTool.ts
class CropTool extends BaseTool {
  id = 'crop'
  name = 'Crop'
  description = 'Crop image to selection'
  
  private cropRect: fabric.Rect | null = null
  private isDragging = false
  
  async activate(): Promise<void> {
    this.canvas.defaultCursor = 'crosshair'
    this.setupCropOverlay()
    this.setupEventHandlers()
  }
  
  private setupCropOverlay(): void {
    const canvasWidth = this.canvas.width
    const canvasHeight = this.canvas.height
    
    // Create semi-transparent overlay
    const overlay = new fabric.Rect({
      left: 0,
      top: 0,
      width: canvasWidth,
      height: canvasHeight,
      fill: 'rgba(0, 0, 0, 0.5)',
      selectable: false,
      evented: false
    })
    
    this.canvas.add(overlay)
    this.canvas.renderAll()
  }
  
  async execute(params: CropParams): Promise<CropResult> {
    const { bounds, preserveAspectRatio } = params
    
    // Validate bounds
    if (!this.validateCropBounds(bounds)) {
      throw new Error('Invalid crop bounds')
    }
    
    // Execute crop command
    const command = new CropCommand(this.canvas, bounds, preserveAspectRatio)
    await command.execute()
    
    // Add to history
    this.historyManager.addCommand(command)
    
    return {
      success: true,
      newDimensions: {
        width: bounds.width,
        height: bounds.height
      }
    }
  }
}
```

#### Rotate Tool
```typescript
// File: rotateTool.ts
class RotateTool extends BaseTool {
  id = 'rotate'
  name = 'Rotate'
  description = 'Rotate image or selection'
  
  options = {
    snapToAngles: [0, 90, 180, 270],
    snapTolerance: 5
  }
  
  async execute(params: RotateParams): Promise<RotateResult> {
    const { angle, target = 'canvas' } = params
    
    // Snap to common angles if close
    const snappedAngle = this.snapAngle(angle)
    
    let command: Command
    
    if (target === 'canvas') {
      command = new RotateCanvasCommand(this.canvas, snappedAngle)
    } else {
      const activeObject = this.canvas.getActiveObject()
      if (!activeObject) {
        throw new Error('No object selected to rotate')
      }
      command = new RotateObjectCommand(activeObject, snappedAngle)
    }
    
    await command.execute()
    this.historyManager.addCommand(command)
    
    return {
      success: true,
      appliedAngle: snappedAngle
    }
  }
  
  private snapAngle(angle: number): number {
    for (const snapAngle of this.options.snapToAngles) {
      if (Math.abs(angle - snapAngle) <= this.options.snapTolerance) {
        return snapAngle
      }
    }
    return angle
  }
}
```

### 3. Drawing Tools (2 tools)
**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/drawing/`

#### Brush Tool
```typescript
// File: brushTool.ts
class BrushTool extends DrawingTool {
  id = 'brush'
  name = 'Brush'
  description = 'Paint with brush strokes'
  
  options = {
    size: 10,
    opacity: 1,
    color: '#000000',
    hardness: 0.5,
    pressure: false
  }
  
  private currentPath: fabric.Path | null = null
  private pathPoints: fabric.Point[] = []
  
  async activate(): Promise<void> {
    this.canvas.isDrawingMode = true
    this.canvas.freeDrawingBrush = this.createBrush()
    this.setupEventHandlers()
  }
  
  private createBrush(): fabric.BaseBrush {
    const brush = new fabric.PencilBrush(this.canvas)
    brush.width = this.options.size
    brush.color = this.options.color
    
    // Custom brush implementation for hardness
    if (this.options.hardness < 1) {
      brush.strokeLineCap = 'round'
      brush.strokeLineJoin = 'round'
    }
    
    return brush
  }
  
  private setupEventHandlers(): void {
    this.canvas.on('path:created', this.handlePathCreated.bind(this))
  }
  
  private handlePathCreated(e: fabric.IEvent): void {
    const path = e.path as fabric.Path
    
    // Apply brush effects
    this.applyBrushEffects(path)
    
    // Create command for undo/redo
    const command = new AddPathCommand(this.canvas, path)
    this.historyManager.addCommand(command)
  }
  
  private applyBrushEffects(path: fabric.Path): void {
    // Apply opacity
    path.opacity = this.options.opacity
    
    // Apply hardness by adjusting stroke properties
    if (this.options.hardness < 1) {
      path.shadow = new fabric.Shadow({
        blur: (1 - this.options.hardness) * 10,
        color: this.options.color,
        offsetX: 0,
        offsetY: 0
      })
    }
  }
}
```

### 4. Text Tools (4 tools)
**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/text/`

#### Horizontal Type Tool
```typescript
// File: HorizontalTypeTool.ts
class HorizontalTypeTool extends BaseTextTool {
  id = 'horizontal-type'
  name = 'Horizontal Type'
  description = 'Create horizontal text'
  
  async activate(): Promise<void> {
    this.canvas.defaultCursor = 'text'
    this.canvas.on('mouse:down', this.handleMouseDown.bind(this))
  }
  
  private handleMouseDown(e: fabric.IEvent): void {
    // Check if clicking on existing text
    const target = e.target
    if (target && target.type === 'textbox') {
      this.enterEditMode(target as fabric.Textbox)
      return
    }
    
    // Create new text
    this.createText(e.pointer)
  }
  
  private createText(point: fabric.Point): void {
    const text = new fabric.Textbox('Type here', {
      left: point.x,
      top: point.y,
      fontFamily: this.options.fontFamily,
      fontSize: this.options.fontSize,
      fill: this.options.color,
      width: 200,
      editable: true
    })
    
    this.canvas.add(text)
    this.canvas.setActiveObject(text)
    this.enterEditMode(text)
  }
  
  private enterEditMode(textbox: fabric.Textbox): void {
    textbox.enterEditing()
    textbox.selectAll()
    
    // Setup exit handlers
    textbox.on('editing:exited', () => {
      this.exitEditMode(textbox)
    })
  }
  
  private exitEditMode(textbox: fabric.Textbox): void {
    // Create command for undo/redo
    const command = new AddTextCommand(this.canvas, textbox)
    this.historyManager.addCommand(command)
    
    // Clean up event handlers
    textbox.off('editing:exited')
  }
}
```

### 5. Adjustment Tools (6 tools)
**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/adjustments/`

#### Brightness Tool
```typescript
// File: brightnessTool.ts
class BrightnessTool extends AdjustmentTool {
  id = 'brightness'
  name = 'Brightness'
  description = 'Adjust image brightness'
  
  options = {
    adjustment: 0, // -100 to +100
    previewEnabled: true
  }
  
  async execute(params: BrightnessParams): Promise<BrightnessResult> {
    const { adjustment } = params
    
    // Validate adjustment range
    if (adjustment < -100 || adjustment > 100) {
      throw new Error('Brightness adjustment must be between -100 and +100')
    }
    
    // Get target images
    const images = this.getTargetImages()
    if (images.length === 0) {
      throw new Error('No images found to adjust brightness')
    }
    
    // Apply brightness adjustment
    const results = await Promise.all(
      images.map(image => this.applyBrightnessToImage(image, adjustment))
    )
    
    // Create command for undo/redo
    const command = new AdjustBrightnessCommand(images, adjustment)
    this.historyManager.addCommand(command)
    
    return {
      success: true,
      imagesAdjusted: results.length,
      adjustment
    }
  }
  
  private async applyBrightnessToImage(
    image: fabric.Image,
    adjustment: number
  ): Promise<void> {
    // Create brightness filter
    const filter = new fabric.Image.filters.Brightness({
      brightness: adjustment / 100
    })
    
    // Apply filter
    if (!image.filters) {
      image.filters = []
    }
    
    // Remove existing brightness filters
    image.filters = image.filters.filter(f => f.type !== 'Brightness')
    
    // Add new brightness filter
    image.filters.push(filter)
    
    // Apply filters and render
    image.applyFilters()
    this.canvas.renderAll()
  }
  
  // Preview support
  async preview(params: BrightnessParams): Promise<void> {
    if (!this.options.previewEnabled) return
    
    const { adjustment } = params
    const images = this.getTargetImages()
    
    // Store original state for preview
    this.storeOriginalState(images)
    
    // Apply temporary adjustment
    await Promise.all(
      images.map(image => this.applyBrightnessToImage(image, adjustment))
    )
  }
  
  async cancelPreview(): Promise<void> {
    if (!this.hasStoredState()) return
    
    // Restore original state
    this.restoreOriginalState()
    this.canvas.renderAll()
  }
}
```

### 6. Filter Tools (5 tools)
**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/filters/`

#### Blur Tool
```typescript
// File: blurTool.ts
class BlurTool extends BaseFilterTool {
  id = 'blur'
  name = 'Blur'
  description = 'Apply blur effect'
  
  options = {
    radius: 5,
    type: 'gaussian' as 'gaussian' | 'motion' | 'radial'
  }
  
  protected getFilterType(): string {
    return 'Blur'
  }
  
  protected createFilter(params: BlurParams): fabric.Image.filters.Blur {
    const { radius } = params
    
    return new fabric.Image.filters.Blur({
      blur: radius / 100
    })
  }
  
  protected shouldApplyFilter(params: BlurParams): boolean {
    return params.radius > 0
  }
  
  async execute(params: BlurParams): Promise<BlurResult> {
    const { radius, type = 'gaussian' } = params
    
    // Validate parameters
    if (radius < 0 || radius > 100) {
      throw new Error('Blur radius must be between 0 and 100')
    }
    
    // Get target images
    const images = this.getTargetImages()
    if (images.length === 0) {
      throw new Error('No images found to blur')
    }
    
    // Apply blur filter
    await this.applyFilterToImages(images, params, this.canvas)
    
    // Create command for undo/redo
    const command = new ApplyFilterCommand(images, this.getFilterType(), params)
    this.historyManager.addCommand(command)
    
    return {
      success: true,
      imagesProcessed: images.length,
      filterType: type,
      radius
    }
  }
  
  // Advanced blur types
  private createMotionBlur(params: MotionBlurParams): fabric.Image.filters.BaseFilter {
    return new fabric.Image.filters.Convolute({
      matrix: this.createMotionBlurMatrix(params.angle, params.distance)
    })
  }
  
  private createRadialBlur(params: RadialBlurParams): fabric.Image.filters.BaseFilter {
    // Custom radial blur implementation
    return new fabric.Image.filters.BaseFilter({
      type: 'RadialBlur',
      fragmentSource: this.getRadialBlurShader(),
      uniforms: {
        uRadius: params.radius,
        uCenterX: params.centerX,
        uCenterY: params.centerY
      }
    })
  }
}
```

### 7. AI-Native Tools (2 tools)
**Location**: `/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/ai-native/`

#### Image Generation Tool
```typescript
// File: imageGenerationCanvasTool.ts
class ImageGenerationCanvasTool extends AIToolWrapper {
  id = 'image-generation'
  name = 'Image Generation'
  description = 'Generate images from text descriptions'
  
  baseAITool = new ImageGenerationTool()
  
  async execute(params: ImageGenerationParams): Promise<ImageGenerationResult> {
    const { prompt, width = 512, height = 512, style = 'photographic' } = params
    
    // Validate parameters
    this.validateParams(params)
    
    // Check for cost approval
    const cost = this.estimateCost(params)
    if (cost > 0.01) { // Threshold for auto-approval
      throw new Error('Cost approval required for image generation')
    }
    
    // Generate image
    const result = await this.baseAITool.execute({
      prompt,
      width,
      height,
      style
    })
    
    if (!result.success) {
      throw new Error(result.error || 'Image generation failed')
    }
    
    // Add generated image to canvas
    const image = await this.createImageFromURL(result.imageUrl)
    this.canvas.add(image)
    this.canvas.renderAll()
    
    // Create command for undo/redo
    const command = new AddObjectCommand(this.canvas, image)
    this.historyManager.addCommand(command)
    
    return {
      success: true,
      imageUrl: result.imageUrl,
      dimensions: { width, height },
      generatedPrompt: prompt
    }
  }
  
  private validateParams(params: ImageGenerationParams): void {
    if (!params.prompt || params.prompt.trim().length === 0) {
      throw new Error('Prompt is required for image generation')
    }
    
    if (params.width && (params.width < 256 || params.width > 1024)) {
      throw new Error('Width must be between 256 and 1024 pixels')
    }
    
    if (params.height && (params.height < 256 || params.height > 1024)) {
      throw new Error('Height must be between 256 and 1024 pixels')
    }
  }
  
  private estimateCost(params: ImageGenerationParams): number {
    // Base cost for image generation
    const baseCost = 0.012
    
    // Additional cost for larger images
    const pixelMultiplier = ((params.width || 512) * (params.height || 512)) / (512 * 512)
    
    return baseCost * pixelMultiplier
  }
}
```

## Tool Adapter System

### Base Tool Adapter (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/adapters/base.ts`)

```typescript
abstract class BaseToolAdapter<TInput, TOutput> {
  // Core properties
  abstract tool: Tool
  abstract aiName: string
  abstract description: string
  abstract metadata: ToolMetadata
  abstract inputSchema: z.ZodType<TInput>
  
  // Execution method
  abstract execute(params: TInput, context: CanvasContext): Promise<TOutput>
  
  // Optional methods
  canExecute?(canvas: Canvas): boolean
  generatePreview?(params: TInput, canvas: Canvas): Promise<Preview>
  
  // Convert to AI SDK tool
  toAITool(): unknown {
    return tool({
      description: this.description,
      inputSchema: this.inputSchema,
      execute: async (args: unknown) => {
        return {
          success: true,
          message: `Tool ${this.aiName} will be executed on the client`,
          clientExecutionRequired: true,
          params: args
        }
      }
    })
  }
}
```

### Canvas Tool Adapter

```typescript
abstract class CanvasToolAdapter<TInput, TOutput> extends BaseToolAdapter<TInput, TOutput> {
  // Common execute pattern
  protected async executeWithCommonPatterns(
    params: TInput,
    context: CanvasContext,
    toolExecution: (images: FabricImage[]) => Promise<Partial<TOutput>>
  ): Promise<TOutput> {
    try {
      const canvas = context.canvas
      const images = context.targetImages
      
      if (images.length === 0) {
        throw new Error(`No images found to ${this.getActionVerb()}`)
      }
      
      // Execute tool-specific logic
      const result = await toolExecution(images)
      
      return {
        success: true,
        targetingMode: context.targetingMode,
        ...result
      } as TOutput
      
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : `Failed to ${this.getActionVerb()}`,
        targetingMode: context.targetingMode
      } as TOutput
    }
  }
  
  // Helper methods
  protected async activateTool(): Promise<void> {
    const { useToolStore } = await import('@/store/toolStore')
    useToolStore.getState().setActiveTool(this.tool.id)
  }
  
  protected async updateToolOption(optionName: string, value: unknown): Promise<void> {
    const { useToolOptionsStore } = await import('@/store/toolOptionsStore')
    useToolOptionsStore.getState().updateOption(this.tool.id, optionName, value)
  }
  
  protected abstract getActionVerb(): string
}
```

### Filter Tool Adapter

```typescript
abstract class FilterToolAdapter<TInput, TOutput> extends CanvasToolAdapter<TInput, TOutput> {
  // Filter-specific methods
  protected abstract getFilterType(): string
  protected abstract createFilter(params: TInput): unknown
  protected abstract shouldApplyFilter(params: TInput): boolean
  
  // Apply filters to images
  protected async applyFilterToImages(
    images: FabricImage[],
    params: TInput,
    canvas: Canvas
  ): Promise<void> {
    const filterType = this.getFilterType()
    
    images.forEach((img, index) => {
      const imageWithFilters = img as FabricImageWithFilters
      
      // Initialize filters array
      if (!imageWithFilters.filters) {
        imageWithFilters.filters = []
      } else {
        // Remove existing filters of this type
        imageWithFilters.filters = imageWithFilters.filters.filter(
          (f: unknown) => (f as ImageFilter).type !== filterType
        )
      }
      
      // Add new filter if needed
      if (this.shouldApplyFilter(params)) {
        const filter = this.createFilter(params)
        imageWithFilters.filters.push(filter)
      }
      
      // Apply filters
      imageWithFilters.applyFilters()
    })
    
    canvas.renderAll()
  }
}
```

## Tool Registry System

### Adapter Registry (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/ai/adapters/registry.ts`)

```typescript
class AdapterRegistry {
  private adapters = new Map<string, BaseToolAdapter>()
  private categories = new Map<string, string[]>()
  
  // Registration
  register(adapter: BaseToolAdapter): void {
    this.adapters.set(adapter.aiName, adapter)
    this.addToCategory(adapter.metadata.category, adapter.aiName)
  }
  
  // Retrieval
  get(name: string): BaseToolAdapter | undefined {
    return this.adapters.get(name)
  }
  
  getAll(): Map<string, BaseToolAdapter> {
    return new Map(this.adapters)
  }
  
  // Category management
  getToolNamesByCategory(category: string): string[] {
    return this.categories.get(category) || []
  }
  
  // AI tools conversion
  getAITools(): Record<string, AITool> {
    const tools: Record<string, AITool> = {}
    this.adapters.forEach((adapter) => {
      tools[adapter.aiName] = adapter.toAITool()
    })
    return tools
  }
  
  // Auto-discovery
  async autoDiscoverAdapters(): Promise<void> {
    const adapterFiles = await this.discoverAdapterFiles()
    
    for (const file of adapterFiles) {
      try {
        const module = await import(file)
        const adapter = module.default || module.adapter
        
        if (adapter && this.isValidAdapter(adapter)) {
          this.register(adapter)
        }
      } catch (error) {
        console.warn(`Failed to load adapter from ${file}:`, error)
      }
    }
  }
  
  private isValidAdapter(adapter: unknown): adapter is BaseToolAdapter {
    return (
      typeof adapter === 'object' &&
      adapter !== null &&
      'aiName' in adapter &&
      'description' in adapter &&
      'execute' in adapter
    )
  }
}

// Global registry instance
export const adapterRegistry = new AdapterRegistry()
```

## Tool Options System

### Tool Options Store (`/Users/<USER>/Projects/own/assistant/foto-fun/store/toolOptionsStore.ts`)

```typescript
interface ToolOptionsStore {
  // Options storage
  options: Record<string, Record<string, unknown>>
  
  // Option management
  updateOption(toolId: string, optionName: string, value: unknown): void
  getOption(toolId: string, optionName: string): unknown
  resetOptions(toolId: string): void
  
  // Batch operations
  updateOptions(toolId: string, options: Record<string, unknown>): void
  getOptions(toolId: string): Record<string, unknown>
  
  // Presets
  savePreset(toolId: string, presetName: string, options: Record<string, unknown>): void
  loadPreset(toolId: string, presetName: string): void
  getPresets(toolId: string): Record<string, Record<string, unknown>>
}
```

### Tool Options Components (`/Users/<USER>/Projects/own/assistant/foto-fun/components/editor/ToolOptions/`)

```typescript
// Option components for different data types
interface ToolOptionComponents {
  OptionSlider: React.FC<{
    label: string
    value: number
    min: number
    max: number
    step?: number
    onChange: (value: number) => void
  }>
  
  OptionCheckbox: React.FC<{
    label: string
    value: boolean
    onChange: (value: boolean) => void
  }>
  
  OptionDropdown: React.FC<{
    label: string
    value: string
    options: { label: string; value: string }[]
    onChange: (value: string) => void
  }>
  
  OptionColor: React.FC<{
    label: string
    value: string
    onChange: (value: string) => void
  }>
  
  OptionNumber: React.FC<{
    label: string
    value: number
    min?: number
    max?: number
    step?: number
    onChange: (value: number) => void
  }>
  
  OptionButtonGroup: React.FC<{
    label: string
    value: string
    options: { label: string; value: string; icon?: React.ReactNode }[]
    onChange: (value: string) => void
  }>
}
```

## Command Pattern Integration

### Command System (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/commands/`)

```typescript
// Base command class
abstract class Command {
  abstract execute(): void | Promise<void>
  abstract undo(): void | Promise<void>
  abstract canUndo(): boolean
  abstract getDescription(): string
}

// Tool-specific commands
class ApplyFilterCommand extends Command {
  private images: FabricImage[]
  private filterType: string
  private params: unknown
  private previousState: ImageData[]
  
  constructor(images: FabricImage[], filterType: string, params: unknown) {
    super()
    this.images = images
    this.filterType = filterType
    this.params = params
    this.previousState = []
  }
  
  async execute(): Promise<void> {
    // Store previous state
    this.previousState = this.images.map(img => this.captureImageState(img))
    
    // Apply filter
    await this.applyFilter()
  }
  
  async undo(): Promise<void> {
    // Restore previous state
    this.images.forEach((img, index) => {
      this.restoreImageState(img, this.previousState[index])
    })
  }
  
  canUndo(): boolean {
    return this.previousState.length > 0
  }
  
  getDescription(): string {
    return `Apply ${this.filterType} filter`
  }
}
```

## Tool Constraint System

### Tool Constraints (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/utils/constraints.ts`)

```typescript
interface ToolConstraints {
  // Canvas requirements
  requiresCanvas: boolean
  requiresContent: boolean
  requiresSelection: boolean
  
  // Object requirements
  requiresActiveObject: boolean
  supportedObjectTypes: string[]
  
  // State requirements
  requiresEditableState: boolean
  requiresWritableCanvas: boolean
  
  // Resource requirements
  maxImageSize: number
  maxObjectCount: number
  supportedFormats: string[]
}

class ConstraintValidator {
  static validate(tool: Tool, canvas: Canvas, constraints: ToolConstraints): ValidationResult {
    const errors: string[] = []
    
    // Check canvas requirements
    if (constraints.requiresCanvas && !canvas) {
      errors.push('Canvas is required')
    }
    
    if (constraints.requiresContent && !this.hasContent(canvas)) {
      errors.push('Canvas content is required')
    }
    
    if (constraints.requiresSelection && !this.hasSelection(canvas)) {
      errors.push('Selection is required')
    }
    
    // Check object requirements
    if (constraints.requiresActiveObject && !canvas.getActiveObject()) {
      errors.push('Active object is required')
    }
    
    // Check supported object types
    const activeObject = canvas.getActiveObject()
    if (activeObject && constraints.supportedObjectTypes.length > 0) {
      if (!constraints.supportedObjectTypes.includes(activeObject.type)) {
        errors.push(`Object type ${activeObject.type} is not supported`)
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  private static hasContent(canvas: Canvas): boolean {
    return canvas.getObjects().length > 0
  }
  
  private static hasSelection(canvas: Canvas): boolean {
    const selectionManager = canvas.selectionManager
    return selectionManager && selectionManager.hasSelection()
  }
}
```

## Tool State Management

### Tool State (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/editor/tools/utils/toolState.ts`)

```typescript
interface ToolState {
  id: string
  isActive: boolean
  isEnabled: boolean
  options: Record<string, unknown>
  constraints: ToolConstraints
  lastUsed: number
  usageCount: number
}

class ToolStateManager {
  private states = new Map<string, ToolState>()
  
  // State management
  getState(toolId: string): ToolState | null {
    return this.states.get(toolId) || null
  }
  
  setState(toolId: string, state: Partial<ToolState>): void {
    const current = this.getState(toolId) || this.createDefaultState(toolId)
    this.states.set(toolId, { ...current, ...state })
  }
  
  // Tool lifecycle
  activateTool(toolId: string): void {
    // Deactivate current tool
    this.deactivateAllTools()
    
    // Activate new tool
    this.setState(toolId, {
      isActive: true,
      lastUsed: Date.now(),
      usageCount: (this.getState(toolId)?.usageCount || 0) + 1
    })
  }
  
  deactivateTool(toolId: string): void {
    this.setState(toolId, { isActive: false })
  }
  
  private deactivateAllTools(): void {
    this.states.forEach((state, toolId) => {
      if (state.isActive) {
        this.setState(toolId, { isActive: false })
      }
    })
  }
}
```

## Performance Optimizations

### Tool Performance Monitoring

```typescript
class ToolPerformanceMonitor {
  private metrics = new Map<string, ToolMetrics>()
  
  // Metrics collection
  recordExecution(toolId: string, duration: number, success: boolean): void {
    const current = this.metrics.get(toolId) || this.createDefaultMetrics(toolId)
    
    current.executionCount++
    current.totalExecutionTime += duration
    current.averageExecutionTime = current.totalExecutionTime / current.executionCount
    
    if (success) {
      current.successCount++
    } else {
      current.errorCount++
    }
    
    current.successRate = current.successCount / current.executionCount
    
    this.metrics.set(toolId, current)
  }
  
  // Analysis
  getSlowTools(threshold: number = 1000): string[] {
    return Array.from(this.metrics.entries())
      .filter(([_, metrics]) => metrics.averageExecutionTime > threshold)
      .map(([toolId, _]) => toolId)
  }
  
  getUnreliableTools(threshold: number = 0.9): string[] {
    return Array.from(this.metrics.entries())
      .filter(([_, metrics]) => metrics.successRate < threshold)
      .map(([toolId, _]) => toolId)
  }
}
```

## Extension Points

### Custom Tool Development

```typescript
// Custom tool interface
interface CustomTool {
  // Required properties
  id: string
  name: string
  description: string
  category: ToolCategory
  
  // Optional properties
  icon?: React.ReactNode
  shortcut?: string
  options?: ToolOptions
  constraints?: ToolConstraints
  
  // Required methods
  activate(): Promise<void>
  deactivate(): Promise<void>
  execute(params: unknown): Promise<unknown>
  
  // Optional methods
  canExecute?(canvas: Canvas): boolean
  preview?(params: unknown): Promise<void>
  cancelPreview?(): Promise<void>
}

// Custom tool registration
class CustomToolRegistry {
  private tools = new Map<string, CustomTool>()
  
  register(tool: CustomTool): void {
    // Validate tool
    this.validateTool(tool)
    
    // Register tool
    this.tools.set(tool.id, tool)
    
    // Create adapter
    const adapter = new CustomToolAdapter(tool)
    adapterRegistry.register(adapter)
    
    // Update UI
    this.updateToolPalette(tool)
  }
  
  unregister(toolId: string): void {
    this.tools.delete(toolId)
    // Remove from registry and UI
  }
}
```

### Plugin Architecture

```typescript
// Plugin interface
interface ToolPlugin {
  name: string
  version: string
  tools: CustomTool[]
  
  // Lifecycle
  install(): Promise<void>
  uninstall(): Promise<void>
  
  // Configuration
  configure?(config: PluginConfig): void
  
  // Dependencies
  dependencies?: string[]
  peerDependencies?: string[]
}

// Plugin manager
class ToolPluginManager {
  private plugins = new Map<string, ToolPlugin>()
  
  async install(plugin: ToolPlugin): Promise<void> {
    // Check dependencies
    await this.checkDependencies(plugin)
    
    // Install plugin
    await plugin.install()
    
    // Register tools
    plugin.tools.forEach(tool => {
      customToolRegistry.register(tool)
    })
    
    this.plugins.set(plugin.name, plugin)
  }
  
  async uninstall(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName)
    if (!plugin) return
    
    // Unregister tools
    plugin.tools.forEach(tool => {
      customToolRegistry.unregister(tool.id)
    })
    
    // Uninstall plugin
    await plugin.uninstall()
    
    this.plugins.delete(pluginName)
  }
}
```

## Future Enhancements

### AI-Powered Tool Suggestions

```typescript
// AI tool suggestion system
class AIToolSuggester {
  async suggestTools(
    canvas: Canvas,
    userIntent: string,
    context: EditorContext
  ): Promise<ToolSuggestion[]> {
    const canvasAnalysis = this.analyzeCanvas(canvas)
    const userHistory = this.getUserHistory(context.userId)
    
    const suggestions = await this.generateSuggestions({
      intent: userIntent,
      canvasState: canvasAnalysis,
      userHistory,
      availableTools: this.getAvailableTools()
    })
    
    return suggestions.map(suggestion => ({
      tool: suggestion.tool,
      confidence: suggestion.confidence,
      reason: suggestion.reason,
      expectedOutcome: suggestion.expectedOutcome
    }))
  }
}
```

### Real-time Collaboration Tools

```typescript
// Collaborative tool system
class CollaborativeToolManager {
  private activeCollaborators = new Map<string, CollaboratorState>()
  
  // Tool synchronization
  syncToolActivation(userId: string, toolId: string): void {
    this.broadcastToolChange({
      userId,
      toolId,
      action: 'activate',
      timestamp: Date.now()
    })
  }
  
  // Conflict resolution
  resolveToolConflict(
    userA: string,
    userB: string,
    toolId: string
  ): ConflictResolution {
    // Implement conflict resolution logic
    return {
      winner: userA,
      reason: 'First to activate',
      alternativeActions: []
    }
  }
}
```

## Tools Framework Benefits

### 1. Unified Architecture
- **Consistent Interface**: All tools follow the same patterns
- **AI Integration**: Every tool can be controlled via natural language
- **Extensibility**: Easy to add new tools and categories
- **Type Safety**: Full TypeScript support with compile-time validation

### 2. Performance Optimization
- **Lazy Loading**: Tools loaded on demand
- **Efficient Rendering**: Optimized canvas updates
- **Memory Management**: Proper cleanup and resource management
- **Caching**: Intelligent caching of tool states and results

### 3. User Experience
- **Intuitive Controls**: Consistent tool behavior and options
- **Real-time Preview**: Live preview of tool effects
- **Undo/Redo**: Full command history support
- **Accessibility**: Keyboard shortcuts and screen reader support

### 4. Developer Experience
- **Clear APIs**: Well-documented interfaces and patterns
- **Hot Reload**: Fast development cycles
- **Testing Support**: Comprehensive testing utilities
- **Plugin System**: Easy extension and customization

This tools framework provides a comprehensive foundation for building a professional-grade photo editing application with modern development practices and AI-first design principles.