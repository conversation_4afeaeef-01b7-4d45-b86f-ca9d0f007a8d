# FotoFun - Configuration & Environment Setup

## Overview

FotoFun uses a comprehensive configuration system that manages environment variables, API keys, feature flags, and application settings. The configuration is designed for security, flexibility, and ease of deployment across different environments.

## Environment Variables

### Required Environment Variables

```bash
# .env.local (Development)
# .env.production (Production)

# AI Services Configuration
REPLICATE_API_TOKEN=r8_YOUR_REPLICATE_TOKEN_HERE
OPENAI_API_KEY=sk-YOUR_OPENAI_API_KEY_HERE

# Database Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Authentication
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=FotoFun
NEXT_PUBLIC_APP_VERSION=1.0.0

# Security
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# Analytics (Optional)
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
VERCEL_ANALYTICS_ID=your-vercel-analytics-id

# Error Tracking (Optional)
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project
```

### Development Environment Variables

```bash
# .env.local (Development Only)

# Development Features
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_ENABLE_DEVTOOLS=true

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
NEXT_PUBLIC_WS_URL=ws://localhost:3000

# Database Development
DATABASE_URL=postgresql://username:password@localhost:5432/fotofun_dev
SHADOW_DATABASE_URL=postgresql://username:password@localhost:5432/fotofun_shadow

# Storage Configuration
NEXT_PUBLIC_STORAGE_BUCKET=fotofun-dev
STORAGE_ACCESS_KEY=your-storage-access-key
STORAGE_SECRET_KEY=your-storage-secret-key

# Email Configuration (Development)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
```

### Production Environment Variables

```bash
# .env.production (Production Only)

# Production Features
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_ENABLE_DEVTOOLS=false

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://fotofun.app/api
NEXT_PUBLIC_WS_URL=wss://fotofun.app

# CDN Configuration
NEXT_PUBLIC_CDN_URL=https://cdn.fotofun.app
NEXT_PUBLIC_ASSETS_URL=https://assets.fotofun.app

# Performance Monitoring
NEXT_PUBLIC_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_ERROR_REPORTING=true

# Security
CONTENT_SECURITY_POLICY=strict
HSTS_MAX_AGE=31536000
```

## Configuration Files

### Next.js Configuration (`/Users/<USER>/Projects/own/assistant/foto-fun/next.config.ts`)

```typescript
import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  // Experimental features
  experimental: {
    serverActions: true,
    serverComponentsExternalPackages: ['fabric'],
    optimizePackageImports: ['lucide-react']
  },
  
  // Images configuration
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'replicate.delivery',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'replicate.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'your-supabase-project.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      }
    ],
    domains: ['localhost'],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  
  // Webpack configuration
  webpack: (config, { dev, isServer }) => {
    // Canvas.js configuration for server-side rendering
    config.externals = config.externals || {}
    if (isServer) {
      config.externals['canvas'] = 'commonjs canvas'
    }
    
    // Fabric.js configuration
    config.resolve.alias = {
      ...config.resolve.alias,
      'fabric': 'fabric/dist/fabric.min.js'
    }
    
    // Performance optimizations
    if (!dev) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          fabric: {
            test: /[\\/]node_modules[\\/]fabric[\\/]/,
            name: 'fabric',
            chunks: 'all',
          }
        }
      }
    }
    
    return config
  },
  
  // Headers configuration
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.NODE_ENV === 'production' 
              ? 'https://fotofun.app' 
              : 'http://localhost:3000'
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS'
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization'
          }
        ]
      }
    ]
  },
  
  // Redirects configuration
  async redirects() {
    return [
      {
        source: '/',
        destination: '/editor',
        permanent: false,
        has: [
          {
            type: 'cookie',
            key: 'user-session'
          }
        ]
      }
    ]
  },
  
  // Rewrites configuration
  async rewrites() {
    return [
      {
        source: '/api/ai/:path*',
        destination: '/api/ai/:path*'
      }
    ]
  },
  
  // Environment variables
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // Build configuration
  output: 'standalone',
  distDir: '.next',
  generateEtags: false,
  poweredByHeader: false,
  compress: true,
  
  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: false,
  }
}

export default nextConfig
```

### TypeScript Configuration (`/Users/<USER>/Projects/own/assistant/foto-fun/tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["components/*"],
      "@/lib/*": ["lib/*"],
      "@/hooks/*": ["hooks/*"],
      "@/store/*": ["store/*"],
      "@/types/*": ["types/*"],
      "@/constants/*": ["constants/*"],
      "@/utils/*": ["lib/utils/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}
```

### Tailwind CSS Configuration (`/Users/<USER>/Projects/own/assistant/foto-fun/tailwind.config.ts`)

```typescript
import type { Config } from 'tailwindcss'

const config: Config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        'fade-in': {
          from: { opacity: '0' },
          to: { opacity: '1' },
        },
        'fade-out': {
          from: { opacity: '1' },
          to: { opacity: '0' },
        },
        'slide-in': {
          from: { transform: 'translateX(-100%)' },
          to: { transform: 'translateX(0)' },
        },
        'slide-out': {
          from: { transform: 'translateX(0)' },
          to: { transform: 'translateX(-100%)' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'fade-in': 'fade-in 0.2s ease-out',
        'fade-out': 'fade-out 0.2s ease-out',
        'slide-in': 'slide-in 0.2s ease-out',
        'slide-out': 'slide-out 0.2s ease-out',
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    require('@tailwindcss/typography'),
  ],
}

export default config
```

## Application Configuration

### Environment Configuration (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/env.ts`)

```typescript
import { z } from 'zod'

// Environment schema validation
const envSchema = z.object({
  // Node environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // API Keys
  REPLICATE_API_TOKEN: z.string().min(1, 'Replicate API token is required'),
  OPENAI_API_KEY: z.string().min(1, 'OpenAI API key is required'),
  
  // Database
  SUPABASE_URL: z.string().url('Invalid Supabase URL'),
  SUPABASE_ANON_KEY: z.string().min(1, 'Supabase anon key is required'),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1, 'Supabase service role key is required'),
  
  // Application
  NEXT_PUBLIC_APP_URL: z.string().url('Invalid app URL'),
  NEXT_PUBLIC_APP_NAME: z.string().default('FotoFun'),
  NEXT_PUBLIC_APP_VERSION: z.string().default('1.0.0'),
  
  // Security
  NEXTAUTH_SECRET: z.string().min(1, 'NextAuth secret is required'),
  NEXTAUTH_URL: z.string().url('Invalid NextAuth URL'),
  
  // Optional
  NEXT_PUBLIC_ANALYTICS_ID: z.string().optional(),
  SENTRY_DSN: z.string().optional(),
  VERCEL_ANALYTICS_ID: z.string().optional(),
})

// Validate environment variables
const validateEnv = () => {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => err.path.join('.')).join(', ')
      throw new Error(`Missing or invalid environment variables: ${missingVars}`)
    }
    throw error
  }
}

export const env = validateEnv()

// Type-safe environment access
export const getEnvVar = (key: keyof typeof env): string => {
  const value = env[key]
  if (!value) {
    throw new Error(`Environment variable ${key} is not set`)
  }
  return value
}

// Environment helpers
export const isDevelopment = env.NODE_ENV === 'development'
export const isProduction = env.NODE_ENV === 'production'
export const isTest = env.NODE_ENV === 'test'
```

### Feature Flags (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/config/features.ts`)

```typescript
// Feature flags configuration
interface FeatureFlags {
  // Core features
  aiChat: boolean
  aiImageGeneration: boolean
  aiBackgroundRemoval: boolean
  aiImageUpscaling: boolean
  aiInpainting: boolean
  
  // Editor features
  advancedSelection: boolean
  layerEffects: boolean
  vectorTools: boolean
  collaborativeEditing: boolean
  
  // UI features
  darkMode: boolean
  advancedPanels: boolean
  customThemes: boolean
  
  // Performance features
  webglRendering: boolean
  workerThreads: boolean
  serviceWorker: boolean
  
  // Development features
  debugMode: boolean
  devTools: boolean
  performanceMonitoring: boolean
  
  // Experimental features
  experimentalFeatures: boolean
  betaFeatures: boolean
}

// Environment-based feature configuration
const developmentFeatures: FeatureFlags = {
  // Core features
  aiChat: true,
  aiImageGeneration: true,
  aiBackgroundRemoval: true,
  aiImageUpscaling: true,
  aiInpainting: true,
  
  // Editor features
  advancedSelection: true,
  layerEffects: true,
  vectorTools: false, // Under development
  collaborativeEditing: false, // Under development
  
  // UI features
  darkMode: true,
  advancedPanels: true,
  customThemes: false, // Under development
  
  // Performance features
  webglRendering: false, // Under development
  workerThreads: true,
  serviceWorker: false,
  
  // Development features
  debugMode: true,
  devTools: true,
  performanceMonitoring: true,
  
  // Experimental features
  experimentalFeatures: true,
  betaFeatures: true,
}

const productionFeatures: FeatureFlags = {
  // Core features
  aiChat: true,
  aiImageGeneration: true,
  aiBackgroundRemoval: true,
  aiImageUpscaling: true,
  aiInpainting: true,
  
  // Editor features
  advancedSelection: true,
  layerEffects: true,
  vectorTools: false,
  collaborativeEditing: false,
  
  // UI features
  darkMode: true,
  advancedPanels: true,
  customThemes: false,
  
  // Performance features
  webglRendering: false,
  workerThreads: true,
  serviceWorker: true,
  
  // Development features
  debugMode: false,
  devTools: false,
  performanceMonitoring: true,
  
  // Experimental features
  experimentalFeatures: false,
  betaFeatures: false,
}

// Get feature flags based on environment
export const getFeatureFlags = (): FeatureFlags => {
  if (isProduction) {
    return productionFeatures
  }
  return developmentFeatures
}

// Feature flag hooks
export const useFeatureFlag = (flag: keyof FeatureFlags): boolean => {
  const flags = getFeatureFlags()
  return flags[flag]
}

// Feature gate component
export function FeatureGate({ 
  feature, 
  children, 
  fallback = null 
}: { 
  feature: keyof FeatureFlags
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  const isEnabled = useFeatureFlag(feature)
  
  if (!isEnabled) {
    return fallback
  }
  
  return children
}
```

### Constants (`/Users/<USER>/Projects/own/assistant/foto-fun/constants/index.ts`)

```typescript
// Application constants
export const APP_NAME = 'FotoFun'
export const APP_DESCRIPTION = 'AI-powered photo editing in your browser'
export const APP_VERSION = '1.0.0'

// Canvas constants
export const DEFAULT_CANVAS_WIDTH = 800
export const DEFAULT_CANVAS_HEIGHT = 600
export const DEFAULT_ZOOM = 100
export const MIN_ZOOM = 25
export const MAX_ZOOM = 500
export const ZOOM_LEVELS = [25, 33, 50, 67, 75, 100, 150, 200, 300, 400, 500]

// Tool constants
export const TOOL_CATEGORIES = [
  'selection',
  'transform',
  'drawing',
  'text',
  'adjustments',
  'filters',
  'ai-native'
] as const

export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif',
  'image/bmp',
  'image/tiff'
] as const

export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
export const MAX_IMAGE_DIMENSION = 4096

// AI constants
export const AI_MODELS = {
  STABLE_DIFFUSION_XL: 'stability-ai/sdxl',
  BACKGROUND_REMOVAL: 'briaai/bria-background-removal',
  IMAGE_UPSCALER: 'google/upscaler',
  INPAINTING: 'stability-ai/sdxl-inpainting'
} as const

export const AI_COST_LIMITS = {
  FREE_TIER: 10.0, // $10 per month
  PRO_TIER: 100.0, // $100 per month
  ENTERPRISE_TIER: 1000.0 // $1000 per month
} as const

// UI constants
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536
} as const

export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
} as const

// Error codes
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR'
} as const

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'fotofun-theme',
  TOOL_PREFERENCES: 'fotofun-tool-preferences',
  CANVAS_SETTINGS: 'fotofun-canvas-settings',
  AI_SETTINGS: 'fotofun-ai-settings',
  RECENT_DOCUMENTS: 'fotofun-recent-documents'
} as const
```

## Database Configuration

### Drizzle Configuration (`/Users/<USER>/Projects/own/assistant/foto-fun/drizzle.config.ts`)

```typescript
import type { Config } from 'drizzle-kit'
import { env } from '@/lib/env'

export default {
  schema: './lib/db/schema.ts',
  out: './lib/db/migrations',
  driver: 'pg',
  dbCredentials: {
    connectionString: env.DATABASE_URL,
  },
  verbose: true,
  strict: true,
} satisfies Config
```

### Database Schema (`/Users/<USER>/Projects/own/assistant/foto-fun/lib/db/schema.ts`)

```typescript
import { pgTable, uuid, text, timestamp, boolean, integer, jsonb } from 'drizzle-orm/pg-core'

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull().unique(),
  name: text('name'),
  avatar: text('avatar'),
  tier: text('tier').default('free'), // free, pro, enterprise
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
})

// Documents table
export const documents = pgTable('documents', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id),
  title: text('title').notNull(),
  description: text('description'),
  canvasData: jsonb('canvas_data'),
  thumbnail: text('thumbnail'),
  width: integer('width').default(800),
  height: integer('height').default(600),
  isPublic: boolean('is_public').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
})

// AI conversations table
export const aiConversations = pgTable('ai_conversations', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id),
  documentId: uuid('document_id').references(() => documents.id),
  messages: jsonb('messages'),
  context: jsonb('context'),
  createdAt: timestamp('created_at').defaultNow(),
})

// AI usage tracking table
export const aiUsage = pgTable('ai_usage', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id),
  service: text('service').notNull(), // replicate, openai
  operation: text('operation').notNull(), // generate-image, chat, etc.
  cost: integer('cost'), // Cost in cents
  tokens: integer('tokens'), // For OpenAI
  createdAt: timestamp('created_at').defaultNow(),
})

// User preferences table
export const userPreferences = pgTable('user_preferences', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id),
  theme: text('theme').default('system'),
  canvasSettings: jsonb('canvas_settings'),
  toolPreferences: jsonb('tool_preferences'),
  aiSettings: jsonb('ai_settings'),
  updatedAt: timestamp('updated_at').defaultNow(),
})
```

## API Configuration

### Replicate Configuration

```typescript
// File: lib/ai/server/replicateClient.ts
import Replicate from 'replicate'
import { env } from '@/lib/env'

const replicate = new Replicate({
  auth: env.REPLICATE_API_TOKEN,
})

// Model configurations
export const REPLICATE_MODELS = {
  STABLE_DIFFUSION_XL: 'stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b',
  BACKGROUND_REMOVAL: 'briaai/bria-background-removal:3619b5d8c1e83f3b4e8f3e6c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c',
  IMAGE_UPSCALER: 'google/upscaler:4739e8b8f3b3d7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c3e7c1b5c',
  INPAINTING: 'stability-ai/sdxl-inpainting:7c4768b7ddc8f5c3b7f8e8b7ddc8f5c3b7f8e8b7ddc8f5c3b7f8e8b7ddc8f5c3b7f8'
} as const

// Cost configuration
export const REPLICATE_COSTS = {
  [REPLICATE_MODELS.STABLE_DIFFUSION_XL]: 0.012,
  [REPLICATE_MODELS.BACKGROUND_REMOVAL]: 0.003,
  [REPLICATE_MODELS.IMAGE_UPSCALER]: 0.005,
  [REPLICATE_MODELS.INPAINTING]: 0.008,
} as const

// Rate limiting configuration
export const REPLICATE_RATE_LIMITS = {
  [REPLICATE_MODELS.STABLE_DIFFUSION_XL]: { requests: 10, window: 60000 },
  [REPLICATE_MODELS.BACKGROUND_REMOVAL]: { requests: 20, window: 60000 },
  [REPLICATE_MODELS.IMAGE_UPSCALER]: { requests: 15, window: 60000 },
  [REPLICATE_MODELS.INPAINTING]: { requests: 10, window: 60000 },
} as const
```

### OpenAI Configuration

```typescript
// File: lib/ai/providers.ts
import { openai } from 'ai'
import { env } from '@/lib/env'

export const openaiClient = openai({
  apiKey: env.OPENAI_API_KEY,
  organization: env.OPENAI_ORGANIZATION, // Optional
})

// Model configurations
export const OPENAI_MODELS = {
  GPT_4O: 'gpt-4o',
  GPT_4O_MINI: 'gpt-4o-mini',
  GPT_4_TURBO: 'gpt-4-turbo',
  GPT_3_5_TURBO: 'gpt-3.5-turbo',
} as const

// Cost configuration (per 1K tokens)
export const OPENAI_COSTS = {
  [OPENAI_MODELS.GPT_4O]: { input: 0.005, output: 0.015 },
  [OPENAI_MODELS.GPT_4O_MINI]: { input: 0.00015, output: 0.0006 },
  [OPENAI_MODELS.GPT_4_TURBO]: { input: 0.01, output: 0.03 },
  [OPENAI_MODELS.GPT_3_5_TURBO]: { input: 0.0015, output: 0.002 },
} as const

// Token limits
export const OPENAI_LIMITS = {
  [OPENAI_MODELS.GPT_4O]: 128000,
  [OPENAI_MODELS.GPT_4O_MINI]: 128000,
  [OPENAI_MODELS.GPT_4_TURBO]: 128000,
  [OPENAI_MODELS.GPT_3_5_TURBO]: 4096,
} as const
```

## Security Configuration

### Content Security Policy

```typescript
// File: lib/security/csp.ts
export const cspConfig = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'",
    'https://replicate.com',
    'https://api.openai.com',
    'https://vercel.live',
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'",
    'https://fonts.googleapis.com',
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https://replicate.delivery',
    'https://replicate.com',
    'https://*.supabase.co',
    'https://vercel.com',
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
  ],
  'connect-src': [
    "'self'",
    'https://api.replicate.com',
    'https://api.openai.com',
    'https://*.supabase.co',
    'wss://*.supabase.co',
    'https://vercel.live',
  ],
  'frame-src': [
    "'self'",
    'https://vercel.live',
  ],
  'worker-src': [
    "'self'",
    'blob:',
  ],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
  'upgrade-insecure-requests': [],
}

export const generateCSP = () => {
  return Object.entries(cspConfig)
    .map(([key, values]) => `${key} ${values.join(' ')}`)
    .join('; ')
}
```

### Rate Limiting Configuration

```typescript
// File: lib/security/rateLimit.ts
export const rateLimitConfig = {
  // API endpoints
  '/api/ai/chat': {
    requests: 100,
    window: 60000, // 1 minute
  },
  '/api/ai/replicate/*': {
    requests: 10,
    window: 60000, // 1 minute
  },
  '/api/auth/*': {
    requests: 5,
    window: 60000, // 1 minute
  },
  
  // Default limits
  default: {
    requests: 200,
    window: 60000, // 1 minute
  },
}

// User tier limits
export const userTierLimits = {
  free: {
    aiRequests: 50,
    imageGeneration: 5,
    storageLimit: 100 * 1024 * 1024, // 100MB
  },
  pro: {
    aiRequests: 500,
    imageGeneration: 50,
    storageLimit: 1024 * 1024 * 1024, // 1GB
  },
  enterprise: {
    aiRequests: 5000,
    imageGeneration: 500,
    storageLimit: 10 * 1024 * 1024 * 1024, // 10GB
  },
}
```

## Development Configuration

### Development Scripts

```json
{
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "typecheck": "tsc --noEmit",
    "test": "vitest",
    "test:watch": "vitest --watch",
    "test:ui": "vitest --ui",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "bun run lib/db/migrate.ts",
    "db:push": "drizzle-kit push",
    "db:studio": "drizzle-kit studio",
    "db:seed": "bun run lib/db/seed.ts",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "analyze": "ANALYZE=true next build",
    "clean": "rm -rf .next dist coverage",
    "postinstall": "husky install"
  }
}
```

### Git Hooks Configuration

```bash
#!/bin/sh
# File: .husky/pre-commit
. "$(dirname "$0")/_/husky.sh"

echo "Running pre-commit hooks..."

# Type checking
echo "🔍 Type checking..."
npm run typecheck

# Linting
echo "🔧 Linting..."
npm run lint

# Testing
echo "🧪 Running tests..."
npm run test

# Format checking
echo "💅 Checking formatting..."
npm run format:check

echo "✅ Pre-commit hooks passed!"
```

### Environment Validation

```typescript
// File: lib/config/validation.ts
import { z } from 'zod'

const configSchema = z.object({
  // Required API keys
  replicateApiToken: z.string().min(1),
  openaiApiKey: z.string().min(1),
  
  // Database
  supabaseUrl: z.string().url(),
  supabaseAnonKey: z.string().min(1),
  
  // Application
  appUrl: z.string().url(),
  appName: z.string().min(1),
  
  // Optional
  analyticsId: z.string().optional(),
  sentryDsn: z.string().optional(),
})

export const validateConfig = () => {
  const config = {
    replicateApiToken: process.env.REPLICATE_API_TOKEN,
    openaiApiKey: process.env.OPENAI_API_KEY,
    supabaseUrl: process.env.SUPABASE_URL,
    supabaseAnonKey: process.env.SUPABASE_ANON_KEY,
    appUrl: process.env.NEXT_PUBLIC_APP_URL,
    appName: process.env.NEXT_PUBLIC_APP_NAME,
    analyticsId: process.env.NEXT_PUBLIC_ANALYTICS_ID,
    sentryDsn: process.env.SENTRY_DSN,
  }
  
  try {
    return configSchema.parse(config)
  } catch (error) {
    console.error('Configuration validation failed:', error)
    process.exit(1)
  }
}
```

## Deployment Configuration

### Docker Configuration

```dockerfile
# File: Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json bun.lockb ./
RUN npm install -g bun
RUN bun install --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the public folder
COPY --from=builder /app/public ./public

# Copy the build output
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### Vercel Configuration

```json
{
  "version": 2,
  "buildCommand": "bun run build",
  "devCommand": "bun run dev",
  "installCommand": "bun install",
  "framework": "nextjs",
  "outputDirectory": ".next",
  "functions": {
    "app/api/ai/chat/route.ts": {
      "maxDuration": 30
    },
    "app/api/ai/replicate/*/route.ts": {
      "maxDuration": 60
    }
  },
  "env": {
    "REPLICATE_API_TOKEN": "@replicate-api-token",
    "OPENAI_API_KEY": "@openai-api-key",
    "SUPABASE_URL": "@supabase-url",
    "SUPABASE_ANON_KEY": "@supabase-anon-key"
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "https://fotofun.app"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        }
      ]
    }
  ]
}
```

## Configuration Management Best Practices

### 1. Environment Separation
- **Development**: Local development with debug features
- **Staging**: Production-like environment for testing
- **Production**: Optimized for performance and security

### 2. Secret Management
- Use environment variables for sensitive data
- Never commit secrets to version control
- Rotate API keys regularly
- Use secret management services in production

### 3. Configuration Validation
- Validate all configuration at startup
- Fail fast on invalid configuration
- Provide clear error messages
- Use TypeScript for type safety

### 4. Performance Optimization
- Cache configuration values
- Use environment-specific optimizations
- Monitor configuration impact
- Optimize build settings

This comprehensive configuration system ensures FotoFun runs securely and efficiently across all environments while maintaining flexibility for development and deployment.