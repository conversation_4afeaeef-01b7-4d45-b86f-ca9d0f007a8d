# FotoFun - Deployment Architecture

## Overview

FotoFun's deployment architecture is designed for **scalability**, **reliability**, and **global performance**. Built on modern cloud infrastructure with automated CI/CD pipelines, the deployment system supports multiple environments and provides robust monitoring and rollback capabilities.

## Deployment Stack

### Primary Platform: Vercel
- **Framework**: Next.js 15 App Router
- **Runtime**: Node.js 18+ / Bun
- **Edge Runtime**: Vercel Edge Functions
- **Global CDN**: Vercel Edge Network
- **Database**: Supabase (PostgreSQL)
- **Storage**: Vercel Blob Storage
- **Analytics**: Vercel Analytics & Speed Insights

### Infrastructure Components

```mermaid
graph TB
    subgraph "Client Layer"
        Browser[Web Browser]
        Mobile[Mobile Browser]
        PWA[Progressive Web App]
    end
    
    subgraph "Edge Network"
        CDN[Vercel CDN]
        Edge[Edge Functions]
        Cache[Edge Cache]
    end
    
    subgraph "Application Layer"
        NextApp[Next.js App]
        API[API Routes]
        SSR[Server-Side Rendering]
        Static[Static Generation]
    end
    
    subgraph "External Services"
        Supabase[Supabase DB]
        Replicate[Replicate AI]
        OpenAI[OpenAI API]
        Storage[Blob Storage]
    end
    
    subgraph "Monitoring"
        Vercel_Analytics[Vercel Analytics]
        Sentry[Error Tracking]
        Performance[Performance Monitoring]
    end
    
    Browser --> CDN
    Mobile --> CDN
    PWA --> CDN
    CDN --> Edge
    CDN --> Cache
    Edge --> NextApp
    Cache --> Static
    NextApp --> API
    NextApp --> SSR
    API --> Supabase
    API --> Replicate
    API --> OpenAI
    API --> Storage
    NextApp --> Vercel_Analytics
    NextApp --> Sentry
    NextApp --> Performance
```

## Build Process

### Build Configuration (`/Users/<USER>/Projects/own/assistant/foto-fun/next.config.js`)

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // App Router configuration
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['fabric'],
    esmExternals: 'loose'
  },
  
  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'replicate.delivery',
        port: '',
        pathname: '/**'
      },
      {
        protocol: 'https',
        hostname: 'supabase.co',
        port: '',
        pathname: '/**'
      }
    ],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 3600
  },
  
  // Webpack configuration
  webpack: (config, { isServer }) => {
    // Handle fabric.js for SSR
    if (isServer) {
      config.externals.push('fabric')
    }
    
    // Canvas polyfill for Node.js
    config.resolve.fallback = {
      ...config.resolve.fallback,
      canvas: false,
      encoding: false
    }
    
    return config
  },
  
  // Bundle analyzer
  bundleAnalyzer: {
    enabled: process.env.ANALYZE === 'true'
  },
  
  // Compression
  compress: true,
  
  // Security headers
  headers: async () => [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'X-Frame-Options',
          value: 'DENY'
        },
        {
          key: 'X-Content-Type-Options',
          value: 'nosniff'
        },
        {
          key: 'Referrer-Policy',
          value: 'strict-origin-when-cross-origin'
        },
        {
          key: 'Permissions-Policy',
          value: 'camera=(), microphone=(), geolocation=()'
        }
      ]
    }
  ],
  
  // Environment variables
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
    BUILD_TIME: new Date().toISOString()
  },
  
  // Output configuration
  output: 'standalone',
  
  // Experimental features
  experimental: {
    serverComponentsExternalPackages: ['fabric'],
    optimizeCss: true,
    nextScriptWorkers: true,
    gzipSize: true
  }
}

module.exports = nextConfig
```

### Build Scripts (`/Users/<USER>/Projects/own/assistant/foto-fun/package.json`)

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "analyze": "ANALYZE=true npm run build",
    "preview": "next build && next start",
    "deploy": "vercel --prod",
    "deploy:staging": "vercel",
    "postbuild": "next-sitemap"
  }
}
```

### Build Optimization

```typescript
// Build-time optimizations
const buildOptimizations = {
  // Bundle splitting
  splitChunks: {
    chunks: 'all',
    cacheGroups: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        chunks: 'all'
      },
      fabric: {
        test: /[\\/]node_modules[\\/]fabric[\\/]/,
        name: 'fabric',
        chunks: 'all'
      },
      ai: {
        test: /[\\/]lib[\\/]ai[\\/]/,
        name: 'ai',
        chunks: 'all'
      }
    }
  },
  
  // Tree shaking
  optimization: {
    usedExports: true,
    sideEffects: false,
    moduleIds: 'deterministic',
    chunkIds: 'deterministic'
  },
  
  // Code splitting
  dynamicImports: [
    'fabric' // Load fabric.js only when needed
  ]
}
```

## Environment Configuration

### Development Environment

```bash
# .env.local
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=FotoFun Dev
NEXT_PUBLIC_DEBUG_MODE=true

# API Keys (Development)
OPENAI_API_KEY=sk-dev-...
REPLICATE_API_TOKEN=r8_dev-...

# Database (Local/Staging)
SUPABASE_URL=https://dev-project.supabase.co
SUPABASE_ANON_KEY=eyJ...dev...
SUPABASE_SERVICE_ROLE_KEY=eyJ...dev...

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=dev-secret-key

# Analytics (Disabled in dev)
NEXT_PUBLIC_ANALYTICS_ENABLED=false
```

### Staging Environment

```bash
# .env.staging
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://staging.fotofun.app
NEXT_PUBLIC_APP_NAME=FotoFun Staging
NEXT_PUBLIC_DEBUG_MODE=false

# API Keys (Staging)
OPENAI_API_KEY=sk-staging-...
REPLICATE_API_TOKEN=r8_staging-...

# Database (Staging)
SUPABASE_URL=https://staging-project.supabase.co
SUPABASE_ANON_KEY=eyJ...staging...
SUPABASE_SERVICE_ROLE_KEY=eyJ...staging...

# Authentication
NEXTAUTH_URL=https://staging.fotofun.app
NEXTAUTH_SECRET=staging-secret-key

# Analytics (Limited in staging)
NEXT_PUBLIC_ANALYTICS_ENABLED=true
VERCEL_ANALYTICS_ID=staging-analytics-id
```

### Production Environment

```bash
# .env.production
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://fotofun.app
NEXT_PUBLIC_APP_NAME=FotoFun
NEXT_PUBLIC_DEBUG_MODE=false

# API Keys (Production)
OPENAI_API_KEY=sk-prod-...
REPLICATE_API_TOKEN=r8_prod-...

# Database (Production)
SUPABASE_URL=https://prod-project.supabase.co
SUPABASE_ANON_KEY=eyJ...prod...
SUPABASE_SERVICE_ROLE_KEY=eyJ...prod...

# Authentication
NEXTAUTH_URL=https://fotofun.app
NEXTAUTH_SECRET=production-secret-key

# Analytics (Full in production)
NEXT_PUBLIC_ANALYTICS_ENABLED=true
VERCEL_ANALYTICS_ID=prod-analytics-id
SENTRY_DSN=https://...@sentry.io/...
```

## CI/CD Pipeline

### GitHub Actions Workflow (`.github/workflows/deploy.yml`)

```yaml
name: Deploy to Vercel

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      
      - name: Install dependencies
        run: bun install
      
      - name: Run type check
        run: bun run type-check
      
      - name: Run linting
        run: bun run lint
      
      - name: Run tests
        run: bun run test
        env:
          NODE_ENV: test
  
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      
      - name: Install dependencies
        run: bun install
      
      - name: Build application
        run: bun run build
        env:
          NODE_ENV: production
          NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          REPLICATE_API_TOKEN: ${{ secrets.REPLICATE_API_TOKEN }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: .next/
  
  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          scope: ${{ secrets.VERCEL_TEAM_ID }}
  
  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./
          scope: ${{ secrets.VERCEL_TEAM_ID }}
```

### Deployment Hooks

```typescript
// File: lib/deployment/hooks.ts
interface DeploymentHook {
  name: string
  execute: (context: DeploymentContext) => Promise<void>
}

const deploymentHooks: DeploymentHook[] = [
  {
    name: 'pre-build',
    execute: async (context) => {
      // Validate environment variables
      await validateEnvironment(context.env)
      
      // Check external service availability
      await checkExternalServices()
      
      // Generate build metadata
      await generateBuildMetadata(context)
    }
  },
  
  {
    name: 'post-build',
    execute: async (context) => {
      // Generate sitemap
      await generateSitemap()
      
      // Optimize images
      await optimizeImages()
      
      // Update service worker
      await updateServiceWorker(context)
    }
  },
  
  {
    name: 'pre-deploy',
    execute: async (context) => {
      // Database migrations
      await runMigrations(context.env)
      
      // Warm up caches
      await warmupCaches()
      
      // Health checks
      await performHealthChecks(context)
    }
  },
  
  {
    name: 'post-deploy',
    execute: async (context) => {
      // Invalidate CDN cache
      await invalidateCDNCache()
      
      // Update monitoring
      await updateMonitoring(context)
      
      // Send deployment notifications
      await sendDeploymentNotifications(context)
    }
  }
]

export const executeDeploymentHooks = async (
  stage: string,
  context: DeploymentContext
): Promise<void> => {
  const hooks = deploymentHooks.filter(hook => hook.name === stage)
  
  for (const hook of hooks) {
    try {
      await hook.execute(context)
      console.log(`✅ Deployment hook '${hook.name}' completed successfully`)
    } catch (error) {
      console.error(`❌ Deployment hook '${hook.name}' failed:`, error)
      throw error
    }
  }
}
```

## Database Deployment

### Supabase Configuration

```sql
-- Database schema deployment
-- File: supabase/migrations/001_initial_schema.sql

-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  subscription_tier VARCHAR(50) DEFAULT 'free',
  usage_credits INTEGER DEFAULT 100
);

-- Documents table
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  canvas_data JSONB,
  thumbnail_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_public BOOLEAN DEFAULT FALSE,
  version INTEGER DEFAULT 1
);

-- Usage tracking
CREATE TABLE usage_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  action VARCHAR(100) NOT NULL,
  cost_credits INTEGER DEFAULT 0,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_created_at ON documents(created_at);
CREATE INDEX idx_usage_logs_user_id ON usage_logs(user_id);
CREATE INDEX idx_usage_logs_created_at ON usage_logs(created_at);

-- Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_logs ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own documents" ON documents
  FOR SELECT USING (auth.uid() = user_id OR is_public = true);

CREATE POLICY "Users can create own documents" ON documents
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own documents" ON documents
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own documents" ON documents
  FOR DELETE USING (auth.uid() = user_id);
```

### Migration Strategy

```typescript
// File: lib/database/migrations.ts
interface Migration {
  version: number
  description: string
  up: (client: SupabaseClient) => Promise<void>
  down: (client: SupabaseClient) => Promise<void>
}

const migrations: Migration[] = [
  {
    version: 1,
    description: 'Initial schema',
    up: async (client) => {
      // Apply initial schema
      await client.rpc('apply_migration', {
        migration_sql: readFileSync('./supabase/migrations/001_initial_schema.sql', 'utf8')
      })
    },
    down: async (client) => {
      // Rollback initial schema
      await client.rpc('rollback_migration', { version: 1 })
    }
  },
  
  {
    version: 2,
    description: 'Add collaboration features',
    up: async (client) => {
      await client.rpc('apply_migration', {
        migration_sql: readFileSync('./supabase/migrations/002_collaboration.sql', 'utf8')
      })
    },
    down: async (client) => {
      await client.rpc('rollback_migration', { version: 2 })
    }
  }
]

export const runMigrations = async (env: string): Promise<void> => {
  const client = createSupabaseClient(env)
  
  // Get current version
  const { data: currentVersion } = await client
    .from('schema_version')
    .select('version')
    .single()
  
  const version = currentVersion?.version || 0
  
  // Run pending migrations
  const pendingMigrations = migrations.filter(m => m.version > version)
  
  for (const migration of pendingMigrations) {
    try {
      console.log(`Running migration ${migration.version}: ${migration.description}`)
      await migration.up(client)
      
      // Update version
      await client
        .from('schema_version')
        .upsert({ version: migration.version })
        
      console.log(`✅ Migration ${migration.version} completed`)
    } catch (error) {
      console.error(`❌ Migration ${migration.version} failed:`, error)
      throw error
    }
  }
}
```

## Monitoring & Observability

### Error Tracking (Sentry)

```typescript
// File: lib/monitoring/sentry.ts
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  tracesSampleRate: 1.0,
  environment: process.env.NODE_ENV,
  
  beforeSend(event) {
    // Filter out non-critical errors
    if (event.exception) {
      const error = event.exception.values?.[0]
      if (error?.type === 'ChunkLoadError') {
        return null // Ignore chunk load errors
      }
    }
    
    return event
  },
  
  integrations: [
    new Sentry.Integrations.Http({ tracing: true }),
    new Sentry.Integrations.OnUncaughtException(),
    new Sentry.Integrations.OnUnhandledRejection()
  ]
})

// Custom error boundary
export const withSentryErrorBoundary = (Component: React.ComponentType) => {
  return Sentry.withErrorBoundary(Component, {
    fallback: ({ error, resetError }) => (
      <div className="error-boundary">
        <h2>Something went wrong</h2>
        <p>{error.message}</p>
        <button onClick={resetError}>Try again</button>
      </div>
    ),
    beforeCapture: (scope, error) => {
      scope.setTag('component', Component.name)
      scope.setLevel('error')
    }
  })
}
```

### Performance Monitoring

```typescript
// File: lib/monitoring/performance.ts
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'

// Custom performance tracking
export const trackPerformance = (name: string, duration: number) => {
  // Send to Vercel Analytics
  Analytics.track('performance', {
    name,
    duration,
    timestamp: Date.now()
  })
  
  // Send to custom analytics
  if (typeof window !== 'undefined') {
    window.gtag?.('event', 'performance', {
      event_category: 'performance',
      event_label: name,
      value: Math.round(duration)
    })
  }
}

// Web Vitals tracking
export const trackWebVitals = (metric: any) => {
  const { name, value, id } = metric
  
  Analytics.track('web-vital', {
    name,
    value,
    id,
    timestamp: Date.now()
  })
}

// Canvas performance tracking
export const trackCanvasPerformance = (operation: string, duration: number) => {
  trackPerformance(`canvas-${operation}`, duration)
  
  // Additional canvas-specific metrics
  if (operation === 'render') {
    const fps = Math.round(1000 / duration)
    Analytics.track('canvas-fps', { fps })
  }
}
```

### Health Checks

```typescript
// File: app/api/health/route.ts
export async function GET() {
  const checks = {
    database: await checkDatabase(),
    openai: await checkOpenAI(),
    replicate: await checkReplicate(),
    storage: await checkStorage()
  }
  
  const allHealthy = Object.values(checks).every(check => check.healthy)
  
  return Response.json(
    {
      status: allHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks
    },
    { status: allHealthy ? 200 : 503 }
  )
}

async function checkDatabase() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
    
    return {
      healthy: !error,
      latency: Date.now() - start,
      error: error?.message
    }
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

async function checkOpenAI() {
  try {
    const start = Date.now()
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: { Authorization: `Bearer ${process.env.OPENAI_API_KEY}` }
    })
    
    return {
      healthy: response.ok,
      latency: Date.now() - start,
      status: response.status
    }
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
```

## Security Considerations

### Environment Security

```typescript
// File: lib/security/environment.ts
const requiredEnvVars = [
  'NEXTAUTH_SECRET',
  'OPENAI_API_KEY',
  'REPLICATE_API_TOKEN',
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY'
]

export const validateEnvironment = (env: Record<string, string | undefined>) => {
  const missing = requiredEnvVars.filter(key => !env[key])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
  
  // Validate API key formats
  if (env.OPENAI_API_KEY && !env.OPENAI_API_KEY.startsWith('sk-')) {
    throw new Error('Invalid OpenAI API key format')
  }
  
  if (env.REPLICATE_API_TOKEN && !env.REPLICATE_API_TOKEN.startsWith('r8_')) {
    throw new Error('Invalid Replicate API token format')
  }
  
  console.log('✅ Environment validation passed')
}
```

### Content Security Policy

```typescript
// File: lib/security/csp.ts
export const generateCSP = (env: string) => {
  const isProduction = env === 'production'
  
  return {
    'default-src': "'self'",
    'script-src': [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'",
      'https://vercel.live',
      ...(isProduction ? [] : ["'unsafe-inline'"])
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'",
      'https://fonts.googleapis.com'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https://replicate.delivery',
      'https://supabase.co',
      'https://vercel.com'
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com'
    ],
    'connect-src': [
      "'self'",
      'https://api.openai.com',
      'https://api.replicate.com',
      'https://supabase.co',
      'https://vercel.com'
    ],
    'worker-src': [
      "'self'",
      'blob:'
    ]
  }
}
```

## Rollback Strategy

### Automated Rollback

```typescript
// File: lib/deployment/rollback.ts
interface RollbackStrategy {
  trigger: 'error-rate' | 'response-time' | 'manual'
  threshold: number
  action: 'revert' | 'canary' | 'blue-green'
}

export const rollbackStrategies: RollbackStrategy[] = [
  {
    trigger: 'error-rate',
    threshold: 0.05, // 5% error rate
    action: 'revert'
  },
  {
    trigger: 'response-time',
    threshold: 5000, // 5 second response time
    action: 'canary'
  }
]

export const executeRollback = async (
  strategy: RollbackStrategy,
  deploymentId: string
): Promise<void> => {
  console.log(`Initiating rollback for deployment ${deploymentId}`)
  
  switch (strategy.action) {
    case 'revert':
      await revertDeployment(deploymentId)
      break
    case 'canary':
      await initiateCanaryRollback(deploymentId)
      break
    case 'blue-green':
      await blueGreenRollback(deploymentId)
      break
  }
  
  // Notify team
  await sendRollbackNotification(deploymentId, strategy)
}

const revertDeployment = async (deploymentId: string): Promise<void> => {
  // Use Vercel API to revert to previous deployment
  const response = await fetch(`https://api.vercel.com/v9/deployments/${deploymentId}/cancel`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${process.env.VERCEL_TOKEN}`,
      'Content-Type': 'application/json'
    }
  })
  
  if (!response.ok) {
    throw new Error(`Failed to revert deployment: ${response.statusText}`)
  }
  
  console.log(`✅ Deployment ${deploymentId} reverted successfully`)
}
```

## Performance Optimization

### Static Generation

```typescript
// File: app/page.tsx
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'FotoFun - AI-Powered Photo Editor',
  description: 'Professional photo editing with AI assistance',
  openGraph: {
    title: 'FotoFun - AI-Powered Photo Editor',
    description: 'Professional photo editing with AI assistance',
    url: 'https://fotofun.app',
    siteName: 'FotoFun',
    images: [
      {
        url: 'https://fotofun.app/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'FotoFun Preview'
      }
    ]
  }
}

// Static generation for marketing pages
export async function generateStaticParams() {
  return [
    { slug: 'features' },
    { slug: 'pricing' },
    { slug: 'about' }
  ]
}
```

### Edge Functions

```typescript
// File: app/api/edge-example/route.ts
import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'edge'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q')
  
  if (!query) {
    return NextResponse.json({ error: 'Query required' }, { status: 400 })
  }
  
  // Process at the edge for low latency
  const result = await processQuery(query)
  
  return NextResponse.json(result)
}

const processQuery = async (query: string) => {
  // Lightweight processing suitable for edge runtime
  return {
    processed: true,
    query,
    timestamp: new Date().toISOString()
  }
}
```

## Disaster Recovery

### Backup Strategy

```typescript
// File: lib/backup/strategy.ts
interface BackupStrategy {
  type: 'database' | 'storage' | 'configuration'
  frequency: 'hourly' | 'daily' | 'weekly'
  retention: number // days
  encryption: boolean
}

export const backupStrategies: BackupStrategy[] = [
  {
    type: 'database',
    frequency: 'hourly',
    retention: 7,
    encryption: true
  },
  {
    type: 'storage',
    frequency: 'daily',
    retention: 30,
    encryption: true
  },
  {
    type: 'configuration',
    frequency: 'weekly',
    retention: 90,
    encryption: false
  }
]

export const executeBackup = async (strategy: BackupStrategy): Promise<void> => {
  console.log(`Starting ${strategy.type} backup`)
  
  switch (strategy.type) {
    case 'database':
      await backupDatabase(strategy)
      break
    case 'storage':
      await backupStorage(strategy)
      break
    case 'configuration':
      await backupConfiguration(strategy)
      break
  }
  
  console.log(`✅ ${strategy.type} backup completed`)
}
```

### Recovery Procedures

```typescript
// File: lib/recovery/procedures.ts
export const recoveryProcedures = {
  database: async (backupId: string) => {
    // Restore database from backup
    console.log(`Restoring database from backup ${backupId}`)
    
    // 1. Create new database instance
    const newInstance = await createDatabaseInstance()
    
    // 2. Restore data
    await restoreFromBackup(newInstance, backupId)
    
    // 3. Update connection strings
    await updateConnectionStrings(newInstance)
    
    // 4. Verify integrity
    await verifyDatabaseIntegrity(newInstance)
    
    console.log('✅ Database recovery completed')
  },
  
  storage: async (backupId: string) => {
    // Restore storage from backup
    console.log(`Restoring storage from backup ${backupId}`)
    
    // Implementation specific to storage provider
    await restoreStorageFromBackup(backupId)
    
    console.log('✅ Storage recovery completed')
  },
  
  fullSystem: async (timestamp: string) => {
    // Full system recovery
    console.log(`Performing full system recovery to ${timestamp}`)
    
    // 1. Restore database
    await recoveryProcedures.database(`db-${timestamp}`)
    
    // 2. Restore storage
    await recoveryProcedures.storage(`storage-${timestamp}`)
    
    // 3. Redeploy application
    await redeployApplication(timestamp)
    
    // 4. Verify all systems
    await verifySystemHealth()
    
    console.log('✅ Full system recovery completed')
  }
}
```

## Deployment Benefits

### 1. Scalability
- **Global CDN**: Fast content delivery worldwide
- **Edge computing**: Low-latency processing
- **Auto-scaling**: Handles traffic spikes automatically
- **Database scaling**: Supabase handles connection pooling

### 2. Reliability
- **Zero-downtime deployments**: Smooth updates
- **Automatic rollbacks**: Quick recovery from issues
- **Health monitoring**: Proactive issue detection
- **Redundancy**: Multiple availability zones

### 3. Performance
- **Static generation**: Pre-rendered pages
- **Edge caching**: Reduced server load
- **Image optimization**: Automatic format conversion
- **Bundle optimization**: Efficient code splitting

### 4. Security
- **Environment isolation**: Secure configuration
- **HTTPS everywhere**: End-to-end encryption
- **Security headers**: Protection against attacks
- **Regular updates**: Automated security patches

### 5. Developer Experience
- **Preview deployments**: Review changes before merge
- **Instant rollbacks**: Quick recovery from issues
- **Comprehensive monitoring**: Full visibility
- **Automated testing**: Continuous integration

This deployment architecture ensures FotoFun can scale globally while maintaining high performance, security, and reliability standards.