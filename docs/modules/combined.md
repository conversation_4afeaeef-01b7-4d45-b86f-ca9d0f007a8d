# Tech Stack Analysis - Assistant Projects

This document provides a comprehensive overview of the technology stacks used across all projects in the assistant workspace.

## Project Overview

The workspace contains 8 distinct projects, each serving different purposes:

1. **agent-inbox** - AI-powered agent communication platform
2. **foto-fun** - AI-enhanced photo editing application
3. **gen-ui-computer-use** - Computer use automation with UI generation
4. **open-canvas** - Collaborative AI canvas for content creation
5. **social-media-agent** - Automated social media management system
6. **vcode** - Modern IDE with AI integration
7. **vibe-kanban** - AI coding agent orchestration and task management platform
8. **langflow** - Visual AI workflow builder and deployment platform

## Detailed Tech Stack Breakdown

### 1. **agent-inbox**
**Purpose**: AI-powered agent communication and collaboration platform

- **Framework**: Next.js 14 (React 18)
- **UI Library**: 
  - Radix UI components (@radix-ui/react-*)
  - Tailwind CSS for styling
  - @assistant-ui/react (AI chat interface)
  - @blocknote (rich text editor)
  - CodeMirror (code editor with multi-language support)
  - Framer Motion for animations
- **AI Libraries**: 
  - LangChain ecosystem (@langchain/anthropic, @langchain/openai, @langchain/langgraph)
  - @langchain/community for extended integrations
  - @langchain/google-genai for Google AI integration
- **Database**: Supabase (@supabase/supabase-js, @supabase/ssr)
- **State Management**: Built-in React state
- **Package Manager**: Yarn
- **Additional Tools**:
  - @vercel/kv for caching
  - React Syntax Highlighter
  - React Markdown with math support

### 2. **foto-fun**
**Purpose**: AI-enhanced photo editing application with professional tools

- **Framework**: Next.js 15 (React 19) with Turbopack
- **UI Library**:
  - Radix UI components (comprehensive set)
  - Tailwind CSS 4
  - Fabric.js 6 (canvas manipulation and editing)
  - PIXI.js (@pixi/react) for advanced graphics rendering
- **AI Libraries**:
  - @ai-sdk/openai and @ai-sdk/react (Vercel AI SDK v5 beta)
  - Replicate for AI image processing and generation
- **Database**: 
  - Supabase for authentication and data
  - Drizzle ORM with PostgreSQL
- **State Management**: Zustand
- **Package Manager**: Bun
- **Additional Tools**:
  - File-saver for downloads
  - Next Themes for dark/light mode
  - Testing with Vitest and Testing Library

### 3. **gen-ui-computer-use**
**Purpose**: Computer use automation with AI-generated user interfaces

- **Framework**: Next.js 15 (React 19)
- **UI Library**:
  - Radix UI components
  - Tailwind CSS 4
  - Framer Motion for animations
  - Recharts for data visualization
  - Sonner for notifications
- **AI Libraries**:
  - LangChain (@langchain/langgraph, @langchain/openai)
  - @langchain/langgraph-cua (Computer Use Agent)
  - @langchain/langgraph-sdk for API integration
  - Scrapybara for web scraping automation
- **Database**: Supabase
- **State Management**: URL-based with nuqs
- **Package Manager**: pnpm
- **Additional Tools**:
  - React Markdown with KaTeX math support
  - React Syntax Highlighter
  - ESBuild with Tailwind plugin

### 4. **open-canvas** (Monorepo)
**Purpose**: Collaborative AI canvas for content creation and editing

- **Framework**: Next.js 14 (React 18) - web application
- **UI Library**:
  - Radix UI components (extensive set)
  - Tailwind CSS
  - @assistant-ui/react for AI chat
  - @blocknote (rich text editor with Mantine integration)
  - CodeMirror for code editing (multi-language)
  - React Resizable Panels
- **AI Libraries**:
  - LangChain (@langchain/anthropic, @langchain/openai, @langchain/langgraph)
  - @ai-sdk/openai, @ai-sdk/react (Vercel AI SDK)
  - @mendable/firecrawl-js for web crawling
  - Multiple AI provider support (Groq, Ollama, Google)
- **Database**: Supabase with SSR support
- **State Management**: Zustand
- **Package Manager**: pnpm
- **Build Tools**: 
  - Turbo (monorepo management)
  - Workspace architecture with shared packages
- **Additional Tools**:
  - FFmpeg for media processing
  - PDF parsing capabilities
  - Comprehensive evaluation suite with Vitest

### 5. **social-media-agent**
**Purpose**: Automated social media management and content generation

- **Framework**: LangGraph (Node.js/TypeScript backend service)
- **UI Library**: None (headless backend service)
- **AI Libraries**:
  - LangChain (@langchain/anthropic, @langchain/openai, @langchain/langgraph)
  - @langchain/langgraph-sdk for workflow management
  - @arcadeai/arcadejs for enhanced AI capabilities
- **External API Integrations**:
  - Twitter API v2 for Twitter automation
  - Reddit API (snoowrap) for Reddit management
  - YouTube API (@googleapis/youtube) for video content
  - Slack API (@slack/web-api) for team communication
  - GitHub API (@octokit/rest) for code integration
  - @mendable/firecrawl-js for web content scraping
- **Database**: Supabase for data persistence
- **Automation Tools**: 
  - Playwright for web automation and testing
  - Express.js for authentication server
- **Package Manager**: Yarn
- **Additional Tools**:
  - Cheerio for HTML parsing
  - XML2JS for feed processing
  - Date-fns with timezone support
  - File type detection
  - Comprehensive testing with Jest

### 6. **vcode**
**Purpose**: Modern IDE with AI integration and terminal support

- **Framework**: Electron 37 (cross-platform desktop application)
- **UI Library**:
  - React 19 with React Compiler
  - Radix UI components (complete component library)
  - Tailwind CSS 4 with Vite plugin
  - TanStack Router for application routing
  - TanStack Query for data fetching
  - Monaco Editor for advanced code editing
  - Xterm.js for integrated terminal
- **AI Libraries**:
  - @ai-sdk/openai for OpenAI integration
  - @ai-sdk/xai for X.AI (Grok) integration
  - ChromaDB for vector database and embeddings
- **Editor Features**:
  - TipTap for rich text editing
  - React Hook Form for form management
  - Embla Carousel for UI components
- **Development Tools**:
  - Electron Forge for building and packaging
  - Node-pty for terminal process management
  - Vite for fast development and building
- **State Management**: Zustand with Immer
- **Package Manager**: pnpm
- **Testing**: 
  - Vitest for unit testing
  - Playwright for E2E testing
  - Testing Library for React component testing
- **Additional Tools**:
  - Internationalization with i18next
  - Day.js for date handling
  - Sonner for notifications
  - React Markdown with GitHub Flavored Markdown

### 7. **vibe-kanban**
**Purpose**: AI coding agent orchestration and task management platform

- **Framework**: 
  - **Frontend**: Vite + React 18 (TypeScript)
  - **Backend**: Rust with Axum web framework
- **UI Library**:
  - Radix UI components (@radix-ui/react-*)
  - Tailwind CSS with TailwindCSS Animate
  - Lucide React for icons
  - @dnd-kit for drag-and-drop functionality
- **Backend Technologies**:
  - **Web Framework**: Axum (async Rust web framework)
  - **Database**: SQLite with SQLx ORM
  - **Async Runtime**: Tokio
  - **Serialization**: Serde + Serde JSON
  - **Logging**: Tracing + Tracing Subscriber
  - **Git Integration**: Git2 (libgit2 bindings)
  - **Type Generation**: ts-rs for TypeScript type generation
- **State Management**: React built-in state with async server communication
- **Package Manager**: pnpm
- **Build Tools**:
  - Vite for frontend bundling and development
  - Rust Cargo for backend compilation
  - Concurrently for running frontend and backend in development
- **AI Integration**:
  - Supports multiple coding agents (Claude Code, Gemini CLI, Codex, Amp)
  - Agent orchestration and task management
  - MCP (Model Context Protocol) configuration management
- **Additional Features**:
  - Real-time task tracking and status updates
  - Multi-agent workflow orchestration
  - Centralized coding agent configuration
  - Task review and approval workflows
  - Development server management
  - NPX CLI distribution for easy installation

### 8. **langflow**
**Purpose**: Visual AI workflow builder and deployment platform

- **Framework**: 
  - **Frontend**: Vite + React 18 (TypeScript)
  - **Backend**: FastAPI + Python 3.10-3.13
- **UI Library**:
  - Radix UI components (comprehensive set)
  - Tailwind CSS 4 with TailwindCSS Typography
  - @xyflow/react (React Flow) for visual workflow building
  - @headlessui/react for advanced components
  - Framer Motion for animations
  - Lucide React and Tabler Icons for iconography
- **Backend Technologies**:
  - **Web Framework**: FastAPI (async Python web framework)
  - **Database**: Multiple database support (PostgreSQL, SQLite, MongoDB)
  - **ORM/ODM**: SQLAlchemy, PyMongo
  - **Async Runtime**: Asyncio with Uvicorn/Gunicorn
  - **API Client**: HTTPx with HTTP/2 support
  - **Logging**: Loguru with Rich formatting
- **AI Integration Ecosystem**:
  - **Core AI Framework**: LangChain 0.3.21 (langchain, langchain-community, langchain-core)
  - **LLM Providers**: OpenAI, Anthropic, Google, Hugging Face, Ollama, Groq
  - **Vector Databases**: Qdrant, Weaviate, FAISS, ChromaDB, Supabase Vector
  - **Additional AI Tools**: LangFuse for observability, Metal SDK, Qianfan
- **State Management**: Zustand + React Hook Form
- **Package Manager**: uv (modern Python package manager)
- **Build Tools**:
  - Vite for frontend with SWC compiler
  - Python setuptools for backend packaging
  - Docker support for deployment
- **Visual Workflow Features**:
  - Drag-and-drop workflow builder
  - Real-time flow execution and debugging
  - Component marketplace and custom components
  - Export flows as JSON or Python code
  - API deployment and MCP server generation
- **Development Tools**:
  - Jest for frontend testing
  - Playwright for E2E testing
  - PyTest for backend testing
  - Pre-commit hooks with formatting
- **Additional Features**:
  - Multi-tenant architecture support
  - Enterprise authentication and security
  - Cloud deployment integrations
  - Observability and monitoring
  - Plugin system for custom components

## Technology Patterns & Common Stack

### Frontend Framework Distribution
- **Next.js Applications**: 4 projects (agent-inbox, foto-fun, gen-ui-computer-use, open-canvas)
- **Vite + React**: 2 projects (vibe-kanban frontend, langflow frontend)
- **Electron Desktop**: 1 project (vcode)
- **Backend Service**: 1 project (social-media-agent)
- **Rust Backend**: 1 project (vibe-kanban backend)
- **Python Backend**: 1 project (langflow backend)

### UI Library Standardization
**Common UI Stack**:
- **Radix UI**: Universal across all frontend projects
- **Tailwind CSS**: Standard styling solution (versions 3-4)
- **React 18/19**: Modern React with latest features
- **TypeScript**: 100% TypeScript adoption for frontend

### AI Integration Patterns
**Primary AI Libraries**:
- **LangChain**: Dominant in 5/8 projects for AI orchestration
- **Vercel AI SDK**: Growing adoption (foto-fun, open-canvas, vcode)
- **Multi-Agent Support**: vibe-kanban (orchestration platform), langflow (visual workflows)
- **Multiple Provider Support**: OpenAI, Anthropic, Google, X.AI, Hugging Face

### Database & State Management
**Database Choice**:
- **Supabase**: Universal database solution across most projects
- **SQLite**: High-performance local database (vibe-kanban)
- **Multi-Database**: PostgreSQL, MongoDB, vector databases (langflow)
- **Specialized ORMs**: Drizzle (foto-fun), ChromaDB (vcode), SQLx (vibe-kanban), SQLAlchemy (langflow)

**State Management**:
- **Zustand**: Preferred state management (foto-fun, open-canvas, vcode, langflow)
- **Built-in React State**: Simpler projects (agent-inbox, vibe-kanban)
- **URL State**: Advanced routing needs (gen-ui-computer-use)

### Package Manager Distribution
- **pnpm**: 4 projects (gen-ui-computer-use, open-canvas, vcode, vibe-kanban)
- **Yarn**: 2 projects (agent-inbox, social-media-agent)
- **Bun**: 1 project (foto-fun) - cutting edge performance
- **uv**: 1 project (langflow) - modern Python package management

### Development & Build Tools
**Common Tools**:
- **ESLint + Prettier**: Universal code quality
- **Vitest**: Modern testing framework adoption
- **Vite**: Fast development and building (vcode, vibe-kanban, langflow)
- **Turbo**: Monorepo management (open-canvas)
- **Electron Forge**: Desktop app packaging (vcode)
- **Rust Cargo**: Backend compilation (vibe-kanban)
- **FastAPI**: Modern Python web framework (langflow)

### Specialized Technologies by Domain

#### Agent Orchestration & Task Management (vibe-kanban)
- Rust backend for high performance and reliability
- SQLite for fast local data storage
- Drag-and-drop task management interface
- Multi-agent workflow coordination
- Real-time status tracking and updates
- MCP configuration management

#### Visual Workflow Building (langflow)
- React Flow for visual workflow construction
- FastAPI for high-performance Python backend
- Multi-database support for enterprise needs
- Comprehensive AI ecosystem integration
- Visual debugging and real-time execution
- Enterprise deployment and security features

#### Graphics & Canvas (foto-fun)
- Fabric.js 6 for canvas manipulation
- PIXI.js for high-performance graphics
- Replicate for AI image processing

#### Automation & Scraping (gen-ui-computer-use, social-media-agent)
- Playwright for browser automation
- Scrapybara for advanced web scraping
- Multiple social media APIs

#### Code Editing (agent-inbox, open-canvas, vcode)
- Monaco Editor for VS Code-like editing
- CodeMirror for lightweight code editing
- Multiple language support

#### Rich Text & Content (open-canvas, vcode)
- BlockNote for rich text editing
- TipTap for advanced text manipulation
- Markdown processing with math support

## Architecture Insights

### Multi-Language Architecture
- **TypeScript/JavaScript**: Frontend applications and Node.js services
- **Rust**: High-performance backend (vibe-kanban)
- **Python**: AI workflow backend (langflow)
- **Mixed Language**: Full-stack applications with different backend technologies

### Monorepo vs Single Repo
- **Monorepo**: open-canvas (Turbo-managed workspace), vibe-kanban (frontend/backend), langflow (frontend/backend)
- **Single Repo**: All other projects

### Deployment Strategies
- **Vercel**: Next.js applications (assumed)
- **LangGraph Cloud**: Backend AI services (social-media-agent)
- **Desktop Distribution**: Electron packaging (vcode)
- **NPX Distribution**: CLI tools (vibe-kanban)
- **Cloud Deployment**: Docker and cloud platforms (langflow)
- **Self-Hosted**: On-premises deployment support (langflow)

### AI Integration Maturity
1. **Visual AI Workflow Platform**: langflow (comprehensive visual builder)
2. **AI Orchestration Platform**: vibe-kanban (multi-agent coordination)
3. **Advanced AI Orchestration**: social-media-agent, open-canvas
4. **Integrated AI Features**: agent-inbox, gen-ui-computer-use
5. **AI-Enhanced Tools**: foto-fun, vcode

### Performance Considerations
- **Bun Runtime**: foto-fun (fastest package manager)
- **Rust Backend**: vibe-kanban (systems programming performance)
- **FastAPI**: langflow (high-performance Python web framework)
- **Turbopack**: foto-fun (Next.js dev acceleration)
- **WebGL**: vcode (terminal rendering optimization)
- **Canvas Optimization**: foto-fun (graphics performance)
- **SQLite**: vibe-kanban (fast local database operations)
- **UV Package Manager**: langflow (modern Python dependency management)

This tech stack analysis reveals a mature, modern development ecosystem with strong consistency in core technologies while maintaining flexibility for specialized use cases. The projects demonstrate best practices in AI integration, modern React development, cross-platform application architecture, and now include high-performance backend services with Rust and Python. The addition of langflow introduces comprehensive visual AI workflow building capabilities and enterprise-grade deployment options to the ecosystem.