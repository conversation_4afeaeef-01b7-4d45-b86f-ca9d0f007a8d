# Gen-UI Computer Use - Technology Stack Documentation

## Technology Stack Overview

The Gen-UI Computer Use project leverages a modern, type-safe technology stack optimized for real-time AI agent interactions and dynamic UI generation. This document provides comprehensive analysis of all technologies, their versions, integration patterns, and architectural decisions.

## Core Framework Technologies

### 1. Next.js 15.2.4 (React Framework)

**Purpose**: Full-stack React framework with App Router
**Location**: Primary framework for web application
**Key Features**:
- **App Router**: New routing system with improved performance
- **Server Components**: Hybrid rendering for optimal performance
- **Edge Runtime**: Serverless deployment optimization
- **API Routes**: Built-in API layer for custom endpoints

**Configuration** (`next.config.mjs`):
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // Enable experimental features for better performance
  },
  // Optimized for edge deployment
  runtime: 'edge'
};
```

**Integration Points**:
- **API Passthrough**: Direct LangGraph agent communication
- **Static Generation**: Optimized build output
- **Image Optimization**: Automatic image processing
- **Font Optimization**: Built-in font loading optimization

### 2. React 19.0.0 (UI Library)

**Purpose**: User interface library with concurrent features
**Key Features**:
- **Concurrent Features**: Improved performance with React 18+ features
- **Suspense**: Loading state management
- **Server Components**: Partial hydration support
- **Hooks**: Advanced state management patterns

**Override Configuration**:
```json
{
  "overrides": {
    "react-is": "^19.0.0-rc-69d4b800-20241021"
  }
}
```

**Usage Patterns**:
- **Functional Components**: Exclusively using function components
- **Custom Hooks**: Reusable stateful logic
- **Context API**: Global state management
- **Suspense Boundaries**: Loading state management

### 3. TypeScript 5.7.2 (Type System)

**Purpose**: Type safety and developer experience
**Configuration** (`tsconfig.json`):
```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

**Type Safety Benefits**:
- **Full Stack Types**: End-to-end type safety
- **Component Props**: Strictly typed component interfaces
- **API Responses**: Typed API response structures
- **State Management**: Typed state and reducers

## AI Agent Technologies

### 1. LangGraph Framework

**Core Package**: `@langchain/langgraph` v0.2.60

**Purpose**: Agent orchestration and workflow management
**Key Features**:
- **Graph-based Workflows**: Node-based agent execution
- **State Management**: Persistent agent state
- **Tool Integration**: Computer use tool support
- **Streaming Support**: Real-time agent communication

**Configuration** (`langgraph.json`):
```json
{
  "node_version": "20",
  "graphs": {
    "agent": "./src/agent/index.ts:graph"
  },
  "ui": {
    "agent": "./src/agent/ui/index.tsx"
  },
  "ui_config": {
    "shared": [
      "nuqs",
      "nuqs/adapters/next/app",
      "@/components/ui/sonner",
      "sonner"
    ]
  }
}
```

**Integration Components**:
- **LangGraph SDK**: `@langchain/langgraph-sdk` v0.0.61
- **LangGraph API**: `@langchain/langgraph-api` v0.0.19
- **LangGraph CLI**: `@langchain/langgraph-cli` v0.0.19

### 2. Computer Use Agent (CUA)

**Package**: `@langchain/langgraph-cua` v0.0.5

**Purpose**: Specialized agent for computer control tasks
**Key Features**:
- **Screen Capture**: Automated screenshot functionality
- **Action Execution**: Computer interaction primitives
- **State Tracking**: VM instance state management
- **Tool Integration**: Built-in computer use tools

**Implementation** (`src/agent/index.ts`):
```typescript
import {
  createCua,
  CUAAnnotation,
  CUAState,
  CUAUpdate,
  getToolOutputs,
  isComputerCallToolMessage,
} from "@langchain/langgraph-cua";

export const graph = createCua({
  nodeBeforeAction: beforeNode,
  nodeAfterAction: afterNode,
  stateModifier: GraphAnnotation,
  recursionLimit: 150,
  timeoutHours: 0.1,
  uploadScreenshot,
});
```

### 3. LangChain Core

**Package**: `@langchain/core` v0.3.43

**Purpose**: Core language model abstractions
**Key Features**:
- **Message Types**: Structured message handling
- **Tool Definitions**: Computer use tool specifications
- **Streaming Support**: Real-time response handling
- **Memory Management**: Conversation history tracking

### 4. OpenAI Integration

**Package**: `@langchain/openai` v0.5.1

**Purpose**: OpenAI model integration for computer use
**Key Features**:
- **GPT-4 Vision**: Screenshot analysis capabilities
- **Function Calling**: Structured tool execution
- **Streaming**: Real-time response generation
- **Error Handling**: Robust API error management

**Configuration**:
```typescript
// Environment variable configuration
OPENAI_API_KEY=your_openai_api_key
```

## UI Component Technologies

### 1. Radix UI Components

**Purpose**: Accessible, unstyled component primitives

**Core Components**:
- `@radix-ui/react-alert-dialog` v1.1.6
- `@radix-ui/react-avatar` v1.1.3
- `@radix-ui/react-dialog` v1.1.6
- `@radix-ui/react-label` v2.1.2
- `@radix-ui/react-separator` v1.1.2
- `@radix-ui/react-slot` v1.1.2
- `@radix-ui/react-switch` v1.1.3
- `@radix-ui/react-tooltip` v1.1.8

**Benefits**:
- **Accessibility**: WCAG compliance out of the box
- **Unstyled**: Full styling control
- **Composable**: Flexible component composition
- **Keyboard Navigation**: Full keyboard support

**Implementation Pattern**:
```typescript
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
```

### 2. Tailwind CSS 4.0.13

**Purpose**: Utility-first CSS framework
**Configuration** (`tailwind.config.js`):
```javascript
module.exports = {
  content: ["./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-in": "slideIn 0.3s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("tailwind-scrollbar"),
  ],
};
```

**Key Features**:
- **Utility Classes**: Rapid UI development
- **Responsive Design**: Mobile-first approach
- **Dark Mode**: Built-in dark mode support
- **Custom Animations**: Tailwind Animate integration

### 3. Framer Motion 12.4.9

**Purpose**: Animation and gesture library
**Key Features**:
- **Layout Animations**: Smooth layout transitions
- **Gesture Handling**: Touch and mouse interactions
- **Spring Physics**: Natural motion animations
- **Variants**: Declarative animation states

**Implementation Examples**:
```typescript
<motion.div
  className="relative flex items-center p-1 rounded-lg bg-gray-200"
  animate={{ x: isShowingInstance ? "95%" : "0%" }}
  transition={{ type: "spring", stiffness: 300, damping: 30 }}
>
```

### 4. Lucide React 0.476.0

**Purpose**: Icon library with consistent design
**Features**:
- **SVG Icons**: Scalable vector graphics
- **Tree Shaking**: Optimized bundle size
- **Consistent Style**: Unified icon design
- **Accessibility**: ARIA-compliant icons

**Usage Pattern**:
```typescript
import { LoaderCircle, PanelRightOpen, SquarePen } from "lucide-react";
```

## State Management Technologies

### 1. NUQS (Next.js URL State)

**Package**: `nuqs` v2.4.1

**Purpose**: URL-based state management
**Key Features**:
- **Type Safety**: Typed URL state
- **Persistence**: State persists across page reloads
- **SSR Support**: Server-side rendering compatibility
- **Validation**: Built-in state validation

**Implementation**:
```typescript
import { useQueryState, parseAsBoolean } from "nuqs";

const [threadId, setThreadId] = useQueryState("threadId");
const [chatHistoryOpen, setChatHistoryOpen] = useQueryState(
  "chatHistoryOpen",
  parseAsBoolean.withDefault(false)
);
```

### 2. React Context API

**Purpose**: Global state management
**Implementation Pattern**:
```typescript
const StreamContext = createContext<StreamContextType | undefined>(undefined);

export const StreamProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const streamValue = useTypedStream({
    apiUrl,
    assistantId,
    threadId: threadId ?? null,
    onCustomEvent,
    onThreadId,
  });

  return (
    <StreamContext.Provider value={streamValue}>
      {children}
    </StreamContext.Provider>
  );
};
```

### 3. Local Storage Management

**Package**: Custom implementation
**Location**: `src/lib/local-storage.ts`

**Purpose**: Client-side persistence
**Features**:
- **Type Safety**: Typed storage operations
- **Error Handling**: Robust error management
- **Serialization**: Automatic JSON handling
- **Expiration**: Optional expiration support

## VM Control Technologies

### 1. Scrapybara Integration

**Package**: `scrapybara` v2.4.4

**Purpose**: VM management and control
**Key Features**:
- **VM Creation**: Dynamic VM instance creation
- **Screen Sharing**: Real-time screen streaming
- **Control Interface**: Remote desktop functionality
- **Status Management**: VM state monitoring

**API Integration**:
```typescript
// Environment configuration
SCrapybara_API_KEY=your_scrapybara_api_key

// VM instance management
const response = await fetch(`/api/instance/status`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ instanceId }),
});
```

### 2. Iframe Integration

**Purpose**: Direct VM interaction
**Implementation**:
```typescript
<iframe
  src={streamUrl}
  className="w-full h-full"
  title="Instance Frame"
  allow="clipboard-write"
/>
```

**Features**:
- **Real-time Interaction**: Direct VM control
- **Clipboard Access**: Copy/paste functionality
- **Full Screen Support**: Expandable interface
- **Responsive Design**: Mobile-friendly controls

## Storage Technologies

### 1. Supabase Integration

**Package**: `@supabase/supabase-js` v2.49.4

**Purpose**: Screenshot storage and management
**Configuration**:
```typescript
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseApiKey = process.env.SUPABASE_API_KEY;
const bucketName = "cua-screenshots";
```

**Features**:
- **File Upload**: Screenshot image storage
- **Signed URLs**: Time-limited access
- **Bucket Management**: Organized storage
- **Authentication**: Service role access

### 2. UUID Generation

**Package**: `uuid` v11.0.5

**Purpose**: Unique identifier generation
**Usage**:
```typescript
import { v4 as uuidv4 } from "uuid";

const newHumanMessage: Message = {
  id: uuidv4(),
  type: "human",
  content: input,
};
```

## Development Tools

### 1. ESLint Configuration

**Packages**:
- `eslint` v9.19.0
- `eslint-config-next` v15.2.2
- `eslint-plugin-react-hooks` v5.0.0
- `typescript-eslint` v8.22.0

**Configuration** (`eslint.config.js`):
```javascript
import js from "@eslint/js";
import globals from "globals";
import reactHooks from "eslint-plugin-react-hooks";
import tseslint from "typescript-eslint";

export default tseslint.config(
  { ignores: ["dist"] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ["**/*.{ts,tsx}"],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      "react-hooks": reactHooks,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
    },
  }
);
```

### 2. Prettier Configuration

**Package**: `prettier` v3.5.2

**Purpose**: Code formatting
**Configuration**: Standard Prettier settings with TypeScript support

### 3. PostCSS Configuration

**Package**: `postcss` v8.5.3

**Purpose**: CSS processing
**Configuration** (`postcss.config.mjs`):
```javascript
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};
```

## Build and Deployment Technologies

### 1. Package Management

**Package Manager**: `pnpm` v10.6.3

**Benefits**:
- **Fast Installation**: Efficient dependency resolution
- **Disk Space**: Shared dependency storage
- **Strict Mode**: Strict dependency isolation
- **Lockfile**: Deterministic installations

### 2. Build Tools

**ESBuild**: 
- **Package**: `esbuild` v0.25.0
- **Plugin**: `esbuild-plugin-tailwindcss` v2.0.1
- **Purpose**: Fast TypeScript compilation

**Turbo**:
- **Package**: `turbo` (latest)
- **Purpose**: Monorepo build optimization

### 3. Runtime Configuration

**Edge Runtime**: Optimized for serverless deployment
**Node.js Version**: 20 (specified in langgraph.json)

## API and Communication Technologies

### 1. LangGraph API Passthrough

**Package**: `langgraph-nextjs-api-passthrough` v0.0.4

**Purpose**: Seamless LangGraph API integration
**Implementation**:
```typescript
import { initApiPassthrough } from "langgraph-nextjs-api-passthrough";

export const { GET, POST, PUT, PATCH, DELETE, OPTIONS, runtime } =
  initApiPassthrough({
    apiUrl: process.env.LANGGRAPH_API_URL,
    apiKey: process.env.LANGCHAIN_API_KEY,
    runtime: "edge",
  });
```

### 2. WebSocket Communication

**Implementation**: Through LangGraph SDK
**Features**:
- **Real-time Streaming**: Bidirectional communication
- **Automatic Reconnection**: Connection resilience
- **Type Safety**: Typed message handling
- **Error Recovery**: Robust error handling

## Specialized Libraries

### 1. Markdown Processing

**Packages**:
- `react-markdown` v10.0.1
- `remark-gfm` v4.0.1
- `remark-math` v6.0.0
- `rehype-katex` v7.0.1

**Purpose**: Rich text rendering with math support

### 2. Syntax Highlighting

**Package**: `react-syntax-highlighter` v15.5.0

**Purpose**: Code block highlighting in conversations

### 3. Date Handling

**Package**: `date-fns` v4.1.0

**Purpose**: Date manipulation and formatting

### 4. Utility Libraries

**Packages**:
- `lodash` v4.17.21: Utility functions
- `clsx` v2.1.1: Conditional class names
- `tailwind-merge` v3.0.2: Tailwind class merging
- `zod` v3.24.2: Schema validation

## Performance Optimization

### 1. Bundle Optimization

**Features**:
- **Tree Shaking**: Unused code elimination
- **Code Splitting**: Dynamic imports
- **Compression**: Automatic asset compression
- **Minification**: JavaScript/CSS minification

### 2. Runtime Optimization

**Edge Runtime**:
- **Cold Start**: Minimal startup time
- **Memory Usage**: Optimized memory footprint
- **Geographic Distribution**: Global edge deployment
- **Auto-scaling**: Automatic capacity management

### 3. Caching Strategies

**Client-side Caching**:
- **React Query**: API response caching
- **Browser Cache**: Static asset caching
- **Local Storage**: User preference caching

This comprehensive technology stack provides a robust foundation for real-time AI agent interactions with optimal performance, type safety, and developer experience.