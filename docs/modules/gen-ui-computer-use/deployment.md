# Gen-UI Computer Use - Deployment Documentation

## Deployment Overview

The Gen-UI Computer Use system requires a sophisticated deployment strategy that coordinates multiple services: the Next.js web application, the LangGraph agent server, and various external dependencies. This document provides comprehensive deployment guidance for different environments and platforms.

## Deployment Architecture

### 1. Multi-Service Architecture

```mermaid
graph TB
    subgraph "Frontend Deployment"
        WebApp[Next.js App<br/>Vercel/Netlify]
        CDN[CDN<br/>Static Assets]
        DNS[DNS<br/>Domain Management]
    end
    
    subgraph "Backend Services"
        Agent[LangGraph Agent<br/>Cloud/Self-hosted]
        API[API Gateway<br/>Load Balancer]
        Queue[Message Queue<br/>Redis/RabbitMQ]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Scrapybara[Scrapybara VM]
        Supabase[Supabase<br/>Database & Storage]
    end
    
    subgraph "Infrastructure"
        Monitor[Monitoring<br/>Datadog/New Relic]
        Logs[Logging<br/>CloudWatch/ELK]
        Secrets[Secrets<br/>Vault/AWS Secrets]
    end
    
    WebApp --> API
    API --> Agent
    Agent --> Queue
    Agent --> OpenAI
    Agent --> Scrapybara
    Agent --> Supabase
    
    WebApp --> CDN
    DNS --> WebApp
    
    Monitor --> WebApp
    Monitor --> Agent
    Logs --> WebApp
    Logs --> Agent
    Secrets --> Agent
```

### 2. Deployment Strategies

**Blue-Green Deployment**:
- Zero-downtime deployments
- Instant rollback capability
- Full environment testing
- Traffic switching

**Rolling Deployment**:
- Gradual instance replacement
- Reduced resource requirements
- Continuous availability
- Progressive rollout

**Canary Deployment**:
- Risk mitigation
- Performance monitoring
- Gradual traffic increase
- Quick rollback

## Local Development Setup

### 1. Prerequisites

**System Requirements**:
```bash
# Node.js and package managers
Node.js >= 20.0.0
npm >= 10.0.0
pnpm >= 8.0.0

# Development tools
Git >= 2.30.0
Docker >= 24.0.0
Docker Compose >= 2.0.0

# Optional tools
VSCode with extensions
Postman or similar API testing tool
```

**Environment Setup**:
```bash
# Clone repository
git clone https://github.com/bracesproul/gen-ui-computer-use.git
cd gen-ui-computer-use

# Install dependencies
pnpm install

# Copy environment template
cp .env.example .env.local

# Configure environment variables
# Edit .env.local with your API keys
```

### 2. Local Development Commands

**Development Workflow**:
```bash
# Start development servers
# Terminal 1: Next.js web server
pnpm run dev

# Terminal 2: LangGraph agent server
pnpm run agent

# Additional development commands
pnpm run lint          # Run ESLint
pnpm run format        # Format code with Prettier
pnpm run type-check    # TypeScript type checking
pnpm run test          # Run tests
pnpm run build         # Build for production
```

**Docker Development**:
```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 3. Development Environment Configuration

**Development Docker Compose**:
```yaml
version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - WATCHPACK_POLLING=true
    env_file:
      - .env.local
    depends_on:
      - agent
      - redis

  agent:
    build:
      context: .
      dockerfile: Dockerfile.agent
    ports:
      - "8000:8000"
    volumes:
      - ./src/agent:/app/agent
      - ./langgraph.json:/app/langgraph.json
    environment:
      - NODE_ENV=development
    env_file:
      - .env.local
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

## Production Deployment

### 1. Vercel Deployment (Recommended)

**Vercel Configuration**:
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    }
  ],
  "env": {
    "OPENAI_API_KEY": "@openai-api-key",
    "SCrapybara_API_KEY": "@scrapybara-api-key",
    "SUPABASE_URL": "@supabase-url",
    "SUPABASE_API_KEY": "@supabase-api-key",
    "LANGGRAPH_API_URL": "@langgraph-api-url",
    "LANGCHAIN_API_KEY": "@langchain-api-key"
  },
  "functions": {
    "src/app/api/**/route.ts": {
      "runtime": "edge",
      "regions": ["iad1", "sfo1"]
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "no-store, no-cache, must-revalidate"
        }
      ]
    }
  ]
}
```

**Deployment Steps**:
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy to preview
vercel

# Deploy to production
vercel --prod

# Set environment variables
vercel env add OPENAI_API_KEY production
vercel env add SCrapybara_API_KEY production
vercel env add SUPABASE_URL production
vercel env add SUPABASE_API_KEY production
vercel env add LANGGRAPH_API_URL production
vercel env add LANGCHAIN_API_KEY production
```

### 2. Docker Production Deployment

**Production Dockerfile**:
```dockerfile
# Multi-stage build for production
FROM node:20-alpine AS base
WORKDIR /app
COPY package*.json pnpm-lock.yaml ./
RUN npm install -g pnpm

FROM base AS deps
RUN pnpm install --frozen-lockfile --prod

FROM base AS builder
COPY . .
RUN pnpm install --frozen-lockfile
RUN pnpm run build

FROM node:20-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/next.config.mjs ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
```

**Production Docker Compose**:
```yaml
version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - LANGGRAPH_API_URL=http://agent:8000
    env_file:
      - .env.production
    depends_on:
      - agent
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  agent:
    build:
      context: .
      dockerfile: Dockerfile.agent
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  redis_data:
```

### 3. Kubernetes Deployment

**Kubernetes Manifests**:
```yaml
# app-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gen-ui-app
  labels:
    app: gen-ui-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: gen-ui-app
  template:
    metadata:
      labels:
        app: gen-ui-app
    spec:
      containers:
      - name: app
        image: gen-ui-computer-use:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: LANGGRAPH_API_URL
          value: "http://agent-service:8000"
        envFrom:
        - secretRef:
            name: app-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: app-service
spec:
  selector:
    app: gen-ui-app
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# agent-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gen-ui-agent
  labels:
    app: gen-ui-agent
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gen-ui-agent
  template:
    metadata:
      labels:
        app: gen-ui-agent
    spec:
      containers:
      - name: agent
        image: gen-ui-agent:latest
        ports:
        - containerPort: 8000
        env:
        - name: NODE_ENV
          value: "production"
        envFrom:
        - secretRef:
            name: agent-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: agent-service
spec:
  selector:
    app: gen-ui-agent
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gen-ui-ingress
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: gen-ui-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app-service
            port:
              number: 80
```

## LangGraph Agent Deployment

### 1. LangGraph Cloud Deployment

**LangGraph Cloud Configuration**:
```bash
# Install LangGraph CLI
npm install -g @langchain/langgraph-cli

# Login to LangGraph Cloud
langgraph login

# Deploy agent
langgraph deploy

# Configure environment variables
langgraph env set OPENAI_API_KEY your_key
langgraph env set SCrapybara_API_KEY your_key
langgraph env set SUPABASE_URL your_url
langgraph env set SUPABASE_API_KEY your_key

# Monitor deployment
langgraph status
langgraph logs
```

### 2. Self-Hosted Agent Deployment

**Agent Dockerfile**:
```dockerfile
FROM node:20-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm install -g pnpm

FROM base AS deps
COPY pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

FROM base AS builder
COPY . .
RUN pnpm install --frozen-lockfile
RUN pnpm run build:agent

FROM base AS runner
WORKDIR /app
ENV NODE_ENV=production

COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/langgraph.json ./langgraph.json

EXPOSE 8000
CMD ["node", "dist/agent/index.js"]
```

**Agent Deployment Script**:
```bash
#!/bin/bash
set -e

# Build agent image
docker build -t gen-ui-agent -f Dockerfile.agent .

# Deploy to production
docker run -d \
  --name gen-ui-agent \
  --restart unless-stopped \
  -p 8000:8000 \
  --env-file .env.production \
  gen-ui-agent

# Health check
sleep 10
curl -f http://localhost:8000/health || exit 1

echo "Agent deployed successfully"
```

### 3. Scaling Configuration

**Auto-scaling Configuration**:
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: gen-ui-app-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gen-ui-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: gen-ui-agent-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gen-ui-agent
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
```

## CI/CD Pipeline

### 1. GitHub Actions Workflow

**CI/CD Configuration**:
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Run tests
        run: pnpm test
      
      - name: Run linting
        run: pnpm lint
      
      - name: Type check
        run: pnpm type-check
      
      - name: Build
        run: pnpm build

  deploy-preview:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Deploy to Vercel Preview
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./

  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Deploy to Vercel Production
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./
      
      - name: Deploy Agent
        run: |
          # Deploy agent to production
          curl -X POST ${{ secrets.AGENT_DEPLOY_WEBHOOK }} \
            -H "Authorization: Bearer ${{ secrets.AGENT_API_KEY }}" \
            -H "Content-Type: application/json" \
            -d '{"ref": "main"}'
```

### 2. Automated Testing

**Test Configuration**:
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Run unit tests
        run: pnpm test:unit
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

  integration-tests:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Run integration tests
        run: pnpm test:integration
        env:
          REDIS_URL: redis://localhost:6379

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Install Playwright
        run: npx playwright install
      
      - name: Run E2E tests
        run: pnpm test:e2e
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-results
          path: test-results/
```

## Monitoring and Observability

### 1. Application Monitoring

**Health Check Endpoints**:
```typescript
// src/app/api/health/route.ts
export async function GET() {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version,
    environment: process.env.NODE_ENV,
    services: {
      langgraph: await checkLangGraphHealth(),
      supabase: await checkSupabaseHealth(),
      redis: await checkRedisHealth(),
    },
  };

  const isHealthy = Object.values(health.services).every(
    service => service.status === 'healthy'
  );

  return Response.json(health, {
    status: isHealthy ? 200 : 503,
  });
}

async function checkLangGraphHealth() {
  try {
    const response = await fetch(`${process.env.LANGGRAPH_API_URL}/health`);
    return {
      status: response.ok ? 'healthy' : 'unhealthy',
      responseTime: Date.now() - startTime,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
    };
  }
}
```

### 2. Logging Configuration

**Structured Logging**:
```typescript
// src/lib/logger.ts
import winston from 'winston';

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    ...(process.env.NODE_ENV === 'production' ? [
      new winston.transports.File({ filename: 'error.log', level: 'error' }),
      new winston.transports.File({ filename: 'combined.log' }),
    ] : []),
  ],
});

// Request logging middleware
export const requestLogger = (req: Request) => {
  const start = Date.now();
  
  return {
    end: (res: Response) => {
      const duration = Date.now() - start;
      logger.info('HTTP Request', {
        method: req.method,
        url: req.url,
        status: res.status,
        duration,
        userAgent: req.headers.get('user-agent'),
        ip: req.headers.get('x-forwarded-for') || 'unknown',
      });
    },
  };
};
```

### 3. Error Tracking

**Error Monitoring Setup**:
```typescript
// src/lib/error-tracking.ts
import * as Sentry from '@sentry/nextjs';

if (process.env.SENTRY_DSN) {
  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV,
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    beforeSend: (event, hint) => {
      // Filter out expected errors
      if (event.exception) {
        const error = hint.originalException;
        if (error?.name === 'AbortError' || error?.name === 'TimeoutError') {
          return null;
        }
      }
      return event;
    },
  });
}

export const captureError = (error: Error, context?: Record<string, any>) => {
  Sentry.captureException(error, {
    contexts: {
      custom: context,
    },
  });
};
```

## Backup and Recovery

### 1. Database Backup

**Automated Backup Script**:
```bash
#!/bin/bash
# backup.sh
set -e

BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup Supabase data
pg_dump $DATABASE_URL > $BACKUP_DIR/supabase.sql

# Backup Redis data
redis-cli --rdb $BACKUP_DIR/redis.rdb

# Backup uploaded files
aws s3 sync s3://your-bucket $BACKUP_DIR/files

# Compress backup
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

# Upload to backup storage
aws s3 cp $BACKUP_DIR.tar.gz s3://your-backup-bucket/

echo "Backup completed: $BACKUP_DIR.tar.gz"
```

### 2. Disaster Recovery

**Recovery Procedures**:
```bash
#!/bin/bash
# recovery.sh
set -e

BACKUP_FILE=$1
if [ -z "$BACKUP_FILE" ]; then
  echo "Usage: $0 <backup-file>"
  exit 1
fi

# Download backup
aws s3 cp s3://your-backup-bucket/$BACKUP_FILE ./

# Extract backup
tar -xzf $BACKUP_FILE

# Restore database
psql $DATABASE_URL < $(basename $BACKUP_FILE .tar.gz)/supabase.sql

# Restore Redis
redis-cli --rdb $(basename $BACKUP_FILE .tar.gz)/redis.rdb

# Restore files
aws s3 sync $(basename $BACKUP_FILE .tar.gz)/files s3://your-bucket

echo "Recovery completed from: $BACKUP_FILE"
```

## Security Considerations

### 1. Production Security

**Security Checklist**:
- [ ] Enable HTTPS/TLS encryption
- [ ] Configure proper CORS policies
- [ ] Implement rate limiting
- [ ] Use security headers
- [ ] Regular security updates
- [ ] API key rotation
- [ ] Network security rules
- [ ] Container security scanning

**Security Headers**:
```typescript
// next.config.mjs
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';",
          },
        ],
      },
    ];
  },
};
```

### 2. Environment Security

**Secret Management**:
```yaml
# kubernetes-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
type: Opaque
data:
  OPENAI_API_KEY: <base64-encoded-key>
  SCrapybara_API_KEY: <base64-encoded-key>
  SUPABASE_API_KEY: <base64-encoded-key>
  LANGCHAIN_API_KEY: <base64-encoded-key>
```

This comprehensive deployment guide ensures secure, scalable, and maintainable deployment of the Gen-UI Computer Use system across various environments and platforms.