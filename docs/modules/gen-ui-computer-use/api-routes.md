# Gen-UI Computer Use - API Routes Documentation

## API Routes Overview

The Gen-UI Computer Use system implements a comprehensive API routing system that combines Next.js API routes with LangGraph API passthrough functionality. This architecture provides seamless integration between the frontend application and the LangGraph agent server while maintaining custom endpoints for VM management.

## API Architecture

### 1. API Route Structure

```mermaid
graph TB
    subgraph "Client Layer"
        Frontend[Next.js Frontend]
        APIClient[API Client]
    end
    
    subgraph "Next.js API Routes"
        Passthrough[LangGraph Passthrough<br/>api/[..._path]]
        VMRoutes[VM Instance Routes<br/>api/instance/*]
        CustomRoutes[Custom API Routes]
    end
    
    subgraph "External Services"
        LangGraph[LangGraph Agent Server]
        Scrapybara[Scrapybara VM API]
        Supabase[Supabase Storage]
    end
    
    Frontend --> APIClient
    APIClient --> Passthrough
    APIClient --> VMRoutes
    APIClient --> CustomRoutes
    
    Passthrough --> LangGraph
    VMRoutes --> Scrapybara
    CustomRoutes --> Supabase
```

### 2. Route Categories

**LangGraph Passthrough Routes**:
- **Pattern**: `/api/[...path]`
- **Purpose**: Direct proxy to LangGraph agent server
- **Features**: Authentication, error handling, edge runtime

**VM Instance Routes**:
- **Pattern**: `/api/instance/*`
- **Purpose**: Virtual machine management
- **Features**: Scrapybara API integration, status monitoring

**Custom API Routes**:
- **Pattern**: `/api/custom/*`
- **Purpose**: Application-specific functionality
- **Features**: Business logic, data processing

## LangGraph API Passthrough

### 1. Passthrough Implementation

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/[..._path]/route.ts`

**Core Implementation**:
```typescript
import { initApiPassthrough } from "langgraph-nextjs-api-passthrough";

export const { GET, POST, PUT, PATCH, DELETE, OPTIONS, runtime } =
  initApiPassthrough({
    apiUrl: process.env.LANGGRAPH_API_URL,
    apiKey: process.env.LANGCHAIN_API_KEY,
    runtime: "edge" as const,
  });
```

**Configuration Parameters**:
- **apiUrl**: LangGraph server endpoint URL
- **apiKey**: Authentication key for LangGraph API
- **runtime**: Edge runtime for optimal performance

### 2. Environment Configuration

**Required Environment Variables**:
```bash
# LangGraph API Configuration
LANGGRAPH_API_URL=http://localhost:8000
LANGCHAIN_API_KEY=your_langchain_api_key

# Next.js API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

**Development Setup**:
```bash
# Start LangGraph agent server
pnpm run agent

# Start Next.js development server
pnpm run dev
```

### 3. Edge Runtime Benefits

**Performance Advantages**:
- **Cold Start**: Minimal startup latency
- **Geographic Distribution**: Global edge deployment
- **Auto-scaling**: Automatic request handling
- **Memory Efficiency**: Optimized memory usage

**Runtime Limitations**:
- **API Subset**: Limited Node.js API access
- **File System**: No filesystem access
- **Environment**: Restricted execution environment

## VM Instance API Routes

### 1. Instance Status Route

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/instance/status/route.ts`

**Purpose**: Check virtual machine instance status

**Request/Response Interface**:
```typescript
// Request
interface StatusRequest {
  instanceId: string;
}

// Response
interface StatusResponse {
  success: boolean;
  status: "unknown" | "running" | "paused" | "terminated";
  instanceId: string;
  streamUrl?: string;
  environment?: string;
  lastActivity?: string;
  resources?: {
    cpu: number;
    memory: number;
    disk: number;
  };
  error?: string;
}
```

**Implementation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId } = await request.json();
    
    // Validate request
    if (!instanceId) {
      return NextResponse.json(
        { error: "Instance ID is required" },
        { status: 400 }
      );
    }

    // Validate instance ID format
    if (!/^[a-zA-Z0-9-_]+$/.test(instanceId)) {
      return NextResponse.json(
        { error: "Invalid instance ID format" },
        { status: 400 }
      );
    }

    // Check API key
    const apiKey = process.env.SCrapybara_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: "Scrapybara API key not configured" },
        { status: 500 }
      );
    }

    // Call Scrapybara API
    const response = await fetch(`https://api.scrapybara.com/v1/instances/${instanceId}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json(
          { error: "Instance not found" },
          { status: 404 }
        );
      }
      
      throw new Error(`Scrapybara API error: ${response.status}`);
    }

    const instanceData = await response.json();
    
    return NextResponse.json({
      success: true,
      status: instanceData.status,
      instanceId: instanceData.id,
      streamUrl: instanceData.streamUrl,
      environment: instanceData.environment,
      lastActivity: instanceData.lastActivity,
      resources: instanceData.resources,
    });
  } catch (error) {
    console.error("Failed to check instance status:", error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Internal server error",
        success: false 
      },
      { status: 500 }
    );
  }
}
```

### 2. Instance Pause Route

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/instance/pause/route.ts`

**Purpose**: Pause running VM instance

**Request/Response Interface**:
```typescript
// Request
interface PauseRequest {
  instanceId: string;
}

// Response
interface PauseResponse {
  success: boolean;
  message: string;
  instanceId: string;
  newStatus: "paused";
  error?: string;
}
```

**Implementation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId } = await request.json();
    
    if (!instanceId) {
      return NextResponse.json(
        { error: "Instance ID is required", success: false },
        { status: 400 }
      );
    }

    const apiKey = process.env.SCrapybara_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: "Scrapybara API key not configured", success: false },
        { status: 500 }
      );
    }

    // Pause instance via Scrapybara API
    const response = await fetch(`https://api.scrapybara.com/v1/instances/${instanceId}/pause`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }

    const result = await response.json();
    
    return NextResponse.json({
      success: true,
      message: "Instance paused successfully",
      instanceId: instanceId,
      newStatus: "paused",
    });
  } catch (error) {
    console.error("Failed to pause instance:", error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to pause instance",
        success: false 
      },
      { status: 500 }
    );
  }
}
```

### 3. Instance Resume Route

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/instance/resume/route.ts`

**Purpose**: Resume paused VM instance

**Request/Response Interface**:
```typescript
// Request
interface ResumeRequest {
  instanceId: string;
}

// Response
interface ResumeResponse {
  success: boolean;
  message: string;
  instanceId: string;
  newStatus: "running";
  streamUrl?: string;
  error?: string;
}
```

**Implementation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId } = await request.json();
    
    if (!instanceId) {
      return NextResponse.json(
        { error: "Instance ID is required", success: false },
        { status: 400 }
      );
    }

    const apiKey = process.env.SCrapybara_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: "Scrapybara API key not configured", success: false },
        { status: 500 }
      );
    }

    // Resume instance via Scrapybara API
    const response = await fetch(`https://api.scrapybara.com/v1/instances/${instanceId}/resume`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }

    const result = await response.json();
    
    return NextResponse.json({
      success: true,
      message: "Instance resumed successfully",
      instanceId: instanceId,
      newStatus: "running",
      streamUrl: result.streamUrl,
    });
  } catch (error) {
    console.error("Failed to resume instance:", error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to resume instance",
        success: false 
      },
      { status: 500 }
    );
  }
}
```

### 4. Instance Stop Route

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/instance/stop/route.ts`

**Purpose**: Terminate VM instance

**Request/Response Interface**:
```typescript
// Request
interface StopRequest {
  instanceId: string;
  force?: boolean;
}

// Response
interface StopResponse {
  success: boolean;
  message: string;
  instanceId: string;
  newStatus: "terminated";
  error?: string;
}
```

**Implementation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId, force = false } = await request.json();
    
    if (!instanceId) {
      return NextResponse.json(
        { error: "Instance ID is required", success: false },
        { status: 400 }
      );
    }

    const apiKey = process.env.SCrapybara_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: "Scrapybara API key not configured", success: false },
        { status: 500 }
      );
    }

    // Terminate instance via Scrapybara API
    const response = await fetch(`https://api.scrapybara.com/v1/instances/${instanceId}/terminate`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ force }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }

    const result = await response.json();
    
    return NextResponse.json({
      success: true,
      message: "Instance terminated successfully",
      instanceId: instanceId,
      newStatus: "terminated",
    });
  } catch (error) {
    console.error("Failed to terminate instance:", error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to terminate instance",
        success: false 
      },
      { status: 500 }
    );
  }
}
```

## Client-Side API Integration

### 1. API Client Configuration

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/providers/client.ts`

**Purpose**: LangGraph client configuration for frontend

**Implementation**:
```typescript
import { Client } from "@langchain/langgraph-sdk";

export const createClient = (apiUrl: string): Client => {
  return new Client({
    apiUrl,
    // Additional client configuration
    defaultHeaders: {
      "Content-Type": "application/json",
    },
    timeout: 30000, // 30 second timeout
  });
};

// Usage in providers
const client = createClient(
  process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:3000/api"
);
```

### 2. Stream Integration

**Stream API Usage**:
```typescript
const useTypedStream = useStream<
  StateType,
  {
    UpdateType: {
      messages?: Message[] | Message | string;
      ui?: (UIMessage | RemoveUIMessage)[] | UIMessage | RemoveUIMessage;
      streamUrl?: string;
      instanceId?: string;
      environment?: string;
    };
    CustomEventType: UIMessage | RemoveUIMessage;
  }
>;

const streamValue = useTypedStream({
  apiUrl: process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:3000/api",
  assistantId: "agent",
  threadId: threadId ?? null,
  onCustomEvent: (event, options) => {
    options.mutate((prev) => {
      const ui = uiMessageReducer(prev.ui ?? [], event);
      return { ...prev, ui };
    });
  },
});
```

### 3. VM Instance API Calls

**Status Check Implementation**:
```typescript
const checkInstanceStatus = async (instanceId: string) => {
  const response = await fetch(`/api/instance/status`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ instanceId }),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();
  if (!data.success) {
    throw new Error(data.error || "Unknown error");
  }

  return data;
};
```

**Instance Control Implementation**:
```typescript
const controlInstance = async (
  instanceId: string, 
  action: "pause" | "resume" | "stop"
) => {
  const response = await fetch(`/api/instance/${action}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ instanceId }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `Failed to ${action} instance`);
  }

  return await response.json();
};
```

## Error Handling

### 1. API Error Types

**Error Classification**:
```typescript
export enum APIErrorType {
  VALIDATION_ERROR = "VALIDATION_ERROR",
  AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
  NOT_FOUND_ERROR = "NOT_FOUND_ERROR",
  RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR",
  SERVER_ERROR = "SERVER_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
}

export class APIError extends Error {
  public type: APIErrorType;
  public status: number;
  public details?: any;

  constructor(
    message: string,
    type: APIErrorType,
    status: number,
    details?: any
  ) {
    super(message);
    this.name = "APIError";
    this.type = type;
    this.status = status;
    this.details = details;
  }
}
```

### 2. Error Response Format

**Standardized Error Response**:
```typescript
interface ErrorResponse {
  success: false;
  error: string;
  type?: APIErrorType;
  details?: any;
  timestamp: string;
  requestId?: string;
}

const createErrorResponse = (
  error: string,
  type: APIErrorType = APIErrorType.SERVER_ERROR,
  details?: any
): ErrorResponse => ({
  success: false,
  error,
  type,
  details,
  timestamp: new Date().toISOString(),
  requestId: generateRequestId(),
});
```

### 3. Client-Side Error Handling

**Error Handler Hook**:
```typescript
const useAPIErrorHandler = () => {
  const { toast } = useToast();

  const handleError = useCallback((error: unknown) => {
    console.error("API Error:", error);

    if (error instanceof APIError) {
      switch (error.type) {
        case APIErrorType.VALIDATION_ERROR:
          toast.error("Invalid request", {
            description: error.message,
          });
          break;
        
        case APIErrorType.AUTHENTICATION_ERROR:
          toast.error("Authentication failed", {
            description: "Please check your credentials",
          });
          break;
        
        case APIErrorType.NOT_FOUND_ERROR:
          toast.error("Resource not found", {
            description: error.message,
          });
          break;
        
        case APIErrorType.RATE_LIMIT_ERROR:
          toast.error("Rate limit exceeded", {
            description: "Please try again later",
          });
          break;
        
        default:
          toast.error("An error occurred", {
            description: error.message,
          });
      }
    } else {
      toast.error("An unexpected error occurred", {
        description: "Please try again later",
      });
    }
  }, [toast]);

  return { handleError };
};
```

## Performance Optimization

### 1. Request Caching

**Cache Implementation**:
```typescript
const apiCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5000; // 5 seconds

const getCachedResponse = (key: string) => {
  const cached = apiCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  return null;
};

const setCachedResponse = (key: string, data: any) => {
  apiCache.set(key, { data, timestamp: Date.now() });
};

const cachedFetch = async (url: string, options: RequestInit = {}) => {
  const cacheKey = `${url}:${JSON.stringify(options)}`;
  const cached = getCachedResponse(cacheKey);
  
  if (cached) {
    return cached;
  }
  
  const response = await fetch(url, options);
  const data = await response.json();
  
  setCachedResponse(cacheKey, data);
  return data;
};
```

### 2. Request Debouncing

**Debounced API Calls**:
```typescript
import { debounce } from 'lodash';

const debouncedStatusCheck = debounce(
  async (instanceId: string) => {
    try {
      const status = await checkInstanceStatus(instanceId);
      setVMStatus(status);
    } catch (error) {
      console.error("Status check failed:", error);
    }
  },
  1000 // 1 second debounce
);

// Usage
useEffect(() => {
  if (instanceId) {
    debouncedStatusCheck(instanceId);
  }
}, [instanceId]);
```

### 3. Request Batching

**Batch API Requests**:
```typescript
class APIBatcher {
  private queue: Array<{
    request: () => Promise<any>;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];
  
  private timeoutId: NodeJS.Timeout | null = null;

  public batch<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push({ request, resolve, reject });
      
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
      }
      
      this.timeoutId = setTimeout(() => {
        this.processBatch();
      }, 100); // 100ms batch window
    });
  }

  private async processBatch() {
    const batch = this.queue.splice(0);
    
    try {
      const results = await Promise.allSettled(
        batch.map(item => item.request())
      );
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          batch[index].resolve(result.value);
        } else {
          batch[index].reject(result.reason);
        }
      });
    } catch (error) {
      batch.forEach(item => item.reject(error));
    }
  }
}

const apiBatcher = new APIBatcher();
```

## Security Considerations

### 1. Input Validation

**Request Validation**:
```typescript
import { z } from "zod";

const instanceIdSchema = z.string().regex(/^[a-zA-Z0-9-_]+$/);
const statusRequestSchema = z.object({
  instanceId: instanceIdSchema,
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validatedData = statusRequestSchema.parse(body);
    
    // Use validated data
    const { instanceId } = validatedData;
    
    // ... rest of implementation
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }
    
    // ... other error handling
  }
}
```

### 2. Rate Limiting

**Rate Limiter Implementation**:
```typescript
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

const checkRateLimit = (clientId: string, limit: number = 100, window: number = 60000) => {
  const now = Date.now();
  const client = rateLimiter.get(clientId);
  
  if (!client || now > client.resetTime) {
    rateLimiter.set(clientId, { count: 1, resetTime: now + window });
    return true;
  }
  
  if (client.count >= limit) {
    return false;
  }
  
  client.count++;
  return true;
};

// Usage in API route
export async function POST(request: Request) {
  const clientId = getClientId(request);
  
  if (!checkRateLimit(clientId)) {
    return NextResponse.json(
      { error: "Rate limit exceeded" },
      { status: 429 }
    );
  }
  
  // ... rest of implementation
}
```

### 3. Authentication

**API Key Validation**:
```typescript
const validateAPIKey = (request: Request): boolean => {
  const authHeader = request.headers.get("Authorization");
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return false;
  }
  
  const token = authHeader.slice(7);
  const validToken = process.env.API_SECRET_KEY;
  
  return token === validToken;
};

// Usage in protected routes
export async function POST(request: Request) {
  if (!validateAPIKey(request)) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }
  
  // ... rest of implementation
}
```

This comprehensive API routing system provides secure, performant, and scalable communication between the frontend application and backend services while maintaining clear separation of concerns and robust error handling.