# Gen-UI Computer Use - Architecture Documentation

## System Architecture Overview

The Gen-UI Computer Use system implements a sophisticated dual-server architecture that separates concerns between the user interface layer and the AI agent processing layer. This design enables scalable, maintainable, and performant computer use agent interactions.

## High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        UI[Next.js Web App<br/>Port 3000]
        Browser[Browser<br/>User Interface]
    end
    
    subgraph "Server Layer"
        API[Next.js API Routes<br/>Proxy & Custom]
        Agent[LangGraph Agent Server<br/>Agent Processing]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API<br/>Language Models]
        Scrapybara[Scrapybara API<br/>VM Management]
        Supabase[Supabase Storage<br/>Screenshot Storage]
    end
    
    Browser --> UI
    UI --> API
    API --> Agent
    Agent --> OpenAI
    Agent --> Scrapybara
    Agent --> Supabase
    
    UI -.-> Agent
    Scrapybara -.-> UI
```

## Component Architecture

### 1. Next.js Web Application Layer

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/`

The web application layer serves as the primary user interface, built on Next.js 15.2.4 with React 19.0.0. This layer handles:

- **Static Asset Serving**: Public files, images, and client-side resources
- **Server-Side Rendering**: Initial page rendering and SEO optimization
- **API Route Handling**: Proxy to LangGraph agent and custom VM endpoints
- **Client-Side Hydration**: React application bootstrapping

#### Key Components:
- **Main Layout** (`layout.tsx`): Root component with providers and global styles
- **Page Component** (`page.tsx`): Primary application entry point
- **API Passthrough** (`api/[..._path]/route.ts`): LangGraph API proxy
- **VM Instance APIs** (`api/instance/`): Custom VM management endpoints

### 2. LangGraph Agent Server Layer

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/`

The agent server layer processes AI requests and manages computer use interactions. Built on LangGraph framework with custom UI generation capabilities.

#### Core Components:

**Agent Graph Definition** (`index.ts`):
```typescript
export const graph = createCua({
  nodeBeforeAction: beforeNode,
  nodeAfterAction: afterNode,
  stateModifier: GraphAnnotation,
  recursionLimit: 150,
  timeoutHours: 0.1,
  uploadScreenshot,
});
```

**State Management**:
```typescript
const GraphAnnotation = Annotation.Root({
  ...CUAAnnotation.spec,
  ui: Annotation<
    UIMessage[],
    UIMessage | RemoveUIMessage | (UIMessage | RemoveUIMessage)[]
  >({ default: () => [], reducer: uiMessageReducer }),
});
```

### 3. UI Component Generation System

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/`

The generative UI system dynamically creates components based on agent state and actions.

#### Component Mapping:
```typescript
const ComponentMap = {
  "computer-use-tool-output": ComputerUseToolOutput,
  "computer-use-tool-call": ComputerUseToolCall,
  "render-vm-button": RenderVMButton,
  instance: InstanceFrame,
} as const;
```

#### Component Lifecycle:
1. **Before Node Action**: Generate UI components based on pending actions
2. **After Node Action**: Update UI components with action results
3. **State Synchronization**: Maintain UI state consistency across updates

## Data Flow Architecture

### 1. Request Processing Flow

```mermaid
sequenceDiagram
    participant User
    participant NextJS as Next.js App
    participant API as API Routes
    participant Agent as LangGraph Agent
    participant VM as Scrapybara VM
    participant OpenAI as OpenAI API

    User->>NextJS: Enter command
    NextJS->>API: HTTP Request
    API->>Agent: Forward to LangGraph
    Agent->>OpenAI: Process with LLM
    OpenAI->>Agent: Response
    Agent->>VM: Execute computer action
    VM->>Agent: Screenshot & result
    Agent->>NextJS: Stream response
    NextJS->>User: Update UI
```

### 2. State Management Flow

```mermaid
graph LR
    subgraph "Client State"
        CS[Component State]
        PS[Provider State]
        US[URL State]
        LS[Local Storage]
    end
    
    subgraph "Server State"
        AS[Agent State]
        GS[Graph State]
        SS[Session State]
    end
    
    subgraph "External State"
        VS[VM State]
        DS[Database State]
    end
    
    CS <--> PS
    PS <--> AS
    AS <--> GS
    GS <--> VS
    AS <--> DS
    PS <--> US
    PS <--> LS
```

## Communication Patterns

### 1. WebSocket Streaming

**Implementation**: `@langchain/langgraph-sdk/react`

```typescript
const useTypedStream = useStream<StateType, {
  UpdateType: {
    messages?: Message[] | Message | string;
    ui?: (UIMessage | RemoveUIMessage)[] | UIMessage | RemoveUIMessage;
    streamUrl?: string;
    instanceId?: string;
    environment?: string;
  };
  CustomEventType: UIMessage | RemoveUIMessage;
}>;
```

**Features**:
- **Real-time Updates**: Immediate state synchronization
- **Optimistic Updates**: Client-side predictions with server reconciliation
- **Error Recovery**: Automatic reconnection and state restoration
- **Type Safety**: Full TypeScript integration

### 2. API Passthrough Pattern

**Implementation**: `langgraph-nextjs-api-passthrough`

```typescript
export const { GET, POST, PUT, PATCH, DELETE, OPTIONS, runtime } =
  initApiPassthrough({
    apiUrl: process.env.LANGGRAPH_API_URL,
    apiKey: process.env.LANGCHAIN_API_KEY,
    runtime: "edge",
  });
```

**Benefits**:
- **Seamless Integration**: Direct LangGraph API access
- **Authentication Handling**: Centralized API key management
- **Edge Runtime**: Optimal performance with edge deployment
- **CORS Management**: Proper cross-origin request handling

### 3. VM Instance Management

**Custom API Endpoints**:
- `POST /api/instance/status`: Check VM instance status
- `POST /api/instance/pause`: Pause VM instance
- `POST /api/instance/resume`: Resume VM instance
- `POST /api/instance/stop`: Terminate VM instance

**Status Polling Pattern**:
```typescript
const checkStatus = async () => {
  const response = await fetch(`/api/instance/status`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ instanceId }),
  });
  const data = await response.json();
  setStatus(data.status);
};
```

## State Architecture

### 1. Client-Side State Management

**Provider Hierarchy**:
```typescript
<ThreadProvider>
  <StreamProvider>
    <Thread />
  </StreamProvider>
</ThreadProvider>
```

**State Layers**:
- **Thread State**: Conversation history and metadata
- **Stream State**: Real-time communication state
- **UI State**: Component visibility and interactions
- **URL State**: Persistent navigation state

### 2. Server-Side State Management

**Graph State Definition**:
```typescript
type GraphState = typeof GraphAnnotation.State & CUAState;
```

**State Components**:
- **Messages**: Conversation history
- **UI Components**: Dynamic UI element state
- **VM Instance**: VM connection details
- **Tool Outputs**: Computer use action results

### 3. External State Synchronization

**VM State Management**:
- **Instance ID**: Unique VM identifier
- **Stream URL**: Real-time VM access URL
- **Status**: Running, paused, or terminated
- **Screenshots**: Captured screen images

## Security Architecture

### 1. Authentication & Authorization

**API Key Management**:
- **Environment Variables**: Secure key storage
- **Server-Side Only**: Keys never exposed to client
- **Rotation Support**: Easy key updates

**Service Authentication**:
- **OpenAI**: API key authentication
- **Scrapybara**: API key authentication
- **Supabase**: Service role authentication

### 2. Data Security

**Screenshot Processing**:
```typescript
async function uploadScreenshot(screenshot: string): Promise<string> {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseApiKey = process.env.SUPABASE_API_KEY;
  
  const client = createClient(supabaseUrl, supabaseApiKey);
  const fileName = `${uuidv4()}.png`;
  
  const { data } = await client.storage
    .from("cua-screenshots")
    .createSignedUrl(fileName, 60 * 60 * 24 * 90); // 90 days
  
  return data.signedUrl;
}
```

**Security Features**:
- **Signed URLs**: Time-limited access to screenshots
- **UUID Generation**: Unique identifiers for resources
- **Base64 Processing**: Secure image handling
- **Storage Isolation**: Separate buckets for different data types

### 3. Network Security

**Edge Runtime Configuration**:
```typescript
export const runtime = "edge";
```

**Benefits**:
- **Reduced Attack Surface**: Minimal runtime environment
- **Geographic Distribution**: Edge deployment for performance
- **Automatic Scaling**: Serverless scaling capabilities

## Performance Architecture

### 1. Client-Side Optimization

**Component Optimization**:
- **React Suspense**: Lazy loading of components
- **Memoization**: Prevent unnecessary re-renders
- **Code Splitting**: Dynamic imports for large components
- **Image Optimization**: Next.js image optimization

**State Optimization**:
- **Selective Updates**: Only update changed state
- **Debounced Actions**: Prevent excessive API calls
- **Local Caching**: Client-side cache for frequently accessed data

### 2. Server-Side Optimization

**Agent Processing**:
- **Recursion Limits**: Prevent infinite loops
- **Timeout Configuration**: Prevent hanging requests
- **Streaming Responses**: Immediate user feedback
- **Connection Pooling**: Efficient resource utilization

**API Optimization**:
- **Edge Runtime**: Minimal cold start time
- **Response Caching**: Cache static responses
- **Connection Reuse**: Persistent connections to external services

### 3. External Service Optimization

**VM Management**:
- **Status Polling**: Efficient instance monitoring
- **Screenshot Caching**: Reduce storage API calls
- **Connection Reuse**: Persistent VM connections

## Scalability Architecture

### 1. Horizontal Scaling

**Stateless Design**:
- **Session Management**: External session storage
- **Load Balancing**: Multiple instance support
- **Database Scaling**: Separate data layer

**Microservice Separation**:
- **UI Service**: Next.js application
- **Agent Service**: LangGraph server
- **VM Service**: Scrapybara API
- **Storage Service**: Supabase

### 2. Vertical Scaling

**Resource Optimization**:
- **Memory Management**: Efficient state handling
- **CPU Optimization**: Optimized algorithms
- **Network Optimization**: Minimal data transfer

**Caching Strategies**:
- **Browser Cache**: Static asset caching
- **CDN Cache**: Geographic content distribution
- **Application Cache**: In-memory caching

### 3. Auto-scaling Configuration

**Serverless Architecture**:
- **Function Scaling**: Automatic function scaling
- **Edge Distribution**: Global edge deployment
- **Cost Optimization**: Pay-per-use model

## Monitoring & Observability

### 1. Application Monitoring

**Error Tracking**:
- **Client-Side Errors**: Browser error reporting
- **Server-Side Errors**: API error logging
- **External Service Errors**: Service failure detection

**Performance Monitoring**:
- **Response Times**: API response monitoring
- **UI Performance**: Client-side performance metrics
- **Resource Usage**: Memory and CPU monitoring

### 2. Business Metrics

**Usage Analytics**:
- **User Interactions**: Command tracking
- **VM Usage**: Instance utilization
- **Feature Usage**: Component usage statistics

**Success Metrics**:
- **Completion Rates**: Task completion tracking
- **Error Rates**: Failure rate monitoring
- **Performance Metrics**: Response time tracking

This architecture provides a robust foundation for scalable computer use agent interactions while maintaining clear separation of concerns and optimal performance characteristics.