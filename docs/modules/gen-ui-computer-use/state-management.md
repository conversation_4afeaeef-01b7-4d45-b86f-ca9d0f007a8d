# Gen-UI Computer Use - State Management Documentation

## State Management Overview

The Gen-UI Computer Use system implements a sophisticated multi-layered state management architecture that combines React Context API, LangGraph state management, URL-based persistence, and local storage. This hybrid approach ensures optimal performance, data consistency, and user experience across all components.

## State Architecture Layers

### 1. State Management Hierarchy

```mermaid
graph TB
    subgraph "Client State"
        URLState[URL State<br/>nuqs]
        LocalState[Local Storage<br/>Custom Implementation]
        ReactContext[React Context<br/>Provider Pattern]
        ComponentState[Component State<br/>useState/useReducer]
    end
    
    subgraph "Server State"
        LangGraphState[LangGraph State<br/>Agent State]
        UIState[UI State<br/>Dynamic Components]
        ThreadState[Thread State<br/>Conversation Data]
    end
    
    subgraph "External State"
        VMState[VM State<br/>Scrapybara API]
        StorageState[Storage State<br/>Supabase]
        APIState[API State<br/>External Services]
    end
    
    URLState --> ReactContext
    LocalState --> ReactContext
    ReactContext --> ComponentState
    LangGraphState --> ReactContext
    UIState --> ComponentState
    ThreadState --> LocalState
    VMState --> LangGraphState
    StorageState --> LangGraphState
    APIState --> LangGraphState
```

### 2. State Flow Patterns

**Data Flow Direction**:
- **Downward**: State passed from providers to components
- **Upward**: Actions dispatched from components to providers
- **Sideways**: State synchronization between peer components
- **External**: State updates from external services

**State Synchronization**:
- **Immediate**: Real-time updates through WebSocket
- **Optimistic**: Client-side predictions with server reconciliation
- **Lazy**: On-demand state loading and caching
- **Persistent**: Long-term state storage and retrieval

## LangGraph State Management

### 1. Graph State Definition

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/index.ts`

**State Annotation**:
```typescript
import { Annotation } from "@langchain/langgraph";
import { uiMessageReducer } from "@langchain/langgraph-sdk/react-ui";
import { CUAAnnotation } from "@langchain/langgraph-cua";

const GraphAnnotation = Annotation.Root({
  ...CUAAnnotation.spec,
  ui: Annotation<
    UIMessage[],
    UIMessage | RemoveUIMessage | (UIMessage | RemoveUIMessage)[]
  >({ 
    default: () => [], 
    reducer: uiMessageReducer 
  }),
});

type GraphState = typeof GraphAnnotation.State & CUAState;
```

**State Components**:
- **Messages**: Conversation history array
- **UI Components**: Dynamic UI element state
- **VM Instance**: Virtual machine connection details
- **Tool Outputs**: Computer use action results
- **Agent Context**: Current agent execution context

### 2. UI Message Reducer

**Purpose**: Manages dynamic UI component state

**Reducer Implementation**:
```typescript
import { uiMessageReducer } from "@langchain/langgraph-sdk/react-ui";

// State structure
interface UIMessage {
  id: string;
  name: string;
  props: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp: number;
}

interface RemoveUIMessage {
  type: "remove";
  id: string;
}

// Reducer usage in state annotation
const GraphAnnotation = Annotation.Root({
  ui: Annotation<
    UIMessage[],
    UIMessage | RemoveUIMessage | (UIMessage | RemoveUIMessage)[]
  >({ 
    default: () => [], 
    reducer: uiMessageReducer 
  }),
});
```

**UI State Operations**:
```typescript
// Add UI component
const ui = typedUi<typeof ComponentMap>(config);
ui.push({
  name: "computer-use-tool-call",
  props: {
    toolCallId: tc.id,
    action: tc.action,
  },
}, {
  message: lastMessage,
});

// Update existing component
const existingComponent = state.ui.find(
  (message) => message.name === "instance"
);
if (existingComponent) {
  ui.delete(existingComponent.id);
  ui.push({
    name: "instance",
    props: {
      instanceId: state.instanceId,
      streamUrl: state.streamUrl,
    },
  });
}

// Remove component
const componentToRemove = state.ui.find(
  (message) => message.name === "render-vm-button"
);
if (componentToRemove) {
  ui.delete(componentToRemove.id);
}
```

### 3. State Persistence in Graph

**Before Node Hook**:
```typescript
async function beforeNode(
  state: GraphState,
  config: LangGraphRunnableConfig,
): Promise<CUAUpdate> {
  const ui = typedUi<typeof ComponentMap>(config);
  const lastMessage = state.messages[state.messages.length - 1];
  
  // Persist VM instance state
  if (state.instanceId && state.streamUrl) {
    const instanceFrame = state.ui.find(
      (message) => message.name === "instance"
    );
    
    if (!instanceFrame) {
      ui.push({
        name: "instance",
        props: {
          instanceId: state.instanceId,
          streamUrl: state.streamUrl,
        },
      });
    }
  }

  // Persist tool call state
  const toolCalls = getToolOutputs(lastMessage);
  if (toolCalls?.length) {
    toolCalls.map((tc) => {
      ui.push({
        name: "computer-use-tool-call",
        props: {
          toolCallId: tc.id,
          action: tc.action,
        },
      });
    });
  }

  return {};
}
```

**After Node Hook**:
```typescript
async function afterNode(
  state: GraphState,
  config: LangGraphRunnableConfig,
): Promise<CUAUpdate> {
  const ui = typedUi<typeof ComponentMap>(config);
  const lastMessage = state.messages[state.messages.length - 1];
  
  // Persist tool output state
  if (isComputerCallToolMessage(lastMessage)) {
    ui.push({
      name: "computer-use-tool-output",
      props: {
        toolCallId: lastMessage.tool_call_id,
        screenshot: lastMessage.content as string,
      },
    });
  }

  return {};
}
```

## React Context State Management

### 1. Stream Provider

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/providers/Stream.tsx`

**Purpose**: Manages real-time streaming state with LangGraph agent

**State Type Definition**:
```typescript
export type StateType = {
  messages: Message[];
  ui?: UIMessage[];
  streamUrl?: string;
  instanceId?: string;
  environment?: string;
};

type StreamContextType = ReturnType<typeof useTypedStream>;
```

**Provider Implementation**:
```typescript
const StreamContext = createContext<StreamContextType | undefined>(undefined);

export const StreamProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [threadId, setThreadId] = useQueryState("threadId");
  const [userId, setUserId] = useState<string>();
  const { getThreads, setThreads } = useThreads();
  
  const streamValue = useTypedStream({
    apiUrl: process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:3000/api",
    assistantId: "agent",
    threadId: threadId ?? null,
    onCustomEvent: (event, options) => {
      options.mutate((prev) => {
        const ui = uiMessageReducer(prev.ui ?? [], event);
        return { ...prev, ui };
      });
    },
    onThreadId: (id) => {
      setThreadId(id);
      setIsShowingInstanceFrame(null);
      
      // Update thread metadata
      if (userId) {
        client.threads.update(id, {
          metadata: { user_id: userId },
        }).then(() => {
          // Re-fetch threads after update
          sleep(4000).then(() =>
            getThreads(userId).then(setThreads).catch(console.error)
          );
        });
      }
    },
  });

  return (
    <StreamContext.Provider value={streamValue}>
      {children}
    </StreamContext.Provider>
  );
};
```

**Context Hook**:
```typescript
export const useStreamContext = (): StreamContextType => {
  const context = useContext(StreamContext);
  if (context === undefined) {
    throw new Error("useStreamContext must be used within a StreamProvider");
  }
  return context;
};
```

### 2. Thread Provider

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/providers/Thread.tsx`

**Purpose**: Manages conversation threads and history

**State Interface**:
```typescript
interface Thread {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  metadata: {
    user_id: string;
    last_message?: string;
    message_count?: number;
  };
}

interface ThreadContextType {
  threads: Thread[];
  setThreads: (threads: Thread[]) => void;
  getThreads: (userId: string) => Promise<Thread[]>;
  deleteThread: (threadId: string) => Promise<void>;
  updateThread: (threadId: string, updates: Partial<Thread>) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}
```

**Provider Implementation**:
```typescript
export const ThreadProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [threads, setThreads] = useState<Thread[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const client = createClient(
    process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:3000/api"
  );

  const getThreads = useCallback(async (userId: string): Promise<Thread[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await client.threads.search({
        metadata: { user_id: userId },
        limit: 50,
      });
      
      const threadList = response.threads.map(thread => ({
        id: thread.thread_id,
        title: thread.metadata.title || "New Conversation",
        created_at: thread.created_at,
        updated_at: thread.updated_at,
        metadata: thread.metadata,
      }));
      
      setThreads(threadList);
      return threadList;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch threads";
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [client]);

  const deleteThread = useCallback(async (threadId: string): Promise<void> => {
    try {
      await client.threads.delete(threadId);
      setThreads(prev => prev.filter(thread => thread.id !== threadId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete thread";
      setError(errorMessage);
      throw err;
    }
  }, [client]);

  const updateThread = useCallback(async (
    threadId: string, 
    updates: Partial<Thread>
  ): Promise<void> => {
    try {
      await client.threads.update(threadId, {
        metadata: { ...updates.metadata },
      });
      
      setThreads(prev => 
        prev.map(thread => 
          thread.id === threadId 
            ? { ...thread, ...updates }
            : thread
        )
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update thread";
      setError(errorMessage);
      throw err;
    }
  }, [client]);

  const value: ThreadContextType = {
    threads,
    setThreads,
    getThreads,
    deleteThread,
    updateThread,
    isLoading,
    error,
  };

  return (
    <ThreadContext.Provider value={value}>
      {children}
    </ThreadContext.Provider>
  );
};
```

## URL State Management

### 1. NUQS Integration

**Package**: `nuqs` v2.4.1

**Purpose**: URL-based state persistence for navigation and sharing

**Key Features**:
- **Type Safety**: Typed URL state with validation
- **SSR Support**: Server-side rendering compatibility
- **Persistence**: State survives page reloads
- **Sharing**: Shareable URLs with state

**Implementation Examples**:
```typescript
import { useQueryState, parseAsBoolean, parseAsString } from "nuqs";

// Thread ID state
const [threadId, setThreadId] = useQueryState("threadId");

// Boolean state with default
const [chatHistoryOpen, setChatHistoryOpen] = useQueryState(
  "chatHistoryOpen",
  parseAsBoolean.withDefault(false)
);

// Instance frame visibility
const [isShowingInstanceFrame, setIsShowingInstanceFrame] = useQueryState(
  "isShowingInstanceFrame",
  parseAsBoolean
);

// Search query state
const [searchQuery, setSearchQuery] = useQueryState(
  "search",
  parseAsString.withDefault("")
);
```

### 2. URL State Patterns

**State Synchronization**:
```typescript
// Sync URL state with component state
useEffect(() => {
  if (threadId) {
    // Load thread data when URL changes
    loadThreadData(threadId);
  }
}, [threadId]);

// Update URL when component state changes
const handleThreadSelect = (newThreadId: string) => {
  setThreadId(newThreadId);
  setIsShowingInstanceFrame(null); // Reset instance frame
};
```

**Navigation Handling**:
```typescript
// Handle navigation between threads
const newThread = () => {
  setThreadId(null);
  setIsShowingInstanceFrame(null);
  setSearchQuery("");
};

// Handle back/forward navigation
useEffect(() => {
  const handlePopState = () => {
    // URL state automatically updates
    // Component state will sync via useEffect
  };
  
  window.addEventListener("popstate", handlePopState);
  return () => window.removeEventListener("popstate", handlePopState);
}, []);
```

## Local Storage Management

### 1. Storage Utilities

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/lib/local-storage.ts`

**Purpose**: Type-safe local storage operations

**Implementation**:
```typescript
export const USER_ID_KEY = "gen-ui-computer-use-user-id";
export const THREAD_CACHE_KEY = "gen-ui-computer-use-thread-cache";
export const PREFERENCES_KEY = "gen-ui-computer-use-preferences";

export interface UserPreferences {
  theme: "light" | "dark" | "system";
  chatHistoryOpen: boolean;
  instanceFrameSize: "small" | "medium" | "large";
  notifications: boolean;
}

export const getItem = <T>(key: string, defaultValue?: T): T | null => {
  if (typeof window === "undefined") return defaultValue ?? null;
  
  try {
    const item = window.localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue ?? null;
  } catch (error) {
    console.error(`Error reading localStorage key "${key}":`, error);
    return defaultValue ?? null;
  }
};

export const setItem = <T>(key: string, value: T): void => {
  if (typeof window === "undefined") return;
  
  try {
    window.localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error setting localStorage key "${key}":`, error);
  }
};

export const removeItem = (key: string): void => {
  if (typeof window === "undefined") return;
  
  try {
    window.localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing localStorage key "${key}":`, error);
  }
};

export const clear = (): void => {
  if (typeof window === "undefined") return;
  
  try {
    window.localStorage.clear();
  } catch (error) {
    console.error("Error clearing localStorage:", error);
  }
};
```

### 2. User Session Management

**User ID Persistence**:
```typescript
// User ID initialization
useEffect(() => {
  if (typeof window === "undefined" || userId) return;
  
  const storedUserId = getItem(USER_ID_KEY);
  if (storedUserId) {
    setUserId(storedUserId);
  } else {
    const newUserId = uuidv4();
    setUserId(newUserId);
    setItem(USER_ID_KEY, newUserId);
  }
}, [userId]);
```

**Preferences Management**:
```typescript
const useUserPreferences = () => {
  const [preferences, setPreferences] = useState<UserPreferences>(() => 
    getItem(PREFERENCES_KEY, {
      theme: "system",
      chatHistoryOpen: false,
      instanceFrameSize: "medium",
      notifications: true,
    })
  );

  const updatePreferences = useCallback((updates: Partial<UserPreferences>) => {
    setPreferences(prev => {
      const newPreferences = { ...prev, ...updates };
      setItem(PREFERENCES_KEY, newPreferences);
      return newPreferences;
    });
  }, []);

  return { preferences, updatePreferences };
};
```

## Component State Management

### 1. useState Patterns

**Simple State**:
```typescript
const [isLoading, setIsLoading] = useState(false);
const [error, setError] = useState<string | null>(null);
const [data, setData] = useState<Data | null>(null);
```

**Complex State Objects**:
```typescript
interface VMInstanceState {
  instanceId: string | null;
  status: "unknown" | "running" | "paused" | "terminated";
  streamUrl: string | null;
  screenshot: string | null;
  isLoading: boolean;
  error: string | null;
}

const [vmState, setVMState] = useState<VMInstanceState>({
  instanceId: null,
  status: "unknown",
  streamUrl: null,
  screenshot: null,
  isLoading: false,
  error: null,
});

// Update specific fields
const updateVMState = (updates: Partial<VMInstanceState>) => {
  setVMState(prev => ({ ...prev, ...updates }));
};
```

### 2. useReducer Patterns

**Action Types**:
```typescript
type VMAction = 
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "SET_INSTANCE"; payload: { instanceId: string; streamUrl: string } }
  | { type: "SET_STATUS"; payload: VMInstanceState["status"] }
  | { type: "SET_SCREENSHOT"; payload: string | null }
  | { type: "RESET" };
```

**Reducer Implementation**:
```typescript
const vmReducer = (state: VMInstanceState, action: VMAction): VMInstanceState => {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };
    
    case "SET_ERROR":
      return { ...state, error: action.payload, isLoading: false };
    
    case "SET_INSTANCE":
      return { 
        ...state, 
        instanceId: action.payload.instanceId,
        streamUrl: action.payload.streamUrl,
        status: "running",
        error: null 
      };
    
    case "SET_STATUS":
      return { ...state, status: action.payload };
    
    case "SET_SCREENSHOT":
      return { ...state, screenshot: action.payload };
    
    case "RESET":
      return {
        instanceId: null,
        status: "unknown",
        streamUrl: null,
        screenshot: null,
        isLoading: false,
        error: null,
      };
    
    default:
      return state;
  }
};

// Usage in component
const [vmState, dispatch] = useReducer(vmReducer, initialState);
```

### 3. Custom Hooks for State

**Instance Actions Hook**:
```typescript
export function useInstanceActions({ instanceId }: { instanceId: string }) {
  const [state, dispatch] = useReducer(vmReducer, initialState);

  const handleStop = useCallback(async () => {
    dispatch({ type: "SET_LOADING", payload: true });
    
    try {
      await fetch("/api/instance/stop", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });
      
      dispatch({ type: "SET_STATUS", payload: "terminated" });
    } catch (error) {
      dispatch({ 
        type: "SET_ERROR", 
        payload: error instanceof Error ? error.message : "Failed to stop instance" 
      });
    }
  }, [instanceId]);

  const handlePause = useCallback(async () => {
    try {
      await fetch("/api/instance/pause", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });
      
      dispatch({ type: "SET_STATUS", payload: "paused" });
    } catch (error) {
      dispatch({ 
        type: "SET_ERROR", 
        payload: error instanceof Error ? error.message : "Failed to pause instance" 
      });
    }
  }, [instanceId]);

  return {
    ...state,
    handleStop,
    handlePause,
    dispatch,
  };
}
```

## State Synchronization Patterns

### 1. Optimistic Updates

**Pattern Implementation**:
```typescript
const submitMessage = async (message: string) => {
  const optimisticMessage: Message = {
    id: uuidv4(),
    type: "human",
    content: message,
    created_at: new Date().toISOString(),
  };

  // Optimistic update
  setMessages(prev => [...prev, optimisticMessage]);

  try {
    const response = await stream.submit(
      { messages: [optimisticMessage] },
      {
        optimisticValues: (prev) => ({
          ...prev,
          messages: [...(prev.messages ?? []), optimisticMessage],
        }),
      }
    );
    
    // Server response will update state
  } catch (error) {
    // Rollback optimistic update
    setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
    setError("Failed to send message");
  }
};
```

### 2. State Reconciliation

**Conflict Resolution**:
```typescript
const reconcileState = (clientState: State, serverState: State): State => {
  // Merge strategies based on data type
  return {
    messages: serverState.messages, // Server is authoritative
    ui: mergeUIState(clientState.ui, serverState.ui),
    instanceId: serverState.instanceId || clientState.instanceId,
    streamUrl: serverState.streamUrl || clientState.streamUrl,
    userPreferences: clientState.userPreferences, // Client is authoritative
  };
};

const mergeUIState = (clientUI: UIMessage[], serverUI: UIMessage[]): UIMessage[] => {
  const merged = [...serverUI];
  
  // Add client-only UI elements that haven't been synced
  clientUI.forEach(clientMsg => {
    if (!serverUI.find(serverMsg => serverMsg.id === clientMsg.id)) {
      merged.push(clientMsg);
    }
  });
  
  return merged.sort((a, b) => a.timestamp - b.timestamp);
};
```

### 3. Error Recovery

**State Recovery Patterns**:
```typescript
const useErrorRecovery = () => {
  const [lastKnownGoodState, setLastKnownGoodState] = useState<State | null>(null);
  
  const saveCheckpoint = useCallback((state: State) => {
    setLastKnownGoodState(state);
  }, []);
  
  const recoverFromError = useCallback(() => {
    if (lastKnownGoodState) {
      // Restore to last known good state
      restoreState(lastKnownGoodState);
    } else {
      // Reset to initial state
      resetToInitialState();
    }
  }, [lastKnownGoodState]);
  
  return { saveCheckpoint, recoverFromError };
};
```

## Performance Optimization

### 1. Memoization

**Component Memoization**:
```typescript
const MemoizedComponent = React.memo(({ data, onAction }) => {
  return <ExpensiveComponent data={data} onAction={onAction} />;
}, (prevProps, nextProps) => {
  // Custom comparison
  return prevProps.data.id === nextProps.data.id;
});
```

**Value Memoization**:
```typescript
const expensiveValue = useMemo(() => {
  return processLargeDataset(data);
}, [data]);

const memoizedCallback = useCallback((arg: string) => {
  return processArgument(arg);
}, [dependency]);
```

### 2. State Slicing

**Selective State Updates**:
```typescript
const useMessageSlice = (messageId: string) => {
  const { messages } = useStreamContext();
  
  return useMemo(() => {
    return messages.find(m => m.id === messageId);
  }, [messages, messageId]);
};
```

### 3. Lazy Loading

**Lazy State Initialization**:
```typescript
const [expensiveState, setExpensiveState] = useState(() => {
  return computeExpensiveInitialState();
});
```

**Lazy Effect Execution**:
```typescript
useEffect(() => {
  let cancelled = false;
  
  const loadData = async () => {
    const data = await fetchExpensiveData();
    if (!cancelled) {
      setData(data);
    }
  };
  
  loadData();
  
  return () => {
    cancelled = true;
  };
}, [dependency]);
```

This comprehensive state management system ensures optimal performance, data consistency, and user experience across all components of the Gen-UI Computer Use application.