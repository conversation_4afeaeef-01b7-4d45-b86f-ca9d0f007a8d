# Gen-UI Computer Use - Project Overview

## Executive Summary

The **Gen-UI Computer Use** project is a sophisticated web application that provides a generative user interface for interacting with Computer Use Agents (CUA) through the `@langchain/langgraph-cua` prebuilt package. This system enables users to control and monitor virtual machines through natural language commands while providing real-time visual feedback through an innovative dual-interface design.

## Project Architecture

### Core Philosophy
- **Generative UI**: Dynamic user interface generation based on agent state and actions
- **Real-time Interaction**: Seamless streaming communication between user, agent, and VM
- **Computer Use Agent Integration**: Direct integration with LangGraph's Computer Use Agent framework
- **Dual-Server Architecture**: Separate Next.js web server and LangGraph agent server

## Key Features

### 1. Computer Use Agent Integration
- **LangGraph CUA**: Built on `@langchain/langgraph-cua` v0.0.5
- **OpenAI Model**: Uses OpenAI's latest models for computer use capabilities
- **Scrapybara VM**: Integrates with Scrapybara API for VM management and control
- **Screenshot Processing**: Automatic screenshot capture and processing with Supabase storage

### 2. Generative UI Components
- **Dynamic Component Rendering**: Components generated based on agent state
- **Computer Use Tool Visualization**: Real-time tool call and output display
- **VM Instance Management**: Interactive VM control through UI components
- **State-Driven Interface**: UI adapts to agent actions and VM status

### 3. Real-time Communication
- **WebSocket Streaming**: Live communication between frontend and LangGraph agent
- **Server-Sent Events**: Continuous state updates from agent to UI
- **Optimistic Updates**: Immediate UI feedback with server reconciliation
- **Error Handling**: Robust error recovery and user feedback

## Technical Stack

### Frontend Framework
- **Next.js 15.2.4**: React-based web framework with App Router
- **React 19.0.0**: Latest React with concurrent features
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first CSS framework with custom design system

### State Management
- **LangGraph SDK**: `@langchain/langgraph-sdk` for agent communication
- **React Context**: State management through providers
- **NUQS**: URL state management for persistent UI state
- **Local Storage**: Client-side persistence for user preferences

### UI Components
- **Radix UI**: Accessible component primitives
- **Framer Motion**: Animation and gesture library
- **Lucide React**: Icon library with consistent design
- **Tailwind Animate**: CSS animations integrated with Tailwind

### Agent Framework
- **LangGraph**: `@langchain/langgraph` v0.2.60 for agent orchestration
- **LangChain Core**: `@langchain/core` v0.3.43 for language model integration
- **Computer Use Agent**: `@langchain/langgraph-cua` v0.0.5 for computer control

## Project Structure

```
gen-ui-computer-use/
├── src/
│   ├── agent/                     # LangGraph agent implementation
│   │   ├── index.ts              # Main agent graph definition
│   │   └── ui/                   # Agent UI components
│   │       ├── computer-use-tool-call.tsx
│   │       ├── computer-use-tool-output.tsx
│   │       ├── render-vm-button.tsx
│   │       └── instance/         # VM instance components
│   ├── app/                      # Next.js App Router
│   │   ├── api/                  # API routes
│   │   │   ├── [..._path]/       # LangGraph API passthrough
│   │   │   └── instance/         # VM instance API endpoints
│   │   ├── layout.tsx            # Root layout
│   │   └── page.tsx              # Main page component
│   ├── components/               # Reusable UI components
│   │   ├── thread/               # Chat thread components
│   │   └── ui/                   # Base UI components
│   ├── providers/                # React context providers
│   │   ├── Stream.tsx            # Streaming state management
│   │   ├── Thread.tsx            # Thread management
│   │   └── client.ts             # LangGraph client configuration
│   ├── hooks/                    # Custom React hooks
│   └── lib/                      # Utility functions and constants
├── public/                       # Static assets
├── langgraph.json               # LangGraph configuration
├── package.json                 # Dependencies and scripts
└── tailwind.config.js           # Tailwind CSS configuration
```

## Core Workflows

### 1. User Interaction Flow
1. User enters natural language command in chat interface
2. Message sent to LangGraph agent through streaming API
3. Agent processes command and generates computer use actions
4. VM receives and executes commands through Scrapybara API
5. Screenshots and results streamed back to UI in real-time
6. Generative UI components update based on agent state

### 2. VM Management Flow
1. Agent creates VM instance through Scrapybara API
2. Instance details (ID, stream URL) passed to UI state
3. UI renders VM control components (pause, resume, terminate)
4. Real-time status monitoring through polling API
5. User can interact with VM through iframe or screenshot overlay
6. Instance state persisted in URL and context

### 3. Screenshot Processing Flow
1. Agent captures screenshots during computer use actions
2. Screenshots processed and converted to base64 format
3. Images uploaded to Supabase storage with signed URLs
4. UI displays screenshots with tool call context
5. Screenshots cached for performance and offline viewing

## Integration Points

### LangGraph Agent Server
- **Port**: Configurable (default via LangGraph CLI)
- **Protocol**: HTTP/WebSocket
- **Authentication**: API key based
- **Endpoints**: Full LangGraph API surface

### Next.js Web Server
- **Port**: 3000 (development), configurable (production)
- **API Routes**: Proxy to LangGraph + custom VM endpoints
- **Static Assets**: Served through Next.js
- **Environment**: Server-side and client-side configuration

### Scrapybara VM Service
- **API**: RESTful HTTP API
- **Authentication**: API key based
- **Capabilities**: VM creation, control, screenshot capture
- **Streaming**: Real-time VM screen sharing

### Supabase Storage
- **Purpose**: Screenshot storage and retrieval
- **Authentication**: Service role key
- **Bucket**: `cua-screenshots`
- **Access**: Signed URLs with 90-day expiration

## Development Workflow

### Local Development
1. **Environment Setup**: Configure `.env` with required API keys
2. **Install Dependencies**: `pnpm install`
3. **Start Web Server**: `pnpm run dev` (port 3000)
4. **Start Agent Server**: `pnpm run agent` (LangGraph CLI)
5. **Access Application**: `http://localhost:3000`

### Production Deployment
1. **Build Application**: `pnpm run build`
2. **Environment Variables**: Configure production environment
3. **Deploy Web Server**: Next.js deployment (Vercel, etc.)
4. **Deploy Agent Server**: LangGraph Cloud or self-hosted
5. **Configure Networking**: Ensure communication between services

## Key Design Decisions

### 1. Dual-Server Architecture
- **Separation of Concerns**: Web UI separate from agent logic
- **Scalability**: Independent scaling of UI and agent components
- **Development**: Parallel development of frontend and backend

### 2. Generative UI Pattern
- **Dynamic Components**: UI components generated based on agent state
- **Type Safety**: Full TypeScript integration with component mapping
- **Performance**: Efficient rendering with React optimizations

### 3. Real-time Communication
- **WebSocket Streaming**: Low-latency communication
- **State Synchronization**: Consistent state across client and server
- **Error Recovery**: Robust handling of connection issues

### 4. VM Integration Strategy
- **Iframe Embedding**: Direct VM interaction through iframe
- **Screenshot Overlay**: Fallback for non-interactive viewing
- **State Management**: Persistent VM state across sessions

## Future Considerations

### Scalability Enhancements
- **Multi-tenant Support**: User isolation and resource management
- **Load Balancing**: Horizontal scaling of agent servers
- **Caching Strategies**: Redis for session and state caching

### Feature Expansions
- **Multi-VM Support**: Parallel VM instances for complex workflows
- **Collaboration Features**: Shared sessions and real-time collaboration
- **Analytics Integration**: Usage tracking and performance monitoring

### Security Improvements
- **Authentication System**: User authentication and authorization
- **VM Isolation**: Enhanced security boundaries
- **Audit Logging**: Comprehensive action logging for compliance

## Success Metrics

### Technical Performance
- **Response Time**: < 200ms for UI interactions
- **Streaming Latency**: < 100ms for agent communication
- **VM Control Latency**: < 500ms for VM operations
- **Screenshot Processing**: < 2s for capture and display

### User Experience
- **Interface Responsiveness**: Smooth animations and transitions
- **Error Recovery**: Graceful handling of failures
- **Accessibility**: WCAG compliance through Radix UI
- **Mobile Compatibility**: Responsive design for mobile devices

This project represents a sophisticated integration of modern web technologies with advanced AI agent capabilities, providing a unique platform for natural language computer control through an intuitive generative user interface.