# Gen-UI Computer Use - Streaming System Documentation

## Streaming System Overview

The Gen-UI Computer Use system implements a sophisticated real-time streaming architecture that enables seamless bidirectional communication between the frontend interface and the LangGraph agent server. This system provides low-latency updates, optimistic UI rendering, and robust error recovery mechanisms.

## Streaming Architecture

### 1. Communication Flow

```mermaid
sequenceDiagram
    participant UI as Frontend UI
    participant Stream as Stream Provider
    participant SDK as LangGraph SDK
    participant Agent as Agent Server
    participant VM as VM Instance
    participant Storage as Supabase Storage

    UI->>Stream: User Input
    Stream->>SDK: Submit Message
    SDK->>Agent: WebSocket Connection
    Agent->>Agent: Process Message
    Agent->>VM: Execute Action
    VM->>Agent: Screenshot/Result
    Agent->>Storage: Upload Screenshot
    Storage->>Agent: Signed URL
    Agent->>SDK: Stream Update
    SDK->>Stream: State Update
    Stream->>UI: Render Update
```

### 2. Technology Stack

**Core Technologies**:
- **LangGraph SDK**: `@langchain/langgraph-sdk` v0.0.61
- **WebSocket Protocol**: Real-time bidirectional communication
- **React Streaming**: `useStream` hook for state management
- **UI Message Reducer**: Dynamic component state management
- **Optimistic Updates**: Immediate UI feedback

**Communication Protocols**:
- **WebSocket**: Primary real-time communication
- **HTTP/REST**: Fallback and initial connections
- **Server-Sent Events**: Alternative streaming method
- **Polling**: Fallback for connection issues

## Stream Provider Implementation

### 1. Stream Provider Setup

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/providers/Stream.tsx`

**Core Implementation**:
```typescript
import { useStream } from "@langchain/langgraph-sdk/react";
import { uiMessageReducer } from "@langchain/langgraph-sdk/react-ui";

export type StateType = {
  messages: Message[];
  ui?: UIMessage[];
  streamUrl?: string;
  instanceId?: string;
  environment?: string;
};

const useTypedStream = useStream<
  StateType,
  {
    UpdateType: {
      messages?: Message[] | Message | string;
      ui?: (UIMessage | RemoveUIMessage)[] | UIMessage | RemoveUIMessage;
      streamUrl?: string;
      instanceId?: string;
      environment?: string;
    };
    CustomEventType: UIMessage | RemoveUIMessage;
  }
>;

const StreamSession = ({
  children,
  apiUrl,
  assistantId,
}: {
  children: ReactNode;
  apiUrl: string;
  assistantId: string;
}) => {
  const [threadId, setThreadId] = useQueryState("threadId");
  const [userId, setUserId] = useState<string>();
  const { getThreads, setThreads } = useThreads();
  
  const streamValue = useTypedStream({
    apiUrl,
    assistantId,
    threadId: threadId ?? null,
    onCustomEvent: (event, options) => {
      options.mutate((prev) => {
        const ui = uiMessageReducer(prev.ui ?? [], event);
        return { ...prev, ui };
      });
    },
    onThreadId: (id) => {
      setThreadId(id);
      setIsShowingInstanceFrame(null);
      
      // Update thread metadata
      if (userId) {
        client.threads.update(id, {
          metadata: { user_id: userId },
        }).then(() => {
          sleep(4000).then(() =>
            getThreads(userId).then(setThreads).catch(console.error)
          );
        });
      }
    },
  });

  return (
    <StreamContext.Provider value={streamValue}>
      {children}
    </StreamContext.Provider>
  );
};
```

### 2. Stream State Management

**State Type Definition**:
```typescript
interface StreamState {
  messages: Message[];
  ui: UIMessage[];
  streamUrl?: string;
  instanceId?: string;
  environment?: string;
  isLoading: boolean;
  error?: Error;
  values: StateType;
  isStreaming: boolean;
  connectionStatus: 'connected' | 'disconnected' | 'reconnecting';
}
```

**Update Patterns**:
```typescript
// Message updates
const handleMessageUpdate = (
  update: Message[] | Message | string
) => {
  if (Array.isArray(update)) {
    setMessages(update);
  } else if (typeof update === 'object') {
    setMessages(prev => [...prev, update]);
  } else {
    // Handle string updates (streaming content)
    setMessages(prev => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage && lastMessage.type === 'ai') {
        return [...prev.slice(0, -1), {
          ...lastMessage,
          content: update,
        }];
      }
      return prev;
    });
  }
};

// UI component updates
const handleUIUpdate = (
  update: UIMessage | RemoveUIMessage | (UIMessage | RemoveUIMessage)[]
) => {
  setUI(prev => uiMessageReducer(prev, update));
};
```

### 3. Custom Event Handling

**Event Types**:
```typescript
type CustomEvent = UIMessage | RemoveUIMessage;

interface UIMessage {
  id: string;
  name: string;
  props: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp: number;
}

interface RemoveUIMessage {
  type: "remove";
  id: string;
}
```

**Event Handler Implementation**:
```typescript
const streamValue = useTypedStream({
  // ... other configuration
  onCustomEvent: (event, options) => {
    options.mutate((prev) => {
      const ui = uiMessageReducer(prev.ui ?? [], event);
      return { ...prev, ui };
    });
  },
  onError: (error) => {
    console.error("Stream error:", error);
    setError(error);
  },
  onReconnect: () => {
    console.log("Stream reconnected");
    setError(null);
  },
});
```

## WebSocket Communication

### 1. Connection Management

**Connection Lifecycle**:
```typescript
class StreamConnection {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(url: string) {
    this.ws = new WebSocket(url);
    
    this.ws.onopen = () => {
      console.log("WebSocket connected");
      this.reconnectAttempts = 0;
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
    
    this.ws.onclose = () => {
      console.log("WebSocket disconnected");
      this.attemptReconnect();
    };
    
    this.ws.onerror = (error) => {
      console.error("WebSocket error:", error);
    };
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        this.connect(this.url);
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  send(data: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    }
  }

  close() {
    this.ws?.close();
  }
}
```

### 2. Message Protocol

**Message Types**:
```typescript
interface StreamMessage {
  type: 'update' | 'error' | 'complete';
  data: any;
  timestamp: number;
  messageId: string;
}

interface UpdateMessage extends StreamMessage {
  type: 'update';
  data: {
    messages?: Message[];
    ui?: UIMessage[];
    streamUrl?: string;
    instanceId?: string;
  };
}

interface ErrorMessage extends StreamMessage {
  type: 'error';
  data: {
    error: string;
    code?: string;
    details?: any;
  };
}

interface CompleteMessage extends StreamMessage {
  type: 'complete';
  data: {
    finalState: StateType;
    metrics?: {
      duration: number;
      messageCount: number;
      errorCount: number;
    };
  };
}
```

### 3. Protocol Implementation

**Message Handling**:
```typescript
const handleMessage = (message: StreamMessage) => {
  switch (message.type) {
    case 'update':
      handleUpdateMessage(message as UpdateMessage);
      break;
    
    case 'error':
      handleErrorMessage(message as ErrorMessage);
      break;
    
    case 'complete':
      handleCompleteMessage(message as CompleteMessage);
      break;
    
    default:
      console.warn('Unknown message type:', message.type);
  }
};

const handleUpdateMessage = (message: UpdateMessage) => {
  const { messages, ui, streamUrl, instanceId } = message.data;
  
  // Update state with new data
  if (messages) {
    setMessages(messages);
  }
  
  if (ui) {
    setUI(prev => uiMessageReducer(prev, ui));
  }
  
  if (streamUrl) {
    setStreamUrl(streamUrl);
  }
  
  if (instanceId) {
    setInstanceId(instanceId);
  }
};
```

## Optimistic Updates

### 1. Optimistic Rendering

**Pattern Implementation**:
```typescript
const submitMessage = async (content: string) => {
  const optimisticMessage: Message = {
    id: uuidv4(),
    type: "human",
    content,
    created_at: new Date().toISOString(),
    optimistic: true,
  };

  // Immediately add to UI
  setMessages(prev => [...prev, optimisticMessage]);
  
  try {
    // Submit to server
    const response = await stream.submit(
      { messages: [optimisticMessage] },
      {
        streamMode: ["values"],
        optimisticValues: (prev) => ({
          ...prev,
          messages: [...(prev.messages ?? []), optimisticMessage],
        }),
      }
    );
    
    // Server will send back the actual message
    // Optimistic message will be replaced
  } catch (error) {
    // Remove optimistic message on error
    setMessages(prev => 
      prev.filter(m => m.id !== optimisticMessage.id)
    );
    
    setError(error);
  }
};
```

### 2. Conflict Resolution

**State Reconciliation**:
```typescript
const reconcileMessages = (
  optimisticMessages: Message[],
  serverMessages: Message[]
): Message[] => {
  const serverIds = new Set(serverMessages.map(m => m.id));
  
  // Remove optimistic messages that have been confirmed by server
  const filteredOptimistic = optimisticMessages.filter(
    m => !m.optimistic || !serverIds.has(m.id)
  );
  
  // Merge server messages with remaining optimistic messages
  return [...serverMessages, ...filteredOptimistic]
    .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
};
```

### 3. Rollback Mechanism

**Error Recovery**:
```typescript
const useOptimisticUpdates = () => {
  const [optimisticActions, setOptimisticActions] = useState<Array<{
    id: string;
    action: () => void;
    rollback: () => void;
  }>>([]);

  const addOptimisticAction = (action: () => void, rollback: () => void) => {
    const id = uuidv4();
    setOptimisticActions(prev => [...prev, { id, action, rollback }]);
    
    // Execute optimistic action
    action();
    
    return id;
  };

  const confirmAction = (id: string) => {
    setOptimisticActions(prev => prev.filter(a => a.id !== id));
  };

  const rollbackAction = (id: string) => {
    const action = optimisticActions.find(a => a.id === id);
    if (action) {
      action.rollback();
      setOptimisticActions(prev => prev.filter(a => a.id !== id));
    }
  };

  const rollbackAll = () => {
    optimisticActions.forEach(action => action.rollback());
    setOptimisticActions([]);
  };

  return {
    addOptimisticAction,
    confirmAction,
    rollbackAction,
    rollbackAll,
  };
};
```

## UI Message Reducer

### 1. Reducer Implementation

**Core Reducer Logic**:
```typescript
import { uiMessageReducer } from "@langchain/langgraph-sdk/react-ui";

// The reducer handles various UI message operations
const handleUIMessage = (
  state: UIMessage[],
  action: UIMessage | RemoveUIMessage | (UIMessage | RemoveUIMessage)[]
): UIMessage[] => {
  if (Array.isArray(action)) {
    return action.reduce((acc, item) => uiMessageReducer(acc, item), state);
  }
  
  if ('type' in action && action.type === 'remove') {
    return state.filter(msg => msg.id !== action.id);
  }
  
  // Add or update UI message
  const existingIndex = state.findIndex(msg => msg.id === action.id);
  if (existingIndex >= 0) {
    return state.map((msg, index) => 
      index === existingIndex ? action : msg
    );
  }
  
  return [...state, action];
};
```

### 2. UI Component Lifecycle

**Component Creation**:
```typescript
const createUIComponent = (
  name: string,
  props: Record<string, any>,
  metadata?: Record<string, any>
): UIMessage => ({
  id: uuidv4(),
  name,
  props,
  metadata,
  timestamp: Date.now(),
});

// Usage in agent
const ui = typedUi<typeof ComponentMap>(config);
ui.push(createUIComponent("computer-use-tool-call", {
  toolCallId: tc.id,
  action: tc.action,
}));
```

**Component Updates**:
```typescript
const updateUIComponent = (
  id: string,
  updates: Partial<UIMessage>
): UIMessage => ({
  ...existingComponent,
  ...updates,
  timestamp: Date.now(),
});

// Usage
const existingComponent = state.ui.find(msg => msg.name === "instance");
if (existingComponent) {
  ui.delete(existingComponent.id);
  ui.push(updateUIComponent(existingComponent.id, {
    props: {
      instanceId: state.instanceId,
      streamUrl: state.streamUrl,
    },
  }));
}
```

**Component Removal**:
```typescript
const removeUIComponent = (id: string): RemoveUIMessage => ({
  type: "remove",
  id,
});

// Usage
const componentToRemove = state.ui.find(msg => msg.name === "render-vm-button");
if (componentToRemove) {
  ui.delete(componentToRemove.id);
}
```

## Error Handling and Recovery

### 1. Connection Error Handling

**Connection States**:
```typescript
enum ConnectionState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

const useConnectionState = () => {
  const [connectionState, setConnectionState] = useState<ConnectionState>(
    ConnectionState.CONNECTING
  );
  
  const [error, setError] = useState<Error | null>(null);
  
  return {
    connectionState,
    setConnectionState,
    error,
    setError,
    isConnected: connectionState === ConnectionState.CONNECTED,
    isReconnecting: connectionState === ConnectionState.RECONNECTING,
  };
};
```

### 2. Retry Logic

**Exponential Backoff**:
```typescript
class RetryManager {
  private retryCount = 0;
  private maxRetries = 5;
  private baseDelay = 1000;
  private maxDelay = 30000;

  async retry<T>(fn: () => Promise<T>): Promise<T> {
    try {
      const result = await fn();
      this.retryCount = 0; // Reset on success
      return result;
    } catch (error) {
      if (this.retryCount >= this.maxRetries) {
        throw error;
      }

      this.retryCount++;
      const delay = Math.min(
        this.baseDelay * Math.pow(2, this.retryCount - 1),
        this.maxDelay
      );

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.retry(fn);
    }
  }

  reset() {
    this.retryCount = 0;
  }
}
```

### 3. Graceful Degradation

**Fallback Mechanisms**:
```typescript
const useStreamingWithFallback = () => {
  const [streamingMode, setStreamingMode] = useState<'websocket' | 'polling' | 'disabled'>('websocket');
  
  const fallbackToPolling = () => {
    setStreamingMode('polling');
    // Implement polling mechanism
  };
  
  const disableStreaming = () => {
    setStreamingMode('disabled');
    // Use synchronous updates only
  };
  
  useEffect(() => {
    if (streamingMode === 'websocket') {
      // Try WebSocket connection
      const ws = new WebSocket(wsUrl);
      
      ws.onerror = () => {
        console.log('WebSocket failed, falling back to polling');
        fallbackToPolling();
      };
    }
  }, [streamingMode]);
  
  return {
    streamingMode,
    fallbackToPolling,
    disableStreaming,
  };
};
```

## Performance Optimization

### 1. Message Batching

**Batch Processing**:
```typescript
class MessageBatcher {
  private queue: any[] = [];
  private batchSize = 10;
  private batchTimeout = 100;
  private timeoutId: NodeJS.Timeout | null = null;

  add(message: any) {
    this.queue.push(message);
    
    if (this.queue.length >= this.batchSize) {
      this.flush();
    } else if (!this.timeoutId) {
      this.timeoutId = setTimeout(() => this.flush(), this.batchTimeout);
    }
  }

  private flush() {
    if (this.queue.length === 0) return;
    
    const batch = this.queue.splice(0);
    this.processBatch(batch);
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  private processBatch(batch: any[]) {
    // Process batch of messages
    batch.forEach(message => {
      handleMessage(message);
    });
  }
}
```

### 2. State Optimization

**Selective Updates**:
```typescript
const optimizeStateUpdates = (
  prevState: StateType,
  newState: StateType
): StateType => {
  const changes: Partial<StateType> = {};
  
  // Only update changed fields
  if (prevState.messages !== newState.messages) {
    changes.messages = newState.messages;
  }
  
  if (prevState.ui !== newState.ui) {
    changes.ui = newState.ui;
  }
  
  if (prevState.streamUrl !== newState.streamUrl) {
    changes.streamUrl = newState.streamUrl;
  }
  
  if (prevState.instanceId !== newState.instanceId) {
    changes.instanceId = newState.instanceId;
  }
  
  return Object.keys(changes).length > 0 
    ? { ...prevState, ...changes }
    : prevState;
};
```

### 3. Memory Management

**Cleanup and Garbage Collection**:
```typescript
const useStreamCleanup = () => {
  const cleanupRef = useRef<Array<() => void>>([]);
  
  const addCleanup = (fn: () => void) => {
    cleanupRef.current.push(fn);
  };
  
  const cleanup = () => {
    cleanupRef.current.forEach(fn => fn());
    cleanupRef.current = [];
  };
  
  useEffect(() => {
    return cleanup;
  }, []);
  
  return { addCleanup, cleanup };
};
```

## Monitoring and Metrics

### 1. Performance Metrics

**Metrics Collection**:
```typescript
interface StreamMetrics {
  messageCount: number;
  errorCount: number;
  reconnectCount: number;
  averageLatency: number;
  connectionUptime: number;
  lastMessageTime: number;
}

const useStreamMetrics = () => {
  const [metrics, setMetrics] = useState<StreamMetrics>({
    messageCount: 0,
    errorCount: 0,
    reconnectCount: 0,
    averageLatency: 0,
    connectionUptime: 0,
    lastMessageTime: 0,
  });
  
  const updateMetrics = (update: Partial<StreamMetrics>) => {
    setMetrics(prev => ({ ...prev, ...update }));
  };
  
  return { metrics, updateMetrics };
};
```

### 2. Health Monitoring

**Health Checks**:
```typescript
const useStreamHealth = () => {
  const [health, setHealth] = useState<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    lastCheck: number;
    issues: string[];
  }>({
    status: 'healthy',
    lastCheck: Date.now(),
    issues: [],
  });
  
  const checkHealth = () => {
    const issues: string[] = [];
    
    // Check connection status
    if (!isConnected) {
      issues.push('Connection lost');
    }
    
    // Check message flow
    if (Date.now() - lastMessageTime > 30000) {
      issues.push('No messages received in 30 seconds');
    }
    
    // Check error rate
    if (errorCount > 5) {
      issues.push('High error rate detected');
    }
    
    const status = issues.length === 0 ? 'healthy' : 
                  issues.length <= 2 ? 'degraded' : 'unhealthy';
    
    setHealth({
      status,
      lastCheck: Date.now(),
      issues,
    });
  };
  
  useEffect(() => {
    const interval = setInterval(checkHealth, 10000);
    return () => clearInterval(interval);
  }, []);
  
  return health;
};
```

This comprehensive streaming system provides robust real-time communication capabilities with advanced error handling, performance optimization, and monitoring features for the Gen-UI Computer Use application.