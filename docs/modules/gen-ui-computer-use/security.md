# Gen-UI Computer Use - Security Documentation

## Security Overview

The Gen-UI Computer Use system implements comprehensive security measures to protect against threats while maintaining usability. This document covers authentication, authorization, data protection, network security, and compliance considerations for the multi-service architecture.

## Security Architecture

### 1. Security Layers

```mermaid
graph TB
    subgraph "Client Security"
        CSP[Content Security Policy]
        CORS[CORS Protection]
        HTTPS[HTTPS/TLS Encryption]
        XSS[XSS Protection]
    end
    
    subgraph "Application Security"
        Auth[Authentication]
        Authz[Authorization]
        Session[Session Management]
        Input[Input Validation]
    end
    
    subgraph "API Security"
        RateLimit[Rate Limiting]
        APIKey[API Key Management]
        JWT[JWT Tokens]
        Encryption[Data Encryption]
    end
    
    subgraph "Infrastructure Security"
        Network[Network Security]
        Firewall[Firewall Rules]
        VPN[VPN Access]
        Monitor[Security Monitoring]
    end
    
    subgraph "Data Security"
        Storage[Encrypted Storage]
        Backup[Secure Backups]
        Audit[Audit Logging]
        Privacy[Data Privacy]
    end
    
    CSP --> Auth
    CORS --> Auth
    HTTPS --> Auth
    XSS --> Auth
    
    Auth --> RateLimit
    Authz --> APIKey
    Session --> JWT
    Input --> Encryption
    
    RateLimit --> Network
    APIKey --> Firewall
    JWT --> VPN
    Encryption --> Monitor
    
    Network --> Storage
    Firewall --> Backup
    VPN --> Audit
    Monitor --> Privacy
```

### 2. Threat Model

**Identified Threats**:
- **Unauthorized Access**: Malicious users accessing VM instances
- **Data Breaches**: Exposure of sensitive user data or API keys
- **Injection Attacks**: SQL injection, command injection, XSS
- **DDoS Attacks**: Service disruption through overwhelming requests
- **Man-in-the-Middle**: Interception of communications
- **Privilege Escalation**: Unauthorized elevation of user permissions
- **Data Exfiltration**: Unauthorized data extraction
- **Supply Chain Attacks**: Compromised dependencies

**Risk Assessment Matrix**:
```typescript
interface SecurityRisk {
  threat: string;
  likelihood: 'Low' | 'Medium' | 'High';
  impact: 'Low' | 'Medium' | 'High';
  riskScore: number;
  mitigations: string[];
}

const securityRisks: SecurityRisk[] = [
  {
    threat: 'Unauthorized VM Access',
    likelihood: 'Medium',
    impact: 'High',
    riskScore: 7,
    mitigations: ['Authentication', 'Session Management', 'Access Controls'],
  },
  {
    threat: 'API Key Exposure',
    likelihood: 'Low',
    impact: 'High',
    riskScore: 6,
    mitigations: ['Environment Variables', 'Secret Management', 'Key Rotation'],
  },
  {
    threat: 'XSS Attacks',
    likelihood: 'Medium',
    impact: 'Medium',
    riskScore: 5,
    mitigations: ['Input Sanitization', 'CSP Headers', 'Output Encoding'],
  },
];
```

## Authentication and Authorization

### 1. User Authentication

**Authentication Flow**:
```mermaid
sequenceDiagram
    participant User
    participant App
    participant Auth
    participant Session
    participant API

    User->>App: Access Application
    App->>Auth: Check Authentication
    Auth->>Session: Validate Session
    Session-->>Auth: Session Status
    Auth-->>App: Authentication Result
    
    alt Not Authenticated
        App->>User: Redirect to Login
        User->>Auth: Provide Credentials
        Auth->>Session: Create Session
        Session-->>Auth: Session Token
        Auth-->>User: Authentication Success
    end
    
    User->>App: Access Protected Resource
    App->>API: Request with Token
    API->>Auth: Validate Token
    Auth-->>API: Token Valid
    API-->>App: Protected Resource
    App-->>User: Display Resource
```

**Authentication Implementation**:
```typescript
// src/lib/auth.ts
import { NextAuthOptions } from 'next-auth';
import { JWT } from 'next-auth/jwt';

export const authOptions: NextAuthOptions = {
  providers: [
    {
      id: 'credentials',
      name: 'credentials',
      type: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Verify credentials against database
        const user = await verifyCredentials(credentials.email, credentials.password);
        
        if (!user) {
          return null;
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        };
      },
    },
  ],
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
    updateAge: 60 * 60, // 1 hour
  },
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
    secret: process.env.NEXTAUTH_SECRET,
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      session.user.id = token.sub;
      session.user.role = token.role;
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
  },
};

async function verifyCredentials(email: string, password: string) {
  // Implement secure password verification
  const user = await getUserByEmail(email);
  if (!user) return null;

  const isValidPassword = await bcrypt.compare(password, user.hashedPassword);
  if (!isValidPassword) return null;

  return user;
}
```

### 2. Role-Based Access Control

**RBAC Implementation**:
```typescript
// src/lib/rbac.ts
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest',
}

export enum Permission {
  CREATE_VM = 'create_vm',
  CONTROL_VM = 'control_vm',
  VIEW_ANALYTICS = 'view_analytics',
  MANAGE_USERS = 'manage_users',
}

const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    Permission.CREATE_VM,
    Permission.CONTROL_VM,
    Permission.VIEW_ANALYTICS,
    Permission.MANAGE_USERS,
  ],
  [UserRole.USER]: [
    Permission.CREATE_VM,
    Permission.CONTROL_VM,
  ],
  [UserRole.GUEST]: [],
};

export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  return rolePermissions[userRole].includes(permission);
}

export function requirePermission(permission: Permission) {
  return async (req: Request) => {
    const session = await getSession(req);
    
    if (!session?.user?.role) {
      throw new Error('Unauthorized');
    }

    if (!hasPermission(session.user.role, permission)) {
      throw new Error('Insufficient permissions');
    }
  };
}
```

### 3. Session Management

**Secure Session Handling**:
```typescript
// src/lib/session.ts
import { SignJWT, jwtVerify } from 'jose';

const secretKey = new TextEncoder().encode(process.env.SESSION_SECRET);

export interface SessionData {
  userId: string;
  role: UserRole;
  email: string;
  issuedAt: number;
  expiresAt: number;
}

export async function createSession(userData: Omit<SessionData, 'issuedAt' | 'expiresAt'>): Promise<string> {
  const now = Date.now();
  const sessionData: SessionData = {
    ...userData,
    issuedAt: now,
    expiresAt: now + (24 * 60 * 60 * 1000), // 24 hours
  };

  const token = await new SignJWT(sessionData)
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('24h')
    .sign(secretKey);

  return token;
}

export async function verifySession(token: string): Promise<SessionData | null> {
  try {
    const { payload } = await jwtVerify(token, secretKey);
    return payload as SessionData;
  } catch (error) {
    return null;
  }
}

export async function refreshSession(token: string): Promise<string | null> {
  const sessionData = await verifySession(token);
  if (!sessionData) return null;

  // Check if token is close to expiration (within 1 hour)
  const timeUntilExpiry = sessionData.expiresAt - Date.now();
  if (timeUntilExpiry > 60 * 60 * 1000) {
    return token; // Token is still valid for more than 1 hour
  }

  // Create new session
  return createSession({
    userId: sessionData.userId,
    role: sessionData.role,
    email: sessionData.email,
  });
}
```

## API Security

### 1. Input Validation and Sanitization

**Input Validation Framework**:
```typescript
// src/lib/validation.ts
import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

// Common validation schemas
export const userInputSchema = z.object({
  message: z.string()
    .min(1, 'Message cannot be empty')
    .max(10000, 'Message too long')
    .transform(value => DOMPurify.sanitize(value)),
  threadId: z.string().uuid().optional(),
});

export const vmControlSchema = z.object({
  instanceId: z.string().regex(/^[a-zA-Z0-9-_]+$/, 'Invalid instance ID'),
  action: z.enum(['start', 'stop', 'pause', 'resume']),
});

export const apiKeySchema = z.string()
  .min(1, 'API key required')
  .regex(/^[a-zA-Z0-9-_]+$/, 'Invalid API key format');

// Validation middleware
export function validateInput<T>(schema: z.ZodSchema<T>) {
  return async (req: Request): Promise<T> => {
    try {
      const body = await req.json();
      return schema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ValidationError('Invalid input', error.errors);
      }
      throw error;
    }
  };
}

// SQL injection prevention
export function sanitizeSQL(input: string): string {
  return input.replace(/[';--]/g, '');
}

// XSS prevention
export function sanitizeHTML(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'code', 'pre'],
    ALLOWED_ATTR: [],
  });
}
```

### 2. Rate Limiting

**Rate Limiting Implementation**:
```typescript
// src/lib/rate-limit.ts
import { NextRequest } from 'next/server';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

class RateLimiter {
  private requests = new Map<string, { count: number; resetTime: number }>();
  
  constructor(private config: RateLimitConfig) {}

  async isAllowed(identifier: string): Promise<boolean> {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    // Clean up old entries
    this.cleanup(windowStart);

    const userRequests = this.requests.get(identifier);
    
    if (!userRequests) {
      this.requests.set(identifier, { count: 1, resetTime: now + this.config.windowMs });
      return true;
    }

    if (now > userRequests.resetTime) {
      this.requests.set(identifier, { count: 1, resetTime: now + this.config.windowMs });
      return true;
    }

    if (userRequests.count >= this.config.maxRequests) {
      return false;
    }

    userRequests.count++;
    return true;
  }

  private cleanup(windowStart: number) {
    for (const [key, value] of this.requests.entries()) {
      if (value.resetTime < windowStart) {
        this.requests.delete(key);
      }
    }
  }
}

// Rate limiting middleware
export function createRateLimiter(config: RateLimitConfig) {
  const limiter = new RateLimiter(config);
  
  return async (req: NextRequest) => {
    const identifier = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
    
    const allowed = await limiter.isAllowed(identifier);
    if (!allowed) {
      return new Response('Rate limit exceeded', { status: 429 });
    }
  };
}

// Usage in API routes
export const rateLimitMiddleware = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100,
});
```

### 3. API Key Management

**API Key Security**:
```typescript
// src/lib/api-keys.ts
import crypto from 'crypto';

export class APIKeyManager {
  private static instance: APIKeyManager;
  private keys = new Map<string, { 
    userId: string; 
    permissions: string[]; 
    createdAt: Date; 
    expiresAt?: Date 
  }>();

  static getInstance(): APIKeyManager {
    if (!APIKeyManager.instance) {
      APIKeyManager.instance = new APIKeyManager();
    }
    return APIKeyManager.instance;
  }

  generateAPIKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  async createAPIKey(userId: string, permissions: string[], expiresAt?: Date): Promise<string> {
    const apiKey = this.generateAPIKey();
    const hashedKey = await this.hashAPIKey(apiKey);
    
    this.keys.set(hashedKey, {
      userId,
      permissions,
      createdAt: new Date(),
      expiresAt,
    });

    // Store in database
    await this.storeAPIKey(hashedKey, userId, permissions, expiresAt);
    
    return apiKey;
  }

  async validateAPIKey(apiKey: string): Promise<{ userId: string; permissions: string[] } | null> {
    const hashedKey = await this.hashAPIKey(apiKey);
    const keyData = this.keys.get(hashedKey);
    
    if (!keyData) {
      return null;
    }

    if (keyData.expiresAt && keyData.expiresAt < new Date()) {
      this.keys.delete(hashedKey);
      return null;
    }

    return {
      userId: keyData.userId,
      permissions: keyData.permissions,
    };
  }

  async revokeAPIKey(apiKey: string): Promise<void> {
    const hashedKey = await this.hashAPIKey(apiKey);
    this.keys.delete(hashedKey);
    await this.deleteAPIKey(hashedKey);
  }

  private async hashAPIKey(apiKey: string): Promise<string> {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  private async storeAPIKey(hashedKey: string, userId: string, permissions: string[], expiresAt?: Date): Promise<void> {
    // Implementation depends on your database
    // Store hashed key, never store plain text
  }

  private async deleteAPIKey(hashedKey: string): Promise<void> {
    // Implementation depends on your database
  }
}
```

## Data Protection

### 1. Encryption at Rest

**Data Encryption Implementation**:
```typescript
// src/lib/encryption.ts
import crypto from 'crypto';

export class DataEncryption {
  private algorithm = 'aes-256-gcm';
  private keyLength = 32;
  private ivLength = 16;

  constructor(private secretKey: string) {}

  encrypt(data: string): { encrypted: string; iv: string; tag: string } {
    const key = crypto.scryptSync(this.secretKey, 'salt', this.keyLength);
    const iv = crypto.randomBytes(this.ivLength);
    const cipher = crypto.createCipher(this.algorithm, key, { iv });
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
    };
  }

  decrypt(encrypted: string, iv: string, tag: string): string {
    const key = crypto.scryptSync(this.secretKey, 'salt', this.keyLength);
    const decipher = crypto.createDecipher(this.algorithm, key, { 
      iv: Buffer.from(iv, 'hex') 
    });
    
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

// Usage for sensitive data
export const dataEncryption = new DataEncryption(process.env.ENCRYPTION_KEY!);

// Encrypt sensitive user data before storage
export function encryptSensitiveData(data: any): string {
  const { encrypted, iv, tag } = dataEncryption.encrypt(JSON.stringify(data));
  return JSON.stringify({ encrypted, iv, tag });
}

// Decrypt sensitive user data after retrieval
export function decryptSensitiveData(encryptedData: string): any {
  const { encrypted, iv, tag } = JSON.parse(encryptedData);
  const decrypted = dataEncryption.decrypt(encrypted, iv, tag);
  return JSON.parse(decrypted);
}
```

### 2. Secure Screenshot Handling

**Screenshot Security**:
```typescript
// src/lib/screenshot-security.ts
import { dataEncryption } from './encryption';

export class ScreenshotSecurity {
  private static readonly ALLOWED_FORMATS = ['image/png', 'image/jpeg'];
  private static readonly MAX_SIZE = 10 * 1024 * 1024; // 10MB

  static validateScreenshot(screenshot: string): boolean {
    try {
      // Check if it's a valid base64 image
      const matches = screenshot.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
      if (!matches) return false;

      const [, mimeType, base64Data] = matches;
      
      // Validate MIME type
      if (!this.ALLOWED_FORMATS.includes(mimeType)) {
        return false;
      }

      // Validate size
      const buffer = Buffer.from(base64Data, 'base64');
      if (buffer.length > this.MAX_SIZE) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  static sanitizeScreenshot(screenshot: string): string {
    // Remove potential XSS vectors
    const sanitized = screenshot.replace(/[<>]/g, '');
    
    // Validate format
    if (!this.validateScreenshot(sanitized)) {
      throw new Error('Invalid screenshot format');
    }

    return sanitized;
  }

  static async encryptScreenshot(screenshot: string): Promise<string> {
    const sanitized = this.sanitizeScreenshot(screenshot);
    return dataEncryption.encrypt(sanitized).encrypted;
  }

  static async decryptScreenshot(encryptedScreenshot: string): Promise<string> {
    // Implementation depends on your encryption method
    return dataEncryption.decrypt(encryptedScreenshot, '', '');
  }
}
```

### 3. PII Protection

**Personal Information Security**:
```typescript
// src/lib/pii-protection.ts
export class PIIProtection {
  private static readonly PII_PATTERNS = {
    email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    phone: /\b\d{3}-\d{3}-\d{4}\b/g,
    ssn: /\b\d{3}-\d{2}-\d{4}\b/g,
    creditCard: /\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b/g,
  };

  static detectPII(text: string): { type: string; matches: string[] }[] {
    const results: { type: string; matches: string[] }[] = [];
    
    for (const [type, pattern] of Object.entries(this.PII_PATTERNS)) {
      const matches = text.match(pattern);
      if (matches) {
        results.push({ type, matches });
      }
    }
    
    return results;
  }

  static redactPII(text: string): string {
    let redacted = text;
    
    for (const [type, pattern] of Object.entries(this.PII_PATTERNS)) {
      redacted = redacted.replace(pattern, `[REDACTED_${type.toUpperCase()}]`);
    }
    
    return redacted;
  }

  static logPIIAccess(userId: string, dataType: string, action: string): void {
    // Log PII access for compliance
    console.log(`PII Access: User ${userId} performed ${action} on ${dataType} at ${new Date().toISOString()}`);
  }
}
```

## Network Security

### 1. HTTPS/TLS Configuration

**TLS Security Headers**:
```typescript
// src/middleware/security-headers.ts
import { NextRequest, NextResponse } from 'next/server';

export function securityHeaders(request: NextRequest) {
  const response = NextResponse.next();

  // Force HTTPS
  response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  
  // Prevent clickjacking
  response.headers.set('X-Frame-Options', 'DENY');
  
  // Prevent MIME type sniffing
  response.headers.set('X-Content-Type-Options', 'nosniff');
  
  // XSS protection
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // Referrer policy
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Content Security Policy
  response.headers.set('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self' https://api.openai.com https://api.scrapybara.com",
    "frame-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ].join('; '));
  
  // Permissions Policy
  response.headers.set('Permissions-Policy', [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'interest-cohort=()',
  ].join(', '));

  return response;
}
```

### 2. CORS Configuration

**CORS Security**:
```typescript
// src/lib/cors.ts
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
    
    if (!origin) {
      // Allow requests with no origin (like mobile apps or curl requests)
      return callback(null, true);
    }

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400, // 24 hours
};
```

### 3. Network Monitoring

**Network Security Monitoring**:
```typescript
// src/lib/network-monitoring.ts
export class NetworkMonitoring {
  private static readonly SUSPICIOUS_PATTERNS = [
    /\bSELECT\b.*\bFROM\b/i, // SQL injection
    /<script[^>]*>.*?<\/script>/i, // XSS
    /\.\.\//g, // Path traversal
    /\bUNION\b.*\bSELECT\b/i, // SQL injection
  ];

  static detectSuspiciousActivity(request: string): string[] {
    const detected: string[] = [];
    
    for (const pattern of this.SUSPICIOUS_PATTERNS) {
      if (pattern.test(request)) {
        detected.push(pattern.source);
      }
    }
    
    return detected;
  }

  static logSecurityEvent(event: {
    type: string;
    severity: 'low' | 'medium' | 'high';
    source: string;
    description: string;
    timestamp: Date;
  }): void {
    // Log security events for analysis
    console.log(`Security Event: ${JSON.stringify(event)}`);
    
    // Alert if high severity
    if (event.severity === 'high') {
      this.alertSecurityTeam(event);
    }
  }

  private static alertSecurityTeam(event: any): void {
    // Implementation depends on your alerting system
    // Could send to Slack, email, PagerDuty, etc.
  }
}
```

## Compliance and Auditing

### 1. Audit Logging

**Comprehensive Audit System**:
```typescript
// src/lib/audit-logging.ts
export interface AuditEvent {
  id: string;
  userId?: string;
  sessionId?: string;
  action: string;
  resource: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  details?: Record<string, any>;
}

export class AuditLogger {
  private static logs: AuditEvent[] = [];

  static log(event: Omit<AuditEvent, 'id' | 'timestamp'>): void {
    const auditEvent: AuditEvent = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      ...event,
    };

    this.logs.push(auditEvent);
    
    // Persist to database
    this.persistAuditEvent(auditEvent);
    
    // Real-time monitoring
    this.monitorAuditEvent(auditEvent);
  }

  static async getAuditTrail(filters: {
    userId?: string;
    action?: string;
    resource?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<AuditEvent[]> {
    let filtered = this.logs;

    if (filters.userId) {
      filtered = filtered.filter(event => event.userId === filters.userId);
    }

    if (filters.action) {
      filtered = filtered.filter(event => event.action === filters.action);
    }

    if (filters.resource) {
      filtered = filtered.filter(event => event.resource === filters.resource);
    }

    if (filters.startDate) {
      filtered = filtered.filter(event => event.timestamp >= filters.startDate!);
    }

    if (filters.endDate) {
      filtered = filtered.filter(event => event.timestamp <= filters.endDate!);
    }

    return filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  private static async persistAuditEvent(event: AuditEvent): Promise<void> {
    // Implementation depends on your database
    // Store in a dedicated audit table
  }

  private static monitorAuditEvent(event: AuditEvent): void {
    // Check for suspicious patterns
    if (this.isSuspiciousActivity(event)) {
      NetworkMonitoring.logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'high',
        source: event.ipAddress || 'unknown',
        description: `Suspicious activity detected: ${event.action} on ${event.resource}`,
        timestamp: event.timestamp,
      });
    }
  }

  private static isSuspiciousActivity(event: AuditEvent): boolean {
    // Implement suspicious activity detection logic
    // Multiple failed login attempts, unusual access patterns, etc.
    return false;
  }
}

// Usage in API routes
export function auditApiCall(action: string, resource: string, req: Request) {
  return async (success: boolean, details?: Record<string, any>) => {
    AuditLogger.log({
      userId: req.headers.get('x-user-id') || undefined,
      sessionId: req.headers.get('x-session-id') || undefined,
      action,
      resource,
      ipAddress: req.headers.get('x-forwarded-for') || 'unknown',
      userAgent: req.headers.get('user-agent') || undefined,
      success,
      details,
    });
  };
}
```

### 2. GDPR Compliance

**Data Privacy Implementation**:
```typescript
// src/lib/gdpr-compliance.ts
export class GDPRCompliance {
  static async handleDataRequest(userId: string, requestType: 'access' | 'portability' | 'deletion'): Promise<any> {
    switch (requestType) {
      case 'access':
        return this.generateDataReport(userId);
      case 'portability':
        return this.exportUserData(userId);
      case 'deletion':
        return this.deleteUserData(userId);
      default:
        throw new Error('Invalid request type');
    }
  }

  private static async generateDataReport(userId: string): Promise<any> {
    const userData = await this.getUserData(userId);
    const auditTrail = await AuditLogger.getAuditTrail({ userId });
    
    return {
      personalData: userData,
      auditTrail,
      dataRetention: this.getDataRetentionInfo(userId),
      thirdPartySharing: this.getThirdPartyInfo(userId),
    };
  }

  private static async exportUserData(userId: string): Promise<any> {
    const userData = await this.getUserData(userId);
    
    return {
      format: 'JSON',
      data: userData,
      exportDate: new Date().toISOString(),
    };
  }

  private static async deleteUserData(userId: string): Promise<void> {
    // Delete user data from all systems
    await this.deleteFromDatabase(userId);
    await this.deleteFromStorage(userId);
    await this.deleteFromAuditLogs(userId);
    
    // Log deletion
    AuditLogger.log({
      userId,
      action: 'data_deletion',
      resource: 'user_data',
      success: true,
    });
  }

  private static async getUserData(userId: string): Promise<any> {
    // Implementation depends on your database
    return {};
  }

  private static getDataRetentionInfo(userId: string): any {
    return {
      retentionPeriod: '2 years',
      nextDeletionDate: new Date(Date.now() + 2 * 365 * 24 * 60 * 60 * 1000).toISOString(),
    };
  }

  private static getThirdPartyInfo(userId: string): any {
    return {
      openai: 'Processing prompts and responses',
      scrapybara: 'VM management and screenshots',
      supabase: 'Data storage and authentication',
    };
  }

  private static async deleteFromDatabase(userId: string): Promise<void> {
    // Implementation depends on your database
  }

  private static async deleteFromStorage(userId: string): Promise<void> {
    // Delete user files from cloud storage
  }

  private static async deleteFromAuditLogs(userId: string): Promise<void> {
    // Anonymize audit logs (don't delete for compliance)
  }
}
```

### 3. Security Compliance Monitoring

**Compliance Monitoring System**:
```typescript
// src/lib/compliance-monitoring.ts
export class ComplianceMonitoring {
  private static readonly COMPLIANCE_RULES = {
    'password_policy': {
      minLength: 12,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSymbols: true,
    },
    'session_timeout': {
      maxDuration: 24 * 60 * 60 * 1000, // 24 hours
      inactivityTimeout: 30 * 60 * 1000, // 30 minutes
    },
    'data_retention': {
      auditLogs: 7 * 365 * 24 * 60 * 60 * 1000, // 7 years
      userData: 2 * 365 * 24 * 60 * 60 * 1000, // 2 years
    },
  };

  static async checkCompliance(): Promise<ComplianceReport> {
    const report: ComplianceReport = {
      timestamp: new Date(),
      checks: [],
      overallStatus: 'compliant',
    };

    // Check password policy compliance
    const passwordCompliance = await this.checkPasswordPolicy();
    report.checks.push(passwordCompliance);

    // Check session management compliance
    const sessionCompliance = await this.checkSessionManagement();
    report.checks.push(sessionCompliance);

    // Check data retention compliance
    const dataRetentionCompliance = await this.checkDataRetention();
    report.checks.push(dataRetentionCompliance);

    // Determine overall status
    const hasFailures = report.checks.some(check => check.status === 'non_compliant');
    report.overallStatus = hasFailures ? 'non_compliant' : 'compliant';

    return report;
  }

  private static async checkPasswordPolicy(): Promise<ComplianceCheck> {
    // Implementation depends on your user management system
    return {
      rule: 'password_policy',
      status: 'compliant',
      details: 'All users have strong passwords',
    };
  }

  private static async checkSessionManagement(): Promise<ComplianceCheck> {
    // Check for expired sessions, proper logout, etc.
    return {
      rule: 'session_timeout',
      status: 'compliant',
      details: 'Session timeouts properly configured',
    };
  }

  private static async checkDataRetention(): Promise<ComplianceCheck> {
    // Check for data that should be deleted
    return {
      rule: 'data_retention',
      status: 'compliant',
      details: 'Data retention policies followed',
    };
  }
}

interface ComplianceReport {
  timestamp: Date;
  checks: ComplianceCheck[];
  overallStatus: 'compliant' | 'non_compliant';
}

interface ComplianceCheck {
  rule: string;
  status: 'compliant' | 'non_compliant';
  details: string;
}
```

This comprehensive security documentation provides a robust framework for protecting the Gen-UI Computer Use system against threats while maintaining compliance with data protection regulations and industry standards.