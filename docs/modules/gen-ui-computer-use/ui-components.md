# Gen-UI Computer Use - UI Components Documentation

## UI Components System Overview

The Gen-UI Computer Use system implements a sophisticated UI component architecture that combines traditional React components with dynamically generated UI elements. This system provides a consistent design language while supporting real-time component generation based on agent state and actions.

## Design System Foundation

### 1. Design Principles

**Core Principles**:
- **Accessibility First**: WCAG 2.1 AA compliance through Radix UI
- **Responsive Design**: Mobile-first approach with progressive enhancement
- **Consistency**: Unified design language across all components
- **Performance**: Optimized rendering and minimal re-renders
- **Type Safety**: Full TypeScript integration

**Visual Language**:
- **Color System**: Semantic color tokens with light/dark mode support
- **Typography**: Hierarchical type scale with consistent spacing
- **Iconography**: Lucide React icons for consistency
- **Spacing**: 8px grid system for consistent layouts
- **Animations**: Subtle, purposeful animations using Framer Motion

### 2. Component Architecture

**Component Hierarchy**:
```mermaid
graph TB
    subgraph "Base Components"
        Button[Button]
        Input[Input]
        Card[Card]
        Avatar[Avatar]
        Tooltip[Tooltip]
    end
    
    subgraph "Composite Components"
        Thread[Thread]
        Messages[Message Components]
        History[Thread History]
        Controls[Control Components]
    end
    
    subgraph "Agent Components"
        ToolCall[Tool Call]
        ToolOutput[Tool Output]
        VMButton[VM Button]
        Instance[Instance Frame]
    end
    
    subgraph "Layout Components"
        Layout[App Layout]
        Provider[Context Providers]
        Container[Container Components]
    end
    
    Button --> Thread
    Input --> Thread
    Card --> Messages
    Avatar --> Messages
    Tooltip --> Controls
    Thread --> Layout
    Messages --> Layout
    ToolCall --> Agent
    ToolOutput --> Agent
    VMButton --> Agent
    Instance --> Agent
```

## Base UI Components

### 1. Button Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/button.tsx`

**Purpose**: Reusable button component with comprehensive variant system

**Design Features**:
- **Variant System**: 6 distinct button styles (default, destructive, outline, secondary, ghost, link)
- **Size System**: 3 size options (sm, default, lg)
- **State Management**: Loading, disabled, and active states
- **Accessibility**: Full keyboard navigation and ARIA support

**Implementation**:
```typescript
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
```

**Usage Examples**:
```typescript
// Primary button
<Button onClick={handleSubmit}>Submit</Button>

// Destructive button
<Button variant="destructive" onClick={handleDelete}>Delete</Button>

// Loading button
<Button disabled={isLoading}>
  {isLoading && <LoaderCircle className="w-4 h-4 animate-spin mr-2" />}
  Send
</Button>

// Ghost button with icon
<Button variant="ghost" size="sm">
  <SquarePen className="w-4 h-4" />
</Button>
```

### 2. Input Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/input.tsx`

**Purpose**: Form input component with validation states

**Design Features**:
- **Validation States**: Error, success, and warning states
- **Responsive Design**: Adaptive sizing for mobile and desktop
- **Accessibility**: Label association and screen reader support
- **Customization**: Size variants and custom styling

**Implementation**:
```typescript
export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  success?: boolean;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, success, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          error && "border-destructive focus-visible:ring-destructive",
          success && "border-green-500 focus-visible:ring-green-500",
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
```

### 3. Card Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/card.tsx`

**Purpose**: Container component for content sections

**Design Features**:
- **Semantic Structure**: Header, content, and footer sections
- **Elevation System**: Consistent shadow and border styling
- **Responsive Design**: Mobile-first responsive behavior
- **Customization**: Flexible styling overrides

**Implementation**:
```typescript
export const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-lg border bg-card text-card-foreground shadow-sm",
      className
    )}
    {...props}
  />
));

export const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));

export const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn("text-2xl font-semibold leading-none tracking-tight", className)}
    {...props}
  />
));

export const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
));
```

### 4. Avatar Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/avatar.tsx`

**Purpose**: User avatar display with fallback system

**Design Features**:
- **Image Handling**: Automatic fallback to initials
- **Size System**: Multiple size options (sm, md, lg)
- **Performance**: Lazy loading and error handling
- **Accessibility**: Alt text and screen reader support

**Implementation**:
```typescript
export const Avatar = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Root
    ref={ref}
    className={cn(
      "relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",
      className
    )}
    {...props}
  />
));

export const AvatarImage = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Image>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Image
    ref={ref}
    className={cn("aspect-square h-full w-full", className)}
    {...props}
  />
));

export const AvatarFallback = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Fallback>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Fallback
    ref={ref}
    className={cn(
      "flex h-full w-full items-center justify-center rounded-full bg-muted",
      className
    )}
    {...props}
  />
));
```

### 5. Tooltip Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/tooltip.tsx`

**Purpose**: Contextual help and information display

**Design Features**:
- **Positioning**: Intelligent tooltip positioning
- **Trigger Options**: Hover, focus, and click triggers
- **Animations**: Smooth enter/exit animations
- **Accessibility**: Keyboard navigation and screen reader support

**Implementation**:
```typescript
export const TooltipProvider = TooltipPrimitive.Provider;

export const Tooltip = TooltipPrimitive.Root;

export const TooltipTrigger = TooltipPrimitive.Trigger;

export const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      "z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
    )}
    {...props}
  />
));
```

## Composite Components

### 1. Thread Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/index.tsx`

**Purpose**: Main conversation interface with dual-pane layout

**Design Features**:
- **Dual Interface**: Seamless chat and computer view switching
- **Responsive Layout**: Mobile-first design with adaptive behavior
- **Animation System**: Smooth transitions using Framer Motion
- **Accessibility**: Full keyboard navigation and screen reader support

**Layout Implementation**:
```typescript
export function Thread() {
  const [chatHistoryOpen, setChatHistoryOpen] = useQueryState(
    "chatHistoryOpen",
    parseAsBoolean.withDefault(false)
  );
  const [isShowingInstanceFrame, setIsShowingInstanceFrame] = useQueryState(
    "isShowingInstanceFrame",
    parseAsBoolean
  );
  const isLargeScreen = useMediaQuery("(min-width: 1024px)");

  return (
    <div className="flex w-full h-screen overflow-hidden">
      {/* Animated Sidebar */}
      <div className="relative lg:flex hidden">
        <motion.div
          className="absolute h-full border-r bg-white overflow-hidden z-20"
          style={{ width: 300 }}
          animate={{ x: chatHistoryOpen ? 0 : -300 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          <ThreadHistory />
        </motion.div>
      </div>

      {/* Main Content Area */}
      <motion.div
        className="flex-1 flex flex-col min-w-0 overflow-hidden relative"
        animate={{
          marginLeft: chatHistoryOpen ? (isLargeScreen ? 300 : 0) : 0,
          width: chatHistoryOpen ? (isLargeScreen ? "calc(100% - 300px)" : "100%") : "100%",
        }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <ThreadHeader />
        <MobileToggle />
        <ThreadContent />
      </motion.div>
    </div>
  );
}
```

### 2. Mobile Toggle Component

**Purpose**: Mobile-friendly interface switcher

**Design Features**:
- **Smooth Animations**: Physics-based spring animations
- **Touch Optimization**: Optimized touch targets
- **Visual Feedback**: Clear active state indication
- **Accessibility**: Proper ARIA labels and keyboard support

**Implementation**:
```typescript
function MobileToggle() {
  const [isShowingInstance, setIsShowingInstance] = useQueryState(
    "isShowingInstanceFrame",
    parseAsBoolean
  );

  return (
    <div className="flex items-center justify-center my-4 lg:hidden">
      <motion.div
        className="relative flex items-center p-1 rounded-lg bg-gray-200 shadow-inner border border-slate-300"
        style={{ width: "280px" }}
      >
        <motion.div
          className="absolute inset-1 rounded-md shadow-sm z-0 bg-white h-[36px]"
          animate={{ x: isShowingInstance ? "95%" : "0%" }}
          style={{ width: "50%" }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        />
        
        <Button
          variant="ghost"
          className={cn(
            "relative z-10 transition-colors flex-1 justify-center",
            !isShowingInstance ? "text-black" : "text-muted-foreground"
          )}
          onClick={() => setIsShowingInstance(false)}
        >
          Chat
        </Button>
        
        <Button
          variant="ghost"
          className={cn(
            "relative z-10 transition-colors flex-1 justify-center",
            isShowingInstance ? "text-black" : "text-muted-foreground"
          )}
          onClick={() => setIsShowingInstance(true)}
        >
          Computer
        </Button>
      </motion.div>
    </div>
  );
}
```

### 3. Message Components

**Purpose**: Display conversation messages with rich content

**Design Features**:
- **Message Types**: Human and AI message variants
- **Content Rendering**: Markdown support with syntax highlighting
- **Streaming Support**: Real-time message updates
- **Action Buttons**: Copy, regenerate, and contextual actions

**Human Message Implementation**:
```typescript
export function HumanMessage({
  message,
  isLoading,
}: {
  message: Message;
  isLoading: boolean;
}) {
  return (
    <div className="flex gap-3 items-start">
      <Avatar className="w-8 h-8 flex-shrink-0">
        <AvatarFallback className="bg-blue-500 text-white">U</AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-sm font-medium">You</span>
          <span className="text-xs text-muted-foreground">
            {format(new Date(message.created_at), "HH:mm")}
          </span>
        </div>
        
        <div className="bg-muted rounded-lg p-3 max-w-3xl">
          <MarkdownText content={message.content} />
        </div>
      </div>
    </div>
  );
}
```

**AI Message Implementation**:
```typescript
export function AssistantMessage({
  message,
  isLoading,
  handleRegenerate,
}: {
  message: Message;
  isLoading: boolean;
  handleRegenerate: (checkpoint: Checkpoint | null | undefined) => void;
}) {
  return (
    <div className="flex gap-3 items-start">
      <Avatar className="w-8 h-8 flex-shrink-0">
        <AvatarFallback className="bg-green-500 text-white">AI</AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-sm font-medium">Assistant</span>
          <span className="text-xs text-muted-foreground">
            {format(new Date(message.created_at), "HH:mm")}
          </span>
        </div>
        
        <div className="prose prose-sm max-w-none">
          <MarkdownText content={message.content} />
        </div>
        
        <ToolCalls message={message} />
        
        {!isLoading && (
          <div className="flex gap-2 mt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRegenerate(message.checkpoint)}
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Regenerate
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigator.clipboard.writeText(message.content)}
            >
              <Copy className="w-4 h-4 mr-1" />
              Copy
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
```

### 4. Tooltip Icon Button

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/tooltip-icon-button.tsx`

**Purpose**: Icon button with contextual tooltip

**Design Features**:
- **Tooltip Integration**: Contextual help on hover
- **Icon Support**: Lucide React icon integration
- **Size Variants**: Multiple size options
- **Accessibility**: Keyboard navigation and screen reader support

**Implementation**:
```typescript
interface TooltipIconButtonProps {
  tooltip: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  onClick?: () => void;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg";
  className?: string;
  disabled?: boolean;
}

export function TooltipIconButton({
  tooltip,
  icon,
  children,
  onClick,
  variant = "ghost",
  size = "default",
  className,
  disabled = false,
}: TooltipIconButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            onClick={onClick}
            className={className}
            disabled={disabled}
          >
            {icon || children}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
```

## Agent UI Components

### 1. Computer Use Tool Call Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/computer-use-tool-call.tsx`

**Purpose**: Display computer use tool execution

**Design Features**:
- **Action Visualization**: Clear display of computer actions
- **Parameter Display**: Tool parameters with syntax highlighting
- **Status Indicators**: Visual execution status
- **Accessibility**: Screen reader friendly descriptions

**Implementation**:
```typescript
export function ComputerUseToolCall({
  toolCallId,
  action,
}: {
  toolCallId: string;
  action: string;
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  const actionData = useMemo(() => {
    try {
      return JSON.parse(action);
    } catch {
      return { action: "Unknown", raw: action };
    }
  }, [action]);

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
            <MonitorIcon className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">
              Computer Action
            </span>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-blue-600 hover:text-blue-800"
          >
            {isExpanded ? (
              <ChevronUpIcon className="w-4 h-4" />
            ) : (
              <ChevronDownIcon className="w-4 h-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-xs font-medium text-gray-600">Action:</span>
            <span className="text-sm font-mono bg-white px-2 py-1 rounded border">
              {actionData.action}
            </span>
          </div>
          
          {actionData.coordinate && (
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-gray-600">Position:</span>
              <span className="text-sm font-mono bg-white px-2 py-1 rounded border">
                [{actionData.coordinate.join(", ")}]
              </span>
            </div>
          )}
          
          {actionData.text && (
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-gray-600">Text:</span>
              <span className="text-sm bg-white px-2 py-1 rounded border">
                "{actionData.text}"
              </span>
            </div>
          )}
          
          {isExpanded && (
            <div className="mt-3 p-3 bg-white rounded border">
              <span className="text-xs font-medium text-gray-600 block mb-1">
                Raw Data:
              </span>
              <pre className="text-xs text-gray-800 overflow-x-auto">
                {JSON.stringify(actionData, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

### 2. Computer Use Tool Output Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/computer-use-tool-output.tsx`

**Purpose**: Display computer use tool results and screenshots

**Design Features**:
- **Screenshot Display**: Full-size screenshot with zoom capability
- **Result Visualization**: Tool execution results with formatting
- **Error Handling**: Error state visualization
- **Performance**: Lazy loading and image optimization

**Implementation**:
```typescript
export function ComputerUseToolOutput({
  toolCallId,
  screenshot,
}: {
  toolCallId: string;
  screenshot: string;
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Card className="border-green-200 bg-green-50">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <CheckCircleIcon className="w-4 h-4 text-green-600" />
          <span className="text-sm font-medium text-green-800">
            Computer Action Result
          </span>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {screenshot && !imageError && (
          <div className="space-y-2">
            <div className="relative">
              <img
                src={screenshot}
                alt="Computer screenshot"
                className={cn(
                  "cursor-pointer border rounded transition-all duration-200",
                  isExpanded 
                    ? "w-full max-w-none" 
                    : "w-full max-w-md h-48 object-cover hover:shadow-md"
                )}
                onClick={() => setIsExpanded(!isExpanded)}
                onError={handleImageError}
                loading="lazy"
              />
              
              {!isExpanded && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-200 rounded">
                  <div className="opacity-0 hover:opacity-100 transition-opacity duration-200">
                    <ZoomInIcon className="w-6 h-6 text-white" />
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex items-center justify-between text-xs text-gray-600">
              <span>Screenshot captured</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-green-600 hover:text-green-800"
              >
                {isExpanded ? "Collapse" : "Expand"}
              </Button>
            </div>
          </div>
        )}
        
        {imageError && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded">
            <XCircleIcon className="w-4 h-4 text-red-600" />
            <span className="text-sm text-red-800">
              Failed to load screenshot
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

### 3. Render VM Button Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/render-vm-button.tsx`

**Purpose**: Toggle button for VM instance display

**Design Features**:
- **State Indication**: Clear visual state indication
- **Icon Integration**: Contextual icons for different states
- **Accessibility**: Proper ARIA labels and keyboard support
- **Animation**: Smooth state transitions

**Implementation**:
```typescript
export function RenderVMButton() {
  const [isShowingInstanceFrame, setIsShowingInstanceFrame] = useQueryState(
    "isShowingInstanceFrame",
    parseAsBoolean
  );

  return (
    <Card className="border-purple-200 bg-purple-50">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MonitorIcon className="w-4 h-4 text-purple-600" />
            <span className="text-sm font-medium text-purple-800">
              Virtual Machine
            </span>
          </div>
          
          <Button
            variant={isShowingInstanceFrame ? "default" : "outline"}
            size="sm"
            onClick={() => setIsShowingInstanceFrame(!isShowingInstanceFrame)}
            className={cn(
              "transition-all duration-200",
              isShowingInstanceFrame 
                ? "bg-purple-600 text-white hover:bg-purple-700" 
                : "text-purple-600 border-purple-600 hover:bg-purple-50"
            )}
          >
            {isShowingInstanceFrame ? (
              <>
                <EyeOffIcon className="w-4 h-4 mr-1" />
                Hide
              </>
            ) : (
              <>
                <EyeIcon className="w-4 h-4 mr-1" />
                Show
              </>
            )}
          </Button>
        </div>
        
        <p className="text-xs text-purple-600 mt-2">
          {isShowingInstanceFrame 
            ? "VM interface is currently visible" 
            : "Click to show the virtual machine interface"
          }
        </p>
      </CardContent>
    </Card>
  );
}
```

## Layout Components

### 1. Sticky Bottom Content

**Purpose**: Chat interface with sticky bottom input

**Design Features**:
- **Sticky Positioning**: Input stays at bottom during scroll
- **Auto-scroll**: Automatic scroll to bottom on new messages
- **Responsive Design**: Adaptive layout for different screen sizes
- **Accessibility**: Proper focus management

**Implementation**:
```typescript
function StickyToBottomContent(props: {
  content: ReactNode;
  footer?: ReactNode;
  className?: string;
  contentClassName?: string;
}) {
  const context = useStickToBottomContext();
  
  return (
    <div
      ref={context.scrollRef}
      style={{ width: "100%", height: "100%" }}
      className={props.className}
    >
      <div ref={context.contentRef} className={props.contentClassName}>
        {props.content}
      </div>
      {props.footer}
    </div>
  );
}
```

### 2. Scroll to Bottom Button

**Purpose**: Navigation aid for long conversations

**Design Features**:
- **Conditional Display**: Only shows when not at bottom
- **Smooth Scrolling**: Animated scroll to bottom
- **Accessibility**: Keyboard navigation support
- **Visual Feedback**: Clear call-to-action styling

**Implementation**:
```typescript
function ScrollToBottom(props: { className?: string }) {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  if (isAtBottom) return null;
  
  return (
    <Button
      variant="outline"
      className={cn(
        "absolute bottom-full left-1/2 -translate-x-1/2 mb-4 animate-in fade-in-0 zoom-in-95",
        props.className
      )}
      onClick={() => scrollToBottom()}
    >
      <ArrowDown className="w-4 h-4 mr-2" />
      <span>Scroll to bottom</span>
    </Button>
  );
}
```

## Utility Components

### 1. Markdown Text Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/markdown-text.tsx`

**Purpose**: Render markdown content with syntax highlighting

**Design Features**:
- **Syntax Highlighting**: Code block highlighting with multiple languages
- **Math Rendering**: LaTeX math support with KaTeX
- **Link Handling**: External link management with security
- **Performance**: Memoized rendering for optimization

**Implementation**:
```typescript
export function MarkdownText({ content }: { content: string }) {
  const processedContent = useMemo(() => {
    // Process markdown content
    return content
      .replace(/\n/g, '\n\n') // Add line breaks for better rendering
      .replace(/`([^`]+)`/g, '<code>$1</code>'); // Inline code highlighting
  }, [content]);

  return (
    <div className="prose prose-sm max-w-none">
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex]}
        components={{
          code({ node, inline, className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <SyntaxHighlighter
                style={oneDark}
                language={match[1]}
                PreTag="div"
                className="rounded-md"
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          a({ href, children, ...props }) {
            return (
              <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline"
                {...props}
              >
                {children}
              </a>
            );
          },
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
}
```

### 2. Syntax Highlighter Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/syntax-highlighter.tsx`

**Purpose**: Code syntax highlighting with copy functionality

**Design Features**:
- **Language Detection**: Automatic language detection
- **Theme Support**: Light and dark theme support
- **Copy Functionality**: Copy code to clipboard
- **Performance**: Lazy loading of language definitions

**Implementation**:
```typescript
export function SyntaxHighlighter({
  language,
  code,
  className,
}: {
  language: string;
  code: string;
  className?: string;
}) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  return (
    <div className={cn("relative group", className)}>
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="h-8 w-8 p-0"
        >
          {copied ? (
            <CheckIcon className="w-4 h-4 text-green-600" />
          ) : (
            <CopyIcon className="w-4 h-4" />
          )}
        </Button>
      </div>
      
      <ReactSyntaxHighlighter
        language={language}
        style={oneDark}
        customStyle={{
          margin: 0,
          padding: '1rem',
          borderRadius: '0.5rem',
        }}
        showLineNumbers
      >
        {code}
      </ReactSyntaxHighlighter>
    </div>
  );
}
```

## Animation System

### 1. Framer Motion Integration

**Purpose**: Smooth animations and transitions

**Key Features**:
- **Layout Animations**: Automatic layout transitions
- **Spring Physics**: Natural motion animations
- **Gesture Support**: Touch and mouse interactions
- **Performance**: GPU-accelerated animations

**Animation Examples**:
```typescript
// Sidebar slide animation
<motion.div
  animate={{ x: isOpen ? 0 : -300 }}
  transition={{ type: "spring", stiffness: 300, damping: 30 }}
>

// Toggle switch animation
<motion.div
  animate={{ x: isActive ? "100%" : "0%" }}
  transition={{ type: "spring", stiffness: 300, damping: 30 }}
>

// Fade in animation
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0, y: -20 }}
  transition={{ duration: 0.2 }}
>
```

### 2. CSS Animations

**Purpose**: Lightweight animations for simple transitions

**Implementation**:
```css
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}
```

This comprehensive UI component system provides a robust foundation for building consistent, accessible, and performant user interfaces in the Gen-UI Computer Use application.