# Gen-UI Computer Use - Configuration Documentation

## Configuration Overview

The Gen-UI Computer Use system requires comprehensive configuration management to coordinate multiple services, APIs, and runtime environments. This document provides detailed configuration setup, environment management, and deployment considerations.

## Environment Configuration

### 1. Environment Variables

**Required Environment Variables**:
```bash
# OpenAI Configuration
OPENAI_API_KEY=sk-...your_openai_api_key

# Scrapybara VM Configuration
SCrapybara_API_KEY=your_scrapybara_api_key

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_API_KEY=your_supabase_service_role_key

# LangGraph Configuration
LANGGRAPH_API_URL=http://localhost:8000
LANGCHAIN_API_KEY=your_langchain_api_key

# Next.js Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NODE_ENV=development
```

**Optional Environment Variables**:
```bash
# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_ERROR_TRACKING=true
ENABLE_PERFORMANCE_MONITORING=true

# Performance Tuning
MAX_RECURSION_LIMIT=150
TIMEOUT_HOURS=0.1
BATCH_SIZE=10
CACHE_DURATION=300

# Security Settings
CORS_ORIGINS=http://localhost:3000,https://your-domain.com
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
ENABLE_REQUEST_LOGGING=true
```

### 2. Environment Setup

**Development Environment (.env.local)**:
```bash
# Copy from .env.example
cp .env.example .env.local

# Edit with your API keys
OPENAI_API_KEY=sk-...
SCrapybara_API_KEY=...
SUPABASE_URL=...
SUPABASE_API_KEY=...
LANGGRAPH_API_URL=http://localhost:8000
LANGCHAIN_API_KEY=...
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

**Production Environment (.env.production)**:
```bash
# Production API endpoints
LANGGRAPH_API_URL=https://your-agent-server.com
NEXT_PUBLIC_API_URL=https://your-app.com/api

# Production database
SUPABASE_URL=https://your-prod-project.supabase.co
SUPABASE_API_KEY=your_prod_service_role_key

# Production VM service
SCrapybara_API_KEY=your_prod_scrapybara_key

# Security settings
NODE_ENV=production
CORS_ORIGINS=https://your-app.com
RATE_LIMIT_MAX=1000
```

**Staging Environment (.env.staging)**:
```bash
# Staging configuration
LANGGRAPH_API_URL=https://staging-agent-server.com
NEXT_PUBLIC_API_URL=https://staging-app.com/api
NODE_ENV=staging

# Staging services
SUPABASE_URL=https://staging-project.supabase.co
SCrapybara_API_KEY=your_staging_scrapybara_key

# Reduced limits for staging
RATE_LIMIT_MAX=500
TIMEOUT_HOURS=0.05
```

### 3. Environment Validation

**Environment Validation Script**:
```typescript
// src/lib/env-validation.ts
import { z } from 'zod';

const envSchema = z.object({
  OPENAI_API_KEY: z.string().min(1, "OpenAI API key is required"),
  SCrapybara_API_KEY: z.string().min(1, "Scrapybara API key is required"),
  SUPABASE_URL: z.string().url("Valid Supabase URL is required"),
  SUPABASE_API_KEY: z.string().min(1, "Supabase API key is required"),
  LANGGRAPH_API_URL: z.string().url("Valid LangGraph API URL is required"),
  LANGCHAIN_API_KEY: z.string().min(1, "LangChain API key is required"),
  NEXT_PUBLIC_API_URL: z.string().url("Valid Next.js API URL is required"),
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
});

export const validateEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Environment validation failed:');
      error.errors.forEach(err => {
        console.error(`  ${err.path.join('.')}: ${err.message}`);
      });
      process.exit(1);
    }
    throw error;
  }
};

// Usage in app startup
validateEnv();
```

## LangGraph Configuration

### 1. LangGraph Configuration File

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/langgraph.json`

**Configuration Structure**:
```json
{
  "node_version": "20",
  "graphs": {
    "agent": "./src/agent/index.ts:graph"
  },
  "ui": {
    "agent": "./src/agent/ui/index.tsx"
  },
  "ui_config": {
    "shared": [
      "nuqs",
      "nuqs/adapters/next/app",
      "@/components/ui/sonner",
      "sonner"
    ]
  },
  "env": ".env",
  "dependencies": ["."],
  "runtime": {
    "timeout": 300,
    "memory": 512,
    "cpu": 1.0
  },
  "scaling": {
    "min_instances": 1,
    "max_instances": 10,
    "target_utilization": 0.8
  }
}
```

**Configuration Options**:
- **node_version**: Node.js runtime version
- **graphs**: Agent graph definitions
- **ui**: UI component mappings
- **ui_config**: Shared dependencies configuration
- **env**: Environment variables file
- **dependencies**: Package dependencies
- **runtime**: Runtime resource limits
- **scaling**: Auto-scaling configuration

### 2. Agent Configuration

**Agent Configuration Interface**:
```typescript
interface AgentConfig {
  recursionLimit: number;
  timeoutHours: number;
  model: string;
  temperature: number;
  maxTokens: number;
  enableScreenshots: boolean;
  screenshotQuality: 'low' | 'medium' | 'high';
  retryAttempts: number;
  retryDelay: number;
}

const defaultAgentConfig: AgentConfig = {
  recursionLimit: 150,
  timeoutHours: 0.1,
  model: 'gpt-4-vision-preview',
  temperature: 0.1,
  maxTokens: 4000,
  enableScreenshots: true,
  screenshotQuality: 'medium',
  retryAttempts: 3,
  retryDelay: 1000,
};
```

**Agent Configuration Usage**:
```typescript
export const graph = createCua({
  nodeBeforeAction: beforeNode,
  nodeAfterAction: afterNode,
  stateModifier: GraphAnnotation,
  recursionLimit: config.recursionLimit,
  timeoutHours: config.timeoutHours,
  uploadScreenshot,
  model: config.model,
  temperature: config.temperature,
});
```

### 3. UI Configuration

**UI Shared Dependencies**:
```typescript
// Shared dependencies loaded in LangGraph UI
const sharedDependencies = [
  'nuqs',
  'nuqs/adapters/next/app',
  '@/components/ui/sonner',
  'sonner',
  'framer-motion',
  'lucide-react',
  '@radix-ui/react-tooltip',
];

// Component map configuration
const ComponentMap = {
  'computer-use-tool-output': ComputerUseToolOutput,
  'computer-use-tool-call': ComputerUseToolCall,
  'render-vm-button': RenderVMButton,
  'instance': InstanceFrame,
} as const;
```

## Next.js Configuration

### 1. Next.js Configuration File

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/next.config.mjs`

**Configuration Structure**:
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverActions: true,
    serverComponentsExternalPackages: ['@langchain/core'],
  },
  images: {
    domains: ['supabase.co'],
    formats: ['image/webp', 'image/avif'],
  },
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.CORS_ORIGINS || '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: '/chat',
        permanent: false,
      },
    ];
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
};

export default nextConfig;
```

### 2. TypeScript Configuration

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/tsconfig.json`

**TypeScript Configuration**:
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    ".next",
    "dist",
    "build"
  ]
}
```

### 3. Tailwind Configuration

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/tailwind.config.js`

**Tailwind Configuration**:
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: 0 },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: 0 },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    require('tailwind-scrollbar')({ nocompatible: true }),
  ],
}
```

## Build Configuration

### 1. Package.json Scripts

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/package.json`

**Build Scripts**:
```json
{
  "scripts": {
    "dev": "next dev",
    "agent": "npx @langchain/langgraph-cli dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "analyze": "ANALYZE=true npm run build",
    "clean": "rm -rf .next dist build"
  }
}
```

### 2. PostCSS Configuration

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/postcss.config.mjs`

**PostCSS Configuration**:
```javascript
/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    ...(process.env.NODE_ENV === 'production' && {
      cssnano: {
        preset: 'default',
      },
    }),
  },
};

export default config;
```

### 3. ESLint Configuration

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/eslint.config.js`

**ESLint Configuration**:
```javascript
import js from '@eslint/js';
import globals from 'globals';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      '@typescript-eslint/no-unused-vars': [
        'error',
        { argsIgnorePattern: '^_' },
      ],
      '@typescript-eslint/no-explicit-any': 'warn',
    },
  },
);
```

## Service Configuration

### 1. Supabase Configuration

**Database Setup**:
```sql
-- Create screenshots bucket
CREATE BUCKET cua-screenshots;

-- Set up RLS policies
CREATE POLICY "Allow public access" ON storage.objects
  FOR SELECT USING (bucket_id = 'cua-screenshots');

CREATE POLICY "Allow authenticated uploads" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'cua-screenshots');

-- Create threads table
CREATE TABLE threads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  title TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for threads
ALTER TABLE threads ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own threads" ON threads
  FOR SELECT USING (user_id = current_setting('request.user_id', true));

CREATE POLICY "Users can insert their own threads" ON threads
  FOR INSERT WITH CHECK (user_id = current_setting('request.user_id', true));
```

**Client Configuration**:
```typescript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: false,
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'X-Client-Info': 'gen-ui-computer-use',
    },
  },
});
```

### 2. Scrapybara Configuration

**API Client Configuration**:
```typescript
class ScrapybaraClient {
  private apiKey: string;
  private baseUrl: string;
  private timeout: number;

  constructor(config: {
    apiKey: string;
    baseUrl?: string;
    timeout?: number;
  }) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://api.scrapybara.com/v1';
    this.timeout = config.timeout || 30000;
  }

  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
      signal: AbortSignal.timeout(this.timeout),
    });

    if (!response.ok) {
      throw new Error(`Scrapybara API error: ${response.status}`);
    }

    return response.json();
  }

  async createInstance(config: {
    environment: string;
    region?: string;
    resources?: {
      cpu: number;
      memory: number;
      disk: number;
    };
  }) {
    return this.request('/instances', {
      method: 'POST',
      body: JSON.stringify(config),
    });
  }

  async getInstance(instanceId: string) {
    return this.request(`/instances/${instanceId}`);
  }

  async pauseInstance(instanceId: string) {
    return this.request(`/instances/${instanceId}/pause`, {
      method: 'POST',
    });
  }

  async resumeInstance(instanceId: string) {
    return this.request(`/instances/${instanceId}/resume`, {
      method: 'POST',
    });
  }

  async terminateInstance(instanceId: string) {
    return this.request(`/instances/${instanceId}/terminate`, {
      method: 'POST',
    });
  }
}
```

### 3. OpenAI Configuration

**Model Configuration**:
```typescript
import { OpenAI } from '@langchain/openai';

const openaiConfig = {
  apiKey: process.env.OPENAI_API_KEY,
  model: 'gpt-4-vision-preview',
  temperature: 0.1,
  maxTokens: 4000,
  timeout: 30000,
  maxRetries: 3,
};

export const openai = new OpenAI(openaiConfig);
```

## Deployment Configuration

### 1. Production Deployment

**Dockerfile**:
```dockerfile
FROM node:20-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM base AS build
COPY . .
RUN npm run build

FROM base AS runtime
COPY --from=build /app/.next ./.next
COPY --from=build /app/public ./public
COPY --from=build /app/package.json ./package.json
EXPOSE 3000
CMD ["npm", "start"]
```

**Docker Compose**:
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SCrapybara_API_KEY=${SCrapybara_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_API_KEY=${SUPABASE_API_KEY}
      - LANGGRAPH_API_URL=${LANGGRAPH_API_URL}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
    depends_on:
      - agent
    restart: unless-stopped

  agent:
    image: langchain/langgraph-agent:latest
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SCrapybara_API_KEY=${SCrapybara_API_KEY}
    volumes:
      - ./src/agent:/app/agent
      - ./langgraph.json:/app/langgraph.json
    restart: unless-stopped
```

### 2. Vercel Deployment

**vercel.json**:
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    }
  ],
  "env": {
    "OPENAI_API_KEY": "@openai-api-key",
    "SCrapybara_API_KEY": "@scrapybara-api-key",
    "SUPABASE_URL": "@supabase-url",
    "SUPABASE_API_KEY": "@supabase-api-key",
    "LANGGRAPH_API_URL": "@langgraph-api-url",
    "LANGCHAIN_API_KEY": "@langchain-api-key"
  },
  "functions": {
    "src/app/api/**/route.ts": {
      "runtime": "edge"
    }
  }
}
```

### 3. Environment-Specific Configurations

**Development Configuration**:
```typescript
const devConfig = {
  apiUrl: 'http://localhost:3000/api',
  langGraphUrl: 'http://localhost:8000',
  enableDebug: true,
  enableHotReload: true,
  corsOrigins: ['http://localhost:3000'],
  logLevel: 'debug',
  cacheDisabled: true,
};
```

**Production Configuration**:
```typescript
const prodConfig = {
  apiUrl: 'https://your-app.vercel.app/api',
  langGraphUrl: 'https://your-agent-server.com',
  enableDebug: false,
  enableHotReload: false,
  corsOrigins: ['https://your-app.vercel.app'],
  logLevel: 'error',
  cacheDisabled: false,
  compressionEnabled: true,
  httpsOnly: true,
};
```

## Configuration Best Practices

### 1. Security Best Practices

**Environment Variables Security**:
- Never commit environment variables to version control
- Use different API keys for different environments
- Rotate API keys regularly
- Use environment variable validation
- Implement proper access controls

**Configuration Validation**:
```typescript
const validateConfig = (config: any) => {
  const required = [
    'OPENAI_API_KEY',
    'SCrapybara_API_KEY',
    'SUPABASE_URL',
    'SUPABASE_API_KEY',
  ];

  const missing = required.filter(key => !config[key]);
  if (missing.length > 0) {
    throw new Error(`Missing required configuration: ${missing.join(', ')}`);
  }
};
```

### 2. Performance Configuration

**Optimization Settings**:
```typescript
const performanceConfig = {
  // Caching
  cacheEnabled: process.env.NODE_ENV === 'production',
  cacheTtl: 300, // 5 minutes
  
  // Rate limiting
  rateLimitWindow: 60000, // 1 minute
  rateLimitMax: process.env.NODE_ENV === 'production' ? 1000 : 100,
  
  // Timeouts
  apiTimeout: 30000,
  streamTimeout: 60000,
  
  // Batching
  batchSize: 10,
  batchTimeout: 100,
  
  // Resources
  maxConcurrentRequests: 10,
  maxMemoryUsage: 512 * 1024 * 1024, // 512MB
};
```

### 3. Monitoring Configuration

**Logging Configuration**:
```typescript
const loggingConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: process.env.LOG_FORMAT || 'json',
  enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING === 'true',
  enableErrorTracking: process.env.ENABLE_ERROR_TRACKING === 'true',
  enablePerformanceMonitoring: process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
  
  // Log destinations
  destinations: {
    console: true,
    file: process.env.NODE_ENV === 'production',
    remote: process.env.NODE_ENV === 'production',
  },
};
```

This comprehensive configuration system ensures secure, performant, and maintainable deployment across all environments while maintaining flexibility for different use cases and requirements.