# Gen-UI Computer Use - Components Documentation

## Component Architecture Overview

The Gen-UI Computer Use system implements a sophisticated component architecture that combines traditional React components with dynamically generated UI elements. This system supports both static UI components and runtime-generated components based on agent state and actions.

## Component Hierarchy

```mermaid
graph TB
    subgraph "Application Layer"
        App[App Layout<br/>src/app/layout.tsx]
        Page[Main Page<br/>src/app/page.tsx]
    end
    
    subgraph "Provider Layer"
        ThreadProvider[Thread Provider<br/>src/providers/Thread.tsx]
        StreamProvider[Stream Provider<br/>src/providers/Stream.tsx]
    end
    
    subgraph "Core Components"
        Thread[Thread Component<br/>src/components/thread/index.tsx]
        Messages[Message Components<br/>src/components/thread/messages/]
        History[Thread History<br/>src/components/thread/history/]
    end
    
    subgraph "Agent UI Components"
        AgentUI[Agent UI Index<br/>src/agent/ui/index.tsx]
        ToolCall[Tool Call Component<br/>src/agent/ui/computer-use-tool-call.tsx]
        ToolOutput[Tool Output Component<br/>src/agent/ui/computer-use-tool-output.tsx]
        VMButton[VM Button Component<br/>src/agent/ui/render-vm-button.tsx]
        Instance[Instance Frame<br/>src/agent/ui/instance/]
    end
    
    subgraph "Base UI Components"
        Button[Button<br/>src/components/ui/button.tsx]
        Card[Card<br/>src/components/ui/card.tsx]
        Input[Input<br/>src/components/ui/input.tsx]
        Avatar[Avatar<br/>src/components/ui/avatar.tsx]
    end
    
    App --> Page
    Page --> ThreadProvider
    ThreadProvider --> StreamProvider
    StreamProvider --> Thread
    Thread --> Messages
    Thread --> History
    Thread --> AgentUI
    AgentUI --> ToolCall
    AgentUI --> ToolOutput
    AgentUI --> VMButton
    AgentUI --> Instance
    Thread --> Button
    Thread --> Card
    Thread --> Input
    Thread --> Avatar
```

## Core Application Components

### 1. App Layout Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/layout.tsx`

**Purpose**: Root layout component with global providers and styling

**Key Features**:
- **Global Styles**: Imports global CSS and Tailwind styles
- **Font Configuration**: Manages font loading and optimization
- **Metadata**: Sets page title, description, and meta tags
- **Provider Setup**: Initializes React Query and other global providers

**Implementation Structure**:
```typescript
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={cn("min-h-screen bg-background font-sans antialiased")}>
        {children}
      </body>
    </html>
  );
}
```

### 2. Main Page Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/page.tsx`

**Purpose**: Primary application entry point with provider hierarchy

**Key Features**:
- **Suspense Boundary**: Loading state management
- **Provider Nesting**: ThreadProvider > StreamProvider > Thread
- **Toast Notifications**: Global notification system
- **Error Boundaries**: Error handling and recovery

**Implementation**:
```typescript
export default function DemoPage(): React.ReactNode {
  return (
    <React.Suspense fallback={<div>Loading (layout)...</div>}>
      <Toaster />
      <ThreadProvider>
        <StreamProvider>
          <Thread />
        </StreamProvider>
      </ThreadProvider>
    </React.Suspense>
  );
}
```

## Provider Components

### 1. Thread Provider

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/providers/Thread.tsx`

**Purpose**: Manages conversation threads and history

**Key Features**:
- **Thread Management**: Create, update, and delete threads
- **History Persistence**: Local storage integration
- **User Context**: User identification and session management
- **API Integration**: LangGraph client configuration

**State Management**:
```typescript
interface ThreadContextType {
  threads: Thread[];
  setThreads: (threads: Thread[]) => void;
  getThreads: (userId: string) => Promise<Thread[]>;
  deleteThread: (threadId: string) => Promise<void>;
  updateThread: (threadId: string, updates: Partial<Thread>) => Promise<void>;
}
```

### 2. Stream Provider

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/providers/Stream.tsx`

**Purpose**: Manages real-time streaming communication with LangGraph agent

**Key Features**:
- **WebSocket Management**: Real-time communication
- **State Synchronization**: Agent state updates
- **UI Message Handling**: Dynamic UI component management
- **Error Recovery**: Connection resilience

**State Type Definition**:
```typescript
export type StateType = {
  messages: Message[];
  ui?: UIMessage[];
  streamUrl?: string;
  instanceId?: string;
  environment?: string;
};
```

**Stream Configuration**:
```typescript
const useTypedStream = useStream<StateType, {
  UpdateType: {
    messages?: Message[] | Message | string;
    ui?: (UIMessage | RemoveUIMessage)[] | UIMessage | RemoveUIMessage;
    streamUrl?: string;
    instanceId?: string;
    environment?: string;
  };
  CustomEventType: UIMessage | RemoveUIMessage;
}>;
```

## Core Thread Components

### 1. Thread Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/index.tsx`

**Purpose**: Main conversation interface with dual-pane layout

**Key Features**:
- **Dual Interface**: Chat and computer view toggle
- **Responsive Design**: Mobile-first responsive layout
- **Animation Support**: Framer Motion animations
- **State Management**: URL state persistence

**Layout Structure**:
```typescript
<div className="flex w-full h-screen overflow-hidden">
  {/* Sidebar */}
  <motion.div className="absolute h-full border-r bg-white overflow-hidden z-20">
    <ThreadHistory />
  </motion.div>
  
  {/* Main Content */}
  <motion.div className="flex-1 flex flex-col min-w-0 overflow-hidden">
    {/* Header */}
    <div className="flex items-center justify-between gap-3 p-2 pl-4 z-10">
      {/* Navigation and controls */}
    </div>
    
    {/* Chat/Computer Toggle */}
    <div className="flex items-center justify-center my-4 lg:hidden">
      {/* Toggle buttons */}
    </div>
    
    {/* Content Area */}
    <div className="flex flex-1 overflow-hidden">
      <ChatView />
      {customInstanceViewComponent && (
        <div className="overflow-hidden flex-1 my-auto">
          <LoadExternalComponent />
        </div>
      )}
    </div>
  </motion.div>
</div>
```

**Animation Implementation**:
```typescript
<motion.div
  animate={{
    marginLeft: chatHistoryOpen ? (isLargeScreen ? 300 : 0) : 0,
    width: chatHistoryOpen ? (isLargeScreen ? "calc(100% - 300px)" : "100%") : "100%",
  }}
  transition={{ type: "spring", stiffness: 300, damping: 30 }}
>
```

### 2. Chat View Component

**Purpose**: Chat interface with sticky bottom input

**Key Features**:
- **Sticky Bottom**: Input stays at bottom during scroll
- **Auto-scroll**: Automatic scroll to bottom on new messages
- **Message Rendering**: Human and AI message display
- **Loading States**: Skeleton loading for pending messages

**Sticky Bottom Implementation**:
```typescript
function StickyToBottomContent(props: {
  content: ReactNode;
  footer?: ReactNode;
  className?: string;
  contentClassName?: string;
}) {
  const context = useStickToBottomContext();
  return (
    <div ref={context.scrollRef} className={props.className}>
      <div ref={context.contentRef} className={props.contentClassName}>
        {props.content}
      </div>
      {props.footer}
    </div>
  );
}
```

### 3. Thread History Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/history/index.tsx`

**Purpose**: Sidebar with conversation history

**Key Features**:
- **Thread List**: Chronological thread display
- **Search Functionality**: Filter threads by content
- **Quick Actions**: Delete, rename, and share threads
- **Performance**: Virtualized list for large thread counts

## Message Components

### 1. Human Message Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/messages/human.tsx`

**Purpose**: Display user messages with avatar and timestamp

**Key Features**:
- **Avatar Display**: User avatar with fallback
- **Timestamp**: Relative time display
- **Message Content**: Markdown rendering support
- **Action Buttons**: Copy, edit, and delete options

**Implementation**:
```typescript
export function HumanMessage({
  message,
  isLoading,
}: {
  message: Message;
  isLoading: boolean;
}) {
  return (
    <div className="flex gap-3 items-start">
      <Avatar className="w-8 h-8 flex-shrink-0">
        <AvatarFallback>U</AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-sm font-medium">You</span>
          <span className="text-xs text-muted-foreground">
            {format(new Date(message.created_at), "HH:mm")}
          </span>
        </div>
        
        <div className="bg-muted rounded-lg p-3">
          <MarkdownText content={message.content} />
        </div>
      </div>
    </div>
  );
}
```

### 2. AI Message Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/messages/ai.tsx`

**Purpose**: Display AI responses with tool calls and actions

**Key Features**:
- **Streaming Display**: Real-time message rendering
- **Tool Call Display**: Computer use action visualization
- **Regenerate Button**: Message regeneration capability
- **Loading States**: Skeleton and spinner components

**Streaming Implementation**:
```typescript
export function AssistantMessage({
  message,
  isLoading,
  handleRegenerate,
}: {
  message: Message;
  isLoading: boolean;
  handleRegenerate: (checkpoint: Checkpoint | null | undefined) => void;
}) {
  const [displayedContent, setDisplayedContent] = useState("");
  
  useEffect(() => {
    if (message.content && typeof message.content === "string") {
      // Simulate streaming by gradually revealing content
      const content = message.content;
      let currentIndex = 0;
      
      const interval = setInterval(() => {
        if (currentIndex < content.length) {
          setDisplayedContent(content.slice(0, currentIndex + 1));
          currentIndex++;
        } else {
          clearInterval(interval);
        }
      }, 10);
      
      return () => clearInterval(interval);
    }
  }, [message.content]);
  
  return (
    <div className="flex gap-3 items-start">
      <Avatar className="w-8 h-8 flex-shrink-0">
        <AvatarFallback>AI</AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <MarkdownText content={displayedContent} />
        <ToolCalls message={message} />
        
        {!isLoading && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleRegenerate(message.checkpoint)}
          >
            Regenerate
          </Button>
        )}
      </div>
    </div>
  );
}
```

### 3. Tool Calls Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/messages/tool-calls.tsx`

**Purpose**: Display computer use tool executions

**Key Features**:
- **Action Visualization**: Computer actions with icons
- **Result Display**: Tool execution results
- **Error Handling**: Error state visualization
- **Performance**: Lazy loading of tool results

## Agent UI Components

### 1. Agent UI Index

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/index.tsx`

**Purpose**: Component mapping for dynamic UI generation

**Component Mapping**:
```typescript
const ComponentMap = {
  "computer-use-tool-output": ComputerUseToolOutput,
  "computer-use-tool-call": ComputerUseToolCall,
  "render-vm-button": RenderVMButton,
  instance: InstanceFrame,
} as const;

export default ComponentMap;
```

**Usage in Agent**:
```typescript
const ui = typedUi<typeof ComponentMap>(config);
ui.push({
  name: "computer-use-tool-call",
  props: {
    toolCallId: tc.id,
    action: tc.action,
  },
});
```

### 2. Computer Use Tool Call Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/computer-use-tool-call.tsx`

**Purpose**: Display computer use tool execution

**Key Features**:
- **Action Display**: Visual representation of computer actions
- **Parameter Visualization**: Tool parameters and arguments
- **Status Indicators**: Pending, running, and completed states
- **Error Handling**: Error state visualization

**Implementation**:
```typescript
export function ComputerUseToolCall({
  toolCallId,
  action,
}: {
  toolCallId: string;
  action: string;
}) {
  const actionData = JSON.parse(action);
  
  return (
    <div className="border rounded-lg p-4 bg-blue-50">
      <div className="flex items-center gap-2 mb-2">
        <MonitorIcon className="w-4 h-4 text-blue-600" />
        <span className="text-sm font-medium">Computer Action</span>
      </div>
      
      <div className="text-sm text-gray-600">
        <div><strong>Action:</strong> {actionData.action}</div>
        {actionData.coordinate && (
          <div><strong>Coordinate:</strong> [{actionData.coordinate.join(", ")}]</div>
        )}
        {actionData.text && (
          <div><strong>Text:</strong> {actionData.text}</div>
        )}
      </div>
    </div>
  );
}
```

### 3. Computer Use Tool Output Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/computer-use-tool-output.tsx`

**Purpose**: Display computer use tool results and screenshots

**Key Features**:
- **Screenshot Display**: Full-size screenshot with zoom
- **Result Visualization**: Tool execution results
- **Error Display**: Error messages and stack traces
- **Performance**: Lazy loading and caching

**Implementation**:
```typescript
export function ComputerUseToolOutput({
  toolCallId,
  screenshot,
}: {
  toolCallId: string;
  screenshot: string;
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  return (
    <div className="border rounded-lg p-4 bg-green-50">
      <div className="flex items-center gap-2 mb-2">
        <CheckCircleIcon className="w-4 h-4 text-green-600" />
        <span className="text-sm font-medium">Computer Action Result</span>
      </div>
      
      {screenshot && (
        <div className="mt-2">
          <img
            src={screenshot}
            alt="Computer screenshot"
            className={cn(
              "cursor-pointer border rounded",
              isExpanded ? "w-full" : "w-48 h-32 object-cover"
            )}
            onClick={() => setIsExpanded(!isExpanded)}
          />
        </div>
      )}
    </div>
  );
}
```

### 4. Render VM Button Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/render-vm-button.tsx`

**Purpose**: Button to show/hide VM instance

**Key Features**:
- **Toggle Functionality**: Show/hide VM interface
- **State Persistence**: Remember user preference
- **Visual Indicators**: Button state visualization
- **Accessibility**: Keyboard navigation support

### 5. Instance Frame Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/instance/index.tsx`

**Purpose**: VM instance display and control

**Key Features**:
- **Iframe Integration**: Direct VM interaction
- **Status Management**: VM state monitoring
- **Control Buttons**: Pause, resume, terminate
- **Screenshot Fallback**: Static image when VM is paused

**Status Management**:
```typescript
export function InstanceFrame({ streamUrl, instanceId }: InstanceFrameProps) {
  const {
    handleStop,
    handlePause,
    handleResume,
    isStopping,
    isStopped,
    status,
    screenshot,
    isLoading,
  } = useInstanceActions({ instanceId });
  
  if (isLoading) {
    return (
      <div className="w-[630px] h-[420px] lg:w-[830px] lg:h-[620px] flex items-center justify-center">
        <LoaderCircle className="w-8 h-8 animate-spin" />
      </div>
    );
  }
  
  if (status === "terminated" && screenshot) {
    return (
      <div className="relative">
        <img src={screenshot} alt="Terminated instance" className="opacity-70" />
        <div className="absolute inset-0 flex items-center justify-center bg-black/30">
          <div className="bg-card/90 p-6 rounded-lg shadow-lg text-center">
            <h3 className="text-lg font-semibold mb-2">Instance Terminated</h3>
            <Button onClick={() => setThreadId(null)}>Create New Chat</Button>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <iframe
      src={streamUrl}
      className="w-full h-full min-h-[400px] md:min-h-[632px]"
      title="Instance Frame"
      allow="clipboard-write"
    />
  );
}
```

## Instance Management Components

### 1. Instance View Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/instance/instance-view.tsx`

**Purpose**: Container for VM instance with controls

**Key Features**:
- **Window Manager**: VM window controls
- **Responsive Layout**: Adaptive sizing
- **Control Bar**: Action buttons and status
- **Fullscreen Support**: Expandable interface

### 2. Instance Actions Hook

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/instance/useInstanceActions.tsx`

**Purpose**: VM instance action management

**Key Features**:
- **API Integration**: VM control API calls
- **State Management**: Instance state tracking
- **Error Handling**: Robust error recovery
- **Performance**: Debounced actions

**Implementation**:
```typescript
export function useInstanceActions({ instanceId }: { instanceId: string }) {
  const [status, setStatus] = useState<"unknown" | "running" | "paused" | "terminated">("unknown");
  const [isStopping, setIsStopping] = useState(false);
  const [screenshot, setScreenshot] = useState<string | undefined>(undefined);
  
  const handleStop = async () => {
    setIsStopping(true);
    try {
      await fetch("/api/instance/stop", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });
      setStatus("terminated");
    } catch (error) {
      console.error("Failed to stop instance:", error);
    } finally {
      setIsStopping(false);
    }
  };
  
  const handlePause = async () => {
    try {
      await fetch("/api/instance/pause", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });
      setStatus("paused");
    } catch (error) {
      console.error("Failed to pause instance:", error);
    }
  };
  
  const handleResume = async () => {
    try {
      await fetch("/api/instance/resume", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });
      setStatus("running");
    } catch (error) {
      console.error("Failed to resume instance:", error);
    }
  };
  
  return {
    status,
    setStatus,
    handleStop,
    handlePause,
    handleResume,
    isStopping,
    screenshot,
    setScreenshot,
  };
}
```

### 3. Window Manager Buttons

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/instance/window-manager-buttons.tsx`

**Purpose**: VM window control buttons

**Key Features**:
- **Window Controls**: Minimize, maximize, close
- **Visual Feedback**: Hover and active states
- **Accessibility**: Keyboard navigation
- **Responsive**: Mobile-friendly controls

## Base UI Components

### 1. Button Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/button.tsx`

**Purpose**: Reusable button component with variants

**Key Features**:
- **Variant System**: Primary, secondary, destructive, ghost
- **Size System**: Small, medium, large
- **Loading States**: Spinner integration
- **Accessibility**: Focus management and ARIA attributes

**Implementation**:
```typescript
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "underline-offset-4 hover:underline text-primary",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);
```

### 2. Card Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/card.tsx`

**Purpose**: Container component for content sections

**Key Features**:
- **Semantic Structure**: Header, content, footer
- **Responsive Design**: Mobile-first approach
- **Shadow System**: Consistent elevation
- **Customizable**: Props for styling overrides

### 3. Input Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/input.tsx`

**Purpose**: Form input component with validation

**Key Features**:
- **Validation States**: Error, success, warning
- **Accessibility**: Label association and screen reader support
- **Customization**: Size and variant options
- **Performance**: Debounced input handling

### 4. Avatar Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/ui/avatar.tsx`

**Purpose**: User avatar display with fallback

**Key Features**:
- **Image Handling**: Automatic fallback to initials
- **Size System**: Multiple size options
- **Performance**: Lazy loading and caching
- **Accessibility**: Alt text and screen reader support

## Utility Components

### 1. Markdown Text Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/markdown-text.tsx`

**Purpose**: Render markdown content with syntax highlighting

**Key Features**:
- **Syntax Highlighting**: Code block highlighting
- **Math Rendering**: LaTeX math support
- **Link Handling**: External link management
- **Performance**: Memoized rendering

### 2. Syntax Highlighter Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/syntax-highlighter.tsx`

**Purpose**: Code syntax highlighting

**Key Features**:
- **Language Detection**: Automatic language detection
- **Theme Support**: Light and dark themes
- **Copy Functionality**: Copy code to clipboard
- **Performance**: Lazy loading of language definitions

### 3. Tooltip Icon Button

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/tooltip-icon-button.tsx`

**Purpose**: Icon button with tooltip

**Key Features**:
- **Tooltip Display**: Hover tooltip with description
- **Icon Integration**: Lucide icon support
- **Accessibility**: Keyboard navigation and screen reader support
- **Customization**: Size and variant options

## Component Communication Patterns

### 1. Props Drilling Prevention

**Context Usage**:
- **Stream Context**: Real-time data sharing
- **Thread Context**: Conversation state sharing
- **UI Context**: Component state sharing

### 2. Event Handling

**Event Bubbling**:
- **Click Events**: Proper event delegation
- **Keyboard Events**: Comprehensive keyboard support
- **Form Events**: Validation and submission handling

### 3. State Synchronization

**State Updates**:
- **Optimistic Updates**: Immediate UI feedback
- **Server Reconciliation**: State consistency
- **Error Recovery**: Rollback mechanisms

This comprehensive component system provides a robust foundation for the generative UI computer use application, combining traditional React patterns with dynamic component generation for optimal user experience.