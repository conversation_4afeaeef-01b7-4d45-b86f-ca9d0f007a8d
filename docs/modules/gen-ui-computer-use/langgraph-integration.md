# Gen-UI Computer Use - LangGraph Integration Documentation

## LangGraph Integration Overview

The Gen-UI Computer Use system is built on a sophisticated LangGraph integration that enables seamless AI agent orchestration with dynamic UI generation. This document provides comprehensive details on how LangGraph is integrated, configured, and utilized throughout the system.

## LangGraph Architecture

### 1. Core LangGraph Components

**LangGraph Packages**:
- `@langchain/langgraph` v0.2.60: Core graph execution engine
- `@langchain/langgraph-sdk` v0.0.61: TypeScript SDK for client integration
- `@langchain/langgraph-api` v0.0.19: API interface layer
- `@langchain/langgraph-cli` v0.0.19: Command-line interface for development
- `@langchain/langgraph-cua` v0.0.5: Computer Use Agent prebuilt package

**Integration Architecture**:
```mermaid
graph TB
    subgraph "Client Layer"
        UI[Next.js UI]
        SDK[LangGraph SDK]
    end
    
    subgraph "LangGraph Server"
        API[LangGraph API]
        Graph[Agent Graph]
        CUA[Computer Use Agent]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        VM[Scrapybara VM]
        Storage[Supabase Storage]
    end
    
    UI --> SDK
    SDK --> API
    API --> Graph
    Graph --> CUA
    CUA --> OpenAI
    CUA --> VM
    CUA --> Storage
```

### 2. LangGraph Configuration

**Configuration File** (`langgraph.json`):
```json
{
  "node_version": "20",
  "graphs": {
    "agent": "./src/agent/index.ts:graph"
  },
  "ui": {
    "agent": "./src/agent/ui/index.tsx"
  },
  "ui_config": {
    "shared": [
      "nuqs",
      "nuqs/adapters/next/app",
      "@/components/ui/sonner",
      "sonner"
    ]
  },
  "env": ".env",
  "dependencies": ["."]
}
```

**Key Configuration Elements**:
- **Node Version**: Specifies Node.js runtime version
- **Graph Definition**: Points to the main agent graph export
- **UI Configuration**: Defines the UI component mapping
- **Shared Dependencies**: Lists shared client-side dependencies
- **Environment**: Specifies environment variable file

## Computer Use Agent Integration

### 1. CUA Implementation

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/index.ts`

**Core Implementation**:
```typescript
import {
  createCua,
  CUAAnnotation,
  CUAState,
  CUAUpdate,
  getToolOutputs,
  isComputerCallToolMessage,
} from "@langchain/langgraph-cua";

const GraphAnnotation = Annotation.Root({
  ...CUAAnnotation.spec,
  ui: Annotation<
    UIMessage[],
    UIMessage | RemoveUIMessage | (UIMessage | RemoveUIMessage)[]
  >({ default: () => [], reducer: uiMessageReducer }),
});

type GraphState = typeof GraphAnnotation.State & CUAState;

export const graph = createCua({
  nodeBeforeAction: beforeNode,
  nodeAfterAction: afterNode,
  stateModifier: GraphAnnotation,
  recursionLimit: 150,
  timeoutHours: 0.1,
  uploadScreenshot,
});
```

**CUA Configuration Parameters**:
- **nodeBeforeAction**: Pre-action hook for UI updates
- **nodeAfterAction**: Post-action hook for result processing
- **stateModifier**: Custom state annotation for UI integration
- **recursionLimit**: Maximum graph execution depth
- **timeoutHours**: Maximum execution time
- **uploadScreenshot**: Custom screenshot handling function

### 2. State Management

**State Annotation**:
```typescript
const GraphAnnotation = Annotation.Root({
  ...CUAAnnotation.spec,
  ui: Annotation<
    UIMessage[],
    UIMessage | RemoveUIMessage | (UIMessage | RemoveUIMessage)[]
  >({ 
    default: () => [], 
    reducer: uiMessageReducer 
  }),
});
```

**State Components**:
- **CUAAnnotation.spec**: Standard Computer Use Agent state
- **UI Messages**: Dynamic UI component state
- **Message Reducer**: UI state update logic
- **Custom Fields**: Additional state for VM management

**State Type Definition**:
```typescript
type GraphState = typeof GraphAnnotation.State & CUAState;

interface ExtendedState extends CUAState {
  messages: Message[];
  ui: UIMessage[];
  streamUrl?: string;
  instanceId?: string;
  environment?: string;
}
```

### 3. Node Lifecycle Hooks

**Before Node Action**:
```typescript
async function beforeNode(
  state: GraphState,
  config: LangGraphRunnableConfig,
): Promise<CUAUpdate> {
  const ui = typedUi<typeof ComponentMap>(config);
  const lastMessage = state.messages[state.messages.length - 1];
  const toolCalls = getToolOutputs(lastMessage);

  // Manage VM instance UI components
  if (state.instanceId && state.streamUrl) {
    const instanceFrame = state.ui.find((message) => message.name === "instance");
    
    if (!instanceFrame) {
      ui.push({
        name: "instance",
        props: {
          instanceId: state.instanceId,
          streamUrl: state.streamUrl,
        },
      });
    }
  }

  // Create tool call UI components
  if (toolCalls?.length) {
    toolCalls.map((tc) => {
      ui.push({
        name: "computer-use-tool-call",
        props: {
          toolCallId: tc.id,
          action: tc.action,
        },
      });
    });
  }

  return {};
}
```

**After Node Action**:
```typescript
async function afterNode(
  state: GraphState,
  config: LangGraphRunnableConfig,
): Promise<CUAUpdate> {
  const ui = typedUi<typeof ComponentMap>(config);
  const lastMessage = state.messages[state.messages.length - 1];
  
  if (isComputerCallToolMessage(lastMessage)) {
    ui.push({
      name: "computer-use-tool-output",
      props: {
        toolCallId: lastMessage.tool_call_id,
        screenshot: lastMessage.content as string,
      },
    });
  }

  return {};
}
```

## UI Component Integration

### 1. Component Mapping System

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/index.tsx`

**Component Map**:
```typescript
import { ComputerUseToolCall } from "./computer-use-tool-call";
import { ComputerUseToolOutput } from "./computer-use-tool-output";
import { RenderVMButton } from "./render-vm-button";
import { InstanceFrame } from "./instance";

const ComponentMap = {
  "computer-use-tool-output": ComputerUseToolOutput,
  "computer-use-tool-call": ComputerUseToolCall,
  "render-vm-button": RenderVMButton,
  instance: InstanceFrame,
} as const;

export default ComponentMap;
```

**Type Safety**:
```typescript
const ui = typedUi<typeof ComponentMap>(config);
```

This ensures full TypeScript type safety when creating UI components from the agent.

### 2. Dynamic UI Generation

**UI Push Pattern**:
```typescript
ui.push({
  name: "computer-use-tool-call",
  props: {
    toolCallId: tc.id,
    action: tc.action,
  },
}, {
  message: lastMessage,
});
```

**UI Update Pattern**:
```typescript
const existingComponent = state.ui.find(
  (message) => message.name === "instance"
);

if (existingComponent) {
  // Update existing component
  ui.delete(existingComponent.id);
  ui.push({
    name: "instance",
    props: {
      instanceId: state.instanceId,
      streamUrl: state.streamUrl,
    },
  });
}
```

**UI Deletion Pattern**:
```typescript
const componentToRemove = state.ui.find(
  (message) => message.name === "render-vm-button"
);

if (componentToRemove) {
  ui.delete(componentToRemove.id);
}
```

### 3. UI Message Reducer

**Reducer Implementation**:
```typescript
import { uiMessageReducer } from "@langchain/langgraph-sdk/react-ui";

const GraphAnnotation = Annotation.Root({
  ui: Annotation<
    UIMessage[],
    UIMessage | RemoveUIMessage | (UIMessage | RemoveUIMessage)[]
  >({ 
    default: () => [], 
    reducer: uiMessageReducer 
  }),
});
```

**Message Types**:
- **UIMessage**: Add new UI component
- **RemoveUIMessage**: Remove existing UI component
- **Array Support**: Batch operations for multiple components

## Client-Side Integration

### 1. LangGraph SDK Configuration

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/providers/Stream.tsx`

**Stream Setup**:
```typescript
const useTypedStream = useStream<
  StateType,
  {
    UpdateType: {
      messages?: Message[] | Message | string;
      ui?: (UIMessage | RemoveUIMessage)[] | UIMessage | RemoveUIMessage;
      streamUrl?: string;
      instanceId?: string;
      environment?: string;
    };
    CustomEventType: UIMessage | RemoveUIMessage;
  }
>;
```

**Stream Configuration**:
```typescript
const streamValue = useTypedStream({
  apiUrl,
  assistantId: "agent",
  threadId: threadId ?? null,
  onCustomEvent: (event, options) => {
    options.mutate((prev) => {
      const ui = uiMessageReducer(prev.ui ?? [], event);
      return { ...prev, ui };
    });
  },
  onThreadId: (id) => {
    setThreadId(id);
    // Additional thread management logic
  },
});
```

### 2. External Component Loading

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/components/thread/index.tsx`

**Component Loading**:
```typescript
import {
  experimental_loadShare,
  LoadExternalComponent,
} from "@langchain/langgraph-sdk/react-ui";

// Load shared dependencies
experimental_loadShare("nuqs", nuqs);
experimental_loadShare("nuqs/adapters/next/app", nuqsAdapters);
experimental_loadShare("@/components/ui/sonner", Toaster);
experimental_loadShare("sonner", sonner);
```

**Component Rendering**:
```typescript
const customInstanceViewComponent = stream.values.ui?.find(
  (ui) => ui.name === "instance"
);

{customInstanceViewComponent && (
  <LoadExternalComponent
    key={customInstanceViewComponent.id}
    stream={stream}
    message={customInstanceViewComponent}
    meta={{ ui: customInstanceViewComponent }}
  />
)}
```

### 3. API Passthrough

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/[..._path]/route.ts`

**Passthrough Implementation**:
```typescript
import { initApiPassthrough } from "langgraph-nextjs-api-passthrough";

export const { GET, POST, PUT, PATCH, DELETE, OPTIONS, runtime } =
  initApiPassthrough({
    apiUrl: process.env.LANGGRAPH_API_URL,
    apiKey: process.env.LANGCHAIN_API_KEY,
    runtime: "edge",
  });
```

**Benefits**:
- **Seamless Integration**: Direct LangGraph API access
- **Authentication**: Automatic API key handling
- **Edge Runtime**: Optimized for serverless deployment
- **CORS Management**: Proper cross-origin handling

## Advanced LangGraph Features

### 1. Custom Screenshot Handling

**Screenshot Upload Function**:
```typescript
async function uploadScreenshot(screenshot: string): Promise<string> {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseApiKey = process.env.SUPABASE_API_KEY;
  
  if (!supabaseUrl || !supabaseApiKey) {
    throw new Error("Missing Supabase credentials");
  }

  const client = createClient(supabaseUrl, supabaseApiKey);
  const fileName = `${uuidv4()}.png`;
  const blob = convertBase64ToBlob(screenshot);

  const { error } = await client.storage
    .from("cua-screenshots")
    .upload(fileName, blob, {
      contentType: "image/png",
      duplex: "half",
      upsert: false,
    });

  if (error) {
    throw new Error(`Failed to upload screenshot: ${error.message}`);
  }

  const { data } = await client.storage
    .from("cua-screenshots")
    .createSignedUrl(fileName, 60 * 60 * 24 * 90); // 90 days

  return data.signedUrl;
}
```

**Base64 Conversion**:
```typescript
function convertBase64ToBlob(screenshot: string): Blob | null {
  let base64Data = screenshot;
  if (screenshot.startsWith("data:image/png;base64,")) {
    base64Data = screenshot.slice("data:image/png;base64,".length);
  }

  try {
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: "image/png" });
  } catch (error) {
    console.error("Failed to convert base64 to Blob:", error);
    return null;
  }
}
```

### 2. Tool Output Processing

**Tool Call Detection**:
```typescript
import { 
  getToolOutputs, 
  isComputerCallToolMessage 
} from "@langchain/langgraph-cua";

const toolCalls = getToolOutputs(lastMessage);
const isComputerCall = isComputerCallToolMessage(lastMessage);
```

**Tool Output Handling**:
```typescript
if (isComputerCallToolMessage(lastMessage)) {
  ui.push({
    name: "computer-use-tool-output",
    props: {
      toolCallId: lastMessage.tool_call_id,
      screenshot: lastMessage.content as string,
    },
  }, {
    message: lastMessage,
  });
}
```

### 3. Graph Execution Control

**Recursion Limit**:
```typescript
const graph = createCua({
  recursionLimit: 150,
  timeoutHours: 0.1,
  // ... other configuration
});
```

**Stream Configuration**:
```typescript
stream.submit(
  { messages: [newHumanMessage] },
  {
    streamMode: ["values"],
    config: {
      recursion_limit: 150,
      configurable: {
        timeoutHours: 0.1,
      },
    },
  }
);
```

## Development Workflow

### 1. Local Development

**Start Commands**:
```bash
# Terminal 1: Start Next.js development server
pnpm run dev

# Terminal 2: Start LangGraph agent server
pnpm run agent
```

**Environment Variables**:
```bash
# Required for LangGraph
LANGGRAPH_API_URL=http://localhost:8000
LANGCHAIN_API_KEY=your_api_key

# Required for OpenAI
OPENAI_API_KEY=your_openai_api_key

# Required for Scrapybara
SCrapybara_API_KEY=your_scrapybara_api_key

# Required for Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_API_KEY=your_supabase_api_key
```

### 2. Production Deployment

**LangGraph Cloud**:
- **Managed Service**: Fully managed LangGraph hosting
- **Auto-scaling**: Automatic capacity management
- **Monitoring**: Built-in observability
- **Security**: Enterprise-grade security features

**Self-hosted Options**:
- **Docker Deployment**: Containerized LangGraph server
- **Kubernetes**: Scalable container orchestration
- **VM Deployment**: Traditional server deployment

### 3. Monitoring and Debugging

**LangGraph Studio**:
- **Graph Visualization**: Visual graph execution flow
- **State Inspection**: Real-time state monitoring
- **Performance Metrics**: Execution time analysis
- **Error Tracking**: Comprehensive error logging

**Development Tools**:
```typescript
// Debug logging
console.log("Agent state:", state);
console.log("UI messages:", state.ui);
console.log("Tool calls:", toolCalls);
```

## Error Handling and Resilience

### 1. Connection Resilience

**Automatic Reconnection**:
```typescript
const streamValue = useTypedStream({
  // ... configuration
  onError: (error) => {
    console.error("Stream error:", error);
    // Automatic reconnection handled by SDK
  },
});
```

### 2. Error Boundaries

**Component Error Handling**:
```typescript
try {
  const ui = typedUi<typeof ComponentMap>(config);
  // UI operations
} catch (error) {
  console.error("UI generation error:", error);
  // Fallback UI handling
}
```

### 3. Graceful Degradation

**Fallback Mechanisms**:
- **UI Fallbacks**: Default components when generation fails
- **State Recovery**: Automatic state restoration
- **Service Fallbacks**: Alternative service endpoints

## Performance Optimization

### 1. Efficient State Updates

**Selective Updates**:
```typescript
// Only update changed UI components
const existingComponent = state.ui.find(
  (message) => message.name === "instance"
);

if (existingComponent?.props.instanceId !== state.instanceId) {
  ui.delete(existingComponent.id);
  ui.push({ /* new component */ });
}
```

### 2. Streaming Optimization

**Message Batching**:
```typescript
// Batch multiple UI updates
const updates = [
  { name: "computer-use-tool-call", props: { /* ... */ } },
  { name: "computer-use-tool-output", props: { /* ... */ } },
];

updates.forEach(update => ui.push(update));
```

### 3. Resource Management

**Memory Management**:
- **Component Cleanup**: Automatic cleanup of unused components
- **State Pruning**: Removal of old state data
- **Connection Pooling**: Efficient connection reuse

This comprehensive LangGraph integration provides a robust foundation for AI agent orchestration with dynamic UI generation, ensuring optimal performance and user experience.