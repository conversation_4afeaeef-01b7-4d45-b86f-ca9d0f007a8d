# Gen-UI Computer Use - VM Control System Documentation

## VM Control System Overview

The VM Control System is a comprehensive solution for managing virtual machine instances through the Scrapybara API. This system provides complete lifecycle management, real-time monitoring, and seamless integration with the Computer Use Agent for remote desktop control.

## Scrapybara Integration Architecture

### 1. API Integration Layer

**Scrapybara Package**: `scrapybara` v2.4.4

**Core Integration Points**:
- **VM Instance Creation**: Dynamic VM provisioning
- **State Management**: Real-time VM status monitoring
- **Screen Streaming**: Live desktop streaming
- **Control Interface**: Remote desktop interaction
- **Lifecycle Management**: Start, pause, resume, terminate operations

**API Configuration**:
```typescript
// Environment configuration
const SCRAPYBARA_API_KEY = process.env.SCrapybara_API_KEY;

// API client initialization
const scrapybaraClient = new ScrapybaraClient({
  apiKey: SCRAPYBARA_API_KEY,
  baseUrl: "https://api.scrapybara.com/v1",
});
```

### 2. VM Instance Management

**Instance State Model**:
```typescript
interface VMInstance {
  instanceId: string;
  streamUrl: string;
  status: "unknown" | "running" | "paused" | "terminated";
  environment: string;
  createdAt: Date;
  lastActivity: Date;
  resources: {
    cpu: number;
    memory: number;
    disk: number;
  };
}
```

**State Transitions**:
```mermaid
stateDiagram-v2
    [*] --> Unknown
    Unknown --> Running: VM Created
    Running --> Paused: User Pause
    Running --> Terminated: User Stop
    Paused --> Running: User Resume
    Paused --> Terminated: User Stop
    Terminated --> [*]: VM Cleanup
```

## VM Control Components

### 1. Instance Frame Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/instance/index.tsx`

**Purpose**: Main VM display and control interface

**Key Features**:
- **Iframe Integration**: Direct VM interaction through iframe
- **Status Display**: Real-time VM status visualization
- **Control Buttons**: Pause, resume, terminate operations
- **Screenshot Fallback**: Static image display when VM is paused
- **Loading States**: Progress indicators during state transitions

**Component Implementation**:
```typescript
export function InstanceFrame({ streamUrl, instanceId }: InstanceFrameProps) {
  const {
    handleStop,
    handlePause,
    handleResume,
    handleExpand,
    isStopping,
    isStopped,
    status,
    screenshot,
    isLoading,
  } = useInstanceActions({ instanceId });

  const [isShowingInstanceFrame, setIsShowingInstanceFrame] = useQueryState(
    "isShowingInstanceFrame",
    parseAsBoolean
  );

  // Status checking effect
  useEffect(() => {
    if (!instanceId || !isShowingInstanceFrame) return;

    const checkStatus = async () => {
      try {
        const response = await fetch(`/api/instance/status`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ instanceId }),
        });
        const data = await response.json();
        setStatus(data.status);
      } catch (error) {
        console.error("Failed to check instance status:", error);
      }
    };

    checkStatus();
  }, [instanceId, isShowingInstanceFrame]);

  // Render based on status
  if (isLoading) {
    return <LoadingView />;
  }

  if (status === "terminated" && screenshot) {
    return <TerminatedView screenshot={screenshot} />;
  }

  if (status === "paused" && screenshot) {
    return <PausedView screenshot={screenshot} />;
  }

  return (
    <InstanceView>
      <iframe
        src={streamUrl}
        className="w-full h-full min-h-[400px] md:min-h-[632px]"
        title="Instance Frame"
        allow="clipboard-write"
      />
    </InstanceView>
  );
}
```

### 2. Instance Actions Hook

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/instance/useInstanceActions.tsx`

**Purpose**: VM control actions and state management

**Key Features**:
- **Action Handlers**: Stop, pause, resume, expand operations
- **State Management**: VM status tracking
- **Error Handling**: Robust error recovery
- **Performance**: Debounced actions and caching

**Hook Implementation**:
```typescript
export function useInstanceActions({ instanceId }: { instanceId: string }) {
  const [status, setStatus] = useState<VMStatus>("unknown");
  const [isStopping, setIsStopping] = useState(false);
  const [isStopped, setIsStopped] = useState(false);
  const [screenshot, setScreenshot] = useState<string | undefined>();
  const [isLoading, setIsLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleStop = async () => {
    setIsStopping(true);
    try {
      const response = await fetch("/api/instance/stop", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });

      if (!response.ok) {
        throw new Error(`Failed to stop instance: ${response.statusText}`);
      }

      setStatus("terminated");
      setIsStopped(true);
    } catch (error) {
      console.error("Failed to stop instance:", error);
      // Show error notification
    } finally {
      setIsStopping(false);
    }
  };

  const handlePause = async () => {
    try {
      const response = await fetch("/api/instance/pause", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });

      if (!response.ok) {
        throw new Error(`Failed to pause instance: ${response.statusText}`);
      }

      setStatus("paused");
    } catch (error) {
      console.error("Failed to pause instance:", error);
    }
  };

  const handleResume = async () => {
    try {
      const response = await fetch("/api/instance/resume", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });

      if (!response.ok) {
        throw new Error(`Failed to resume instance: ${response.statusText}`);
      }

      setStatus("running");
    } catch (error) {
      console.error("Failed to resume instance:", error);
    }
  };

  const handleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return {
    status,
    setStatus,
    handleStop,
    handlePause,
    handleResume,
    handleExpand,
    isStopping,
    isStopped,
    setIsStopped,
    screenshot,
    setScreenshot,
    isLoading,
    setIsLoading,
    isExpanded,
  };
}
```

### 3. Instance View Component

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/agent/ui/instance/instance-view.tsx`

**Purpose**: Container component for VM display with controls

**Key Features**:
- **Window Manager**: VM window controls
- **Responsive Layout**: Adaptive sizing for different screen sizes
- **Control Bar**: Action buttons and status indicators
- **Fullscreen Support**: Expandable interface option

**Component Structure**:
```typescript
export function InstanceView({
  children,
  handleStop,
  handlePause,
  handleExpand,
  isStopping,
  isStopped,
  allDisabled,
  isExpanded,
}: InstanceViewProps) {
  return (
    <div className={cn(
      "relative rounded-lg border bg-card overflow-hidden",
      isExpanded ? "fixed inset-4 z-50" : "w-full max-w-4xl mx-auto"
    )}>
      <div className="flex items-center justify-between px-3 py-2 border-b bg-muted/50">
        <div className="flex items-center gap-2">
          <div className="flex gap-1">
            <div className="w-3 h-3 rounded-full bg-red-500" />
            <div className="w-3 h-3 rounded-full bg-yellow-500" />
            <div className="w-3 h-3 rounded-full bg-green-500" />
          </div>
          <span className="text-sm font-medium">VM Instance</span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handlePause}
            disabled={allDisabled || isStopping}
          >
            <PauseIcon className="w-4 h-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleStop}
            disabled={allDisabled || isStopping}
          >
            {isStopping ? (
              <LoaderCircle className="w-4 h-4 animate-spin" />
            ) : (
              <StopIcon className="w-4 h-4" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleExpand}
          >
            {isExpanded ? (
              <MinimizeIcon className="w-4 h-4" />
            ) : (
              <MaximizeIcon className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>
      
      <div className="relative">
        {children}
      </div>
    </div>
  );
}
```

## API Endpoints

### 1. Instance Status Endpoint

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/instance/status/route.ts`

**Purpose**: Check VM instance status

**Implementation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId } = await request.json();
    
    if (!instanceId) {
      return NextResponse.json(
        { error: "Instance ID is required" },
        { status: 400 }
      );
    }

    const scrapybaraClient = new ScrapybaraClient({
      apiKey: process.env.SCrapybara_API_KEY,
    });

    const instance = await scrapybaraClient.getInstance(instanceId);
    
    return NextResponse.json({
      status: instance.status,
      instanceId: instance.id,
      streamUrl: instance.streamUrl,
      environment: instance.environment,
      lastActivity: instance.lastActivity,
    });
  } catch (error) {
    console.error("Failed to check instance status:", error);
    return NextResponse.json(
      { error: "Failed to check instance status" },
      { status: 500 }
    );
  }
}
```

### 2. Instance Pause Endpoint

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/instance/pause/route.ts`

**Purpose**: Pause VM instance

**Implementation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId } = await request.json();
    
    const scrapybaraClient = new ScrapybaraClient({
      apiKey: process.env.SCrapybara_API_KEY,
    });

    await scrapybaraClient.pauseInstance(instanceId);
    
    return NextResponse.json({
      success: true,
      message: "Instance paused successfully",
    });
  } catch (error) {
    console.error("Failed to pause instance:", error);
    return NextResponse.json(
      { error: "Failed to pause instance" },
      { status: 500 }
    );
  }
}
```

### 3. Instance Resume Endpoint

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/instance/resume/route.ts`

**Purpose**: Resume paused VM instance

**Implementation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId } = await request.json();
    
    const scrapybaraClient = new ScrapybaraClient({
      apiKey: process.env.SCrapybara_API_KEY,
    });

    await scrapybaraClient.resumeInstance(instanceId);
    
    return NextResponse.json({
      success: true,
      message: "Instance resumed successfully",
    });
  } catch (error) {
    console.error("Failed to resume instance:", error);
    return NextResponse.json(
      { error: "Failed to resume instance" },
      { status: 500 }
    );
  }
}
```

### 4. Instance Stop Endpoint

**Location**: `/Users/<USER>/Projects/own/assistant/gen-ui-computer-use/src/app/api/instance/stop/route.ts`

**Purpose**: Terminate VM instance

**Implementation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId } = await request.json();
    
    const scrapybaraClient = new ScrapybaraClient({
      apiKey: process.env.SCrapybara_API_KEY,
    });

    await scrapybaraClient.terminateInstance(instanceId);
    
    return NextResponse.json({
      success: true,
      message: "Instance terminated successfully",
    });
  } catch (error) {
    console.error("Failed to terminate instance:", error);
    return NextResponse.json(
      { error: "Failed to terminate instance" },
      { status: 500 }
    );
  }
}
```

## VM State Management

### 1. Status Polling System

**Polling Implementation**:
```typescript
useEffect(() => {
  if (!instanceId || !isShowingInstanceFrame) return;

  const checkStatus = async () => {
    try {
      const response = await fetch(`/api/instance/status`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ instanceId }),
      });
      const data = await response.json();
      
      if (["terminated", "paused", "running"].includes(data.status)) {
        setStatus(data.status);
      }

      if (data.status === "terminated") {
        setIsStopped(true);
      }
    } catch (error) {
      console.error("Failed to check instance status:", error);
    }
  };

  // Initial status check
  if (status === "unknown" && !isCheckingStatus.current) {
    isCheckingStatus.current = true;
    checkStatus().finally(() => setIsLoading(false));
  }

  // Periodic status updates
  const interval = setInterval(checkStatus, 5000);
  return () => clearInterval(interval);
}, [instanceId, status, isShowingInstanceFrame]);
```

### 2. Screenshot Management

**Screenshot Retrieval**:
```typescript
const findAndSetScreenshot = () => {
  const lastScreenshot = stream.messages.findLast(
    (m) =>
      m.type === "tool" &&
      m.additional_kwargs?.type === "computer_call_output"
  );
  
  if (lastScreenshot) {
    setScreenshot(lastScreenshot.content as string);
  }
};

// Update screenshot based on VM status
if (["paused", "terminated"].includes(status)) {
  findAndSetScreenshot();
  setIsLoading(false);
}
```

### 3. State Persistence

**URL State Integration**:
```typescript
const [isShowingInstanceFrame, setIsShowingInstanceFrame] = useQueryState(
  "isShowingInstanceFrame",
  parseAsBoolean
);

const [threadId, setThreadId] = useQueryState("threadId");

// Reset state on thread change
useEffect(() => {
  if (!threadId) {
    setIsShowingInstanceFrame(null);
    setStatus("unknown");
    setScreenshot(undefined);
  }
}, [threadId]);
```

## VM Display States

### 1. Loading State

**Loading View**:
```typescript
if (isLoading) {
  return (
    <InstanceView
      handleStop={handleStop}
      handlePause={handlePause}
      handleExpand={handleExpand}
      isStopping={isStopping}
      isStopped={isStopped}
      allDisabled={false}
      isExpanded={isExpanded}
    >
      <div className="w-[630px] h-[420px] lg:w-[830px] lg:h-[620px] flex items-center justify-center p-4 my-auto">
        <LoaderCircle className="w-8 h-8 animate-spin" />
      </div>
    </InstanceView>
  );
}
```

### 2. Terminated State

**Terminated View**:
```typescript
if (status === "terminated" && screenshot) {
  return (
    <InstanceView
      handleStop={handleStop}
      handlePause={handlePause}
      handleExpand={handleExpand}
      isStopping={isStopping}
      isStopped={isStopped}
      allDisabled={true}
      isExpanded={isExpanded}
    >
      <img
        src={screenshot}
        alt="Terminated instance screenshot"
        className="w-full object-contain opacity-70"
      />
      <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/30">
        <div className="bg-card/90 p-6 rounded-lg shadow-lg text-center max-w-xs">
          <h3 className="text-lg font-semibold mb-2">Instance Terminated</h3>
          <p className="text-muted-foreground text-sm mb-4">
            All progress has been lost.
          </p>
          <Button
            onClick={() => {
              setIsShowingInstanceFrame(null);
              setScreenshot(undefined);
              setStatus("unknown");
              setThreadId(null);
            }}
            className="w-full"
          >
            Create New Chat
          </Button>
        </div>
      </div>
    </InstanceView>
  );
}
```

### 3. Paused State

**Paused View**:
```typescript
if (status === "paused" && screenshot) {
  return (
    <InstanceView
      handleStop={handleStop}
      handlePause={handlePause}
      handleExpand={handleExpand}
      isStopping={isStopping}
      isStopped={isStopped}
      allDisabled={true}
      isExpanded={isExpanded}
    >
      <div
        onMouseEnter={() => setIsScreenshotHovered(true)}
        onMouseLeave={() => setIsScreenshotHovered(false)}
      >
        <img
          src={screenshot}
          alt="Paused instance screenshot"
          className="w-full object-contain"
        />
        <div
          className={cn(
            "absolute inset-0 flex items-center justify-center transition-all duration-300 ease-in-out gap-3",
            isScreenshotHovered
              ? "bg-black/40 opacity-100"
              : "bg-black/0 opacity-0"
          )}
        >
          <Button onClick={handleStop} variant="destructive">
            Terminate
          </Button>
          <Button onClick={handleResume}>Resume</Button>
        </div>
      </div>
    </InstanceView>
  );
}
```

### 4. Running State

**Active VM View**:
```typescript
return (
  <InstanceView
    handleStop={handleStop}
    handlePause={handlePause}
    handleExpand={handleExpand}
    isStopping={isStopping}
    isStopped={isStopped}
    allDisabled={false}
    isExpanded={isExpanded}
  >
    {isStopping && <div className="absolute inset-0 bg-black/20 z-10" />}
    <iframe
      src={streamUrl}
      className={cn(
        "w-full h-full",
        isExpanded ? "aspect-[4/3]" : "min-h-[400px] md:min-h-[632px]"
      )}
      title="Instance Frame"
      allow="clipboard-write"
    />
  </InstanceView>
);
```

## Security Considerations

### 1. API Key Security

**Environment Variables**:
```typescript
const SCRAPYBARA_API_KEY = process.env.SCrapybara_API_KEY;

if (!SCRAPYBARA_API_KEY) {
  throw new Error("Scrapybara API key is required");
}
```

### 2. Input Validation

**Request Validation**:
```typescript
export async function POST(request: Request) {
  try {
    const { instanceId } = await request.json();
    
    if (!instanceId || typeof instanceId !== "string") {
      return NextResponse.json(
        { error: "Valid instance ID is required" },
        { status: 400 }
      );
    }

    // Additional validation
    if (!/^[a-zA-Z0-9-_]+$/.test(instanceId)) {
      return NextResponse.json(
        { error: "Invalid instance ID format" },
        { status: 400 }
      );
    }

    // ... rest of implementation
  } catch (error) {
    return NextResponse.json(
      { error: "Invalid request" },
      { status: 400 }
    );
  }
}
```

### 3. Error Handling

**Comprehensive Error Handling**:
```typescript
try {
  const response = await scrapybaraClient.getInstance(instanceId);
  return NextResponse.json(response);
} catch (error) {
  console.error("Scrapybara API error:", error);
  
  if (error.status === 404) {
    return NextResponse.json(
      { error: "Instance not found" },
      { status: 404 }
    );
  }
  
  if (error.status === 403) {
    return NextResponse.json(
      { error: "Insufficient permissions" },
      { status: 403 }
    );
  }
  
  return NextResponse.json(
    { error: "Internal server error" },
    { status: 500 }
  );
}
```

## Performance Optimization

### 1. Efficient Status Polling

**Optimized Polling**:
```typescript
// Adaptive polling interval based on VM status
const getPollingInterval = (status: string) => {
  switch (status) {
    case "running":
      return 5000; // 5 seconds
    case "paused":
      return 10000; // 10 seconds
    case "terminated":
      return 0; // No polling
    default:
      return 2000; // 2 seconds for unknown status
  }
};

// Use optimized polling
useEffect(() => {
  const interval = getPollingInterval(status);
  if (interval === 0) return;

  const timer = setInterval(checkStatus, interval);
  return () => clearInterval(timer);
}, [status]);
```

### 2. Resource Management

**Memory Optimization**:
```typescript
// Cleanup on component unmount
useEffect(() => {
  return () => {
    // Cancel pending requests
    if (abortController.current) {
      abortController.current.abort();
    }
    
    // Clear timers
    clearInterval(statusInterval.current);
    clearTimeout(retryTimeout.current);
  };
}, []);
```

### 3. Caching Strategy

**Response Caching**:
```typescript
const statusCache = new Map<string, { status: string; timestamp: number }>();

const getCachedStatus = (instanceId: string) => {
  const cached = statusCache.get(instanceId);
  if (cached && Date.now() - cached.timestamp < 5000) {
    return cached.status;
  }
  return null;
};

const setCachedStatus = (instanceId: string, status: string) => {
  statusCache.set(instanceId, { status, timestamp: Date.now() });
};
```

This comprehensive VM Control System provides robust virtual machine management with real-time monitoring, secure API integration, and optimal user experience through the Scrapybara platform.