# Agent Inbox - Deployment and Build Processes

## Overview

The Agent Inbox is designed as a Next.js application with flexible deployment options supporting various environments from local development to enterprise production deployments. The build process emphasizes performance, security, and compatibility across different hosting platforms.

## Build Process

### 1. Development Build

**Command**: `yarn dev`

```bash
# Start development server
yarn dev

# Development server features:
# - Hot module replacement (HMR)
# - Fast refresh for React components
# - Real-time error overlay
# - TypeScript compilation on-demand
# - Automatic port detection (default: 3000)
```

**Development Features**:
- **Hot Reload**: Instant updates without page refresh
- **Error Boundaries**: Development error overlay with stack traces
- **Source Maps**: Full source map support for debugging
- **Type Checking**: Real-time TypeScript error reporting
- **CSS Hot Reload**: Instant style updates

**Development Configuration**:
```typescript
// next.config.mjs for development
const nextConfig = {
  experimental: {
    typedRoutes: true,
  },
  compiler: {
    removeConsole: false, // Keep console logs in development
  },
  eslint: {
    dirs: ['src'], // Only lint src directory in development
  },
};
```

### 2. Production Build

**Command**: `yarn build`

```bash
# Generate optimized production build
yarn build

# Build process includes:
# - TypeScript compilation and type checking
# - Code splitting and tree shaking
# - CSS optimization and minification
# - Image optimization
# - Static generation where applicable
# - Bundle analysis and optimization
```

**Build Optimizations**:
- **Code Splitting**: Automatic route-based code splitting
- **Tree Shaking**: Removal of unused code
- **Bundle Optimization**: Webpack optimization for smaller bundles
- **CSS Optimization**: PostCSS processing and minification
- **Image Optimization**: Next.js Image component optimization

**Production Build Configuration**:
```typescript
// next.config.mjs for production
const nextConfig = {
  output: 'standalone', // For containerized deployments
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  compiler: {
    removeConsole: {
      exclude: ['error'], // Keep error logs in production
    },
  },
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@radix-ui/react-icons'],
  },
};
```

### 3. Production Server

**Command**: `yarn start`

```bash
# Start production server (requires build first)
yarn build && yarn start

# Production server features:
# - Optimized static file serving
# - Gzip compression
# - Security headers
# - Performance monitoring
# - Error logging
```

## Deployment Strategies

### 1. Static Site Deployment

**Configuration for Static Export**:
```typescript
// next.config.mjs for static deployment
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  distDir: 'dist',
  images: {
    unoptimized: true, // Required for static export
  },
};
```

**Build Process**:
```bash
# Generate static files
yarn build

# Deploy to static hosting (Netlify, Vercel, S3, etc.)
# Files are generated in the 'dist' directory
```

**Static Deployment Targets**:
- **Netlify**: Zero-config deployment with form handling
- **Vercel**: Optimized Next.js hosting with edge functions
- **AWS S3 + CloudFront**: Scalable static hosting
- **GitHub Pages**: Free hosting for open source projects
- **Firebase Hosting**: Google Cloud static hosting

### 2. Server-Side Deployment

**Node.js Server Deployment**:
```bash
# Build for server deployment
yarn build

# Start production server
yarn start

# Or use PM2 for process management
pm2 start yarn --name "agent-inbox" -- start
```

**Server Requirements**:
- **Node.js**: Version 18.0.0 or higher
- **Memory**: Minimum 512MB RAM
- **Storage**: 1GB for application and dependencies
- **Network**: HTTPS support recommended

### 3. Container Deployment

**Dockerfile Configuration**:
```dockerfile
# Multi-stage Docker build
FROM node:18-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Environment variables for build
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

RUN yarn build

# Production image
FROM node:18-alpine AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

**Docker Compose Configuration**:
```yaml
# docker-compose.yml
version: '3.8'
services:
  agent-inbox:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://api.langgraph.app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - agent-inbox
    restart: unless-stopped
```

### 4. Cloud Platform Deployments

#### Vercel Deployment

**Configuration**:
```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "yarn build",
  "outputDirectory": ".next",
  "installCommand": "yarn install",
  "functions": {
    "src/pages/api/**/*.ts": {
      "runtime": "nodejs18.x"
    }
  },
  "redirects": [
    {
      "source": "/health",
      "destination": "/api/health",
      "permanent": false
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    }
  ]
}
```

**Deployment Commands**:
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy to preview
vercel

# Deploy to production
vercel --prod
```

#### AWS Deployment

**AWS Lambda + API Gateway**:
```bash
# Install Serverless Framework
npm install -g serverless

# Configure serverless.yml
serverless deploy

# Monitor deployment
serverless logs -f nextjsLambda
```

**AWS ECS Deployment**:
```json
// ecs-task-definition.json
{
  "family": "agent-inbox",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "agent-inbox",
      "image": "your-registry/agent-inbox:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/agent-inbox",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

## Environment Configuration

### 1. Environment Variables

**Required Variables**:
```bash
# API Configuration
LANGSMITH_API_KEY=your_langsmith_api_key_here

# Application Configuration
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=production
```

**Optional Variables**:
```bash
# Analytics
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Error Tracking
SENTRY_DSN=your_sentry_dsn
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project

# Feature Flags
NEXT_PUBLIC_ENABLE_EXPERIMENTAL_FEATURES=false
NEXT_PUBLIC_ENABLE_DEBUG_MODE=false

# Performance
NEXT_PUBLIC_API_TIMEOUT=30000
NEXT_PUBLIC_MAX_RETRIES=3
```

### 2. Environment-Specific Configurations

**Development Environment**:
```bash
# .env.local (development)
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_ENABLE_DEBUG_MODE=true
NEXT_PUBLIC_ENABLE_HOT_RELOAD=true
```

**Staging Environment**:
```bash
# .env.staging
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://staging-api.langgraph.app
NEXT_PUBLIC_ENVIRONMENT=staging
NEXT_PUBLIC_ENABLE_DEBUG_MODE=true
```

**Production Environment**:
```bash
# .env.production
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.langgraph.app
NEXT_PUBLIC_ENVIRONMENT=production
NEXT_PUBLIC_ENABLE_DEBUG_MODE=false
NEXT_TELEMETRY_DISABLED=1
```

## Performance Optimization

### 1. Build Optimizations

**Bundle Analysis**:
```bash
# Analyze bundle size
npm install -g @next/bundle-analyzer
ANALYZE=true yarn build

# Alternative: webpack-bundle-analyzer
npx webpack-bundle-analyzer .next/static/chunks/*.js
```

**Image Optimization**:
```typescript
// next.config.mjs image optimization
const nextConfig = {
  images: {
    domains: ['example.com', 'cdn.example.com'],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 31536000,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
};
```

### 2. Caching Strategies

**HTTP Headers**:
```typescript
// next.config.mjs caching headers
const nextConfig = {
  async headers() {
    return [
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/((?!api/).*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, must-revalidate',
          },
        ],
      },
    ];
  },
};
```

**Service Worker (Future Enhancement)**:
```javascript
// sw.js - Service worker for offline support
const CACHE_NAME = 'agent-inbox-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});
```

## Monitoring and Logging

### 1. Application Monitoring

**Health Check Endpoint**:
```typescript
// pages/api/health.ts
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.NEXT_PUBLIC_APP_VERSION,
    environment: process.env.NODE_ENV,
    uptime: process.uptime(),
  };
  
  res.status(200).json(healthData);
}
```

**Performance Monitoring**:
```typescript
// lib/monitoring.ts
export const trackPerformance = (metricName: string, value: number) => {
  if (typeof window !== 'undefined' && 'gtag' in window) {
    (window as any).gtag('event', 'timing_complete', {
      name: metricName,
      value: Math.round(value),
    });
  }
};

// Usage in components
React.useEffect(() => {
  const startTime = performance.now();
  
  return () => {
    const duration = performance.now() - startTime;
    trackPerformance('component_render_time', duration);
  };
}, []);
```

### 2. Error Tracking

**Error Boundary with Sentry**:
```typescript
// components/ErrorBoundary.tsx
import * as Sentry from '@sentry/nextjs';

class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    Sentry.captureException(error, {
      contexts: {
        react: {
          componentStack: errorInfo.componentStack,
        },
      },
    });
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <button onClick={() => window.location.reload()}>
            Reload page
          </button>
        </div>
      );
    }
    
    return this.props.children;
  }
}
```

## Security Considerations

### 1. Build Security

**Dependency Scanning**:
```bash
# Audit dependencies for vulnerabilities
yarn audit

# Fix vulnerabilities automatically
yarn audit fix

# Check for outdated packages
yarn outdated
```

**Content Security Policy**:
```typescript
// next.config.mjs CSP configuration
const ContentSecurityPolicy = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.com;
  style-src 'self' 'unsafe-inline';
  img-src 'self' blob: data:;
  font-src 'self';
  object-src 'none';
  base-uri 'self';
  form-action 'self';
  frame-ancestors 'none';
  block-all-mixed-content;
  upgrade-insecure-requests;
`;

const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: ContentSecurityPolicy.replace(/\n/g, ''),
          },
        ],
      },
    ];
  },
};
```

### 2. Deployment Security

**Environment Variable Security**:
```bash
# Use encrypted environment variables
# Avoid committing secrets to version control
# Use platform-specific secret management

# Example: AWS Systems Manager Parameter Store
aws ssm put-parameter \
  --name "/agent-inbox/prod/langsmith-api-key" \
  --value "your-secret-key" \
  --type "SecureString"
```

**HTTPS Enforcement**:
```typescript
// middleware.ts - Force HTTPS in production
import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  if (
    process.env.NODE_ENV === 'production' &&
    request.headers.get('x-forwarded-proto') !== 'https'
  ) {
    return NextResponse.redirect(
      `https://${request.headers.get('host')}${request.nextUrl.pathname}`,
      301
    );
  }
}
```

This comprehensive deployment strategy ensures the Agent Inbox can be reliably deployed across various environments while maintaining performance, security, and scalability requirements.