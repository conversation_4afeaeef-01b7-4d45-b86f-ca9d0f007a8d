# Agent Inbox - Data Models and Schemas

## Overview

The Agent Inbox uses a comprehensive set of TypeScript interfaces and types to ensure type safety and consistency across the application. The data models are designed to support flexible thread management, interrupt handling, and multi-deployment scenarios.

## Core Data Models

### 1. Human Interrupt Schema

**Location**: `/Users/<USER>/Projects/own/assistant/agent-inbox/src/components/agent-inbox/types.ts`

#### HumanInterruptConfig
```typescript
export interface HumanInterruptConfig {
  allow_ignore: boolean;    // Whether the user can ignore the interrupt
  allow_respond: boolean;   // Whether the user can respond to the interrupt
  allow_edit: boolean;      // Whether the user can edit the interrupt
  allow_accept: boolean;    // Whether the user can accept the interrupt
}
```

**Purpose**: Defines the allowable actions for each interrupt, providing fine-grained control over user interactions.

**Usage Patterns**:
- **Restrictive**: Only allow specific actions (e.g., only accept/reject)
- **Permissive**: Allow all actions for maximum flexibility
- **Contextual**: Different permissions based on interrupt type

#### ActionRequest
```typescript
export interface ActionRequest {
  action: string;                    // Name or title of the action
  args: Record<string, any>;         // Arguments for the action
}
```

**Purpose**: Represents a specific action that an agent wants to perform, including all necessary parameters.

**Examples**:
- Tool calls with arguments
- API requests with parameters
- Function calls with inputs

#### HumanInterrupt
```typescript
export interface HumanInterrupt {
  action_request: ActionRequest;     // The action being requested
  config: HumanInterruptConfig;      // Configuration for allowed responses
  description?: string;              // Optional markdown description
}
```

**Purpose**: Complete interrupt definition combining action details, configuration, and context.

**Features**:
- **Rich Descriptions**: Markdown support for complex explanations
- **Action Context**: Full action details for informed decisions
- **Configuration Flexibility**: Customizable interaction patterns

### 2. Human Response Schema

#### HumanResponse
```typescript
export type HumanResponse = {
  type: "accept" | "ignore" | "response" | "edit";
  args: null | string | ActionRequest;
};
```

**Purpose**: Standardized response format for human interactions with agents.

**Response Types**:
- **accept**: Approve the action with original parameters
- **ignore**: Skip the action and continue
- **response**: Provide textual response
- **edit**: Modify action parameters

#### HumanResponseWithEdits
```typescript
export type HumanResponseWithEdits = HumanResponse &
  (
    | { acceptAllowed?: false; editsMade?: never }
    | { acceptAllowed?: true; editsMade?: boolean }
  );
```

**Purpose**: Extended response type that tracks editing state and acceptance capabilities.

**Features**:
- **Edit Tracking**: Boolean flag for modification detection
- **Accept Optimization**: Automatic accept when no edits made
- **Type Safety**: Conditional types for different scenarios

### 3. Thread Management Schema

#### ThreadData
```typescript
/**
 * Union type for all thread data types.
 * Using discriminated union pattern for better type safety.
 */
export type ThreadData<T extends Record<string, any> = Record<string, any>> =
  | GenericThreadData<T>
  | InterruptedThreadData<T>
  | HumanResponseNeededThreadData<T>;

/**
 * Base thread data interface with common properties.
 */
interface BaseThreadData<T extends Record<string, any> = Record<string, any>> {
  thread: Thread<T>;
  invalidSchema?: boolean;
}

/**
 * Thread data for non-interrupted states.
 */
export interface GenericThreadData<T extends Record<string, any> = Record<string, any>>
  extends BaseThreadData<T> {
  status: "idle" | "busy" | "error";
  interrupts?: undefined;
}

/**
 * Thread data for interrupted state.
 */
export interface InterruptedThreadData<T extends Record<string, any> = Record<string, any>>
  extends BaseThreadData<T> {
  status: "interrupted";
  interrupts?: HumanInterrupt[];
}

/**
 * Thread data for human_response_needed state.
 */
export interface HumanResponseNeededThreadData<T extends Record<string, any> = Record<string, any>>
  extends BaseThreadData<T> {
  status: "human_response_needed";
  interrupts?: HumanInterrupt[];
}
```

**Purpose**: Comprehensive thread representation with discriminated unions for different states.

**Features**:
- **Generic Type Support**: Flexible thread value types
- **Discriminated Unions**: Type-safe status handling
- **Interrupt Management**: Optional interrupt array for interrupted threads
- **Schema Validation**: Invalid schema tracking
- **Enhanced Status**: Support for human_response_needed state

#### Enhanced Thread Status Types
```typescript
/**
 * Extended thread status type that includes our custom statuses.
 */
export type EnhancedThreadStatus = ThreadStatus | "human_response_needed";

/**
 * Thread status with special "all" option for filtering.
 */
export type ThreadStatusWithAll = EnhancedThreadStatus | "all";
```

**Purpose**: Extended thread status types including custom statuses and filtering options.

**Usage**: UI filter controls that allow viewing all threads regardless of status, with support for enhanced statuses.

### 4. Agent Inbox Configuration

#### AgentInbox
```typescript
export interface AgentInbox {
  id: string;              // Unique identifier for the inbox
  graphId: string;         // The ID of the graph
  deploymentUrl: string;   // URL of the deployment
  name?: string;           // Optional name for the inbox
  selected: boolean;       // Whether the inbox is selected
  tenantId?: string;       // The tenant ID for the deployment (only for deployed graphs)
  createdAt: string;       // Creation timestamp
}
```

**Purpose**: Configuration for individual agent deployments and connections.

**Features**:
- **Multi-Deployment Support**: Separate configurations per deployment
- **Optional Naming**: User-friendly labels for deployments
- **Selection State**: Active deployment tracking
- **Tenant Support**: Multi-tenant deployment support
- **Timestamp Tracking**: Creation time tracking

### 5. Email Data Model (Example Domain)

#### Email
```typescript
export type Email = {
  id: string;
  thread_id: string;
  from_email: string;
  to_email: string;
  subject: string;
  page_content: string;
  send_time: string | undefined;
  read?: boolean;
  status?: "in-queue" | "processing" | "hitl" | "done";
};
```

**Purpose**: Example domain model for email processing workflows.

**Features**:
- **Workflow Status**: Track email processing stages
- **Metadata**: Complete email metadata
- **Optional Fields**: Flexible schema for different use cases

#### ThreadValues
```typescript
export interface ThreadValues {
  email: Email;
  messages: BaseMessage[];
  triage: {
    logic: string;
    response: string;
  };
}
```

**Purpose**: Example thread state structure for email processing agents.

**Components**:
- **Email Data**: Core email information
- **Message History**: LangChain message array
- **Triage Information**: Decision logic and responses

## Type System Architecture

### 1. Generic Type Parameters

**Pattern**: Flexible type parameters for different domain models

```typescript
// Generic component support
export function AgentInbox<
  ThreadValues extends Record<string, any> = Record<string, any>,
>() {
  // Implementation uses ThreadValues type throughout
}

// Generic hook support
export function useThreadsContext<
  T extends Record<string, any> = Record<string, any>,
>() {
  // Type-safe context access
}
```

**Benefits**:
- **Domain Flexibility**: Support for different agent domains
- **Type Safety**: Compile-time type checking
- **Code Reusability**: Generic components for various use cases

### 2. Discriminated Unions

**Pattern**: Type-safe state management with discriminated unions

```typescript
// Thread status discrimination
export type ThreadData<ThreadValues> = {
  thread: Thread<ThreadValues>;
} & (
  | {
      status: "interrupted";
      interrupts: HumanInterrupt[] | undefined;
    }
  | {
      status: "idle" | "busy" | "error";
      interrupts?: never;
    }
);
```

**Benefits**:
- **Type Safety**: Compile-time guarantees about data structure
- **Exhaustive Checking**: TypeScript ensures all cases are handled
- **Runtime Safety**: Prevents invalid state combinations

### 3. Utility Types

#### SubmitType
```typescript
export type SubmitType = "accept" | "response" | "edit";
```

**Purpose**: Restricted type for submission actions in UI components.

#### Constants Integration
```typescript
// From constants.ts
export const VIEW_STATE_THREAD_QUERY_PARAM = "view_state_thread_id";
export const AGENT_INBOXES_LOCAL_STORAGE_KEY = "inbox:agent_inboxes";
export const LANGCHAIN_API_KEY_LOCAL_STORAGE_KEY = "inbox:langchain_api_key";
export const OFFSET_PARAM = "offset";
export const LIMIT_PARAM = "limit";
export const INBOX_PARAM = "inbox";
export const AGENT_INBOX_PARAM = "agent_inbox";
export const NO_INBOXES_FOUND_PARAM = "no_inboxes_found";
export const AGENT_INBOX_GITHUB_README_URL = "https://github.com/langchain-ai/agent-inbox/blob/main/README.md";
export const IMPROPER_SCHEMA = "improper_schema";
export const STUDIO_NOT_WORKING_TROUBLESHOOTING_URL = `${AGENT_INBOX_GITHUB_README_URL}#the-open-in-studio-button-doesnt-work-for-my-deployed-graphs`;
```

**Purpose**: Type-safe constant definitions for query parameters, storage keys, and configuration URLs.

## Data Validation and Processing

### 1. Input Validation

**Pattern**: Runtime validation with TypeScript integration

```typescript
// UUID validation for agent inbox IDs
import { validate } from "uuid";

// Validation in agent inbox loading
if (!agentInboxSearchParam || !validate(agentInboxSearchParam)) {
  const selectedInbox = parsedAgentInboxes.find((i) => i.selected);
  // Handle invalid or missing ID
}
```

### 2. Data Transformation

**Pattern**: Type-safe data transformation functions

```typescript
// Thread processing functions
export const processInterruptedThread = (
  thread: Thread<ThreadValues>
): ThreadData<ThreadValues> | undefined => {
  const interrupts = getInterruptFromThread(thread);
  if (!interrupts?.length) return undefined;
  
  return {
    thread,
    status: "interrupted" as const,
    interrupts,
  };
};
```

### 3. Default Value Creation

**Pattern**: Factory functions for creating default data structures

```typescript
// Default human response creation
export const createDefaultHumanResponse = (
  interrupts: HumanInterrupt[],
  initialValues: React.MutableRefObject<Record<string, string>>
): {
  responses: HumanResponseWithEdits[];
  defaultSubmitType: SubmitType | undefined;
  hasAccept: boolean;
} => {
  // Implementation creates properly typed default responses
};
```

## Data Flow Patterns

### 1. Client-Server Data Flow

```mermaid
graph TB
    A[LangGraph API] --> B[Thread<ThreadValues>]
    B --> C[ThreadData<ThreadValues>]
    C --> D[UI Components]
    D --> E[HumanResponse]
    E --> F[LangGraph API]
```

### 2. State Management Flow

```mermaid
graph LR
    A[Local Storage] --> B[AgentInbox[]]
    B --> C[ThreadsProvider]
    C --> D[useThreadsContext]
    D --> E[Components]
    E --> F[State Updates]
    F --> A
```

### 3. Interrupt Processing Flow

```mermaid
graph TB
    A[HumanInterrupt] --> B[UI Form]
    B --> C[User Input]
    C --> D[HumanResponseWithEdits]
    D --> E[Validation]
    E --> F[HumanResponse]
    F --> G[API Call]
```

## Schema Evolution and Compatibility

### 1. Backward Compatibility

**Pattern**: Optional fields and graceful degradation

```typescript
// Optional fields for backward compatibility
export interface HumanInterrupt {
  action_request: ActionRequest;
  config: HumanInterruptConfig;
  description?: string;  // Optional for backward compatibility
}
```

### 2. Version Handling

**Pattern**: Legacy processing with fallbacks

```typescript
// Legacy thread processing
const threadsWithoutInterrupts = interruptedThreads.filter(
  (t) => !getInterruptFromThread(t)?.length
);

if (threadsWithoutInterrupts.length > 0) {
  // Legacy processing fallback
  const states = await bulkGetThreadStates(
    threadsWithoutInterrupts.map((t) => t.thread_id)
  );
  // Process with legacy format
}
```

### 3. Migration Strategies

**Pattern**: Automatic data migration and validation

```typescript
// Agent inbox migration
parsedAgentInboxes = parsedAgentInboxes.map((i) => {
  return {
    ...i,
    id: i.id || uuidv4(),  // Add missing IDs
  };
});
```

## Performance Considerations

### 1. Type Optimization

**Pattern**: Generic constraints for performance

```typescript
// Constrained generic types
export function AgentInbox<
  ThreadValues extends Record<string, any> = Record<string, any>,
>() {
  // TypeScript can optimize based on constraints
}
```

### 2. Memory Management

**Pattern**: Efficient data structures and cleanup

```typescript
// Memoized data processing
const threadDataToRender = React.useMemo(
  () =>
    threadData.filter((t) => {
      if (selectedInbox === "all") return true;
      return t.status === selectedInbox;
    }),
  [selectedInbox, threadData]
);
```

### 3. Serialization Optimization

**Pattern**: Efficient JSON serialization

```typescript
// Optimized local storage serialization
setItem(
  AGENT_INBOXES_LOCAL_STORAGE_KEY,
  JSON.stringify(parsedAgentInboxes)
);
```

This comprehensive data model architecture ensures type safety, flexibility, and performance while supporting complex human-AI interaction patterns.