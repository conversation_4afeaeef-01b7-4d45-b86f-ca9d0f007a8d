# Agent Inbox - Configuration and Setup

## Overview

The Agent Inbox uses a multi-layered configuration system that combines build-time settings, runtime configuration, and user preferences. The system is designed to support flexible deployment scenarios while maintaining security and ease of use.

## Build Configuration

### 1. Next.js Configuration

**File**: `/Users/<USER>/Projects/own/assistant/agent-inbox/next.config.mjs`

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {};

export default nextConfig;
```

**Features**:
- **Minimal Configuration**: Default Next.js settings for maximum compatibility
- **ES Module Format**: Modern module syntax
- **Extensible**: Ready for additional configuration as needed

**Potential Extensions**:
```javascript
const nextConfig = {
  experimental: {
    serverComponents: true,
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  rewrites: async () => {
    return [
      {
        source: '/api/:path*',
        destination: 'https://api.example.com/:path*',
      },
    ];
  },
};
```

### 2. TypeScript Configuration

**File**: `/Users/<USER>/Projects/own/assistant/agent-inbox/tsconfig.json`

```json
{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

**Key Features**:
- **Strict Mode**: Maximum type safety with strict TypeScript rules
- **Path Mapping**: `@/*` alias for clean imports
- **Next.js Integration**: Built-in Next.js TypeScript plugin
- **Modern Module Resolution**: Bundler-style module resolution

### 3. Tailwind CSS Configuration

**File**: `/Users/<USER>/Projects/own/assistant/agent-inbox/tailwind.config.ts`

```typescript
import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("tailwind-scrollbar"),
    require("tailwind-scrollbar-hide"),
  ],
};

export default config;
```

**Key Features**:
- **CSS Variables**: Dynamic theming support
- **Dark Mode**: Class-based dark mode support
- **Custom Animations**: Accordion and other UI animations
- **Responsive Design**: Container queries and breakpoints
- **Plugin Integration**: Animations, scrollbars, and utility plugins

### 4. PostCSS Configuration

**File**: `/Users/<USER>/Projects/own/assistant/agent-inbox/postcss.config.mjs`

```javascript
/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};

export default config;
```

**Features**:
- **Tailwind Processing**: Tailwind CSS compilation
- **Autoprefixer**: Automatic vendor prefix addition
- **Modern Syntax**: ES module configuration

## Component Configuration

### 1. Shadcn/UI Configuration

**File**: `/Users/<USER>/Projects/own/assistant/agent-inbox/components.json`

```json
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "src/app/globals.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib",
    "hooks": "@/hooks"
  }
}
```

**Configuration Options**:
- **Style**: "new-york" design system variant
- **RSC Support**: React Server Components compatibility
- **TypeScript**: Full TypeScript integration
- **CSS Variables**: Dynamic theming system
- **Path Aliases**: Organized import structure

### 2. UI Component Constants

**File**: `/Users/<USER>/Projects/own/assistant/agent-inbox/src/components/agent-inbox/constants.ts`

```typescript
// Query parameter constants
export const INBOX_PARAM = "inbox";
export const VIEW_STATE_THREAD_QUERY_PARAM = "thread";
export const LIMIT_PARAM = "limit";
export const OFFSET_PARAM = "offset";
export const AGENT_INBOX_PARAM = "agent_inbox";
export const NO_INBOXES_FOUND_PARAM = "no_inboxes_found";

// Local storage keys
export const AGENT_INBOXES_LOCAL_STORAGE_KEY = "agent-inboxes";
export const LANGCHAIN_API_KEY_LOCAL_STORAGE_KEY = "langchain-api-key";

// Default values
export const DEFAULT_LIMIT = 10;
export const DEFAULT_OFFSET = 0;
export const MAX_THREADS_PER_REQUEST = 100;
export const CHUNK_SIZE = 25;
```

**Features**:
- **Centralized Constants**: Single source of truth for configuration values
- **Type Safety**: Exported constants for compile-time checking
- **Maintainability**: Easy to modify application-wide settings

## Runtime Configuration

### 1. Agent Inbox Configuration Schema

```typescript
// Agent inbox configuration interface
export interface AgentInbox {
  id: string;              // Unique identifier (UUID)
  graphId: string;         // LangGraph graph/assistant ID
  deploymentUrl: string;   // API endpoint URL
  name?: string;           // Optional human-readable name
  selected: boolean;       // Whether this inbox is active
}

// Configuration validation
const validateAgentInbox = (config: Partial<AgentInbox>): AgentInbox => {
  if (!config.graphId) {
    throw new Error("Graph ID is required");
  }
  
  if (!config.deploymentUrl) {
    throw new Error("Deployment URL is required");
  }
  
  // Validate URL format
  try {
    new URL(config.deploymentUrl);
  } catch {
    throw new Error("Invalid deployment URL format");
  }
  
  return {
    id: config.id || uuidv4(),
    graphId: config.graphId,
    deploymentUrl: config.deploymentUrl,
    name: config.name,
    selected: config.selected || false,
  };
};
```

### 2. Environment Detection

```typescript
// Environment-specific configuration
const getEnvironmentConfig = (deploymentUrl: string) => {
  const isLocal = deploymentUrl.includes("localhost") || deploymentUrl.includes("127.0.0.1");
  const isProduction = deploymentUrl.includes("us.langgraph.app");
  const isStaging = deploymentUrl.includes("staging") || deploymentUrl.includes("dev");
  
  return {
    isLocal,
    isProduction,
    isStaging,
    requiresApiKey: isProduction,
    maxRetries: isLocal ? 1 : 3,
    timeout: isLocal ? 5000 : 30000,
  };
};
```

### 3. Client Configuration

```typescript
// LangGraph client configuration
export const createClient = ({
  deploymentUrl,
  langchainApiKey,
}: {
  deploymentUrl: string;
  langchainApiKey: string | undefined;
}) => {
  const config = getEnvironmentConfig(deploymentUrl);
  
  return new Client({
    apiUrl: deploymentUrl,
    defaultHeaders: {
      ...(langchainApiKey && { "x-api-key": langchainApiKey }),
      "User-Agent": "Agent-Inbox/1.0",
    },
    timeout: config.timeout,
    retries: config.maxRetries,
  });
};
```

## User Configuration Management

### 1. Local Storage Schema

```typescript
// Local storage configuration structure
interface LocalStorageConfig {
  "agent-inboxes": AgentInbox[];
  "langchain-api-key": string;
  "user-preferences": UserPreferences;
  "ui-settings": UISettings;
}

interface UserPreferences {
  theme: "light" | "dark" | "system";
  defaultInboxView: ThreadStatusWithAll;
  autoRefreshInterval: number;
  notificationsEnabled: boolean;
}

interface UISettings {
  sidebarCollapsed: boolean;
  threadsPerPage: number;
  showAdvancedOptions: boolean;
  compactMode: boolean;
}
```

### 2. Configuration Validation and Migration

```typescript
// Configuration migration system
const CURRENT_CONFIG_VERSION = "1.0.0";

const migrateConfiguration = (storedConfig: any): LocalStorageConfig => {
  const version = storedConfig.version || "0.0.0";
  
  if (version < "1.0.0") {
    // Migrate from legacy format
    storedConfig = migrateLegacyConfig(storedConfig);
  }
  
  // Validate and sanitize configuration
  return validateConfiguration(storedConfig);
};

const validateConfiguration = (config: any): LocalStorageConfig => {
  // Validate agent inboxes
  const agentInboxes = Array.isArray(config["agent-inboxes"]) 
    ? config["agent-inboxes"].map(validateAgentInbox)
    : [];
  
  // Validate API key
  const apiKey = typeof config["langchain-api-key"] === "string" 
    ? config["langchain-api-key"] 
    : "";
  
  // Set defaults for missing preferences
  const userPreferences: UserPreferences = {
    theme: config["user-preferences"]?.theme || "system",
    defaultInboxView: config["user-preferences"]?.defaultInboxView || "interrupted",
    autoRefreshInterval: config["user-preferences"]?.autoRefreshInterval || 30000,
    notificationsEnabled: config["user-preferences"]?.notificationsEnabled ?? true,
  };
  
  return {
    "agent-inboxes": agentInboxes,
    "langchain-api-key": apiKey,
    "user-preferences": userPreferences,
    "ui-settings": config["ui-settings"] || {},
  };
};
```

### 3. Settings Management Hook

```typescript
// Custom hook for settings management
export const useSettings = () => {
  const { getItem, setItem } = useLocalStorage();
  
  const getSettings = (): LocalStorageConfig => {
    const rawConfig = {
      "agent-inboxes": getItem(AGENT_INBOXES_LOCAL_STORAGE_KEY),
      "langchain-api-key": getItem(LANGCHAIN_API_KEY_LOCAL_STORAGE_KEY),
      "user-preferences": getItem("user-preferences"),
      "ui-settings": getItem("ui-settings"),
    };
    
    return migrateConfiguration(rawConfig);
  };
  
  const updateSettings = (updates: Partial<LocalStorageConfig>) => {
    const currentSettings = getSettings();
    const newSettings = { ...currentSettings, ...updates };
    
    // Save individual settings
    Object.entries(newSettings).forEach(([key, value]) => {
      setItem(key, JSON.stringify(value));
    });
  };
  
  const resetSettings = () => {
    localStorage.clear();
    window.location.reload();
  };
  
  return {
    getSettings,
    updateSettings,
    resetSettings,
  };
};
```

## Development Configuration

### 1. Package Scripts

**File**: `/Users/<USER>/Projects/own/assistant/agent-inbox/package.json`

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "format": "prettier --config .prettierrc --write \"src\"",
    "format:check": "prettier --config .prettierrc --check \"src\""
  }
}
```

**Script Purposes**:
- **dev**: Development server with hot reload
- **build**: Production build generation
- **start**: Production server startup
- **lint**: Code quality checking
- **format**: Code formatting with Prettier

### 2. ESLint Configuration

```json
// .eslintrc.json (implied)
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended"
  ],
  "plugins": [
    "unused-imports"
  ],
  "rules": {
    "unused-imports/no-unused-imports": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off"
  }
}
```

### 3. Prettier Configuration

```json
// .prettierrc (implied)
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": false,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

## Deployment Configuration

### 1. Production Environment Variables

```bash
# Required environment variables
LANGSMITH_API_KEY=your_langsmith_api_key_here

# Optional environment variables
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=production
NEXT_PUBLIC_API_BASE_URL=https://api.langgraph.app

# Analytics (optional)
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id

# Error tracking (optional)
SENTRY_DSN=your_sentry_dsn
```

### 2. Docker Configuration (Potential)

```dockerfile
# Dockerfile (not present but recommended)
FROM node:18-alpine AS deps
WORKDIR /app
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN yarn build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

### 3. Vercel Configuration (Potential)

```json
// vercel.json (not present but recommended)
{
  "buildCommand": "yarn build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "functions": {
    "src/pages/api/**/*.ts": {
      "runtime": "nodejs18.x"
    }
  },
  "redirects": [
    {
      "source": "/inbox",
      "destination": "/",
      "permanent": true
    }
  ]
}
```

## Security Configuration

### 1. Content Security Policy

```typescript
// next.config.mjs security headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];

const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ];
  },
};
```

### 2. API Security Configuration

```typescript
// Client configuration with security considerations
export const createSecureClient = ({
  deploymentUrl,
  langchainApiKey,
}: {
  deploymentUrl: string;
  langchainApiKey: string | undefined;
}) => {
  // Validate URL to prevent SSRF
  const url = new URL(deploymentUrl);
  if (!['https:', 'http:'].includes(url.protocol)) {
    throw new Error('Invalid protocol. Only HTTP and HTTPS are allowed.');
  }
  
  // Validate domain whitelist for production
  if (process.env.NODE_ENV === 'production') {
    const allowedDomains = ['us.langgraph.app', 'api.langchain.com'];
    if (!allowedDomains.some(domain => url.hostname.includes(domain))) {
      throw new Error('Domain not in allowlist for production environment.');
    }
  }
  
  return new Client({
    apiUrl: deploymentUrl,
    defaultHeaders: {
      ...(langchainApiKey && { "x-api-key": langchainApiKey }),
    },
  });
};
```

This comprehensive configuration system provides flexibility, security, and maintainability while supporting various deployment scenarios and user preferences.