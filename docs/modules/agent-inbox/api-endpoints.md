# Agent Inbox - API Endpoints and Communication

## Overview

The Agent Inbox operates as a client-side application that communicates with external APIs, primarily the LangGraph platform. While it doesn't expose its own API endpoints, it acts as a sophisticated API client that interfaces with multiple external services.

## LangGraph API Integration

### 1. Client Configuration

**Base Client Setup**:
```typescript
// File: /Users/<USER>/Projects/own/assistant/agent-inbox/src/lib/client.ts
export const createClient = ({
  deploymentUrl,
  langchainApiKey,
}: {
  deploymentUrl: string;
  langchainApiKey: string | undefined;
}) => {
  return new Client({
    apiUrl: deploymentUrl,
    defaultHeaders: {
      ...(langchainApiKey && { "x-api-key": langchainApiKey }),
    },
  });
};
```

**Authentication Headers**:
- `x-api-key`: LangSmith API key for authentication
- Dynamic deployment URL support for different environments

### 2. Thread Management Endpoints

#### Thread Search
**Method**: `GET`  
**SDK Method**: `client.threads.search()`  
**Purpose**: Retrieve filtered list of threads  

**Request Parameters**:
```typescript
interface ThreadSearchParams {
  offset: number;           // Pagination offset
  limit: number;            // Maximum results (max 100)
  status?: ThreadStatus;    // Optional status filter
  metadata?: Record<string, any>; // Optional metadata filter
}
```

**Response**: Array of `Thread<ThreadValues>` objects

**Usage Example**:
```typescript
const threads = await client.threads.search({
  offset: 0,
  limit: 10,
  status: "interrupted",
  metadata: getThreadFilterMetadata(agentInboxes)
});
```

#### Individual Thread Retrieval
**Method**: `GET`  
**SDK Method**: `client.threads.get(threadId)`  
**Purpose**: Retrieve specific thread by ID  

**Request Parameters**:
- `threadId`: String - Unique thread identifier

**Response**: Single `Thread<ThreadValues>` object

**Usage Example**:
```typescript
const thread = await client.threads.get(threadId);
```

#### Thread State Retrieval
**Method**: `GET`  
**SDK Method**: `client.threads.getState(threadId)`  
**Purpose**: Get current state of a thread  

**Request Parameters**:
- `threadId`: String - Unique thread identifier

**Response**: `ThreadState<ThreadValues>` object

**Bulk State Implementation**:
```typescript
const bulkGetThreadStates = async (threadIds: string[]) => {
  const chunkSize = 25;
  const chunks = [];
  
  // Split into chunks to avoid overwhelming the API
  for (let i = 0; i < threadIds.length; i += chunkSize) {
    chunks.push(threadIds.slice(i, i + chunkSize));
  }
  
  const results = [];
  for (const chunk of chunks) {
    const chunkResults = await Promise.all(
      chunk.map(async (id) => ({
        thread_id: id,
        thread_state: await client.threads.getState<ThreadValues>(id),
      }))
    );
    results.push(...chunkResults);
  }
  
  return results;
};
```

#### Thread State Updates
**Method**: `POST`/`PUT`  
**SDK Method**: `client.threads.updateState(threadId, update)`  
**Purpose**: Update thread state  

**Request Parameters**:
```typescript
interface ThreadStateUpdate {
  values: any | null;       // New state values
  asNode?: string;          // Optional node to update as
}
```

**Usage Example**:
```typescript
// Ignore thread (terminate execution)
await client.threads.updateState(threadId, {
  values: null,
  asNode: END,
});
```

### 3. Run Management Endpoints

#### Run Creation
**Method**: `POST`  
**SDK Method**: `client.runs.create(threadId, graphId, config)`  
**Purpose**: Create new run for thread  

**Request Parameters**:
```typescript
interface RunCreateConfig {
  command: {
    resume: HumanResponse[];  // Human response to resume with
  };
}
```

**Response**: `Run` object with execution details

**Usage Example**:
```typescript
const run = await client.runs.create(threadId, graphId, {
  command: {
    resume: response,
  },
});
```

#### Streaming Runs
**Method**: `POST` (with streaming)  
**SDK Method**: `client.runs.stream(threadId, graphId, config)`  
**Purpose**: Create run with real-time streaming updates  

**Request Parameters**:
```typescript
interface StreamConfig {
  command: {
    resume: HumanResponse[];
  };
  streamMode: "events";     // Stream mode configuration
}
```

**Response**: `AsyncGenerator` of streaming events

**Streaming Implementation**:
```typescript
const response = client.runs.stream(threadId, graphId, {
  command: { resume: response },
  streamMode: "events",
});

for await (const chunk of response) {
  if (chunk.data?.event === "on_chain_start" && chunk.data?.metadata?.langgraph_node) {
    setCurrentNode(chunk.data.metadata.langgraph_node);
  } else if (typeof chunk.event === "string" && chunk.event === "error") {
    // Handle streaming errors
    handleStreamingError(chunk.data);
  }
}
```

## API Communication Patterns

### 1. Error Handling

**Pattern**: Comprehensive error handling with user feedback

```typescript
const getClient = ({ agentInboxes, getItem, toast }: GetClientArgs) => {
  if (agentInboxes.length === 0) {
    toast({
      title: "Error",
      description: "Agent inbox not found. Please add an inbox in settings.",
      variant: "destructive",
      duration: 3000,
    });
    return;
  }
  
  const deploymentUrl = agentInboxes.find((i) => i.selected)?.deploymentUrl;
  if (!deploymentUrl) {
    toast({
      title: "Error",
      description: "Please ensure your selected agent inbox has a deployment URL.",
      variant: "destructive",
      duration: 5000,
    });
    return;
  }
  
  const langchainApiKeyLS = getItem(LANGCHAIN_API_KEY_LOCAL_STORAGE_KEY) || undefined;
  if (!langchainApiKeyLS && deploymentUrl.includes("us.langgraph.app")) {
    toast({
      title: "Error",
      description: "Please add your LangSmith API key in settings.",
      variant: "destructive",
      duration: 5000,
    });
    return;
  }
  
  return createClient({ deploymentUrl, langchainApiKey: langchainApiKeyLS });
};
```

### 2. Rate Limiting and Performance

**Chunked Processing**:
```typescript
// Prevent API overload with chunked requests
const chunkSize = 25;
const chunks = [];

for (let i = 0; i < threadIds.length; i += chunkSize) {
  chunks.push(threadIds.slice(i, i + chunkSize));
}

// Process chunks sequentially
for (const chunk of chunks) {
  const chunkResults = await Promise.all(
    chunk.map(async (id) => ({
      thread_id: id,
      thread_state: await client.threads.getState<ThreadValues>(id),
    }))
  );
  results.push(...chunkResults);
}
```

**Limit Enforcement**:
```typescript
if (limit > 100) {
  toast({
    title: "Error",
    description: "Cannot fetch more than 100 threads at a time",
    variant: "destructive",
    duration: 3000,
  });
  return;
}
```

### 3. Authentication Flow

**Environment-Based Authentication**:
```typescript
// Production deployments require API key
if (!langchainApiKeyLS && deploymentUrl.includes("us.langgraph.app")) {
  toast({
    title: "Error",
    description: "Please add your LangSmith API key in settings.",
    variant: "destructive",
    duration: 5000,
  });
  return;
}

// Local deployments may not require authentication
return createClient({ deploymentUrl, langchainApiKey: langchainApiKeyLS });
```

## Data Transformation Layer

### 1. Request Transformation

**Thread Filtering**:
```typescript
const statusInput = inbox === "all" ? {} : { status: inbox };
const metadataInput = getThreadFilterMetadata(agentInboxes);

const threadSearchArgs = {
  offset,
  limit,
  ...statusInput,
  ...(metadataInput ? { metadata: metadataInput } : {}),
};
```

**Human Response Transformation**:
```typescript
const humanResponseInput: HumanResponse[] = humanResponse.flatMap((r) => {
  if (r.type === "edit") {
    if (r.acceptAllowed && !r.editsMade) {
      return { type: "accept", args: r.args };
    } else {
      return { type: "edit", args: r.args };
    }
  }
  
  if (r.type === "response" && !r.args) {
    return []; // Skip empty responses
  }
  
  return { type: r.type, args: r.args };
});
```

### 2. Response Processing

**Thread Data Processing**:
```typescript
const processInterruptedThread = (
  thread: Thread<ThreadValues>
): ThreadData<ThreadValues> | undefined => {
  const interrupts = getInterruptFromThread(thread);
  if (!interrupts?.length) return undefined;
  
  return {
    thread,
    status: "interrupted" as const,
    interrupts,
  };
};
```

**Legacy Support**:
```typescript
// Handle threads without native interrupt support
const threadsWithoutInterrupts = interruptedThreads.filter(
  (t) => !getInterruptFromThread(t)?.length
);

if (threadsWithoutInterrupts.length > 0) {
  const states = await bulkGetThreadStates(
    threadsWithoutInterrupts.map((t) => t.thread_id)
  );
  
  const interruptedData = states.map((state) => {
    const thread = threadsWithoutInterrupts.find(
      (t) => t.thread_id === state.thread_id
    );
    return processThreadWithoutInterrupts(thread, state);
  });
  
  data.push(...interruptedData);
}
```

## WebSocket and Real-Time Communication

### 1. Streaming Events

**Event Types**:
- `on_chain_start`: Node execution start
- `on_chain_end`: Node execution completion
- `error`: Error events
- `data`: Data updates

**Stream Processing**:
```typescript
for await (const chunk of response) {
  if (chunk.data?.event === "on_chain_start" && chunk.data?.metadata?.langgraph_node) {
    setCurrentNode(chunk.data.metadata.langgraph_node);
  } else if (typeof chunk.event === "string" && chunk.event === "error") {
    toast({
      title: "Error",
      description: (
        <div className="flex flex-col gap-1 items-start">
          <p>Something went wrong while attempting to run the graph.</p>
          <span>
            <strong>Error:</strong>
            <span className="font-mono">
              {JSON.stringify(chunk.data, null)}
            </span>
          </span>
        </div>
      ),
      variant: "destructive",
      duration: 15000,
    });
    setCurrentNode("__error__");
    errorOccurred = true;
  }
}
```

### 2. Connection Management

**Dynamic Client Creation**:
```typescript
const getClient = ({ agentInboxes, getItem, toast }: GetClientArgs) => {
  const deploymentUrl = agentInboxes.find((i) => i.selected)?.deploymentUrl;
  const langchainApiKey = getItem(LANGCHAIN_API_KEY_LOCAL_STORAGE_KEY) || undefined;
  
  return createClient({ deploymentUrl, langchainApiKey });
};
```

**Multi-Deployment Support**:
```typescript
// Switch between different deployments
const changeAgentInbox = (id: string, replaceAll?: boolean) => {
  setAgentInboxes((prev) =>
    prev.map((i) => ({
      ...i,
      selected: i.id === id,
    }))
  );
  
  if (!replaceAll) {
    updateQueryParams(AGENT_INBOX_PARAM, id);
  } else {
    const url = new URL(window.location.href);
    const newParams = new URLSearchParams({
      [AGENT_INBOX_PARAM]: id,
    });
    const newUrl = url.pathname + "?" + newParams.toString();
    window.location.href = newUrl;
  }
};
```

## Future API Considerations

### 1. RESTful API Extension

**Potential Endpoints**:
- `GET /api/health` - System health check
- `GET /api/config` - Configuration management
- `POST /api/webhooks` - Webhook management
- `GET /api/analytics` - Usage analytics

### 2. GraphQL Support

**Potential Schema**:
```graphql
type Thread {
  id: ID!
  status: ThreadStatus!
  interrupts: [HumanInterrupt!]
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Query {
  threads(
    status: ThreadStatus
    limit: Int = 10
    offset: Int = 0
  ): [Thread!]!
  
  thread(id: ID!): Thread
}

type Mutation {
  respondToInterrupt(
    threadId: ID!
    response: HumanResponseInput!
  ): Thread!
}
```

### 3. Server-Side API

**Potential Express.js Routes**:
```typescript
// Future server-side API structure
app.get('/api/threads', async (req, res) => {
  const { status, limit, offset } = req.query;
  const threads = await threadService.search({ status, limit, offset });
  res.json(threads);
});

app.post('/api/threads/:id/respond', async (req, res) => {
  const { id } = req.params;
  const { response } = req.body;
  const result = await threadService.respond(id, response);
  res.json(result);
});
```

This comprehensive API communication architecture ensures reliable, performant, and secure interaction with external systems while maintaining flexibility for future enhancements.