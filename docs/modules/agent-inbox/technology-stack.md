# Agent Inbox - Technology Stack Analysis

## Core Framework Stack

### 1. Frontend Framework
- **Next.js 14.2.25**: React-based web framework with SSR capabilities
  - Used primarily as SPA with client-side rendering
  - File-based routing system
  - Built-in optimization features

### 2. React Ecosystem
- **React 18**: Core UI library with concurrent features
- **React DOM 18**: DOM rendering and hydration
- **React Hooks**: State management and lifecycle handling

### 3. TypeScript Integration
- **TypeScript 5**: Static type checking and improved developer experience
- **@types/react**: React type definitions
- **@types/react-dom**: React DOM type definitions
- **@types/node**: Node.js type definitions

## LangGraph Integration Stack

### 1. LangChain Ecosystem
- **@langchain/langgraph**: Core LangGraph integration library
- **@langchain/langgraph-sdk**: SDK for LangGraph API communication
- **@langchain/core**: Core LangChain abstractions and interfaces
- **@langchain/anthropic**: Anthropic AI model integration
- **@langchain/openai**: OpenAI API integration
- **@langchain/google-genai**: Google Generative AI integration
- **@langchain/community**: Community-contributed components

### 2. Language Model Providers
- **langchain**: Primary LangChain framework
- **langsmith**: LangSmith tracing and monitoring

## UI Component Libraries

### 1. Radix UI Primitives
- **@radix-ui/react-avatar**: Avatar component primitives
- **@radix-ui/react-checkbox**: Checkbox input primitives
- **@radix-ui/react-collapsible**: Collapsible content primitives
- **@radix-ui/react-dialog**: Modal dialog primitives
- **@radix-ui/react-dropdown-menu**: Dropdown menu primitives
- **@radix-ui/react-hover-card**: Hover card primitives
- **@radix-ui/react-icons**: Icon component library
- **@radix-ui/react-label**: Label component primitives
- **@radix-ui/react-popover**: Popover primitives
- **@radix-ui/react-progress**: Progress bar primitives
- **@radix-ui/react-select**: Select dropdown primitives
- **@radix-ui/react-separator**: Separator line primitives
- **@radix-ui/react-slider**: Slider input primitives
- **@radix-ui/react-slot**: Slot component primitives
- **@radix-ui/react-toast**: Toast notification primitives
- **@radix-ui/react-tooltip**: Tooltip primitives

### 2. Assistant UI Components
- **@assistant-ui/react**: AI assistant interface components
- **@assistant-ui/react-markdown**: Markdown rendering for AI responses
- **@assistant-ui/react-syntax-highlighter**: Code syntax highlighting

### 3. Additional UI Libraries
- **lucide-react**: Modern icon library
- **react-icons**: Popular icon collections
- **framer-motion**: Animation library

## Rich Text and Code Editing

### 1. Markdown and Rich Text
- **react-markdown**: Markdown parsing and rendering
- **remark-gfm**: GitHub Flavored Markdown support
- **remark-math**: Mathematical expressions in markdown
- **rehype-katex**: LaTeX math rendering
- **rehype-raw**: Raw HTML support in markdown

### 2. Code Editing Stack
- **@blocknote/core**: Block-based rich text editor core
- **@blocknote/react**: React components for BlockNote
- **@blocknote/mantine**: Mantine UI integration
- **@blocknote/shadcn**: Shadcn UI integration
- **@uiw/react-codemirror**: CodeMirror React integration
- **@uiw/react-md-editor**: Markdown editor component

### 3. Language Support
- **@codemirror/lang-cpp**: C++ language support
- **@codemirror/lang-html**: HTML language support
- **@codemirror/lang-java**: Java language support
- **@codemirror/lang-javascript**: JavaScript language support
- **@codemirror/lang-json**: JSON language support
- **@codemirror/lang-php**: PHP language support
- **@codemirror/lang-python**: Python language support
- **@codemirror/lang-rust**: Rust language support
- **@codemirror/lang-sql**: SQL language support
- **@codemirror/lang-xml**: XML language support
- **@replit/codemirror-lang-csharp**: C# language support
- **@nextjournal/lang-clojure**: Clojure language support

### 4. Syntax Highlighting
- **react-syntax-highlighter**: Code syntax highlighting
- **@types/react-syntax-highlighter**: Type definitions

## Styling and Design System

### 1. CSS Framework
- **Tailwind CSS 3.4.1**: Utility-first CSS framework
- **PostCSS 8**: CSS post-processing
- **tailwindcss-animate**: Animation utilities
- **tailwind-merge**: Utility class merging
- **tailwind-scrollbar**: Custom scrollbar utilities
- **tailwind-scrollbar-hide**: Scrollbar hiding utilities

### 2. Component Styling
- **class-variance-authority**: CSS class variance management
- **clsx**: Conditional class name utility

### 3. Color and Theming
- **react-colorful**: Color picker components

## Data Management and Utilities

### 1. State Management
- **React Context**: Global state management
- **Local Storage**: Browser storage integration
- **URL Parameters**: Session state persistence

### 2. Data Processing
- **lodash**: Utility library for data manipulation
- **zod**: TypeScript-first schema validation
- **uuid**: UUID generation and validation
- **@types/uuid**: UUID type definitions

### 3. Date and Time
- **date-fns**: Date manipulation and formatting

### 4. Data Visualization
- **@tanstack/react-table**: Table component with sorting/filtering
- **react-json-view**: JSON data visualization

## Network and API Integration

### 1. HTTP Client
- **fetch**: Native browser API (via LangGraph SDK)
- **WebSocket**: Real-time communication support

### 2. Database Integration
- **@supabase/supabase-js**: Supabase client library
- **@supabase/ssr**: Server-side rendering support

### 3. Key-Value Storage
- **@vercel/kv**: Vercel KV storage integration

## Testing and Data Generation

### 1. Development Data
- **@faker-js/faker**: Fake data generation for testing

### 2. Cookie Management
- **js-cookie**: Client-side cookie handling
- **@types/js-cookie**: Cookie type definitions

## Development Tools

### 1. Linting and Formatting
- **ESLint 8**: Code linting
- **@eslint/js**: ESLint JavaScript configuration
- **@typescript-eslint/eslint-plugin**: TypeScript ESLint rules
- **@typescript-eslint/parser**: TypeScript parser for ESLint
- **typescript-eslint**: TypeScript ESLint configuration
- **eslint-config-next**: Next.js ESLint configuration
- **eslint-plugin-unused-imports**: Unused import detection

### 2. Code Formatting
- **Prettier 3.3.3**: Code formatting
- **tailwind-scrollbar**: Tailwind scrollbar plugin

### 3. Build Tools
- **tsx**: TypeScript execution
- **dotenv**: Environment variable management

## Package Management

### 1. Package Manager
- **Yarn 1.22.22**: Package manager with lock file
- **yarn.lock**: Dependency resolution lock file

### 2. Package Scripts
```json
{
  "dev": "next dev",
  "build": "next build",
  "start": "next start",
  "lint": "next lint",
  "lint:fix": "next lint --fix",
  "format": "prettier --config .prettierrc --write \"src\"",
  "format:check": "prettier --config .prettierrc --check \"src\""
}
```

## Configuration Files

### 1. TypeScript Configuration
- **tsconfig.json**: TypeScript compiler configuration
- **@types/eslint__js**: ESLint type definitions
- **@types/lodash**: Lodash type definitions

### 2. Build Configuration
- **next.config.mjs**: Next.js configuration
- **postcss.config.mjs**: PostCSS configuration
- **tailwind.config.ts**: Tailwind CSS configuration

### 3. Component Configuration
- **components.json**: UI component configuration (likely for shadcn/ui)

## Security Considerations

### 1. Dependencies Security
- All packages are regularly updated
- No known high-severity vulnerabilities
- Proper API key management patterns

### 2. Authentication
- LangSmith API key authentication
- Local storage security considerations
- Environment-specific configuration

## Performance Optimization

### 1. Bundle Optimization
- Next.js automatic code splitting
- Tree shaking for unused code
- Dynamic imports for large components

### 2. Runtime Performance
- React 18 concurrent features
- Memoization for expensive operations
- Efficient re-rendering patterns

### 3. Network Optimization
- Chunked API requests
- Polling optimization
- Caching strategies

## Deployment Architecture

### 1. Static Site Generation
- Next.js build system
- Client-side hydration
- CDN compatibility

### 2. Environment Management
- Development vs production builds
- Environment variable handling
- API endpoint configuration

This comprehensive technology stack provides a robust foundation for building scalable, maintainable, and feature-rich AI agent management interfaces.