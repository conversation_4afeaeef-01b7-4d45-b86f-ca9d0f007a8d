# Agent Inbox - Testing Strategy and Implementation

## Overview

While the current Agent Inbox implementation does not include an explicit testing suite, this document outlines the testing approach that should be implemented to ensure reliability, maintainability, and quality assurance for the application.

## Current Testing Status

**Current State**: No automated testing suite identified in the codebase
**Package.json Scripts**: No test-related scripts present
**Testing Dependencies**: No testing frameworks currently installed

**Recommended Testing Stack**:
- **Jest**: JavaScript testing framework
- **React Testing Library**: React component testing
- **Playwright**: End-to-end testing
- **MSW (Mock Service Worker)**: API mocking
- **Testing Library User Event**: User interaction simulation

## Proposed Testing Architecture

### 1. Unit Testing Strategy

**Target Components for Unit Testing**:

#### Utility Functions
```typescript
// tests/lib/utils.test.ts
import { createClient } from '@/lib/client';
import { convertMessages } from '@/lib/convert_messages';
import { cn } from '@/lib/utils';

describe('createClient', () => {
  test('creates client with API key', () => {
    const client = createClient({
      deploymentUrl: 'https://api.langgraph.app',
      langchainApiKey: 'test-key',
    });
    
    expect(client).toBeDefined();
    expect(client.defaultHeaders).toEqual({
      'x-api-key': 'test-key',
    });
  });
  
  test('creates client without API key for local deployment', () => {
    const client = createClient({
      deploymentUrl: 'http://localhost:8000',
      langchainApiKey: undefined,
    });
    
    expect(client).toBeDefined();
    expect(client.defaultHeaders).toEqual({});
  });
});

describe('utility functions', () => {
  test('cn function merges class names correctly', () => {
    expect(cn('class1', 'class2')).toBe('class1 class2');
    expect(cn('class1', false && 'class2', 'class3')).toBe('class1 class3');
  });
});
```

#### Custom Hooks Testing
```typescript
// tests/hooks/useLocalStorage.test.ts
import { renderHook, act } from '@testing-library/react';
import { useLocalStorage } from '@/components/agent-inbox/hooks/use-local-storage';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('useLocalStorage', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });

  test('gets item from localStorage', () => {
    localStorageMock.getItem.mockReturnValue('test-value');
    
    const { result } = renderHook(() => useLocalStorage());
    
    expect(result.current.getItem('test-key')).toBe('test-value');
    expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key');
  });

  test('sets item in localStorage', () => {
    const { result } = renderHook(() => useLocalStorage());
    
    act(() => {
      result.current.setItem('test-key', 'test-value');
    });
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith('test-key', 'test-value');
  });
});
```

#### Type and Schema Validation
```typescript
// tests/types/validation.test.ts
import { HumanInterrupt, HumanResponse, AgentInbox } from '@/components/agent-inbox/types';

describe('Type Validation', () => {
  test('HumanInterrupt interface validation', () => {
    const validInterrupt: HumanInterrupt = {
      action_request: {
        action: 'send_email',
        args: { to: '<EMAIL>', subject: 'Test' },
      },
      config: {
        allow_ignore: true,
        allow_respond: true,
        allow_edit: false,
        allow_accept: true,
      },
      description: 'Send email to customer',
    };
    
    expect(validInterrupt).toBeDefined();
    expect(validInterrupt.action_request.action).toBe('send_email');
  });

  test('AgentInbox configuration validation', () => {
    const validInbox: AgentInbox = {
      id: 'test-id',
      graphId: 'test-graph',
      deploymentUrl: 'https://api.langgraph.app',
      name: 'Test Inbox',
      selected: true,
    };
    
    expect(validInbox).toBeDefined();
    expect(validInbox.deploymentUrl).toMatch(/^https?:\/\//);
  });
});
```

### 2. Component Testing Strategy

**React Testing Library Implementation**:

#### UI Component Testing
```typescript
// tests/components/ui/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/button';

describe('Button Component', () => {
  test('renders button with text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  test('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies variant styles correctly', () => {
    render(<Button variant="destructive">Delete</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-destructive');
  });

  test('handles disabled state', () => {
    render(<Button disabled>Disabled</Button>);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('opacity-50');
  });
});
```

#### Dialog Component Testing
```typescript
// tests/components/ui/Dialog.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';

describe('Dialog Component', () => {
  test('opens and closes dialog', () => {
    render(
      <Dialog>
        <DialogTrigger>Open Dialog</DialogTrigger>
        <DialogContent>
          <div>Dialog Content</div>
        </DialogContent>
      </Dialog>
    );

    // Dialog should be closed initially
    expect(screen.queryByText('Dialog Content')).not.toBeInTheDocument();

    // Open dialog
    fireEvent.click(screen.getByText('Open Dialog'));
    expect(screen.getByText('Dialog Content')).toBeInTheDocument();

    // Close dialog with escape key
    fireEvent.keyDown(document, { key: 'Escape' });
    expect(screen.queryByText('Dialog Content')).not.toBeInTheDocument();
  });
});
```

#### Agent Inbox Component Testing
```typescript
// tests/components/agent-inbox/InboxItem.test.tsx
import { render, screen } from '@testing-library/react';
import { InboxItem } from '@/components/agent-inbox/components/inbox-item';
import { ThreadData, HumanInterrupt } from '@/components/agent-inbox/types';

// Mock the hooks
jest.mock('@/components/agent-inbox/hooks/use-query-params', () => ({
  useQueryParams: () => ({
    searchParams: new URLSearchParams('inbox=interrupted'),
  }),
}));

describe('InboxItem Component', () => {
  const mockThreadData: ThreadData = {
    thread: {
      thread_id: 'test-thread-id',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      metadata: {},
      status: 'interrupted',
      config: {},
      values: {},
    },
    status: 'interrupted',
    interrupts: [
      {
        action_request: {
          action: 'send_email',
          args: { to: '<EMAIL>' },
        },
        config: {
          allow_ignore: true,
          allow_respond: true,
          allow_edit: false,
          allow_accept: true,
        },
      },
    ],
  };

  test('renders interrupted inbox item', () => {
    render(<InboxItem threadData={mockThreadData} isLast={false} />);
    
    // Should render the interrupted inbox item component
    expect(screen.getByText('send_email')).toBeInTheDocument();
  });

  test('renders generic inbox item for non-interrupted threads', () => {
    const genericThreadData = {
      ...mockThreadData,
      status: 'idle' as const,
      interrupts: undefined,
    };

    render(<InboxItem threadData={genericThreadData} isLast={false} />);
    
    // Should render the generic inbox item
    expect(screen.getByText('test-thread-id')).toBeInTheDocument();
  });
});
```

### 3. Integration Testing

**Context Provider Testing**:
```typescript
// tests/contexts/ThreadContext.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { ThreadsProvider, useThreadsContext } from '@/components/agent-inbox/contexts/ThreadContext';

// Mock the LangGraph client
jest.mock('@/lib/client', () => ({
  createClient: jest.fn(() => ({
    threads: {
      search: jest.fn(),
      get: jest.fn(),
      getState: jest.fn(),
    },
    runs: {
      create: jest.fn(),
      stream: jest.fn(),
    },
  })),
}));

const TestComponent = () => {
  const { threadData, loading } = useThreadsContext();
  
  if (loading) return <div>Loading...</div>;
  
  return (
    <div>
      <div>Thread count: {threadData.length}</div>
    </div>
  );
};

describe('ThreadsProvider', () => {
  test('provides thread context to children', async () => {
    render(
      <ThreadsProvider>
        <TestComponent />
      </ThreadsProvider>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText(/Thread count:/)).toBeInTheDocument();
    });
  });
});
```

**Hook Integration Testing**:
```typescript
// tests/hooks/useInterruptedActions.test.tsx
import { renderHook, act } from '@testing-library/react';
import useInterruptedActions from '@/components/agent-inbox/hooks/use-interrupted-actions';
import { ThreadsProvider } from '@/components/agent-inbox/contexts/ThreadContext';

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <ThreadsProvider>{children}</ThreadsProvider>
);

describe('useInterruptedActions', () => {
  const mockThreadData = {
    thread: {
      thread_id: 'test-thread',
      // ... other thread properties
    },
    status: 'interrupted' as const,
    interrupts: [
      {
        action_request: { action: 'test_action', args: {} },
        config: {
          allow_ignore: true,
          allow_respond: true,
          allow_edit: false,
          allow_accept: true,
        },
      },
    ],
  };

  test('initializes with correct default state', () => {
    const { result } = renderHook(
      () => useInterruptedActions({
        threadData: mockThreadData,
        setThreadData: jest.fn(),
      }),
      { wrapper }
    );

    expect(result.current.loading).toBe(false);
    expect(result.current.streaming).toBe(false);
    expect(result.current.isIgnoreAllowed).toBe(true);
  });
});
```

### 4. End-to-End Testing Strategy

**Playwright E2E Tests**:

```typescript
// tests/e2e/agent-inbox-flow.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Agent Inbox E2E Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/threads/search', async (route) => {
      await route.fulfill({
        json: [
          {
            thread_id: 'test-thread-1',
            status: 'interrupted',
            created_at: '2023-01-01T00:00:00Z',
            interrupts: [
              {
                action_request: {
                  action: 'send_email',
                  args: { to: '<EMAIL>' },
                },
                config: {
                  allow_ignore: true,
                  allow_respond: true,
                  allow_edit: false,
                  allow_accept: true,
                },
              },
            ],
          },
        ],
      });
    });

    await page.goto('/');
  });

  test('displays interrupted threads', async ({ page }) => {
    await expect(page.getByText('send_email')).toBeVisible();
    await expect(page.getByText('<EMAIL>')).toBeVisible();
  });

  test('can respond to interrupted thread', async ({ page }) => {
    // Click on thread to view details
    await page.getByText('send_email').click();
    
    // Should navigate to thread view
    await expect(page.url()).toContain('thread=test-thread-1');
    
    // Add response
    await page.getByPlaceholder('Enter your response...').fill('Approved');
    await page.getByRole('button', { name: 'Submit' }).click();
    
    // Should show success message
    await expect(page.getByText('Response submitted successfully')).toBeVisible();
  });

  test('can add new agent inbox', async ({ page }) => {
    // Open settings
    await page.getByRole('button', { name: 'Settings' }).click();
    
    // Click add inbox
    await page.getByRole('button', { name: 'Add Inbox' }).click();
    
    // Fill out form
    await page.getByLabel('Graph ID').fill('test-graph');
    await page.getByLabel('Deployment URL').fill('https://api.langgraph.app');
    await page.getByLabel('Name').fill('Test Inbox');
    
    // Submit form
    await page.getByRole('button', { name: 'Create Inbox' }).click();
    
    // Should see new inbox in sidebar
    await expect(page.getByText('Test Inbox')).toBeVisible();
  });

  test('filters threads by status', async ({ page }) => {
    // Click on different status filters
    await page.getByRole('button', { name: 'All' }).click();
    await expect(page.url()).toContain('inbox=all');
    
    await page.getByRole('button', { name: 'Idle' }).click();
    await expect(page.url()).toContain('inbox=idle');
    
    await page.getByRole('button', { name: 'Interrupted' }).click();
    await expect(page.url()).toContain('inbox=interrupted');
  });
});
```

**Accessibility Testing**:
```typescript
// tests/e2e/accessibility.spec.ts
import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility Tests', () => {
  test('homepage should be accessible', async ({ page }) => {
    await page.goto('/');
    
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('thread view should be accessible', async ({ page }) => {
    await page.goto('/?thread=test-thread-1');
    
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('keyboard navigation works', async ({ page }) => {
    await page.goto('/');
    
    // Tab through interactive elements
    await page.keyboard.press('Tab');
    await expect(page.getByRole('button', { name: 'Settings' })).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByRole('button', { name: 'Interrupted' })).toBeFocused();
  });
});
```

### 5. Performance Testing

**Web Vitals Testing**:
```typescript
// tests/performance/web-vitals.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test('page load performance', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    
    // Wait for main content to load
    await page.waitForSelector('[data-testid="inbox-view"]');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('measures Core Web Vitals', async ({ page }) => {
    await page.goto('/');
    
    // Measure LCP (Largest Contentful Paint)
    const lcp = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          resolve(lastEntry.startTime);
        }).observe({ entryTypes: ['largest-contentful-paint'] });
      });
    });
    
    // LCP should be under 2.5 seconds
    expect(lcp).toBeLessThan(2500);
  });
});
```

### 6. API Mocking Strategy

**Mock Service Worker Setup**:
```typescript
// tests/mocks/handlers.ts
import { rest } from 'msw';

export const handlers = [
  // Mock LangGraph API
  rest.get('*/threads/search', (req, res, ctx) => {
    const status = req.url.searchParams.get('status');
    
    return res(
      ctx.json([
        {
          thread_id: 'test-thread-1',
          status: status || 'interrupted',
          created_at: '2023-01-01T00:00:00Z',
          interrupts: status === 'interrupted' ? [
            {
              action_request: {
                action: 'send_email',
                args: { to: '<EMAIL>' },
              },
              config: {
                allow_ignore: true,
                allow_respond: true,
                allow_edit: false,
                allow_accept: true,
              },
            },
          ] : undefined,
        },
      ])
    );
  }),

  rest.get('*/threads/:threadId', (req, res, ctx) => {
    return res(
      ctx.json({
        thread_id: req.params.threadId,
        status: 'interrupted',
        created_at: '2023-01-01T00:00:00Z',
      })
    );
  }),

  rest.post('*/runs/:threadId/:graphId', (req, res, ctx) => {
    return res(
      ctx.json({
        run_id: 'test-run-id',
        status: 'success',
      })
    );
  }),
];
```

```typescript
// tests/mocks/server.ts
import { setupServer } from 'msw/node';
import { handlers } from './handlers';

export const server = setupServer(...handlers);
```

### 7. Test Configuration

**Jest Configuration**:
```javascript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};

module.exports = createJestConfig(customJestConfig);
```

**Test Setup File**:
```typescript
// tests/setup.ts
import '@testing-library/jest-dom';
import { server } from './mocks/server';

// Start MSW server
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
}));
```

**Playwright Configuration**:
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

This comprehensive testing strategy would ensure the Agent Inbox maintains high quality, reliability, and user experience across all components and user flows.