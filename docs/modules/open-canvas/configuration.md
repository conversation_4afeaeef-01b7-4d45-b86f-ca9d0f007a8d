# Open Canvas - Configuration Documentation

## Configuration Architecture

Open Canvas uses a comprehensive configuration system that supports multiple environments, provider-specific settings, and runtime configuration management. The system is designed to be secure, flexible, and maintainable across different deployment scenarios.

```mermaid
graph TB
    subgraph "Configuration Sources"
        A[Environment Variables] --> B[Configuration Manager]
        C[Config Files] --> B
        D[Runtime Settings] --> B
        E[User Preferences] --> B
    end
    
    subgraph "Configuration Categories"
        B --> F[Authentication Config]
        B --> G[LLM Provider Config]
        B --> H[Database Config]
        B --> I[Feature Flags]
        B --> J[Performance Config]
        B --> K[Security Config]
    end
    
    subgraph "Applications"
        F --> L[Web App]
        G --> M[Agent Server]
        H --> N[Database]
        I --> O[Feature Toggle]
        J --> P[Optimization]
        K --> Q[Security Layer]
    end
```

## Environment Configuration

### 1. Root Environment Variables

#### Core Configuration (`.env`)
```bash
# LangGraph Configuration
LANGGRAPH_API_URL=http://localhost:54367
LANGGRAPH_API_KEY=your-api-key-here

# LangSmith Configuration
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your-langsmith-api-key
LANGCHAIN_PROJECT=open-canvas

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/opencanvas
REDIS_URL=redis://localhost:6379

# Security Configuration
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000
ENCRYPTION_KEY=your-encryption-key-here
```

#### LLM Provider Configuration
```bash
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORG_ID=your-org-id

# Anthropic Configuration
ANTHROPIC_API_KEY=your-anthropic-api-key

# Google AI Configuration
GOOGLE_GENAI_API_KEY=your-google-api-key

# Fireworks Configuration
FIREWORKS_API_KEY=your-fireworks-api-key

# Groq Configuration
GROQ_API_KEY=your-groq-api-key

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your-azure-openai-key
AZURE_OPENAI_API_INSTANCE_NAME=your-instance-name
AZURE_OPENAI_API_DEPLOYMENT_NAME=your-deployment-name
AZURE_OPENAI_API_VERSION=2024-08-01-preview
AZURE_OPENAI_API_BASE_PATH=https://your-instance.openai.azure.com

# Ollama Configuration
OLLAMA_API_URL=http://localhost:11434
OLLAMA_MODEL=llama3.3
```

#### External Services Configuration
```bash
# Supabase Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# FireCrawl Configuration
FIRECRAWL_API_KEY=your-firecrawl-api-key

# Exa Search Configuration
EXA_API_KEY=your-exa-api-key

# Audio Processing Configuration
WHISPER_API_KEY=your-whisper-api-key
WHISPER_MODEL=whisper-large-v3
```

### 2. Web Application Environment (`.env.local`)

#### Frontend Configuration
```bash
# Next.js Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Supabase Public Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Feature Flags
NEXT_PUBLIC_OLLAMA_ENABLED=true
NEXT_PUBLIC_WEB_SEARCH_ENABLED=true
NEXT_PUBLIC_CUSTOM_ACTIONS_ENABLED=true
NEXT_PUBLIC_MEMORY_ENABLED=true
NEXT_PUBLIC_ANALYTICS_ENABLED=true

# Performance Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760  # 10MB
NEXT_PUBLIC_MAX_CONCURRENT_REQUESTS=5
NEXT_PUBLIC_REQUEST_TIMEOUT=30000   # 30 seconds
NEXT_PUBLIC_CACHE_TTL=300000        # 5 minutes

# UI Configuration
NEXT_PUBLIC_THEME=system
NEXT_PUBLIC_DEFAULT_LANGUAGE=en
NEXT_PUBLIC_ENABLE_ANIMATIONS=true
```

#### Development Configuration
```bash
# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_ENABLE_LOGGING=true

# Hot Reload Configuration
NEXT_PUBLIC_FAST_REFRESH=true
NEXT_PUBLIC_STRICT_MODE=true

# Development URLs
NEXT_PUBLIC_DEV_SERVER_URL=http://localhost:3000
NEXT_PUBLIC_AGENT_SERVER_URL=http://localhost:54367
```

### 3. Production Environment Configuration

#### Production Variables
```bash
# Production Environment
NODE_ENV=production
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_DEBUG_MODE=false

# Production URLs
NEXT_PUBLIC_BASE_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://your-domain.com/api

# Performance Optimizations
NEXT_PUBLIC_ENABLE_COMPRESSION=true
NEXT_PUBLIC_ENABLE_CACHING=true
NEXT_PUBLIC_ENABLE_CDN=true

# Security Configuration
NEXT_PUBLIC_SECURITY_HEADERS=true
NEXT_PUBLIC_CSP_ENABLED=true
NEXT_PUBLIC_HTTPS_ONLY=true

# Monitoring Configuration
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
NEXT_PUBLIC_ERROR_REPORTING_DSN=your-sentry-dsn
```

## Configuration Management

### 1. Configuration Schema

#### Configuration Interface
```typescript
// Location: /packages/shared/src/config/types.ts
interface ApplicationConfig {
  // Environment
  environment: 'development' | 'production' | 'testing';
  
  // API Configuration
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  
  // Database Configuration
  database: {
    url: string;
    poolSize: number;
    connectionTimeout: number;
    queryTimeout: number;
  };
  
  // Authentication Configuration
  auth: {
    jwtSecret: string;
    sessionTimeout: number;
    tokenRefreshInterval: number;
    enableMFA: boolean;
  };
  
  // LLM Configuration
  llm: {
    defaultProvider: string;
    fallbackProviders: string[];
    maxTokens: number;
    temperature: number;
    requestTimeout: number;
  };
  
  // Feature Flags
  features: {
    webSearch: boolean;
    customActions: boolean;
    memorySystem: boolean;
    audioProcessing: boolean;
    collaborativeEditing: boolean;
  };
  
  // Performance Configuration
  performance: {
    maxConcurrentRequests: number;
    cacheSize: number;
    cacheTTL: number;
    enableCompression: boolean;
  };
  
  // Security Configuration
  security: {
    enableCSP: boolean;
    enableHSTS: boolean;
    enableCORS: boolean;
    allowedOrigins: string[];
  };
  
  // Logging Configuration
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    enableConsole: boolean;
    enableFile: boolean;
    enableRemote: boolean;
  };
}
```

### 2. Configuration Loader

#### Configuration Manager
```typescript
// Location: /packages/shared/src/config/manager.ts
import { z } from 'zod';

const ConfigSchema = z.object({
  environment: z.enum(['development', 'production', 'testing']),
  api: z.object({
    baseUrl: z.string().url(),
    timeout: z.number().positive(),
    retryAttempts: z.number().min(0),
    retryDelay: z.number().positive(),
  }),
  database: z.object({
    url: z.string(),
    poolSize: z.number().positive(),
    connectionTimeout: z.number().positive(),
    queryTimeout: z.number().positive(),
  }),
  auth: z.object({
    jwtSecret: z.string().min(32),
    sessionTimeout: z.number().positive(),
    tokenRefreshInterval: z.number().positive(),
    enableMFA: z.boolean(),
  }),
  llm: z.object({
    defaultProvider: z.string(),
    fallbackProviders: z.array(z.string()),
    maxTokens: z.number().positive(),
    temperature: z.number().min(0).max(2),
    requestTimeout: z.number().positive(),
  }),
  features: z.object({
    webSearch: z.boolean(),
    customActions: z.boolean(),
    memorySystem: z.boolean(),
    audioProcessing: z.boolean(),
    collaborativeEditing: z.boolean(),
  }),
  performance: z.object({
    maxConcurrentRequests: z.number().positive(),
    cacheSize: z.number().positive(),
    cacheTTL: z.number().positive(),
    enableCompression: z.boolean(),
  }),
  security: z.object({
    enableCSP: z.boolean(),
    enableHSTS: z.boolean(),
    enableCORS: z.boolean(),
    allowedOrigins: z.array(z.string()),
  }),
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']),
    enableConsole: z.boolean(),
    enableFile: z.boolean(),
    enableRemote: z.boolean(),
  }),
});

export class ConfigurationManager {
  private config: ApplicationConfig | null = null;
  private watchers: Map<string, (value: any) => void> = new Map();

  async load(): Promise<ApplicationConfig> {
    const rawConfig = this.loadFromEnvironment();
    
    try {
      this.config = ConfigSchema.parse(rawConfig);
      return this.config;
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Configuration validation failed: ${error.errors.map(e => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  private loadFromEnvironment(): Partial<ApplicationConfig> {
    return {
      environment: (process.env.NODE_ENV as any) || 'development',
      api: {
        baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
        timeout: parseInt(process.env.NEXT_PUBLIC_REQUEST_TIMEOUT || '30000'),
        retryAttempts: parseInt(process.env.API_RETRY_ATTEMPTS || '3'),
        retryDelay: parseInt(process.env.API_RETRY_DELAY || '1000'),
      },
      database: {
        url: process.env.DATABASE_URL || '',
        poolSize: parseInt(process.env.DB_POOL_SIZE || '10'),
        connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
        queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'),
      },
      auth: {
        jwtSecret: process.env.NEXTAUTH_SECRET || '',
        sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'), // 24 hours
        tokenRefreshInterval: parseInt(process.env.TOKEN_REFRESH_INTERVAL || '3600000'), // 1 hour
        enableMFA: process.env.ENABLE_MFA === 'true',
      },
      llm: {
        defaultProvider: process.env.DEFAULT_LLM_PROVIDER || 'openai',
        fallbackProviders: (process.env.FALLBACK_LLM_PROVIDERS || 'anthropic,google').split(','),
        maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '2000'),
        temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
        requestTimeout: parseInt(process.env.LLM_REQUEST_TIMEOUT || '60000'),
      },
      features: {
        webSearch: process.env.NEXT_PUBLIC_WEB_SEARCH_ENABLED === 'true',
        customActions: process.env.NEXT_PUBLIC_CUSTOM_ACTIONS_ENABLED === 'true',
        memorySystem: process.env.NEXT_PUBLIC_MEMORY_ENABLED === 'true',
        audioProcessing: process.env.NEXT_PUBLIC_AUDIO_PROCESSING_ENABLED === 'true',
        collaborativeEditing: process.env.NEXT_PUBLIC_COLLABORATIVE_EDITING_ENABLED === 'true',
      },
      performance: {
        maxConcurrentRequests: parseInt(process.env.NEXT_PUBLIC_MAX_CONCURRENT_REQUESTS || '5'),
        cacheSize: parseInt(process.env.CACHE_SIZE || '100'),
        cacheTTL: parseInt(process.env.NEXT_PUBLIC_CACHE_TTL || '300000'),
        enableCompression: process.env.NEXT_PUBLIC_ENABLE_COMPRESSION === 'true',
      },
      security: {
        enableCSP: process.env.NEXT_PUBLIC_CSP_ENABLED === 'true',
        enableHSTS: process.env.ENABLE_HSTS === 'true',
        enableCORS: process.env.ENABLE_CORS === 'true',
        allowedOrigins: (process.env.ALLOWED_ORIGINS || '').split(',').filter(Boolean),
      },
      logging: {
        level: (process.env.LOG_LEVEL as any) || 'info',
        enableConsole: process.env.ENABLE_CONSOLE_LOGGING !== 'false',
        enableFile: process.env.ENABLE_FILE_LOGGING === 'true',
        enableRemote: process.env.ENABLE_REMOTE_LOGGING === 'true',
      },
    };
  }

  get<K extends keyof ApplicationConfig>(key: K): ApplicationConfig[K] {
    if (!this.config) {
      throw new Error('Configuration not loaded. Call load() first.');
    }
    return this.config[key];
  }

  watch<K extends keyof ApplicationConfig>(key: K, callback: (value: ApplicationConfig[K]) => void): () => void {
    const watcherId = `${key}_${Date.now()}`;
    this.watchers.set(watcherId, callback);
    
    return () => {
      this.watchers.delete(watcherId);
    };
  }

  async reload(): Promise<void> {
    const newConfig = await this.load();
    
    // Notify watchers of changes
    for (const [watcherId, callback] of this.watchers) {
      const [key] = watcherId.split('_');
      callback(newConfig[key as keyof ApplicationConfig]);
    }
  }

  validate(): boolean {
    try {
      if (!this.config) return false;
      ConfigSchema.parse(this.config);
      return true;
    } catch {
      return false;
    }
  }
}

// Singleton instance
export const configManager = new ConfigurationManager();
```

### 3. Runtime Configuration

#### Dynamic Configuration
```typescript
// Location: /packages/shared/src/config/runtime.ts
interface RuntimeConfig {
  userPreferences: UserPreferences;
  featureFlags: FeatureFlags;
  experimentalFeatures: ExperimentalFeatures;
  performanceSettings: PerformanceSettings;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  defaultModel: string;
  customActions: CustomQuickAction[];
}

interface FeatureFlags {
  enableBetaFeatures: boolean;
  enableExperimentalUI: boolean;
  enableAdvancedMemory: boolean;
  enableCollaboration: boolean;
}

interface ExperimentalFeatures {
  reactRendering: boolean;
  voiceInterface: boolean;
  realTimeCollaboration: boolean;
  advancedSearch: boolean;
}

interface PerformanceSettings {
  enableCaching: boolean;
  maxCacheSize: number;
  prefetchArtifacts: boolean;
  enableLazyLoading: boolean;
}

export class RuntimeConfigManager {
  private config: RuntimeConfig | null = null;
  private subscribers: Map<string, (config: RuntimeConfig) => void> = new Map();

  async initialize(userId: string): Promise<RuntimeConfig> {
    const userPreferences = await this.loadUserPreferences(userId);
    const featureFlags = await this.loadFeatureFlags(userId);
    const experimentalFeatures = await this.loadExperimentalFeatures(userId);
    const performanceSettings = await this.loadPerformanceSettings(userId);

    this.config = {
      userPreferences,
      featureFlags,
      experimentalFeatures,
      performanceSettings,
    };

    this.notifySubscribers();
    return this.config;
  }

  private async loadUserPreferences(userId: string): Promise<UserPreferences> {
    try {
      const { data } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      return {
        theme: data?.theme || 'system',
        language: data?.language || 'en',
        timezone: data?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
        defaultModel: data?.default_model || 'gpt-4o-mini',
        customActions: data?.custom_actions || [],
      };
    } catch (error) {
      console.error('Failed to load user preferences:', error);
      return this.getDefaultUserPreferences();
    }
  }

  private async loadFeatureFlags(userId: string): Promise<FeatureFlags> {
    try {
      const { data } = await supabase
        .from('feature_flags')
        .select('*')
        .eq('user_id', userId)
        .single();

      return {
        enableBetaFeatures: data?.enable_beta_features || false,
        enableExperimentalUI: data?.enable_experimental_ui || false,
        enableAdvancedMemory: data?.enable_advanced_memory || false,
        enableCollaboration: data?.enable_collaboration || false,
      };
    } catch (error) {
      console.error('Failed to load feature flags:', error);
      return this.getDefaultFeatureFlags();
    }
  }

  private async loadExperimentalFeatures(userId: string): Promise<ExperimentalFeatures> {
    try {
      const { data } = await supabase
        .from('experimental_features')
        .select('*')
        .eq('user_id', userId)
        .single();

      return {
        reactRendering: data?.react_rendering || false,
        voiceInterface: data?.voice_interface || false,
        realTimeCollaboration: data?.real_time_collaboration || false,
        advancedSearch: data?.advanced_search || false,
      };
    } catch (error) {
      console.error('Failed to load experimental features:', error);
      return this.getDefaultExperimentalFeatures();
    }
  }

  private async loadPerformanceSettings(userId: string): Promise<PerformanceSettings> {
    try {
      const { data } = await supabase
        .from('performance_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      return {
        enableCaching: data?.enable_caching !== false,
        maxCacheSize: data?.max_cache_size || 100,
        prefetchArtifacts: data?.prefetch_artifacts || false,
        enableLazyLoading: data?.enable_lazy_loading !== false,
      };
    } catch (error) {
      console.error('Failed to load performance settings:', error);
      return this.getDefaultPerformanceSettings();
    }
  }

  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void> {
    try {
      await supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          ...preferences,
          updated_at: new Date().toISOString(),
        });

      if (this.config) {
        this.config.userPreferences = {
          ...this.config.userPreferences,
          ...preferences,
        };
        this.notifySubscribers();
      }
    } catch (error) {
      console.error('Failed to update user preferences:', error);
      throw error;
    }
  }

  async updateFeatureFlags(userId: string, flags: Partial<FeatureFlags>): Promise<void> {
    try {
      await supabase
        .from('feature_flags')
        .upsert({
          user_id: userId,
          ...flags,
          updated_at: new Date().toISOString(),
        });

      if (this.config) {
        this.config.featureFlags = {
          ...this.config.featureFlags,
          ...flags,
        };
        this.notifySubscribers();
      }
    } catch (error) {
      console.error('Failed to update feature flags:', error);
      throw error;
    }
  }

  subscribe(callback: (config: RuntimeConfig) => void): () => void {
    const id = `subscriber_${Date.now()}_${Math.random()}`;
    this.subscribers.set(id, callback);
    
    return () => {
      this.subscribers.delete(id);
    };
  }

  private notifySubscribers(): void {
    if (this.config) {
      for (const callback of this.subscribers.values()) {
        callback(this.config);
      }
    }
  }

  get(): RuntimeConfig | null {
    return this.config;
  }

  private getDefaultUserPreferences(): UserPreferences {
    return {
      theme: 'system',
      language: 'en',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      defaultModel: 'gpt-4o-mini',
      customActions: [],
    };
  }

  private getDefaultFeatureFlags(): FeatureFlags {
    return {
      enableBetaFeatures: false,
      enableExperimentalUI: false,
      enableAdvancedMemory: false,
      enableCollaboration: false,
    };
  }

  private getDefaultExperimentalFeatures(): ExperimentalFeatures {
    return {
      reactRendering: false,
      voiceInterface: false,
      realTimeCollaboration: false,
      advancedSearch: false,
    };
  }

  private getDefaultPerformanceSettings(): PerformanceSettings {
    return {
      enableCaching: true,
      maxCacheSize: 100,
      prefetchArtifacts: false,
      enableLazyLoading: true,
    };
  }
}
```

## Provider-Specific Configuration

### 1. LLM Provider Configuration

#### Provider Config Factory
```typescript
// Location: /packages/shared/src/config/providers.ts
export interface ProviderConfig {
  name: string;
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  maxRetries?: number;
  rateLimit?: {
    requests: number;
    window: number;
  };
  features?: {
    streaming: boolean;
    functionCalling: boolean;
    imageInput: boolean;
    audioInput: boolean;
  };
}

export class ProviderConfigManager {
  private configs: Map<string, ProviderConfig> = new Map();

  constructor() {
    this.loadProviderConfigs();
  }

  private loadProviderConfigs(): void {
    // OpenAI Configuration
    if (process.env.OPENAI_API_KEY) {
      this.configs.set('openai', {
        name: 'OpenAI',
        apiKey: process.env.OPENAI_API_KEY,
        baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
        timeout: parseInt(process.env.OPENAI_TIMEOUT || '60000'),
        maxRetries: parseInt(process.env.OPENAI_MAX_RETRIES || '3'),
        rateLimit: {
          requests: parseInt(process.env.OPENAI_RATE_LIMIT_REQUESTS || '3000'),
          window: parseInt(process.env.OPENAI_RATE_LIMIT_WINDOW || '60000'),
        },
        features: {
          streaming: true,
          functionCalling: true,
          imageInput: true,
          audioInput: false,
        },
      });
    }

    // Anthropic Configuration
    if (process.env.ANTHROPIC_API_KEY) {
      this.configs.set('anthropic', {
        name: 'Anthropic',
        apiKey: process.env.ANTHROPIC_API_KEY,
        baseUrl: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com',
        timeout: parseInt(process.env.ANTHROPIC_TIMEOUT || '60000'),
        maxRetries: parseInt(process.env.ANTHROPIC_MAX_RETRIES || '3'),
        rateLimit: {
          requests: parseInt(process.env.ANTHROPIC_RATE_LIMIT_REQUESTS || '1000'),
          window: parseInt(process.env.ANTHROPIC_RATE_LIMIT_WINDOW || '60000'),
        },
        features: {
          streaming: true,
          functionCalling: true,
          imageInput: true,
          audioInput: false,
        },
      });
    }

    // Google Configuration
    if (process.env.GOOGLE_GENAI_API_KEY) {
      this.configs.set('google', {
        name: 'Google',
        apiKey: process.env.GOOGLE_GENAI_API_KEY,
        baseUrl: process.env.GOOGLE_BASE_URL || 'https://generativelanguage.googleapis.com',
        timeout: parseInt(process.env.GOOGLE_TIMEOUT || '60000'),
        maxRetries: parseInt(process.env.GOOGLE_MAX_RETRIES || '3'),
        rateLimit: {
          requests: parseInt(process.env.GOOGLE_RATE_LIMIT_REQUESTS || '1500'),
          window: parseInt(process.env.GOOGLE_RATE_LIMIT_WINDOW || '60000'),
        },
        features: {
          streaming: true,
          functionCalling: true,
          imageInput: true,
          audioInput: false,
        },
      });
    }

    // Ollama Configuration
    if (process.env.OLLAMA_API_URL) {
      this.configs.set('ollama', {
        name: 'Ollama',
        apiKey: '', // No API key needed for Ollama
        baseUrl: process.env.OLLAMA_API_URL,
        timeout: parseInt(process.env.OLLAMA_TIMEOUT || '120000'),
        maxRetries: parseInt(process.env.OLLAMA_MAX_RETRIES || '2'),
        rateLimit: {
          requests: parseInt(process.env.OLLAMA_RATE_LIMIT_REQUESTS || '100'),
          window: parseInt(process.env.OLLAMA_RATE_LIMIT_WINDOW || '60000'),
        },
        features: {
          streaming: true,
          functionCalling: false,
          imageInput: false,
          audioInput: false,
        },
      });
    }
  }

  getConfig(provider: string): ProviderConfig | undefined {
    return this.configs.get(provider);
  }

  getAllConfigs(): Map<string, ProviderConfig> {
    return new Map(this.configs);
  }

  isProviderAvailable(provider: string): boolean {
    return this.configs.has(provider);
  }

  getAvailableProviders(): string[] {
    return Array.from(this.configs.keys());
  }

  validateConfig(provider: string): boolean {
    const config = this.configs.get(provider);
    if (!config) return false;

    // Validate required fields
    if (!config.name || !config.baseUrl) return false;

    // Validate API key for providers that require it
    if (provider !== 'ollama' && !config.apiKey) return false;

    return true;
  }
}
```

### 2. Database Configuration

#### Database Config Manager
```typescript
// Location: /packages/shared/src/config/database.ts
export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
  poolSize: number;
  connectionTimeout: number;
  queryTimeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export class DatabaseConfigManager {
  private config: DatabaseConfig;

  constructor() {
    this.config = this.loadDatabaseConfig();
  }

  private loadDatabaseConfig(): DatabaseConfig {
    const databaseUrl = process.env.DATABASE_URL;
    
    if (databaseUrl) {
      return this.parseConnectionString(databaseUrl);
    }

    return {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'opencanvas',
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || '',
      ssl: process.env.DB_SSL === 'true',
      poolSize: parseInt(process.env.DB_POOL_SIZE || '10'),
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
      queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.DB_RETRY_DELAY || '1000'),
    };
  }

  private parseConnectionString(connectionString: string): DatabaseConfig {
    const url = new URL(connectionString);
    
    return {
      host: url.hostname,
      port: parseInt(url.port) || 5432,
      database: url.pathname.slice(1),
      username: url.username,
      password: url.password,
      ssl: url.searchParams.get('sslmode') === 'require',
      poolSize: parseInt(process.env.DB_POOL_SIZE || '10'),
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
      queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.DB_RETRY_DELAY || '1000'),
    };
  }

  getConfig(): DatabaseConfig {
    return { ...this.config };
  }

  getConnectionString(): string {
    const { host, port, database, username, password, ssl } = this.config;
    const sslParam = ssl ? '?sslmode=require' : '';
    
    return `postgresql://${username}:${password}@${host}:${port}/${database}${sslParam}`;
  }

  validate(): boolean {
    const { host, port, database, username, password } = this.config;
    
    return !!(
      host &&
      port > 0 &&
      port < 65536 &&
      database &&
      username &&
      password
    );
  }
}
```

## Development vs Production Configuration

### 1. Environment-Specific Overrides

#### Development Configuration
```typescript
// Location: /packages/shared/src/config/development.ts
export const developmentConfig: Partial<ApplicationConfig> = {
  api: {
    baseUrl: 'http://localhost:3000/api',
    timeout: 30000,
    retryAttempts: 1,
    retryDelay: 1000,
  },
  
  logging: {
    level: 'debug',
    enableConsole: true,
    enableFile: false,
    enableRemote: false,
  },
  
  performance: {
    maxConcurrentRequests: 10,
    cacheSize: 50,
    cacheTTL: 60000, // 1 minute
    enableCompression: false,
  },
  
  security: {
    enableCSP: false,
    enableHSTS: false,
    enableCORS: true,
    allowedOrigins: ['http://localhost:3000', 'http://localhost:54367'],
  },
  
  features: {
    webSearch: true,
    customActions: true,
    memorySystem: true,
    audioProcessing: true,
    collaborativeEditing: false,
  },
};
```

#### Production Configuration
```typescript
// Location: /packages/shared/src/config/production.ts
export const productionConfig: Partial<ApplicationConfig> = {
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL!,
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 2000,
  },
  
  logging: {
    level: 'info',
    enableConsole: false,
    enableFile: true,
    enableRemote: true,
  },
  
  performance: {
    maxConcurrentRequests: 5,
    cacheSize: 200,
    cacheTTL: 300000, // 5 minutes
    enableCompression: true,
  },
  
  security: {
    enableCSP: true,
    enableHSTS: true,
    enableCORS: true,
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [],
  },
  
  features: {
    webSearch: process.env.NEXT_PUBLIC_WEB_SEARCH_ENABLED === 'true',
    customActions: process.env.NEXT_PUBLIC_CUSTOM_ACTIONS_ENABLED === 'true',
    memorySystem: process.env.NEXT_PUBLIC_MEMORY_ENABLED === 'true',
    audioProcessing: process.env.NEXT_PUBLIC_AUDIO_PROCESSING_ENABLED === 'true',
    collaborativeEditing: process.env.NEXT_PUBLIC_COLLABORATIVE_EDITING_ENABLED === 'true',
  },
};
```

### 2. Configuration Validation

#### Validation Manager
```typescript
// Location: /packages/shared/src/config/validation.ts
export class ConfigurationValidator {
  private requiredEnvVars: string[] = [
    'NEXTAUTH_SECRET',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'LANGGRAPH_API_URL',
  ];

  private optionalEnvVars: string[] = [
    'OPENAI_API_KEY',
    'ANTHROPIC_API_KEY',
    'GOOGLE_GENAI_API_KEY',
    'FIREWORKS_API_KEY',
    'GROQ_API_KEY',
    'FIRECRAWL_API_KEY',
    'EXA_API_KEY',
  ];

  validateEnvironment(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required environment variables
    for (const envVar of this.requiredEnvVars) {
      if (!process.env[envVar]) {
        errors.push(`Missing required environment variable: ${envVar}`);
      }
    }

    // Check optional environment variables
    for (const envVar of this.optionalEnvVars) {
      if (!process.env[envVar]) {
        warnings.push(`Missing optional environment variable: ${envVar}`);
      }
    }

    // Validate specific configurations
    this.validateUrlFormats(errors);
    this.validateApiKeys(errors);
    this.validateNumericValues(errors);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private validateUrlFormats(errors: string[]): void {
    const urlVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'LANGGRAPH_API_URL',
      'OLLAMA_API_URL',
      'NEXT_PUBLIC_API_URL',
    ];

    for (const envVar of urlVars) {
      const value = process.env[envVar];
      if (value) {
        try {
          new URL(value);
        } catch {
          errors.push(`Invalid URL format for ${envVar}: ${value}`);
        }
      }
    }
  }

  private validateApiKeys(errors: string[]): void {
    const apiKeyVars = [
      'OPENAI_API_KEY',
      'ANTHROPIC_API_KEY',
      'GOOGLE_GENAI_API_KEY',
      'FIREWORKS_API_KEY',
      'GROQ_API_KEY',
    ];

    for (const envVar of apiKeyVars) {
      const value = process.env[envVar];
      if (value && value.length < 10) {
        errors.push(`API key too short for ${envVar}`);
      }
    }
  }

  private validateNumericValues(errors: string[]): void {
    const numericVars = [
      'NEXT_PUBLIC_MAX_CONCURRENT_REQUESTS',
      'NEXT_PUBLIC_REQUEST_TIMEOUT',
      'NEXT_PUBLIC_CACHE_TTL',
      'NEXT_PUBLIC_MAX_FILE_SIZE',
    ];

    for (const envVar of numericVars) {
      const value = process.env[envVar];
      if (value && isNaN(parseInt(value))) {
        errors.push(`Invalid numeric value for ${envVar}: ${value}`);
      }
    }
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
```

## Configuration Best Practices

### 1. Security Considerations

```typescript
// Secure configuration handling
export class SecureConfigManager {
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = process.env.ENCRYPTION_KEY || this.generateEncryptionKey();
  }

  encryptSensitiveValue(value: string): string {
    // Implement encryption logic
    return encrypt(value, this.encryptionKey);
  }

  decryptSensitiveValue(encryptedValue: string): string {
    // Implement decryption logic
    return decrypt(encryptedValue, this.encryptionKey);
  }

  private generateEncryptionKey(): string {
    // Generate a secure key if not provided
    return crypto.randomBytes(32).toString('hex');
  }

  maskSensitiveConfig(config: any): any {
    const masked = { ...config };
    const sensitiveKeys = ['apiKey', 'password', 'secret', 'token'];
    
    for (const key of sensitiveKeys) {
      if (masked[key]) {
        masked[key] = '***MASKED***';
      }
    }
    
    return masked;
  }
}
```

### 2. Configuration Monitoring

```typescript
// Configuration monitoring and alerting
export class ConfigurationMonitor {
  private lastCheck: Date = new Date();
  private healthStatus: Map<string, boolean> = new Map();

  async performHealthCheck(): Promise<HealthCheckResult> {
    const results: HealthCheckResult = {
      timestamp: new Date(),
      overall: true,
      checks: [],
    };

    // Check API connectivity
    for (const provider of ['openai', 'anthropic', 'google']) {
      const isHealthy = await this.checkProviderHealth(provider);
      this.healthStatus.set(provider, isHealthy);
      
      results.checks.push({
        name: `${provider}_api`,
        status: isHealthy ? 'healthy' : 'unhealthy',
        message: isHealthy ? 'API accessible' : 'API not accessible',
      });
      
      if (!isHealthy) {
        results.overall = false;
      }
    }

    // Check database connectivity
    const dbHealthy = await this.checkDatabaseHealth();
    results.checks.push({
      name: 'database',
      status: dbHealthy ? 'healthy' : 'unhealthy',
      message: dbHealthy ? 'Database accessible' : 'Database not accessible',
    });

    if (!dbHealthy) {
      results.overall = false;
    }

    return results;
  }

  private async checkProviderHealth(provider: string): Promise<boolean> {
    // Implement provider-specific health checks
    return true;
  }

  private async checkDatabaseHealth(): Promise<boolean> {
    // Implement database health check
    return true;
  }
}
```

This comprehensive configuration system provides Open Canvas with flexible, secure, and maintainable configuration management across all environments and deployment scenarios.