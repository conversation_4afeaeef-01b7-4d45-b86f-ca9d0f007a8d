# Open Canvas - Features Documentation

## Core Features Overview

Open Canvas provides a comprehensive set of features designed to enhance collaborative document creation between humans and AI. Each feature is designed with user experience and productivity in mind.

## 1. Memory System

### Persistent Memory Architecture
The memory system is the cornerstone of Open Canvas's personalization capabilities, enabling the AI to learn and adapt to user preferences over time.

#### Reflection Agent
- **Location**: `/apps/agents/src/reflection/`
- **Purpose**: Automatically analyzes conversations to extract insights
- **Capabilities**:
  - Style rule extraction from user interactions
  - Content preference identification
  - Writing pattern recognition
  - Collaboration style analysis

#### Memory Components

##### Style Rules
```typescript
interface StyleRules {
  writingStyle: string[];      // Preferred writing approaches
  tone: string[];              // Preferred communication tone
  formatting: string[];        // Preferred document formatting
  vocabulary: string[];        // Preferred terminology usage
}
```

##### User Insights
```typescript
interface UserInsights {
  preferences: string[];       // General user preferences
  expertise: string[];         // Areas of subject matter expertise
  goals: string[];            // User's stated objectives
  context: string[];          // Personal or professional context
}
```

#### Memory Persistence
- **Storage**: Shared memory store accessible across sessions
- **Retrieval**: Automatic integration into conversation context
- **Updates**: Real-time updates based on user interactions
- **Privacy**: User-controlled data with deletion capabilities

#### Memory Integration Workflow
1. **Conversation Analysis**: Reflection agent processes completed conversations
2. **Pattern Recognition**: Identifies recurring themes and preferences
3. **Rule Extraction**: Generates style rules and user insights
4. **Memory Storage**: Persists insights in shared memory store
5. **Context Integration**: Includes relevant memories in future conversations

### Benefits
- **Personalized Experience**: AI adapts to individual user preferences
- **Consistency**: Maintains consistent style across sessions
- **Efficiency**: Reduces need to repeat preferences
- **Learning**: Continuously improves based on user feedback

## 2. Quick Actions System

### Pre-built Quick Actions

#### Text Actions (`/src/components/artifacts/actions_toolbar/text/`)

##### Length Modification
- **Shortest**: Extremely concise content
- **Short**: Brief, focused content
- **Long**: Detailed, comprehensive content
- **Longest**: Exhaustive, thorough content

##### Reading Level Adjustment
- **Pirate**: Humorous, casual tone
- **Child**: Simple language and concepts
- **Teenager**: Accessible but more sophisticated
- **College**: Academic level complexity
- **PhD**: Advanced, technical language

##### Translation Options
- **English**: Default language
- **Mandarin**: Chinese language support
- **Spanish**: Spanish language support
- **French**: French language support
- **Hindi**: Hindi language support

##### Style Transformations
- **Emoji Integration**: Add emojis for engagement
- **Tone Adjustment**: Modify communication tone
- **Format Changes**: Restructure content organization
- **Clarity Enhancement**: Improve readability

#### Code Actions (`/src/components/artifacts/actions_toolbar/code/`)

##### Language Porting
```typescript
interface LanguagePortingOptions {
  sourceLanguage: ProgrammingLanguageOptions;
  targetLanguage: ProgrammingLanguageOptions;
  preserveComments: boolean;
  optimizeForTarget: boolean;
}
```

Supported languages:
- **JavaScript/TypeScript**: Modern web development
- **Python**: Data science and backend development
- **Java**: Enterprise applications
- **C++**: System programming
- **C#**: .NET development
- **Rust**: Systems programming
- **PHP**: Web development
- **HTML/CSS**: Web markup and styling
- **SQL**: Database queries
- **JSON**: Data interchange
- **XML**: Structured documents
- **Clojure**: Functional programming

##### Code Enhancement
- **Add Comments**: Inline documentation
- **Add Logging**: Debug and monitoring statements
- **Bug Fixes**: Identify and resolve issues
- **Optimization**: Performance improvements
- **Refactoring**: Code structure improvements

### Custom Quick Actions

#### Action Creation Interface
- **Location**: `/src/components/artifacts/actions_toolbar/custom/`
- **Purpose**: User-defined actions for specific use cases

#### Custom Action Properties
```typescript
interface CustomQuickAction {
  id: string;                    // Unique identifier
  title: string;                 // Display name
  prompt: string;                // AI instruction
  includeReflections: boolean;   // Include user memory
  includePrefix: boolean;        // Include system prefix
  includeRecentHistory: boolean; // Include recent messages
}
```

#### Action Configuration Options
- **Reflection Integration**: Include user's style rules and insights
- **History Context**: Include recent conversation history
- **Prefix Options**: Control system message inclusion
- **Custom Prompts**: Tailored AI instructions

#### Action Management
- **Create**: Define new custom actions
- **Edit**: Modify existing actions
- **Delete**: Remove unwanted actions
- **Share**: Export actions for team use
- **Import**: Load actions from others

### Action Execution Flow
1. **User Selection**: Choose quick action from toolbar
2. **Context Assembly**: Gather relevant context and parameters
3. **Agent Processing**: Send request to appropriate agent node
4. **AI Processing**: Execute action using selected model
5. **Response Integration**: Apply changes to artifact
6. **Memory Update**: Update user memory with action results

## 3. Artifact Versioning

### Version Management System
Every artifact maintains a complete history of changes, enabling users to track evolution and revert to previous versions.

#### Version Structure
```typescript
interface ArtifactVersion {
  index: number;                 // Version number
  timestamp: Date;               // Creation time
  content: string;               // Version content
  title: string;                 // Version title
  type: 'text' | 'code';        // Content type
  language?: string;             // Programming language
  changes: ChangeRecord[];       // Change summary
  author: 'user' | 'ai';        // Change source
}
```

#### Version Navigation
- **Previous/Next**: Navigate through version history
- **Version List**: View all versions with metadata
- **Diff View**: Compare versions side-by-side
- **Quick Restore**: One-click version restoration

#### Change Tracking
- **Automatic Versioning**: Every modification creates new version
- **Change Annotation**: Detailed change descriptions
- **Merge Capabilities**: Combine changes from different versions
- **Conflict Resolution**: Handle conflicting modifications

### Version Control Features
- **Branching**: Create alternate version paths
- **Merging**: Combine changes from different branches
- **Tagging**: Mark significant versions
- **Export**: Save versions to external files
- **Sharing**: Share specific versions with others

## 4. Multi-Format Support

### Content Type Flexibility
Open Canvas seamlessly handles multiple content formats within the same interface.

#### Text Artifacts
- **Markdown Support**: Full markdown syntax support
- **Live Preview**: Real-time rendered preview
- **Editing Mode**: Direct markdown editing
- **Rich Text**: Formatted text display
- **LaTeX Math**: Mathematical notation support

#### Code Artifacts
- **Syntax Highlighting**: Language-specific highlighting
- **Line Numbers**: Optional line numbering
- **Code Folding**: Collapsible code sections
- **Error Detection**: Real-time error highlighting
- **Auto-completion**: Intelligent code suggestions

#### Mixed Content
- **Format Switching**: Easy transitions between formats
- **Content Preservation**: Maintain content during format changes
- **Embedded Code**: Code blocks within text documents
- **Documentation**: Code with explanatory text

### Format-Specific Features

#### Markdown Enhancements
- **Table Support**: Rich table editing
- **Link Management**: URL validation and preview
- **Image Embedding**: Inline image support
- **Code Blocks**: Syntax-highlighted code snippets
- **Task Lists**: Interactive checkbox lists

#### Code Editor Features
- **Multi-cursor**: Edit multiple locations simultaneously
- **Find/Replace**: Advanced search and replace
- **Bracket Matching**: Automatic bracket completion
- **Indentation**: Smart indentation handling
- **Minimap**: Code overview navigation

## 5. Live Editing Experience

### Real-time Collaboration
Open Canvas provides a fluid editing experience with immediate feedback and updates.

#### Streaming Updates
- **Real-time Processing**: Live updates during AI processing
- **Progress Indicators**: Visual feedback for long operations
- **Cancellation**: Ability to stop ongoing operations
- **Resumption**: Resume interrupted operations

#### Concurrent Editing
- **Optimistic Updates**: Immediate UI feedback
- **Conflict Resolution**: Handle simultaneous changes
- **State Synchronization**: Maintain consistent state
- **Error Recovery**: Graceful handling of failures

### Interactive Features
- **Highlight Selection**: Select text/code for targeted edits
- **Contextual Menus**: Right-click context actions
- **Keyboard Shortcuts**: Efficient navigation and editing
- **Drag-and-Drop**: File and content manipulation

## 6. Web Search Integration

### Enhanced Context Gathering
Open Canvas can integrate web search results to provide more informed and current responses.

#### Search Capabilities
- **Real-time Search**: Current web information
- **Source Verification**: Credible source identification
- **Content Summarization**: Relevant information extraction
- **Citation Support**: Proper source attribution

#### Search Providers
- **Exa AI**: Semantic search capabilities
- **FireCrawl**: Web scraping and content extraction
- **Custom APIs**: Integration with additional search services

#### Search Integration Workflow
1. **Query Analysis**: Determine when search is beneficial
2. **Search Execution**: Perform relevant web searches
3. **Content Processing**: Extract and summarize findings
4. **Context Integration**: Incorporate results into responses
5. **Source Attribution**: Maintain proper citations

## 7. Audio/Video Processing

### Multimedia Support
Open Canvas supports various multimedia inputs to enhance the collaborative experience.

#### Audio Features
- **Speech-to-Text**: Convert voice input to text
- **Multiple Languages**: Support for various languages
- **Real-time Processing**: Live transcription
- **Audio File Upload**: Process audio files

#### Video Features
- **Video Transcription**: Extract text from video content
- **Frame Analysis**: Visual content understanding
- **Format Support**: Multiple video formats
- **Batch Processing**: Process multiple files

### Processing Capabilities
- **ffmpeg Integration**: Comprehensive media processing
- **Format Conversion**: Convert between media formats
- **Quality Optimization**: Optimize media quality
- **Metadata Extraction**: Extract file information

## 8. Context Documents

### Document Integration
Users can upload and reference documents to provide context for AI interactions.

#### Supported Formats
- **PDF**: Document processing and text extraction
- **Text Files**: Plain text document support
- **Images**: Image analysis and description
- **Audio**: Audio transcription and analysis
- **Video**: Video content extraction

#### Document Management
- **Upload Interface**: Drag-and-drop document upload
- **Document Library**: Organized document storage
- **Search**: Find relevant documents
- **Metadata**: Document information tracking

### Context Integration
- **Automatic Relevance**: AI determines document relevance
- **Selective Inclusion**: Choose which documents to include
- **Content Summarization**: Extract key information
- **Citation Support**: Reference document sources

## 9. Model Selection and Configuration

### Multi-Provider Support
Open Canvas supports multiple AI providers, allowing users to choose the best model for their needs.

#### Model Options
- **OpenAI**: GPT-4o Mini for general tasks
- **Anthropic**: Claude 3 Haiku for fast processing
- **Google**: Gemini for advanced reasoning
- **Fireworks**: Llama 3 70B for open-source solutions
- **Groq**: High-speed inference
- **Ollama**: Local model support

#### Model Configuration
- **Temperature**: Control randomness in responses
- **Max Tokens**: Limit response length
- **Top-p**: Nucleus sampling parameter
- **Frequency Penalty**: Reduce repetition
- **Presence Penalty**: Encourage diversity

### Performance Optimization
- **Model Selection**: Choose appropriate model for task
- **Cost Optimization**: Balance cost and quality
- **Response Speed**: Optimize for different use cases
- **Quality Control**: Maintain output quality

## 10. Collaboration Features

### Sharing and Export
Open Canvas provides various ways to share and export work.

#### Sharing Options
- **Link Sharing**: Share artifacts via URLs
- **Export Options**: Download in various formats
- **Collaboration**: Multi-user editing (planned)
- **Version Sharing**: Share specific versions

#### Export Formats
- **Markdown**: Export text as markdown
- **HTML**: Web-ready format
- **PDF**: Document format
- **Plain Text**: Simple text format
- **Code Files**: Export with appropriate extensions

### Team Features (Planned)
- **Team Workspaces**: Shared collaboration spaces
- **Permission Management**: Control access levels
- **Change Tracking**: Monitor team contributions
- **Comments**: Collaborative feedback system

## Feature Integration

### Unified Experience
All features work together seamlessly to provide a cohesive experience:

1. **Memory-Enhanced Actions**: Quick actions leverage user memory
2. **Version-Aware Editing**: All changes tracked with versions
3. **Multi-Format Consistency**: Features work across content types
4. **Search-Informed Responses**: Web search enhances AI responses
5. **Context-Aware Processing**: Documents inform AI decisions

### Workflow Example
1. User uploads context documents
2. AI analyzes documents and user memory
3. User requests content creation
4. AI generates artifact using context and memory
5. User applies quick actions for refinement
6. Changes tracked in version history
7. Final artifact exported or shared

This comprehensive feature set makes Open Canvas a powerful platform for AI-assisted content creation, providing users with the tools they need for effective collaboration with AI agents.