# Open Canvas - Deployment Documentation

## Deployment Architecture Overview

Open Canvas supports multiple deployment strategies designed for scalability, reliability, and cost-effectiveness. The monorepo architecture enables flexible deployment options from single-server setups to fully distributed cloud deployments.

```mermaid
graph TB
    subgraph "Deployment Options"
        A[Local Development] --> B[Single Server]
        B --> C[Container Deployment]
        C --> D[Cloud Deployment]
        D --> E[Distributed Deployment]
    end
    
    subgraph "Infrastructure Components"
        F[Frontend App] --> G[Load Balancer]
        H[Agent Server] --> I[Container Registry]
        J[Database] --> K[Storage]
        L[Cache] --> M[Monitoring]
    end
    
    subgraph "Deployment Targets"
        N[Vercel] --> O[AWS]
        N --> P[Google Cloud]
        N --> Q[Azure]
        N --> R[Self-Hosted]
    end
    
    A --> F
    B --> H
    C --> J
    D --> L
    E --> M
```

## Local Development Deployment

### 1. Development Environment Setup

#### Prerequisites Installation
```bash
# Install Node.js 20+
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Yarn
npm install -g yarn

# Install Docker (for optional services)
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### Environment Configuration
```bash
# Clone repository
git clone https://github.com/langchain-ai/open-canvas.git
cd open-canvas

# Install dependencies
yarn install

# Setup environment variables
cp .env.example .env
cp apps/web/.env.example apps/web/.env.local

# Build the project
yarn build
```

#### Development Server Startup
```bash
# Terminal 1: Start LangGraph agent server
cd apps/agents
yarn dev

# Terminal 2: Start Next.js web application
cd apps/web
yarn dev

# Terminal 3: Start optional services (if needed)
docker-compose up -d postgres redis
```

### 2. Local Docker Deployment

#### Docker Compose Configuration
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: opencanvas
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/opencanvas
      - REDIS_URL=redis://redis:6379
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./apps/web:/app
      - /app/node_modules
    
  agents:
    build:
      context: .
      dockerfile: apps/agents/Dockerfile
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/opencanvas
      - REDIS_URL=redis://redis:6379
    ports:
      - "54367:54367"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./apps/agents:/app
      - /app/node_modules

volumes:
  postgres_data:
  redis_data:
```

#### Docker Build Process
```bash
# Build all services
docker-compose build

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f web
docker-compose logs -f agents

# Stop services
docker-compose down
```

## Production Deployment

### 1. Vercel Deployment (Recommended)

#### Vercel Configuration
```json
{
  "name": "open-canvas",
  "version": 2,
  "builds": [
    {
      "src": "apps/web/package.json",
      "use": "@vercel/next",
      "config": {
        "buildCommand": "cd ../.. && yarn build",
        "outputDirectory": "apps/web/.next"
      }
    },
    {
      "src": "apps/agents/package.json",
      "use": "@vercel/node",
      "config": {
        "buildCommand": "cd ../.. && yarn build"
      }
    }
  ],
  "routes": [
    {
      "src": "/api/agents/(.*)",
      "dest": "apps/agents/src/index.js"
    },
    {
      "src": "/(.*)",
      "dest": "apps/web/$1"
    }
  ],
  "env": {
    "NEXTAUTH_SECRET": "@nextauth-secret",
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "OPENAI_API_KEY": "@openai-api-key",
    "ANTHROPIC_API_KEY": "@anthropic-api-key",
    "GOOGLE_GENAI_API_KEY": "@google-genai-api-key",
    "LANGGRAPH_API_URL": "@langgraph-api-url"
  },
  "regions": ["iad1", "sfo1"],
  "functions": {
    "apps/agents/src/index.js": {
      "runtime": "nodejs18.x",
      "memory": 1024,
      "timeout": 30
    }
  }
}
```

#### Vercel Deployment Commands
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy to preview
vercel

# Deploy to production
vercel --prod

# Set environment variables
vercel env add NEXTAUTH_SECRET
vercel env add NEXT_PUBLIC_SUPABASE_URL
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
vercel env add OPENAI_API_KEY
vercel env add ANTHROPIC_API_KEY
```

### 2. AWS Deployment

#### AWS Infrastructure Setup
```yaml
# infrastructure/aws/cloudformation.yml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'Open Canvas AWS Infrastructure'

Parameters:
  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]
  
  VpcCidr:
    Type: String
    Default: 10.0.0.0/16
  
  SubnetCidr1:
    Type: String
    Default: ********/24
  
  SubnetCidr2:
    Type: String
    Default: ********/24

Resources:
  # VPC and Networking
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: !Ref VpcCidr
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub ${Environment}-open-canvas-vpc

  # Subnets
  PublicSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      CidrBlock: !Ref SubnetCidr1
      AvailabilityZone: !Select [0, !GetAZs '']
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub ${Environment}-open-canvas-public-subnet-1

  PublicSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      CidrBlock: !Ref SubnetCidr2
      AvailabilityZone: !Select [1, !GetAZs '']
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub ${Environment}-open-canvas-public-subnet-2

  # Internet Gateway
  InternetGateway:
    Type: AWS::EC2::InternetGateway
    Properties:
      Tags:
        - Key: Name
          Value: !Sub ${Environment}-open-canvas-igw

  InternetGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      InternetGatewayId: !Ref InternetGateway
      VpcId: !Ref VPC

  # Route Table
  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub ${Environment}-open-canvas-public-routes

  DefaultPublicRoute:
    Type: AWS::EC2::Route
    DependsOn: InternetGatewayAttachment
    Properties:
      RouteTableId: !Ref PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub ${Environment}-open-canvas
      CapacityProviders:
        - FARGATE
        - FARGATE_SPOT
      DefaultCapacityProviderStrategy:
        - CapacityProvider: FARGATE
          Weight: 1
        - CapacityProvider: FARGATE_SPOT
          Weight: 2

  # Application Load Balancer
  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub ${Environment}-open-canvas-alb
      Scheme: internet-facing
      Type: application
      Subnets:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2
      SecurityGroups:
        - !Ref ALBSecurityGroup

  # RDS PostgreSQL
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for Open Canvas database
      SubnetIds:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2
      Tags:
        - Key: Name
          Value: !Sub ${Environment}-open-canvas-db-subnet-group

  DatabaseInstance:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: !Sub ${Environment}-open-canvas-db
      DBInstanceClass: db.t3.micro
      Engine: postgres
      EngineVersion: '15.4'
      MasterUsername: postgres
      MasterUserPassword: !Ref DatabasePassword
      AllocatedStorage: 20
      StorageType: gp2
      DBSubnetGroupName: !Ref DBSubnetGroup
      VPCSecurityGroups:
        - !Ref DatabaseSecurityGroup
      BackupRetentionPeriod: 7
      MultiAZ: false
      StorageEncrypted: true

  # ElastiCache Redis
  RedisSubnetGroup:
    Type: AWS::ElastiCache::SubnetGroup
    Properties:
      Description: Subnet group for Open Canvas Redis
      SubnetIds:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2

  RedisCluster:
    Type: AWS::ElastiCache::CacheCluster
    Properties:
      CacheClusterId: !Sub ${Environment}-open-canvas-redis
      CacheNodeType: cache.t3.micro
      Engine: redis
      NumCacheNodes: 1
      CacheSubnetGroupName: !Ref RedisSubnetGroup
      VpcSecurityGroupIds:
        - !Ref RedisSecurityGroup

Parameters:
  DatabasePassword:
    Type: String
    NoEcho: true
    Description: Password for the database
    MinLength: 8
    MaxLength: 128
    AllowedPattern: ^[a-zA-Z0-9]*$
```

#### ECS Task Definitions
```yaml
# infrastructure/aws/ecs-web-task.yml
family: open-canvas-web
networkMode: awsvpc
requiresCompatibilities:
  - FARGATE
cpu: 512
memory: 1024
executionRoleArn: arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole
taskRoleArn: arn:aws:iam::ACCOUNT:role/ecsTaskRole

containerDefinitions:
  - name: web
    image: ACCOUNT.dkr.ecr.REGION.amazonaws.com/open-canvas-web:latest
    portMappings:
      - containerPort: 3000
        protocol: tcp
    environment:
      - name: NODE_ENV
        value: production
      - name: NEXT_PUBLIC_SUPABASE_URL
        value: !Ref SupabaseUrl
      - name: NEXT_PUBLIC_SUPABASE_ANON_KEY
        value: !Ref SupabaseAnonKey
    secrets:
      - name: NEXTAUTH_SECRET
        valueFrom: arn:aws:ssm:REGION:ACCOUNT:parameter/open-canvas/nextauth-secret
      - name: OPENAI_API_KEY
        valueFrom: arn:aws:ssm:REGION:ACCOUNT:parameter/open-canvas/openai-api-key
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/open-canvas-web
        awslogs-region: !Ref AWS::Region
        awslogs-stream-prefix: ecs
```

#### Deployment Scripts
```bash
#!/bin/bash
# scripts/deploy-aws.sh

set -e

ENVIRONMENT=${1:-production}
REGION=${2:-us-east-1}
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

echo "Deploying Open Canvas to AWS..."
echo "Environment: $ENVIRONMENT"
echo "Region: $REGION"
echo "Account ID: $ACCOUNT_ID"

# Build and push Docker images
echo "Building Docker images..."
docker build -t open-canvas-web -f apps/web/Dockerfile .
docker build -t open-canvas-agents -f apps/agents/Dockerfile .

# Tag and push to ECR
echo "Pushing images to ECR..."
aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com

docker tag open-canvas-web:latest $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/open-canvas-web:latest
docker tag open-canvas-agents:latest $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/open-canvas-agents:latest

docker push $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/open-canvas-web:latest
docker push $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/open-canvas-agents:latest

# Deploy infrastructure
echo "Deploying infrastructure..."
aws cloudformation deploy \
  --template-file infrastructure/aws/cloudformation.yml \
  --stack-name open-canvas-$ENVIRONMENT \
  --parameter-overrides Environment=$ENVIRONMENT \
  --capabilities CAPABILITY_IAM \
  --region $REGION

# Update ECS services
echo "Updating ECS services..."
aws ecs update-service \
  --cluster open-canvas-$ENVIRONMENT \
  --service open-canvas-web \
  --force-new-deployment \
  --region $REGION

aws ecs update-service \
  --cluster open-canvas-$ENVIRONMENT \
  --service open-canvas-agents \
  --force-new-deployment \
  --region $REGION

echo "Deployment complete!"
```

### 3. Google Cloud Deployment

#### Google Cloud Setup
```yaml
# infrastructure/gcp/cloudbuild.yml
steps:
  # Build web application
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/open-canvas-web:$COMMIT_SHA'
      - '-f'
      - 'apps/web/Dockerfile'
      - '.'
    
  # Build agents application
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/open-canvas-agents:$COMMIT_SHA'
      - '-f'
      - 'apps/agents/Dockerfile'
      - '.'
    
  # Push images
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/open-canvas-web:$COMMIT_SHA']
    
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/open-canvas-agents:$COMMIT_SHA']
    
  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'open-canvas-web'
      - '--image'
      - 'gcr.io/$PROJECT_ID/open-canvas-web:$COMMIT_SHA'
      - '--platform'
      - 'managed'
      - '--region'
      - 'us-central1'
      - '--allow-unauthenticated'
      - '--port'
      - '3000'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'NODE_ENV=production'
      
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'open-canvas-agents'
      - '--image'
      - 'gcr.io/$PROJECT_ID/open-canvas-agents:$COMMIT_SHA'
      - '--platform'
      - 'managed'
      - '--region'
      - 'us-central1'
      - '--allow-unauthenticated'
      - '--port'
      - '54367'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--max-instances'
      - '5'
      - '--set-env-vars'
      - 'NODE_ENV=production'

images:
  - 'gcr.io/$PROJECT_ID/open-canvas-web:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/open-canvas-agents:$COMMIT_SHA'
```

#### Terraform Configuration
```hcl
# infrastructure/gcp/main.tf
provider "google" {
  project = var.project_id
  region  = var.region
}

resource "google_project_service" "services" {
  for_each = toset([
    "run.googleapis.com",
    "cloudbuild.googleapis.com",
    "sql.googleapis.com",
    "redis.googleapis.com",
    "secretmanager.googleapis.com"
  ])
  
  service = each.value
}

resource "google_sql_database_instance" "postgres" {
  name             = "open-canvas-db"
  database_version = "POSTGRES_15"
  region           = var.region
  deletion_protection = false

  settings {
    tier = "db-f1-micro"
    
    database_flags {
      name  = "cloudsql.iam_authentication"
      value = "on"
    }
    
    backup_configuration {
      enabled = true
      start_time = "03:00"
    }
    
    ip_configuration {
      ipv4_enabled = false
      private_network = google_compute_network.vpc.self_link
    }
  }
}

resource "google_sql_database" "database" {
  name     = "opencanvas"
  instance = google_sql_database_instance.postgres.name
}

resource "google_redis_instance" "cache" {
  name           = "open-canvas-cache"
  memory_size_gb = 1
  region         = var.region
}

resource "google_cloud_run_service" "web" {
  name     = "open-canvas-web"
  location = var.region

  template {
    spec {
      containers {
        image = "gcr.io/${var.project_id}/open-canvas-web:latest"
        
        ports {
          container_port = 3000
        }
        
        resources {
          limits = {
            cpu    = "1000m"
            memory = "1Gi"
          }
        }
        
        env {
          name  = "NODE_ENV"
          value = "production"
        }
        
        env {
          name = "DATABASE_URL"
          value_from {
            secret_key_ref {
              name = google_secret_manager_secret.database_url.secret_id
              key  = "latest"
            }
          }
        }
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }
}

resource "google_cloud_run_service" "agents" {
  name     = "open-canvas-agents"
  location = var.region

  template {
    spec {
      containers {
        image = "gcr.io/${var.project_id}/open-canvas-agents:latest"
        
        ports {
          container_port = 54367
        }
        
        resources {
          limits = {
            cpu    = "2000m"
            memory = "2Gi"
          }
        }
        
        env {
          name  = "NODE_ENV"
          value = "production"
        }
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }
}

resource "google_secret_manager_secret" "database_url" {
  secret_id = "database-url"
  
  replication {
    automatic = true
  }
}
```

## Container Deployment

### 1. Docker Images

#### Web Application Dockerfile
```dockerfile
# apps/web/Dockerfile
FROM node:20-alpine AS base
WORKDIR /app

# Install dependencies
FROM base AS deps
COPY package.json yarn.lock ./
COPY apps/web/package.json ./apps/web/
COPY packages/shared/package.json ./packages/shared/
RUN yarn install --frozen-lockfile

# Build application
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN yarn build

# Production image
FROM base AS runner
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/apps/web/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000

CMD ["node", "apps/web/server.js"]
```

#### Agents Dockerfile
```dockerfile
# apps/agents/Dockerfile
FROM node:20-alpine AS base
WORKDIR /app

# Install dependencies
FROM base AS deps
COPY package.json yarn.lock ./
COPY apps/agents/package.json ./apps/agents/
COPY packages/shared/package.json ./packages/shared/
RUN yarn install --frozen-lockfile

# Build application
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN yarn build

# Production image
FROM base AS runner
ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 agents

COPY --from=builder --chown=agents:nodejs /app/apps/agents/dist ./dist
COPY --from=builder --chown=agents:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=agents:nodejs /app/packages ./packages

USER agents

EXPOSE 54367
ENV PORT 54367

CMD ["node", "dist/index.js"]
```

### 2. Kubernetes Deployment

#### Kubernetes Manifests
```yaml
# kubernetes/namespace.yml
apiVersion: v1
kind: Namespace
metadata:
  name: open-canvas

---
# kubernetes/web-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-canvas-web
  namespace: open-canvas
spec:
  replicas: 3
  selector:
    matchLabels:
      app: open-canvas-web
  template:
    metadata:
      labels:
        app: open-canvas-web
    spec:
      containers:
      - name: web
        image: open-canvas-web:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: url
        - name: NEXTAUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: secret
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
# kubernetes/agents-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-canvas-agents
  namespace: open-canvas
spec:
  replicas: 2
  selector:
    matchLabels:
      app: open-canvas-agents
  template:
    metadata:
      labels:
        app: open-canvas-agents
    spec:
      containers:
      - name: agents
        image: open-canvas-agents:latest
        ports:
        - containerPort: 54367
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-secrets
              key: openai-key
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 54367
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 54367
          initialDelaySeconds: 10
          periodSeconds: 10

---
# kubernetes/services.yml
apiVersion: v1
kind: Service
metadata:
  name: open-canvas-web-service
  namespace: open-canvas
spec:
  selector:
    app: open-canvas-web
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer

---
apiVersion: v1
kind: Service
metadata:
  name: open-canvas-agents-service
  namespace: open-canvas
spec:
  selector:
    app: open-canvas-agents
  ports:
  - protocol: TCP
    port: 54367
    targetPort: 54367
  type: ClusterIP

---
# kubernetes/ingress.yml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: open-canvas-ingress
  namespace: open-canvas
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: open-canvas-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: open-canvas-web-service
            port:
              number: 80
      - path: /api/agents
        pathType: Prefix
        backend:
          service:
            name: open-canvas-agents-service
            port:
              number: 54367
```

## Monitoring and Observability

### 1. Health Checks

#### Health Check Endpoints
```typescript
// apps/web/src/app/api/health/route.ts
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check database connection
    const dbHealthy = await checkDatabaseHealth();
    
    // Check external services
    const servicesHealthy = await checkExternalServices();
    
    const health = {
      status: dbHealthy && servicesHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version,
      environment: process.env.NODE_ENV,
      checks: {
        database: dbHealthy ? 'healthy' : 'unhealthy',
        external_services: servicesHealthy ? 'healthy' : 'unhealthy',
      },
    };
    
    return NextResponse.json(health, {
      status: health.status === 'healthy' ? 200 : 503,
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 503 }
    );
  }
}

async function checkDatabaseHealth(): Promise<boolean> {
  // Implement database health check
  return true;
}

async function checkExternalServices(): Promise<boolean> {
  // Implement external service health checks
  return true;
}
```

### 2. Monitoring Setup

#### Prometheus Configuration
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'open-canvas-web'
    static_configs:
      - targets: ['open-canvas-web:3000']
    metrics_path: '/api/metrics'
    
  - job_name: 'open-canvas-agents'
    static_configs:
      - targets: ['open-canvas-agents:54367']
    metrics_path: '/metrics'
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

#### Grafana Dashboard
```json
{
  "dashboard": {
    "title": "Open Canvas Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{job}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m])",
            "legendFormat": "5xx errors"
          }
        ]
      }
    ]
  }
}
```

## Scaling Strategies

### 1. Horizontal Scaling

#### Auto-scaling Configuration
```yaml
# kubernetes/hpa.yml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: open-canvas-web-hpa
  namespace: open-canvas
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: open-canvas-web
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: open-canvas-agents-hpa
  namespace: open-canvas
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: open-canvas-agents
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85
```

### 2. Performance Optimization

#### CDN Configuration
```yaml
# cloudflare-config.yml
rules:
  - name: "Cache static assets"
    expression: '(http.request.uri.path matches "^/static/.*") or (http.request.uri.path matches "^/_next/static/.*")'
    action: "cache"
    cache_settings:
      cache_level: "cache_everything"
      edge_cache_ttl: 2592000  # 30 days
      browser_cache_ttl: 31536000  # 1 year
      
  - name: "Cache API responses"
    expression: 'http.request.uri.path matches "^/api/.*"'
    action: "cache"
    cache_settings:
      cache_level: "cache_everything"
      edge_cache_ttl: 300  # 5 minutes
      browser_cache_ttl: 0
      
  - name: "Compress responses"
    expression: 'true'
    action: "compress"
    compression_settings:
      gzip: true
      brotli: true
```

## Security Considerations

### 1. Network Security

#### Security Groups Configuration
```yaml
# aws-security-groups.yml
WebSecurityGroup:
  Type: AWS::EC2::SecurityGroup
  Properties:
    GroupDescription: Security group for web application
    VpcId: !Ref VPC
    SecurityGroupIngress:
      - IpProtocol: tcp
        FromPort: 443
        ToPort: 443
        CidrIp: 0.0.0.0/0
      - IpProtocol: tcp
        FromPort: 80
        ToPort: 80
        CidrIp: 0.0.0.0/0
    SecurityGroupEgress:
      - IpProtocol: -1
        CidrIp: 0.0.0.0/0

AgentsSecurityGroup:
  Type: AWS::EC2::SecurityGroup
  Properties:
    GroupDescription: Security group for agents
    VpcId: !Ref VPC
    SecurityGroupIngress:
      - IpProtocol: tcp
        FromPort: 54367
        ToPort: 54367
        SourceSecurityGroupId: !Ref WebSecurityGroup
```

### 2. Secrets Management

#### Secrets Configuration
```bash
# scripts/setup-secrets.sh
#!/bin/bash

# AWS Secrets Manager
aws secretsmanager create-secret \
  --name "open-canvas/database-url" \
  --description "Database connection string" \
  --secret-string "************************************/database"

aws secretsmanager create-secret \
  --name "open-canvas/openai-api-key" \
  --description "OpenAI API key" \
  --secret-string "sk-..."

aws secretsmanager create-secret \
  --name "open-canvas/nextauth-secret" \
  --description "NextAuth secret" \
  --secret-string "your-secret-key"

# Kubernetes Secrets
kubectl create secret generic database-secrets \
  --from-literal=url="************************************/database" \
  --namespace=open-canvas

kubectl create secret generic llm-secrets \
  --from-literal=openai-key="sk-..." \
  --from-literal=anthropic-key="sk-ant-..." \
  --namespace=open-canvas

kubectl create secret generic auth-secrets \
  --from-literal=secret="your-secret-key" \
  --namespace=open-canvas
```

This comprehensive deployment documentation provides multiple deployment strategies and configurations for Open Canvas, enabling teams to choose the approach that best fits their infrastructure requirements and operational preferences.