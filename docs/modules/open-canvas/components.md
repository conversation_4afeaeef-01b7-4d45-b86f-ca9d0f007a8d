# Open Canvas - Component Architecture

## Component Hierarchy Overview

Open Canvas follows a well-structured component architecture with clear separation of concerns and reusable patterns. The component system is built around the core Canvas interface with specialized components for different functionalities.

```mermaid
graph TB
    A[App Layout] --> B[Canvas]
    B --> C[Chat Interface]
    B --> D[Artifact Renderer]
    B --> E[Assistant Select]
    
    C --> F[Composer]
    C --> G[Messages]
    C --> H[Thread History]
    C --> I[Welcome]
    
    D --> J[Text Renderer]
    D --> K[Code Renderer]
    D --> L[Actions Toolbar]
    
    E --> M[Assistant Item]
    E --> N[Create Edit Dialog]
    E --> O[Context Documents]
    
    F --> P[Composer Actions]
    F --> Q[Model Selector]
    F --> R[Drag Drop Wrapper]
    
    L --> S[Text Actions]
    L --> T[Code Actions]
    L --> U[Custom Actions]
```

## Core Components

### 1. Canvas Component (`/src/components/canvas/`)

#### Main Canvas (`canvas.tsx`)
- **Purpose**: Primary application container and layout manager
- **Key Features**:
  - Responsive design with chat and artifact panels
  - State management for panel visibility
  - Drag-and-drop file handling
  - Keyboard shortcuts
  - Real-time updates

```typescript
interface CanvasProps {
  className?: string;
  children?: React.ReactNode;
}

// Key functionality:
- Panel resizing and responsive layout
- File upload handling
- Keyboard navigation
- State persistence
```

#### Content Composer (`content-composer.tsx`)
- **Purpose**: Input interface for creating new artifacts
- **Key Features**:
  - Multi-format content creation
  - Template selection
  - Language specification
  - Live preview

#### Canvas Loading (`canvas-loading.tsx`)
- **Purpose**: Loading state management
- **Key Features**:
  - Skeleton loading states
  - Progress indicators
  - Error boundary handling
  - Graceful degradation

### 2. Chat Interface (`/src/components/chat-interface/`)

#### Main Chat Interface (`index.tsx`)
- **Purpose**: Central chat management component
- **Key Features**:
  - Message display and scrolling
  - Input handling
  - Thread management
  - Real-time updates

#### Composer (`composer.tsx`)
- **Purpose**: Message input and composition
- **Key Features**:
  - Rich text input
  - File attachment support
  - Voice input integration
  - Auto-complete suggestions
  - Keyboard shortcuts

```typescript
interface ComposerProps {
  placeholder?: string;
  onSubmit: (message: string) => void;
  disabled?: boolean;
  attachments?: AttachmentType[];
}

// Key functionality:
- Multi-line text input
- File drag-and-drop
- Voice-to-text conversion
- Auto-resize textarea
```

#### Messages (`messages.tsx`)
- **Purpose**: Message display and rendering
- **Key Features**:
  - Message threading
  - Syntax highlighting
  - Markdown rendering
  - Link previews
  - Timestamp display

#### Thread History (`thread-history.tsx`)
- **Purpose**: Conversation history management
- **Key Features**:
  - Thread listing
  - Search functionality
  - Thread metadata
  - Deletion capabilities

#### Welcome Screen (`welcome.tsx`)
- **Purpose**: First-time user experience
- **Key Features**:
  - Feature introduction
  - Getting started guide
  - Example interactions
  - Quick start actions

### 3. Artifact System (`/src/components/artifacts/`)

#### Artifact Renderer (`ArtifactRenderer.tsx`)
- **Purpose**: Dynamic artifact content rendering
- **Key Features**:
  - Multi-format support (text, code)
  - Live preview
  - Version management
  - Export capabilities

```typescript
interface ArtifactRendererProps {
  artifact: ArtifactV3;
  onUpdate?: (artifact: ArtifactV3) => void;
  readOnly?: boolean;
}

// Supported formats:
- Markdown text with live preview
- Code with syntax highlighting
- Mixed content rendering
```

#### Text Renderer (`TextRenderer.tsx`)
- **Purpose**: Markdown and text content display
- **Key Features**:
  - Live markdown preview
  - Editing capabilities
  - Syntax highlighting
  - Custom styling

#### Code Renderer (`CodeRenderer.tsx`)
- **Purpose**: Code artifact display and editing
- **Key Features**:
  - Multi-language support
  - Syntax highlighting
  - Line numbers
  - Error detection
  - Code folding

#### Actions Toolbar (`actions_toolbar/`)
- **Purpose**: Quick action interface for artifacts
- **Key Features**:
  - Context-sensitive actions
  - Custom action support
  - Keyboard shortcuts
  - Tooltip guidance

##### Text Actions (`text/index.tsx`)
- Length modification options
- Reading level adjustments
- Translation capabilities
- Style transformations

##### Code Actions (`code/index.tsx`)
- Language porting
- Comment addition
- Bug fixing
- Optimization suggestions

##### Custom Actions (`custom/index.tsx`)
- User-defined actions
- Prompt customization
- Action management
- Sharing capabilities

### 4. Assistant Management (`/src/components/assistant-select/`)

#### Assistant Select (`index.tsx`)
- **Purpose**: AI model and assistant selection
- **Key Features**:
  - Model comparison
  - Configuration management
  - Performance metrics
  - Cost information

#### Assistant Item (`assistant-item.tsx`)
- **Purpose**: Individual assistant display
- **Key Features**:
  - Model information
  - Capability indicators
  - Configuration options
  - Status indicators

#### Create Edit Dialog (`create-edit-assistant-dialog.tsx`)
- **Purpose**: Assistant configuration interface
- **Key Features**:
  - Model parameter tuning
  - Custom prompt creation
  - Performance settings
  - Validation

### 5. Authentication Components (`/src/components/auth/`)

#### Login (`login/Login.tsx`)
- **Purpose**: User authentication interface
- **Key Features**:
  - Email/password login
  - Social authentication
  - Password recovery
  - Error handling

#### Signup (`signup/Signup.tsx`)
- **Purpose**: User registration interface
- **Key Features**:
  - Account creation
  - Email verification
  - Terms acceptance
  - Validation

### 6. UI Components (`/src/components/ui/`)

#### Assistant UI (`assistant-ui/`)
- **Purpose**: AI interaction components
- **Key Features**:
  - Message threading
  - Attachment handling
  - Streaming responses
  - Error recovery

#### Form Components
- **Input**: Text input with validation
- **Button**: Styled button variants
- **Select**: Dropdown selection
- **Checkbox**: Boolean input
- **Textarea**: Multi-line input

#### Layout Components
- **Card**: Content containers
- **Dialog**: Modal interfaces
- **Sheet**: Slide-out panels
- **Accordion**: Collapsible sections
- **Popover**: Overlay content

#### Feedback Components
- **Toast**: Notification system
- **Alert**: Warning and error messages
- **Progress**: Loading indicators
- **Skeleton**: Loading placeholders

## Component Patterns

### Context Integration
Most components integrate with React contexts for state management:

```typescript
// Common context usage pattern
const { user } = useUserContext();
const { threadId, modelConfig } = useThreadContext();
const { assistant } = useAssistantContext();
```

### Custom Hooks
Components utilize custom hooks for common functionality:

```typescript
// Example custom hook usage
const { toast } = useToast();
const { store, update } = useStore();
const { documents } = useContextDocuments();
```

### Error Boundaries
Components implement error handling patterns:

```typescript
interface ErrorBoundaryProps {
  fallback?: React.ComponentType<{error: Error}>;
  onError?: (error: Error) => void;
}
```

## Styling System

### Tailwind CSS Classes
Components use utility-first styling:

```typescript
// Example styling patterns
const buttonStyles = {
  base: "inline-flex items-center justify-center rounded-md text-sm font-medium",
  variants: {
    primary: "bg-blue-600 text-white hover:bg-blue-700",
    secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300"
  }
};
```

### CSS Modules
Specific components use CSS modules for complex styling:

```typescript
// CodeRenderer.module.css
.codeContainer {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
}

.editor {
  min-height: 200px;
  font-family: 'Monaco', 'Menlo', monospace;
}
```

### Theme System
Components support light/dark themes:

```typescript
interface ThemeConfig {
  mode: 'light' | 'dark';
  colors: {
    primary: string;
    secondary: string;
    background: string;
    foreground: string;
  };
}
```

## Performance Optimizations

### Lazy Loading
Non-critical components are loaded on demand:

```typescript
const LazyComponent = lazy(() => import('./ExpensiveComponent'));

// Usage with Suspense
<Suspense fallback={<Loading />}>
  <LazyComponent />
</Suspense>
```

### Memoization
Components use React.memo and useMemo for performance:

```typescript
const MemoizedComponent = React.memo(({ data }) => {
  const expensiveValue = useMemo(() => 
    computeExpensiveValue(data), [data]
  );
  
  return <div>{expensiveValue}</div>;
});
```

### Virtual Scrolling
Large lists use virtualization:

```typescript
interface VirtualListProps {
  items: any[];
  itemHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
}
```

## Accessibility Features

### ARIA Support
Components implement proper ARIA attributes:

```typescript
// Example ARIA implementation
<button
  aria-label="Submit message"
  aria-describedby="submit-help"
  aria-pressed={isPressed}
>
  Submit
</button>
```

### Keyboard Navigation
Components support keyboard interaction:

```typescript
// Keyboard event handling
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && e.metaKey) {
    handleSubmit();
  }
};
```

### Screen Reader Support
Components provide screen reader compatibility:

```typescript
// Screen reader announcements
<div 
  role="status" 
  aria-live="polite"
  aria-atomic="true"
>
  {statusMessage}
</div>
```

## Testing Strategy

### Component Testing
Each component has associated tests:

```typescript
// Example test structure
describe('Canvas Component', () => {
  it('renders chat interface', () => {
    render(<Canvas />);
    expect(screen.getByRole('main')).toBeInTheDocument();
  });
  
  it('handles file uploads', () => {
    const { container } = render(<Canvas />);
    // Test file upload functionality
  });
});
```

### Integration Testing
Components are tested in integration scenarios:

```typescript
// Integration test example
describe('Artifact Workflow', () => {
  it('creates and edits artifacts', () => {
    // Test complete artifact creation and editing flow
  });
});
```

## Component Documentation

### Props Documentation
Each component has detailed prop documentation:

```typescript
interface ComponentProps {
  /** The primary content to display */
  children: React.ReactNode;
  
  /** Optional CSS class name */
  className?: string;
  
  /** Callback fired when component value changes */
  onChange?: (value: string) => void;
  
  /** Whether the component is disabled */
  disabled?: boolean;
}
```

### Usage Examples
Components include usage examples:

```typescript
// Basic usage
<Canvas>
  <ChatInterface />
  <ArtifactRenderer />
</Canvas>

// Advanced usage with props
<Canvas 
  className="custom-canvas"
  onFileUpload={handleFileUpload}
  theme="dark"
>
  {/* Component content */}
</Canvas>
```

## Future Component Enhancements

### Planned Features
- **React Component Rendering**: Live React component preview
- **Multi-user Collaboration**: Real-time editing indicators
- **Advanced Search**: Component-level search functionality
- **Plugin System**: Third-party component integration

### Performance Improvements
- **Streaming Updates**: Real-time component updates
- **Optimistic Updates**: Immediate UI feedback
- **Background Processing**: Non-blocking operations
- **Caching**: Component-level caching

### Accessibility Enhancements
- **Voice Navigation**: Voice command support
- **High Contrast**: Enhanced visual accessibility
- **Gesture Support**: Touch and gesture navigation
- **Internationalization**: Multi-language support

This component architecture provides a solid foundation for Open Canvas, enabling rich user interactions while maintaining performance and accessibility standards.