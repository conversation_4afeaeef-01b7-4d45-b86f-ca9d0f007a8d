# Open Canvas - API Routes Documentation

## API Architecture Overview

Open Canvas implements a comprehensive API architecture combining Next.js API routes for frontend services and LangGraph SDK for agent communication. The system provides secure, scalable endpoints for all application functionality.

```mermaid
graph TB
    subgraph "Frontend API Routes"
        A[Next.js API] --> B[Authentication]
        A --> C[File Upload]
        A --> D[Store Management]
        A --> E[Run Feedback]
        A --> F[Audio Processing]
        A --> G[Web Scraping]
    end
    
    subgraph "Agent Communication"
        H[LangGraph SDK] --> I[Thread Management]
        H --> J[Stream Processing]
        H --> K[Agent Execution]
        H --> L[State Management]
    end
    
    subgraph "External Services"
        M[Supabase] --> N[Authentication]
        M --> O[Database]
        P[FireCrawl] --> Q[Web Scraping]
        R[Groq] --> S[Audio Processing]
        T[Multiple LLMs] --> U[AI Processing]
    end
    
    B --> N
    C --> O
    G --> Q
    F --> S
    J --> U
```

## Next.js API Routes

### 1. Authentication Routes (`/src/app/api/auth/`)

#### Authentication Callback
**Route**: `/api/auth/callback`
**Method**: GET
**Purpose**: Handle OAuth and magic link callbacks

```typescript
// apps/web/src/app/api/auth/callback/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const next = searchParams.get('next') ?? '/';

  if (code) {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    
    if (!error) {
      const forwardedHost = request.headers.get('x-forwarded-host');
      const isLocalEnv = process.env.NODE_ENV === 'development';
      
      if (isLocalEnv) {
        return NextResponse.redirect(`${origin}${next}`);
      } else if (forwardedHost) {
        return NextResponse.redirect(`https://${forwardedHost}${next}`);
      } else {
        return NextResponse.redirect(`${origin}${next}`);
      }
    }
  }

  // Return to login if authentication failed
  return NextResponse.redirect(`${origin}/auth/login`);
}
```

### 2. File Management Routes (`/src/app/api/store/`)

#### Store Data
**Route**: `/api/store/put`
**Method**: PUT
**Purpose**: Store user data and preferences

```typescript
// apps/web/src/app/api/store/put/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyUserServer } from '@/lib/supabase/verify_user_server';

export async function PUT(request: NextRequest) {
  try {
    const user = await verifyUserServer();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { key, value } = body;

    if (!key || value === undefined) {
      return NextResponse.json(
        { error: 'Key and value are required' },
        { status: 400 }
      );
    }

    // Store data in Supabase or other storage
    const { data, error } = await supabase
      .from('user_data')
      .upsert({
        user_id: user.id,
        key,
        value,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error storing data:', error);
      return NextResponse.json(
        { error: 'Failed to store data' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Store PUT error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

#### Retrieve Data
**Route**: `/api/store/get`
**Method**: GET
**Purpose**: Retrieve user data

```typescript
// apps/web/src/app/api/store/get/route.ts
export async function GET(request: NextRequest) {
  try {
    const user = await verifyUserServer();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    if (!key) {
      return NextResponse.json(
        { error: 'Key parameter is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('user_data')
      .select('value')
      .eq('user_id', user.id)
      .eq('key', key)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error retrieving data:', error);
      return NextResponse.json(
        { error: 'Failed to retrieve data' },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      data: data?.value || null 
    });
  } catch (error) {
    console.error('Store GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

#### Delete Data
**Route**: `/api/store/delete`
**Method**: DELETE
**Purpose**: Delete user data

```typescript
// apps/web/src/app/api/store/delete/route.ts
export async function DELETE(request: NextRequest) {
  try {
    const user = await verifyUserServer();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { key } = body;

    if (!key) {
      return NextResponse.json(
        { error: 'Key is required' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('user_data')
      .delete()
      .eq('user_id', user.id)
      .eq('key', key);

    if (error) {
      console.error('Error deleting data:', error);
      return NextResponse.json(
        { error: 'Failed to delete data' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Store DELETE error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### 3. Run Management Routes (`/src/app/api/runs/`)

#### Run Feedback
**Route**: `/api/runs/feedback`
**Method**: POST
**Purpose**: Submit feedback for AI responses

```typescript
// apps/web/src/app/api/runs/feedback/route.ts
export async function POST(request: NextRequest) {
  try {
    const user = await verifyUserServer();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { runId, score, feedback, key } = body;

    if (!runId || typeof score !== 'number') {
      return NextResponse.json(
        { error: 'Run ID and score are required' },
        { status: 400 }
      );
    }

    // Store feedback in LangSmith
    const langSmithClient = new LangSmithClient({
      apiKey: process.env.LANGCHAIN_API_KEY,
      apiUrl: process.env.LANGCHAIN_ENDPOINT,
    });

    await langSmithClient.createFeedback({
      runId,
      score,
      feedback,
      key,
      sessionId: user.id,
    });

    // Also store in local database for analytics
    const { data, error } = await supabase
      .from('feedback')
      .insert({
        user_id: user.id,
        run_id: runId,
        score,
        feedback,
        key,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error storing feedback:', error);
      return NextResponse.json(
        { error: 'Failed to store feedback' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Feedback POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

#### Share Run
**Route**: `/api/runs/share`
**Method**: POST
**Purpose**: Share conversation runs

```typescript
// apps/web/src/app/api/runs/share/route.ts
export async function POST(request: NextRequest) {
  try {
    const user = await verifyUserServer();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { runId, isPublic, description } = body;

    if (!runId) {
      return NextResponse.json(
        { error: 'Run ID is required' },
        { status: 400 }
      );
    }

    // Generate share URL
    const shareId = generateShareId();
    const shareUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/share/${shareId}`;

    // Store share information
    const { data, error } = await supabase
      .from('shared_runs')
      .insert({
        id: shareId,
        user_id: user.id,
        run_id: runId,
        is_public: isPublic,
        description,
        share_url: shareUrl,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating share:', error);
      return NextResponse.json(
        { error: 'Failed to create share' },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      shareUrl,
      data 
    });
  } catch (error) {
    console.error('Share POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### 4. Audio Processing Routes (`/src/app/api/whisper/`)

#### Audio Transcription
**Route**: `/api/whisper/audio`
**Method**: POST
**Purpose**: Transcribe audio files to text

```typescript
// apps/web/src/app/api/whisper/audio/route.ts
import { NextRequest, NextResponse } from 'next/server';
import Groq from 'groq-sdk';

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const user = await verifyUserServer();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;

    if (!audioFile) {
      return NextResponse.json(
        { error: 'Audio file is required' },
        { status: 400 }
      );
    }

    // Convert File to Buffer
    const audioBuffer = Buffer.from(await audioFile.arrayBuffer());

    // Transcribe using Groq Whisper
    const transcription = await groq.audio.transcriptions.create({
      file: new File([audioBuffer], audioFile.name, { type: audioFile.type }),
      model: 'whisper-large-v3',
      response_format: 'json',
      temperature: 0.0,
    });

    return NextResponse.json({
      success: true,
      transcription: transcription.text,
      duration: transcription.duration,
    });
  } catch (error) {
    console.error('Audio transcription error:', error);
    return NextResponse.json(
      { error: 'Failed to transcribe audio' },
      { status: 500 }
    );
  }
}
```

### 5. Web Scraping Routes (`/src/app/api/firecrawl/`)

#### Scrape URL
**Route**: `/api/firecrawl/scrape`
**Method**: POST
**Purpose**: Scrape web content for context

```typescript
// apps/web/src/app/api/firecrawl/scrape/route.ts
import { NextRequest, NextResponse } from 'next/server';
import FirecrawlApp from '@mendable/firecrawl-js';

const firecrawl = new FirecrawlApp({
  apiKey: process.env.FIRECRAWL_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const user = await verifyUserServer();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { url, options = {} } = body;

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Validate URL
    try {
      new URL(url);
    } catch {
      return NextResponse.json(
        { error: 'Invalid URL provided' },
        { status: 400 }
      );
    }

    // Scrape the URL
    const scrapeResult = await firecrawl.scrapeUrl(url, {
      formats: ['markdown', 'html'],
      onlyMainContent: true,
      ...options,
    });

    if (!scrapeResult.success) {
      return NextResponse.json(
        { error: 'Failed to scrape URL' },
        { status: 500 }
      );
    }

    // Log scraping activity
    await supabase
      .from('scraping_activity')
      .insert({
        user_id: user.id,
        url,
        success: true,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      data: {
        content: scrapeResult.data.content,
        metadata: scrapeResult.data.metadata,
        markdown: scrapeResult.data.markdown,
      },
    });
  } catch (error) {
    console.error('Scraping error:', error);
    
    // Log failed scraping attempt
    try {
      const body = await request.json();
      await supabase
        .from('scraping_activity')
        .insert({
          user_id: user?.id || null,
          url: body.url || null,
          success: false,
          error: error.message,
          created_at: new Date().toISOString(),
        });
    } catch (logError) {
      console.error('Failed to log scraping error:', logError);
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### 6. Dynamic Route Handler (`/src/app/api/[...path]/`)

#### Catch-All Route Handler
**Route**: `/api/[...path]`
**Method**: ALL
**Purpose**: Handle dynamic API routes and proxy to LangGraph

```typescript
// apps/web/src/app/api/[...path]/route.ts
export async function GET(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context, 'GET');
}

export async function POST(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context, 'POST');
}

export async function PUT(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context, 'PUT');
}

export async function DELETE(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context, 'DELETE');
}

async function handleRequest(
  request: NextRequest,
  context: { params: { path: string[] } },
  method: string
) {
  try {
    const user = await verifyUserServer();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const path = context.params.path.join('/');
    const apiUrl = process.env.LANGGRAPH_API_URL || 'http://localhost:54367';
    const targetUrl = `${apiUrl}/${path}`;

    // Forward request to LangGraph server
    const forwardedRequest = new Request(targetUrl, {
      method,
      headers: {
        'Content-Type': request.headers.get('content-type') || 'application/json',
        'Authorization': `Bearer ${process.env.LANGGRAPH_API_KEY}`,
      },
      body: method !== 'GET' ? await request.text() : undefined,
    });

    const response = await fetch(forwardedRequest);
    const responseData = await response.text();

    return new NextResponse(responseData, {
      status: response.status,
      headers: {
        'Content-Type': response.headers.get('content-type') || 'application/json',
      },
    });
  } catch (error) {
    console.error('Dynamic route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## LangGraph SDK Integration

### 1. Client Configuration

#### LangGraph Client Setup
```typescript
// apps/web/src/hooks/utils.ts
import { Client } from '@langchain/langgraph-sdk';

export function createClient(): Client {
  return new Client({
    apiUrl: process.env.LANGGRAPH_API_URL || 'http://localhost:54367',
    apiKey: process.env.LANGGRAPH_API_KEY,
    callerOptions: {
      timeout: 120000, // 2 minutes timeout
    },
  });
}
```

### 2. Thread Operations

#### Thread Management
```typescript
// Thread operations using LangGraph SDK
export class ThreadManager {
  private client: Client;

  constructor() {
    this.client = createClient();
  }

  async createThread(metadata: Record<string, any>) {
    return await this.client.threads.create({ metadata });
  }

  async getThread(threadId: string) {
    return await this.client.threads.get(threadId);
  }

  async updateThread(threadId: string, metadata: Record<string, any>) {
    return await this.client.threads.update(threadId, { metadata });
  }

  async deleteThread(threadId: string) {
    return await this.client.threads.delete(threadId);
  }

  async searchThreads(metadata: Record<string, any>, limit: number = 100) {
    return await this.client.threads.search({ metadata, limit });
  }

  async getThreadState(threadId: string) {
    return await this.client.threads.getState(threadId);
  }

  async updateThreadState(threadId: string, values: Record<string, any>) {
    return await this.client.threads.updateState(threadId, { values });
  }
}
```

### 3. Run Operations

#### Run Management
```typescript
// Run operations for agent execution
export class RunManager {
  private client: Client;

  constructor() {
    this.client = createClient();
  }

  async createRun(threadId: string, assistantId: string, input: any) {
    return await this.client.runs.create(threadId, assistantId, { input });
  }

  async getRun(threadId: string, runId: string) {
    return await this.client.runs.get(threadId, runId);
  }

  async listRuns(threadId: string, limit: number = 50) {
    return await this.client.runs.list(threadId, { limit });
  }

  async cancelRun(threadId: string, runId: string) {
    return await this.client.runs.cancel(threadId, runId);
  }

  async streamRun(threadId: string, assistantId: string, input: any) {
    return this.client.runs.stream(threadId, assistantId, {
      input,
      streamMode: 'values',
    });
  }

  async waitForRun(threadId: string, runId: string) {
    return await this.client.runs.join(threadId, runId);
  }
}
```

### 4. Streaming Operations

#### Stream Management
```typescript
// Streaming operations for real-time updates
export class StreamManager {
  private client: Client;
  private activeStreams: Map<string, AsyncGenerator> = new Map();

  constructor() {
    this.client = createClient();
  }

  async startStream(threadId: string, assistantId: string, input: any): Promise<string> {
    const streamId = `${threadId}-${Date.now()}`;
    
    const stream = this.client.runs.stream(threadId, assistantId, {
      input,
      streamMode: 'values',
    });

    this.activeStreams.set(streamId, stream);
    return streamId;
  }

  async processStream(streamId: string, onUpdate: (data: any) => void) {
    const stream = this.activeStreams.get(streamId);
    if (!stream) {
      throw new Error(`Stream ${streamId} not found`);
    }

    try {
      for await (const chunk of stream) {
        onUpdate(chunk);
      }
    } catch (error) {
      console.error('Stream processing error:', error);
      throw error;
    } finally {
      this.activeStreams.delete(streamId);
    }
  }

  stopStream(streamId: string) {
    const stream = this.activeStreams.get(streamId);
    if (stream) {
      stream.return?.(undefined);
      this.activeStreams.delete(streamId);
    }
  }

  stopAllStreams() {
    for (const [streamId, stream] of this.activeStreams) {
      stream.return?.(undefined);
    }
    this.activeStreams.clear();
  }
}
```

## Error Handling

### 1. API Error Handling

#### Error Response Structure
```typescript
interface ApiError {
  error: string;
  code?: string;
  details?: any;
  timestamp: string;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}
```

#### Error Handler Middleware
```typescript
export function withErrorHandler<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>
) {
  return async (...args: T): Promise<NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API Error:', error);
      
      if (error instanceof ApiError) {
        return NextResponse.json(
          {
            success: false,
            error: error.message,
            code: error.code,
            timestamp: new Date().toISOString(),
          },
          { status: error.statusCode || 500 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Internal server error',
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }
  };
}
```

### 2. Rate Limiting

#### Rate Limiting Middleware
```typescript
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(
    private limit: number = 100,
    private window: number = 60000 // 1 minute
  ) {}

  async checkLimit(identifier: string): Promise<boolean> {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.window);
    
    if (validRequests.length >= this.limit) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }
}

const rateLimiter = new RateLimiter(100, 60000); // 100 requests per minute

export function withRateLimit(handler: Function) {
  return async (request: NextRequest, ...args: any[]) => {
    const identifier = request.ip || 'unknown';
    
    if (!(await rateLimiter.checkLimit(identifier))) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }
    
    return handler(request, ...args);
  };
}
```

## Security Measures

### 1. Authentication Middleware
```typescript
export async function withAuth(
  handler: (request: NextRequest, user: User) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    const user = await verifyUserServer();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return handler(request, user);
  };
}
```

### 2. Input Validation
```typescript
export function withValidation<T>(
  schema: z.ZodSchema<T>,
  handler: (request: NextRequest, data: T) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    try {
      const body = await request.json();
      const validatedData = schema.parse(body);
      
      return handler(request, validatedData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation failed', details: error.errors },
          { status: 400 }
        );
      }
      
      throw error;
    }
  };
}
```

## Performance Optimization

### 1. Caching
```typescript
export class ApiCache {
  private cache: Map<string, { data: any; expiry: number }> = new Map();
  
  get(key: string): any | null {
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    if (Date.now() > cached.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }
  
  set(key: string, data: any, ttl: number = 300000): void { // 5 minutes default
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl,
    });
  }
  
  clear(): void {
    this.cache.clear();
  }
}
```

### 2. Request Batching
```typescript
export class RequestBatcher {
  private batches: Map<string, any[]> = new Map();
  private timers: Map<string, NodeJS.Timeout> = new Map();
  
  constructor(private batchSize: number = 10, private batchTimeout: number = 100) {}
  
  async addToBatch(key: string, item: any): Promise<any> {
    const batch = this.batches.get(key) || [];
    batch.push(item);
    this.batches.set(key, batch);
    
    if (batch.length >= this.batchSize) {
      return this.processBatch(key);
    }
    
    if (!this.timers.has(key)) {
      const timer = setTimeout(() => {
        this.processBatch(key);
      }, this.batchTimeout);
      
      this.timers.set(key, timer);
    }
    
    return new Promise((resolve) => {
      item.resolve = resolve;
    });
  }
  
  private async processBatch(key: string) {
    const batch = this.batches.get(key) || [];
    const timer = this.timers.get(key);
    
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(key);
    }
    
    this.batches.delete(key);
    
    if (batch.length === 0) return;
    
    try {
      const results = await this.executeBatch(key, batch);
      batch.forEach((item, index) => {
        item.resolve(results[index]);
      });
    } catch (error) {
      batch.forEach(item => {
        item.resolve(Promise.reject(error));
      });
    }
  }
  
  private async executeBatch(key: string, batch: any[]): Promise<any[]> {
    // Implementation depends on the specific batching logic
    return batch.map(item => item.data);
  }
}
```

The API architecture provides a comprehensive, secure, and performant foundation for all Open Canvas operations, ensuring reliable communication between frontend, agents, and external services.