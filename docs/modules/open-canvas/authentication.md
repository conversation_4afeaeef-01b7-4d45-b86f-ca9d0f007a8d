# Open Canvas - Authentication System Documentation

## Authentication Overview

Open Canvas implements a robust authentication system built on Supabase, providing secure user management with multiple authentication methods, session management, and comprehensive security features. The system is designed to support both individual users and future team collaboration features.

```mermaid
graph TB
    subgraph "Authentication Flow"
        A[User Login] --> B{Auth Method}
        B -->|Email/Password| C[Supabase Auth]
        B -->|OAuth| D[OAuth Provider]
        B -->|Magic Link| E[Email Link]
        
        C --> F[JWT Token]
        D --> F
        E --> F
        
        F --> G[Session Management]
        G --> H[Thread Creation]
        G --> I[Memory Access]
        G --> J[LangGraph Integration]
    end
    
    subgraph "Security Layers"
        K[JWT Validation]
        L[Session Persistence]
        M[CSRF Protection]
        N[Rate Limiting]
        O[Data Isolation]
    end
    
    G --> K
    G --> L
    G --> M
    G --> N
    G --> O
```

## Authentication Architecture

### Core Components

#### 1. Supabase Integration
**Location**: `/apps/web/src/lib/supabase/`

Supabase provides the authentication backend with comprehensive user management capabilities.

```typescript
// Client Configuration
interface SupabaseConfig {
  url: string;
  anonKey: string;
  cookieOptions: CookieOptions;
  serverSideProps: boolean;
}

// Location: /apps/web/src/lib/supabase/client.ts
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    auth: {
      flowType: 'pkce',
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    }
  }
);
```

#### 2. Authentication Methods
**Location**: `/apps/web/src/components/auth/`

Multiple authentication methods provide flexibility for different user preferences.

##### Email/Password Authentication
```typescript
// Location: /apps/web/src/components/auth/login/actions.ts
export async function loginWithEmail(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  
  if (error) {
    throw new Error(error.message);
  }
  
  return data;
}
```

##### OAuth Authentication
```typescript
// OAuth Provider Configuration
interface OAuthConfig {
  provider: 'google' | 'github';
  redirectTo: string;
  scopes?: string;
}

export async function loginWithOAuth(provider: OAuthConfig['provider']) {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: `${window.location.origin}/auth/callback`,
      queryParams: {
        access_type: 'offline',
        prompt: 'consent',
      },
    },
  });
  
  if (error) {
    throw new Error(error.message);
  }
  
  return data;
}
```

##### Magic Link Authentication
```typescript
export async function loginWithMagicLink(email: string) {
  const { data, error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      emailRedirectTo: `${window.location.origin}/auth/callback`,
    },
  });
  
  if (error) {
    throw new Error(error.message);
  }
  
  return data;
}
```

#### 3. Session Management
**Location**: `/apps/web/src/contexts/UserContext.tsx`

Session management maintains user state across the application.

```typescript
interface UserSession {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
}

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setIsLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setIsLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    setUser(null);
    setSession(null);
  };

  return (
    <UserContext.Provider value={{
      user,
      session,
      isLoading,
      isAuthenticated: !!user,
      signOut
    }}>
      {children}
    </UserContext.Provider>
  );
}
```

## Authentication Components

### 1. Login Component
**Location**: `/apps/web/src/components/auth/login/`

#### Login Form
```typescript
// Login.tsx
interface LoginFormData {
  email: string;
  password: string;
}

export function LoginForm() {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await loginWithEmail(formData.email, formData.password);
      // Redirect handled by auth state change
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleEmailLogin} className="space-y-4">
      <div>
        <label htmlFor="email" className="block text-sm font-medium">
          Email
        </label>
        <input
          id="email"
          type="email"
          required
          value={formData.email}
          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
        />
      </div>
      
      <div>
        <label htmlFor="password" className="block text-sm font-medium">
          Password
        </label>
        <input
          id="password"
          type="password"
          required
          value={formData.password}
          onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
        />
      </div>
      
      {error && (
        <div className="text-red-600 text-sm">{error}</div>
      )}
      
      <button
        type="submit"
        disabled={isLoading}
        className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {isLoading ? 'Signing in...' : 'Sign In'}
      </button>
    </form>
  );
}
```

#### OAuth Login
```typescript
// OAuth login buttons
export function OAuthButtons() {
  const handleGoogleLogin = async () => {
    try {
      await loginWithOAuth('google');
    } catch (err) {
      console.error('Google login failed:', err);
    }
  };

  const handleGitHubLogin = async () => {
    try {
      await loginWithOAuth('github');
    } catch (err) {
      console.error('GitHub login failed:', err);
    }
  };

  return (
    <div className="space-y-2">
      <button
        onClick={handleGoogleLogin}
        className="w-full py-2 px-4 bg-red-600 text-white rounded-md hover:bg-red-700"
      >
        Continue with Google
      </button>
      
      <button
        onClick={handleGitHubLogin}
        className="w-full py-2 px-4 bg-gray-800 text-white rounded-md hover:bg-gray-900"
      >
        Continue with GitHub
      </button>
    </div>
  );
}
```

### 2. Signup Component
**Location**: `/apps/web/src/components/auth/signup/`

#### Registration Form
```typescript
// Signup.tsx
interface SignupFormData {
  email: string;
  password: string;
  confirmPassword: string;
}

export function SignupForm() {
  const [formData, setFormData] = useState<SignupFormData>({
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        throw error;
      }

      if (data.user && !data.user.email_confirmed_at) {
        // Redirect to confirmation page
        window.location.href = '/auth/signup/success';
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Signup failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSignup} className="space-y-4">
      {/* Form fields similar to login */}
    </form>
  );
}
```

### 3. Auth Callback Handler
**Location**: `/apps/web/src/app/auth/callback/route.ts`

```typescript
// Handle authentication callbacks
export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const next = searchParams.get('next') ?? '/';

  if (code) {
    const supabase = createRouteHandlerClient({ cookies });
    
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    
    if (!error) {
      const forwardedHost = request.headers.get('x-forwarded-host');
      const isLocalEnv = process.env.NODE_ENV === 'development';
      
      if (isLocalEnv) {
        return NextResponse.redirect(`${origin}${next}`);
      } else if (forwardedHost) {
        return NextResponse.redirect(`https://${forwardedHost}${next}`);
      } else {
        return NextResponse.redirect(`${origin}${next}`);
      }
    }
  }

  // Return to login if something went wrong
  return NextResponse.redirect(`${origin}/auth/login`);
}
```

## Server-Side Authentication

### 1. Middleware Protection
**Location**: `/apps/web/src/middleware.ts`

```typescript
// Protect routes requiring authentication
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip middleware for public routes
  if (pathname.startsWith('/auth/') || pathname.startsWith('/api/auth/')) {
    return NextResponse.next();
  }

  try {
    const supabase = createMiddlewareClient({ req: request, res: NextResponse.next() });
    
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      const url = request.nextUrl.clone();
      url.pathname = '/auth/login';
      url.searchParams.set('next', pathname);
      return NextResponse.redirect(url);
    }

    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    const url = request.nextUrl.clone();
    url.pathname = '/auth/login';
    return NextResponse.redirect(url);
  }
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
```

### 2. Server-Side User Verification
**Location**: `/apps/web/src/lib/supabase/verify_user_server.ts`

```typescript
// Verify user on server side
export async function verifyUserServer(): Promise<User | null> {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('Error verifying user:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('Server user verification error:', error);
    return null;
  }
}
```

## Thread Integration

### 1. Thread Creation with User Context
**Location**: `/apps/web/src/contexts/ThreadProvider.tsx`

```typescript
// Create thread with user authentication
const createThread = async (): Promise<Thread | undefined> => {
  if (!user) {
    toast({
      title: "Failed to create thread",
      description: "User not found",
      duration: 5000,
      variant: "destructive",
    });
    return;
  }

  const client = createClient();
  setCreateThreadLoading(true);

  try {
    const thread = await client.threads.create({
      metadata: {
        supabase_user_id: user.id,
        customModelName: modelName,
        modelConfig: {
          ...modelConfig,
          ...(modelConfig.provider === "azure_openai" && {
            azureConfig: modelConfig.azureConfig,
          }),
        },
      },
    });

    setThreadId(thread.thread_id);
    await getUserThreads();
    return thread;
  } catch (e) {
    console.error("Failed to create thread", e);
    toast({
      title: "Failed to create thread",
      description: "An error occurred while trying to create a new thread.",
      duration: 5000,
      variant: "destructive",
    });
  } finally {
    setCreateThreadLoading(false);
  }
};
```

### 2. Thread Access Control
```typescript
// Ensure user can only access their own threads
const getUserThreads = async () => {
  if (!user) return;

  setIsUserThreadsLoading(true);
  try {
    const client = createClient();
    
    const userThreads = await client.threads.search({
      metadata: {
        supabase_user_id: user.id, // Filter by user ID
      },
      limit: 100,
    });

    // Filter threads with valid content
    const validThreads = userThreads.filter(
      (thread) => thread.values && Object.keys(thread.values).length > 0
    );
    
    setUserThreads(validThreads);
  } catch (error) {
    console.error('Failed to get user threads:', error);
  } finally {
    setIsUserThreadsLoading(false);
  }
};
```

## Security Features

### 1. JWT Token Management
```typescript
// Token refresh and validation
interface TokenManagement {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  tokenType: 'bearer';
}

// Automatic token refresh
useEffect(() => {
  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    async (event, session) => {
      if (event === 'TOKEN_REFRESHED') {
        // Update session state
        setSession(session);
        
        // Update API client with new token
        if (session?.access_token) {
          updateApiToken(session.access_token);
        }
      }
    }
  );

  return () => subscription.unsubscribe();
}, []);
```

### 2. CSRF Protection
```typescript
// CSRF token validation
interface CSRFProtection {
  token: string;
  validate: (token: string) => boolean;
  generate: () => string;
}

// Implement CSRF protection for forms
export function withCSRFProtection<T extends FormData>(
  handler: (data: T) => Promise<void>
) {
  return async (data: T) => {
    const csrfToken = data.get('csrf_token') as string;
    
    if (!validateCSRFToken(csrfToken)) {
      throw new Error('Invalid CSRF token');
    }
    
    return handler(data);
  };
}
```

### 3. Rate Limiting
```typescript
// Rate limiting for authentication attempts
interface RateLimiter {
  identifier: string;
  limit: number;
  window: number;
  attempts: number;
  resetTime: number;
}

export class AuthRateLimiter {
  private attempts = new Map<string, RateLimiter>();
  
  async checkLimit(identifier: string): Promise<boolean> {
    const now = Date.now();
    const limiter = this.attempts.get(identifier);
    
    if (!limiter) {
      this.attempts.set(identifier, {
        identifier,
        limit: 5, // 5 attempts
        window: 15 * 60 * 1000, // 15 minutes
        attempts: 1,
        resetTime: now + 15 * 60 * 1000,
      });
      return true;
    }
    
    if (now > limiter.resetTime) {
      limiter.attempts = 1;
      limiter.resetTime = now + limiter.window;
      return true;
    }
    
    if (limiter.attempts >= limiter.limit) {
      return false;
    }
    
    limiter.attempts++;
    return true;
  }
}
```

## Error Handling

### 1. Authentication Errors
```typescript
interface AuthError {
  type: 'invalid_credentials' | 'user_not_found' | 'email_not_confirmed' | 'rate_limit_exceeded';
  message: string;
  code?: string;
}

export function handleAuthError(error: AuthError): string {
  switch (error.type) {
    case 'invalid_credentials':
      return 'Invalid email or password. Please try again.';
    case 'user_not_found':
      return 'No account found with this email address.';
    case 'email_not_confirmed':
      return 'Please confirm your email address before signing in.';
    case 'rate_limit_exceeded':
      return 'Too many attempts. Please try again later.';
    default:
      return 'An error occurred during authentication. Please try again.';
  }
}
```

### 2. Session Recovery
```typescript
// Handle session recovery
export async function recoverSession(): Promise<Session | null> {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Session recovery error:', error);
      return null;
    }
    
    if (session) {
      // Validate session is still valid
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        // Session invalid, sign out
        await supabase.auth.signOut();
        return null;
      }
    }
    
    return session;
  } catch (error) {
    console.error('Session recovery failed:', error);
    return null;
  }
}
```

## Privacy and Data Protection

### 1. Data Isolation
```typescript
// Ensure user data isolation
interface DataIsolation {
  userId: string;
  threadIds: string[];
  memoryStore: UserMemory;
  customActions: CustomAction[];
}

// Filter data by user ID
export function filterUserData<T extends { userId: string }>(
  data: T[],
  currentUserId: string
): T[] {
  return data.filter(item => item.userId === currentUserId);
}
```

### 2. Data Encryption
```typescript
// Encrypt sensitive user data
interface EncryptionService {
  encrypt: (data: string) => string;
  decrypt: (encryptedData: string) => string;
  hash: (data: string) => string;
}

export const encryptionService: EncryptionService = {
  encrypt: (data: string) => {
    // Implement encryption logic
    return encrypt(data, process.env.ENCRYPTION_KEY!);
  },
  
  decrypt: (encryptedData: string) => {
    // Implement decryption logic
    return decrypt(encryptedData, process.env.ENCRYPTION_KEY!);
  },
  
  hash: (data: string) => {
    // Implement hashing logic
    return hash(data);
  }
};
```

## Future Authentication Enhancements

### Planned Features
- **Multi-factor Authentication**: SMS and authenticator app support
- **Single Sign-On (SSO)**: Enterprise SSO integration
- **Team Management**: Organization and team-based authentication
- **Advanced Security**: Biometric authentication, device management

### Security Improvements
- **Enhanced Monitoring**: Advanced security monitoring and alerting
- **Audit Logging**: Comprehensive authentication audit trails
- **Threat Detection**: Automated threat detection and response
- **Compliance**: GDPR, CCPA, and other privacy regulation compliance

The authentication system provides a secure, scalable foundation for Open Canvas user management, ensuring data privacy and security while maintaining an excellent user experience.