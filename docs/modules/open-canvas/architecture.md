# Open Canvas - Architecture Documentation

## System Architecture Overview

Open Canvas follows a sophisticated monorepo architecture with clear separation of concerns between frontend, backend agents, and shared libraries. The system is designed for scalability, maintainability, and extensibility.

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Next.js Web App] --> B[React Components]
        B --> C[Context Providers]
        C --> D[Custom Hooks]
        D --> E[UI Components]
    end
    
    subgraph "Agent Layer"
        F[LangGraph Server] --> G[Open Canvas Agent]
        G --> H[Reflection Agent]
        G --> I[Summarizer Agent]
        G --> J[Title Generator Agent]
        G --> K[Web Search Agent]
    end
    
    subgraph "External Services"
        L[Supabase Auth]
        M[Multiple LLM Providers]
        N[Web Search APIs]
        O[Audio Processing APIs]
    end
    
    subgraph "Shared Libraries"
        P[Types & Interfaces]
        Q[Model Configurations]
        R[Utility Functions]
        S[Constants & Prompts]
    end
    
    A --> F
    F --> L
    F --> M
    F --> N
    F --> O
    A --> L
    B --> P
    G --> P
    G --> Q
    G --> R
    G --> S
```

## Monorepo Structure

### High-Level Organization
```
open-canvas/
├── apps/
│   ├── agents/          # LangGraph agent implementations
│   └── web/             # Next.js frontend application
├── packages/
│   └── shared/          # Common types, utilities, and configurations
├── static/              # Static assets and documentation
├── langgraph.json       # LangGraph configuration
└── package.json         # Root package configuration
```

### Apps Directory Structure

#### Agent Application (`apps/agents/`)
```
apps/agents/
├── src/
│   ├── open-canvas/        # Main conversation agent
│   │   ├── index.ts        # State graph definition
│   │   ├── state.ts        # State management
│   │   ├── prompts.ts      # AI prompts and templates
│   │   └── nodes/          # Individual processing nodes
│   ├── reflection/         # Memory and learning agent
│   ├── summarizer/         # Conversation summarization
│   ├── thread-title/       # Thread title generation
│   ├── web-search/         # Web search integration
│   └── utils.ts           # Shared utilities
├── package.json
└── tsconfig.json
```

#### Web Application (`apps/web/`)
```
apps/web/
├── src/
│   ├── app/               # Next.js app router
│   │   ├── api/           # API endpoints
│   │   ├── auth/          # Authentication pages
│   │   ├── layout.tsx     # Root layout
│   │   └── page.tsx       # Home page
│   ├── components/        # React components
│   │   ├── artifacts/     # Artifact rendering
│   │   ├── canvas/        # Main canvas interface
│   │   ├── chat-interface/ # Chat components
│   │   └── ui/            # Reusable UI components
│   ├── contexts/          # React contexts
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries
│   └── workers/           # Web workers
├── package.json
├── next.config.mjs
└── tailwind.config.ts
```

## LangGraph Agent Orchestration

### Agent Graph Architecture

The core of Open Canvas is built on LangGraph, providing sophisticated agent orchestration with state management and conditional routing.

```mermaid
graph TD
    START([Start]) --> A[Generate Path]
    A --> B{Route Decision}
    
    B -->|New Content| C[Generate Artifact]
    B -->|Update Existing| D[Update Artifact]
    B -->|Rewrite| E[Rewrite Artifact]
    B -->|Theme Change| F[Rewrite Theme]
    B -->|Custom Action| G[Custom Action]
    B -->|General Chat| H[Reply to Input]
    B -->|Web Search| I[Web Search]
    B -->|Highlight Update| J[Update Highlighted Text]
    
    C --> K[Generate Followup]
    D --> K
    E --> K
    F --> K
    G --> K
    J --> K
    
    H --> L[Clean State]
    I --> M[Route Post Search]
    M --> C
    M --> E
    
    K --> N[Reflect]
    N --> L
    L --> O{Title Generation?}
    O -->|Yes| P[Generate Title]
    O -->|No| Q{Summarization?}
    P --> END([End])
    Q -->|Yes| R[Summarizer]
    Q -->|No| END
    R --> END
```

### State Management

#### OpenCanvasGraphAnnotation
The central state object that flows through all agent nodes:

```typescript
interface OpenCanvasGraphState {
  // Core conversation data
  messages: BaseMessage[];          // User-visible messages
  _messages: BaseMessage[];         // Internal messages (including summaries)
  
  // Artifact management
  artifact: ArtifactV3;            // Current artifact state
  highlightedCode: CodeHighlight;   // Selected code sections
  highlightedText: TextHighlight;   // Selected text sections
  
  // Processing controls
  next: string;                     // Next node to route to
  language: LanguageOptions;        // Target language for translations
  artifactLength: ArtifactLengthOptions;  // Desired content length
  readingLevel: ReadingLevelOptions;      // Target reading level
  
  // Code-specific controls
  portLanguage: ProgrammingLanguageOptions;  // Target programming language
  addComments: boolean;             // Add code comments
  addLogs: boolean;                 // Add logging statements
  fixBugs: boolean;                 // Fix identified bugs
  
  // Web search integration
  webSearchEnabled: boolean;        // Enable web search
  webSearchResults: SearchResult[]; // Search results
  
  // Custom actions
  customQuickActionId: string;      // ID of custom quick action
  regenerateWithEmojis: boolean;    // Include emojis in generation
}
```

### Agent Nodes

#### 1. Generate Path Node
- **Purpose**: Initial routing and request classification
- **Location**: `/apps/agents/src/open-canvas/nodes/generate-path/index.ts`
- **Responsibilities**:
  - Analyze user input and context
  - Determine appropriate processing path
  - Set routing parameters for subsequent nodes
  - Handle web search activation

#### 2. Generate Artifact Node
- **Purpose**: Create new content artifacts
- **Location**: `/apps/agents/src/open-canvas/nodes/generate-artifact/index.ts`
- **Responsibilities**:
  - Generate text or code artifacts based on user requests
  - Apply context from previous conversations
  - Integrate web search results when available
  - Set appropriate artifact metadata

#### 3. Update Artifact Node
- **Purpose**: Modify existing artifacts
- **Location**: `/apps/agents/src/open-canvas/nodes/updateArtifact.ts`
- **Responsibilities**:
  - Apply targeted changes to existing content
  - Maintain artifact version history
  - Handle highlighted section updates
  - Preserve original formatting when possible

#### 4. Rewrite Artifact Node
- **Purpose**: Comprehensive artifact regeneration
- **Location**: `/apps/agents/src/open-canvas/nodes/rewrite-artifact/index.ts`
- **Responsibilities**:
  - Complete artifact regeneration with new requirements
  - Apply style and theme changes
  - Handle format conversions
  - Maintain content essence while applying changes

#### 5. Custom Action Node
- **Purpose**: Execute user-defined custom actions
- **Location**: `/apps/agents/src/open-canvas/nodes/customAction.ts`
- **Responsibilities**:
  - Execute user-defined prompts
  - Apply custom transformations
  - Handle reflection integration
  - Maintain action history

#### 6. Reflection Node
- **Purpose**: Extract insights and learning from conversations
- **Location**: `/apps/agents/src/open-canvas/nodes/reflect.ts`
- **Responsibilities**:
  - Analyze conversation patterns
  - Extract style rules and user preferences
  - Update persistent memory store
  - Generate insights for future interactions

#### 7. Web Search Node
- **Purpose**: Integrate external web search capabilities
- **Location**: `/apps/agents/src/web-search/index.ts`
- **Responsibilities**:
  - Execute web searches based on user queries
  - Process and format search results
  - Integrate results into conversation context
  - Handle search result summarization

## Data Flow Architecture

### Request Processing Flow

1. **User Input Reception**
   - Frontend captures user input and context
   - Thread management system maintains session state
   - Context providers inject user preferences and model configuration

2. **Agent Processing**
   - LangGraph server receives structured request
   - Generate Path node analyzes and routes request
   - Appropriate processing nodes execute based on routing decision
   - State flows through nodes with updates and transformations

3. **Response Generation**
   - Processing nodes generate responses and artifacts
   - Followup generation provides conversation continuity
   - Reflection agent processes session for learning
   - Clean state preparation for next interaction

4. **Frontend Updates**
   - Streaming responses update UI in real-time
   - Artifact rendering handles content display
   - Version management tracks changes
   - Context updates reflect new conversation state

### State Persistence

#### Thread-Level Persistence
- **LangGraph State**: Maintained in LangGraph server memory
- **Conversation History**: Stored in thread metadata
- **User Preferences**: Cached in frontend context providers
- **Session Data**: Maintained across browser sessions

#### User-Level Persistence
- **Authentication State**: Managed by Supabase
- **Custom Quick Actions**: Stored in user profile
- **Reflection Data**: Persistent memory store
- **Model Configurations**: User-specific settings

## Integration Architecture

### Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant W as Web App
    participant S as Supabase
    participant L as LangGraph Server
    
    U->>W: Login Request
    W->>S: Authenticate
    S-->>W: JWT Token
    W->>L: Request with User ID
    L-->>W: Thread Created
    W-->>U: Authenticated Session
```

### LLM Provider Integration
```mermaid
graph TB
    A[Model Selector] --> B[Configuration Manager]
    B --> C{Provider Type}
    
    C -->|OpenAI| D[OpenAI Client]
    C -->|Anthropic| E[Anthropic Client]
    C -->|Google| F[Google GenAI Client]
    C -->|Fireworks| G[Fireworks Client]
    C -->|Groq| H[Groq Client]
    C -->|Ollama| I[Ollama Client]
    
    D --> J[Model Response]
    E --> J
    F --> J
    G --> J
    H --> J
    I --> J
    
    J --> K[Response Processing]
    K --> L[Artifact Generation]
```

### Web Search Integration
```mermaid
sequenceDiagram
    participant U as User
    participant W as Web App
    participant L as LangGraph Server
    participant E as Exa AI
    participant F as FireCrawl
    
    U->>W: Query with Search
    W->>L: Search-enabled Request
    L->>E: Search Query
    E-->>L: Search Results
    L->>F: Content Scraping
    F-->>L: Scraped Content
    L->>L: Process & Summarize
    L-->>W: Enhanced Response
    W-->>U: Content with Sources
```

## Performance Considerations

### Scalability Design
- **Horizontal Scaling**: Multiple LangGraph server instances
- **Load Balancing**: Request distribution across agents
- **Caching**: Response caching for common requests
- **Database Optimization**: Efficient query patterns

### Memory Management
- **Conversation Summarization**: Automatic summarization for long conversations
- **State Cleanup**: Regular cleanup of unused state
- **Artifact Versioning**: Efficient storage of version history
- **Cache Invalidation**: Proper cache management

### Real-time Performance
- **Streaming Responses**: Real-time UI updates
- **WebSocket Management**: Efficient connection handling
- **Concurrent Processing**: Parallel agent execution
- **Error Recovery**: Graceful handling of failures

## Security Architecture

### Authentication & Authorization
- **JWT-based Authentication**: Secure token-based auth
- **Role-based Access**: User permission management
- **Session Management**: Secure session handling
- **API Security**: Rate limiting and validation

### Data Protection
- **Input Sanitization**: Malicious input prevention
- **Output Filtering**: Response content filtering
- **Encryption**: Data encryption at rest and in transit
- **Privacy Controls**: User data protection

## Monitoring & Observability

### LangSmith Integration
- **Request Tracing**: End-to-end request tracking
- **Performance Metrics**: Response time and success rates
- **Error Monitoring**: Comprehensive error tracking
- **Usage Analytics**: User behavior analysis

### System Health
- **Agent Health Checks**: Monitor agent availability
- **Resource Usage**: Memory and CPU monitoring
- **Database Performance**: Query performance tracking
- **External Service Health**: Provider availability monitoring

## Future Architectural Considerations

### Planned Enhancements
- **Multi-tenant Architecture**: Support for multiple organizations
- **Real-time Collaboration**: Multi-user editing capabilities
- **Plugin Architecture**: Third-party extension support
- **Edge Computing**: CDN-based content delivery

### Technical Debt Management
- **Code Quality**: Automated testing and linting
- **Documentation**: Comprehensive API documentation
- **Dependency Management**: Regular updates and security patches
- **Performance Optimization**: Continuous performance improvements