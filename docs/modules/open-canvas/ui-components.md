# Open Canvas - UI Components Documentation

## UI Component System Overview

Open Canvas employs a comprehensive UI component system built on modern React patterns, Tailwind CSS, and Radix UI primitives. The system prioritizes accessibility, consistency, and reusability while providing a rich user experience for collaborative document creation.

```mermaid
graph TB
    subgraph "UI Foundation"
        A[Radix UI Primitives] --> B[Base Components]
        B --> C[Composite Components]
        C --> D[Feature Components]
        D --> E[Page Components]
    end
    
    subgraph "Styling System"
        F[Tailwind CSS] --> G[Design Tokens]
        G --> H[Component Variants]
        H --> I[Theme System]
        I --> J[Responsive Design]
    end
    
    subgraph "Accessibility"
        K[ARIA Support] --> L[Keyboard Navigation]
        L --> M[Screen Reader Support]
        M --> N[Focus Management]
        N --> O[Color Contrast]
    end
    
    A --> F
    B --> K
    C --> L
    D --> M
    E --> N
```

## Base UI Components

### 1. Form Components (`/src/components/ui/`)

#### Button Component
```typescript
// button.tsx
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
}

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline: "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
```

#### Input Component
```typescript
// input.tsx
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  label?: string;
  helper?: string;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, label, helper, ...props }, ref) => {
    const id = useId();
    
    return (
      <div className="space-y-2">
        {label && (
          <label htmlFor={id} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {label}
          </label>
        )}
        
        <input
          id={id}
          type={type}
          className={cn(
            "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
            error && "border-destructive focus-visible:ring-destructive",
            className
          )}
          ref={ref}
          {...props}
        />
        
        {helper && !error && (
          <p className="text-xs text-muted-foreground">{helper}</p>
        )}
        
        {error && (
          <p className="text-xs text-destructive" role="alert" aria-live="polite">
            {error}
          </p>
        )}
      </div>
    );
  }
);
```

#### Select Component
```typescript
// select.tsx
interface SelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  children: React.ReactNode;
}

export function Select({ value, onValueChange, placeholder, children }: SelectProps) {
  return (
    <SelectPrimitive.Root value={value} onValueChange={onValueChange}>
      <SelectPrimitive.Trigger className="flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1">
        <SelectPrimitive.Value placeholder={placeholder} />
        <SelectPrimitive.Icon asChild>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </SelectPrimitive.Icon>
      </SelectPrimitive.Trigger>
      
      <SelectPrimitive.Portal>
        <SelectPrimitive.Content className="relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2">
          <SelectPrimitive.Viewport className="p-1">
            {children}
          </SelectPrimitive.Viewport>
        </SelectPrimitive.Content>
      </SelectPrimitive.Portal>
    </SelectPrimitive.Root>
  );
}
```

#### Textarea Component
```typescript
// textarea.tsx
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string;
  label?: string;
  helper?: string;
  autoResize?: boolean;
}

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, error, label, helper, autoResize = false, ...props }, ref) => {
    const id = useId();
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    
    useEffect(() => {
      if (autoResize && textareaRef.current) {
        const textarea = textareaRef.current;
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
      }
    }, [props.value, autoResize]);
    
    return (
      <div className="space-y-2">
        {label && (
          <label htmlFor={id} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {label}
          </label>
        )}
        
        <textarea
          id={id}
          className={cn(
            "flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
            error && "border-destructive focus-visible:ring-destructive",
            className
          )}
          ref={ref || textareaRef}
          {...props}
        />
        
        {helper && !error && (
          <p className="text-xs text-muted-foreground">{helper}</p>
        )}
        
        {error && (
          <p className="text-xs text-destructive" role="alert" aria-live="polite">
            {error}
          </p>
        )}
      </div>
    );
  }
);
```

### 2. Layout Components

#### Card Component
```typescript
// card.tsx
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {}

export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("rounded-xl border bg-card text-card-foreground shadow", className)}
      {...props}
    />
  )
);

export const CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("flex flex-col space-y-1.5 p-6", className)} {...props} />
  )
);

export const CardTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3 ref={ref} className={cn("font-semibold leading-none tracking-tight", className)} {...props} />
  )
);

export const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
  )
);
```

#### Dialog Component
```typescript
// dialog.tsx
interface DialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}

export function Dialog({ open, onOpenChange, children }: DialogProps) {
  return (
    <DialogPrimitive.Root open={open} onOpenChange={onOpenChange}>
      {children}
    </DialogPrimitive.Root>
  );
}

export const DialogTrigger = DialogPrimitive.Trigger;

export const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPrimitive.Portal>
    <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPrimitive.Portal>
));
```

### 3. Feedback Components

#### Toast Component
```typescript
// toast.tsx
interface ToastProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}

export function Toast({ title, description, variant = 'default', duration = 5000 }: ToastProps) {
  const [isVisible, setIsVisible] = useState(true);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
    }, duration);
    
    return () => clearTimeout(timer);
  }, [duration]);
  
  if (!isVisible) return null;
  
  return (
    <div className={cn(
      "fixed top-4 right-4 z-50 w-full max-w-sm rounded-md border p-4 shadow-lg transition-all",
      variant === 'destructive' 
        ? "border-destructive bg-destructive text-destructive-foreground" 
        : "bg-background"
    )}>
      {title && (
        <div className="font-semibold mb-1">{title}</div>
      )}
      {description && (
        <div className="text-sm opacity-90">{description}</div>
      )}
      
      <button
        onClick={() => setIsVisible(false)}
        className="absolute top-2 right-2 rounded-sm opacity-70 hover:opacity-100"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  );
}
```

#### Alert Component
```typescript
// alert.tsx
interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'destructive' | 'warning' | 'info';
}

const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive: "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
        warning: "border-orange-200 bg-orange-50 text-orange-800 dark:border-orange-900 dark:bg-orange-950 dark:text-orange-300",
        info: "border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-900 dark:bg-blue-950 dark:text-blue-300",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  ({ className, variant, ...props }, ref) => (
    <div
      ref={ref}
      role="alert"
      className={cn(alertVariants({ variant }), className)}
      {...props}
    />
  )
);
```

## Specialized Components

### 1. Code Editor Integration

#### CodeMirror Wrapper
```typescript
// code-editor.tsx
interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: string;
  theme?: 'light' | 'dark';
  readOnly?: boolean;
  lineNumbers?: boolean;
  autoFocus?: boolean;
}

export function CodeEditor({
  value,
  onChange,
  language,
  theme = 'light',
  readOnly = false,
  lineNumbers = true,
  autoFocus = false
}: CodeEditorProps) {
  const extensions = useMemo(() => {
    const exts = [
      basicSetup,
      lineNumbers ? lineNumbersExt() : [],
      readOnly ? [] : EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          onChange(update.state.doc.toString());
        }
      }),
    ];
    
    // Add language support
    switch (language) {
      case 'javascript':
      case 'typescript':
        exts.push(javascript({ typescript: language === 'typescript' }));
        break;
      case 'python':
        exts.push(python());
        break;
      case 'java':
        exts.push(java());
        break;
      case 'cpp':
        exts.push(cpp());
        break;
      case 'rust':
        exts.push(rust());
        break;
      case 'html':
        exts.push(html());
        break;
      case 'css':
        exts.push(css());
        break;
      case 'json':
        exts.push(json());
        break;
      case 'xml':
        exts.push(xml());
        break;
      case 'sql':
        exts.push(sql());
        break;
      case 'php':
        exts.push(php());
        break;
      case 'clojure':
        exts.push(clojure());
        break;
      case 'csharp':
        exts.push(csharp());
        break;
      default:
        break;
    }
    
    return exts;
  }, [language, readOnly, lineNumbers, onChange]);
  
  return (
    <div className="border rounded-md overflow-hidden">
      <CodeMirror
        value={value}
        extensions={extensions}
        theme={theme === 'dark' ? oneDark : undefined}
        readOnly={readOnly}
        autoFocus={autoFocus}
      />
    </div>
  );
}
```

### 2. Markdown Editor

#### Rich Markdown Editor
```typescript
// markdown-editor.tsx
interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  preview?: boolean;
  height?: number;
}

export function MarkdownEditor({
  value,
  onChange,
  preview = true,
  height = 400
}: MarkdownEditorProps) {
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');
  
  return (
    <div className="border rounded-md overflow-hidden">
      <div className="flex border-b">
        <button
          onClick={() => setActiveTab('edit')}
          className={cn(
            "px-4 py-2 text-sm font-medium border-b-2 transition-colors",
            activeTab === 'edit' 
              ? "border-primary text-primary" 
              : "border-transparent text-muted-foreground hover:text-foreground"
          )}
        >
          Edit
        </button>
        
        {preview && (
          <button
            onClick={() => setActiveTab('preview')}
            className={cn(
              "px-4 py-2 text-sm font-medium border-b-2 transition-colors",
              activeTab === 'preview' 
                ? "border-primary text-primary" 
                : "border-transparent text-muted-foreground hover:text-foreground"
            )}
          >
            Preview
          </button>
        )}
      </div>
      
      <div style={{ height }}>
        {activeTab === 'edit' ? (
          <MDEditor
            value={value}
            onChange={(val) => onChange(val || '')}
            preview="edit"
            hideToolbar
            height={height}
          />
        ) : (
          <div className="p-4 prose prose-sm max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkMath]}
              rehypePlugins={[rehypeKatex]}
              components={{
                code({ node, inline, className, children, ...props }) {
                  const match = /language-(\w+)/.exec(className || '');
                  return !inline && match ? (
                    <SyntaxHighlighter
                      style={tomorrow}
                      language={match[1]}
                      PreTag="div"
                      {...props}
                    >
                      {String(children).replace(/\n$/, '')}
                    </SyntaxHighlighter>
                  ) : (
                    <code className={className} {...props}>
                      {children}
                    </code>
                  );
                }
              }}
            >
              {value}
            </ReactMarkdown>
          </div>
        )}
      </div>
    </div>
  );
}
```

### 3. File Upload

#### Drag and Drop File Upload
```typescript
// file-upload.tsx
interface FileUploadProps {
  onUpload: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  className?: string;
}

export function FileUpload({
  onUpload,
  accept = "*/*",
  multiple = false,
  maxSize = 10 * 1024 * 1024, // 10MB
  className
}: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };
  
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };
  
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFiles(files);
  };
  
  const handleFiles = (files: File[]) => {
    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        console.warn(`File ${file.name} is too large`);
        return false;
      }
      return true;
    });
    
    if (validFiles.length > 0) {
      onUpload(validFiles);
    }
  };
  
  return (
    <div
      className={cn(
        "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
        isDragOver 
          ? "border-primary bg-primary/10" 
          : "border-muted-foreground/25 hover:border-muted-foreground/50",
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => inputRef.current?.click()}
    >
      <input
        ref={inputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileInput}
        className="hidden"
      />
      
      <Upload className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
      <p className="text-sm text-muted-foreground">
        Drop files here or click to browse
      </p>
      <p className="text-xs text-muted-foreground mt-1">
        Max size: {(maxSize / (1024 * 1024)).toFixed(0)}MB
      </p>
    </div>
  );
}
```

## Theme System

### 1. Theme Configuration
```typescript
// theme.ts
interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    muted: string;
    destructive: string;
    border: string;
    input: string;
    ring: string;
  };
  radius: string;
  fontFamily: {
    sans: string[];
    mono: string[];
  };
}

export const lightTheme: ThemeConfig = {
  colors: {
    primary: "222.2 84% 4.9%",
    secondary: "210 40% 96%",
    accent: "210 40% 96%",
    background: "0 0% 100%",
    foreground: "222.2 84% 4.9%",
    muted: "210 40% 96%",
    destructive: "0 84.2% 60.2%",
    border: "214.3 31.8% 91.4%",
    input: "214.3 31.8% 91.4%",
    ring: "222.2 84% 4.9%",
  },
  radius: "0.5rem",
  fontFamily: {
    sans: ["Inter", "system-ui", "sans-serif"],
    mono: ["JetBrains Mono", "Monaco", "monospace"],
  },
};

export const darkTheme: ThemeConfig = {
  colors: {
    primary: "210 40% 98%",
    secondary: "222.2 84% 4.9%",
    accent: "217.2 32.6% 17.5%",
    background: "222.2 84% 4.9%",
    foreground: "210 40% 98%",
    muted: "217.2 32.6% 17.5%",
    destructive: "0 62.8% 30.6%",
    border: "217.2 32.6% 17.5%",
    input: "217.2 32.6% 17.5%",
    ring: "212.7 26.8% 83.9%",
  },
  radius: "0.5rem",
  fontFamily: {
    sans: ["Inter", "system-ui", "sans-serif"],
    mono: ["JetBrains Mono", "Monaco", "monospace"],
  },
};
```

### 2. Theme Provider
```typescript
// theme-provider.tsx
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: 'light' | 'dark' | 'system';
  storageKey?: string;
}

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'ui-theme'
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>(defaultTheme);
  
  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');
    
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(theme);
    }
  }, [theme]);
  
  const value = {
    theme,
    setTheme: (theme: 'light' | 'dark' | 'system') => {
      localStorage.setItem(storageKey, theme);
      setTheme(theme);
    },
  };
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}
```

## Accessibility Features

### 1. ARIA Support
```typescript
// accessibility.tsx
interface AccessibilityProps {
  label?: string;
  description?: string;
  required?: boolean;
  invalid?: boolean;
  errorMessage?: string;
}

export function useAccessibility({
  label,
  description,
  required = false,
  invalid = false,
  errorMessage
}: AccessibilityProps) {
  const id = useId();
  const descriptionId = description ? `${id}-description` : undefined;
  const errorId = errorMessage ? `${id}-error` : undefined;
  
  const ariaProps = {
    id,
    'aria-label': label,
    'aria-describedby': [descriptionId, errorId].filter(Boolean).join(' ') || undefined,
    'aria-required': required,
    'aria-invalid': invalid,
    'aria-errormessage': errorId,
  };
  
  return {
    inputProps: ariaProps,
    labelProps: { htmlFor: id },
    descriptionProps: { id: descriptionId },
    errorProps: { id: errorId, role: 'alert', 'aria-live': 'polite' as const },
  };
}
```

### 2. Keyboard Navigation
```typescript
// keyboard-navigation.tsx
export function useKeyboardNavigation() {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Escape':
        // Handle escape key
        break;
      case 'Enter':
        if (e.metaKey || e.ctrlKey) {
          // Handle Cmd/Ctrl + Enter
          e.preventDefault();
        }
        break;
      case 'ArrowUp':
      case 'ArrowDown':
        // Handle arrow navigation
        break;
      case 'Tab':
        // Handle tab navigation
        break;
      default:
        break;
    }
  };
  
  return { handleKeyDown };
}
```

### 3. Focus Management
```typescript
// focus-management.tsx
export function useFocusManagement() {
  const [focusedElement, setFocusedElement] = useState<HTMLElement | null>(null);
  
  const trapFocus = (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };
    
    container.addEventListener('keydown', handleKeyDown);
    return () => container.removeEventListener('keydown', handleKeyDown);
  };
  
  return { trapFocus, focusedElement, setFocusedElement };
}
```

## Performance Optimizations

### 1. Virtualization
```typescript
// virtual-list.tsx
interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
}

export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );
  
  const visibleItems = items.slice(startIndex, endIndex);
  
  return (
    <div
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => (
          <div
            key={startIndex + index}
            style={{
              position: 'absolute',
              top: (startIndex + index) * itemHeight,
              height: itemHeight,
              width: '100%',
            }}
          >
            {renderItem(item, startIndex + index)}
          </div>
        ))}
      </div>
    </div>
  );
}
```

### 2. Lazy Loading
```typescript
// lazy-loading.tsx
export function useLazyLoading() {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );
    
    if (ref.current) {
      observer.observe(ref.current);
    }
    
    return () => observer.disconnect();
  }, []);
  
  return { ref, isIntersecting };
}
```

## Testing Support

### 1. Component Testing
```typescript
// component-testing.tsx
export function renderWithProviders(
  ui: React.ReactElement,
  options: {
    theme?: 'light' | 'dark';
    user?: User;
    initialRoute?: string;
  } = {}
) {
  function AllProviders({ children }: { children: React.ReactNode }) {
    return (
      <ThemeProvider defaultTheme={options.theme}>
        <UserProvider initialUser={options.user}>
          <Router initialEntries={[options.initialRoute || '/']}>
            {children}
          </Router>
        </UserProvider>
      </ThemeProvider>
    );
  }
  
  return render(ui, { wrapper: AllProviders, ...options });
}
```

### 2. Accessibility Testing
```typescript
// accessibility-testing.tsx
export function testAccessibility(component: React.ReactElement) {
  return {
    hasProperLabels: () => {
      const { container } = render(component);
      const inputs = container.querySelectorAll('input, select, textarea');
      
      inputs.forEach(input => {
        expect(input).toHaveAccessibleName();
      });
    },
    
    hasKeyboardNavigation: () => {
      const { container } = render(component);
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      focusableElements.forEach(element => {
        expect(element).toBeInTheDocument();
      });
    },
  };
}
```

The UI component system provides a comprehensive foundation for building accessible, performant, and maintainable user interfaces in Open Canvas, supporting both current features and future enhancements.