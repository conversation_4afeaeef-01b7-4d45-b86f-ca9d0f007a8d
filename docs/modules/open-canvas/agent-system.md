# Open Canvas - Agent System Documentation

## Agent Architecture Overview

Open Canvas employs a sophisticated multi-agent system built on LangGraph, providing specialized agents for different aspects of the collaborative document creation process. Each agent has specific responsibilities and capabilities, working together to deliver a seamless user experience.

```mermaid
graph TB
    subgraph "Agent Ecosystem"
        A[Open Canvas Agent] --> B[Reflection Agent]
        A --> C[Summarizer Agent]
        A --> D[Title Generator Agent]
        A --> E[Web Search Agent]
        
        B --> F[Memory Store]
        C --> G[Conversation Store]
        D --> H[Thread Metadata]
        E --> I[Search Results]
    end
    
    subgraph "Processing Nodes"
        J[Generate Path]
        K[Generate Artifact]
        L[Update Artifact]
        M[Rewrite Artifact]
        N[Custom Actions]
        O[Reflect]
        P[Summarize]
    end
    
    A --> J
    J --> K
    J --> L
    J --> M
    J --> N
    K --> O
    L --> O
    M --> O
    N --> O
    O --> P
```

## 1. Open Canvas Agent

### Core Agent (`/apps/agents/src/open-canvas/`)

The main agent orchestrates the entire conversation flow and artifact management process.

#### Agent Configuration
```typescript
// Location: apps/agents/src/open-canvas/index.ts
export const graph = new StateGraph(OpenCanvasGraphAnnotation)
  .addNode("generatePath", generatePath)
  .addNode("generateArtifact", generateArtifact)
  .addNode("updateArtifact", updateArtifact)
  .addNode("rewriteArtifact", rewriteArtifact)
  .addNode("customAction", customAction)
  .addNode("reflect", reflectNode)
  .addNode("summarizer", summarizer)
  .compile();
```

#### State Management
The agent maintains comprehensive state throughout the conversation:

```typescript
interface OpenCanvasState {
  // Core conversation
  messages: BaseMessage[];
  _messages: BaseMessage[];
  
  // Artifact management
  artifact: ArtifactV3;
  highlightedCode: CodeHighlight;
  highlightedText: TextHighlight;
  
  // Processing controls
  next: string;
  language: LanguageOptions;
  artifactLength: ArtifactLengthOptions;
  readingLevel: ReadingLevelOptions;
  
  // Code controls
  portLanguage: ProgrammingLanguageOptions;
  addComments: boolean;
  addLogs: boolean;
  fixBugs: boolean;
  
  // Web search
  webSearchEnabled: boolean;
  webSearchResults: SearchResult[];
  
  // Custom actions
  customQuickActionId: string;
  regenerateWithEmojis: boolean;
}
```

### Processing Nodes

#### 1. Generate Path Node
**Location**: `/apps/agents/src/open-canvas/nodes/generate-path/`

**Purpose**: Initial request analysis and routing decision

**Capabilities**:
- User intent classification
- Context analysis
- Route determination
- Web search activation
- Parameter extraction

**Processing Flow**:
1. Analyze user input and conversation history
2. Determine if web search is needed
3. Classify request type (generate, update, rewrite, etc.)
4. Set appropriate routing parameters
5. Prepare context for subsequent nodes

#### 2. Generate Artifact Node
**Location**: `/apps/agents/src/open-canvas/nodes/generate-artifact/`

**Purpose**: Create new artifacts from user requests

**Capabilities**:
- Content generation from scratch
- Multi-format support (text, code)
- Context integration
- Style consistency
- Quality validation

**Processing Flow**:
1. Analyze user request and context
2. Determine appropriate content type
3. Generate initial artifact
4. Apply style rules from memory
5. Validate and format output

#### 3. Update Artifact Node
**Location**: `/apps/agents/src/open-canvas/nodes/updateArtifact.ts`

**Purpose**: Modify existing artifacts with targeted changes

**Capabilities**:
- Selective content modification
- Highlighting-based updates
- Version preservation
- Context-aware changes
- Format maintenance

**Processing Flow**:
1. Identify update target (highlighted text/code)
2. Analyze requested changes
3. Apply modifications while preserving context
4. Maintain formatting and structure
5. Create new version

#### 4. Rewrite Artifact Node
**Location**: `/apps/agents/src/open-canvas/nodes/rewrite-artifact/`

**Purpose**: Comprehensive artifact regeneration

**Capabilities**:
- Complete content regeneration
- Style and theme changes
- Format conversions
- Content restructuring
- Quality improvements

**Processing Flow**:
1. Analyze current artifact and requirements
2. Determine rewrite scope and approach
3. Generate new content maintaining essence
4. Apply new styles and formats
5. Validate and optimize output

#### 5. Custom Action Node
**Location**: `/apps/agents/src/open-canvas/nodes/customAction.ts`

**Purpose**: Execute user-defined custom actions

**Capabilities**:
- Custom prompt execution
- User-defined transformations
- Reflection integration
- History inclusion
- Flexible parameter handling

**Processing Flow**:
1. Retrieve custom action configuration
2. Build context based on action settings
3. Execute custom prompt
4. Apply transformations
5. Update artifact with results

#### 6. Reflect Node
**Location**: `/apps/agents/src/open-canvas/nodes/reflect.ts`

**Purpose**: Learning and memory management

**Capabilities**:
- Conversation analysis
- Pattern recognition
- Style rule extraction
- User insight generation
- Memory updating

**Processing Flow**:
1. Analyze completed conversation
2. Extract patterns and preferences
3. Generate style rules
4. Identify user insights
5. Update persistent memory

## 2. Reflection Agent

### Agent Purpose
The reflection agent provides Open Canvas with its learning capabilities, analyzing user interactions to build a personalized experience.

#### Agent Configuration
```typescript
// Location: apps/agents/src/reflection/index.ts
export const reflectionGraph = new StateGraph(ReflectionAnnotation)
  .addNode("analyze", analyzeConversation)
  .addNode("extract", extractInsights)
  .addNode("store", storeMemory)
  .compile();
```

#### Reflection Capabilities

##### Style Rule Extraction
- **Writing Style**: Preferred writing approaches and techniques
- **Tone Preferences**: Communication style and voice
- **Formatting Patterns**: Document structure preferences
- **Vocabulary Usage**: Terminology and language choices

##### User Insight Generation
- **Subject Expertise**: Areas of user knowledge and interest
- **Work Context**: Professional or personal context
- **Goals and Objectives**: User's stated or implied goals
- **Collaboration Patterns**: How user prefers to work with AI

##### Memory Management
- **Storage**: Persistent memory store across sessions
- **Retrieval**: Context-aware memory integration
- **Updates**: Continuous learning from interactions
- **Privacy**: User-controlled data management

### Reflection Process

#### 1. Conversation Analysis
```typescript
interface ConversationAnalysis {
  patterns: IdentifiedPattern[];
  preferences: UserPreference[];
  context: ContextualInfo[];
  feedback: UserFeedback[];
}
```

#### 2. Insight Extraction
```typescript
interface ExtractedInsights {
  styleRules: StyleRule[];
  userInsights: UserInsight[];
  improvements: SuggestedImprovement[];
  patterns: BehaviorPattern[];
}
```

#### 3. Memory Integration
```typescript
interface MemoryUpdate {
  newInsights: Insight[];
  updatedRules: StyleRule[];
  consolidatedPatterns: Pattern[];
  confidence: number;
}
```

## 3. Summarizer Agent

### Agent Purpose
The summarizer agent manages conversation length and maintains context efficiency by creating intelligent summaries of long conversations.

#### Agent Configuration
```typescript
// Location: apps/agents/src/summarizer/index.ts
export const summarizerGraph = new StateGraph(SummarizerAnnotation)
  .addNode("analyze", analyzeLength)
  .addNode("summarize", createSummary)
  .addNode("compress", compressHistory)
  .compile();
```

#### Summarization Capabilities

##### Length Management
- **Character Limit**: ~300,000 characters (~75,000 tokens)
- **Smart Truncation**: Preserve important context
- **Incremental Summarization**: Progressive compression
- **Context Preservation**: Maintain conversation flow

##### Summary Generation
- **Key Point Extraction**: Identify important information
- **Context Preservation**: Maintain essential context
- **Artifact History**: Preserve artifact evolution
- **User Preferences**: Maintain personalization data

##### Compression Strategies
- **Selective Compression**: Compress less important messages
- **Hierarchical Summarization**: Multi-level compression
- **Context-Aware**: Preserve relevant information
- **Quality Maintenance**: Ensure summary quality

### Summarization Process

#### 1. Length Analysis
```typescript
interface LengthAnalysis {
  totalCharacters: number;
  messageCount: number;
  compressionNeeded: boolean;
  criticalMessages: Message[];
}
```

#### 2. Summary Creation
```typescript
interface SummaryCreation {
  keyPoints: string[];
  contextualInfo: string[];
  artifactHistory: ArtifactHistory[];
  userPreferences: UserPreference[];
}
```

#### 3. Message Compression
```typescript
interface MessageCompression {
  originalMessages: Message[];
  summarizedContent: string;
  compressionRatio: number;
  preservedContext: Context[];
}
```

## 4. Title Generator Agent

### Agent Purpose
The title generator agent creates concise, descriptive titles for conversation threads based on the initial exchange.

#### Agent Configuration
```typescript
// Location: apps/agents/src/thread-title/index.ts
export const titleGraph = new StateGraph(TitleAnnotation)
  .addNode("analyze", analyzeConversation)
  .addNode("generate", generateTitle)
  .addNode("validate", validateTitle)
  .compile();
```

#### Title Generation Capabilities

##### Content Analysis
- **Topic Identification**: Determine main conversation topic
- **Intent Recognition**: Understand user's primary intent
- **Context Extraction**: Identify key contextual elements
- **Scope Assessment**: Determine conversation scope

##### Title Creation
- **Concise Generation**: Create brief, descriptive titles
- **Relevance Optimization**: Ensure title accuracy
- **Uniqueness**: Avoid duplicate titles
- **Clarity**: Maintain clear communication

##### Validation
- **Length Check**: Ensure appropriate title length
- **Relevance Verification**: Confirm title accuracy
- **Uniqueness Validation**: Check for duplicates
- **Quality Assessment**: Ensure title quality

### Title Generation Process

#### 1. Conversation Analysis
```typescript
interface ConversationAnalysis {
  mainTopic: string;
  userIntent: string;
  keyTerms: string[];
  context: string[];
}
```

#### 2. Title Generation
```typescript
interface TitleGeneration {
  candidates: string[];
  selectedTitle: string;
  reasoning: string;
  confidence: number;
}
```

#### 3. Title Validation
```typescript
interface TitleValidation {
  lengthCheck: boolean;
  relevanceScore: number;
  uniqueness: boolean;
  finalTitle: string;
}
```

## 5. Web Search Agent

### Agent Purpose
The web search agent enhances conversations by providing current, relevant information from the web.

#### Agent Configuration
```typescript
// Location: apps/agents/src/web-search/index.ts
export const webSearchGraph = new StateGraph(WebSearchAnnotation)
  .addNode("classify", classifyMessage)
  .addNode("generate", generateQuery)
  .addNode("search", executeSearch)
  .addNode("process", processResults)
  .compile();
```

#### Web Search Capabilities

##### Search Providers
- **Exa AI**: Semantic search with AI-powered relevance
- **FireCrawl**: Web scraping and content extraction
- **Custom APIs**: Integration with additional search services

##### Query Generation
- **Intent Analysis**: Understand search intent
- **Query Optimization**: Create effective search queries
- **Context Integration**: Include relevant context
- **Multi-query Support**: Generate multiple search queries

##### Result Processing
- **Content Extraction**: Extract relevant information
- **Summarization**: Create concise summaries
- **Source Verification**: Validate source credibility
- **Citation Management**: Maintain proper attribution

### Web Search Process

#### 1. Message Classification
```typescript
interface MessageClassification {
  needsSearch: boolean;
  searchType: 'factual' | 'current' | 'research';
  priority: 'low' | 'medium' | 'high';
  context: string[];
}
```

#### 2. Query Generation
```typescript
interface QueryGeneration {
  primaryQuery: string;
  alternativeQueries: string[];
  searchParameters: SearchParams;
  expectedResults: number;
}
```

#### 3. Search Execution
```typescript
interface SearchExecution {
  provider: 'exa' | 'firecrawl' | 'custom';
  query: string;
  results: SearchResult[];
  processingTime: number;
}
```

#### 4. Result Processing
```typescript
interface ResultProcessing {
  filteredResults: SearchResult[];
  summaries: string[];
  citations: Citation[];
  confidence: number;
}
```

## Agent Coordination

### Inter-Agent Communication
Agents communicate through shared state and message passing:

```typescript
interface AgentMessage {
  type: 'request' | 'response' | 'notification';
  source: string;
  target: string;
  payload: any;
  timestamp: Date;
}
```

### State Synchronization
Agents maintain synchronized state through LangGraph:

```typescript
interface SharedState {
  conversationState: ConversationState;
  memoryState: MemoryState;
  searchState: SearchState;
  processingState: ProcessingState;
}
```

### Error Handling
Comprehensive error handling across all agents:

```typescript
interface ErrorHandling {
  errorType: 'processing' | 'network' | 'validation' | 'timeout';
  errorMessage: string;
  recoveryAction: RecoveryAction;
  fallbackStrategy: FallbackStrategy;
}
```

## Performance Optimization

### Agent Efficiency
- **Parallel Processing**: Concurrent agent execution
- **Caching**: Result caching for common requests
- **Load Balancing**: Distribute processing load
- **Resource Management**: Efficient resource utilization

### Memory Management
- **State Cleanup**: Regular state cleanup
- **Memory Optimization**: Efficient memory usage
- **Garbage Collection**: Automatic cleanup
- **Cache Management**: Intelligent cache management

### Response Time
- **Streaming**: Real-time response streaming
- **Async Processing**: Non-blocking operations
- **Prioritization**: Priority-based processing
- **Optimization**: Continuous performance optimization

## Monitoring and Analytics

### Agent Performance
- **Response Times**: Monitor agent response times
- **Success Rates**: Track agent success rates
- **Error Rates**: Monitor error frequencies
- **Resource Usage**: Track resource consumption

### Quality Metrics
- **Accuracy**: Measure response accuracy
- **Relevance**: Assess response relevance
- **User Satisfaction**: Track user satisfaction
- **Improvement Tracking**: Monitor learning progress

### System Health
- **Agent Health**: Monitor agent availability
- **State Consistency**: Ensure state consistency
- **Error Recovery**: Track error recovery
- **Performance Trends**: Monitor performance trends

## Future Agent Enhancements

### Planned Features
- **Custom Agent Tools**: User-defined agent capabilities
- **Multi-Modal Agents**: Support for images, audio, video
- **Collaborative Agents**: Multi-user agent support
- **Specialized Agents**: Domain-specific agents

### Advanced Capabilities
- **Reasoning Agents**: Advanced logical reasoning
- **Planning Agents**: Multi-step planning capabilities
- **Learning Agents**: Advanced machine learning
- **Creative Agents**: Enhanced creative capabilities

This comprehensive agent system provides Open Canvas with sophisticated AI capabilities, enabling personalized, efficient, and intelligent collaboration between users and AI agents.