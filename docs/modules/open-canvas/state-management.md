# Open Canvas - State Management Documentation

## State Management Overview

Open Canvas employs a multi-layered state management architecture combining React Context, Zustand, and LangGraph state management. This hybrid approach provides optimal performance, maintainability, and user experience across frontend interactions and agent processing.

```mermaid
graph TB
    subgraph "Frontend State"
        A[React Context Providers] --> B[User Context]
        A --> C[Thread Context]
        A --> D[Assistant Context]
        A --> E[Graph Context]
        
        F[Zustand Store] --> G[Local Storage]
        F --> H[UI State]
        F --> I[Cache Management]
    end
    
    subgraph "Agent State"
        J[LangGraph State] --> K[Conversation State]
        J --> L[Artifact State]
        J --> M[Processing State]
        J --> N[Memory State]
    end
    
    subgraph "Persistence"
        O[Supabase] --> P[User Data]
        O --> Q[Thread Data]
        O --> R[Custom Actions]
        
        S[LangGraph Memory] --> T[Conversation History]
        S --> U[Reflection Data]
        S --> V[Summarization]
    end
    
    B --> P
    C --> Q
    D --> R
    K --> T
    L --> U
    M --> V
```

## Frontend State Management

### 1. React Context Architecture

#### User Context Provider
**Location**: `/apps/web/src/contexts/UserContext.tsx`

Manages user authentication and profile state across the application.

```typescript
interface UserContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
}

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Authentication state management
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setSession(session);
        setUser(session?.user ?? null);
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Listen for authentication changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session);
        setSession(session);
        setUser(session?.user ?? null);
        setIsLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      setSession(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return;

    try {
      const { data, error } = await supabase.auth.updateUser({
        data: updates
      });

      if (error) throw error;
      
      setUser(data.user);
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  };

  return (
    <UserContext.Provider value={{
      user,
      session,
      isLoading,
      isAuthenticated: !!user,
      signOut,
      updateProfile
    }}>
      {children}
    </UserContext.Provider>
  );
}
```

#### Thread Context Provider
**Location**: `/apps/web/src/contexts/ThreadProvider.tsx`

Manages conversation threads and model configuration.

```typescript
interface ThreadContextType {
  threadId: string | null;
  userThreads: Thread[];
  isUserThreadsLoading: boolean;
  modelName: ALL_MODEL_NAMES;
  modelConfig: CustomModelConfig;
  modelConfigs: Record<ALL_MODEL_NAMES, CustomModelConfig>;
  createThreadLoading: boolean;
  
  // Thread operations
  getThread: (id: string) => Promise<Thread | undefined>;
  createThread: () => Promise<Thread | undefined>;
  getUserThreads: () => Promise<void>;
  deleteThread: (id: string, clearMessages: () => void) => Promise<void>;
  setThreadId: (id: string | null) => void;
  
  // Model operations
  setModelName: (name: ALL_MODEL_NAMES) => void;
  setModelConfig: (modelName: ALL_MODEL_NAMES, config: CustomModelConfig) => void;
}

export function ThreadProvider({ children }: { children: ReactNode }) {
  const { user } = useUserContext();
  const [threadId, setThreadId] = useQueryState("threadId");
  const [userThreads, setUserThreads] = useState<Thread[]>([]);
  const [isUserThreadsLoading, setIsUserThreadsLoading] = useState(false);
  const [modelName, setModelName] = useState<ALL_MODEL_NAMES>(DEFAULT_MODEL_NAME);
  const [createThreadLoading, setCreateThreadLoading] = useState(false);

  // Model configurations state
  const [modelConfigs, setModelConfigs] = useState<Record<ALL_MODEL_NAMES, CustomModelConfig>>(() => {
    const initialConfigs: Record<ALL_MODEL_NAMES, CustomModelConfig> = {} as Record<ALL_MODEL_NAMES, CustomModelConfig>;

    ALL_MODELS.forEach((model) => {
      const modelKey = model.modelName || model.name;
      initialConfigs[modelKey] = {
        ...model.config,
        provider: model.config.provider,
        temperatureRange: model.config.temperatureRange || DEFAULT_MODEL_CONFIG.temperatureRange,
        maxTokens: model.config.maxTokens || DEFAULT_MODEL_CONFIG.maxTokens,
      };
    });

    return initialConfigs;
  });

  // Thread creation with user context
  const createThread = async (): Promise<Thread | undefined> => {
    if (!user) {
      console.error('No user found for thread creation');
      return;
    }

    const client = createClient();
    setCreateThreadLoading(true);

    try {
      const thread = await client.threads.create({
        metadata: {
          supabase_user_id: user.id,
          customModelName: modelName,
          modelConfig: modelConfig,
        },
      });

      setThreadId(thread.thread_id);
      await getUserThreads();
      return thread;
    } catch (error) {
      console.error('Failed to create thread:', error);
      throw error;
    } finally {
      setCreateThreadLoading(false);
    }
  };

  // User threads management
  const getUserThreads = async () => {
    if (!user) return;

    setIsUserThreadsLoading(true);
    try {
      const client = createClient();
      const threads = await client.threads.search({
        metadata: {
          supabase_user_id: user.id,
        },
        limit: 100,
      });

      const validThreads = threads.filter(
        (thread) => thread.values && Object.keys(thread.values).length > 0
      );
      
      setUserThreads(validThreads);
    } catch (error) {
      console.error('Failed to get user threads:', error);
    } finally {
      setIsUserThreadsLoading(false);
    }
  };

  return (
    <ThreadContext.Provider value={{
      threadId,
      userThreads,
      isUserThreadsLoading,
      modelName,
      modelConfig: modelConfigs[modelName],
      modelConfigs,
      createThreadLoading,
      getThread,
      createThread,
      getUserThreads,
      deleteThread,
      setThreadId,
      setModelName,
      setModelConfig
    }}>
      {children}
    </ThreadContext.Provider>
  );
}
```

#### Assistant Context Provider
**Location**: `/apps/web/src/contexts/AssistantContext.tsx`

Manages AI assistant selection and custom actions.

```typescript
interface AssistantContextType {
  selectedAssistant: AssistantConfig | null;
  customActions: CustomQuickAction[];
  isLoadingActions: boolean;
  
  // Assistant operations
  selectAssistant: (assistant: AssistantConfig) => void;
  updateAssistantConfig: (config: Partial<AssistantConfig>) => void;
  
  // Custom actions
  createCustomAction: (action: Omit<CustomQuickAction, 'id'>) => Promise<void>;
  updateCustomAction: (id: string, updates: Partial<CustomQuickAction>) => Promise<void>;
  deleteCustomAction: (id: string) => Promise<void>;
  getCustomActions: () => Promise<void>;
}

export function AssistantProvider({ children }: { children: ReactNode }) {
  const { user } = useUserContext();
  const [selectedAssistant, setSelectedAssistant] = useState<AssistantConfig | null>(null);
  const [customActions, setCustomActions] = useState<CustomQuickAction[]>([]);
  const [isLoadingActions, setIsLoadingActions] = useState(false);

  // Load user's custom actions
  const getCustomActions = async () => {
    if (!user) return;

    setIsLoadingActions(true);
    try {
      const { data, error } = await supabase
        .from('custom_actions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      setCustomActions(data || []);
    } catch (error) {
      console.error('Failed to load custom actions:', error);
    } finally {
      setIsLoadingActions(false);
    }
  };

  // Create new custom action
  const createCustomAction = async (action: Omit<CustomQuickAction, 'id'>) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('custom_actions')
        .insert({
          ...action,
          user_id: user.id,
        })
        .select()
        .single();

      if (error) throw error;
      
      setCustomActions(prev => [data, ...prev]);
    } catch (error) {
      console.error('Failed to create custom action:', error);
      throw error;
    }
  };

  // Update existing custom action
  const updateCustomAction = async (id: string, updates: Partial<CustomQuickAction>) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('custom_actions')
        .update(updates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      
      setCustomActions(prev => 
        prev.map(action => action.id === id ? data : action)
      );
    } catch (error) {
      console.error('Failed to update custom action:', error);
      throw error;
    }
  };

  // Delete custom action
  const deleteCustomAction = async (id: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('custom_actions')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;
      
      setCustomActions(prev => prev.filter(action => action.id !== id));
    } catch (error) {
      console.error('Failed to delete custom action:', error);
      throw error;
    }
  };

  // Load custom actions on mount
  useEffect(() => {
    if (user) {
      getCustomActions();
    }
  }, [user]);

  return (
    <AssistantContext.Provider value={{
      selectedAssistant,
      customActions,
      isLoadingActions,
      selectAssistant: setSelectedAssistant,
      updateAssistantConfig: (config) => setSelectedAssistant(prev => prev ? { ...prev, ...config } : null),
      createCustomAction,
      updateCustomAction,
      deleteCustomAction,
      getCustomActions
    }}>
      {children}
    </AssistantContext.Provider>
  );
}
```

#### Graph Context Provider
**Location**: `/apps/web/src/contexts/GraphContext.tsx`

Manages LangGraph client and streaming operations.

```typescript
interface GraphContextType {
  client: Client | null;
  isConnected: boolean;
  connectionError: string | null;
  
  // Stream management
  activeStreams: Map<string, AsyncGenerator>;
  startStream: (threadId: string, input: any) => Promise<string>;
  stopStream: (streamId: string) => void;
  
  // Graph operations
  invokeGraph: (threadId: string, input: any) => Promise<any>;
  getGraphState: (threadId: string) => Promise<any>;
}

export function GraphProvider({ children }: { children: ReactNode }) {
  const [client, setClient] = useState<Client | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [activeStreams, setActiveStreams] = useState<Map<string, AsyncGenerator>>(new Map());

  // Initialize LangGraph client
  useEffect(() => {
    const initializeClient = async () => {
      try {
        const langGraphClient = createClient({
          apiUrl: process.env.LANGGRAPH_API_URL || 'http://localhost:54367',
        });

        setClient(langGraphClient);
        setIsConnected(true);
        setConnectionError(null);
      } catch (error) {
        console.error('Failed to initialize LangGraph client:', error);
        setConnectionError('Failed to connect to LangGraph server');
        setIsConnected(false);
      }
    };

    initializeClient();
  }, []);

  // Start streaming response
  const startStream = async (threadId: string, input: any): Promise<string> => {
    if (!client) {
      throw new Error('LangGraph client not initialized');
    }

    const streamId = `${threadId}-${Date.now()}`;
    
    try {
      const stream = client.runs.stream(threadId, 'agent', {
        input,
        streamMode: 'values',
      });

      setActiveStreams(prev => new Map(prev).set(streamId, stream));
      return streamId;
    } catch (error) {
      console.error('Failed to start stream:', error);
      throw error;
    }
  };

  // Stop streaming
  const stopStream = (streamId: string) => {
    const stream = activeStreams.get(streamId);
    if (stream) {
      stream.return?.(undefined);
      setActiveStreams(prev => {
        const newMap = new Map(prev);
        newMap.delete(streamId);
        return newMap;
      });
    }
  };

  // Invoke graph directly
  const invokeGraph = async (threadId: string, input: any) => {
    if (!client) {
      throw new Error('LangGraph client not initialized');
    }

    try {
      const result = await client.runs.invoke(threadId, 'agent', {
        input,
      });

      return result;
    } catch (error) {
      console.error('Failed to invoke graph:', error);
      throw error;
    }
  };

  // Get current graph state
  const getGraphState = async (threadId: string) => {
    if (!client) {
      throw new Error('LangGraph client not initialized');
    }

    try {
      const state = await client.threads.getState(threadId);
      return state;
    } catch (error) {
      console.error('Failed to get graph state:', error);
      throw error;
    }
  };

  return (
    <GraphContext.Provider value={{
      client,
      isConnected,
      connectionError,
      activeStreams,
      startStream,
      stopStream,
      invokeGraph,
      getGraphState
    }}>
      {children}
    </GraphContext.Provider>
  );
}
```

### 2. Zustand Store Management

#### Main Application Store
**Location**: `/apps/web/src/hooks/useStore.tsx`

Handles UI state, caching, and local storage persistence.

```typescript
interface AppStore {
  // UI State
  sidebarOpen: boolean;
  theme: 'light' | 'dark' | 'system';
  fontSize: 'sm' | 'md' | 'lg';
  
  // Artifact State
  currentArtifact: ArtifactV3 | null;
  artifactHistory: ArtifactV3[];
  selectedText: string | null;
  selectedCode: CodeHighlight | null;
  
  // Cache Management
  cachedResponses: Map<string, CachedResponse>;
  cacheSize: number;
  maxCacheSize: number;
  
  // Actions
  setSidebarOpen: (open: boolean) => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  setFontSize: (size: 'sm' | 'md' | 'lg') => void;
  
  // Artifact Actions
  setCurrentArtifact: (artifact: ArtifactV3 | null) => void;
  addToArtifactHistory: (artifact: ArtifactV3) => void;
  setSelectedText: (text: string | null) => void;
  setSelectedCode: (code: CodeHighlight | null) => void;
  
  // Cache Actions
  addToCache: (key: string, response: CachedResponse) => void;
  getFromCache: (key: string) => CachedResponse | undefined;
  clearCache: () => void;
  pruneCache: () => void;
}

export const useStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Initial state
      sidebarOpen: true,
      theme: 'system',
      fontSize: 'md',
      currentArtifact: null,
      artifactHistory: [],
      selectedText: null,
      selectedCode: null,
      cachedResponses: new Map(),
      cacheSize: 0,
      maxCacheSize: 100,

      // UI Actions
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      setTheme: (theme) => set({ theme }),
      setFontSize: (fontSize) => set({ fontSize }),

      // Artifact Actions
      setCurrentArtifact: (artifact) => set({ currentArtifact: artifact }),
      
      addToArtifactHistory: (artifact) => set((state) => ({
        artifactHistory: [artifact, ...state.artifactHistory.slice(0, 49)] // Keep last 50
      })),
      
      setSelectedText: (text) => set({ selectedText: text }),
      setSelectedCode: (code) => set({ selectedCode: code }),

      // Cache Actions
      addToCache: (key, response) => set((state) => {
        const newCache = new Map(state.cachedResponses);
        newCache.set(key, response);
        
        // Prune cache if needed
        if (newCache.size > state.maxCacheSize) {
          const oldestKey = newCache.keys().next().value;
          newCache.delete(oldestKey);
        }
        
        return {
          cachedResponses: newCache,
          cacheSize: newCache.size
        };
      }),

      getFromCache: (key) => {
        const { cachedResponses } = get();
        return cachedResponses.get(key);
      },

      clearCache: () => set({
        cachedResponses: new Map(),
        cacheSize: 0
      }),

      pruneCache: () => set((state) => {
        const { cachedResponses, maxCacheSize } = state;
        if (cachedResponses.size <= maxCacheSize) return state;

        const newCache = new Map();
        const entries = Array.from(cachedResponses.entries());
        const keepEntries = entries.slice(-maxCacheSize);
        
        keepEntries.forEach(([key, value]) => {
          newCache.set(key, value);
        });

        return {
          cachedResponses: newCache,
          cacheSize: newCache.size
        };
      })
    }),
    {
      name: 'open-canvas-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        sidebarOpen: state.sidebarOpen,
        theme: state.theme,
        fontSize: state.fontSize,
        artifactHistory: state.artifactHistory.slice(0, 10), // Persist only recent history
      }),
    }
  )
);
```

#### Settings Store
**Location**: `/apps/web/src/hooks/useSettings.tsx`

Manages user preferences and application settings.

```typescript
interface SettingsStore {
  // Editor Settings
  editorTheme: 'light' | 'dark' | 'auto';
  fontSize: number;
  lineNumbers: boolean;
  wordWrap: boolean;
  minimap: boolean;
  
  // AI Settings
  defaultModel: ALL_MODEL_NAMES;
  temperature: number;
  maxTokens: number;
  streamingEnabled: boolean;
  
  // Feature Flags
  webSearchEnabled: boolean;
  memoryEnabled: boolean;
  customActionsEnabled: boolean;
  
  // Notification Settings
  notificationsEnabled: boolean;
  soundEnabled: boolean;
  
  // Actions
  updateEditorSettings: (settings: Partial<EditorSettings>) => void;
  updateAISettings: (settings: Partial<AISettings>) => void;
  updateFeatureFlags: (flags: Partial<FeatureFlags>) => void;
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => void;
  resetToDefaults: () => void;
}

export const useSettings = create<SettingsStore>()(
  persist(
    (set) => ({
      // Default settings
      editorTheme: 'auto',
      fontSize: 14,
      lineNumbers: true,
      wordWrap: true,
      minimap: true,
      
      defaultModel: DEFAULT_MODEL_NAME,
      temperature: 0.7,
      maxTokens: 2000,
      streamingEnabled: true,
      
      webSearchEnabled: true,
      memoryEnabled: true,
      customActionsEnabled: true,
      
      notificationsEnabled: true,
      soundEnabled: false,

      // Actions
      updateEditorSettings: (settings) => set(settings),
      updateAISettings: (settings) => set(settings),
      updateFeatureFlags: (flags) => set(flags),
      updateNotificationSettings: (settings) => set(settings),
      
      resetToDefaults: () => set({
        editorTheme: 'auto',
        fontSize: 14,
        lineNumbers: true,
        wordWrap: true,
        minimap: true,
        defaultModel: DEFAULT_MODEL_NAME,
        temperature: 0.7,
        maxTokens: 2000,
        streamingEnabled: true,
        webSearchEnabled: true,
        memoryEnabled: true,
        customActionsEnabled: true,
        notificationsEnabled: true,
        soundEnabled: false,
      })
    }),
    {
      name: 'open-canvas-settings',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
```

## Agent State Management

### 1. LangGraph State Structure

#### OpenCanvas Graph State
**Location**: `/apps/agents/src/open-canvas/state.ts`

The central state object that flows through all agent nodes.

```typescript
export const OpenCanvasGraphAnnotation = Annotation.Root({
  // Core conversation data
  messages: MessagesAnnotation,
  _messages: Annotation<BaseMessage[], Messages>({
    reducer: (state, update) => {
      const latestMsg = Array.isArray(update) ? update[update.length - 1] : update;
      
      if (isSummaryMessage(latestMsg)) {
        return messagesStateReducer([], update);
      }
      
      return messagesStateReducer(state, update);
    },
    default: () => [],
  }),
  
  // Artifact management
  artifact: Annotation<ArtifactV3>,
  highlightedCode: Annotation<CodeHighlight | undefined>,
  highlightedText: Annotation<TextHighlight | undefined>,
  
  // Processing controls
  next: Annotation<string | undefined>,
  language: Annotation<LanguageOptions | undefined>,
  artifactLength: Annotation<ArtifactLengthOptions | undefined>,
  readingLevel: Annotation<ReadingLevelOptions | undefined>,
  regenerateWithEmojis: Annotation<boolean | undefined>,
  
  // Code-specific controls
  addComments: Annotation<boolean | undefined>,
  addLogs: Annotation<boolean | undefined>,
  portLanguage: Annotation<ProgrammingLanguageOptions | undefined>,
  fixBugs: Annotation<boolean | undefined>,
  
  // Custom actions
  customQuickActionId: Annotation<string | undefined>,
  
  // Web search
  webSearchEnabled: Annotation<boolean | undefined>,
  webSearchResults: Annotation<SearchResult[] | undefined>,
});
```

#### State Reducers
```typescript
// Custom state reducers for complex state management
interface StateReducers {
  messagesReducer: (state: BaseMessage[], update: Messages) => BaseMessage[];
  artifactReducer: (state: ArtifactV3, update: ArtifactV3) => ArtifactV3;
  highlightReducer: (state: any, update: any) => any;
}

// Messages reducer with summarization support
export const messagesStateReducer = (
  state: BaseMessage[], 
  update: Messages
): BaseMessage[] => {
  if (Array.isArray(update)) {
    return [...state, ...update];
  }
  return [...state, update];
};

// Artifact reducer with version management
export const artifactStateReducer = (
  state: ArtifactV3, 
  update: ArtifactV3
): ArtifactV3 => {
  if (!update) return state;
  
  return {
    ...state,
    ...update,
    contents: update.contents || state.contents,
    currentIndex: update.currentIndex ?? state.currentIndex,
  };
};
```

### 2. State Persistence

#### Thread State Persistence
```typescript
// Thread state is automatically persisted by LangGraph
interface ThreadStatePersistence {
  threadId: string;
  state: OpenCanvasGraphState;
  metadata: ThreadMetadata;
  checkpointId: string;
  parentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Checkpoint management
export class CheckpointManager {
  async saveCheckpoint(threadId: string, state: OpenCanvasGraphState) {
    const checkpoint = {
      threadId,
      state,
      checkpointId: generateCheckpointId(),
      createdAt: new Date(),
    };
    
    // Save to LangGraph storage
    await this.storage.save(checkpoint);
    return checkpoint.checkpointId;
  }
  
  async loadCheckpoint(threadId: string, checkpointId?: string) {
    if (checkpointId) {
      return await this.storage.load(threadId, checkpointId);
    }
    
    // Load latest checkpoint
    return await this.storage.loadLatest(threadId);
  }
  
  async listCheckpoints(threadId: string) {
    return await this.storage.list(threadId);
  }
}
```

#### Memory State Persistence
```typescript
// Memory state managed by reflection agent
interface MemoryStatePersistence {
  userId: string;
  reflections: Reflections;
  styleRules: StyleRule[];
  userInsights: UserInsight[];
  lastUpdated: Date;
  version: number;
}

// Memory storage manager
export class MemoryStorageManager {
  async saveMemory(userId: string, memory: MemoryState) {
    const memoryRecord = {
      userId,
      ...memory,
      lastUpdated: new Date(),
      version: (await this.getMemoryVersion(userId)) + 1,
    };
    
    await this.storage.save(memoryRecord);
  }
  
  async loadMemory(userId: string): Promise<MemoryState | null> {
    return await this.storage.load(userId);
  }
  
  async updateMemory(userId: string, updates: Partial<MemoryState>) {
    const currentMemory = await this.loadMemory(userId);
    if (!currentMemory) return null;
    
    const updatedMemory = {
      ...currentMemory,
      ...updates,
      lastUpdated: new Date(),
      version: currentMemory.version + 1,
    };
    
    await this.storage.save(updatedMemory);
    return updatedMemory;
  }
}
```

## State Synchronization

### 1. Frontend-Agent Synchronization

#### State Bridge
```typescript
// Bridge between frontend and agent state
interface StateBridge {
  // Sync frontend state to agent
  syncToAgent: (frontendState: FrontendState) => AgentState;
  
  // Sync agent state to frontend
  syncToFrontend: (agentState: AgentState) => FrontendState;
  
  // Handle state conflicts
  resolveConflicts: (frontendState: FrontendState, agentState: AgentState) => ResolvedState;
}

export class OpenCanvasStateBridge implements StateBridge {
  syncToAgent(frontendState: FrontendState): AgentState {
    return {
      messages: frontendState.messages,
      artifact: frontendState.currentArtifact,
      highlightedCode: frontendState.selectedCode,
      highlightedText: frontendState.selectedText ? {
        fullMarkdown: frontendState.selectedText,
        markdownBlock: frontendState.selectedText,
        selectedText: frontendState.selectedText,
      } : undefined,
      // Map other frontend state to agent state
    };
  }
  
  syncToFrontend(agentState: AgentState): FrontendState {
    return {
      messages: agentState.messages,
      currentArtifact: agentState.artifact,
      selectedCode: agentState.highlightedCode,
      selectedText: agentState.highlightedText?.selectedText,
      // Map other agent state to frontend state
    };
  }
  
  resolveConflicts(frontendState: FrontendState, agentState: AgentState): ResolvedState {
    // Implement conflict resolution logic
    // Agent state typically takes precedence for artifacts
    // Frontend state takes precedence for UI preferences
    
    return {
      ...frontendState,
      currentArtifact: agentState.artifact, // Agent wins for artifacts
      messages: agentState.messages, // Agent wins for messages
    };
  }
}
```

### 2. Real-time State Updates

#### WebSocket State Sync
```typescript
// Real-time state synchronization
interface StateSync {
  subscribe: (callback: (state: any) => void) => () => void;
  publish: (state: any) => void;
  getState: () => any;
}

export class WebSocketStateSync implements StateSync {
  private ws: WebSocket | null = null;
  private subscribers: Set<(state: any) => void> = new Set();
  private currentState: any = null;
  
  constructor(private url: string) {
    this.connect();
  }
  
  private connect() {
    this.ws = new WebSocket(this.url);
    
    this.ws.onmessage = (event) => {
      const { type, data } = JSON.parse(event.data);
      
      if (type === 'state_update') {
        this.currentState = data;
        this.subscribers.forEach(callback => callback(data));
      }
    };
    
    this.ws.onclose = () => {
      // Reconnect after delay
      setTimeout(() => this.connect(), 5000);
    };
  }
  
  subscribe(callback: (state: any) => void) {
    this.subscribers.add(callback);
    
    return () => {
      this.subscribers.delete(callback);
    };
  }
  
  publish(state: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'state_update',
        data: state,
      }));
    }
  }
  
  getState() {
    return this.currentState;
  }
}
```

## State Optimization

### 1. Performance Optimizations

#### Memoization
```typescript
// Memoized state selectors
export const useOptimizedState = () => {
  const { currentArtifact, artifactHistory } = useStore();
  
  // Memoize expensive computations
  const artifactMetrics = useMemo(() => {
    if (!currentArtifact) return null;
    
    return {
      wordCount: currentArtifact.contents[currentArtifact.currentIndex]?.content.split(' ').length || 0,
      lineCount: currentArtifact.contents[currentArtifact.currentIndex]?.content.split('\n').length || 0,
      characterCount: currentArtifact.contents[currentArtifact.currentIndex]?.content.length || 0,
    };
  }, [currentArtifact]);
  
  // Memoize filtered history
  const recentHistory = useMemo(() => {
    return artifactHistory.slice(0, 10);
  }, [artifactHistory]);
  
  return {
    currentArtifact,
    artifactMetrics,
    recentHistory,
  };
};
```

#### Selective Updates
```typescript
// Selective state updates to minimize re-renders
export const useSelectiveUpdates = () => {
  const updateArtifact = useStore(state => state.setCurrentArtifact);
  const updateSelection = useStore(state => state.setSelectedText);
  
  // Debounced updates
  const debouncedUpdateArtifact = useMemo(
    () => debounce(updateArtifact, 300),
    [updateArtifact]
  );
  
  const debouncedUpdateSelection = useMemo(
    () => debounce(updateSelection, 100),
    [updateSelection]
  );
  
  return {
    updateArtifact: debouncedUpdateArtifact,
    updateSelection: debouncedUpdateSelection,
  };
};
```

### 2. Memory Management

#### State Cleanup
```typescript
// Automatic state cleanup
export const useStateCleanup = () => {
  const { clearCache, pruneCache } = useStore();
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearCache();
    };
  }, [clearCache]);
  
  // Periodic cleanup
  useEffect(() => {
    const interval = setInterval(() => {
      pruneCache();
    }, 60000); // Every minute
    
    return () => clearInterval(interval);
  }, [pruneCache]);
};
```

#### Memory Monitoring
```typescript
// Monitor memory usage
export const useMemoryMonitoring = () => {
  const [memoryUsage, setMemoryUsage] = useState<MemoryInfo | null>(null);
  
  useEffect(() => {
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        setMemoryUsage((performance as any).memory);
      }
    };
    
    updateMemoryUsage();
    const interval = setInterval(updateMemoryUsage, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  return memoryUsage;
};
```

## Testing State Management

### 1. State Testing Utilities
```typescript
// Testing utilities for state management
export const createTestStore = (initialState?: Partial<AppStore>) => {
  return create<AppStore>(() => ({
    ...defaultState,
    ...initialState,
  }));
};

export const renderWithStore = (
  ui: React.ReactElement,
  initialState?: Partial<AppStore>
) => {
  const TestStore = createTestStore(initialState);
  
  return render(
    <TestStore.Provider>
      {ui}
    </TestStore.Provider>
  );
};
```

### 2. Integration Testing
```typescript
// Integration tests for state synchronization
describe('State Synchronization', () => {
  it('should sync frontend state to agent', async () => {
    const bridge = new OpenCanvasStateBridge();
    const frontendState = createMockFrontendState();
    
    const agentState = bridge.syncToAgent(frontendState);
    
    expect(agentState.messages).toEqual(frontendState.messages);
    expect(agentState.artifact).toEqual(frontendState.currentArtifact);
  });
  
  it('should handle state conflicts', async () => {
    const bridge = new OpenCanvasStateBridge();
    const frontendState = createMockFrontendState();
    const agentState = createMockAgentState();
    
    const resolvedState = bridge.resolveConflicts(frontendState, agentState);
    
    expect(resolvedState.currentArtifact).toEqual(agentState.artifact);
  });
});
```

The state management system provides a robust foundation for maintaining consistency across the complex, multi-layered Open Canvas application, ensuring optimal performance and user experience.