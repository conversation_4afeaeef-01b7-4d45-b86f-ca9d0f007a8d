# Open Canvas - LLM Integration Documentation

## Multi-Provider LLM Architecture

Open Canvas implements a sophisticated multi-provider LLM integration system that supports multiple AI providers with flexible configuration, cost optimization, and failover capabilities. The system is designed to be provider-agnostic while leveraging the unique strengths of each LLM provider.

```mermaid
graph TB
    subgraph "Model Configuration"
        A[Model Registry] --> B[Provider Configs]
        B --> C[Model Metadata]
        C --> D[Capability Matrix]
        D --> E[Cost Optimization]
    end
    
    subgraph "Provider Integrations"
        F[OpenAI] --> G[GPT Models]
        H[Anthropic] --> I[Claude Models]
        J[Google] --> K[Gemini Models]
        L[Fireworks] --> M[Llama Models]
        N[Groq] --> O[Fast Inference]
        P[Ollama] --> Q[Local Models]
    end
    
    subgraph "Request Processing"
        R[Model Selector] --> S[Request Router]
        S --> T[Provider Client]
        T --> U[Response Handler]
        U --> V[Error Recovery]
    end
    
    A --> R
    F --> T
    H --> T
    J --> T
    L --> T
    N --> T
    P --> T
```

## Model Configuration System

### 1. Model Registry

#### Model Definitions
**Location**: `/packages/shared/src/models.ts`

The central registry of all supported models with their configurations.

```typescript
export interface ModelConfigurationParams {
  name: string;
  label: string;
  modelName?: string;
  config: CustomModelConfig;
  isNew: boolean;
}

export interface CustomModelConfig {
  provider: string;
  temperatureRange: {
    min: number;
    max: number;
    default: number;
    current: number;
  };
  maxTokens: {
    min: number;
    max: number;
    default: number;
    current: number;
  };
  azureConfig?: AzureOpenAIConfig;
}

export const ALL_MODELS: ModelConfigurationParams[] = [
  {
    name: "OpenAI GPT-4o Mini",
    label: "GPT-4o Mini 💨",
    modelName: "gpt-4o-mini",
    config: {
      provider: "openai",
      temperatureRange: {
        min: 0,
        max: 2,
        default: 0.7,
        current: 0.7,
      },
      maxTokens: {
        min: 1,
        max: 4096,
        default: 2000,
        current: 2000,
      },
    },
    isNew: false,
  },
  {
    name: "Anthropic Claude 3 Haiku",
    label: "Claude 3 Haiku 👤",
    modelName: "claude-3-haiku-20240307",
    config: {
      provider: "anthropic",
      temperatureRange: {
        min: 0,
        max: 1,
        default: 0.7,
        current: 0.7,
      },
      maxTokens: {
        min: 1,
        max: 4096,
        default: 2000,
        current: 2000,
      },
    },
    isNew: false,
  },
  {
    name: "Google Gemini 1.5 Flash",
    label: "Gemini 1.5 Flash ⚡",
    modelName: "gemini-1.5-flash-latest",
    config: {
      provider: "google",
      temperatureRange: {
        min: 0,
        max: 2,
        default: 0.7,
        current: 0.7,
      },
      maxTokens: {
        min: 1,
        max: 8192,
        default: 2000,
        current: 2000,
      },
    },
    isNew: false,
  },
  {
    name: "Fireworks Llama 3.1 70B",
    label: "Llama 3.1 70B 🦙",
    modelName: "accounts/fireworks/models/llama-v3p1-70b-instruct",
    config: {
      provider: "fireworks",
      temperatureRange: {
        min: 0,
        max: 2,
        default: 0.7,
        current: 0.7,
      },
      maxTokens: {
        min: 1,
        max: 4096,
        default: 2000,
        current: 2000,
      },
    },
    isNew: false,
  },
  {
    name: "Groq Llama 3.1 70B",
    label: "Groq Llama 3.1 70B 🚀",
    modelName: "llama-3.1-70b-versatile",
    config: {
      provider: "groq",
      temperatureRange: {
        min: 0,
        max: 2,
        default: 0.7,
        current: 0.7,
      },
      maxTokens: {
        min: 1,
        max: 8192,
        default: 2000,
        current: 2000,
      },
    },
    isNew: false,
  },
  {
    name: "Ollama Llama 3.3",
    label: "Ollama Llama 3.3 🏠",
    modelName: "llama3.3",
    config: {
      provider: "ollama",
      temperatureRange: {
        min: 0,
        max: 2,
        default: 0.7,
        current: 0.7,
      },
      maxTokens: {
        min: 1,
        max: 2048,
        default: 1000,
        current: 1000,
      },
    },
    isNew: false,
  },
];
```

### 2. Provider Client Factory

#### Model Client Factory
**Location**: `/apps/agents/src/utils.ts`

Creates appropriate LLM clients based on provider configuration.

```typescript
export function getModelConfig(
  customModelName: string,
  temperature: number = 0.7,
  maxTokens: number = 2000
): BaseLanguageModel {
  const modelConfig = ALL_MODELS.find(
    m => m.modelName === customModelName || m.name === customModelName
  );

  if (!modelConfig) {
    throw new Error(`Model ${customModelName} not found`);
  }

  switch (modelConfig.config.provider) {
    case "openai":
      return new ChatOpenAI({
        modelName: modelConfig.modelName || customModelName,
        temperature,
        maxTokens,
        streaming: true,
      });

    case "anthropic":
      return new ChatAnthropic({
        modelName: modelConfig.modelName || customModelName,
        temperature,
        maxTokens,
        streaming: true,
      });

    case "google":
      return new ChatGoogleGenerativeAI({
        modelName: modelConfig.modelName || customModelName,
        temperature,
        maxOutputTokens: maxTokens,
        streaming: true,
      });

    case "fireworks":
      return new ChatFireworks({
        modelName: modelConfig.modelName || customModelName,
        temperature,
        maxTokens,
        streaming: true,
      });

    case "groq":
      return new ChatGroq({
        modelName: modelConfig.modelName || customModelName,
        temperature,
        maxTokens,
        streaming: true,
      });

    case "ollama":
      return new ChatOllama({
        model: modelConfig.modelName || customModelName,
        temperature,
        numCtx: maxTokens,
        streaming: true,
        baseUrl: process.env.OLLAMA_API_URL || "http://localhost:11434",
      });

    case "azure_openai":
      return new ChatOpenAI({
        modelName: modelConfig.modelName || customModelName,
        temperature,
        maxTokens,
        streaming: true,
        azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY,
        azureOpenAIApiInstanceName: process.env.AZURE_OPENAI_API_INSTANCE_NAME,
        azureOpenAIApiDeploymentName: process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME,
        azureOpenAIApiVersion: process.env.AZURE_OPENAI_API_VERSION,
      });

    default:
      throw new Error(`Unsupported provider: ${modelConfig.config.provider}`);
  }
}
```

### 3. Provider-Specific Configurations

#### OpenAI Configuration
```typescript
interface OpenAIConfig {
  apiKey: string;
  organization?: string;
  baseURL?: string;
  timeout?: number;
  maxRetries?: number;
  headers?: Record<string, string>;
}

export class OpenAIProvider {
  private client: ChatOpenAI;

  constructor(config: OpenAIConfig) {
    this.client = new ChatOpenAI({
      openAIApiKey: config.apiKey,
      organization: config.organization,
      configuration: {
        baseURL: config.baseURL,
        timeout: config.timeout || 60000,
        maxRetries: config.maxRetries || 3,
        defaultHeaders: config.headers,
      },
    });
  }

  async generateResponse(messages: BaseMessage[], options: GenerationOptions) {
    return await this.client.invoke(messages, {
      temperature: options.temperature,
      maxTokens: options.maxTokens,
      streaming: options.streaming,
    });
  }

  async streamResponse(messages: BaseMessage[], options: GenerationOptions) {
    return this.client.stream(messages, {
      temperature: options.temperature,
      maxTokens: options.maxTokens,
    });
  }
}
```

#### Anthropic Configuration
```typescript
interface AnthropicConfig {
  apiKey: string;
  baseURL?: string;
  timeout?: number;
  maxRetries?: number;
}

export class AnthropicProvider {
  private client: ChatAnthropic;

  constructor(config: AnthropicConfig) {
    this.client = new ChatAnthropic({
      anthropicApiKey: config.apiKey,
      clientOptions: {
        baseURL: config.baseURL,
        timeout: config.timeout || 60000,
        maxRetries: config.maxRetries || 3,
      },
    });
  }

  async generateResponse(messages: BaseMessage[], options: GenerationOptions) {
    return await this.client.invoke(messages, {
      temperature: options.temperature,
      maxTokens: options.maxTokens,
      streaming: options.streaming,
    });
  }

  async streamResponse(messages: BaseMessage[], options: GenerationOptions) {
    return this.client.stream(messages, {
      temperature: options.temperature,
      maxTokens: options.maxTokens,
    });
  }
}
```

#### Google Configuration
```typescript
interface GoogleConfig {
  apiKey: string;
  baseURL?: string;
  timeout?: number;
  maxRetries?: number;
}

export class GoogleProvider {
  private client: ChatGoogleGenerativeAI;

  constructor(config: GoogleConfig) {
    this.client = new ChatGoogleGenerativeAI({
      apiKey: config.apiKey,
      clientOptions: {
        baseURL: config.baseURL,
        timeout: config.timeout || 60000,
        maxRetries: config.maxRetries || 3,
      },
    });
  }

  async generateResponse(messages: BaseMessage[], options: GenerationOptions) {
    return await this.client.invoke(messages, {
      temperature: options.temperature,
      maxOutputTokens: options.maxTokens,
      streaming: options.streaming,
    });
  }

  async streamResponse(messages: BaseMessage[], options: GenerationOptions) {
    return this.client.stream(messages, {
      temperature: options.temperature,
      maxOutputTokens: options.maxTokens,
    });
  }
}
```

#### Ollama Configuration
```typescript
interface OllamaConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export class OllamaProvider {
  private client: ChatOllama;

  constructor(config: OllamaConfig) {
    this.client = new ChatOllama({
      baseUrl: config.baseURL,
      timeout: config.timeout || 120000, // Longer timeout for local models
      headers: config.headers,
    });
  }

  async generateResponse(messages: BaseMessage[], options: GenerationOptions) {
    return await this.client.invoke(messages, {
      temperature: options.temperature,
      numCtx: options.maxTokens,
      streaming: options.streaming,
    });
  }

  async streamResponse(messages: BaseMessage[], options: GenerationOptions) {
    return this.client.stream(messages, {
      temperature: options.temperature,
      numCtx: options.maxTokens,
    });
  }

  async listModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.client.baseUrl}/api/tags`);
      const data = await response.json();
      return data.models?.map((model: any) => model.name) || [];
    } catch (error) {
      console.error('Failed to list Ollama models:', error);
      return [];
    }
  }
}
```

## Model Selection Logic

### 1. Intelligent Model Selection

#### Model Selector
```typescript
interface ModelSelectionCriteria {
  taskType: 'generation' | 'editing' | 'analysis' | 'coding';
  priority: 'speed' | 'quality' | 'cost';
  contentLength: 'short' | 'medium' | 'long';
  complexity: 'simple' | 'medium' | 'complex';
  userPreference?: string;
}

export class ModelSelector {
  private models: ModelConfigurationParams[];
  private performanceMetrics: Map<string, PerformanceMetrics>;

  constructor(models: ModelConfigurationParams[]) {
    this.models = models;
    this.performanceMetrics = new Map();
  }

  selectOptimalModel(criteria: ModelSelectionCriteria): ModelConfigurationParams {
    // Filter available models
    const availableModels = this.models.filter(model => 
      this.isModelAvailable(model)
    );

    // Apply user preference if specified
    if (criteria.userPreference) {
      const preferredModel = availableModels.find(
        model => model.modelName === criteria.userPreference
      );
      if (preferredModel) return preferredModel;
    }

    // Score models based on criteria
    const scoredModels = availableModels.map(model => ({
      model,
      score: this.scoreModel(model, criteria)
    }));

    // Sort by score and return best match
    scoredModels.sort((a, b) => b.score - a.score);
    return scoredModels[0].model;
  }

  private scoreModel(model: ModelConfigurationParams, criteria: ModelSelectionCriteria): number {
    let score = 0;
    const metrics = this.performanceMetrics.get(model.modelName!) || this.getDefaultMetrics();

    // Task type scoring
    switch (criteria.taskType) {
      case 'generation':
        score += model.config.provider === 'openai' ? 8 : 7;
        break;
      case 'editing':
        score += model.config.provider === 'anthropic' ? 9 : 7;
        break;
      case 'analysis':
        score += model.config.provider === 'google' ? 8 : 6;
        break;
      case 'coding':
        score += model.config.provider === 'openai' ? 9 : 7;
        break;
    }

    // Priority scoring
    switch (criteria.priority) {
      case 'speed':
        score += model.config.provider === 'groq' ? 10 : 
                 model.config.provider === 'fireworks' ? 8 : 6;
        break;
      case 'quality':
        score += model.config.provider === 'openai' ? 9 :
                 model.config.provider === 'anthropic' ? 8 : 6;
        break;
      case 'cost':
        score += model.config.provider === 'fireworks' ? 9 :
                 model.config.provider === 'groq' ? 8 : 5;
        break;
    }

    // Content length scoring
    switch (criteria.contentLength) {
      case 'short':
        score += model.config.maxTokens.max < 2000 ? 8 : 6;
        break;
      case 'medium':
        score += model.config.maxTokens.max >= 2000 && model.config.maxTokens.max < 4000 ? 8 : 6;
        break;
      case 'long':
        score += model.config.maxTokens.max >= 4000 ? 8 : 5;
        break;
    }

    // Performance metrics
    score += metrics.averageResponseTime < 5000 ? 2 : 0;
    score += metrics.successRate > 0.95 ? 2 : 0;

    return score;
  }

  private isModelAvailable(model: ModelConfigurationParams): boolean {
    switch (model.config.provider) {
      case 'openai':
        return !!process.env.OPENAI_API_KEY;
      case 'anthropic':
        return !!process.env.ANTHROPIC_API_KEY;
      case 'google':
        return !!process.env.GOOGLE_GENAI_API_KEY;
      case 'fireworks':
        return !!process.env.FIREWORKS_API_KEY;
      case 'groq':
        return !!process.env.GROQ_API_KEY;
      case 'ollama':
        return !!process.env.OLLAMA_API_URL;
      case 'azure_openai':
        return !!(process.env.AZURE_OPENAI_API_KEY && process.env.AZURE_OPENAI_API_INSTANCE_NAME);
      default:
        return false;
    }
  }

  private getDefaultMetrics(): PerformanceMetrics {
    return {
      averageResponseTime: 5000,
      successRate: 0.95,
      errorRate: 0.05,
      throughput: 1,
    };
  }
}
```

### 2. Fallback and Load Balancing

#### Fallback Strategy
```typescript
interface FallbackConfig {
  primaryModel: string;
  fallbackModels: string[];
  maxRetries: number;
  timeoutMs: number;
}

export class FallbackManager {
  private fallbackConfigs: Map<string, FallbackConfig> = new Map();
  private modelHealth: Map<string, ModelHealth> = new Map();

  constructor() {
    this.initializeFallbacks();
    this.startHealthChecks();
  }

  private initializeFallbacks() {
    // OpenAI fallbacks
    this.fallbackConfigs.set('openai', {
      primaryModel: 'gpt-4o-mini',
      fallbackModels: ['claude-3-haiku-20240307', 'gemini-1.5-flash-latest'],
      maxRetries: 3,
      timeoutMs: 30000,
    });

    // Anthropic fallbacks
    this.fallbackConfigs.set('anthropic', {
      primaryModel: 'claude-3-haiku-20240307',
      fallbackModels: ['gpt-4o-mini', 'gemini-1.5-flash-latest'],
      maxRetries: 3,
      timeoutMs: 30000,
    });

    // Google fallbacks
    this.fallbackConfigs.set('google', {
      primaryModel: 'gemini-1.5-flash-latest',
      fallbackModels: ['gpt-4o-mini', 'claude-3-haiku-20240307'],
      maxRetries: 3,
      timeoutMs: 30000,
    });
  }

  async executeWithFallback(
    provider: string,
    messages: BaseMessage[],
    options: GenerationOptions
  ): Promise<BaseMessage> {
    const config = this.fallbackConfigs.get(provider);
    if (!config) {
      throw new Error(`No fallback configuration for provider: ${provider}`);
    }

    const modelsToTry = [config.primaryModel, ...config.fallbackModels];
    let lastError: Error | null = null;

    for (const modelName of modelsToTry) {
      try {
        const model = getModelConfig(modelName, options.temperature, options.maxTokens);
        
        // Check model health
        const health = this.modelHealth.get(modelName);
        if (health && health.status === 'unhealthy') {
          console.warn(`Skipping unhealthy model: ${modelName}`);
          continue;
        }

        // Attempt generation with timeout
        const result = await this.withTimeout(
          model.invoke(messages, options),
          config.timeoutMs
        );

        // Update health on success
        this.updateModelHealth(modelName, true);
        return result;

      } catch (error) {
        console.error(`Model ${modelName} failed:`, error);
        lastError = error as Error;
        
        // Update health on failure
        this.updateModelHealth(modelName, false);
        
        // Continue to next model
        continue;
      }
    }

    throw new Error(`All fallback models failed. Last error: ${lastError?.message}`);
  }

  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  private updateModelHealth(modelName: string, success: boolean) {
    const health = this.modelHealth.get(modelName) || {
      status: 'healthy',
      successCount: 0,
      errorCount: 0,
      lastCheck: new Date(),
    };

    if (success) {
      health.successCount++;
      health.status = 'healthy';
    } else {
      health.errorCount++;
      
      // Mark as unhealthy if error rate is high
      const totalRequests = health.successCount + health.errorCount;
      const errorRate = health.errorCount / totalRequests;
      
      if (errorRate > 0.5 && totalRequests > 5) {
        health.status = 'unhealthy';
      }
    }

    health.lastCheck = new Date();
    this.modelHealth.set(modelName, health);
  }

  private startHealthChecks() {
    setInterval(() => {
      this.performHealthChecks();
    }, 300000); // Check every 5 minutes
  }

  private async performHealthChecks() {
    for (const [modelName, health] of this.modelHealth) {
      if (health.status === 'unhealthy') {
        try {
          // Attempt a simple health check
          const model = getModelConfig(modelName, 0.1, 10);
          await model.invoke([new HumanMessage('Test')]);
          
          // Reset health on successful check
          health.status = 'healthy';
          health.errorCount = 0;
          health.successCount = 1;
          
          console.log(`Model ${modelName} is healthy again`);
        } catch (error) {
          console.log(`Model ${modelName} still unhealthy`);
        }
      }
    }
  }
}
```

## Response Processing

### 1. Streaming Response Handler

#### Stream Processor
```typescript
interface StreamingOptions {
  onChunk: (chunk: string) => void;
  onComplete: (fullResponse: string) => void;
  onError: (error: Error) => void;
  bufferSize?: number;
}

export class StreamingProcessor {
  private activeStreams: Map<string, AbortController> = new Map();

  async processStream(
    model: BaseLanguageModel,
    messages: BaseMessage[],
    options: StreamingOptions
  ): Promise<string> {
    const streamId = generateStreamId();
    const controller = new AbortController();
    this.activeStreams.set(streamId, controller);

    try {
      let fullResponse = '';
      const stream = await model.stream(messages, {
        signal: controller.signal,
      });

      for await (const chunk of stream) {
        if (controller.signal.aborted) {
          break;
        }

        const content = chunk.content;
        if (typeof content === 'string') {
          fullResponse += content;
          options.onChunk(content);
        }
      }

      if (!controller.signal.aborted) {
        options.onComplete(fullResponse);
      }

      return fullResponse;
    } catch (error) {
      if (!controller.signal.aborted) {
        options.onError(error as Error);
      }
      throw error;
    } finally {
      this.activeStreams.delete(streamId);
    }
  }

  stopStream(streamId: string) {
    const controller = this.activeStreams.get(streamId);
    if (controller) {
      controller.abort();
      this.activeStreams.delete(streamId);
    }
  }

  stopAllStreams() {
    for (const [streamId, controller] of this.activeStreams) {
      controller.abort();
    }
    this.activeStreams.clear();
  }
}
```

### 2. Response Validation

#### Response Validator
```typescript
interface ValidationRule {
  name: string;
  validate: (response: string) => boolean;
  message: string;
}

export class ResponseValidator {
  private rules: ValidationRule[] = [];

  constructor() {
    this.initializeRules();
  }

  private initializeRules() {
    this.rules = [
      {
        name: 'minimum_length',
        validate: (response) => response.length >= 10,
        message: 'Response too short',
      },
      {
        name: 'maximum_length',
        validate: (response) => response.length <= 50000,
        message: 'Response too long',
      },
      {
        name: 'no_harmful_content',
        validate: (response) => !this.containsHarmfulContent(response),
        message: 'Response contains harmful content',
      },
      {
        name: 'valid_format',
        validate: (response) => this.isValidFormat(response),
        message: 'Response format is invalid',
      },
    ];
  }

  validate(response: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const rule of this.rules) {
      try {
        if (!rule.validate(response)) {
          errors.push(rule.message);
        }
      } catch (error) {
        warnings.push(`Validation rule "${rule.name}" failed: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private containsHarmfulContent(response: string): boolean {
    // Implement content filtering logic
    const harmfulPatterns = [
      /violence/i,
      /hate\s+speech/i,
      /discrimination/i,
      // Add more patterns as needed
    ];

    return harmfulPatterns.some(pattern => pattern.test(response));
  }

  private isValidFormat(response: string): boolean {
    // Check for valid encoding and structure
    try {
      // Test for valid UTF-8
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      const encoded = encoder.encode(response);
      const decoded = decoder.decode(encoded);
      
      return decoded === response;
    } catch {
      return false;
    }
  }
}
```

## Performance Monitoring

### 1. Model Performance Tracking

#### Performance Monitor
```typescript
interface PerformanceMetrics {
  modelName: string;
  averageResponseTime: number;
  successRate: number;
  errorRate: number;
  throughput: number;
  lastUpdated: Date;
}

export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private requestHistory: Map<string, RequestRecord[]> = new Map();

  recordRequest(modelName: string, startTime: number, endTime: number, success: boolean) {
    const responseTime = endTime - startTime;
    
    // Update request history
    const history = this.requestHistory.get(modelName) || [];
    history.push({
      timestamp: new Date(),
      responseTime,
      success,
    });
    
    // Keep only last 100 requests
    if (history.length > 100) {
      history.shift();
    }
    
    this.requestHistory.set(modelName, history);
    
    // Calculate metrics
    this.updateMetrics(modelName, history);
  }

  private updateMetrics(modelName: string, history: RequestRecord[]) {
    const successfulRequests = history.filter(r => r.success);
    const failedRequests = history.filter(r => !r.success);
    
    const metrics: PerformanceMetrics = {
      modelName,
      averageResponseTime: successfulRequests.length > 0 
        ? successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length
        : 0,
      successRate: history.length > 0 ? successfulRequests.length / history.length : 0,
      errorRate: history.length > 0 ? failedRequests.length / history.length : 0,
      throughput: this.calculateThroughput(history),
      lastUpdated: new Date(),
    };
    
    this.metrics.set(modelName, metrics);
  }

  private calculateThroughput(history: RequestRecord[]): number {
    if (history.length < 2) return 0;
    
    const timeWindow = 60000; // 1 minute
    const now = Date.now();
    const recentRequests = history.filter(
      r => now - r.timestamp.getTime() < timeWindow
    );
    
    return recentRequests.length; // Requests per minute
  }

  getMetrics(modelName: string): PerformanceMetrics | undefined {
    return this.metrics.get(modelName);
  }

  getAllMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }

  generateReport(): PerformanceReport {
    const allMetrics = this.getAllMetrics();
    
    return {
      timestamp: new Date(),
      totalModels: allMetrics.length,
      averageResponseTime: allMetrics.reduce((sum, m) => sum + m.averageResponseTime, 0) / allMetrics.length,
      overallSuccessRate: allMetrics.reduce((sum, m) => sum + m.successRate, 0) / allMetrics.length,
      modelMetrics: allMetrics,
      recommendations: this.generateRecommendations(allMetrics),
    };
  }

  private generateRecommendations(metrics: PerformanceMetrics[]): string[] {
    const recommendations: string[] = [];
    
    // Find models with high error rates
    const highErrorModels = metrics.filter(m => m.errorRate > 0.1);
    if (highErrorModels.length > 0) {
      recommendations.push(`Consider reducing usage of models with high error rates: ${highErrorModels.map(m => m.modelName).join(', ')}`);
    }
    
    // Find slow models
    const slowModels = metrics.filter(m => m.averageResponseTime > 10000);
    if (slowModels.length > 0) {
      recommendations.push(`Consider optimizing or replacing slow models: ${slowModels.map(m => m.modelName).join(', ')}`);
    }
    
    // Find best performing models
    const bestModels = metrics
      .filter(m => m.successRate > 0.95 && m.averageResponseTime < 5000)
      .sort((a, b) => b.successRate - a.successRate);
      
    if (bestModels.length > 0) {
      recommendations.push(`Consider increasing usage of high-performing models: ${bestModels.slice(0, 3).map(m => m.modelName).join(', ')}`);
    }
    
    return recommendations;
  }
}
```

## Cost Optimization

### 1. Cost Tracking

#### Cost Calculator
```typescript
interface CostConfig {
  inputTokenPrice: number;  // per 1K tokens
  outputTokenPrice: number; // per 1K tokens
  currency: string;
}

export class CostCalculator {
  private costConfigs: Map<string, CostConfig> = new Map();

  constructor() {
    this.initializeCostConfigs();
  }

  private initializeCostConfigs() {
    this.costConfigs.set('gpt-4o-mini', {
      inputTokenPrice: 0.00015,
      outputTokenPrice: 0.0006,
      currency: 'USD',
    });

    this.costConfigs.set('claude-3-haiku-20240307', {
      inputTokenPrice: 0.00025,
      outputTokenPrice: 0.00125,
      currency: 'USD',
    });

    this.costConfigs.set('gemini-1.5-flash-latest', {
      inputTokenPrice: 0.0000375,
      outputTokenPrice: 0.00015,
      currency: 'USD',
    });

    this.costConfigs.set('llama-v3p1-70b-instruct', {
      inputTokenPrice: 0.0009,
      outputTokenPrice: 0.0009,
      currency: 'USD',
    });

    this.costConfigs.set('llama-3.1-70b-versatile', {
      inputTokenPrice: 0.00059,
      outputTokenPrice: 0.00079,
      currency: 'USD',
    });
  }

  calculateCost(modelName: string, inputTokens: number, outputTokens: number): number {
    const config = this.costConfigs.get(modelName);
    if (!config) {
      return 0; // Free models (like Ollama)
    }

    const inputCost = (inputTokens / 1000) * config.inputTokenPrice;
    const outputCost = (outputTokens / 1000) * config.outputTokenPrice;

    return inputCost + outputCost;
  }

  estimateCost(modelName: string, inputLength: number, estimatedOutputLength: number): number {
    // Rough token estimation (4 chars = 1 token)
    const inputTokens = Math.ceil(inputLength / 4);
    const outputTokens = Math.ceil(estimatedOutputLength / 4);

    return this.calculateCost(modelName, inputTokens, outputTokens);
  }

  compareModelCosts(
    models: string[],
    inputLength: number,
    estimatedOutputLength: number
  ): Array<{ model: string; cost: number; ranking: number }> {
    const costs = models.map(model => ({
      model,
      cost: this.estimateCost(model, inputLength, estimatedOutputLength),
    }));

    // Sort by cost (ascending)
    costs.sort((a, b) => a.cost - b.cost);

    // Add ranking
    return costs.map((item, index) => ({
      ...item,
      ranking: index + 1,
    }));
  }
}
```

## Error Handling and Recovery

### 1. Error Classification

#### Error Handler
```typescript
export enum ErrorType {
  RATE_LIMIT = 'rate_limit',
  AUTHENTICATION = 'authentication',
  TIMEOUT = 'timeout',
  INVALID_REQUEST = 'invalid_request',
  SERVICE_UNAVAILABLE = 'service_unavailable',
  QUOTA_EXCEEDED = 'quota_exceeded',
  UNKNOWN = 'unknown',
}

export class LLMErrorHandler {
  classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase();

    if (message.includes('rate limit') || message.includes('429')) {
      return ErrorType.RATE_LIMIT;
    }

    if (message.includes('authentication') || message.includes('401')) {
      return ErrorType.AUTHENTICATION;
    }

    if (message.includes('timeout') || message.includes('timed out')) {
      return ErrorType.TIMEOUT;
    }

    if (message.includes('invalid') || message.includes('400')) {
      return ErrorType.INVALID_REQUEST;
    }

    if (message.includes('unavailable') || message.includes('503')) {
      return ErrorType.SERVICE_UNAVAILABLE;
    }

    if (message.includes('quota') || message.includes('exceeded')) {
      return ErrorType.QUOTA_EXCEEDED;
    }

    return ErrorType.UNKNOWN;
  }

  getRetryStrategy(errorType: ErrorType): RetryStrategy {
    switch (errorType) {
      case ErrorType.RATE_LIMIT:
        return {
          shouldRetry: true,
          delay: 60000, // 1 minute
          maxRetries: 3,
          backoffMultiplier: 2,
        };

      case ErrorType.TIMEOUT:
        return {
          shouldRetry: true,
          delay: 1000, // 1 second
          maxRetries: 2,
          backoffMultiplier: 1.5,
        };

      case ErrorType.SERVICE_UNAVAILABLE:
        return {
          shouldRetry: true,
          delay: 5000, // 5 seconds
          maxRetries: 5,
          backoffMultiplier: 2,
        };

      case ErrorType.AUTHENTICATION:
      case ErrorType.INVALID_REQUEST:
      case ErrorType.QUOTA_EXCEEDED:
        return {
          shouldRetry: false,
          delay: 0,
          maxRetries: 0,
          backoffMultiplier: 1,
        };

      default:
        return {
          shouldRetry: true,
          delay: 2000, // 2 seconds
          maxRetries: 1,
          backoffMultiplier: 1,
        };
    }
  }

  async handleError(error: Error, context: ErrorContext): Promise<void> {
    const errorType = this.classifyError(error);
    const strategy = this.getRetryStrategy(errorType);

    // Log error with context
    console.error(`LLM Error [${errorType}]:`, {
      message: error.message,
      stack: error.stack,
      context,
      strategy,
    });

    // Send to monitoring service
    await this.sendErrorToMonitoring(errorType, error, context);

    // Update model health
    if (context.modelName) {
      await this.updateModelHealth(context.modelName, false);
    }
  }

  private async sendErrorToMonitoring(
    errorType: ErrorType,
    error: Error,
    context: ErrorContext
  ): Promise<void> {
    // Implementation depends on monitoring service
    // Could be LangSmith, Sentry, custom logging, etc.
  }

  private async updateModelHealth(modelName: string, isHealthy: boolean): Promise<void> {
    // Update model health tracking
    // This would integrate with the FallbackManager
  }
}
```

The multi-provider LLM integration system provides Open Canvas with flexible, reliable, and cost-effective AI capabilities, ensuring optimal performance across different use cases while maintaining robustness through comprehensive error handling and fallback mechanisms.