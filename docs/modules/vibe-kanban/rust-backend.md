# Vibe Kanban - Rust Backend Architecture

## Backend Architecture Overview

The **Rust Backend** serves as the core engine for AI agent orchestration, providing high-performance, memory-safe, and concurrent processing capabilities. Built with modern Rust ecosystem tools, it delivers enterprise-grade reliability and performance for managing multiple AI coding agents.

## Core Framework Stack

### Web Server Architecture
- **Framework**: Axum (v0.7+) - Modern, ergonomic web framework
- **Async Runtime**: Tokio - High-performance async runtime
- **HTTP Middleware**: Tower-HTTP for request/response processing
- **Port Configuration**: Default 3001, configurable via environment

### Key Architecture Benefits
- **Memory Safety**: Rust's ownership system prevents memory leaks
- **Concurrency**: Tokio enables efficient concurrent processing
- **Performance**: Zero-cost abstractions and optimized compilation
- **Reliability**: Compile-time error detection and type safety

## Application Structure

### Main Application Entry
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/main.rs`
- **Responsibilities**:
  - Application bootstrap and initialization
  - Server configuration and startup
  - Signal handling for graceful shutdown
  - Error handling and logging setup

### Library Interface
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/lib.rs`
- **Responsibilities**:
  - Public API definitions
  - Module organization and re-exports
  - Library initialization functions
  - Type definitions and utilities

### Application State Management
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/app_state.rs`
- **Responsibilities**:
  - Shared application state
  - Database connection pool
  - Configuration management
  - Resource coordination

```rust
#[derive(Clone)]
pub struct AppState {
    pub db_pool: SqlitePool,
    pub config: Arc<Config>,
    pub executor_manager: Arc<ExecutorManager>,
    pub notification_service: Arc<NotificationService>,
}
```

## Database Architecture

### Database Technology
- **Database**: SQLite (embedded, file-based)
- **ORM**: SQLX (v0.8.6) with compile-time query verification
- **Features**: runtime-tokio-rustls, sqlite, chrono, uuid
- **Migration System**: SQL-based migrations with versioning

### Database Schema
- **Location**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/migrations/`
- **Migration Files**:
  - `20250617183714_init.sql` - Initial schema
  - `20250620212427_execution_processes.sql` - Process tracking
  - `20250620214100_remove_stdout_stderr_from_task_attempts.sql` - Schema optimization
  - `20250621120000_relate_activities_to_execution_processes.sql` - Activity relationships
  - `20250623120000_executor_sessions.sql` - Session management
  - `20250623130000_add_executor_type_to_execution_processes.sql` - Executor typing
  - `20250625000000_add_dev_script_to_projects.sql` - Development scripts
  - `20250701000000_add_branch_to_task_attempts.sql` - Branch tracking
  - `20250701000001_add_pr_tracking_to_task_attempts.sql` - PR integration
  - `20250701120000_add_assistant_message_to_executor_sessions.sql` - Message tracking
  - `20250708000000_add_base_branch_to_task_attempts.sql` - Base branch support
  - `20250709000000_add_worktree_deleted_flag.sql` - Worktree management
  - `20250710000000_add_setup_completion.sql` - Setup tracking

### Core Database Tables
1. **projects** - Project configuration and metadata
2. **tasks** - Task definitions and status tracking
3. **task_attempts** - Execution attempts with agent assignments
4. **execution_processes** - Process tracking and monitoring
5. **task_attempt_activities** - Activity logging and timeline
6. **executor_sessions** - AI agent session management

## Data Models

### Task Management Models
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/task.rs`
- **Key Features**:
  - Type-safe task operations
  - Status lifecycle management
  - Attempt relationship tracking
  - Performance-optimized queries

### Project Management Models
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/project.rs`
- **Key Features**:
  - Project configuration management
  - Development script integration
  - Git repository association
  - Team member management

### Execution Tracking Models
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/task_attempt.rs`
- **Key Features**:
  - Attempt lifecycle tracking
  - Branch and PR management
  - Execution history
  - Performance metrics

### Session Management Models
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/executor_session.rs`
- **Key Features**:
  - Session lifecycle management
  - Agent state persistence
  - Context preservation
  - Resource tracking

### Configuration Models
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/config.rs`
- **Key Features**:
  - System configuration management
  - Environment-specific settings
  - Agent configuration
  - User preferences

## API Routes and Endpoints

### Route Organization
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/mod.rs`
- **Structure**: Modular route organization with dedicated modules

### Task Management Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/tasks.rs`
- **Endpoints**:
  - `GET /api/tasks` - List tasks with filtering
  - `POST /api/tasks` - Create new task
  - `GET /api/tasks/:id` - Get task details
  - `PUT /api/tasks/:id` - Update task
  - `DELETE /api/tasks/:id` - Delete task
  - `POST /api/tasks/:id/start` - Start task execution

### Project Management Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/projects.rs`
- **Endpoints**:
  - `GET /api/projects` - List projects
  - `POST /api/projects` - Create new project
  - `GET /api/projects/:id` - Get project details
  - `PUT /api/projects/:id` - Update project
  - `DELETE /api/projects/:id` - Delete project

### Task Attempt Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/task_attempts.rs`
- **Endpoints**:
  - `GET /api/task-attempts` - List attempts
  - `POST /api/task-attempts` - Create new attempt
  - `GET /api/task-attempts/:id` - Get attempt details
  - `PUT /api/task-attempts/:id` - Update attempt
  - `POST /api/task-attempts/:id/cancel` - Cancel attempt

### Authentication Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/auth.rs`
- **Endpoints**:
  - `POST /api/auth/login` - User authentication
  - `POST /api/auth/logout` - User logout
  - `GET /api/auth/me` - Get current user
  - `POST /api/auth/refresh` - Refresh token

### Configuration Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/config.rs`
- **Endpoints**:
  - `GET /api/config` - Get system configuration
  - `PUT /api/config` - Update configuration
  - `GET /api/config/agents` - Get agent configuration
  - `PUT /api/config/agents` - Update agent configuration

### Filesystem Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/filesystem.rs`
- **Endpoints**:
  - `GET /api/fs/browse` - Browse filesystem
  - `GET /api/fs/files/:path` - Get file content
  - `POST /api/fs/files/:path` - Create/update file
  - `DELETE /api/fs/files/:path` - Delete file

### Health Check Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/health.rs`
- **Endpoints**:
  - `GET /api/health` - Basic health check
  - `GET /api/health/detailed` - Detailed health status
  - `GET /api/health/database` - Database health
  - `GET /api/health/agents` - Agent health status

## AI Agent Executor System

### Executor Framework
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executor.rs`
- **Key Features**:
  - Standardized executor interface
  - Conversation normalization
  - Process management
  - Error handling

### Executor Implementations

#### Claude Code Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/claude.rs`
- **Integration**: Claude Code CLI integration
- **Features**:
  - Advanced reasoning capabilities
  - Multi-step workflow support
  - Context-aware responses
  - Tool usage tracking

#### Gemini CLI Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/gemini.rs`
- **Integration**: Google Gemini CLI integration
- **Features**:
  - Fast execution times
  - Multi-modal support
  - Efficient resource usage
  - Real-time processing

#### Amp Framework Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/amp.rs`
- **Integration**: Amp framework integration
- **Features**:
  - Specialized transformations
  - Performance optimization
  - Framework-specific features
  - Custom workflow support

#### OpenCode Platform Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/opencode.rs`
- **Integration**: OpenCode platform integration
- **Features**:
  - Open-source integration
  - Community-driven solutions
  - Extensible architecture
  - Custom agent support

#### Echo Executor (Testing)
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/echo.rs`
- **Purpose**: Testing and development
- **Features**:
  - Mock execution
  - Testing utilities
  - Development support
  - Performance benchmarking

#### Setup Script Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/setup_script.rs`
- **Purpose**: Environment setup
- **Features**:
  - Development environment setup
  - Dependency installation
  - Configuration initialization
  - Pre-task preparation

## Business Logic Services

### Git Integration Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/git_service.rs`
- **Dependencies**: git2 (v0.18)
- **Features**:
  - Repository operations
  - Branch management
  - Commit handling
  - Merge operations

### GitHub Integration Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/github_service.rs`
- **Dependencies**: octocrab (v0.44)
- **Features**:
  - PR creation and management
  - Issue tracking
  - Repository operations
  - Webhook handling

### Notification Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/notification_service.rs`
- **Dependencies**: notify-rust (v4.11)
- **Features**:
  - Desktop notifications
  - Sound notifications
  - Email notifications
  - Push notifications

### Process Management Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/process_service.rs`
- **Dependencies**: command-group (v5.0), nix (v0.29)
- **Features**:
  - Process lifecycle management
  - Resource monitoring
  - Signal handling
  - Cleanup operations

### PR Monitor Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/pr_monitor.rs`
- **Features**:
  - PR status tracking
  - CI/CD integration
  - Merge monitoring
  - Status notifications

### Analytics Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/analytics.rs`
- **Features**:
  - Usage analytics
  - Performance metrics
  - User behavior tracking
  - Reporting capabilities

## Utility Components

### Shell Utilities
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/utils/shell.rs`
- **Features**:
  - Command execution
  - Output parsing
  - Error handling
  - Process management

### Text Processing
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/utils/text.rs`
- **Dependencies**: regex (v1.11.1), strip-ansi-escapes (v0.2.1)
- **Features**:
  - String manipulation
  - Text parsing
  - Format conversion
  - Encoding handling

### Worktree Management
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/utils/worktree_manager.rs`
- **Features**:
  - Git worktree operations
  - Branch management
  - Cleanup automation
  - Isolation management

### General Utilities
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/utils.rs`
- **Features**:
  - Common utilities
  - Helper functions
  - Shared logic
  - Convenience methods

## Process Monitoring and Execution

### Execution Monitor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/execution_monitor.rs`
- **Features**:
  - Real-time process monitoring
  - Resource usage tracking
  - Performance metrics
  - Error detection

### Monitoring Capabilities
- **Process Status**: Real-time process execution status
- **Resource Usage**: CPU, memory, and I/O monitoring
- **Performance Metrics**: Execution time and efficiency
- **Error Tracking**: Comprehensive error capture

## MCP (Model Context Protocol) Integration

### MCP Task Server
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/mcp/task_server.rs`
- **Dependencies**: rmcp (v0.1.5)
- **Features**:
  - MCP protocol implementation
  - Task server functionality
  - Agent communication
  - Protocol compliance

### MCP Features
- **Protocol Compliance**: Full MCP specification support
- **Server Management**: Configuration and lifecycle management
- **Agent Communication**: Standardized inter-agent communication
- **Context Sharing**: Shared context across agents

## Error Handling and Observability

### Error Management
- **Error Handling**: anyhow for error propagation
- **Logging**: tracing with structured logging
- **Error Tracking**: Sentry integration for production monitoring
- **Recovery**: Automatic recovery mechanisms

### Observability Stack
- **Tracing**: tracing and tracing-subscriber
- **Metrics**: Custom metrics collection
- **Monitoring**: Sentry (v0.41.0) with comprehensive features
- **Alerting**: Configurable alerting system

### Sentry Integration
- **Features**: anyhow, backtrace, panic, debug-images
- **Middleware**: sentry-tower for HTTP middleware
- **Tracing**: sentry-tracing for log correlation
- **Performance**: Performance monitoring and profiling

## Asset Management

### Static Asset Serving
- **Asset Embedding**: rust-embed (v8.2)
- **MIME Detection**: mime_guess (v2.0)
- **Frontend Serving**: Embedded frontend assets
- **Static Files**: Notification sounds and scripts

### Asset Organization
- **Sounds**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/sounds/`
  - `abstract-sound1.wav` - Abstract notification sound
  - `abstract-sound2.wav` - Alternative abstract sound
  - `cow-mooing.wav` - Humorous notification sound
  - `phone-vibration.wav` - Phone-style vibration sound
  - `rooster.wav` - Morning/completion sound
- **Scripts**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/scripts/`
  - `toast-notification.ps1` - Windows toast notifications

## Security and Authentication

### Security Features
- **Memory Safety**: Rust's ownership system
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries with SQLX
- **Process Isolation**: Sandboxed execution environments

### Authentication System
- **Token-based Authentication**: JWT or similar
- **Session Management**: Secure session handling
- **Role-based Access Control**: Granular permissions
- **Audit Logging**: Complete audit trail

## Performance Optimization

### Performance Features
- **Async Processing**: Tokio-based concurrent execution
- **Database Optimization**: Efficient queries with proper indexing
- **Memory Management**: Rust's zero-cost abstractions
- **Connection Pooling**: Efficient database connections

### Optimization Strategies
- **Caching**: Intelligent caching mechanisms
- **Resource Pooling**: Efficient resource management
- **Load Balancing**: Distributed processing
- **Performance Monitoring**: Continuous performance tracking

## Build and Configuration

### Build System
- **Build Script**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/build.rs`
- **Dependencies**: dotenv (v0.15) for build-time configuration
- **Features**: Custom build logic and asset processing

### Configuration Management
- **Environment Variables**: Environment-based configuration
- **Configuration Files**: Structured configuration files
- **Runtime Configuration**: Dynamic configuration updates
- **Validation**: Configuration validation and error handling

### Development Tools
- **Cargo Features**: Optimized feature sets
- **Testing**: Comprehensive test suite with tempfile
- **Formatting**: rustfmt configuration
- **Linting**: Clippy with custom rules

## Deployment and Distribution

### Build Optimization
- **Release Profile**: Optimized release builds
- **Debug Information**: Configurable debug info
- **Strip Symbols**: Symbol stripping for smaller binaries
- **Split Debug Info**: Separate debug information

### Distribution
- **Single Binary**: Self-contained executable
- **Cross-Platform**: Support for multiple platforms
- **Embedded Assets**: All assets included in binary
- **NPM Package**: Distributed via npm registry

This comprehensive Rust backend provides a robust, performant, and secure foundation for AI agent orchestration, delivering enterprise-grade capabilities while maintaining excellent developer experience and system reliability.