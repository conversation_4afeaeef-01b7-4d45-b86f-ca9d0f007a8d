# Vibe Kanban - Frontend System Architecture

## Frontend Architecture Overview

The **Frontend System** is a modern React 18 application built with TypeScript and Vite, designed specifically for AI agent orchestration and task management. It provides a responsive, intuitive interface for managing multiple AI coding assistants with real-time updates and comprehensive task visualization.

## Core Technology Stack

### Framework Foundation
- **React**: v18.2.0 with concurrent features and hooks
- **TypeScript**: v5.2.2 with strict mode configuration
- **Build Tool**: Vite v5.0.8 for fast development and production builds
- **Package Manager**: npm with workspace support

### UI Framework and Design System
- **Component Library**: shadcn/ui (Radix UI foundation)
- **Styling**: Tailwind CSS v3.4.0 with utility-first approach
- **Icons**: Lucide React v0.303.0 for consistent iconography
- **Animations**: tailwindcss-animate v1.0.7 for smooth transitions

### State Management and Data Flow
- **Context API**: React Context for global state management
- **Local State**: useState and useReducer for component state
- **Form State**: Controlled components with validation
- **Real-time Updates**: WebSocket integration for live data

## Application Structure

### Entry Points and Core Setup
- **Main Entry**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/main.tsx`
- **Root Component**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/App.tsx`
- **Global Styles**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/index.css`

### Application Bootstrap
```typescript
// main.tsx - Application entry point
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import './index.css';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
```

### Root Component Architecture
- **Router Setup**: React Router for client-side navigation
- **Provider Configuration**: Context providers for global state
- **Theme Management**: Dark/light theme support
- **Error Boundaries**: Comprehensive error handling

## Page-Level Components

### Project Management Pages
- **Projects Overview**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/projects.tsx`
- **Project Tasks**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/project-tasks.tsx`
- **Features**:
  - Project listing and creation
  - Task management interface
  - Real-time project updates
  - Responsive grid layouts

### Configuration Pages
- **Settings**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/Settings.tsx`
- **MCP Servers**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/McpServers.tsx`
- **Features**:
  - System configuration management
  - Agent configuration
  - MCP server management
  - Preference settings

## Component Architecture

### Layout Components
- **Navigation Bar**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/layout/navbar.tsx`
- **Logo Component**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/logo.tsx`
- **Features**:
  - Responsive navigation
  - Brand consistency
  - User authentication status
  - Theme switching

### Project Components
- **Project List**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/project-list.tsx`
- **Project Detail**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/project-detail.tsx`
- **Project Form**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/project-form.tsx`
- **Projects Page**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/projects-page.tsx`

### Task Management Components

#### Core Task Components
- **Task Kanban Board**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskKanbanBoard.tsx`
- **Task Card**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskCard.tsx`
- **Task Details Panel**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetailsPanel.tsx`
- **Task Details Header**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetailsHeader.tsx`

#### Task Interaction Components
- **Task Form Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskFormDialog.tsx`
- **Task Details Toolbar**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetailsToolbar.tsx`
- **Task Activity History**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskActivityHistory.tsx`
- **Task Follow-up Section**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskFollowUpSection.tsx`

### Task Details Components

#### Information Display
- **Conversation Viewer**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/Conversation.tsx`
- **Normalized Conversation**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/NormalizedConversationViewer.tsx`
- **Display Entry**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DisplayConversationEntry.tsx`

#### Code Review Components
- **Diff Tab**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DiffTab.tsx`
- **Diff Card**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DiffCard.tsx`
- **Diff File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DiffFile.tsx`
- **Diff Chunk**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DiffChunkSection.tsx`

#### Monitoring Components
- **Logs Tab**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/LogsTab.tsx`
- **Tab Navigation**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/TabNavigation.tsx`
- **Collapsible Toolbar**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/CollapsibleToolbar.tsx`

### Task Toolbar Components
- **Create Attempt**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/Toolbar/CreateAttempt.tsx`
- **Current Attempt**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/Toolbar/CurrentAttempt.tsx`
- **Create PR Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/Toolbar/CreatePRDialog.tsx`

## Dialog and Modal Components

### User Experience Dialogs
- **Onboarding Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/OnboardingDialog.tsx`
- **Disclaimer Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/DisclaimerDialog.tsx`
- **Privacy Opt-in Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/PrivacyOptInDialog.tsx`

### Authentication Dialogs
- **GitHub Login Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/GitHubLoginDialog.tsx`
- **Provide PAT Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ProvidePatDialog.tsx`

### Task Management Dialogs
- **Delete File Confirmation**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/DeleteFileConfirmationDialog.tsx`
- **Editor Selection Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/EditorSelectionDialog.tsx`

## State Management System

### Context Providers
- **Config Provider**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/config-provider.tsx`
- **Theme Provider**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/theme-provider.tsx`
- **Task Details Context**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/context/TaskDetailsContextProvider.tsx`

### Context Definitions
- **Task Details Context**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/context/taskDetailsContext.ts`

### State Management Pattern
```typescript
// Example Context Provider Pattern
interface TaskDetailsContextType {
  currentTask: Task | null;
  selectedAttempt: TaskAttempt | null;
  isLoading: boolean;
  error: string | null;
  // Action methods
  selectTask: (task: Task) => void;
  selectAttempt: (attempt: TaskAttempt) => void;
  refreshData: () => Promise<void>;
}
```

## UI Component System

### Core UI Components
- **Button**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/button.tsx`
- **Card**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/card.tsx`
- **Dialog**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/dialog.tsx`
- **Input**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/input.tsx`

### Form Components
- **Checkbox**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/checkbox.tsx`
- **Label**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/label.tsx`
- **Select**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/select.tsx`
- **Textarea**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/textarea.tsx`

### Navigation Components
- **Dropdown Menu**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/dropdown-menu.tsx`
- **Separator**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/separator.tsx`
- **Tooltip**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/tooltip.tsx`

### Display Components
- **Alert**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/alert.tsx`
- **Badge**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/badge.tsx`
- **Chip**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/chip.tsx`
- **Table**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/table.tsx`

### Specialized Components
- **File Search Textarea**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/file-search-textarea.tsx`
- **Folder Picker**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/folder-picker.tsx`
- **Markdown Renderer**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/markdown-renderer.tsx`

### Kanban Components
- **Kanban Board**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/shadcn-io/kanban/index.tsx`

## Drag and Drop System

### DnD Kit Integration
- **Core Library**: @dnd-kit/core v6.3.1
- **Modifiers**: @dnd-kit/modifiers v9.0.0
- **Features**:
  - Drag and drop task cards
  - Column-based organization
  - Keyboard accessibility
  - Touch support

### Drag and Drop Implementation
```typescript
// Example DnD implementation
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';

function KanbanBoard() {
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (over && active.id !== over.id) {
      // Handle task status change
      updateTaskStatus(active.id, over.id);
    }
  };

  return (
    <DndContext modifiers={[restrictToHorizontalAxis]} onDragEnd={handleDragEnd}>
      {/* Kanban columns and cards */}
    </DndContext>
  );
}
```

## Theme and Styling System

### Theme Management
- **Theme Provider**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/theme-provider.tsx`
- **Theme Toggle**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/theme-toggle.tsx`
- **Features**:
  - Dark/light mode switching
  - System preference detection
  - Persistent theme selection
  - Smooth transitions

### Styling Architecture
- **Tailwind CSS**: Utility-first CSS framework
- **CSS Variables**: Custom CSS properties for theming
- **Component Variants**: class-variance-authority for variant management
- **Responsive Design**: Mobile-first responsive utilities

### Design System
- **Color Palette**: Consistent color scheme across components
- **Typography**: @tailwindcss/typography for rich text
- **Spacing**: Consistent spacing scale
- **Animations**: Smooth transitions and micro-interactions

## Utility Libraries

### Core Utilities
- **API Client**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/lib/api.ts`
- **Utility Functions**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/lib/utils.ts`
- **Keyboard Shortcuts**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/lib/keyboard-shortcuts.ts`
- **Responsive Config**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/lib/responsive-config.ts`

### API Integration
```typescript
// Example API client usage
class ApiClient {
  async getTasks(projectId: string): Promise<Task[]> {
    const response = await fetch(`/api/projects/${projectId}/tasks`);
    return response.json();
  }

  async createTask(projectId: string, task: CreateTaskRequest): Promise<Task> {
    const response = await fetch(`/api/projects/${projectId}/tasks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(task),
    });
    return response.json();
  }
}
```

## Real-Time Features

### WebSocket Integration
- **Real-time Updates**: Live task status updates
- **Progress Monitoring**: Real-time execution progress
- **Collaboration**: Multi-user collaboration features
- **Notifications**: Real-time notifications

### Live Data Synchronization
- **Optimistic Updates**: Immediate UI updates with server sync
- **Conflict Resolution**: Handle concurrent updates
- **Connection Management**: Robust connection handling
- **Fallback Mechanisms**: Graceful degradation

## Performance Optimization

### Code Splitting and Lazy Loading
- **React.lazy()**: Lazy loading of components
- **Route-based Splitting**: Split code by routes
- **Component Splitting**: Split large components
- **Dynamic Imports**: Load modules on demand

### Memoization and Optimization
- **React.memo()**: Prevent unnecessary re-renders
- **useMemo()**: Memoize expensive calculations
- **useCallback()**: Memoize callback functions
- **Virtual Scrolling**: Efficient large list rendering

### Bundle Optimization
- **Tree Shaking**: Remove unused code
- **Code Splitting**: Split bundles efficiently
- **Asset Optimization**: Optimize images and assets
- **Compression**: Gzip and Brotli compression

## Accessibility Features

### ARIA Support
- **ARIA Labels**: Comprehensive ARIA labeling
- **ARIA Roles**: Proper role assignments
- **ARIA States**: Dynamic state management
- **Screen Reader Support**: Full screen reader compatibility

### Keyboard Navigation
- **Tab Order**: Logical tab order
- **Keyboard Shortcuts**: Power user shortcuts
- **Focus Management**: Proper focus handling
- **Escape Handling**: Modal and dialog escape

### Visual Accessibility
- **High Contrast**: High contrast theme support
- **Color Blindness**: Color-blind friendly design
- **Font Scaling**: Responsive font sizing
- **Motion Preferences**: Respect motion preferences

## Error Handling and Boundaries

### Error Boundaries
- **React Error Boundaries**: Catch component errors
- **Fallback UI**: Graceful error display
- **Error Recovery**: Error recovery mechanisms
- **Error Reporting**: Comprehensive error reporting

### Form Validation
- **Input Validation**: Real-time input validation
- **Form State Management**: Robust form state handling
- **Error Messages**: Clear error messaging
- **Validation Feedback**: Visual validation feedback

## Testing Strategy

### Testing Framework
- **Testing Library**: React Testing Library
- **Unit Tests**: Component unit testing
- **Integration Tests**: Component integration testing
- **E2E Tests**: End-to-end testing

### Test Organization
- **Test Files**: Co-located test files
- **Test Utilities**: Shared test utilities
- **Mock Data**: Consistent mock data
- **Test Coverage**: Comprehensive test coverage

## Build and Development

### Development Server
- **Vite Dev Server**: Fast development server
- **Hot Module Replacement**: Instant updates
- **Proxy Configuration**: API proxy for development
- **Environment Variables**: Development configuration

### Build Process
- **TypeScript Compilation**: Type checking and compilation
- **Vite Build**: Optimized production build
- **Asset Processing**: Image and asset optimization
- **Bundle Analysis**: Bundle size analysis

### Code Quality
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting
- **TypeScript**: Type checking
- **Husky**: Git hooks for quality checks

## Deployment Integration

### Production Build
- **Optimized Bundle**: Minified and optimized
- **Asset Optimization**: Compressed assets
- **Cache Headers**: Proper caching configuration
- **Service Worker**: Offline functionality

### Static Asset Serving
- **Rust Backend Integration**: Served from Rust backend
- **Asset Embedding**: Assets embedded in backend
- **CDN Integration**: CDN for static assets
- **Cache Strategy**: Intelligent caching

## Future Enhancements

### Planned Features
- **Mobile App**: React Native mobile app
- **Offline Support**: Progressive Web App features
- **Advanced Analytics**: Enhanced analytics dashboard
- **Collaboration**: Real-time collaboration features

### Technical Improvements
- **Performance**: Further performance optimizations
- **Accessibility**: Enhanced accessibility features
- **Internationalization**: Multi-language support
- **Testing**: Expanded test coverage

This comprehensive frontend system provides a modern, responsive, and feature-rich interface for AI agent orchestration, enabling efficient task management and seamless user experience across all devices and platforms.