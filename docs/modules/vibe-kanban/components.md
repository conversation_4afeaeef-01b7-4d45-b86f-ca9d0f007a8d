# Vibe Kanban - Detailed Component Breakdown

## Component Architecture Overview

Vibe Kanban follows a **component-driven architecture** with clear separation between backend services, frontend components, and shared utilities. This breakdown provides detailed analysis of each component with specific file paths and agile development considerations.

## Backend Components

### Core Application Components

#### 1. Application Entry Point
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/main.rs`
- **Purpose**: Main application entry point and server initialization
- **Key Features**:
  - Tokio async runtime setup
  - Server configuration and startup
  - Error handling and graceful shutdown
  - Port management (default 3001)

#### 2. Application State Management
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/app_state.rs`
- **Purpose**: Shared application state across all components
- **Key Features**:
  - Database connection pool management
  - Configuration state
  - Shared resources and caches
  - Thread-safe state management

#### 3. Library Exports
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/lib.rs`
- **Purpose**: Public library interface and module organization
- **Key Features**:
  - Module re-exports
  - Public API definitions
  - Library initialization

### Data Model Components

#### 1. Task Management
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/task.rs`
- **Purpose**: Task data model and database operations
- **Key Features**:
  - Task CRUD operations
  - Status management (Todo, InProgress, InReview, Done, Cancelled)
  - Attempt status tracking
  - Project relationship management

#### 2. Project Management
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/project.rs`
- **Purpose**: Project configuration and management
- **Key Features**:
  - Project CRUD operations
  - Configuration management
  - Development script handling
  - Git repository integration

#### 3. Task Attempt Tracking
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/task_attempt.rs`
- **Purpose**: Task execution attempt management
- **Key Features**:
  - Attempt lifecycle tracking
  - Branch management
  - PR tracking integration
  - Merge commit handling

#### 4. Execution Process Management
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/execution_process.rs`
- **Purpose**: Process execution tracking and monitoring
- **Key Features**:
  - Process lifecycle management
  - Resource tracking
  - Status monitoring
  - Failure handling

#### 5. Activity Logging
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/task_attempt_activity.rs`
- **Purpose**: Detailed activity logging for task attempts
- **Key Features**:
  - Activity stream management
  - Status change tracking
  - Execution log storage
  - Timeline construction

#### 6. Session Management
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/executor_session.rs`
- **Purpose**: AI agent session management
- **Key Features**:
  - Session lifecycle management
  - Agent state persistence
  - Conversation tracking
  - Resource allocation

#### 7. Configuration Management
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/config.rs`
- **Purpose**: System configuration and settings
- **Key Features**:
  - Configuration CRUD operations
  - Environment management
  - Agent configuration
  - System preferences

#### 8. API Response Models
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/api_response.rs`
- **Purpose**: Standardized API response structures
- **Key Features**:
  - Response standardization
  - Error response handling
  - Type-safe responses
  - Client-server communication

### Route Handler Components

#### 1. Task Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/tasks.rs`
- **Purpose**: Task-related HTTP endpoints
- **Key Features**:
  - Task CRUD operations
  - Status updates
  - Filtering and sorting
  - Validation handling

#### 2. Project Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/projects.rs`
- **Purpose**: Project management endpoints
- **Key Features**:
  - Project CRUD operations
  - Configuration management
  - Git integration
  - Development script handling

#### 3. Task Attempt Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/task_attempts.rs`
- **Purpose**: Task execution attempt endpoints
- **Key Features**:
  - Attempt creation and management
  - Status tracking
  - Log streaming
  - Process monitoring

#### 4. Authentication Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/auth.rs`
- **Purpose**: Authentication and authorization
- **Key Features**:
  - Token management
  - Session handling
  - Permission validation
  - Security middleware

#### 5. Configuration Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/config.rs`
- **Purpose**: System configuration endpoints
- **Key Features**:
  - Configuration CRUD operations
  - Environment management
  - Agent configuration
  - System preferences

#### 6. Filesystem Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/filesystem.rs`
- **Purpose**: File system operations
- **Key Features**:
  - File browsing
  - Directory operations
  - File content access
  - Permission handling

#### 7. Health Check Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/health.rs`
- **Purpose**: System health monitoring
- **Key Features**:
  - Health status reporting
  - Service availability
  - Resource monitoring
  - Diagnostic information

### AI Agent Executor Components

#### 1. Executor Framework
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executor.rs`
- **Purpose**: Core executor framework and abstractions
- **Key Features**:
  - Executor trait definition
  - Conversation normalization
  - Process management
  - Error handling

#### 2. Claude Code Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/claude.rs`
- **Purpose**: Claude Code integration
- **Key Features**:
  - Claude API integration
  - Conversation parsing
  - Command execution
  - Context management

#### 3. Gemini CLI Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/gemini.rs`
- **Purpose**: Gemini CLI integration
- **Key Features**:
  - Gemini API integration
  - Response parsing
  - Command handling
  - Session management

#### 4. Amp Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/amp.rs`
- **Purpose**: Amp framework integration
- **Key Features**:
  - Amp API integration
  - Workflow management
  - Result processing
  - Error handling

#### 5. OpenCode Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/opencode.rs`
- **Purpose**: OpenCode platform integration
- **Key Features**:
  - OpenCode API integration
  - Code generation
  - Execution monitoring
  - Result validation

#### 6. Echo Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/echo.rs`
- **Purpose**: Testing and development executor
- **Key Features**:
  - Mock execution
  - Testing utilities
  - Debug output
  - Development support

#### 7. Setup Script Executor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/executors/setup_script.rs`
- **Purpose**: Environment setup and initialization
- **Key Features**:
  - Environment setup
  - Dependency installation
  - Configuration initialization
  - Pre-task preparation

### Service Components

#### 1. Git Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/git_service.rs`
- **Purpose**: Git repository operations
- **Key Features**:
  - Repository management
  - Branch operations
  - Commit handling
  - Merge operations

#### 2. GitHub Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/github_service.rs`
- **Purpose**: GitHub API integration
- **Key Features**:
  - PR creation and management
  - Issue tracking
  - Repository operations
  - Webhook handling

#### 3. Notification Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/notification_service.rs`
- **Purpose**: System notifications
- **Key Features**:
  - Desktop notifications
  - Sound notifications
  - Email notifications
  - Push notifications

#### 4. Process Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/process_service.rs`
- **Purpose**: Process management and monitoring
- **Key Features**:
  - Process lifecycle management
  - Resource monitoring
  - Signal handling
  - Cleanup operations

#### 5. PR Monitor Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/pr_monitor.rs`
- **Purpose**: Pull request monitoring
- **Key Features**:
  - PR status tracking
  - CI/CD integration
  - Merge monitoring
  - Status updates

#### 6. Analytics Service
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/services/analytics.rs`
- **Purpose**: Usage analytics and metrics
- **Key Features**:
  - Usage tracking
  - Performance metrics
  - User behavior analysis
  - Reporting

### Utility Components

#### 1. Shell Utilities
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/utils/shell.rs`
- **Purpose**: Shell command execution utilities
- **Key Features**:
  - Command execution
  - Output parsing
  - Error handling
  - Process management

#### 2. Text Processing
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/utils/text.rs`
- **Purpose**: Text processing and manipulation
- **Key Features**:
  - String manipulation
  - Text parsing
  - Format conversion
  - Encoding handling

#### 3. Worktree Manager
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/utils/worktree_manager.rs`
- **Purpose**: Git worktree management
- **Key Features**:
  - Worktree creation
  - Branch management
  - Cleanup operations
  - Isolation management

#### 4. General Utilities
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/utils.rs`
- **Purpose**: General utility functions
- **Key Features**:
  - Common utilities
  - Helper functions
  - Shared logic
  - Convenience methods

### MCP Integration Components

#### 1. MCP Task Server
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/mcp/task_server.rs`
- **Purpose**: Model Context Protocol task server
- **Key Features**:
  - MCP protocol implementation
  - Task server functionality
  - Agent communication
  - Protocol compliance

#### 2. MCP Module
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/mcp/mod.rs`
- **Purpose**: MCP module organization
- **Key Features**:
  - Module exports
  - Protocol definitions
  - Integration points
  - Configuration

### Execution Monitoring Components

#### 1. Execution Monitor
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/execution_monitor.rs`
- **Purpose**: Real-time execution monitoring
- **Key Features**:
  - Process monitoring
  - Resource tracking
  - Status updates
  - Performance metrics

## Frontend Components

### Core Application Components

#### 1. Application Root
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/App.tsx`
- **Purpose**: Root React component and application setup
- **Key Features**:
  - Router configuration
  - Global state management
  - Theme provider setup
  - Error boundary handling

#### 2. Application Entry
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/main.tsx`
- **Purpose**: React application entry point
- **Key Features**:
  - React 18 setup
  - DOM rendering
  - Provider configuration
  - Development setup

#### 3. Global Styles
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/index.css`
- **Purpose**: Global CSS styles and Tailwind imports
- **Key Features**:
  - Tailwind CSS imports
  - Global CSS variables
  - Base styles
  - Custom utilities

### Layout Components

#### 1. Navigation Bar
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/layout/navbar.tsx`
- **Purpose**: Main navigation component
- **Key Features**:
  - Navigation links
  - User authentication
  - Theme toggle
  - Responsive design

#### 2. Logo Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/logo.tsx`
- **Purpose**: Application logo and branding
- **Key Features**:
  - SVG logo rendering
  - Theme-aware styling
  - Responsive sizing
  - Brand consistency

### Page Components

#### 1. Projects Page
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/projects.tsx`
- **Purpose**: Project listing and management page
- **Key Features**:
  - Project grid/list view
  - Project creation
  - Project filtering
  - Navigation handling

#### 2. Project Tasks Page
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/project-tasks.tsx`
- **Purpose**: Task management for specific project
- **Key Features**:
  - Kanban board view
  - Task filtering
  - Real-time updates
  - Task creation

#### 3. Settings Page
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/Settings.tsx`
- **Purpose**: Application settings and configuration
- **Key Features**:
  - Configuration management
  - Agent settings
  - Theme preferences
  - System preferences

#### 4. MCP Servers Page
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/McpServers.tsx`
- **Purpose**: MCP server management
- **Key Features**:
  - Server configuration
  - Connection management
  - Protocol settings
  - Status monitoring

### Project Components

#### 1. Project List
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/project-list.tsx`
- **Purpose**: Project listing component
- **Key Features**:
  - Project grid display
  - Sorting and filtering
  - Search functionality
  - Responsive layout

#### 2. Project Detail
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/project-detail.tsx`
- **Purpose**: Individual project details
- **Key Features**:
  - Project information display
  - Configuration viewing
  - Task summary
  - Action buttons

#### 3. Project Form
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/project-form.tsx`
- **Purpose**: Project creation and editing form
- **Key Features**:
  - Form validation
  - Configuration input
  - File path selection
  - Error handling

#### 4. Projects Page Container
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/projects-page.tsx`
- **Purpose**: Main projects page container
- **Key Features**:
  - Layout management
  - State management
  - Navigation handling
  - Data loading

### Task Components

#### 1. Task Card
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskCard.tsx`
- **Purpose**: Individual task display card
- **Key Features**:
  - Task information display
  - Status indicators
  - Action buttons
  - Drag and drop support

#### 2. Task Kanban Board
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskKanbanBoard.tsx`
- **Purpose**: Main Kanban board component
- **Key Features**:
  - Drag and drop functionality
  - Column management
  - Task filtering
  - Real-time updates

#### 3. Task Details Panel
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetailsPanel.tsx`
- **Purpose**: Detailed task information panel
- **Key Features**:
  - Task information display
  - Activity history
  - Conversation viewer
  - Action toolbar

#### 4. Task Details Header
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetailsHeader.tsx`
- **Purpose**: Task details header component
- **Key Features**:
  - Task title and description
  - Status indicators
  - Action buttons
  - Breadcrumb navigation

#### 5. Task Details Toolbar
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetailsToolbar.tsx`
- **Purpose**: Task action toolbar
- **Key Features**:
  - Task actions
  - Status changes
  - Agent controls
  - Quick actions

#### 6. Task Form Dialog
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskFormDialog.tsx`
- **Purpose**: Task creation and editing dialog
- **Key Features**:
  - Form validation
  - Task input fields
  - Agent selection
  - Error handling

#### 7. Task Activity History
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskActivityHistory.tsx`
- **Purpose**: Task activity timeline
- **Key Features**:
  - Activity timeline
  - Status changes
  - User actions
  - Timestamps

#### 8. Task Follow-up Section
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskFollowUpSection.tsx`
- **Purpose**: Task follow-up and next steps
- **Key Features**:
  - Follow-up suggestions
  - Next action items
  - Related tasks
  - Action planning

### Task Details Components

#### 1. Collapsible Toolbar
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/CollapsibleToolbar.tsx`
- **Purpose**: Collapsible task toolbar
- **Key Features**:
  - Expandable toolbar
  - Action grouping
  - Space optimization
  - Responsive design

#### 2. Conversation Viewer
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/Conversation.tsx`
- **Purpose**: AI agent conversation display
- **Key Features**:
  - Conversation rendering
  - Message formatting
  - Code highlighting
  - Scrollable interface

#### 3. Normalized Conversation Viewer
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/NormalizedConversationViewer.tsx`
- **Purpose**: Normalized conversation display
- **Key Features**:
  - Standardized format
  - Cross-agent compatibility
  - Enhanced readability
  - Structured display

#### 4. Display Conversation Entry
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DisplayConversationEntry.tsx`
- **Purpose**: Individual conversation entry display
- **Key Features**:
  - Entry type handling
  - Content formatting
  - Timestamp display
  - Action buttons

#### 5. Diff Tab
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DiffTab.tsx`
- **Purpose**: Code diff visualization tab
- **Key Features**:
  - Diff display
  - File comparison
  - Syntax highlighting
  - Navigation controls

#### 6. Diff Card
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DiffCard.tsx`
- **Purpose**: Individual diff card component
- **Key Features**:
  - Diff summary
  - File information
  - Change statistics
  - Expand/collapse

#### 7. Diff File
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DiffFile.tsx`
- **Purpose**: File diff display
- **Key Features**:
  - File diff rendering
  - Line-by-line comparison
  - Syntax highlighting
  - Change indicators

#### 8. Diff Chunk Section
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/DiffChunkSection.tsx`
- **Purpose**: Diff chunk display
- **Key Features**:
  - Chunk rendering
  - Context lines
  - Change highlighting
  - Navigation

#### 9. Logs Tab
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/LogsTab.tsx`
- **Purpose**: Execution logs display
- **Key Features**:
  - Log streaming
  - Real-time updates
  - Filtering options
  - Search functionality

#### 10. Tab Navigation
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/TaskDetails/TabNavigation.tsx`
- **Purpose**: Tab navigation for task details
- **Key Features**:
  - Tab switching
  - Navigation state
  - Keyboard shortcuts
  - Responsive design

### Task Toolbar Components

#### 1. Create Attempt
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/Toolbar/CreateAttempt.tsx`
- **Purpose**: Create new task attempt
- **Key Features**:
  - Attempt creation
  - Agent selection
  - Configuration options
  - Validation

#### 2. Current Attempt
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/Toolbar/CurrentAttempt.tsx`
- **Purpose**: Current attempt display and controls
- **Key Features**:
  - Attempt information
  - Status display
  - Control actions
  - Progress tracking

#### 3. Create PR Dialog
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/Toolbar/CreatePRDialog.tsx`
- **Purpose**: Pull request creation dialog
- **Key Features**:
  - PR form
  - Branch selection
  - Description input
  - GitHub integration

### Dialog Components

#### 1. Delete File Confirmation
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/DeleteFileConfirmationDialog.tsx`
- **Purpose**: File deletion confirmation
- **Key Features**:
  - Confirmation dialog
  - File information
  - Safety checks
  - Action buttons

#### 2. Editor Selection Dialog
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/tasks/EditorSelectionDialog.tsx`
- **Purpose**: Editor selection for file editing
- **Key Features**:
  - Editor options
  - Configuration
  - Preferences
  - Integration

#### 3. Disclaimer Dialog
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/DisclaimerDialog.tsx`
- **Purpose**: Application disclaimer
- **Key Features**:
  - Legal information
  - Terms display
  - Acceptance handling
  - Dismissal

#### 4. GitHub Login Dialog
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/GitHubLoginDialog.tsx`
- **Purpose**: GitHub authentication
- **Key Features**:
  - Login flow
  - Token input
  - Validation
  - Error handling

#### 5. Onboarding Dialog
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/OnboardingDialog.tsx`
- **Purpose**: User onboarding flow
- **Key Features**:
  - Welcome flow
  - Feature introduction
  - Setup guidance
  - Progress tracking

#### 6. Privacy Opt-in Dialog
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/PrivacyOptInDialog.tsx`
- **Purpose**: Privacy preferences
- **Key Features**:
  - Privacy options
  - Data collection
  - User consent
  - Preference management

#### 7. Provide PAT Dialog
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ProvidePatDialog.tsx`
- **Purpose**: Personal Access Token input
- **Key Features**:
  - Token input
  - Validation
  - Security information
  - Storage handling

### Context Components

#### 1. Task Details Context Provider
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/context/TaskDetailsContextProvider.tsx`
- **Purpose**: Task details state management
- **Key Features**:
  - Context provider
  - State management
  - Data synchronization
  - Event handling

#### 2. Task Details Context
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/context/taskDetailsContext.ts`
- **Purpose**: Task details context definition
- **Key Features**:
  - Context definition
  - Type definitions
  - State interface
  - Hook exports

#### 3. Config Provider
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/config-provider.tsx`
- **Purpose**: Configuration context provider
- **Key Features**:
  - Configuration state
  - Settings management
  - Preference handling
  - Persistence

#### 4. Theme Provider
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/theme-provider.tsx`
- **Purpose**: Theme management
- **Key Features**:
  - Theme switching
  - Dark/light mode
  - Persistence
  - System preference

#### 5. Theme Toggle
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/theme-toggle.tsx`
- **Purpose**: Theme toggle button
- **Key Features**:
  - Toggle functionality
  - Visual indicators
  - Accessibility
  - Smooth transitions

### Utility Components

#### 1. Keyboard Shortcuts Demo
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/keyboard-shortcuts-demo.tsx`
- **Purpose**: Keyboard shortcuts demonstration
- **Key Features**:
  - Shortcut display
  - Help interface
  - Interactive demo
  - Documentation

### UI Components

#### 1. Alert Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/alert.tsx`
- **Purpose**: Alert and notification display
- **Key Features**:
  - Alert variants
  - Dismissible alerts
  - Icon support
  - Styling options

#### 2. Badge Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/badge.tsx`
- **Purpose**: Badge and label display
- **Key Features**:
  - Badge variants
  - Color schemes
  - Size options
  - Content support

#### 3. Button Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/button.tsx`
- **Purpose**: Button component with variants
- **Key Features**:
  - Button variants
  - Size options
  - Loading states
  - Accessibility

#### 4. Card Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/card.tsx`
- **Purpose**: Card layout component
- **Key Features**:
  - Card structure
  - Header and footer
  - Content areas
  - Responsive design

#### 5. Checkbox Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/checkbox.tsx`
- **Purpose**: Checkbox input component
- **Key Features**:
  - Checkbox styling
  - Indeterminate state
  - Accessibility
  - Form integration

#### 6. Chip Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/chip.tsx`
- **Purpose**: Chip and tag display
- **Key Features**:
  - Chip variants
  - Removable chips
  - Icon support
  - Interactive states

#### 7. Dialog Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/dialog.tsx`
- **Purpose**: Modal dialog component
- **Key Features**:
  - Modal functionality
  - Overlay support
  - Close handling
  - Accessibility

#### 8. Dropdown Menu Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/dropdown-menu.tsx`
- **Purpose**: Dropdown menu component
- **Key Features**:
  - Menu items
  - Nested menus
  - Keyboard navigation
  - Positioning

#### 9. File Search Textarea
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/file-search-textarea.tsx`
- **Purpose**: File search input with autocomplete
- **Key Features**:
  - File searching
  - Autocomplete
  - Path suggestion
  - Validation

#### 10. Folder Picker
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/folder-picker.tsx`
- **Purpose**: Folder selection component
- **Key Features**:
  - Folder browsing
  - Path selection
  - Validation
  - Recent folders

#### 11. Input Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/input.tsx`
- **Purpose**: Input field component
- **Key Features**:
  - Input variants
  - Validation states
  - Icon support
  - Accessibility

#### 12. Label Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/label.tsx`
- **Purpose**: Label component for forms
- **Key Features**:
  - Label styling
  - Form association
  - Accessibility
  - Required indicators

#### 13. Markdown Renderer
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/markdown-renderer.tsx`
- **Purpose**: Markdown content rendering
- **Key Features**:
  - Markdown parsing
  - Syntax highlighting
  - Custom components
  - Styling support

#### 14. Select Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/select.tsx`
- **Purpose**: Select dropdown component
- **Key Features**:
  - Select options
  - Search functionality
  - Multiple selection
  - Accessibility

#### 15. Separator Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/separator.tsx`
- **Purpose**: Visual separator component
- **Key Features**:
  - Horizontal/vertical
  - Styling options
  - Spacing control
  - Responsive design

#### 16. Table Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/table.tsx`
- **Purpose**: Table display component
- **Key Features**:
  - Table structure
  - Sorting support
  - Responsive design
  - Accessibility

#### 17. Textarea Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/textarea.tsx`
- **Purpose**: Textarea input component
- **Key Features**:
  - Textarea styling
  - Auto-resize
  - Validation states
  - Character counting

#### 18. Tooltip Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/tooltip.tsx`
- **Purpose**: Tooltip display component
- **Key Features**:
  - Tooltip positioning
  - Hover/focus triggers
  - Accessibility
  - Animation

#### 19. Kanban Component
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/ui/shadcn-io/kanban/index.tsx`
- **Purpose**: Kanban board UI component
- **Key Features**:
  - Kanban layout
  - Drag and drop
  - Column management
  - Responsive design

### Library Components

#### 1. API Client
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/lib/api.ts`
- **Purpose**: API client and HTTP utilities
- **Key Features**:
  - HTTP client
  - Request/response handling
  - Error handling
  - Type safety

#### 2. Utility Functions
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/lib/utils.ts`
- **Purpose**: Common utility functions
- **Key Features**:
  - Helper functions
  - Data manipulation
  - Formatting utilities
  - Validation

#### 3. Keyboard Shortcuts
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/lib/keyboard-shortcuts.ts`
- **Purpose**: Keyboard shortcut management
- **Key Features**:
  - Shortcut handling
  - Key binding
  - Event management
  - Accessibility

#### 4. Responsive Configuration
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/lib/responsive-config.ts`
- **Purpose**: Responsive design configuration
- **Key Features**:
  - Breakpoint management
  - Responsive utilities
  - Device detection
  - Layout adaptation

## Shared Components

### Type Definitions
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/shared/types.ts`
- **Purpose**: Shared TypeScript types between frontend and backend
- **Key Features**:
  - Type definitions
  - Interface sharing
  - Type safety
  - Cross-platform compatibility

## Component Integration Strategy

### Agile Development Considerations

#### 1. Component Reusability
- **Shared UI Components**: Common components used across features
- **Consistent Patterns**: Standardized component patterns
- **Modularity**: Self-contained components with clear interfaces
- **Documentation**: Comprehensive component documentation

#### 2. Development Workflow
- **Component-First Development**: Build components before features
- **Isolated Testing**: Components tested in isolation
- **Storybook Integration**: Component documentation and testing
- **Design System**: Consistent design system implementation

#### 3. Performance Optimization
- **Lazy Loading**: Components loaded on demand
- **Memoization**: Optimized re-rendering
- **Bundle Splitting**: Code splitting for performance
- **Tree Shaking**: Unused code elimination

#### 4. Maintenance Strategy
- **Version Control**: Component versioning
- **Breaking Changes**: Careful handling of breaking changes
- **Migration Guides**: Component migration documentation
- **Deprecation Process**: Structured deprecation process

This comprehensive component breakdown provides a detailed view of the Vibe Kanban architecture, enabling effective sprint planning, task allocation, and development coordination across both backend and frontend components.