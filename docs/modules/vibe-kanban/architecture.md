# Vibe Kanban - Full-Stack Architecture

## Architectural Overview

Vibe Kanban employs a **modern full-stack architecture** designed for AI agent orchestration, combining the performance and safety of Rust on the backend with the flexibility of React on the frontend. This architecture enables sophisticated multi-agent coordination while maintaining excellent user experience and system reliability.

## System Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Layer (Port 3000)"
        A[React 18 + TypeScript]
        B[shadcn/ui Components]
        C[Vite Dev Server]
        D[Tailwind CSS]
    end
    
    subgraph "Backend Layer (Port 3001)"
        E[Axum Web Server]
        F[Tokio Async Runtime]
        G[SQLite Database]
        H[SQLX Type-Safe Queries]
    end
    
    subgraph "AI Agent Layer"
        I[Claude Executor]
        J[Gemini Executor]
        K[Amp Executor]
        L[OpenCode Executor]
        M[Echo Executor]
        N[Setup Script Executor]
    end
    
    subgraph "External Services"
        O[GitHub API]
        P[MCP Servers]
        Q[Sentry Error Tracking]
        R[Notification Services]
    end
    
    subgraph "Development Tools"
        S[Git Integration]
        T[Worktree Management]
        U[Process Monitoring]
        V[File System Watching]
    end
    
    A --> E
    B --> A
    C --> A
    D --> A
    
    E --> F
    E --> G
    E --> H
    
    E --> I
    E --> J
    E --> K
    E --> L
    E --> M
    E --> N
    
    E --> O
    E --> P
    E --> Q
    E --> R
    
    I --> S
    J --> S
    K --> S
    L --> S
    
    F --> T
    F --> U
    F --> V
```

## Backend Architecture

### Core Framework Stack
- **Web Server**: Axum with Tower middleware
- **Async Runtime**: Tokio for concurrent task execution
- **Database**: SQLite with SQLX for type safety
- **Error Handling**: Anyhow for error propagation
- **Serialization**: Serde for JSON handling

### Directory Structure
```
/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/
├── src/
│   ├── main.rs                    # Application entry point
│   ├── lib.rs                     # Library exports
│   ├── app_state.rs              # Shared application state
│   ├── executor.rs               # AI agent execution framework
│   ├── execution_monitor.rs      # Process monitoring
│   ├── utils.rs                  # Utility functions
│   ├── models/                   # Data models and database logic
│   │   ├── mod.rs
│   │   ├── task.rs              # Task management
│   │   ├── project.rs           # Project configuration
│   │   ├── task_attempt.rs      # Task execution attempts
│   │   ├── executor_session.rs  # Agent session management
│   │   └── config.rs            # Configuration management
│   ├── routes/                   # HTTP route handlers
│   │   ├── mod.rs
│   │   ├── tasks.rs             # Task CRUD operations
│   │   ├── projects.rs          # Project management
│   │   ├── task_attempts.rs     # Execution tracking
│   │   ├── auth.rs              # Authentication
│   │   └── health.rs            # Health checks
│   ├── executors/               # AI agent implementations
│   │   ├── mod.rs
│   │   ├── claude.rs            # Claude Code integration
│   │   ├── gemini.rs            # Gemini CLI integration
│   │   ├── amp.rs               # Amp executor
│   │   ├── opencode.rs          # OpenCode executor
│   │   ├── echo.rs              # Echo executor (testing)
│   │   └── setup_script.rs      # Setup script executor
│   ├── services/                # Business logic services
│   │   ├── mod.rs
│   │   ├── git_service.rs       # Git operations
│   │   ├── github_service.rs    # GitHub API integration
│   │   ├── notification_service.rs # System notifications
│   │   ├── process_service.rs   # Process management
│   │   └── analytics.rs         # Usage analytics
│   ├── mcp/                     # Model Context Protocol
│   │   ├── mod.rs
│   │   └── task_server.rs       # MCP task server
│   └── utils/                   # Utility modules
│       ├── shell.rs             # Shell command utilities
│       ├── text.rs              # Text processing
│       └── worktree_manager.rs  # Git worktree management
├── migrations/                   # Database migrations
├── scripts/                     # Build and utility scripts
└── sounds/                      # Notification sounds
```

### Database Schema Architecture

#### Core Tables
1. **projects** - Project configuration and metadata
2. **tasks** - Task definitions and status
3. **task_attempts** - Execution attempts with agent assignments
4. **execution_processes** - Process tracking and monitoring
5. **task_attempt_activities** - Activity logs and status updates
6. **executor_sessions** - Agent session management

#### Key Relationships
```sql
projects (1) -> (many) tasks
tasks (1) -> (many) task_attempts
task_attempts (1) -> (many) execution_processes
execution_processes (1) -> (many) task_attempt_activities
```

### API Layer Design

#### RESTful Endpoints
- **Projects**: `/api/projects/*` - Project CRUD operations
- **Tasks**: `/api/tasks/*` - Task management
- **Attempts**: `/api/task-attempts/*` - Execution tracking
- **Config**: `/api/config/*` - System configuration
- **Health**: `/api/health` - System health checks

#### WebSocket Connections
- **Real-time Updates**: Live task status updates
- **Log Streaming**: Real-time execution logs
- **Process Monitoring**: Live process status updates

### AI Agent Execution Framework

#### Executor Trait
```rust
#[async_trait]
pub trait Executor {
    async fn execute(&self, task: &Task, context: &ExecutionContext) -> Result<ExecutionResult>;
    fn get_type(&self) -> ExecutorType;
    fn normalize_conversation(&self, raw_data: &str) -> Result<NormalizedConversation>;
}
```

#### Supported Executors
1. **ClaudeExecutor**: Claude Code integration
2. **GeminiExecutor**: Gemini CLI integration  
3. **AmpExecutor**: Amp framework support
4. **OpencodeExecutor**: OpenCode platform
5. **EchoExecutor**: Testing and development
6. **SetupScriptExecutor**: Environment setup

### Process Management

#### Execution Monitoring
- **Process Tracking**: Real-time process status monitoring
- **Resource Management**: CPU and memory usage tracking
- **Error Detection**: Automated error detection and recovery
- **Timeout Handling**: Configurable execution timeouts

#### State Management
- **Application State**: Shared state across all components
- **Session Management**: Persistent agent sessions
- **Configuration State**: Dynamic configuration updates
- **Cache Management**: Efficient data caching strategies

## Frontend Architecture

### React Application Structure
```
/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/
├── src/
│   ├── main.tsx                 # Application entry point
│   ├── App.tsx                  # Root component
│   ├── index.css               # Global styles
│   ├── components/             # Reusable components
│   │   ├── layout/
│   │   │   └── navbar.tsx      # Navigation bar
│   │   ├── projects/           # Project components
│   │   │   ├── project-list.tsx
│   │   │   ├── project-detail.tsx
│   │   │   └── project-form.tsx
│   │   ├── tasks/              # Task components
│   │   │   ├── TaskCard.tsx
│   │   │   ├── TaskKanbanBoard.tsx
│   │   │   ├── TaskDetails/
│   │   │   │   ├── Conversation.tsx
│   │   │   │   ├── DiffTab.tsx
│   │   │   │   └── LogsTab.tsx
│   │   │   └── Toolbar/
│   │   │       ├── CreateAttempt.tsx
│   │   │       └── CreatePRDialog.tsx
│   │   ├── ui/                 # UI components
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── dialog.tsx
│   │   │   └── shadcn-io/
│   │   │       └── kanban/
│   │   └── context/            # React contexts
│   │       └── TaskDetailsContextProvider.tsx
│   ├── pages/                  # Page components
│   │   ├── projects.tsx
│   │   ├── project-tasks.tsx
│   │   ├── Settings.tsx
│   │   └── McpServers.tsx
│   └── lib/                    # Utility libraries
│       ├── api.ts              # API client
│       ├── utils.ts            # Utility functions
│       └── keyboard-shortcuts.ts # Keyboard shortcuts
├── public/                     # Static assets
└── tailwind.config.js         # Tailwind configuration
```

### Component Architecture

#### Page-Level Components
- **ProjectsPage**: Project listing and management
- **ProjectTasksPage**: Task management and Kanban board
- **Settings**: Configuration and preferences
- **McpServers**: MCP server management

#### Feature Components
- **TaskKanbanBoard**: Drag-and-drop task management
- **TaskDetails**: Comprehensive task information
- **Conversation**: AI agent conversation viewer
- **DiffTab**: Code diff visualization
- **LogsTab**: Real-time log streaming

#### UI Components
- **shadcn/ui**: Comprehensive UI component library
- **Custom Components**: Domain-specific UI elements
- **Responsive Design**: Mobile-first responsive layouts

### State Management

#### Context Providers
- **TaskDetailsContext**: Task-specific state management
- **ConfigProvider**: Application configuration
- **ThemeProvider**: Dark/light theme management

#### API Integration
- **Type-Safe API**: Shared TypeScript types with backend
- **Real-time Updates**: WebSocket integration for live updates
- **Error Handling**: Comprehensive error boundary implementation

## Inter-Service Communication

### Backend-Frontend Communication
- **REST API**: Standard HTTP/JSON for CRUD operations
- **WebSocket**: Real-time updates and live data streaming
- **Type Safety**: Shared TypeScript types generated from Rust

### Agent Communication
- **Process Spawning**: Controlled process creation and management
- **Stream Processing**: Real-time output parsing and normalization
- **Error Handling**: Comprehensive error capture and reporting

### External Service Integration
- **GitHub API**: Repository management and PR creation
- **MCP Servers**: Model Context Protocol integration
- **Notification Services**: System notifications and alerts

## Performance Considerations

### Backend Performance
- **Async Processing**: Tokio-based concurrent execution
- **Database Optimization**: Efficient SQLite queries with proper indexing
- **Memory Management**: Rust's zero-cost abstractions
- **Connection Pooling**: Efficient database connection management

### Frontend Performance
- **Code Splitting**: Lazy loading of components
- **Memoization**: React.memo and useMemo optimizations
- **Virtual Scrolling**: Efficient rendering of large lists
- **Bundle Optimization**: Tree shaking and dead code elimination

### System Performance
- **Resource Monitoring**: CPU and memory usage tracking
- **Process Isolation**: Containerized agent execution
- **Caching Strategies**: Intelligent data caching
- **Load Balancing**: Distributed agent execution

## Security Architecture

### Authentication & Authorization
- **Token-based Auth**: JWT or similar for API access
- **Session Management**: Secure session handling
- **Role-based Access**: Granular permission system

### Data Security
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries with SQLX
- **XSS Protection**: Content Security Policy implementation
- **CSRF Protection**: Anti-CSRF token implementation

### Process Security
- **Sandboxing**: Isolated execution environments
- **Resource Limits**: CPU and memory constraints
- **File System Access**: Controlled file system operations
- **Network Isolation**: Restricted network access

## Scalability Considerations

### Horizontal Scaling
- **Stateless Design**: Stateless backend services
- **Load Balancing**: Multiple backend instances
- **Database Sharding**: Horizontal database scaling
- **Cache Distribution**: Distributed caching solutions

### Vertical Scaling
- **Resource Optimization**: Efficient resource utilization
- **Memory Management**: Optimal memory allocation
- **CPU Utilization**: Efficient CPU usage patterns
- **I/O Optimization**: Asynchronous I/O operations

## Monitoring & Observability

### Application Monitoring
- **Sentry Integration**: Error tracking and monitoring
- **Performance Metrics**: Application performance monitoring
- **Health Checks**: System health monitoring
- **Custom Metrics**: Business-specific metrics

### Infrastructure Monitoring
- **System Resources**: CPU, memory, disk usage
- **Network Monitoring**: Network performance and availability
- **Database Monitoring**: Query performance and optimization
- **Log Aggregation**: Centralized log collection and analysis

## Deployment Architecture

### Build Process
- **Rust Backend**: Cargo build with optimizations
- **React Frontend**: Vite build with bundling
- **Asset Optimization**: Image and asset optimization
- **Type Generation**: Automated TypeScript type generation

### Distribution
- **NPM Package**: Single package distribution
- **Cross-Platform**: macOS, Linux, Windows support
- **Self-Contained**: Embedded database and assets
- **Version Management**: Semantic versioning

### Runtime Environment
- **Single Binary**: Self-contained executable
- **Embedded Frontend**: Served from backend
- **Local Database**: SQLite for local storage
- **Configuration**: Environment-based configuration

This architecture provides a robust foundation for AI agent orchestration while maintaining excellent developer experience and system reliability. The combination of Rust's performance and safety with React's flexibility creates an optimal platform for modern AI-assisted development workflows.