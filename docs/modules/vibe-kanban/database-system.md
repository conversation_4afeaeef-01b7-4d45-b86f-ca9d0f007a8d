# Vibe Kanban - Database System Architecture

## Database Overview

The **Database System** utilizes **SQLite** as an embedded, high-performance database solution specifically optimized for AI agent orchestration and task management. This architecture provides excellent performance, data integrity, and simplified deployment while supporting complex queries and real-time operations.

## Database Technology Stack

### Core Database Technology
- **Database Engine**: SQLite (embedded, file-based)
- **ORM/Query Builder**: SQLX v0.8.6 with compile-time verification
- **Migration System**: SQL-based migrations with versioning
- **Features**: runtime-tokio-rustls, sqlite, chrono, uuid support

### Database Architecture Benefits
- **Embedded**: No separate database server required
- **ACID Compliance**: Full ACID transaction support
- **Performance**: Optimized for read-heavy workloads
- **Reliability**: Crash-safe with automatic recovery
- **Portability**: Single file database for easy deployment

## Database Schema Design

### Schema Evolution
- **Migration Directory**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/migrations/`
- **Versioning**: Timestamp-based migration files
- **Rollback Support**: Reversible migrations where possible
- **Schema Validation**: SQLX compile-time query verification

### Migration Files Timeline
1. `20250617183714_init.sql` - Initial database schema
2. `20250620212427_execution_processes.sql` - Process tracking tables
3. `20250620214100_remove_stdout_stderr_from_task_attempts.sql` - Schema optimization
4. `20250621120000_relate_activities_to_execution_processes.sql` - Activity relationships
5. `20250623120000_executor_sessions.sql` - Session management
6. `20250623130000_add_executor_type_to_execution_processes.sql` - Executor typing
7. `20250625000000_add_dev_script_to_projects.sql` - Development scripts
8. `20250701000000_add_branch_to_task_attempts.sql` - Git branch tracking
9. `20250701000001_add_pr_tracking_to_task_attempts.sql` - PR integration
10. `20250701120000_add_assistant_message_to_executor_sessions.sql` - Message tracking
11. `20250708000000_add_base_branch_to_task_attempts.sql` - Base branch support
12. `20250709000000_add_worktree_deleted_flag.sql` - Worktree management
13. `20250710000000_add_setup_completion.sql` - Setup completion tracking

## Core Database Tables

### Projects Table
```sql
CREATE TABLE projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    path TEXT NOT NULL,
    dev_script TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Purpose**: Store project configuration and metadata
**Key Features**:
- Project identification and naming
- File system path management
- Development script integration
- Timestamp tracking

### Tasks Table
```sql
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,
    project_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'todo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);
```

**Purpose**: Core task management and tracking
**Key Features**:
- Task lifecycle management
- Status tracking (todo, inprogress, inreview, done, cancelled)
- Project relationship
- Timestamp auditing

### Task Attempts Table
```sql
CREATE TABLE task_attempts (
    id TEXT PRIMARY KEY,
    task_id TEXT NOT NULL,
    executor_type TEXT NOT NULL,
    branch TEXT,
    base_branch TEXT,
    pr_url TEXT,
    pr_number INTEGER,
    merge_commit TEXT,
    worktree_deleted INTEGER DEFAULT 0,
    setup_completion INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);
```

**Purpose**: Track individual execution attempts for tasks
**Key Features**:
- Executor assignment tracking
- Git branch management
- PR integration
- Worktree lifecycle management
- Setup completion tracking

### Execution Processes Table
```sql
CREATE TABLE execution_processes (
    id TEXT PRIMARY KEY,
    task_attempt_id TEXT NOT NULL,
    executor_type TEXT NOT NULL,
    process_type TEXT NOT NULL,
    pid INTEGER,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    exit_code INTEGER,
    FOREIGN KEY (task_attempt_id) REFERENCES task_attempts(id) ON DELETE CASCADE
);
```

**Purpose**: Track individual execution processes within attempts
**Key Features**:
- Process lifecycle management
- Exit code tracking
- Executor type identification
- Process timing information

### Task Attempt Activities Table
```sql
CREATE TABLE task_attempt_activities (
    id TEXT PRIMARY KEY,
    execution_process_id TEXT NOT NULL,
    status TEXT NOT NULL,
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (execution_process_id) REFERENCES execution_processes(id) ON DELETE CASCADE
);
```

**Purpose**: Detailed activity logging for task attempts
**Key Features**:
- Activity stream management
- Status change tracking
- Message logging
- Timeline construction

### Executor Sessions Table
```sql
CREATE TABLE executor_sessions (
    id TEXT PRIMARY KEY,
    executor_type TEXT NOT NULL,
    session_data TEXT,
    assistant_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Purpose**: AI agent session management and persistence
**Key Features**:
- Session state persistence
- Conversation tracking
- Usage tracking
- Session cleanup support

## Database Relationships

### Entity Relationship Diagram
```mermaid
erDiagram
    projects ||--o{ tasks : "contains"
    tasks ||--o{ task_attempts : "has"
    task_attempts ||--o{ execution_processes : "executes"
    execution_processes ||--o{ task_attempt_activities : "logs"
    executor_sessions ||--o{ execution_processes : "uses"
    
    projects {
        string id PK
        string name
        string description
        string path
        string dev_script
        timestamp created_at
        timestamp updated_at
    }
    
    tasks {
        string id PK
        string project_id FK
        string title
        string description
        string status
        timestamp created_at
        timestamp updated_at
    }
    
    task_attempts {
        string id PK
        string task_id FK
        string executor_type
        string branch
        string base_branch
        string pr_url
        integer pr_number
        string merge_commit
        boolean worktree_deleted
        boolean setup_completion
        timestamp created_at
        timestamp updated_at
    }
    
    execution_processes {
        string id PK
        string task_attempt_id FK
        string executor_type
        string process_type
        integer pid
        timestamp started_at
        timestamp completed_at
        integer exit_code
    }
    
    task_attempt_activities {
        string id PK
        string execution_process_id FK
        string status
        string message
        timestamp created_at
    }
    
    executor_sessions {
        string id PK
        string executor_type
        string session_data
        string assistant_message
        timestamp created_at
        timestamp updated_at
        timestamp last_used_at
    }
```

### Key Relationships
1. **Projects → Tasks**: One-to-many relationship
2. **Tasks → Task Attempts**: One-to-many relationship
3. **Task Attempts → Execution Processes**: One-to-many relationship
4. **Execution Processes → Activities**: One-to-many relationship
5. **Executor Sessions → Processes**: One-to-many relationship (via executor_type)

## Database Operations and Queries

### SQLX Integration
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/`
- **Features**:
  - Compile-time query verification
  - Type-safe database operations
  - Automatic migration support
  - Connection pooling

### Task Model Operations
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/task.rs`
- **Key Operations**:
  - `find_by_project_id_with_attempt_status()` - Complex task retrieval with status
  - `create()` - Task creation with validation
  - `update()` - Task updates with optimistic locking
  - `update_status()` - Efficient status updates

### Complex Query Example
```rust
// Complex query with multiple joins and aggregations
pub async fn find_by_project_id_with_attempt_status(
    pool: &SqlitePool,
    project_id: Uuid,
) -> Result<Vec<TaskWithAttemptStatus>, sqlx::Error> {
    let records = sqlx::query!(
        r#"SELECT 
            t.id                  AS "id!: Uuid", 
            t.project_id          AS "project_id!: Uuid", 
            t.title, 
            t.description, 
            t.status              AS "status!: TaskStatus", 
            t.created_at          AS "created_at!: DateTime<Utc>", 
            t.updated_at          AS "updated_at!: DateTime<Utc>",
            CASE 
            WHEN in_progress_attempts.task_id IS NOT NULL THEN true 
            ELSE false 
            END                   AS "has_in_progress_attempt!: i64",
            CASE 
            WHEN merged_attempts.task_id IS NOT NULL THEN true 
            ELSE false 
            END                   AS "has_merged_attempt!",
            CASE 
            WHEN failed_attempts.task_id IS NOT NULL THEN true 
            ELSE false 
            END                   AS "has_failed_attempt!"
        FROM tasks t
        LEFT JOIN (
            SELECT DISTINCT ta.task_id
            FROM task_attempts ta
            JOIN execution_processes ep ON ta.id = ep.task_attempt_id
            JOIN (
                SELECT execution_process_id, status
                FROM (
                    SELECT
                        execution_process_id,
                        status,
                        ROW_NUMBER() OVER (
                            PARTITION BY execution_process_id
                            ORDER BY created_at DESC,
                            CASE 
                            WHEN status IN ('setuprunning','executorrunning') THEN 1 
                            ELSE 0 
                            END
                        ) AS rn
                    FROM task_attempt_activities
                ) sub
                WHERE rn = 1
            ) latest_act ON ep.id = latest_act.execution_process_id
            WHERE latest_act.status IN ('setuprunning','executorrunning')
        ) in_progress_attempts ON t.id = in_progress_attempts.task_id
        WHERE t.project_id = $1
        ORDER BY t.created_at DESC;
        "#,
        project_id
    )
    .fetch_all(pool)
    .await?;
    
    Ok(records.into_iter().map(|record| TaskWithAttemptStatus {
        // Field mapping...
    }).collect())
}
```

## Database Performance Optimization

### Indexing Strategy
```sql
-- Primary key indexes (automatic)
-- Foreign key indexes
CREATE INDEX idx_tasks_project_id ON tasks(project_id);
CREATE INDEX idx_task_attempts_task_id ON task_attempts(task_id);
CREATE INDEX idx_execution_processes_task_attempt_id ON execution_processes(task_attempt_id);
CREATE INDEX idx_task_attempt_activities_execution_process_id ON task_attempt_activities(execution_process_id);

-- Status and timestamp indexes
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_task_attempts_created_at ON task_attempts(created_at);
CREATE INDEX idx_execution_processes_started_at ON execution_processes(started_at);

-- Composite indexes for common queries
CREATE INDEX idx_tasks_project_status ON tasks(project_id, status);
CREATE INDEX idx_task_attempts_task_executor ON task_attempts(task_id, executor_type);
```

### Query Optimization
- **Prepared Statements**: All queries use prepared statements
- **Connection Pooling**: Efficient connection management
- **Query Analysis**: EXPLAIN QUERY PLAN for optimization
- **Batch Operations**: Batch inserts and updates where possible

### Performance Monitoring
- **Query Timing**: Track query execution times
- **Connection Metrics**: Monitor connection pool usage
- **Cache Hit Rates**: Monitor SQLite cache effectiveness
- **Index Usage**: Track index utilization

## Database Backup and Recovery

### Backup Strategy
- **Hot Backups**: SQLite backup API for online backups
- **Incremental Backups**: WAL mode for incremental backups
- **Automated Backups**: Scheduled backup automation
- **Backup Validation**: Integrity checks for backups

### Recovery Procedures
- **Point-in-Time Recovery**: Restore to specific timestamps
- **Corruption Recovery**: Handle database corruption
- **Migration Recovery**: Rollback failed migrations
- **Data Validation**: Verify data integrity post-recovery

### Backup Implementation
```rust
// Example backup implementation
pub async fn backup_database(source: &SqlitePool, backup_path: &str) -> Result<(), sqlx::Error> {
    let backup_sql = format!("BACKUP TO '{}'", backup_path);
    sqlx::query(&backup_sql)
        .execute(source)
        .await?;
    Ok(())
}
```

## Database Security

### Security Features
- **Access Control**: File-based access control
- **Connection Security**: TLS connections where applicable
- **SQL Injection Prevention**: Parameterized queries only
- **Data Encryption**: File-level encryption support

### Security Best Practices
- **Input Validation**: Comprehensive input validation
- **Audit Logging**: Database access logging
- **Principle of Least Privilege**: Minimal required permissions
- **Regular Security Updates**: Keep SQLite updated

## Database Monitoring and Maintenance

### Monitoring Features
- **Performance Metrics**: Query performance tracking
- **Connection Monitoring**: Connection pool statistics
- **Error Tracking**: Database error logging
- **Resource Usage**: Monitor database file size and growth

### Maintenance Tasks
- **VACUUM**: Periodic database optimization
- **ANALYZE**: Update query planner statistics
- **Integrity Checks**: Regular integrity verification
- **Log Cleanup**: Prune old activity logs

### Maintenance Implementation
```rust
// Example maintenance tasks
pub async fn vacuum_database(pool: &SqlitePool) -> Result<(), sqlx::Error> {
    sqlx::query("VACUUM")
        .execute(pool)
        .await?;
    Ok(())
}

pub async fn analyze_database(pool: &SqlitePool) -> Result<(), sqlx::Error> {
    sqlx::query("ANALYZE")
        .execute(pool)
        .await?;
    Ok(())
}
```

## Database Configuration

### SQLite Configuration
```sql
-- WAL mode for better concurrency
PRAGMA journal_mode = WAL;

-- Synchronous mode for performance/durability balance
PRAGMA synchronous = NORMAL;

-- Cache size optimization
PRAGMA cache_size = -64000; -- 64MB cache

-- Foreign key enforcement
PRAGMA foreign_keys = ON;

-- Automatic indexing
PRAGMA automatic_index = ON;
```

### Connection Pool Configuration
```rust
// SQLX connection pool configuration
let pool = SqlitePoolOptions::new()
    .max_connections(10)
    .acquire_timeout(Duration::from_secs(30))
    .connect("sqlite:database.db")
    .await?;
```

## Database Migration Management

### Migration System
- **Version Control**: Migrations stored in version control
- **Rollback Support**: Reversible migrations where possible
- **Testing**: Migration testing in development
- **Automation**: Automated migration deployment

### Migration Example
```sql
-- Migration: Add new column
ALTER TABLE tasks ADD COLUMN priority INTEGER DEFAULT 0;

-- Create index for new column
CREATE INDEX idx_tasks_priority ON tasks(priority);

-- Update existing records
UPDATE tasks SET priority = 1 WHERE status = 'todo';
```

## Database Testing

### Testing Strategy
- **Unit Tests**: Test individual database operations
- **Integration Tests**: Test complex queries and transactions
- **Performance Tests**: Benchmark query performance
- **Migration Tests**: Test migration rollback and forward

### Test Database Management
- **In-Memory Testing**: Use in-memory SQLite for fast tests
- **Test Fixtures**: Consistent test data setup
- **Cleanup**: Automatic test database cleanup
- **Isolation**: Isolated test environments

## Database Documentation

### Schema Documentation
- **Table Descriptions**: Comprehensive table documentation
- **Column Specifications**: Detailed column documentation
- **Relationship Diagrams**: Visual relationship documentation
- **Index Documentation**: Index purpose and usage

### Query Documentation
- **Query Comments**: Inline query documentation
- **Performance Notes**: Query performance characteristics
- **Usage Examples**: Common query patterns
- **Optimization Tips**: Query optimization guidance

## Future Database Enhancements

### Planned Improvements
- **Read Replicas**: Read-only replicas for scaling
- **Partitioning**: Table partitioning for large datasets
- **Compression**: Data compression for storage efficiency
- **Encryption**: Advanced encryption features

### Scalability Considerations
- **Horizontal Scaling**: Multiple database instances
- **Vertical Scaling**: Optimize for larger workloads
- **Caching**: Advanced caching strategies
- **Archiving**: Historical data archiving

This comprehensive database system provides a robust foundation for AI agent orchestration, offering excellent performance, data integrity, and scalability while maintaining simplicity and reliability.