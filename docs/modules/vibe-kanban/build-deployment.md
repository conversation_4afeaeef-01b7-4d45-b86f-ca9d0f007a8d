# Vibe Kanban - Build and Deployment System

## Build System Overview

The **Build and Deployment System** orchestrates the compilation, packaging, and distribution of both Rust backend and React frontend components into a single, deployable NPM package. This system enables seamless distribution via `npx vibe-kanban` while maintaining development efficiency.

## Build Architecture

### Multi-Stage Build Process
1. **Frontend Build**: React TypeScript compilation with Vite
2. **Backend Build**: Rust compilation with Cargo
3. **Asset Embedding**: Frontend assets embedded in Rust binary
4. **Package Assembly**: Complete package creation for NPM distribution
5. **Distribution**: NPM package publishing and deployment

### Build Tools and Dependencies
- **Frontend**: Vite (v5.0.8) for fast builds and HMR
- **Backend**: Cargo with custom build scripts
- **Package Manager**: pnpm (v8+) for monorepo management
- **Concurrency**: concurrently (v8.2.2) for parallel builds
- **Cross-Platform**: Node.js scripts for platform compatibility

## Development Build Process

### Development Server
- **Command**: `pnpm dev`
- **Script**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/package.json`
- **Features**:
  - Concurrent frontend and backend development
  - Hot module replacement (HMR)
  - Automatic port allocation
  - Live reloading

### Development Build Configuration
```json
{
  "scripts": {
    "dev": "export FRONTEND_PORT=$(node scripts/setup-dev-environment.js frontend) && export BACKEND_PORT=$(node scripts/setup-dev-environment.js backend) && concurrently \"npm run backend:dev:watch\" \"npm run frontend:dev\"",
    "frontend:dev": "cd frontend && npm run dev -- --port ${FRONTEND_PORT:-3000} --open",
    "backend:dev:watch": "DISABLE_WORKTREE_ORPHAN_CLEANUP=1 npm run cargo -- watch -w backend -x 'run --manifest-path backend/Cargo.toml'"
  }
}
```

### Environment Setup
- **Script**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/scripts/setup-dev-environment.js`
- **Features**:
  - Dynamic port allocation
  - Environment variable management
  - Development database setup
  - Port conflict resolution

## Production Build Process

### Frontend Build
- **Command**: `npm run frontend:build`
- **Tool**: Vite production build
- **Output**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/dist/`
- **Features**:
  - TypeScript compilation
  - Asset optimization
  - Code splitting
  - Tree shaking

### Backend Build
- **Command**: `npm run backend:build`
- **Tool**: Cargo release build
- **Output**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/target/release/`
- **Features**:
  - Optimized Rust compilation
  - Frontend asset embedding
  - Cross-compilation support
  - Debug symbol handling

### Build Configuration
```json
{
  "scripts": {
    "build": "npm run frontend:build && npm run backend:build",
    "frontend:build": "cd frontend && npm run build",
    "backend:build": "npm run cargo -- build --release --manifest-path backend/Cargo.toml && npm run cargo -- build --release --bin mcp_task_server --manifest-path backend/Cargo.toml"
  }
}
```

## Asset Management

### Asset Embedding
- **Library**: rust-embed (v8.2)
- **Purpose**: Embed frontend assets in Rust binary
- **Benefits**:
  - Single binary distribution
  - No external dependencies
  - Simplified deployment
  - Consistent asset serving

### Asset Processing
```rust
// Asset embedding configuration
#[derive(RustEmbed)]
#[folder = "../frontend/dist/"]
pub struct Assets;

impl Assets {
    pub fn serve_asset(path: &str) -> Option<Response> {
        Self::get(path).map(|content| {
            Response::builder()
                .header("content-type", mime_guess::from_path(path).first_or_octet_stream().as_ref())
                .body(content.data.into())
                .unwrap()
        })
    }
}
```

### Static Asset Organization
- **Public Assets**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/public/`
- **Favicon**: Multiple sizes and formats
- **Icons**: PWA icons and touch icons
- **Images**: Logo variants and screenshots
- **Manifest**: Web app manifest

## NPM Package Creation

### Package Build Script
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/build-npm-package.sh`
- **Purpose**: Create complete NPM package
- **Process**:
  1. Build frontend and backend
  2. Copy binaries to distribution folder
  3. Create package structure
  4. Generate package.json
  5. Create tarball for distribution

### Package Structure
```
npx-cli/
├── bin/
│   └── vibe-kanban              # Main executable
├── package.json                 # NPM package configuration
└── README.md                   # Package documentation
```

### Package Configuration
```json
{
  "name": "vibe-kanban",
  "version": "0.0.47",
  "description": "AI coding agent orchestration platform",
  "bin": {
    "vibe-kanban": "./bin/vibe-kanban"
  },
  "files": [
    "bin/",
    "README.md"
  ],
  "engines": {
    "node": ">=18"
  }
}
```

## Database Management

### Database Preparation
- **Script**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/scripts/prepare-db.js`
- **Purpose**: Prepare database for build
- **Features**:
  - Migration validation
  - Schema generation
  - SQLX macro compilation
  - Database seeding

### Development Database
- **Seed Data**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/dev_assets_seed/`
- **Files**:
  - `config.json` - Default configuration
  - `db.sqlite` - Seed database
- **Purpose**: Consistent development environment

## Type Generation

### Rust-to-TypeScript Types
- **Command**: `npm run generate-types`
- **Tool**: ts-rs (v9.0) for type generation
- **Output**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/shared/types.ts`
- **Features**:
  - Automatic type synchronization
  - Compile-time type checking
  - Shared type definitions
  - Type safety across languages

### Type Generation Process
```rust
// Type generation binary
// File: backend/src/bin/generate_types.rs
use ts_rs::TS;

fn main() {
    // Generate TypeScript types
    Task::export().unwrap();
    TaskAttempt::export().unwrap();
    Project::export().unwrap();
    ExecutorSession::export().unwrap();
    
    println!("TypeScript types generated successfully!");
}
```

## Build Optimization

### Rust Build Optimization
```toml
[profile.release]
debug = true
split-debuginfo = "packed"
strip = true
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
```

### Frontend Build Optimization
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    target: 'es2020',
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dropdown-menu', '@radix-ui/react-dialog'],
        },
      },
    },
  },
});
```

## Cross-Platform Compilation

### Platform Support
- **macOS**: Native ARM64 and x86_64 support
- **Linux**: GNU and musl targets
- **Windows**: Native Windows support
- **Distribution**: Platform-specific binaries

### Cross-Compilation Configuration
```bash
# Cross-compilation targets
rustup target add x86_64-unknown-linux-gnu
rustup target add x86_64-pc-windows-gnu
rustup target add aarch64-apple-darwin
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Build and Test
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      - name: Install dependencies
        run: pnpm install
      - name: Build application
        run: pnpm build
      - name: Run tests
        run: pnpm test
```

### Build Automation
- **Automated Builds**: Trigger builds on code changes
- **Testing**: Automated testing before deployment
- **Deployment**: Automatic deployment to npm registry
- **Versioning**: Semantic versioning with automated tagging

## Testing and Validation

### Package Testing
- **Script**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/test-npm-package.sh`
- **Purpose**: Validate package installation and functionality
- **Tests**:
  - Package installation
  - Binary execution
  - Basic functionality
  - Error handling

### Build Validation
- **Frontend Tests**: Component and integration tests
- **Backend Tests**: Unit and integration tests
- **End-to-End Tests**: Complete workflow testing
- **Performance Tests**: Build performance validation

## Deployment Strategies

### NPM Registry Deployment
- **Registry**: npm public registry
- **Authentication**: NPM token authentication
- **Versioning**: Semantic versioning
- **Release Process**: Automated release pipeline

### Installation Flow
```bash
# User installation
npx vibe-kanban

# Package resolution
npm registry → download package → extract → execute
```

### Distribution Optimization
- **Package Size**: Optimize package size
- **Download Speed**: Minimize download time
- **Startup Time**: Optimize application startup
- **Memory Usage**: Efficient memory utilization

## Monitoring and Analytics

### Build Metrics
- **Build Time**: Track build duration
- **Package Size**: Monitor package size growth
- **Success Rate**: Track build success rates
- **Error Analysis**: Analyze build failures

### Deployment Metrics
- **Download Statistics**: Track package downloads
- **Usage Analytics**: Monitor usage patterns
- **Error Reporting**: Track runtime errors
- **Performance Metrics**: Monitor application performance

## Security Considerations

### Build Security
- **Dependency Scanning**: Scan for vulnerabilities
- **Code Signing**: Sign binaries for authenticity
- **Supply Chain Security**: Secure build pipeline
- **Audit Trail**: Complete build audit trail

### Distribution Security
- **Package Integrity**: Verify package integrity
- **Authentication**: Secure authentication
- **Authorization**: Proper access controls
- **Monitoring**: Security monitoring

## Troubleshooting

### Common Build Issues
1. **Type Generation Failures**: SQLX macro compilation issues
2. **Asset Embedding**: Frontend build missing or corrupt
3. **Cross-Platform Issues**: Platform-specific build failures
4. **Dependency Conflicts**: Version conflicts between components

### Debug Tools
- **Verbose Builds**: Enable verbose build output
- **Build Logs**: Comprehensive build logging
- **Error Analysis**: Detailed error reporting
- **Performance Profiling**: Build performance analysis

## Future Enhancements

### Planned Improvements
- **Docker Support**: Containerized builds
- **Cloud Builds**: Cloud-based build system
- **Incremental Builds**: Faster incremental builds
- **Build Caching**: Distributed build caching

### Advanced Features
- **Multi-Architecture**: Support for more architectures
- **Hot Deployment**: Live deployment updates
- **Rollback Support**: Automated rollback capabilities
- **Blue-Green Deployment**: Zero-downtime deployments

This comprehensive build and deployment system ensures reliable, efficient, and secure distribution of Vibe Kanban while maintaining excellent developer experience and user accessibility.