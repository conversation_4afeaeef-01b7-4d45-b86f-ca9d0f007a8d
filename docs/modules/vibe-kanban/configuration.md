# Vibe Kanban - Configuration Management

## Configuration Overview

The **Configuration Management System** provides comprehensive settings management for AI agents, system preferences, and project-specific configurations. This system enables centralized control over all aspects of the Vibe Kanban platform while maintaining flexibility for different environments and use cases.

## Configuration Architecture

### Configuration Layers
1. **System Configuration**: Global system settings
2. **Agent Configuration**: AI agent-specific settings
3. **Project Configuration**: Project-specific settings
4. **User Configuration**: User preferences and settings
5. **Environment Configuration**: Environment-specific overrides

### Configuration Sources
- **Database**: Persistent configuration storage
- **Environment Variables**: Runtime configuration
- **Configuration Files**: File-based configuration
- **Command Line**: CLI argument overrides
- **UI Settings**: User interface configuration

## System Configuration

### Configuration Model
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/models/config.rs`
- **Purpose**: System-wide configuration management
- **Features**:
  - Configuration CRUD operations
  - Environment management
  - Validation and defaults
  - Change tracking

### Configuration Structure
```rust
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SystemConfig {
    pub system: SystemSettings,
    pub agents: AgentConfigs,
    pub projects: ProjectDefaults,
    pub ui: UiSettings,
    pub security: SecuritySettings,
    pub performance: PerformanceSettings,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemSettings {
    pub app_name: String,
    pub version: String,
    pub debug_mode: bool,
    pub log_level: String,
    pub data_directory: String,
    pub max_concurrent_tasks: u32,
}
```

### Configuration Routes
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/backend/src/routes/config.rs`
- **Endpoints**:
  - `GET /api/config` - Get system configuration
  - `PUT /api/config` - Update system configuration
  - `GET /api/config/agents` - Get agent configurations
  - `PUT /api/config/agents` - Update agent configurations
  - `GET /api/config/projects` - Get project defaults
  - `PUT /api/config/projects` - Update project defaults

## Agent Configuration

### Agent Configuration Structure
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentConfig {
    pub agent_type: String,
    pub enabled: bool,
    pub priority: u32,
    pub max_concurrent_tasks: u32,
    pub timeout_seconds: u32,
    pub retry_attempts: u32,
    pub custom_settings: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentConfigs {
    pub claude: AgentConfig,
    pub gemini: AgentConfig,
    pub amp: AgentConfig,
    pub opencode: AgentConfig,
    pub echo: AgentConfig,
    pub setup_script: AgentConfig,
}
```

### Agent-Specific Settings

#### Claude Configuration
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClaudeConfig {
    pub api_key: Option<String>,
    pub model: String,
    pub max_tokens: u32,
    pub temperature: f32,
    pub top_p: f32,
    pub presence_penalty: f32,
    pub frequency_penalty: f32,
    pub system_prompt: Option<String>,
    pub context_window: u32,
}
```

#### Gemini Configuration
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeminiConfig {
    pub api_key: Option<String>,
    pub model: String,
    pub max_tokens: u32,
    pub temperature: f32,
    pub top_k: u32,
    pub top_p: f32,
    pub safety_settings: Vec<SafetySetting>,
    pub generation_config: GenerationConfig,
}
```

#### Amp Configuration
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AmpConfig {
    pub endpoint: String,
    pub api_key: Option<String>,
    pub version: String,
    pub timeout: u32,
    pub batch_size: u32,
    pub parallel_requests: u32,
}
```

### Agent Configuration Management
- **Dynamic Updates**: Real-time configuration updates
- **Validation**: Configuration validation and error handling
- **Defaults**: Sensible default configurations
- **Profiles**: Multiple configuration profiles

## Project Configuration

### Project Configuration Model
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectConfig {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub path: String,
    pub dev_script: Option<String>,
    pub preferred_agents: Vec<String>,
    pub git_settings: GitSettings,
    pub build_settings: BuildSettings,
    pub deployment_settings: DeploymentSettings,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitSettings {
    pub repository_url: Option<String>,
    pub default_branch: String,
    pub auto_create_branches: bool,
    pub auto_create_prs: bool,
    pub pr_template: Option<String>,
}
```

### Project Configuration Components
- **Project Form**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/project-form.tsx`
- **Project Detail**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/projects/project-detail.tsx`
- **Features**:
  - Project creation and editing
  - Configuration validation
  - Default value management
  - Template support

## User Configuration

### User Preferences
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserConfig {
    pub user_id: String,
    pub theme: String,
    pub language: String,
    pub timezone: String,
    pub notification_settings: NotificationSettings,
    pub keyboard_shortcuts: KeyboardShortcuts,
    pub display_settings: DisplaySettings,
    pub privacy_settings: PrivacySettings,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationSettings {
    pub desktop_notifications: bool,
    pub sound_notifications: bool,
    pub email_notifications: bool,
    pub notification_types: Vec<String>,
}
```

### User Interface Configuration
- **Settings Page**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/Settings.tsx`
- **Theme Provider**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/theme-provider.tsx`
- **Config Provider**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/components/config-provider.tsx`

## MCP Configuration

### MCP Server Configuration
- **Page**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/frontend/src/pages/McpServers.tsx`
- **Purpose**: Configure Model Context Protocol servers
- **Features**:
  - Server endpoint configuration
  - Authentication setup
  - Capability configuration
  - Connection testing

### MCP Configuration Structure
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpServerConfig {
    pub server_id: String,
    pub name: String,
    pub endpoint: String,
    pub auth_type: AuthType,
    pub credentials: Option<Credentials>,
    pub capabilities: Vec<String>,
    pub enabled: bool,
    pub timeout: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpConfig {
    pub servers: Vec<McpServerConfig>,
    pub global_settings: McpGlobalSettings,
}
```

## Environment Configuration

### Environment Variables
```bash
# Development Environment
NODE_ENV=development
RUST_LOG=debug
DATABASE_URL=sqlite:dev.db
FRONTEND_PORT=3000
BACKEND_PORT=3001

# Production Environment
NODE_ENV=production
RUST_LOG=info
DATABASE_URL=sqlite:prod.db
SENTRY_DSN=your_sentry_dsn
```

### Environment Management
- **Development**: Local development configuration
- **Testing**: Test environment configuration
- **Staging**: Staging environment configuration
- **Production**: Production environment configuration

### Environment Loading
```rust
// Environment configuration loading
#[derive(Debug, Clone)]
pub struct EnvConfig {
    pub database_url: String,
    pub log_level: String,
    pub port: u16,
    pub frontend_port: u16,
    pub sentry_dsn: Option<String>,
}

impl EnvConfig {
    pub fn load() -> Result<Self, ConfigError> {
        Ok(Self {
            database_url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| "sqlite:vibe-kanban.db".to_string()),
            log_level: env::var("RUST_LOG")
                .unwrap_or_else(|_| "info".to_string()),
            port: env::var("BACKEND_PORT")
                .unwrap_or_else(|_| "3001".to_string())
                .parse()?,
            frontend_port: env::var("FRONTEND_PORT")
                .unwrap_or_else(|_| "3000".to_string())
                .parse()?,
            sentry_dsn: env::var("SENTRY_DSN").ok(),
        })
    }
}
```

## Configuration Validation

### Validation Framework
```rust
#[derive(Debug, thiserror::Error)]
pub enum ConfigError {
    #[error("Invalid configuration: {0}")]
    Invalid(String),
    
    #[error("Missing required field: {0}")]
    MissingField(String),
    
    #[error("Invalid value for {field}: {value}")]
    InvalidValue { field: String, value: String },
    
    #[error("Configuration conflict: {0}")]
    Conflict(String),
}

pub trait Validate {
    fn validate(&self) -> Result<(), ConfigError>;
}
```

### Configuration Validators
```rust
impl Validate for AgentConfig {
    fn validate(&self) -> Result<(), ConfigError> {
        if self.max_concurrent_tasks == 0 {
            return Err(ConfigError::InvalidValue {
                field: "max_concurrent_tasks".to_string(),
                value: "0".to_string(),
            });
        }
        
        if self.timeout_seconds > 3600 {
            return Err(ConfigError::InvalidValue {
                field: "timeout_seconds".to_string(),
                value: self.timeout_seconds.to_string(),
            });
        }
        
        Ok(())
    }
}
```

## Configuration Storage

### Database Storage
```sql
CREATE TABLE configurations (
    id TEXT PRIMARY KEY,
    config_type TEXT NOT NULL,
    config_key TEXT NOT NULL,
    config_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(config_type, config_key)
);
```

### File-Based Storage
- **Development**: JSON configuration files
- **Production**: Database-backed configuration
- **Backup**: Configuration backup and restore
- **Migration**: Configuration migration support

## Configuration Security

### Sensitive Data Handling
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecureConfig {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub api_key: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub secret_key: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub database_password: Option<String>,
}
```

### Security Features
- **Encryption**: Encrypt sensitive configuration data
- **Access Control**: Role-based configuration access
- **Audit Trail**: Configuration change tracking
- **Masking**: Mask sensitive values in logs

## Configuration Management UI

### Settings Interface
```typescript
// Settings component structure
interface SettingsProps {
  config: SystemConfig;
  onConfigChange: (config: SystemConfig) => void;
  onSave: () => Promise<void>;
  onReset: () => void;
}

const Settings: React.FC<SettingsProps> = ({
  config,
  onConfigChange,
  onSave,
  onReset,
}) => {
  // Settings UI implementation
};
```

### Configuration Forms
- **System Settings**: Global system configuration
- **Agent Settings**: Per-agent configuration
- **Project Settings**: Project-specific configuration
- **User Preferences**: User preference management

## Configuration Defaults

### Default Configuration Values
```rust
impl Default for SystemConfig {
    fn default() -> Self {
        Self {
            system: SystemSettings {
                app_name: "Vibe Kanban".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
                debug_mode: false,
                log_level: "info".to_string(),
                data_directory: "~/.vibe-kanban".to_string(),
                max_concurrent_tasks: 10,
            },
            agents: AgentConfigs::default(),
            projects: ProjectDefaults::default(),
            ui: UiSettings::default(),
            security: SecuritySettings::default(),
            performance: PerformanceSettings::default(),
        }
    }
}
```

### Configuration Templates
- **Development Template**: Development environment defaults
- **Production Template**: Production environment defaults
- **Testing Template**: Testing environment defaults
- **Custom Templates**: User-defined templates

## Configuration Migration

### Migration System
```rust
#[derive(Debug, Clone)]
pub struct ConfigMigration {
    pub version: String,
    pub description: String,
    pub migrate_fn: fn(&mut SystemConfig) -> Result<(), ConfigError>,
}

pub struct ConfigMigrator {
    migrations: Vec<ConfigMigration>,
}

impl ConfigMigrator {
    pub fn migrate(&self, config: &mut SystemConfig) -> Result<(), ConfigError> {
        for migration in &self.migrations {
            (migration.migrate_fn)(config)?;
        }
        Ok(())
    }
}
```

### Migration Examples
- **Version Updates**: Migrate configuration between versions
- **Schema Changes**: Handle configuration schema changes
- **Default Updates**: Update default values
- **Cleanup**: Remove deprecated configuration

## Configuration Monitoring

### Configuration Metrics
- **Configuration Changes**: Track configuration modifications
- **Usage Patterns**: Monitor configuration usage
- **Performance Impact**: Measure configuration performance impact
- **Error Rates**: Track configuration-related errors

### Configuration Alerts
- **Invalid Configuration**: Alert on invalid configurations
- **Security Issues**: Alert on security-related configuration issues
- **Performance Issues**: Alert on performance-impacting configurations
- **Compliance**: Alert on compliance violations

## Configuration Backup and Recovery

### Backup Strategy
```rust
pub struct ConfigBackup {
    pub backup_id: String,
    pub timestamp: DateTime<Utc>,
    pub config_data: SystemConfig,
    pub checksum: String,
}

impl ConfigBackup {
    pub fn create(config: &SystemConfig) -> Self {
        let config_data = config.clone();
        let checksum = calculate_checksum(&config_data);
        
        Self {
            backup_id: Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            config_data,
            checksum,
        }
    }
}
```

### Recovery Features
- **Automatic Backups**: Scheduled configuration backups
- **Point-in-Time Recovery**: Restore to specific points in time
- **Selective Recovery**: Restore specific configuration sections
- **Backup Validation**: Verify backup integrity

## Configuration Testing

### Testing Framework
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config_validation() {
        let config = SystemConfig::default();
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_agent_config_validation() {
        let mut config = AgentConfig::default();
        config.max_concurrent_tasks = 0;
        assert!(config.validate().is_err());
    }
}
```

### Configuration Tests
- **Validation Tests**: Test configuration validation
- **Migration Tests**: Test configuration migration
- **Integration Tests**: Test configuration integration
- **Performance Tests**: Test configuration performance

## Future Enhancements

### Planned Features
- **Configuration Templates**: Pre-defined configuration templates
- **Advanced Validation**: More sophisticated validation rules
- **Configuration Profiles**: Multiple configuration profiles
- **Remote Configuration**: Remote configuration management

### Advanced Features
- **Configuration Sync**: Synchronize configuration across instances
- **Configuration Versioning**: Version configuration changes
- **Configuration Rollback**: Rollback configuration changes
- **Configuration Audit**: Comprehensive configuration auditing

This comprehensive configuration management system provides flexible, secure, and user-friendly configuration management for all aspects of the Vibe Kanban platform, enabling optimal performance and customization for different environments and use cases.