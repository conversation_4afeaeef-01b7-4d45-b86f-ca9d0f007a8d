# Vibe Kanban - Technology Stack Analysis

## Stack Overview

Vibe Kanban employs a **modern, polyglot technology stack** specifically optimized for AI agent orchestration and real-time collaboration. The stack combines the performance and safety of Rust with the flexibility of React, creating a robust platform for managing multiple AI coding assistants.

## Backend Technology Stack

### Core Runtime & Framework
- **Language**: Rust 2021 Edition
- **Web Framework**: Axum (v0.7+)
- **Async Runtime**: Tokio (workspace dependency)
- **HTTP Middleware**: Tower-HTTP for request/response processing

### Database & Persistence
- **Database**: SQLite (embedded, file-based)
- **ORM/Query Builder**: SQLX (v0.8.6)
  - Features: runtime-tokio-rustls, sqlite, chrono, uuid
  - Type-safe compile-time verified queries
  - Automatic migration support
- **Migration System**: Custom SQL migrations in `/backend/migrations/`

### Serialization & Data Handling
- **Serialization**: Serde (workspace dependency)
- **JSON Processing**: serde_j<PERSON> (workspace dependency)
- **Type Generation**: ts-rs (v9.0) for TypeScript type generation
- **Schema Validation**: Schemars (v0.8) for JSON schema generation

### Date/Time & Identifiers
- **Date/Time**: Chrono (v0.4) with serde support
- **UUID Generation**: uuid (v1.0) with v4 and serde features
- **Timezone Handling**: UTC standardization throughout

### Process Management & System Integration
- **Process Control**: command-group (v5.0) with tokio support
- **Signal Handling**: nix (v0.29) for Unix signal processing
- **System Information**: os_info (v3.12.0) for platform detection
- **Directory Management**: dirs (v5.0) and directories (v6.0.0)

### External Service Integration
- **Git Integration**: git2 (v0.18) for repository operations
- **GitHub API**: octocrab (v0.44) for GitHub operations
- **HTTP Client**: reqwest (v0.11) with JSON support
- **File Operations**: ignore (v0.4) for gitignore-style filtering

### Error Handling & Observability
- **Error Handling**: anyhow (workspace dependency)
- **Logging**: tracing (workspace dependency) with tracing-subscriber
- **Error Tracking**: Sentry (v0.41.0) with comprehensive features
  - anyhow, backtrace, panic, debug-images support
  - sentry-tower for middleware integration
  - sentry-tracing for log correlation

### Asset Management & UI
- **Asset Embedding**: rust-embed (v8.2) for static asset embedding
- **MIME Type Detection**: mime_guess (v2.0) for content type detection
- **File Opening**: open (v5.3.2) for system file opening

### Networking & Communication
- **MCP Protocol**: rmcp (v0.1.5) with server and transport-io features
- **TLS/SSL**: openssl-sys (workspace dependency)
- **URL Encoding**: urlencoding (v2.1.3)
- **Text Processing**: regex (v1.11.1) and strip-ansi-escapes (v0.2.1)

### Development & Build Tools
- **Build Scripts**: build.rs with dotenv (v0.15) support
- **Environment**: dotenv for development configuration
- **Formatting**: rustfmt with custom configuration
- **Linting**: Clippy with uninlined-format-args allowance

## Frontend Technology Stack

### Core Framework & Language
- **Language**: TypeScript (v5.2.2) with strict mode enabled
- **Framework**: React (v18.2.0) with concurrent features
- **Build Tool**: Vite (v5.0.8) for development and production builds
- **Package Manager**: npm with workspace support

### UI Framework & Components
- **Component Library**: shadcn/ui (Radix UI based)
- **Styling**: Tailwind CSS (v3.4.0) with utility-first approach
- **Icons**: Lucide React (v0.303.0) for consistent iconography
- **Animations**: tailwindcss-animate (v1.0.7) for smooth transitions

### Radix UI Components
- **Dropdown Menu**: @radix-ui/react-dropdown-menu (v2.1.15)
- **Labels**: @radix-ui/react-label (v2.1.7)
- **Portals**: @radix-ui/react-portal (v1.1.9)
- **Select**: @radix-ui/react-select (v2.2.5)
- **Separator**: @radix-ui/react-separator (v1.1.7)
- **Slot**: @radix-ui/react-slot (v1.2.3)
- **Tooltip**: @radix-ui/react-tooltip (v1.2.7)

### Drag & Drop
- **DnD Core**: @dnd-kit/core (v6.3.1) for drag and drop functionality
- **DnD Modifiers**: @dnd-kit/modifiers (v9.0.0) for drag constraints

### Content & Typography
- **Markdown**: react-markdown (v10.1.0) for rich text rendering
- **Typography**: @tailwindcss/typography (v0.5.16) for prose styling
- **Utility Classes**: clsx (v2.0.0) and tailwind-merge (v2.2.0)

### Routing & Navigation
- **Router**: react-router-dom (v6.8.1) for client-side routing
- **Navigation**: Programmatic navigation with history API

### Development Tools
- **TypeScript**: Strict configuration with path aliases
- **ESLint**: Code linting with React-specific rules
- **Prettier**: Code formatting with consistent style
- **PostCSS**: CSS processing with autoprefixer

### Error Tracking & Monitoring
- **Sentry**: @sentry/react (v9.34.0) for error tracking
- **Vite Plugin**: @sentry/vite-plugin (v3.5.0) for build-time integration
- **Development**: click-to-react-component (v1.1.2) for debugging

### Style Utilities
- **Variant System**: class-variance-authority (v0.7.0) for component variants
- **CSS Variables**: Custom CSS properties for theming
- **Responsive Design**: Mobile-first responsive utilities

## Development Toolchain

### Build System
- **Root Package Manager**: pnpm (v8+) with workspace support
- **Workspace Configuration**: pnpm-workspace.yaml for monorepo management
- **Build Scripts**: Node.js scripts for build orchestration
- **Concurrency**: concurrently (v8.2.2) for parallel development

### Rust Toolchain
- **Rust Version**: Defined in rust-toolchain.toml
- **Cargo Features**: Optimized for development and production
- **Build Scripts**: Custom cargo.js wrapper for build management
- **Testing**: Cargo test with tempfile (v3.8) for testing

### Database Management
- **Migrations**: SQL migration files with timestamp ordering
- **Schema Management**: SQLX for compile-time schema validation
- **Development Database**: SQLite seed data in dev_assets_seed/
- **Type Safety**: Rust structs automatically mapped to database tables

### Type Sharing System
- **ts-rs**: Automatic TypeScript type generation from Rust structs
- **Shared Types**: Common types in `/shared/types.ts`
- **Build Integration**: Automated type generation in build pipeline
- **Validation**: Compile-time validation of shared types

## NPM Package Distribution

### Package Structure
- **Main Package**: vibe-kanban (v0.0.47)
- **CLI Package**: npx-cli/ for command-line interface
- **Build Process**: build-npm-package.sh for packaging
- **Testing**: test-npm-package.sh for package validation

### Installation & Distribution
- **NPM Registry**: Published to npm as public package
- **Installation**: `npx vibe-kanban` for instant setup
- **Versioning**: Semantic versioning with automated releases
- **Cross-Platform**: Support for macOS, Linux, and Windows

## Configuration Management

### Environment Configuration
- **Development**: .env files for local development
- **Production**: Environment variables for production deployment
- **MCP Configuration**: Centralized MCP server configuration
- **Agent Configuration**: Per-agent configuration management

### Build Configuration
- **Vite Config**: Frontend build configuration
- **Cargo Config**: Backend build configuration
- **TypeScript Config**: Type checking and compilation
- **Tailwind Config**: CSS framework configuration

## Performance Optimizations

### Backend Performance
- **Async Processing**: Tokio-based concurrent execution
- **Database Optimization**: Efficient SQLite queries with proper indexing
- **Memory Management**: Rust's zero-cost abstractions
- **Connection Pooling**: Efficient database connection management

### Frontend Performance
- **Code Splitting**: Lazy loading with React.lazy()
- **Bundle Optimization**: Tree shaking and dead code elimination
- **Asset Optimization**: Image and asset optimization
- **Caching**: Intelligent caching strategies

### Build Performance
- **Incremental Builds**: Cargo's incremental compilation
- **Hot Module Replacement**: Vite's HMR for development
- **Parallel Processing**: Concurrent build steps
- **Caching**: Build artifact caching

## Security Considerations

### Backend Security
- **Memory Safety**: Rust's memory safety guarantees
- **SQL Injection Prevention**: Parameterized queries with SQLX
- **Input Validation**: Comprehensive input sanitization
- **Process Isolation**: Sandboxed execution environments

### Frontend Security
- **XSS Prevention**: Content Security Policy implementation
- **CSRF Protection**: Anti-CSRF token implementation
- **Dependency Scanning**: Regular dependency updates
- **Secure Communication**: HTTPS enforcement

### Runtime Security
- **Sandboxing**: Isolated execution environments
- **Resource Limits**: CPU and memory constraints
- **File System Access**: Controlled file system operations
- **Network Isolation**: Restricted network access

## Monitoring & Observability

### Error Tracking
- **Sentry Integration**: Comprehensive error tracking
- **Performance Monitoring**: Application performance monitoring
- **Custom Metrics**: Business-specific metrics
- **Alert System**: Automated alerting for critical issues

### Logging
- **Structured Logging**: Tracing-based structured logging
- **Log Levels**: Configurable log levels
- **Log Aggregation**: Centralized log collection
- **Search & Analysis**: Log search and analysis capabilities

### Metrics
- **System Metrics**: CPU, memory, disk usage
- **Application Metrics**: Request rates, response times
- **Business Metrics**: Task completion rates, agent utilization
- **Custom Dashboards**: Grafana or similar for visualization

## Development Experience

### Developer Tools
- **IDE Support**: VS Code with Rust and TypeScript extensions
- **Debugging**: Integrated debugging for both Rust and TypeScript
- **Testing**: Automated testing with cargo test and npm test
- **Linting**: Automated code quality checks

### Workflow Integration
- **Git Hooks**: Pre-commit hooks for code quality
- **CI/CD**: GitHub Actions for automated builds
- **Release Process**: Automated release management
- **Documentation**: Comprehensive API documentation

### Hot Reloading
- **Backend**: cargo-watch for Rust hot reloading
- **Frontend**: Vite HMR for React hot reloading
- **Database**: Automatic migration on schema changes
- **Configuration**: Dynamic configuration updates

## Deployment Architecture

### Single Binary Distribution
- **Embedded Frontend**: React app served from Rust binary
- **Embedded Database**: SQLite database included
- **Static Assets**: All assets embedded in binary
- **Cross-Platform**: Single binary for multiple platforms

### Runtime Dependencies
- **Minimal Dependencies**: Self-contained execution
- **System Integration**: Native system integration
- **Configuration**: File-based configuration
- **Upgrades**: Automatic update mechanism

This comprehensive technology stack provides a robust foundation for AI agent orchestration while maintaining excellent developer experience and system reliability. The combination of modern Rust backend capabilities with React frontend flexibility creates an optimal platform for managing multiple AI coding assistants in professional development environments.