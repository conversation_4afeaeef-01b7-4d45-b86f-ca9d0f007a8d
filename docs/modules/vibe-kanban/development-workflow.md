# Vibe Kanban - Development Workflow

## Development Workflow Overview

The **Development Workflow** for Vibe Kanban is designed to support efficient **AI-assisted development** with robust tooling, automated processes, and seamless integration between Rust backend and React frontend components. This workflow enables rapid iteration while maintaining code quality and system reliability.

## Development Environment Setup

### Prerequisites
- **Rust**: Latest stable version via rustup
- **Node.js**: v18+ for frontend development
- **pnpm**: v8+ for package management
- **Git**: Version control system
- **IDE**: VS Code with Rust and TypeScript extensions

### Environment Initialization
```bash
# Clone repository
git clone https://github.com/BloopAI/vibe-kanban.git
cd vibe-kanban

# Install dependencies
pnpm install

# Prepare development database
npm run prepare-db

# Start development server
pnpm run dev
```

### Development Commands
- **File**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/AGENT.md`
- **Key Commands**:
  - `pnpm dev` - Full development environment
  - `pnpm frontend:dev` - Frontend only
  - `pnpm backend:dev` - Backend only
  - `pnpm build` - Production build
  - `pnpm test` - Run tests

## Development Server Architecture

### Concurrent Development
- **Script**: Utilizes `concurrently` for parallel frontend and backend development
- **Port Management**: Automatic port allocation via setup scripts
- **Hot Reloading**: Both frontend (Vite HMR) and backend (cargo-watch) hot reloading
- **Environment Variables**: Dynamic environment configuration

### Development Configuration
```json
{
  "dev": "export FRONTEND_PORT=$(node scripts/setup-dev-environment.js frontend) && export BACKEND_PORT=$(node scripts/setup-dev-environment.js backend) && concurrently \"npm run backend:dev:watch\" \"npm run frontend:dev\"",
  "frontend:dev": "cd frontend && npm run dev -- --port ${FRONTEND_PORT:-3000} --open",
  "backend:dev:watch": "DISABLE_WORKTREE_ORPHAN_CLEANUP=1 npm run cargo -- watch -w backend -x 'run --manifest-path backend/Cargo.toml'"
}
```

### Development Database
- **Seed Database**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/dev_assets_seed/db.sqlite`
- **Configuration**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/dev_assets_seed/config.json`
- **Reset**: Automatically copied on development startup

## Code Organization and Structure

### Monorepo Structure
```
vibe-kanban/
├── backend/           # Rust backend
├── frontend/          # React frontend
├── shared/            # Shared TypeScript types
├── scripts/           # Build and utility scripts
├── npx-cli/          # NPM package structure
└── dev_assets_seed/  # Development seed data
```

### Module Organization
- **Backend Modules**: Organized by domain (models, routes, services, executors)
- **Frontend Components**: Organized by feature (tasks, projects, UI components)
- **Shared Types**: Common TypeScript definitions
- **Utilities**: Shared utility functions and helpers

## Frontend Development Workflow

### React Development Stack
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and builds
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Context API
- **Routing**: React Router for navigation

### Component Development
```typescript
// Component development pattern
interface ComponentProps {
  // Props definition
}

const Component: React.FC<ComponentProps> = ({ ...props }) => {
  // Component logic
  return (
    // JSX structure
  );
};

export default Component;
```

### Frontend Development Commands
```bash
# Start frontend development server
cd frontend
npm run dev

# Build frontend for production
npm run build

# Lint frontend code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

## Backend Development Workflow

### Rust Development Stack
- **Framework**: Axum with Tokio async runtime
- **Database**: SQLite with SQLX for type-safe queries
- **Build Tool**: Cargo with custom scripts
- **Testing**: Rust native testing framework
- **Monitoring**: Tracing for structured logging

### Backend Development Commands
```bash
# Start backend development server
cargo run --manifest-path backend/Cargo.toml

# Run with hot reloading
cargo watch -w backend -x 'run --manifest-path backend/Cargo.toml'

# Run tests
cargo test --manifest-path backend/Cargo.toml

# Check code without building
cargo check --manifest-path backend/Cargo.toml

# Generate documentation
cargo doc --manifest-path backend/Cargo.toml
```

### Database Development
```bash
# Prepare database for development
npm run prepare-db

# Run database migrations
# Automatically handled by SQLX

# Generate TypeScript types
npm run generate-types
```

## Type Safety and Sharing

### Rust to TypeScript Type Generation
- **Tool**: ts-rs for automatic type generation
- **Command**: `npm run generate-types`
- **Output**: `/Users/<USER>/Projects/own/assistant/vibe-kanban/shared/types.ts`
- **Process**: Rust structs → TypeScript interfaces

### Type Generation Example
```rust
// Rust struct with ts-rs annotation
#[derive(Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub struct Task {
    pub id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub status: TaskStatus,
}

// Generated TypeScript interface
export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
}
```

## Testing Strategy

### Frontend Testing
- **Framework**: React Testing Library
- **Tests**: Component unit tests and integration tests
- **Coverage**: Test coverage reporting
- **Mocking**: Mock external dependencies

### Backend Testing
- **Framework**: Rust native testing
- **Tests**: Unit tests and integration tests
- **Database**: Test database isolation
- **Mocking**: Mock external services

### End-to-End Testing
- **Framework**: Playwright for E2E testing
- **Scenarios**: Complete user workflows
- **Automation**: Automated test execution
- **Reporting**: Test result reporting

## Code Quality and Standards

### Rust Code Standards
- **Formatting**: rustfmt for consistent formatting
- **Linting**: Clippy for code quality
- **Documentation**: Comprehensive inline documentation
- **Error Handling**: Proper error handling with anyhow

### TypeScript Code Standards
- **Formatting**: Prettier for consistent formatting
- **Linting**: ESLint for code quality
- **Type Safety**: Strict TypeScript configuration
- **Documentation**: JSDoc for API documentation

### Code Quality Tools
```bash
# Rust formatting
cargo fmt --manifest-path backend/Cargo.toml

# Rust linting
cargo clippy --manifest-path backend/Cargo.toml

# TypeScript linting
cd frontend && npm run lint

# TypeScript formatting
cd frontend && npm run format
```

## Git Workflow

### Branch Strategy
- **Main Branch**: `main` for production-ready code
- **Development Branch**: `dev` for development integration
- **Feature Branches**: `feature/feature-name` for new features
- **Hotfix Branches**: `hotfix/issue-description` for urgent fixes

### Git Hooks
- **Pre-commit**: Code formatting and linting
- **Pre-push**: Run tests before pushing
- **Commit Message**: Conventional commit format
- **Automation**: Automated checks and formatting

### Commit Convention
```bash
# Conventional commit format
<type>(<scope>): <description>

# Examples
feat(tasks): add task filtering functionality
fix(backend): resolve database connection issue
docs(readme): update installation instructions
```

## CI/CD Pipeline

### GitHub Actions Workflow
```yaml
name: CI/CD Pipeline
on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      - name: Run tests
        run: |
          pnpm install
          pnpm test
```

### Deployment Pipeline
1. **Code Push**: Push to repository
2. **Automated Tests**: Run all test suites
3. **Build Process**: Build frontend and backend
4. **Package Creation**: Create NPM package
5. **Deployment**: Deploy to npm registry

## Development Debugging

### Backend Debugging
- **Logging**: Structured logging with tracing
- **Debugger**: Rust debugger integration
- **Profiling**: Performance profiling tools
- **Monitoring**: Real-time monitoring

### Frontend Debugging
- **Browser DevTools**: Chrome/Firefox developer tools
- **React DevTools**: React component debugging
- **Network Inspector**: API request debugging
- **Console Logging**: Structured console output

### Debug Configuration
```rust
// Backend debug configuration
#[cfg(debug_assertions)]
fn setup_debug_logging() {
    tracing_subscriber::fmt()
        .with_env_filter("debug")
        .init();
}
```

## Performance Optimization

### Frontend Performance
- **Bundle Analysis**: Analyze bundle size
- **Code Splitting**: Lazy load components
- **Memoization**: Optimize re-renders
- **Asset Optimization**: Optimize images and assets

### Backend Performance
- **Database Optimization**: Query optimization
- **Caching**: Implement caching strategies
- **Connection Pooling**: Optimize database connections
- **Async Processing**: Maximize async efficiency

### Performance Monitoring
```bash
# Bundle analysis
cd frontend && npm run build -- --analyze

# Backend profiling
cargo build --release --manifest-path backend/Cargo.toml
```

## Security Practices

### Development Security
- **Dependency Scanning**: Regular dependency updates
- **Secret Management**: Secure secret handling
- **Input Validation**: Comprehensive input validation
- **Authentication**: Secure authentication implementation

### Security Tools
- **Dependabot**: Automated dependency updates
- **Security Audits**: Regular security audits
- **Code Analysis**: Static code analysis
- **Penetration Testing**: Regular security testing

## Documentation Workflow

### Code Documentation
- **Rust Documentation**: Comprehensive rustdoc comments
- **TypeScript Documentation**: JSDoc for APIs
- **Component Documentation**: Storybook for components
- **API Documentation**: OpenAPI/Swagger documentation

### Documentation Generation
```bash
# Generate Rust documentation
cargo doc --manifest-path backend/Cargo.toml --open

# Generate TypeScript documentation
cd frontend && npm run docs
```

## Development Tools and IDE Setup

### VS Code Configuration
```json
{
  "recommendations": [
    "rust-lang.rust-analyzer",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json"
  ],
  "settings": {
    "rust-analyzer.cargo.buildScripts.enable": true,
    "editor.formatOnSave": true
  }
}
```

### Development Extensions
- **Rust Analyzer**: Rust language support
- **TypeScript**: TypeScript language support
- **Tailwind CSS**: Tailwind CSS IntelliSense
- **GitLens**: Git integration and history

## Troubleshooting Guide

### Common Issues
1. **Port Conflicts**: Development server port conflicts
2. **Type Generation**: TypeScript type generation failures
3. **Database Issues**: SQLite connection problems
4. **Build Failures**: Compilation errors

### Debug Steps
1. **Check Logs**: Review console and server logs
2. **Clean Build**: Clean and rebuild project
3. **Dependency Issues**: Update dependencies
4. **Environment Reset**: Reset development environment

### Recovery Commands
```bash
# Reset development environment
npm run dev:clear-ports
rm -rf node_modules
pnpm install
npm run prepare-db

# Clean build
cargo clean --manifest-path backend/Cargo.toml
cd frontend && rm -rf dist
pnpm build
```

## Future Workflow Enhancements

### Planned Improvements
- **Docker Development**: Containerized development environment
- **Cloud Development**: Cloud-based development setup
- **Advanced Testing**: Enhanced testing capabilities
- **Performance Monitoring**: Advanced performance monitoring

### Automation Enhancements
- **Automated Refactoring**: AI-assisted code refactoring
- **Smart Testing**: Intelligent test selection
- **Code Generation**: Automated code generation
- **Documentation**: Automated documentation generation

This comprehensive development workflow ensures efficient, high-quality development while maintaining code standards and enabling rapid iteration in the AI-assisted development paradigm.