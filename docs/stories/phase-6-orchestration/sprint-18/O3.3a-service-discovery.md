# Story O3.3a: Service Discovery

## Story Overview

**Epic**: O3 - MCP Integration  
**Story ID**: O3.3a  
**Title**: Dynamic Service Discovery for MCP Server and Tool Management  
**Priority**: High  
**Effort**: 8 story points  
**Sprint**: Sprint 18 (Week 35-36)  
**Phase**: Agent Orchestration  

## Dependencies

### Prerequisites
- ✅ O1.1a: Agent Registry & Discovery (Completed in Sprint 17)
- ✅ O1.2a: Agent Communication Protocol (Completed in Sprint 17)
- ✅ O3.1a: MCP Configuration Management (Completed in Sprint 17)
- ✅ O3.2a: Tool Integration Framework (Completed in Sprint 17)

### Enables
- O3.4a: Performance Optimization
- O4.1a: Learning & Adaptation
- O4.2a: Decision Making Engine
- O4.3a: Conflict Resolution
- Dynamic MCP ecosystem management

### Blocks Until Complete
- Automatic MCP service discovery and registration
- Dynamic tool availability and capability detection
- Service health monitoring and failover

## Sub-Agent Assignments

### Primary Agent: BE (Backend Agent)
**Responsibilities**:
- Implement dynamic MCP service discovery engine
- Design service health monitoring and failover system
- Create service registry and metadata management
- Implement service lifecycle management

**Deliverables**:
- Service discovery engine with automatic registration
- Health monitoring and failover system
- Service registry with metadata management
- Service lifecycle management framework

### Supporting Agent: AI (AI Integration Agent)
**Responsibilities**:
- Design intelligent service matching algorithms
- Implement adaptive service selection strategies
- Create service performance optimization
- Develop service recommendation engine

**Deliverables**:
- AI-powered service matching and selection
- Adaptive service optimization algorithms
- Performance-based service recommendations
- Service usage pattern analysis

### Supporting Agent: FE (Frontend Agent)
**Responsibilities**:
- Create service discovery management interface
- Design service topology visualization
- Implement service monitoring dashboard
- Build service configuration interface

**Deliverables**:
- Service discovery management UI
- Service topology visualization
- Monitoring dashboard interface
- Configuration management interface

## Acceptance Criteria

### Functional Requirements

#### O3.3a.1: Automatic Service Discovery
**GIVEN** a need for dynamic MCP service management
**WHEN** MCP servers and tools are deployed or updated
**THEN** it should:
- ✅ Automatically discover new MCP servers and services
- ✅ Register discovered services with comprehensive metadata
- ✅ Update service capabilities and tool inventories dynamically
- ✅ Handle service versioning and compatibility detection
- ✅ Support multiple discovery protocols and mechanisms

#### O3.3a.2: Service Health Monitoring and Failover
**GIVEN** requirements for reliable service availability
**WHEN** monitoring MCP service health and availability
**THEN** it should:
- ✅ Continuously monitor service health and performance
- ✅ Detect service failures and degradation automatically
- ✅ Implement automatic failover to backup services
- ✅ Provide service recovery and restoration capabilities
- ✅ Maintain service availability metrics and SLA tracking

#### O3.3a.3: Intelligent Service Selection
**GIVEN** need for optimal service utilization
**WHEN** selecting services for agent tool execution
**THEN** it should:
- ✅ Intelligently select optimal services based on capability requirements
- ✅ Consider service performance, availability, and load factors
- ✅ Support service affinity and anti-affinity rules
- ✅ Implement load balancing across equivalent services
- ✅ Provide service recommendation and optimization suggestions

### Technical Requirements

#### Service Discovery Engine
```rust
// Dynamic service discovery and management system
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::{RwLock, Mutex};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServiceDiscoveryEngine {
    pub id: Uuid,
    pub discovery_config: DiscoveryConfiguration,
    pub service_registry: ServiceRegistry,
    pub health_monitor: HealthMonitor,
    pub discovery_protocols: Vec<DiscoveryProtocol>,
    pub service_matcher: ServiceMatcher,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DiscoveryConfiguration {
    pub scan_interval: chrono::Duration,
    pub discovery_timeout: chrono::Duration,
    pub health_check_interval: chrono::Duration,
    pub service_ttl: chrono::Duration,
    pub failover_threshold: u32,
    pub load_balancing_strategy: LoadBalancingStrategy,
    pub discovery_sources: Vec<DiscoverySource>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum DiscoverySource {
    NetworkScan { subnet: String, port_range: (u16, u16) },
    DNSService { domain: String, service_type: String },
    ConsulService { consul_endpoint: String },
    KubernetesService { namespace: String, label_selector: String },
    StaticConfiguration { services: Vec<ServiceEndpoint> },
    EnvironmentVariables { prefix: String },
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServiceRegistry {
    pub services: HashMap<Uuid, DiscoveredService>,
    pub service_groups: HashMap<String, Vec<Uuid>>,
    pub capability_index: HashMap<String, Vec<Uuid>>,
    pub health_status: HashMap<Uuid, ServiceHealthStatus>,
    pub performance_metrics: HashMap<Uuid, ServicePerformanceMetrics>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DiscoveredService {
    pub id: Uuid,
    pub name: String,
    pub service_type: ServiceType,
    pub endpoints: Vec<ServiceEndpoint>,
    pub capabilities: Vec<ServiceCapability>,
    pub metadata: ServiceMetadata,
    pub discovery_source: DiscoverySource,
    pub version: String,
    pub status: ServiceStatus,
    pub discovered_at: chrono::DateTime<chrono::Utc>,
    pub last_seen: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ServiceType {
    MCPServer,
    ToolProvider,
    ResourceProvider,
    AuthenticationService,
    StorageService,
    ComputeService,
    MonitoringService,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServiceEndpoint {
    pub protocol: ServiceProtocol,
    pub host: String,
    pub port: u16,
    pub path: Option<String>,
    pub authentication: Option<AuthenticationMethod>,
    pub tls_config: Option<TlsConfiguration>,
    pub connection_pool_size: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ServiceProtocol {
    HTTP,
    HTTPS,
    WebSocket,
    WebSocketSecure,
    gRPC,
    TCP,
    UDP,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServiceCapability {
    pub name: String,
    pub version: String,
    pub description: String,
    pub input_schema: Option<serde_json::Value>,
    pub output_schema: Option<serde_json::Value>,
    pub requirements: Vec<String>,
    pub performance_characteristics: PerformanceCharacteristics,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServiceMetadata {
    pub vendor: String,
    pub description: String,
    pub documentation_url: Option<String>,
    pub support_contact: Option<String>,
    pub license: Option<String>,
    pub tags: Vec<String>,
    pub priority: ServicePriority,
    pub cost_model: Option<CostModel>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ServicePriority {
    Critical,
    High,
    Normal,
    Low,
    BestEffort,
}

pub struct ServiceDiscoveryManager {
    discovery_engine: Arc<RwLock<ServiceDiscoveryEngine>>,
    health_monitor: Arc<HealthMonitor>,
    service_matcher: Arc<ServiceMatcher>,
    discovery_protocols: Vec<Arc<dyn DiscoveryProtocol>>,
    event_publisher: Arc<EventPublisher>,
}

impl ServiceDiscoveryManager {
    pub fn new(config: DiscoveryConfiguration) -> Self {
        let discovery_engine = Arc::new(RwLock::new(ServiceDiscoveryEngine {
            id: Uuid::new_v4(),
            discovery_config: config.clone(),
            service_registry: ServiceRegistry::new(),
            health_monitor: HealthMonitor::new(config.health_check_interval),
            discovery_protocols: vec![],
            service_matcher: ServiceMatcher::new(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        }));
        
        let health_monitor = Arc::new(HealthMonitor::new(config.health_check_interval));
        let service_matcher = Arc::new(ServiceMatcher::new());
        let event_publisher = Arc::new(EventPublisher::new());
        
        // Initialize discovery protocols
        let mut discovery_protocols: Vec<Arc<dyn DiscoveryProtocol>> = vec![];
        for source in &config.discovery_sources {
            match source {
                DiscoverySource::NetworkScan { subnet, port_range } => {
                    discovery_protocols.push(Arc::new(NetworkScanProtocol::new(
                        subnet.clone(),
                        *port_range,
                    )));
                }
                DiscoverySource::DNSService { domain, service_type } => {
                    discovery_protocols.push(Arc::new(DNSServiceProtocol::new(
                        domain.clone(),
                        service_type.clone(),
                    )));
                }
                DiscoverySource::ConsulService { consul_endpoint } => {
                    discovery_protocols.push(Arc::new(ConsulServiceProtocol::new(
                        consul_endpoint.clone(),
                    )));
                }
                DiscoverySource::KubernetesService { namespace, label_selector } => {
                    discovery_protocols.push(Arc::new(KubernetesServiceProtocol::new(
                        namespace.clone(),
                        label_selector.clone(),
                    )));
                }
                DiscoverySource::StaticConfiguration { services } => {
                    discovery_protocols.push(Arc::new(StaticConfigurationProtocol::new(
                        services.clone(),
                    )));
                }
                DiscoverySource::EnvironmentVariables { prefix } => {
                    discovery_protocols.push(Arc::new(EnvironmentVariablesProtocol::new(
                        prefix.clone(),
                    )));
                }
            }
        }
        
        Self {
            discovery_engine,
            health_monitor,
            service_matcher,
            discovery_protocols,
            event_publisher,
        }
    }
    
    pub async fn start_discovery(&self) -> Result<(), DiscoveryError> {
        // Start continuous discovery process
        let discovery_engine = self.discovery_engine.clone();
        let protocols = self.discovery_protocols.clone();
        let event_publisher = self.event_publisher.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(30));
            
            loop {
                interval.tick().await;
                
                // Run discovery across all protocols
                let mut discovered_services = Vec::new();
                
                for protocol in &protocols {
                    match protocol.discover_services().await {
                        Ok(services) => {
                            discovered_services.extend(services);
                        }
                        Err(e) => {
                            eprintln!("Discovery protocol error: {:?}", e);
                        }
                    }
                }
                
                // Update service registry
                {
                    let mut engine = discovery_engine.write().await;
                    for service in discovered_services {
                        engine.service_registry.register_service(service.clone()).await?;
                        
                        // Publish service discovery event
                        event_publisher.publish(ServiceDiscoveryEvent::ServiceDiscovered {
                            service_id: service.id,
                            service_name: service.name.clone(),
                            capabilities: service.capabilities.clone(),
                        }).await?;
                    }
                }
            }
        });
        
        // Start health monitoring
        self.start_health_monitoring().await?;
        
        Ok(())
    }
    
    pub async fn discover_services(&self) -> Result<Vec<DiscoveredService>, DiscoveryError> {
        let mut all_services = Vec::new();
        
        // Run discovery across all protocols
        for protocol in &self.discovery_protocols {
            let services = protocol.discover_services().await?;
            all_services.extend(services);
        }
        
        // Update registry with discovered services
        {
            let mut engine = self.discovery_engine.write().await;
            for service in &all_services {
                engine.service_registry.register_service(service.clone()).await?;
            }
        }
        
        Ok(all_services)
    }
    
    pub async fn find_services_by_capability(&self, capability: &str) -> Result<Vec<DiscoveredService>, DiscoveryError> {
        let engine = self.discovery_engine.read().await;
        
        let service_ids = engine.service_registry.capability_index
            .get(capability)
            .unwrap_or(&vec![])
            .clone();
        
        let mut matching_services = Vec::new();
        for service_id in service_ids {
            if let Some(service) = engine.service_registry.services.get(&service_id) {
                // Check if service is healthy
                if let Some(health_status) = engine.service_registry.health_status.get(&service_id) {
                    if health_status.status == HealthStatus::Healthy {
                        matching_services.push(service.clone());
                    }
                }
            }
        }
        
        // Sort by performance and availability
        matching_services.sort_by(|a, b| {
            let a_perf = engine.service_registry.performance_metrics.get(&a.id);
            let b_perf = engine.service_registry.performance_metrics.get(&b.id);
            
            match (a_perf, b_perf) {
                (Some(a_metrics), Some(b_metrics)) => {
                    // Sort by response time (lower is better)
                    a_metrics.average_response_time.partial_cmp(&b_metrics.average_response_time)
                        .unwrap_or(std::cmp::Ordering::Equal)
                }
                (Some(_), None) => std::cmp::Ordering::Less,
                (None, Some(_)) => std::cmp::Ordering::Greater,
                (None, None) => std::cmp::Ordering::Equal,
            }
        });
        
        Ok(matching_services)
    }
    
    pub async fn select_optimal_service(
        &self,
        capability: &str,
        requirements: &ServiceRequirements,
    ) -> Result<Option<DiscoveredService>, DiscoveryError> {
        let matching_services = self.find_services_by_capability(capability).await?;
        
        if matching_services.is_empty() {
            return Ok(None);
        }
        
        // Use service matcher to find best match
        let optimal_service = self.service_matcher.select_optimal_service(
            &matching_services,
            requirements,
        ).await?;
        
        Ok(optimal_service)
    }
    
    pub async fn get_service_topology(&self) -> Result<ServiceTopology, DiscoveryError> {
        let engine = self.discovery_engine.read().await;
        
        let mut topology = ServiceTopology {
            services: HashMap::new(),
            connections: Vec::new(),
            service_groups: HashMap::new(),
            health_overview: ServiceHealthOverview::default(),
        };
        
        // Build service nodes
        for (id, service) in &engine.service_registry.services {
            let health_status = engine.service_registry.health_status.get(id);
            let performance_metrics = engine.service_registry.performance_metrics.get(id);
            
            topology.services.insert(*id, ServiceNode {
                id: *id,
                name: service.name.clone(),
                service_type: service.service_type.clone(),
                status: service.status.clone(),
                health_status: health_status.cloned(),
                performance_metrics: performance_metrics.cloned(),
                capabilities: service.capabilities.clone(),
                endpoints: service.endpoints.clone(),
            });
        }
        
        // Build service connections (dependencies)
        for (id, service) in &engine.service_registry.services {
            let connections = self.discover_service_dependencies(service).await?;
            topology.connections.extend(connections);
        }
        
        // Build service groups
        topology.service_groups = engine.service_registry.service_groups.clone();
        
        // Calculate health overview
        topology.health_overview = self.calculate_health_overview(&engine.service_registry).await?;
        
        Ok(topology)
    }
    
    async fn start_health_monitoring(&self) -> Result<(), DiscoveryError> {
        let discovery_engine = self.discovery_engine.clone();
        let health_monitor = self.health_monitor.clone();
        let event_publisher = self.event_publisher.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(10));
            
            loop {
                interval.tick().await;
                
                // Get all registered services
                let services = {
                    let engine = discovery_engine.read().await;
                    engine.service_registry.services.values().cloned().collect::<Vec<_>>()
                };
                
                // Monitor health for each service
                for service in services {
                    let health_result = health_monitor.check_service_health(&service).await;
                    
                    // Update health status
                    {
                        let mut engine = discovery_engine.write().await;
                        engine.service_registry.health_status.insert(service.id, health_result.clone());
                    }
                    
                    // Handle health changes
                    if health_result.status != HealthStatus::Healthy {
                        // Publish health event
                        event_publisher.publish(ServiceDiscoveryEvent::ServiceUnhealthy {
                            service_id: service.id,
                            service_name: service.name.clone(),
                            health_status: health_result.clone(),
                        }).await?;
                        
                        // Trigger failover if needed
                        if health_result.status == HealthStatus::Failed {
                            self.handle_service_failover(&service).await?;
                        }
                    }
                }
            }
        });
        
        Ok(())
    }
    
    async fn handle_service_failover(&self, failed_service: &DiscoveredService) -> Result<(), DiscoveryError> {
        // Find backup services with same capabilities
        let mut backup_services = Vec::new();
        
        for capability in &failed_service.capabilities {
            let matching_services = self.find_services_by_capability(&capability.name).await?;
            
            for service in matching_services {
                if service.id != failed_service.id {
                    backup_services.push(service);
                }
            }
        }
        
        if !backup_services.is_empty() {
            // Select best backup service
            let requirements = ServiceRequirements {
                capabilities: failed_service.capabilities.iter().map(|c| c.name.clone()).collect(),
                performance_requirements: PerformanceRequirements::default(),
                availability_requirements: AvailabilityRequirements::default(),
            };
            
            if let Some(backup_service) = self.service_matcher.select_optimal_service(
                &backup_services,
                &requirements,
            ).await? {
                // Publish failover event
                self.event_publisher.publish(ServiceDiscoveryEvent::ServiceFailover {
                    failed_service_id: failed_service.id,
                    backup_service_id: backup_service.id,
                    capabilities: failed_service.capabilities.clone(),
                }).await?;
            }
        }
        
        Ok(())
    }
}

// Service health monitoring
#[derive(Debug, Clone)]
pub struct HealthMonitor {
    check_interval: chrono::Duration,
    timeout: chrono::Duration,
    retry_count: u32,
}

impl HealthMonitor {
    pub fn new(check_interval: chrono::Duration) -> Self {
        Self {
            check_interval,
            timeout: chrono::Duration::seconds(10),
            retry_count: 3,
        }
    }
    
    pub async fn check_service_health(&self, service: &DiscoveredService) -> ServiceHealthStatus {
        let mut health_checks = Vec::new();
        
        // Check each endpoint
        for endpoint in &service.endpoints {
            let check_result = self.check_endpoint_health(endpoint).await;
            health_checks.push(check_result);
        }
        
        // Aggregate health status
        let overall_status = if health_checks.iter().all(|check| check.status == HealthStatus::Healthy) {
            HealthStatus::Healthy
        } else if health_checks.iter().any(|check| check.status == HealthStatus::Healthy) {
            HealthStatus::Degraded
        } else {
            HealthStatus::Failed
        };
        
        ServiceHealthStatus {
            service_id: service.id,
            status: overall_status,
            last_check: chrono::Utc::now(),
            endpoint_checks: health_checks,
            response_time: self.calculate_average_response_time(&health_checks),
            error_rate: self.calculate_error_rate(&health_checks),
        }
    }
    
    async fn check_endpoint_health(&self, endpoint: &ServiceEndpoint) -> EndpointHealthCheck {
        let start_time = std::time::Instant::now();
        
        let result = match endpoint.protocol {
            ServiceProtocol::HTTP | ServiceProtocol::HTTPS => {
                self.check_http_endpoint(endpoint).await
            }
            ServiceProtocol::WebSocket | ServiceProtocol::WebSocketSecure => {
                self.check_websocket_endpoint(endpoint).await
            }
            ServiceProtocol::gRPC => {
                self.check_grpc_endpoint(endpoint).await
            }
            ServiceProtocol::TCP => {
                self.check_tcp_endpoint(endpoint).await
            }
            ServiceProtocol::UDP => {
                self.check_udp_endpoint(endpoint).await
            }
        };
        
        let response_time = start_time.elapsed();
        
        EndpointHealthCheck {
            endpoint: endpoint.clone(),
            status: match result {
                Ok(_) => HealthStatus::Healthy,
                Err(_) => HealthStatus::Failed,
            },
            response_time,
            error_message: result.err().map(|e| e.to_string()),
            checked_at: chrono::Utc::now(),
        }
    }
    
    async fn check_http_endpoint(&self, endpoint: &ServiceEndpoint) -> Result<(), HealthCheckError> {
        let url = format!("{}://{}:{}{}", 
            if endpoint.protocol == ServiceProtocol::HTTPS { "https" } else { "http" },
            endpoint.host,
            endpoint.port,
            endpoint.path.as_deref().unwrap_or("/")
        );
        
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(self.timeout.num_seconds() as u64))
            .build()?;
        
        let response = client.get(&url).send().await?;
        
        if response.status().is_success() {
            Ok(())
        } else {
            Err(HealthCheckError::UnhealthyResponse(response.status().as_u16()))
        }
    }
    
    async fn check_websocket_endpoint(&self, endpoint: &ServiceEndpoint) -> Result<(), HealthCheckError> {
        let url = format!("{}://{}:{}{}", 
            if endpoint.protocol == ServiceProtocol::WebSocketSecure { "wss" } else { "ws" },
            endpoint.host,
            endpoint.port,
            endpoint.path.as_deref().unwrap_or("/")
        );
        
        // Simple WebSocket connection test
        match tokio_tungstenite::connect_async(&url).await {
            Ok(_) => Ok(()),
            Err(e) => Err(HealthCheckError::ConnectionFailed(e.to_string())),
        }
    }
    
    async fn check_tcp_endpoint(&self, endpoint: &ServiceEndpoint) -> Result<(), HealthCheckError> {
        let address = format!("{}:{}", endpoint.host, endpoint.port);
        
        match tokio::time::timeout(
            std::time::Duration::from_secs(self.timeout.num_seconds() as u64),
            tokio::net::TcpStream::connect(&address)
        ).await {
            Ok(Ok(_)) => Ok(()),
            Ok(Err(e)) => Err(HealthCheckError::ConnectionFailed(e.to_string())),
            Err(_) => Err(HealthCheckError::Timeout),
        }
    }
}

// Service matching and selection
pub struct ServiceMatcher {
    matching_algorithms: Vec<Box<dyn MatchingAlgorithm>>,
}

impl ServiceMatcher {
    pub fn new() -> Self {
        Self {
            matching_algorithms: vec![
                Box::new(CapabilityMatcher::new()),
                Box::new(PerformanceMatcher::new()),
                Box::new(AvailabilityMatcher::new()),
                Box::new(CostMatcher::new()),
            ],
        }
    }
    
    pub async fn select_optimal_service(
        &self,
        candidate_services: &[DiscoveredService],
        requirements: &ServiceRequirements,
    ) -> Result<Option<DiscoveredService>, DiscoveryError> {
        if candidate_services.is_empty() {
            return Ok(None);
        }
        
        let mut scored_services = Vec::new();
        
        // Score each service against requirements
        for service in candidate_services {
            let mut total_score = 0.0;
            let mut weight_sum = 0.0;
            
            // Apply each matching algorithm
            for algorithm in &self.matching_algorithms {
                let score = algorithm.score_service(service, requirements).await?;
                let weight = algorithm.get_weight();
                
                total_score += score * weight;
                weight_sum += weight;
            }
            
            let final_score = if weight_sum > 0.0 {
                total_score / weight_sum
            } else {
                0.0
            };
            
            scored_services.push((service.clone(), final_score));
        }
        
        // Sort by score (highest first)
        scored_services.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        
        // Return highest scoring service
        Ok(scored_services.into_iter().next().map(|(service, _)| service))
    }
}

// Discovery protocols
#[async_trait::async_trait]
pub trait DiscoveryProtocol: Send + Sync {
    async fn discover_services(&self) -> Result<Vec<DiscoveredService>, DiscoveryError>;
    fn get_protocol_name(&self) -> &str;
}

pub struct NetworkScanProtocol {
    subnet: String,
    port_range: (u16, u16),
}

#[async_trait::async_trait]
impl DiscoveryProtocol for NetworkScanProtocol {
    async fn discover_services(&self) -> Result<Vec<DiscoveredService>, DiscoveryError> {
        let mut discovered_services = Vec::new();
        
        // Parse subnet and scan IP range
        let base_ip = self.parse_subnet(&self.subnet)?;
        let ip_range = self.generate_ip_range(&base_ip)?;
        
        // Scan each IP and port combination
        for ip in ip_range {
            for port in self.port_range.0..=self.port_range.1 {
                if let Ok(service) = self.probe_service(&ip, port).await {
                    discovered_services.push(service);
                }
            }
        }
        
        Ok(discovered_services)
    }
    
    fn get_protocol_name(&self) -> &str {
        "NetworkScan"
    }
}

pub struct DNSServiceProtocol {
    domain: String,
    service_type: String,
}

#[async_trait::async_trait]
impl DiscoveryProtocol for DNSServiceProtocol {
    async fn discover_services(&self) -> Result<Vec<DiscoveredService>, DiscoveryError> {
        let mut discovered_services = Vec::new();
        
        // Perform DNS service discovery (DNS-SD)
        let service_records = self.query_dns_services().await?;
        
        for record in service_records {
            let service = self.create_service_from_dns_record(record).await?;
            discovered_services.push(service);
        }
        
        Ok(discovered_services)
    }
    
    fn get_protocol_name(&self) -> &str {
        "DNS-SD"
    }
}
```

#### Frontend Service Discovery Interface
```typescript
// Service discovery management interface
export interface ServiceDiscoveryManager {
  // Service discovery operations
  discoverServices(): Promise<DiscoveredService[]>;
  getServiceTopology(): Promise<ServiceTopology>;
  findServicesByCapability(capability: string): Promise<DiscoveredService[]>;
  selectOptimalService(capability: string, requirements: ServiceRequirements): Promise<DiscoveredService | null>;
  
  // Service management
  registerService(service: ServiceConfiguration): Promise<void>;
  updateService(serviceId: string, updates: Partial<ServiceConfiguration>): Promise<void>;
  removeService(serviceId: string): Promise<void>;
  
  // Health monitoring
  getServiceHealth(serviceId: string): Promise<ServiceHealthStatus>;
  subscribeToHealthUpdates(callback: (update: ServiceHealthUpdate) => void): () => void;
  
  // Configuration management
  getDiscoveryConfiguration(): Promise<DiscoveryConfiguration>;
  updateDiscoveryConfiguration(config: DiscoveryConfiguration): Promise<void>;
}

// React components for service discovery
export const ServiceDiscoveryDashboard: React.FC = () => {
  const [services, setServices] = useState<DiscoveredService[]>([]);
  const [topology, setTopology] = useState<ServiceTopology | null>(null);
  const [healthStatus, setHealthStatus] = useState<Map<string, ServiceHealthStatus>>(new Map());
  const [discoveryConfig, setDiscoveryConfig] = useState<DiscoveryConfiguration | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'topology' | 'health'>('grid');

  useEffect(() => {
    // Load initial data
    loadServices();
    loadTopology();
    loadDiscoveryConfiguration();
    
    // Subscribe to real-time updates
    const unsubscribe = serviceDiscovery.subscribeToHealthUpdates((update) => {
      handleHealthUpdate(update);
    });
    
    return unsubscribe;
  }, []);

  const loadServices = async () => {
    try {
      const discoveredServices = await serviceDiscovery.discoverServices();
      setServices(discoveredServices);
    } catch (error) {
      console.error('Failed to load services:', error);
    }
  };

  const loadTopology = async () => {
    try {
      const serviceTopology = await serviceDiscovery.getServiceTopology();
      setTopology(serviceTopology);
    } catch (error) {
      console.error('Failed to load topology:', error);
    }
  };

  const handleHealthUpdate = (update: ServiceHealthUpdate) => {
    setHealthStatus(prev => new Map(prev.set(update.serviceId, update.healthStatus)));
    
    // Update service list with new health status
    setServices(prev => prev.map(service => 
      service.id === update.serviceId 
        ? { ...service, healthStatus: update.healthStatus }
        : service
    ));
  };

  const renderDashboardContent = () => {
    switch (viewMode) {
      case 'topology':
        return (
          <ServiceTopologyView
            topology={topology}
            healthStatus={healthStatus}
            onServiceSelect={handleServiceSelect}
          />
        );
      case 'health':
        return (
          <ServiceHealthView
            services={services}
            healthStatus={healthStatus}
            onHealthAction={handleHealthAction}
          />
        );
      default:
        return (
          <ServiceGridView
            services={services}
            healthStatus={healthStatus}
            onServiceAction={handleServiceAction}
          />
        );
    }
  };

  return (
    <div className="service-discovery-dashboard">
      <div className="dashboard-header">
        <div className="header-controls">
          <h1>Service Discovery</h1>
          <div className="view-controls">
            <ViewModeSelector
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
            <button onClick={loadServices} className="refresh-button">
              Refresh Services
            </button>
          </div>
        </div>
        
        <div className="discovery-summary">
          <ServiceSummaryCards
            totalServices={services.length}
            healthyServices={services.filter(s => s.healthStatus?.status === 'healthy').length}
            capabilities={getUniqueCapabilities(services)}
          />
        </div>
      </div>
      
      <div className="dashboard-content">
        {renderDashboardContent()}
      </div>
      
      <div className="dashboard-sidebar">
        <DiscoveryConfiguration
          config={discoveryConfig}
          onConfigUpdate={handleConfigUpdate}
        />
      </div>
    </div>
  );
};

export const ServiceTopologyView: React.FC<{
  topology: ServiceTopology | null;
  healthStatus: Map<string, ServiceHealthStatus>;
  onServiceSelect: (serviceId: string) => void;
}> = ({ topology, healthStatus, onServiceSelect }) => {
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [layoutMode, setLayoutMode] = useState<'force' | 'hierarchical' | 'circular'>('force');

  if (!topology) {
    return <div className="loading">Loading topology...</div>;
  }

  return (
    <div className="service-topology-view">
      <div className="topology-controls">
        <LayoutModeSelector
          layoutMode={layoutMode}
          onLayoutModeChange={setLayoutMode}
        />
        
        <TopologyFilters
          topology={topology}
          onFilterChange={handleFilterChange}
        />
      </div>
      
      <div className="topology-canvas">
        <ServiceTopologyRenderer
          topology={topology}
          healthStatus={healthStatus}
          layout={layoutMode}
          selectedService={selectedService}
          onServiceSelect={(serviceId) => {
            setSelectedService(serviceId);
            onServiceSelect(serviceId);
          }}
        />
      </div>
      
      {selectedService && (
        <ServiceDetailsPanel
          serviceId={selectedService}
          service={topology.services.get(selectedService)}
          healthStatus={healthStatus.get(selectedService)}
          onClose={() => setSelectedService(null)}
        />
      )}
    </div>
  );
};

export const ServiceGridView: React.FC<{
  services: DiscoveredService[];
  healthStatus: Map<string, ServiceHealthStatus>;
  onServiceAction: (serviceId: string, action: ServiceAction) => void;
}> = ({ services, healthStatus, onServiceAction }) => {
  const [sortBy, setSortBy] = useState<'name' | 'type' | 'health' | 'capabilities'>('name');
  const [filterBy, setFilterBy] = useState<ServiceFilter>({});
  const [searchTerm, setSearchTerm] = useState('');

  const filteredServices = useMemo(() => {
    return services.filter(service => {
      // Search filter
      if (searchTerm && !service.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }
      
      // Type filter
      if (filterBy.serviceType && service.serviceType !== filterBy.serviceType) {
        return false;
      }
      
      // Health filter
      if (filterBy.healthStatus) {
        const health = healthStatus.get(service.id);
        if (!health || health.status !== filterBy.healthStatus) {
          return false;
        }
      }
      
      // Capability filter
      if (filterBy.capabilities && filterBy.capabilities.length > 0) {
        const hasCapability = filterBy.capabilities.some(cap =>
          service.capabilities.some(serviceCap => serviceCap.name === cap)
        );
        if (!hasCapability) {
          return false;
        }
      }
      
      return true;
    });
  }, [services, searchTerm, filterBy, healthStatus]);

  const sortedServices = useMemo(() => {
    return [...filteredServices].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'type':
          return a.serviceType.localeCompare(b.serviceType);
        case 'health':
          const healthA = healthStatus.get(a.id);
          const healthB = healthStatus.get(b.id);
          if (!healthA && !healthB) return 0;
          if (!healthA) return 1;
          if (!healthB) return -1;
          return healthA.status.localeCompare(healthB.status);
        case 'capabilities':
          return b.capabilities.length - a.capabilities.length;
        default:
          return 0;
      }
    });
  }, [filteredServices, sortBy, healthStatus]);

  return (
    <div className="service-grid-view">
      <div className="grid-controls">
        <div className="search-controls">
          <input
            type="text"
            placeholder="Search services..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-controls">
          <ServiceFilterPanel
            filter={filterBy}
            onFilterChange={setFilterBy}
            availableTypes={getUniqueServiceTypes(services)}
            availableCapabilities={getUniqueCapabilities(services)}
          />
        </div>
        
        <div className="sort-controls">
          <SortSelector
            sortBy={sortBy}
            onSortChange={setSortBy}
          />
        </div>
      </div>
      
      <div className="services-grid">
        {sortedServices.map(service => (
          <ServiceCard
            key={service.id}
            service={service}
            healthStatus={healthStatus.get(service.id)}
            onAction={(action) => onServiceAction(service.id, action)}
          />
        ))}
      </div>
      
      {sortedServices.length === 0 && (
        <div className="no-services">
          <p>No services found matching your criteria.</p>
          <button onClick={() => { setSearchTerm(''); setFilterBy({}); }}>
            Clear filters
          </button>
        </div>
      )}
    </div>
  );
};

export const ServiceCard: React.FC<{
  service: DiscoveredService;
  healthStatus?: ServiceHealthStatus;
  onAction: (action: ServiceAction) => void;
}> = ({ service, healthStatus, onAction }) => {
  const getHealthColor = (status?: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'MCPServer': return '🔌';
      case 'ToolProvider': return '🔧';
      case 'ResourceProvider': return '📦';
      case 'AuthenticationService': return '🔐';
      case 'StorageService': return '💾';
      case 'ComputeService': return '⚡';
      case 'MonitoringService': return '📊';
      default: return '⚙️';
    }
  };

  return (
    <div className="service-card p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
      <div className="card-header flex items-center justify-between">
        <div className="service-info">
          <div className="service-name flex items-center space-x-2">
            <span className="service-icon text-lg">
              {getServiceTypeIcon(service.serviceType)}
            </span>
            <h3 className="font-medium text-gray-900">{service.name}</h3>
          </div>
          <div className="service-type text-sm text-gray-500">
            {service.serviceType}
          </div>
        </div>
        
        <div className="health-indicator">
          {healthStatus && (
            <span className={`px-2 py-1 rounded-full text-xs ${getHealthColor(healthStatus.status)}`}>
              {healthStatus.status}
            </span>
          )}
        </div>
      </div>
      
      <div className="card-content mt-3">
        <div className="service-description text-sm text-gray-600">
          {service.metadata.description}
        </div>
        
        <div className="service-capabilities mt-2">
          <div className="capabilities-label text-xs text-gray-500 mb-1">
            Capabilities ({service.capabilities.length})
          </div>
          <div className="capabilities-list flex flex-wrap gap-1">
            {service.capabilities.slice(0, 3).map(capability => (
              <span
                key={capability.name}
                className="capability-tag text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded"
              >
                {capability.name}
              </span>
            ))}
            {service.capabilities.length > 3 && (
              <span className="text-xs text-gray-500">
                +{service.capabilities.length - 3} more
              </span>
            )}
          </div>
        </div>
        
        <div className="service-endpoints mt-2">
          <div className="endpoints-label text-xs text-gray-500 mb-1">
            Endpoints ({service.endpoints.length})
          </div>
          <div className="endpoints-list">
            {service.endpoints.slice(0, 2).map((endpoint, index) => (
              <div key={index} className="endpoint text-xs text-gray-600">
                {endpoint.protocol}://{endpoint.host}:{endpoint.port}
              </div>
            ))}
            {service.endpoints.length > 2 && (
              <div className="text-xs text-gray-500">
                +{service.endpoints.length - 2} more
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="card-actions mt-3 flex space-x-2">
        <button
          onClick={() => onAction('view_details')}
          className="action-button text-sm px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Details
        </button>
        
        <button
          onClick={() => onAction('test_connection')}
          className="action-button text-sm px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Test
        </button>
        
        <button
          onClick={() => onAction('configure')}
          className="action-button text-sm px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Configure
        </button>
      </div>
    </div>
  );
};
```

### Performance Requirements

#### Discovery Performance
- **Service Discovery**: <3 seconds for network-wide service scan
- **Health Monitoring**: <1 second for individual service health check
- **Service Selection**: <500ms for optimal service matching
- **Topology Generation**: <2 seconds for complete service topology

#### Scalability Requirements
- **Service Volume**: Support discovery of 10,000+ services
- **Concurrent Monitoring**: Monitor 5,000+ services simultaneously
- **Discovery Protocols**: Support 20+ different discovery mechanisms
- **Real-time Updates**: Handle 1,000+ service updates per second

### Security Requirements

#### Discovery Security
- ✅ Secure service discovery with encrypted communication
- ✅ Authentication and authorization for service access
- ✅ Protected service metadata and capability information
- ✅ Secure service health monitoring and reporting

#### Service Security
- ✅ Validated service identity and authenticity
- ✅ Encrypted service-to-service communication
- ✅ Protected service configuration and credentials
- ✅ Audit trail for all discovery and selection activities

## Quality Gates

### Definition of Done

#### Discovery Functionality
- ✅ Automatic service discovery works across all configured protocols
- ✅ Service health monitoring provides accurate status information
- ✅ Service selection chooses optimal services based on requirements
- ✅ Service topology visualization shows complete service ecosystem

#### Performance Validation
- ✅ Service discovery completes within performance targets
- ✅ Health monitoring maintains real-time status accuracy
- ✅ Service selection algorithms perform efficiently at scale
- ✅ Topology generation handles large service ecosystems

#### Reliability Validation
- ✅ Service failover mechanisms work automatically
- ✅ Health monitoring detects service issues promptly
- ✅ Service registry maintains consistency across updates
- ✅ Discovery protocols handle network partitions gracefully

### Testing Requirements

#### Unit Tests
- Service discovery algorithm correctness
- Health monitoring accuracy and reliability
- Service matching and selection logic
- Failover and recovery mechanisms

#### Integration Tests
- End-to-end service discovery workflows
- Cross-protocol service discovery scenarios
- Service health monitoring and alerting
- Service topology generation and visualization

#### Performance Tests
- Large-scale service discovery performance
- Concurrent health monitoring scalability
- Service selection algorithm efficiency
- Real-time update propagation speed

## Implementation Timeline

### Week 1: Core Discovery Infrastructure
- **Days 1-2**: Service discovery engine and protocol framework
- **Days 3-4**: Health monitoring system and service registry
- **Day 5**: Basic service selection and matching algorithms

### Week 2: Advanced Features and Interface
- **Days 1-2**: Service topology generation and failover mechanisms
- **Days 3-4**: Frontend dashboard and visualization components
- **Day 5**: Testing, optimization, and documentation

## Follow-up Stories

### Immediate Next Stories
- **O3.4a**: Performance Optimization (uses service discovery for system optimization)
- **O4.1a**: Learning & Adaptation (leverages service patterns for AI improvement)
- **O4.2a**: Decision Making Engine (uses service selection for intelligent decisions)

### Future Enhancements
- **Service Mesh Integration**: Integration with service mesh platforms
- **Auto-scaling Services**: Automatic service scaling based on demand
- **Service Contracts**: Formal service interface contracts and validation
- **Global Service Registry**: Multi-region service discovery and federation

This comprehensive service discovery system enables dynamic, intelligent management of the MCP ecosystem, providing automatic service registration, health monitoring, and optimal service selection for agent tool execution.

## ✅ Implementation Status: COMPLETED

**Sub-Agent E (Service Discovery Focus)** has successfully implemented all O3.3a Service Discovery requirements:

### ✅ Completed Features:
- **Dynamic Service Discovery**: Automatic MCP service registration and discovery with real-time updates
- **Health Monitoring**: Comprehensive service health checking with automated failover
- **Load Balancing**: Intelligent service selection with performance-based routing
- **Service Registry**: Centralized registry with distributed caching and synchronization
- **Tool Capability Detection**: Dynamic tool availability and capability discovery
- **Performance Optimization**: Service performance monitoring and optimization recommendations

### ✅ Implementation Summary:
- **Backend Services**: Complete service discovery engine with MCP integration
- **Service Registry**: Distributed service registry with health monitoring
- **API Endpoints**: Full REST API supporting all service discovery operations
- **Frontend Components**: Service discovery dashboard with real-time monitoring
- **Integration Points**: Seamless integration with agent orchestration and task distribution

**Total Story Points**: 8 (High Priority) - **Status**: ✅ **COMPLETED**