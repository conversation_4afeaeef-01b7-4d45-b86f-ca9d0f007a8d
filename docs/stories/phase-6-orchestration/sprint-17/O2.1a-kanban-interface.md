# Story O2.1a: Kanban Interface

## Story Overview

**Epic**: O2 - Task Management Platform  
**Story ID**: O2.1a  
**Title**: Agent-Aware Kanban Interface for Multi-Agent Task Management  
**Priority**: Critical  
**Effort**: 8 story points  
**Sprint**: Sprint 17 (Week 33-34)  
**Phase**: Agent Orchestration  

## Dependencies

### Prerequisites
- ✅ F1.1a: Monorepo Architecture (Completed in Phase 1)
- ✅ F2.1a: Multi-Provider AI System (Completed in Phase 1)
- ✅ O1.1a: Agent Registry & Discovery
- ✅ O1.2a: Agent Communication Protocol
- ✅ O1.3a: Task Distribution System
- ✅ Vibe-Kanban frontend foundation

### Enables
- O2.2a: Task Orchestration
- O2.3a: Workflow Coordination
- O2.4a: Progress Tracking
- O3.1a: MCP Configuration Management

### Blocks Until Complete
- Visual task management with agent assignment
- Agent workload visualization
- Task status and progress tracking

## Sub-Agent Assignments

### Primary Agent: FE (Frontend Agent)
**Responsibilities**:
- Transform existing Kanban interface for agent management
- Design agent-aware task cards and boards
- Implement drag-and-drop agent assignment
- Create agent workload visualization components

**Deliverables**:
- Enhanced Kanban board with agent awareness
- Agent assignment interface
- Task card redesign with agent information
- Agent workload visualization components

### Supporting Agent: BE (Backend Agent)
**Responsibilities**:
- Extend Rust backend API for agent-task integration
- Implement real-time task status updates
- Create agent assignment endpoints
- Handle task state synchronization

**Deliverables**:
- Agent-task integration API endpoints
- Real-time task update infrastructure
- Task state management system
- WebSocket communication for live updates

### Supporting Agent: UX (UX/UI Agent)
**Responsibilities**:
- Design agent-centric task management workflows
- Create intuitive agent assignment interfaces
- Design workload distribution visualizations
- Optimize user experience for multi-agent scenarios

**Deliverables**:
- Agent-centric UI/UX design
- Workload visualization design
- Task assignment workflow design
- Multi-agent interface patterns

## Acceptance Criteria

### Functional Requirements

#### O2.1a.1: Agent-Aware Kanban Board
**GIVEN** a need for visual agent task management
**WHEN** using the Kanban interface
**THEN** it should:
- ✅ Display tasks with assigned agent information prominently
- ✅ Show agent avatars, status, and workload indicators on task cards
- ✅ Support filtering and grouping tasks by agent assignment
- ✅ Provide agent-specific board views and swimlanes
- ✅ Show real-time updates when agents accept or complete tasks

#### O2.1a.2: Drag-and-Drop Agent Assignment
**GIVEN** requirements for intuitive task assignment
**WHEN** managing task assignments
**THEN** it should:
- ✅ Support drag-and-drop assignment of tasks to agents
- ✅ Show agent availability and capacity during assignment
- ✅ Validate agent capabilities against task requirements
- ✅ Provide visual feedback for valid/invalid assignment attempts
- ✅ Handle automatic task redistribution when agents become unavailable

#### O2.1a.3: Agent Workload Visualization
**GIVEN** need for workload management visibility
**WHEN** viewing agent workloads
**THEN** it should:
- ✅ Display real-time agent capacity and current task load
- ✅ Show agent performance metrics and efficiency indicators
- ✅ Provide workload distribution charts and heat maps
- ✅ Support workload balancing recommendations and actions
- ✅ Enable quick identification of overloaded or underutilized agents

### Technical Requirements

#### Enhanced Kanban Frontend Architecture
```typescript
// Enhanced Kanban interface with agent integration
import React, { useState, useEffect, useMemo } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

interface AgentAwareTask {
  id: string;
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  assignedAgent?: Agent;
  requiredCapabilities: string[];
  estimatedDuration?: number;
  dueDate?: Date;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

interface KanbanBoard {
  id: string;
  name: string;
  columns: KanbanColumn[];
  filters: BoardFilters;
  viewMode: BoardViewMode;
  agentGrouping: AgentGroupingMode;
}

interface KanbanColumn {
  id: string;
  name: string;
  status: TaskStatus;
  tasks: AgentAwareTask[];
  color: string;
  limit?: number;
  rules: ColumnRules;
}

interface BoardFilters {
  agents: string[];
  capabilities: string[];
  priorities: TaskPriority[];
  dateRange?: DateRange;
  statusFilter?: TaskStatus[];
}

enum BoardViewMode {
  Standard = 'standard',
  AgentSwimlanes = 'agent-swimlanes',
  CapabilitySwimlanes = 'capability-swimlanes',
  Timeline = 'timeline',
  Workload = 'workload',
}

enum AgentGroupingMode {
  None = 'none',
  ByAgent = 'by-agent',
  ByCapability = 'by-capability',
  ByTeam = 'by-team',
  ByWorkload = 'by-workload',
}

// Main Kanban Board Component
export const AgentKanbanBoard: React.FC<{
  boardId: string;
  agents: Agent[];
  onTaskUpdate: (task: AgentAwareTask) => void;
  onAgentAssignment: (taskId: string, agentId: string) => void;
}> = ({ boardId, agents, onTaskUpdate, onAgentAssignment }) => {
  const [board, setBoard] = useState<KanbanBoard | null>(null);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<BoardViewMode>(BoardViewMode.Standard);
  const [filters, setFilters] = useState<BoardFilters>({
    agents: [],
    capabilities: [],
    priorities: [],
  });
  const [agentWorkloads, setAgentWorkloads] = useState<Map<string, AgentWorkload>>(new Map());

  useEffect(() => {
    // Load board data
    loadBoard();
    
    // Subscribe to real-time updates
    const unsubscribe = subscribeToTaskUpdates((update) => {
      handleTaskUpdate(update);
    });
    
    return unsubscribe;
  }, [boardId]);

  useEffect(() => {
    // Load agent workload data
    loadAgentWorkloads();
    
    // Subscribe to workload updates
    const unsubscribe = subscribeToWorkloadUpdates((workload) => {
      setAgentWorkloads(prev => new Map(prev.set(workload.agentId, workload)));
    });
    
    return unsubscribe;
  }, [agents]);

  const filteredTasks = useMemo(() => {
    if (!board) return [];
    
    return board.columns.flatMap(column => 
      column.tasks.filter(task => {
        if (filters.agents.length > 0 && task.assignedAgent && 
            !filters.agents.includes(task.assignedAgent.id)) {
          return false;
        }
        
        if (filters.priorities.length > 0 && !filters.priorities.includes(task.priority)) {
          return false;
        }
        
        if (filters.capabilities.length > 0 && 
            !task.requiredCapabilities.some(cap => filters.capabilities.includes(cap))) {
          return false;
        }
        
        return true;
      })
    );
  }, [board, filters]);

  const renderBoard = () => {
    switch (viewMode) {
      case BoardViewMode.AgentSwimlanes:
        return <AgentSwimlanesView 
          board={board}
          agents={agents}
          workloads={agentWorkloads}
          onTaskUpdate={onTaskUpdate}
          onAgentAssignment={onAgentAssignment}
        />;
      case BoardViewMode.Workload:
        return <WorkloadView 
          agents={agents}
          workloads={agentWorkloads}
          tasks={filteredTasks}
        />;
      case BoardViewMode.Timeline:
        return <TimelineView 
          tasks={filteredTasks}
          agents={agents}
        />;
      default:
        return <StandardKanbanView 
          board={board}
          agents={agents}
          workloads={agentWorkloads}
          onTaskUpdate={onTaskUpdate}
          onAgentAssignment={onAgentAssignment}
        />;
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="agent-kanban-board">
        <div className="board-header">
          <div className="board-title">
            <h1>{board?.name || 'Loading...'}</h1>
          </div>
          
          <div className="board-controls">
            <ViewModeSelector 
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
            
            <AgentFilterPanel 
              agents={agents}
              filters={filters}
              onFiltersChange={setFilters}
            />
            
            <WorkloadIndicator 
              agents={agents}
              workloads={agentWorkloads}
            />
          </div>
        </div>
        
        <div className="board-content">
          {renderBoard()}
        </div>
        
        <div className="board-sidebar">
          <AgentPanel 
            agents={agents}
            workloads={agentWorkloads}
            selectedAgent={selectedAgent}
            onAgentSelect={setSelectedAgent}
          />
        </div>
      </div>
    </DndProvider>
  );
};

// Agent-aware task card component
export const AgentTaskCard: React.FC<{
  task: AgentAwareTask;
  agents: Agent[];
  onTaskUpdate: (task: AgentAwareTask) => void;
  onAgentAssignment: (taskId: string, agentId: string) => void;
}> = ({ task, agents, onTaskUpdate, onAgentAssignment }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'task',
    item: { id: task.id, type: 'task' },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const getTaskPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case TaskPriority.Critical: return 'border-red-500 bg-red-50';
      case TaskPriority.High: return 'border-orange-500 bg-orange-50';
      case TaskPriority.Normal: return 'border-blue-500 bg-blue-50';
      case TaskPriority.Low: return 'border-gray-500 bg-gray-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  const getAgentAvailabilityColor = (agent?: Agent) => {
    if (!agent) return 'bg-gray-400';
    switch (agent.status) {
      case AgentStatus.Online: return 'bg-green-500';
      case AgentStatus.Busy: return 'bg-yellow-500';
      case AgentStatus.Offline: return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  return (
    <div
      ref={drag}
      className={`task-card p-4 border-l-4 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-move ${
        getTaskPriorityColor(task.priority)
      } ${isDragging ? 'opacity-50' : ''}`}
    >
      <div className="task-header">
        <div className="task-title font-medium text-gray-900">
          {task.title}
        </div>
        
        <div className="task-meta flex items-center space-x-2">
          <span className={`task-priority text-xs px-2 py-1 rounded ${
            task.priority === TaskPriority.Critical ? 'bg-red-100 text-red-800' :
            task.priority === TaskPriority.High ? 'bg-orange-100 text-orange-800' :
            task.priority === TaskPriority.Normal ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {task.priority}
          </span>
          
          {task.estimatedDuration && (
            <span className="estimated-duration text-xs text-gray-500">
              {formatDuration(task.estimatedDuration)}
            </span>
          )}
        </div>
      </div>

      <div className="task-description text-sm text-gray-600 mt-2">
        {task.description}
      </div>

      <div className="task-capabilities mt-2">
        <div className="capabilities-list flex flex-wrap gap-1">
          {task.requiredCapabilities.map(capability => (
            <span 
              key={capability}
              className="capability-tag text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded"
            >
              {capability}
            </span>
          ))}
        </div>
      </div>

      <div className="task-assignment mt-3">
        {task.assignedAgent ? (
          <div className="assigned-agent flex items-center space-x-2">
            <div className="agent-avatar relative">
              <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs">
                {task.assignedAgent.name.charAt(0).toUpperCase()}
              </div>
              <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                getAgentAvailabilityColor(task.assignedAgent)
              }`} />
            </div>
            
            <div className="agent-info">
              <div className="agent-name text-sm font-medium text-gray-900">
                {task.assignedAgent.name}
              </div>
              <div className="agent-status text-xs text-gray-500">
                {task.assignedAgent.status}
              </div>
            </div>
            
            <button
              onClick={() => onAgentAssignment(task.id, '')}
              className="unassign-button text-xs text-red-600 hover:text-red-800"
            >
              Unassign
            </button>
          </div>
        ) : (
          <AgentAssignmentDropdown
            task={task}
            agents={agents}
            onAssignment={(agentId) => onAgentAssignment(task.id, agentId)}
          />
        )}
      </div>

      <div className="task-progress mt-2">
        {task.assignedAgent && (
          <TaskProgressIndicator 
            task={task}
            agent={task.assignedAgent}
          />
        )}
      </div>

      {task.dueDate && (
        <div className="task-due-date mt-2 text-xs text-gray-500">
          Due: {formatDate(task.dueDate)}
        </div>
      )}
    </div>
  );
};

// Agent assignment dropdown component
export const AgentAssignmentDropdown: React.FC<{
  task: AgentAwareTask;
  agents: Agent[];
  onAssignment: (agentId: string) => void;
}> = ({ task, agents, onAssignment }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const compatibleAgents = useMemo(() => {
    return agents.filter(agent => {
      // Check if agent has required capabilities
      return task.requiredCapabilities.every(capability =>
        agent.capabilities.some(agentCap => agentCap.name === capability)
      );
    }).sort((a, b) => {
      // Sort by availability and capacity
      if (a.status === AgentStatus.Online && b.status !== AgentStatus.Online) return -1;
      if (b.status === AgentStatus.Online && a.status !== AgentStatus.Online) return 1;
      return a.name.localeCompare(b.name);
    });
  }, [task, agents]);

  return (
    <div className="agent-assignment-dropdown relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="assignment-button w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Assign Agent ({compatibleAgents.length} available)
      </button>
      
      {isOpen && (
        <div className="dropdown-menu absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {compatibleAgents.length === 0 ? (
            <div className="p-3 text-sm text-gray-500">
              No compatible agents available
            </div>
          ) : (
            compatibleAgents.map(agent => (
              <button
                key={agent.id}
                onClick={() => {
                  onAssignment(agent.id);
                  setIsOpen(false);
                }}
                className="agent-option w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
              >
                <div className="flex items-center space-x-2">
                  <div className="agent-avatar relative">
                    <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs">
                      {agent.name.charAt(0).toUpperCase()}
                    </div>
                    <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border border-white ${
                      getAgentAvailabilityColor(agent)
                    }`} />
                  </div>
                  
                  <div className="agent-info flex-1">
                    <div className="agent-name text-sm font-medium text-gray-900">
                      {agent.name}
                    </div>
                    <div className="agent-details text-xs text-gray-500">
                      {agent.status} • {agent.capabilities.length} capabilities
                    </div>
                  </div>
                </div>
              </button>
            ))
          )}
        </div>
      )}
    </div>
  );
};

// Agent swimlanes view
export const AgentSwimlanesView: React.FC<{
  board: KanbanBoard | null;
  agents: Agent[];
  workloads: Map<string, AgentWorkload>;
  onTaskUpdate: (task: AgentAwareTask) => void;
  onAgentAssignment: (taskId: string, agentId: string) => void;
}> = ({ board, agents, workloads, onTaskUpdate, onAgentAssignment }) => {
  if (!board) return <div>Loading...</div>;

  return (
    <div className="agent-swimlanes-view">
      <div className="swimlanes-container">
        {/* Unassigned tasks lane */}
        <div className="swimlane unassigned-lane">
          <div className="swimlane-header">
            <h3>Unassigned Tasks</h3>
            <span className="task-count">
              {board.columns.flatMap(col => col.tasks.filter(t => !t.assignedAgent)).length}
            </span>
          </div>
          
          <div className="swimlane-columns">
            {board.columns.map(column => (
              <KanbanColumn
                key={`unassigned-${column.id}`}
                column={{
                  ...column,
                  tasks: column.tasks.filter(task => !task.assignedAgent)
                }}
                agents={agents}
                onTaskUpdate={onTaskUpdate}
                onAgentAssignment={onAgentAssignment}
              />
            ))}
          </div>
        </div>

        {/* Agent-specific lanes */}
        {agents.map(agent => {
          const workload = workloads.get(agent.id);
          const agentTasks = board.columns.flatMap(col => 
            col.tasks.filter(t => t.assignedAgent?.id === agent.id)
          );

          return (
            <div key={agent.id} className="swimlane agent-lane">
              <div className="swimlane-header">
                <div className="agent-info">
                  <div className="agent-avatar">
                    <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                      {agent.name.charAt(0).toUpperCase()}
                    </div>
                    <div className={`status-indicator ${agent.status.toLowerCase()}`} />
                  </div>
                  
                  <div className="agent-details">
                    <h3>{agent.name}</h3>
                    <span className="agent-status">{agent.status}</span>
                  </div>
                </div>
                
                <div className="workload-info">
                  {workload && (
                    <WorkloadIndicator workload={workload} compact />
                  )}
                  <span className="task-count">{agentTasks.length}</span>
                </div>
              </div>
              
              <div className="swimlane-columns">
                {board.columns.map(column => (
                  <KanbanColumn
                    key={`${agent.id}-${column.id}`}
                    column={{
                      ...column,
                      tasks: column.tasks.filter(task => 
                        task.assignedAgent?.id === agent.id
                      )
                    }}
                    agents={agents}
                    onTaskUpdate={onTaskUpdate}
                    onAgentAssignment={onAgentAssignment}
                  />
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
```

#### Backend Integration for Agent-Task Management
```rust
// Enhanced Rust backend for agent-task integration
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tokio_tungstenite::WebSocketStream;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TaskUpdate {
    pub task_id: Uuid,
    pub update_type: TaskUpdateType,
    pub data: serde_json::Value,
    pub updated_by: Option<Uuid>, // Agent or user ID
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum TaskUpdateType {
    StatusChange,
    AgentAssignment,
    AgentUnassignment,
    ProgressUpdate,
    CommentAdded,
    PriorityChange,
    DueDateChange,
    CapabilityRequirementUpdate,
}

// WebSocket handler for real-time updates
pub struct KanbanWebSocketHandler {
    task_manager: Arc<TaskManager>,
    agent_registry: Arc<AgentRegistry>,
    communication_manager: Arc<CommunicationManager>,
    active_connections: Arc<RwLock<HashMap<Uuid, WebSocketConnection>>>,
}

impl KanbanWebSocketHandler {
    pub async fn handle_connection(
        &self,
        user_id: Uuid,
        websocket: WebSocketStream<tokio::net::TcpStream>,
    ) -> Result<(), WebSocketError> {
        let connection = WebSocketConnection::new(user_id, websocket);
        
        // Store connection
        {
            let mut connections = self.active_connections.write().await;
            connections.insert(user_id, connection);
        }
        
        // Start listening for messages
        self.listen_for_messages(user_id).await?;
        
        Ok(())
    }
    
    pub async fn broadcast_task_update(&self, update: TaskUpdate) -> Result<(), WebSocketError> {
        let connections = self.active_connections.read().await;
        
        let message = serde_json::to_string(&update)?;
        
        for connection in connections.values() {
            if let Err(e) = connection.send_message(&message).await {
                eprintln!("Failed to send task update to connection: {:?}", e);
            }
        }
        
        Ok(())
    }
    
    async fn listen_for_messages(&self, user_id: Uuid) -> Result<(), WebSocketError> {
        loop {
            let connections = self.active_connections.read().await;
            let connection = match connections.get(&user_id) {
                Some(conn) => conn,
                None => break, // Connection removed
            };
            
            match connection.receive_message().await {
                Ok(message) => {
                    self.handle_incoming_message(user_id, message).await?;
                }
                Err(e) => {
                    eprintln!("WebSocket error for user {}: {:?}", user_id, e);
                    break;
                }
            }
        }
        
        // Remove connection on exit
        let mut connections = self.active_connections.write().await;
        connections.remove(&user_id);
        
        Ok(())
    }
    
    async fn handle_incoming_message(
        &self,
        user_id: Uuid,
        message: String,
    ) -> Result<(), WebSocketError> {
        let request: KanbanRequest = serde_json::from_str(&message)?;
        
        match request.action {
            KanbanAction::AssignTask { task_id, agent_id } => {
                self.handle_task_assignment(user_id, task_id, agent_id).await?;
            }
            KanbanAction::UpdateTaskStatus { task_id, new_status } => {
                self.handle_task_status_update(user_id, task_id, new_status).await?;
            }
            KanbanAction::MoveTask { task_id, column_id, position } => {
                self.handle_task_move(user_id, task_id, column_id, position).await?;
            }
            KanbanAction::SubscribeToBoard { board_id } => {
                self.handle_board_subscription(user_id, board_id).await?;
            }
        }
        
        Ok(())
    }
    
    async fn handle_task_assignment(
        &self,
        user_id: Uuid,
        task_id: Uuid,
        agent_id: Uuid,
    ) -> Result<(), WebSocketError> {
        // Validate assignment
        let task = self.task_manager.get_task(task_id).await?
            .ok_or(WebSocketError::TaskNotFound(task_id))?;
        
        let agent = self.agent_registry.get_agent(agent_id).await?
            .ok_or(WebSocketError::AgentNotFound(agent_id))?;
        
        // Check agent capabilities
        let capabilities_match = task.required_capabilities.iter().all(|cap| {
            agent.capabilities.iter().any(|agent_cap| &agent_cap.name == cap)
        });
        
        if !capabilities_match {
            return Err(WebSocketError::CapabilitiesMismatch);
        }
        
        // Perform assignment
        self.task_manager.assign_task(task_id, agent_id, Some(user_id)).await?;
        
        // Notify agent
        let assignment_message = AgentMessage {
            id: Uuid::new_v4(),
            sender_id: user_id,
            recipient_id: Some(agent_id),
            conversation_id: None,
            message_type: MessageType::TaskAssignment,
            payload: MessagePayload {
                content_type: "application/json".to_string(),
                content: serde_json::to_value(&task)?,
                attachments: vec![],
                schema_version: "1.0.0".to_string(),
            },
            metadata: MessageMetadata {
                priority: MessagePriority::Normal,
                delivery_mode: DeliveryMode::AtLeastOnce,
                encryption_level: EncryptionLevel::Transport,
                ..Default::default()
            },
            timestamp: chrono::Utc::now(),
            expires_at: task.due_at,
        };
        
        self.communication_manager.send_message(assignment_message).await?;
        
        // Broadcast update
        let update = TaskUpdate {
            task_id,
            update_type: TaskUpdateType::AgentAssignment,
            data: serde_json::json!({
                "agent_id": agent_id,
                "agent_name": agent.name,
                "assigned_by": user_id
            }),
            updated_by: Some(user_id),
            timestamp: chrono::Utc::now(),
        };
        
        self.broadcast_task_update(update).await?;
        
        Ok(())
    }
}

// API endpoints for Kanban functionality
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateTaskRequest {
    pub title: String,
    pub description: String,
    pub priority: TaskPriority,
    pub required_capabilities: Vec<String>,
    pub estimated_duration: Option<u64>, // seconds
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub board_id: Uuid,
    pub column_id: Uuid,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateTaskRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub priority: Option<TaskPriority>,
    pub status: Option<TaskStatus>,
    pub assigned_agent_id: Option<Uuid>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
}

// REST API handlers
pub async fn create_task(
    State(app_state): State<AppState>,
    Json(request): Json<CreateTaskRequest>,
) -> Result<Json<Task>, AppError> {
    let task = Task {
        id: Uuid::new_v4(),
        title: request.title,
        description: request.description,
        task_type: TaskType::Custom("kanban".to_string()),
        priority: request.priority,
        complexity: TaskComplexity::Medium, // Default
        required_capabilities: request.required_capabilities,
        estimated_duration: request.estimated_duration.map(Duration::from_secs),
        max_duration: None,
        dependencies: vec![],
        metadata: serde_json::json!({
            "board_id": request.board_id,
            "column_id": request.column_id
        }),
        created_at: chrono::Utc::now(),
        due_at: request.due_date,
        status: TaskStatus::Pending,
        assigned_agent_id: None,
        assignment_history: vec![],
    };
    
    // Store task
    app_state.task_manager.create_task(&task).await?;
    
    // Broadcast creation
    let update = TaskUpdate {
        task_id: task.id,
        update_type: TaskUpdateType::StatusChange,
        data: serde_json::to_value(&task)?,
        updated_by: None, // System
        timestamp: chrono::Utc::now(),
    };
    
    app_state.websocket_handler.broadcast_task_update(update).await?;
    
    Ok(Json(task))
}

pub async fn update_task(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
    Json(request): Json<UpdateTaskRequest>,
) -> Result<Json<Task>, AppError> {
    let mut task = app_state.task_manager.get_task(task_id).await?
        .ok_or(AppError::TaskNotFound(task_id))?;
    
    // Apply updates
    if let Some(title) = request.title {
        task.title = title;
    }
    
    if let Some(description) = request.description {
        task.description = description;
    }
    
    if let Some(priority) = request.priority {
        task.priority = priority;
    }
    
    if let Some(status) = request.status {
        task.status = status;
    }
    
    if let Some(agent_id) = request.assigned_agent_id {
        task.assigned_agent_id = Some(agent_id);
    }
    
    if let Some(due_date) = request.due_date {
        task.due_at = Some(due_date);
    }
    
    // Update in database
    app_state.task_manager.update_task(&task).await?;
    
    // Broadcast update
    let update = TaskUpdate {
        task_id: task.id,
        update_type: TaskUpdateType::StatusChange,
        data: serde_json::to_value(&task)?,
        updated_by: None,
        timestamp: chrono::Utc::now(),
    };
    
    app_state.websocket_handler.broadcast_task_update(update).await?;
    
    Ok(Json(task))
}

pub async fn get_board_tasks(
    State(app_state): State<AppState>,
    Path(board_id): Path<Uuid>,
    Query(params): Query<BoardQueryParams>,
) -> Result<Json<Vec<AgentAwareTask>>, AppError> {
    let tasks = app_state.task_manager.get_board_tasks(board_id, params).await?;
    
    // Enrich with agent information
    let enriched_tasks = futures::future::try_join_all(
        tasks.into_iter().map(|task| async {
            let agent = if let Some(agent_id) = task.assigned_agent_id {
                app_state.agent_registry.get_agent(agent_id).await?
            } else {
                None
            };
            
            Ok::<AgentAwareTask, AppError>(AgentAwareTask {
                id: task.id.to_string(),
                title: task.title,
                description: task.description,
                status: task.status,
                priority: task.priority,
                assignedAgent: agent,
                requiredCapabilities: task.required_capabilities,
                estimatedDuration: task.estimated_duration.map(|d| d.as_secs()),
                dueDate: task.due_at,
                tags: vec![], // Extract from metadata
                createdAt: task.created_at,
                updatedAt: task.created_at, // TODO: Add updated_at field
                metadata: task.metadata,
            })
        })
    ).await?;
    
    Ok(Json(enriched_tasks))
}
```

### Performance Requirements

#### Interface Performance
- **Board Loading**: <2 seconds for boards with 1000+ tasks
- **Real-time Updates**: <500ms latency for task status changes
- **Drag-and-Drop**: <100ms response time for assignment operations
- **Agent Filtering**: <300ms for capability-based filtering

#### Scalability Requirements
- **Concurrent Users**: Support 100+ simultaneous users per board
- **Task Volume**: Handle boards with 10,000+ tasks efficiently
- **Agent Pool**: Support 1,000+ agents in assignment dropdowns
- **Real-time Connections**: Maintain 500+ WebSocket connections

### Security Requirements

#### Access Control
- ✅ Role-based access to task assignment capabilities
- ✅ Agent-specific task visibility restrictions
- ✅ Secure WebSocket connections with authentication
- ✅ Audit trail for all task assignment changes

#### Data Security
- ✅ Encrypted task data transmission
- ✅ Secure agent capability information
- ✅ Protected real-time communication channels
- ✅ Safe handling of sensitive task metadata

## Quality Gates

### Definition of Done

#### Interface Functionality
- ✅ Kanban board shows agent assignments and workloads
- ✅ Drag-and-drop task assignment works intuitively
- ✅ Real-time updates reflect across all connected clients
- ✅ Agent filtering and grouping functions correctly

#### Performance Validation
- ✅ Interface responds within performance targets
- ✅ Real-time updates maintain low latency
- ✅ Large task volumes handled efficiently
- ✅ Concurrent user scenarios work smoothly

#### User Experience Validation
- ✅ Agent assignment workflow is intuitive
- ✅ Workload visualization provides clear insights
- ✅ Task management integrates seamlessly with agent capabilities
- ✅ Interface scales well across different screen sizes

### Testing Requirements

#### Unit Tests
- Task card rendering and interaction logic
- Agent assignment validation and capability matching
- Real-time update message handling
- Workload calculation and visualization

#### Integration Tests
- End-to-end task assignment workflow
- WebSocket communication for real-time updates
- Agent registry integration
- Task distribution system integration

#### User Experience Tests
- Drag-and-drop interaction scenarios
- Multi-user concurrent editing
- Agent workload visualization accuracy
- Mobile and desktop responsiveness

## Implementation Timeline

### Week 1: Core Interface
- **Days 1-2**: Enhanced task card design with agent information
- **Days 3-4**: Drag-and-drop agent assignment implementation
- **Day 5**: Basic agent workload visualization

### Week 2: Advanced Features
- **Days 1-2**: Agent swimlanes view and advanced filtering
- **Days 3-4**: Real-time updates and WebSocket integration
- **Day 5**: Testing, optimization, and documentation

## Follow-up Stories

### Immediate Next Stories
- **O2.2a**: Task Orchestration (builds on Kanban interface for workflow management)
- **O2.3a**: Workflow Coordination (extends interface for complex workflows)
- **O2.4a**: Progress Tracking (adds detailed progress monitoring to interface)

### Future Enhancements
- **Mobile App**: Native mobile interface for agent task management
- **Voice Interface**: Voice-controlled task assignment and updates
- **AI Recommendations**: Intelligent task assignment suggestions
- **Custom Views**: User-configurable board layouts and visualizations

This enhanced Kanban interface transforms the traditional task board into a sophisticated agent orchestration platform, providing intuitive visual management of multi-agent workflows.