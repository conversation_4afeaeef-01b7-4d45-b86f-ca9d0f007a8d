# Story O1.4a: Agent Performance Monitoring

## Story Overview

**Epic**: O1 - Multi-Agent System  
**Story ID**: O1.4a  
**Title**: Agent Performance Monitoring for Multi-Agent System Optimization  
**Priority**: High  
**Effort**: 8 story points  
**Sprint**: Sprint 17 (Week 33-34)  
**Phase**: Agent Orchestration  

## Dependencies

### Prerequisites
- ✅ F1.1a: Monorepo Architecture (Completed in Phase 1)
- ✅ F2.1a: Multi-Provider AI System (Completed in Phase 1)
- ✅ O1.1a: Agent Registry & Discovery
- ✅ O1.2a: Agent Communication Protocol
- ✅ O1.3a: Task Distribution System

### Enables
- O2.3a: Workflow Coordination (performance-based optimization)
- O2.4a: Progress Tracking (agent performance integration)
- O4.1a: Learning & Adaptation (performance data for learning)
- O4.2a: Decision Making Engine (performance-informed decisions)

### Blocks Until Complete
- Performance-based agent selection and optimization
- System health monitoring and alerting
- Agent capability assessment and improvement

## Sub-Agent Assignments

### Primary Agent: BE (Backend Agent)
**Responsibilities**:
- Implement performance metrics collection system
- Design performance data storage and analytics
- Create performance monitoring algorithms
- Implement alerting and notification system

**Deliverables**:
- Performance metrics collection infrastructure
- Performance data analytics engine
- Monitoring and alerting system
- Performance optimization algorithms

### Supporting Agent: FE (Frontend Agent)
**Responsibilities**:
- Create performance monitoring dashboard
- Design agent performance visualization
- Implement real-time performance displays
- Build performance analytics interface

**Deliverables**:
- Performance monitoring dashboard
- Agent performance analytics UI
- Real-time performance visualization
- Performance reporting interface

### Supporting Agent: AI (AI Integration Agent)
**Responsibilities**:
- Design intelligent performance analysis
- Implement predictive performance modeling
- Create performance anomaly detection
- Develop performance optimization recommendations

**Deliverables**:
- AI-powered performance analysis
- Predictive performance models
- Anomaly detection algorithms
- Performance optimization engine

## Acceptance Criteria

### Functional Requirements

#### O1.4a.1: Comprehensive Performance Metrics Collection
**GIVEN** a need for detailed agent performance monitoring
**WHEN** collecting performance metrics from agents
**THEN** it should:
- ✅ Track task completion times, success rates, and quality scores
- ✅ Monitor resource utilization (CPU, memory, network, custom metrics)
- ✅ Collect communication latency and message processing statistics
- ✅ Record agent availability, uptime, and error rates
- ✅ Support custom metrics defined by agent implementations

#### O1.4a.2: Real-Time Performance Analysis
**GIVEN** requirements for immediate performance insights
**WHEN** analyzing agent performance in real-time
**THEN** it should:
- ✅ Provide live performance dashboards with sub-second updates
- ✅ Detect performance anomalies and degradation automatically
- ✅ Generate alerts for critical performance thresholds
- ✅ Support performance comparison across agents and time periods
- ✅ Enable drill-down analysis from system to individual agent level

#### O1.4a.3: Performance Optimization Recommendations
**GIVEN** need for continuous performance improvement
**WHEN** analyzing historical and current performance data
**THEN** it should:
- ✅ Identify performance bottlenecks and optimization opportunities
- ✅ Recommend agent configuration changes and resource adjustments
- ✅ Suggest task redistribution for better performance
- ✅ Provide capacity planning insights and scaling recommendations
- ✅ Support A/B testing for performance optimization strategies

### Technical Requirements

#### Performance Monitoring System
```rust
// Core performance monitoring infrastructure
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::time::{Duration, Instant};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PerformanceMetrics {
    pub agent_id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub metrics: MetricsSnapshot,
    pub task_metrics: Vec<TaskPerformanceMetric>,
    pub communication_metrics: CommunicationMetrics,
    pub resource_metrics: ResourceMetrics,
    pub custom_metrics: HashMap<String, f64>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MetricsSnapshot {
    pub uptime_seconds: u64,
    pub tasks_completed: u64,
    pub tasks_failed: u64,
    pub average_task_duration: Duration,
    pub success_rate: f64,
    pub quality_score: f64,
    pub efficiency_score: f64,
    pub load_factor: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TaskPerformanceMetric {
    pub task_id: Uuid,
    pub task_type: String,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub duration: Option<Duration>,
    pub status: TaskStatus,
    pub quality_score: Option<f64>,
    pub resource_usage: ResourceUsage,
    pub error_details: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CommunicationMetrics {
    pub messages_sent: u64,
    pub messages_received: u64,
    pub average_response_time: Duration,
    pub communication_errors: u64,
    pub bandwidth_usage: u64,
    pub connection_stability: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ResourceMetrics {
    pub cpu_usage_percent: f64,
    pub memory_usage_mb: u64,
    pub memory_usage_percent: f64,
    pub disk_usage_mb: u64,
    pub network_io_bytes: u64,
    pub active_connections: u32,
    pub thread_count: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ResourceUsage {
    pub cpu_time_ms: u64,
    pub memory_peak_mb: u64,
    pub disk_io_bytes: u64,
    pub network_io_bytes: u64,
}

pub struct PerformanceMonitor {
    metrics_collector: Arc<MetricsCollector>,
    analytics_engine: Arc<PerformanceAnalytics>,
    alerting_system: Arc<AlertingSystem>,
    optimization_engine: Arc<OptimizationEngine>,
    data_store: Arc<PerformanceDataStore>,
}

impl PerformanceMonitor {
    pub fn new(
        db: Arc<Database>,
        communication_manager: Arc<CommunicationManager>,
    ) -> Self {
        let data_store = Arc::new(PerformanceDataStore::new(db.clone()));
        let metrics_collector = Arc::new(MetricsCollector::new(
            communication_manager.clone(),
            data_store.clone(),
        ));
        let analytics_engine = Arc::new(PerformanceAnalytics::new(data_store.clone()));
        let alerting_system = Arc::new(AlertingSystem::new());
        let optimization_engine = Arc::new(OptimizationEngine::new(
            analytics_engine.clone(),
            data_store.clone(),
        ));
        
        Self {
            metrics_collector,
            analytics_engine,
            alerting_system,
            optimization_engine,
            data_store,
        }
    }
    
    pub async fn start_monitoring(&self, agent_id: Uuid) -> Result<(), MonitoringError> {
        // Register agent for monitoring
        self.metrics_collector.register_agent(agent_id).await?;
        
        // Start metrics collection
        self.metrics_collector.start_collection(agent_id).await?;
        
        // Initialize performance baselines
        self.analytics_engine.initialize_baselines(agent_id).await?;
        
        Ok(())
    }
    
    pub async fn stop_monitoring(&self, agent_id: Uuid) -> Result<(), MonitoringError> {
        self.metrics_collector.stop_collection(agent_id).await?;
        Ok(())
    }
    
    pub async fn record_task_performance(
        &self,
        agent_id: Uuid,
        task_metric: TaskPerformanceMetric,
    ) -> Result<(), MonitoringError> {
        // Store task performance data
        self.data_store.store_task_performance(agent_id, &task_metric).await?;
        
        // Update agent performance aggregates
        self.analytics_engine.update_performance_aggregates(agent_id, &task_metric).await?;
        
        // Check for performance anomalies
        if let Some(anomaly) = self.analytics_engine.detect_anomaly(agent_id, &task_metric).await? {
            self.alerting_system.send_anomaly_alert(agent_id, anomaly).await?;
        }
        
        Ok(())
    }
    
    pub async fn get_agent_performance(
        &self,
        agent_id: Uuid,
        time_range: TimeRange,
    ) -> Result<AgentPerformanceSummary, MonitoringError> {
        self.analytics_engine.get_performance_summary(agent_id, time_range).await
    }
    
    pub async fn get_system_performance(
        &self,
        time_range: TimeRange,
    ) -> Result<SystemPerformanceSummary, MonitoringError> {
        self.analytics_engine.get_system_performance_summary(time_range).await
    }
    
    pub async fn get_optimization_recommendations(
        &self,
        agent_id: Option<Uuid>,
    ) -> Result<Vec<OptimizationRecommendation>, MonitoringError> {
        self.optimization_engine.generate_recommendations(agent_id).await
    }
}

// Metrics collection system
pub struct MetricsCollector {
    communication_manager: Arc<CommunicationManager>,
    data_store: Arc<PerformanceDataStore>,
    collection_tasks: Arc<RwLock<HashMap<Uuid, tokio::task::JoinHandle<()>>>>,
    collection_config: MetricsCollectionConfig,
}

#[derive(Debug, Clone)]
pub struct MetricsCollectionConfig {
    pub collection_interval: Duration,
    pub batch_size: usize,
    pub retention_period: Duration,
    pub real_time_metrics: bool,
}

impl MetricsCollector {
    pub async fn start_collection(&self, agent_id: Uuid) -> Result<(), MonitoringError> {
        let communication_manager = self.communication_manager.clone();
        let data_store = self.data_store.clone();
        let config = self.collection_config.clone();
        
        let collection_task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.collection_interval);
            
            loop {
                interval.tick().await;
                
                // Request metrics from agent
                match Self::collect_agent_metrics(
                    communication_manager.clone(),
                    data_store.clone(),
                    agent_id,
                ).await {
                    Ok(_) => {},
                    Err(e) => {
                        eprintln!("Failed to collect metrics for agent {}: {:?}", agent_id, e);
                    }
                }
            }
        });
        
        // Store collection task
        let mut tasks = self.collection_tasks.write().await;
        tasks.insert(agent_id, collection_task);
        
        Ok(())
    }
    
    async fn collect_agent_metrics(
        communication_manager: Arc<CommunicationManager>,
        data_store: Arc<PerformanceDataStore>,
        agent_id: Uuid,
    ) -> Result<(), MonitoringError> {
        // Send metrics request to agent
        let metrics_request = AgentMessage {
            id: Uuid::new_v4(),
            sender_id: Uuid::new_v4(), // System sender
            recipient_id: Some(agent_id),
            conversation_id: None,
            message_type: MessageType::HealthCheck,
            payload: MessagePayload {
                content_type: "application/json".to_string(),
                content: serde_json::json!({
                    "type": "metrics_request",
                    "include_detailed": true
                }),
                attachments: vec![],
                schema_version: "1.0.0".to_string(),
            },
            metadata: MessageMetadata {
                priority: MessagePriority::Normal,
                delivery_mode: DeliveryMode::AtMostOnce,
                encryption_level: EncryptionLevel::Transport,
                ..Default::default()
            },
            timestamp: chrono::Utc::now(),
            expires_at: Some(chrono::Utc::now() + chrono::Duration::seconds(30)),
        };
        
        // Send request and wait for response
        let response = communication_manager.send_message_and_wait_response(
            metrics_request,
            Duration::from_secs(10),
        ).await?;
        
        // Parse metrics from response
        let metrics: PerformanceMetrics = serde_json::from_value(response.payload.content)?;
        
        // Store metrics
        data_store.store_performance_metrics(&metrics).await?;
        
        Ok(())
    }
}

// Performance analytics engine
pub struct PerformanceAnalytics {
    data_store: Arc<PerformanceDataStore>,
    anomaly_detector: Arc<AnomalyDetector>,
    performance_models: Arc<RwLock<HashMap<Uuid, PerformanceModel>>>,
}

#[derive(Debug)]
pub struct PerformanceModel {
    agent_id: Uuid,
    baseline_metrics: MetricsSnapshot,
    performance_trends: Vec<TrendPoint>,
    efficiency_model: EfficiencyModel,
    capacity_model: CapacityModel,
    last_updated: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug)]
struct TrendPoint {
    timestamp: chrono::DateTime<chrono::Utc>,
    metric_name: String,
    value: f64,
    trend_direction: TrendDirection,
}

#[derive(Debug)]
enum TrendDirection {
    Improving,
    Stable,
    Degrading,
}

impl PerformanceAnalytics {
    pub async fn get_performance_summary(
        &self,
        agent_id: Uuid,
        time_range: TimeRange,
    ) -> Result<AgentPerformanceSummary, MonitoringError> {
        // Get raw metrics for time range
        let metrics = self.data_store.get_agent_metrics(agent_id, time_range).await?;
        
        // Calculate performance summary
        let summary = self.calculate_performance_summary(&metrics).await?;
        
        // Add trend analysis
        let trends = self.analyze_performance_trends(agent_id, &metrics).await?;
        
        // Generate performance score
        let performance_score = self.calculate_performance_score(&summary, &trends).await?;
        
        Ok(AgentPerformanceSummary {
            agent_id,
            time_range,
            summary,
            trends,
            performance_score,
            recommendations: self.generate_performance_recommendations(agent_id, &summary).await?,
        })
    }
    
    pub async fn detect_anomaly(
        &self,
        agent_id: Uuid,
        task_metric: &TaskPerformanceMetric,
    ) -> Result<Option<PerformanceAnomaly>, MonitoringError> {
        // Get agent's performance model
        let models = self.performance_models.read().await;
        let model = models.get(&agent_id);
        
        if let Some(model) = model {
            // Check for anomalies against baseline
            if let Some(anomaly) = self.anomaly_detector.detect_task_anomaly(model, task_metric).await? {
                return Ok(Some(anomaly));
            }
        }
        
        Ok(None)
    }
    
    async fn calculate_performance_summary(
        &self,
        metrics: &[PerformanceMetrics],
    ) -> Result<PerformanceSummary, MonitoringError> {
        if metrics.is_empty() {
            return Err(MonitoringError::InsufficientData);
        }
        
        let total_tasks: u64 = metrics.iter().map(|m| m.metrics.tasks_completed + m.metrics.tasks_failed).sum();
        let total_completed: u64 = metrics.iter().map(|m| m.metrics.tasks_completed).sum();
        let total_failed: u64 = metrics.iter().map(|m| m.metrics.tasks_failed).sum();
        
        let success_rate = if total_tasks > 0 {
            total_completed as f64 / total_tasks as f64
        } else {
            0.0
        };
        
        let average_duration: Duration = {
            let total_duration: Duration = metrics.iter().map(|m| m.metrics.average_task_duration).sum();
            total_duration / metrics.len() as u32
        };
        
        let average_quality_score = metrics.iter().map(|m| m.metrics.quality_score).sum::<f64>() / metrics.len() as f64;
        
        let resource_efficiency = self.calculate_resource_efficiency(metrics).await?;
        
        Ok(PerformanceSummary {
            total_tasks,
            success_rate,
            average_duration,
            average_quality_score,
            resource_efficiency,
            uptime_percentage: self.calculate_uptime_percentage(metrics).await?,
            throughput: self.calculate_throughput(metrics).await?,
        })
    }
}

// Optimization engine for performance improvements
pub struct OptimizationEngine {
    analytics: Arc<PerformanceAnalytics>,
    data_store: Arc<PerformanceDataStore>,
    optimization_models: Arc<RwLock<HashMap<String, OptimizationModel>>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OptimizationRecommendation {
    pub id: Uuid,
    pub target_agent_id: Option<Uuid>,
    pub recommendation_type: OptimizationType,
    pub title: String,
    pub description: String,
    pub expected_improvement: f64,
    pub implementation_effort: EffortLevel,
    pub priority: RecommendationPriority,
    pub implementation_steps: Vec<String>,
    pub metrics_to_monitor: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum OptimizationType {
    TaskDistribution,
    ResourceOptimization,
    ConfigurationAdjustment,
    CapacityScaling,
    WorkflowOptimization,
    CommunicationOptimization,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum EffortLevel {
    Low,
    Medium,
    High,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum RecommendationPriority {
    Critical,
    High,
    Medium,
    Low,
}

impl OptimizationEngine {
    pub async fn generate_recommendations(
        &self,
        agent_id: Option<Uuid>,
    ) -> Result<Vec<OptimizationRecommendation>, MonitoringError> {
        let mut recommendations = Vec::new();
        
        if let Some(agent_id) = agent_id {
            // Generate agent-specific recommendations
            recommendations.extend(self.generate_agent_recommendations(agent_id).await?);
        } else {
            // Generate system-wide recommendations
            recommendations.extend(self.generate_system_recommendations().await?);
        }
        
        // Sort by priority and expected improvement
        recommendations.sort_by(|a, b| {
            match a.priority.cmp(&b.priority) {
                std::cmp::Ordering::Equal => {
                    b.expected_improvement.partial_cmp(&a.expected_improvement)
                        .unwrap_or(std::cmp::Ordering::Equal)
                },
                other => other,
            }
        });
        
        Ok(recommendations)
    }
    
    async fn generate_agent_recommendations(
        &self,
        agent_id: Uuid,
    ) -> Result<Vec<OptimizationRecommendation>, MonitoringError> {
        let mut recommendations = Vec::new();
        
        // Get agent performance data
        let time_range = TimeRange::last_week();
        let performance = self.analytics.get_performance_summary(agent_id, time_range).await?;
        
        // Analyze performance issues
        if performance.summary.success_rate < 0.95 {
            recommendations.push(OptimizationRecommendation {
                id: Uuid::new_v4(),
                target_agent_id: Some(agent_id),
                recommendation_type: OptimizationType::TaskDistribution,
                title: "Improve Task Success Rate".to_string(),
                description: format!(
                    "Agent success rate is {:.1}%, below optimal threshold of 95%",
                    performance.summary.success_rate * 100.0
                ),
                expected_improvement: 0.15,
                implementation_effort: EffortLevel::Medium,
                priority: RecommendationPriority::High,
                implementation_steps: vec![
                    "Analyze failed task patterns".to_string(),
                    "Adjust task complexity assignments".to_string(),
                    "Implement additional error handling".to_string(),
                ],
                metrics_to_monitor: vec!["success_rate".to_string(), "error_rate".to_string()],
                created_at: chrono::Utc::now(),
            });
        }
        
        if performance.summary.resource_efficiency < 0.7 {
            recommendations.push(OptimizationRecommendation {
                id: Uuid::new_v4(),
                target_agent_id: Some(agent_id),
                recommendation_type: OptimizationType::ResourceOptimization,
                title: "Optimize Resource Usage".to_string(),
                description: "Agent shows low resource efficiency, potential for optimization".to_string(),
                expected_improvement: 0.25,
                implementation_effort: EffortLevel::Low,
                priority: RecommendationPriority::Medium,
                implementation_steps: vec![
                    "Review resource allocation patterns".to_string(),
                    "Optimize memory usage".to_string(),
                    "Implement resource pooling".to_string(),
                ],
                metrics_to_monitor: vec!["cpu_usage".to_string(), "memory_usage".to_string()],
                created_at: chrono::Utc::now(),
            });
        }
        
        Ok(recommendations)
    }
}
```

#### Frontend Performance Monitoring Interface
```typescript
// Performance monitoring dashboard interface
export interface PerformanceMonitoringManager {
  // Performance data retrieval
  getAgentPerformance(agentId: string, timeRange: TimeRange): Promise<AgentPerformanceSummary>;
  getSystemPerformance(timeRange: TimeRange): Promise<SystemPerformanceSummary>;
  getPerformanceMetrics(agentId: string, timeRange: TimeRange): Promise<PerformanceMetrics[]>;
  
  // Optimization and recommendations
  getOptimizationRecommendations(agentId?: string): Promise<OptimizationRecommendation[]>;
  implementRecommendation(recommendationId: string): Promise<ImplementationResult>;
  
  // Real-time monitoring
  subscribeToPerformanceUpdates(callback: (update: PerformanceUpdate) => void): () => void;
  subscribeToAnomalyAlerts(callback: (alert: AnomalyAlert) => void): () => void;
}

// React components for performance monitoring
export const PerformanceMonitoringDashboard: React.FC = () => {
  const [systemPerformance, setSystemPerformance] = useState<SystemPerformanceSummary | null>(null);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [agentPerformance, setAgentPerformance] = useState<AgentPerformanceSummary | null>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>(TimeRange.lastHour());
  const [recommendations, setRecommendations] = useState<OptimizationRecommendation[]>([]);
  
  useEffect(() => {
    // Subscribe to real-time performance updates
    const unsubscribePerformance = performanceManager.subscribeToPerformanceUpdates((update) => {
      // Update displays based on performance updates
      if (update.agentId === selectedAgent) {
        loadAgentPerformance(selectedAgent);
      }
      loadSystemPerformance();
    });
    
    const unsubscribeAnomalies = performanceManager.subscribeToAnomalyAlerts((alert) => {
      // Show anomaly notifications
      showAnomalyNotification(alert);
    });
    
    return () => {
      unsubscribePerformance();
      unsubscribeAnomalies();
    };
  }, [selectedAgent]);
  
  const loadSystemPerformance = async () => {
    try {
      const performance = await performanceManager.getSystemPerformance(timeRange);
      setSystemPerformance(performance);
    } catch (error) {
      console.error('Failed to load system performance:', error);
    }
  };
  
  const loadAgentPerformance = async (agentId: string) => {
    try {
      const performance = await performanceManager.getAgentPerformance(agentId, timeRange);
      setAgentPerformance(performance);
    } catch (error) {
      console.error('Failed to load agent performance:', error);
    }
  };
  
  return (
    <div className="performance-monitoring-dashboard">
      <div className="dashboard-header">
        <h1>Performance Monitoring</h1>
        <TimeRangeSelector timeRange={timeRange} onTimeRangeChange={setTimeRange} />
      </div>
      
      <div className="dashboard-content">
        <div className="overview-panel">
          <SystemPerformanceOverview 
            performance={systemPerformance}
            timeRange={timeRange}
          />
        </div>
        
        <div className="agent-performance-panel">
          <AgentPerformanceGrid
            onAgentSelect={setSelectedAgent}
            selectedAgent={selectedAgent}
            timeRange={timeRange}
          />
        </div>
        
        <div className="details-panel">
          {selectedAgent && agentPerformance && (
            <AgentPerformanceDetails
              agentId={selectedAgent}
              performance={agentPerformance}
              timeRange={timeRange}
            />
          )}
        </div>
        
        <div className="recommendations-panel">
          <OptimizationRecommendations
            recommendations={recommendations}
            onImplementRecommendation={(id) => 
              performanceManager.implementRecommendation(id)
            }
          />
        </div>
      </div>
    </div>
  );
};

export const SystemPerformanceOverview: React.FC<{
  performance: SystemPerformanceSummary | null;
  timeRange: TimeRange;
}> = ({ performance, timeRange }) => {
  if (!performance) {
    return <div className="loading">Loading system performance...</div>;
  }
  
  return (
    <div className="system-performance-overview">
      <h3>System Performance Overview</h3>
      
      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-label">Total Agents</div>
          <div className="metric-value">{performance.totalAgents}</div>
          <div className="metric-change">
            {performance.agentChange > 0 ? '+' : ''}{performance.agentChange}
          </div>
        </div>
        
        <div className="metric-card">
          <div className="metric-label">Average Success Rate</div>
          <div className="metric-value">{(performance.averageSuccessRate * 100).toFixed(1)}%</div>
          <div className={`metric-change ${performance.successRateChange >= 0 ? 'positive' : 'negative'}`}>
            {performance.successRateChange > 0 ? '+' : ''}{(performance.successRateChange * 100).toFixed(1)}%
          </div>
        </div>
        
        <div className="metric-card">
          <div className="metric-label">Total Tasks</div>
          <div className="metric-value">{performance.totalTasks.toLocaleString()}</div>
          <div className="metric-change">
            {performance.taskChange > 0 ? '+' : ''}{performance.taskChange.toLocaleString()}
          </div>
        </div>
        
        <div className="metric-card">
          <div className="metric-label">System Efficiency</div>
          <div className="metric-value">{(performance.systemEfficiency * 100).toFixed(1)}%</div>
          <div className={`metric-change ${performance.efficiencyChange >= 0 ? 'positive' : 'negative'}`}>
            {performance.efficiencyChange > 0 ? '+' : ''}{(performance.efficiencyChange * 100).toFixed(1)}%
          </div>
        </div>
      </div>
      
      <div className="performance-charts">
        <PerformanceTrendChart 
          data={performance.performanceTrends}
          timeRange={timeRange}
        />
      </div>
    </div>
  );
};

export const AgentPerformanceGrid: React.FC<{
  onAgentSelect: (agentId: string) => void;
  selectedAgent: string | null;
  timeRange: TimeRange;
}> = ({ onAgentSelect, selectedAgent, timeRange }) => {
  const [agentPerformances, setAgentPerformances] = useState<AgentPerformanceSnapshot[]>([]);
  const [sortBy, setSortBy] = useState<'successRate' | 'efficiency' | 'throughput'>('successRate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  useEffect(() => {
    loadAgentPerformances();
  }, [timeRange, sortBy, sortOrder]);
  
  const loadAgentPerformances = async () => {
    try {
      const performances = await performanceManager.getAllAgentPerformances(timeRange);
      const sorted = performances.sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        const comparison = sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
        return comparison;
      });
      setAgentPerformances(sorted);
    } catch (error) {
      console.error('Failed to load agent performances:', error);
    }
  };
  
  const getPerformanceColor = (value: number, metric: string) => {
    const thresholds = {
      successRate: { good: 0.95, warning: 0.9 },
      efficiency: { good: 0.8, warning: 0.6 },
      throughput: { good: 10, warning: 5 },
    };
    
    const threshold = thresholds[metric];
    if (value >= threshold.good) return 'text-green-600';
    if (value >= threshold.warning) return 'text-yellow-600';
    return 'text-red-600';
  };
  
  return (
    <div className="agent-performance-grid">
      <div className="grid-header">
        <h3>Agent Performance</h3>
        <div className="sort-controls">
          <select 
            value={sortBy} 
            onChange={(e) => setSortBy(e.target.value as any)}
          >
            <option value="successRate">Success Rate</option>
            <option value="efficiency">Efficiency</option>
            <option value="throughput">Throughput</option>
          </select>
          <button onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}>
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>
      </div>
      
      <div className="performance-grid">
        {agentPerformances.map(performance => (
          <div
            key={performance.agentId}
            className={`performance-card cursor-pointer ${
              selectedAgent === performance.agentId ? 'selected' : ''
            }`}
            onClick={() => onAgentSelect(performance.agentId)}
          >
            <div className="card-header">
              <span className="agent-name">{performance.agentName}</span>
              <span className={`status-indicator ${performance.status.toLowerCase()}`}>
                {performance.status}
              </span>
            </div>
            
            <div className="performance-metrics">
              <div className="metric">
                <span className="label">Success Rate</span>
                <span className={`value ${getPerformanceColor(performance.successRate, 'successRate')}`}>
                  {(performance.successRate * 100).toFixed(1)}%
                </span>
              </div>
              
              <div className="metric">
                <span className="label">Efficiency</span>
                <span className={`value ${getPerformanceColor(performance.efficiency, 'efficiency')}`}>
                  {(performance.efficiency * 100).toFixed(1)}%
                </span>
              </div>
              
              <div className="metric">
                <span className="label">Tasks/Hour</span>
                <span className={`value ${getPerformanceColor(performance.throughput, 'throughput')}`}>
                  {performance.throughput.toFixed(1)}
                </span>
              </div>
            </div>
            
            <div className="performance-chart">
              <MiniPerformanceChart data={performance.recentTrend} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Performance Requirements

#### Monitoring Performance
- **Metrics Collection**: <1 second latency for real-time metrics
- **Analytics Processing**: <5 seconds for performance summary generation
- **Dashboard Updates**: <2 seconds for real-time dashboard refresh
- **Data Storage**: 1 year retention with efficient querying

#### Scalability Requirements
- **Concurrent Monitoring**: Support 1,000+ agents simultaneously
- **Metrics Volume**: Handle 100,000+ metrics per minute
- **Historical Data**: Efficient querying of large historical datasets
- **Real-time Processing**: Sub-second anomaly detection

### Security Requirements

#### Data Security
- ✅ Encrypted storage of performance metrics
- ✅ Access control for performance data viewing
- ✅ Audit trail for performance data access
- ✅ Secure metrics transmission between agents

#### Privacy Protection
- ✅ Anonymization of sensitive performance data
- ✅ Role-based access to detailed performance metrics
- ✅ Compliance with data retention policies
- ✅ Secure aggregation and reporting

## Quality Gates

### Definition of Done

#### Monitoring Functionality
- ✅ Comprehensive metrics collection from all agents
- ✅ Real-time performance analysis and alerting
- ✅ Performance optimization recommendations generated
- ✅ Dashboard provides actionable insights

#### Performance Validation
- ✅ Monitoring system meets performance targets
- ✅ Analytics processing scales to required volumes
- ✅ Real-time updates maintain low latency
- ✅ Historical data queries perform efficiently

#### Accuracy Validation
- ✅ Performance metrics accurately reflect agent behavior
- ✅ Anomaly detection identifies real issues
- ✅ Optimization recommendations provide measurable improvements
- ✅ Trend analysis predicts performance changes

### Testing Requirements

#### Unit Tests
- Metrics collection and validation logic
- Performance calculation algorithms
- Anomaly detection accuracy
- Optimization recommendation generation

#### Integration Tests
- End-to-end metrics flow from agents to dashboard
- Real-time update propagation
- Alert generation and delivery
- Dashboard functionality with live data

#### Performance Tests
- High-volume metrics collection
- Large-scale analytics processing
- Dashboard responsiveness under load
- Historical data query performance

## Implementation Timeline

### Week 1: Core Monitoring
- **Days 1-2**: Metrics collection infrastructure and data models
- **Days 3-4**: Performance analytics engine and calculations
- **Day 5**: Basic anomaly detection and alerting

### Week 2: Advanced Analytics and UI
- **Days 1-2**: Optimization engine and recommendation system
- **Days 3-4**: Performance monitoring dashboard and visualization
- **Day 5**: Testing, optimization, and documentation

## Follow-up Stories

### Immediate Next Stories
- **O2.3a**: Workflow Coordination (uses performance data for optimization)
- **O2.4a**: Progress Tracking (integrates performance monitoring)
- **O4.1a**: Learning & Adaptation (uses performance data for learning)

### Future Enhancements
- **Predictive Analytics**: Machine learning-based performance prediction
- **Automated Optimization**: Self-optimizing agent configurations
- **Benchmarking**: Comparative performance analysis across agent types
- **Capacity Planning**: Intelligent resource scaling recommendations

This comprehensive performance monitoring system provides the observability foundation needed for intelligent agent orchestration and continuous system optimization.