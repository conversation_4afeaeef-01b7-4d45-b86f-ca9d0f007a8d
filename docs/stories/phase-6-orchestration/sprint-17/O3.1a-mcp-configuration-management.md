# Story O3.1a: MCP Configuration Management

## Story Overview

**Epic**: O3 - MCP Integration  
**Story ID**: O3.1a  
**Title**: MCP Configuration Management for Agent Tool Integration  
**Priority**: Critical  
**Effort**: 8 story points  
**Sprint**: Sprint 17 (Week 33-34)  
**Phase**: Agent Orchestration  

## Dependencies

### Prerequisites
- ✅ F1.1a: Monorepo Architecture (Completed in Phase 1)
- ✅ F2.1a: Multi-Provider AI System (Completed in Phase 1)
- ✅ F4.4a: Security Framework (Completed in Phase 1)
- ✅ O1.1a: Agent Registry & Discovery
- ✅ O1.2a: Agent Communication Protocol

### Enables
- O3.2a: Tool Integration Framework
- O3.3a: Service Discovery
- O3.4a: Performance Optimization
- All MCP-based agent tool interactions

### Blocks Until Complete
- MCP server configuration and management
- Agent tool capability registration
- Dynamic tool discovery and configuration

## Sub-Agent Assignments

### Primary Agent: BE (Backend Agent)
**Responsibilities**:
- Implement MCP server configuration management
- Design dynamic configuration system
- Create MCP capability registry
- Implement configuration validation and security

**Deliverables**:
- MCP configuration management system
- Dynamic configuration framework
- MCP capability registry
- Configuration validation system

### Supporting Agent: FE (Frontend Agent)
**Responsibilities**:
- Create MCP configuration management interface
- Design tool capability visualization
- Implement configuration editing interface
- Build MCP server monitoring dashboard

**Deliverables**:
- MCP configuration management UI
- Tool capability dashboard
- Configuration editor interface
- MCP server monitoring interface

### Supporting Agent: SEC (Security Agent)
**Responsibilities**:
- Design secure MCP configuration storage
- Implement MCP authentication and authorization
- Create configuration access control
- Establish secure MCP communication protocols

**Deliverables**:
- Secure configuration storage
- MCP authentication system
- Access control framework
- Secure communication protocols

## Acceptance Criteria

### Functional Requirements

#### O3.1a.1: Dynamic MCP Server Configuration
**GIVEN** a need for flexible MCP server management
**WHEN** configuring MCP servers for agent integration
**THEN** it should:
- ✅ Support dynamic addition and removal of MCP servers
- ✅ Validate MCP server configurations and connection parameters
- ✅ Handle MCP server authentication and credential management
- ✅ Provide configuration templates for common MCP server types
- ✅ Support environment-specific configuration overrides

#### O3.1a.2: Tool Capability Registration and Discovery
**GIVEN** requirements for comprehensive tool management
**WHEN** managing MCP tool capabilities
**THEN** it should:
- ✅ Automatically discover and register MCP tool capabilities
- ✅ Maintain tool capability metadata and documentation
- ✅ Support capability versioning and compatibility tracking
- ✅ Enable tool capability filtering and search functionality
- ✅ Provide capability dependency mapping and validation

#### O3.1a.3: Configuration Validation and Security
**GIVEN** need for secure and reliable configuration management
**WHEN** managing MCP configurations
**THEN** it should:
- ✅ Validate configuration syntax and semantic correctness
- ✅ Encrypt sensitive configuration data and credentials
- ✅ Implement role-based access control for configuration management
- ✅ Maintain configuration audit trail and change history
- ✅ Support configuration backup and restoration

### Technical Requirements

#### MCP Configuration Management System
```rust
// Core MCP configuration management
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::RwLock;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MCPServerConfig {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub server_type: MCPServerType,
    pub connection_config: ConnectionConfig,
    pub authentication: AuthenticationConfig,
    pub capabilities: Vec<MCPCapability>,
    pub metadata: MCPServerMetadata,
    pub status: MCPServerStatus,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_seen: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum MCPServerType {
    Stdio,
    WebSocket,
    Http,
    Tcp,
    Custom(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ConnectionConfig {
    pub endpoint: String,
    pub transport: TransportConfig,
    pub timeout_seconds: u64,
    pub retry_config: RetryConfig,
    pub connection_pool: PoolConfig,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum TransportConfig {
    Stdio {
        command: String,
        args: Vec<String>,
        env: HashMap<String, String>,
        working_dir: Option<String>,
    },
    WebSocket {
        url: String,
        headers: HashMap<String, String>,
        protocols: Vec<String>,
    },
    Http {
        base_url: String,
        headers: HashMap<String, String>,
        tls_config: Option<TlsConfig>,
    },
    Tcp {
        host: String,
        port: u16,
        tls_config: Option<TlsConfig>,
    },
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AuthenticationConfig {
    pub auth_type: AuthenticationType,
    pub credentials: EncryptedCredentials,
    pub token_refresh: Option<TokenRefreshConfig>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum AuthenticationType {
    None,
    ApiKey,
    Bearer,
    OAuth2,
    Basic,
    Custom(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MCPCapability {
    pub id: Uuid,
    pub name: String,
    pub version: String,
    pub capability_type: CapabilityType,
    pub description: String,
    pub schema: serde_json::Value,
    pub parameters: Vec<CapabilityParameter>,
    pub returns: CapabilityReturn,
    pub tags: Vec<String>,
    pub documentation_url: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CapabilityType {
    Tool,
    Resource,
    Prompt,
    Completion,
    Sampling,
    Custom(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CapabilityParameter {
    pub name: String,
    pub parameter_type: String,
    pub required: bool,
    pub description: String,
    pub default_value: Option<serde_json::Value>,
    pub validation_rules: Vec<ValidationRule>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum MCPServerStatus {
    Configuring,
    Connecting,
    Connected,
    Disconnected,
    Error,
    Maintenance,
}

pub struct MCPConfigurationManager {
    config_store: Arc<ConfigurationStore>,
    capability_registry: Arc<CapabilityRegistry>,
    connection_manager: Arc<MCPConnectionManager>,
    security_manager: Arc<MCPSecurityManager>,
    validation_engine: Arc<ConfigurationValidator>,
}

impl MCPConfigurationManager {
    pub fn new(
        db: Arc<Database>,
        encryption_key: &[u8],
    ) -> Self {
        let config_store = Arc::new(ConfigurationStore::new(db.clone()));
        let security_manager = Arc::new(MCPSecurityManager::new(encryption_key));
        let capability_registry = Arc::new(CapabilityRegistry::new(db.clone()));
        let connection_manager = Arc::new(MCPConnectionManager::new());
        let validation_engine = Arc::new(ConfigurationValidator::new());
        
        Self {
            config_store,
            capability_registry,
            connection_manager,
            security_manager,
            validation_engine,
        }
    }
    
    pub async fn create_mcp_server_config(
        &self,
        config_request: CreateMCPServerRequest,
    ) -> Result<MCPServerConfig, MCPConfigError> {
        // Validate configuration
        self.validation_engine.validate_server_config(&config_request).await?;
        
        // Encrypt sensitive data
        let encrypted_auth = self.security_manager.encrypt_authentication(&config_request.authentication).await?;
        
        // Create configuration
        let config = MCPServerConfig {
            id: Uuid::new_v4(),
            name: config_request.name,
            description: config_request.description,
            server_type: config_request.server_type,
            connection_config: config_request.connection_config,
            authentication: encrypted_auth,
            capabilities: vec![], // Will be discovered after connection
            metadata: config_request.metadata,
            status: MCPServerStatus::Configuring,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_seen: None,
        };
        
        // Store configuration
        self.config_store.save_config(&config).await?;
        
        // Attempt initial connection and capability discovery
        match self.connect_and_discover_capabilities(&config).await {
            Ok(capabilities) => {
                let mut updated_config = config.clone();
                updated_config.capabilities = capabilities;
                updated_config.status = MCPServerStatus::Connected;
                updated_config.last_seen = Some(chrono::Utc::now());
                
                self.config_store.update_config(&updated_config).await?;
                Ok(updated_config)
            }
            Err(e) => {
                let mut error_config = config.clone();
                error_config.status = MCPServerStatus::Error;
                
                self.config_store.update_config(&error_config).await?;
                Err(MCPConfigError::ConnectionFailed(e.to_string()))
            }
        }
    }
    
    pub async fn update_mcp_server_config(
        &self,
        server_id: Uuid,
        updates: UpdateMCPServerRequest,
    ) -> Result<MCPServerConfig, MCPConfigError> {
        let mut config = self.config_store.get_config(server_id).await?
            .ok_or(MCPConfigError::ServerNotFound(server_id))?;
        
        // Apply updates
        if let Some(name) = updates.name {
            config.name = name;
        }
        
        if let Some(description) = updates.description {
            config.description = description;
        }
        
        if let Some(connection_config) = updates.connection_config {
            config.connection_config = connection_config;
        }
        
        if let Some(authentication) = updates.authentication {
            config.authentication = self.security_manager.encrypt_authentication(&authentication).await?;
        }
        
        config.updated_at = chrono::Utc::now();
        
        // Validate updated configuration
        self.validation_engine.validate_existing_config(&config).await?;
        
        // Save updated configuration
        self.config_store.update_config(&config).await?;
        
        // Reconnect if connection parameters changed
        if updates.connection_config.is_some() || updates.authentication.is_some() {
            self.reconnect_server(server_id).await?;
        }
        
        Ok(config)
    }
    
    pub async fn delete_mcp_server_config(&self, server_id: Uuid) -> Result<(), MCPConfigError> {
        // Disconnect server first
        self.connection_manager.disconnect_server(server_id).await?;
        
        // Remove from capability registry
        self.capability_registry.remove_server_capabilities(server_id).await?;
        
        // Delete configuration
        self.config_store.delete_config(server_id).await?;
        
        Ok(())
    }
    
    pub async fn discover_server_capabilities(
        &self,
        server_id: Uuid,
    ) -> Result<Vec<MCPCapability>, MCPConfigError> {
        let config = self.config_store.get_config(server_id).await?
            .ok_or(MCPConfigError::ServerNotFound(server_id))?;
        
        // Connect to server
        let connection = self.connection_manager.connect_to_server(&config).await?;
        
        // Discover capabilities
        let capabilities = self.perform_capability_discovery(connection).await?;
        
        // Update configuration with discovered capabilities
        let mut updated_config = config;
        updated_config.capabilities = capabilities.clone();
        updated_config.status = MCPServerStatus::Connected;
        updated_config.last_seen = Some(chrono::Utc::now());
        
        self.config_store.update_config(&updated_config).await?;
        
        // Register capabilities
        for capability in &capabilities {
            self.capability_registry.register_capability(server_id, capability).await?;
        }
        
        Ok(capabilities)
    }
    
    async fn connect_and_discover_capabilities(
        &self,
        config: &MCPServerConfig,
    ) -> Result<Vec<MCPCapability>, MCPConfigError> {
        // Establish connection
        let connection = self.connection_manager.connect_to_server(config).await?;
        
        // Perform capability discovery
        self.perform_capability_discovery(connection).await
    }
    
    async fn perform_capability_discovery(
        &self,
        connection: MCPConnection,
    ) -> Result<Vec<MCPCapability>, MCPConfigError> {
        let mut capabilities = Vec::new();
        
        // Discover tools
        if let Ok(tools) = connection.list_tools().await {
            for tool in tools {
                capabilities.push(MCPCapability {
                    id: Uuid::new_v4(),
                    name: tool.name,
                    version: tool.version.unwrap_or("1.0.0".to_string()),
                    capability_type: CapabilityType::Tool,
                    description: tool.description.unwrap_or_default(),
                    schema: tool.input_schema,
                    parameters: self.parse_tool_parameters(&tool.input_schema)?,
                    returns: CapabilityReturn {
                        return_type: "object".to_string(),
                        description: "Tool execution result".to_string(),
                        schema: serde_json::json!({}),
                    },
                    tags: vec!["tool".to_string()],
                    documentation_url: None,
                });
            }
        }
        
        // Discover resources
        if let Ok(resources) = connection.list_resources().await {
            for resource in resources {
                capabilities.push(MCPCapability {
                    id: Uuid::new_v4(),
                    name: resource.name,
                    version: "1.0.0".to_string(),
                    capability_type: CapabilityType::Resource,
                    description: resource.description.unwrap_or_default(),
                    schema: serde_json::json!({}),
                    parameters: vec![],
                    returns: CapabilityReturn {
                        return_type: "resource".to_string(),
                        description: "Resource content".to_string(),
                        schema: serde_json::json!({}),
                    },
                    tags: vec!["resource".to_string()],
                    documentation_url: None,
                });
            }
        }
        
        // Discover prompts
        if let Ok(prompts) = connection.list_prompts().await {
            for prompt in prompts {
                capabilities.push(MCPCapability {
                    id: Uuid::new_v4(),
                    name: prompt.name,
                    version: "1.0.0".to_string(),
                    capability_type: CapabilityType::Prompt,
                    description: prompt.description.unwrap_or_default(),
                    schema: serde_json::json!({}),
                    parameters: self.parse_prompt_parameters(&prompt.arguments)?,
                    returns: CapabilityReturn {
                        return_type: "prompt".to_string(),
                        description: "Prompt content".to_string(),
                        schema: serde_json::json!({}),
                    },
                    tags: vec!["prompt".to_string()],
                    documentation_url: None,
                });
            }
        }
        
        Ok(capabilities)
    }
    
    pub async fn get_all_configurations(&self) -> Result<Vec<MCPServerConfig>, MCPConfigError> {
        self.config_store.get_all_configs().await
    }
    
    pub async fn get_configuration(&self, server_id: Uuid) -> Result<Option<MCPServerConfig>, MCPConfigError> {
        self.config_store.get_config(server_id).await
    }
    
    pub async fn test_connection(&self, server_id: Uuid) -> Result<ConnectionTestResult, MCPConfigError> {
        let config = self.config_store.get_config(server_id).await?
            .ok_or(MCPConfigError::ServerNotFound(server_id))?;
        
        let start_time = std::time::Instant::now();
        
        match self.connection_manager.test_connection(&config).await {
            Ok(server_info) => {
                let duration = start_time.elapsed();
                
                Ok(ConnectionTestResult {
                    success: true,
                    response_time_ms: duration.as_millis() as u64,
                    server_info: Some(server_info),
                    error_message: None,
                    tested_at: chrono::Utc::now(),
                })
            }
            Err(e) => {
                let duration = start_time.elapsed();
                
                Ok(ConnectionTestResult {
                    success: false,
                    response_time_ms: duration.as_millis() as u64,
                    server_info: None,
                    error_message: Some(e.to_string()),
                    tested_at: chrono::Utc::now(),
                })
            }
        }
    }
}

// Configuration validation system
pub struct ConfigurationValidator {
    validation_rules: HashMap<String, ValidationRule>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ValidationRule {
    pub name: String,
    pub rule_type: ValidationRuleType,
    pub parameters: serde_json::Value,
    pub error_message: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ValidationRuleType {
    Required,
    Format,
    Range,
    Enum,
    Custom(String),
}

impl ConfigurationValidator {
    pub async fn validate_server_config(
        &self,
        config: &CreateMCPServerRequest,
    ) -> Result<(), ValidationError> {
        let mut errors = Vec::new();
        
        // Validate name
        if config.name.trim().is_empty() {
            errors.push("Server name cannot be empty".to_string());
        }
        
        // Validate connection configuration
        match &config.connection_config.transport {
            TransportConfig::Stdio { command, .. } => {
                if command.trim().is_empty() {
                    errors.push("Command cannot be empty for stdio transport".to_string());
                }
            }
            TransportConfig::WebSocket { url, .. } => {
                if !self.is_valid_websocket_url(url) {
                    errors.push("Invalid WebSocket URL format".to_string());
                }
            }
            TransportConfig::Http { base_url, .. } => {
                if !self.is_valid_http_url(base_url) {
                    errors.push("Invalid HTTP URL format".to_string());
                }
            }
            TransportConfig::Tcp { host, port } => {
                if host.trim().is_empty() {
                    errors.push("Host cannot be empty for TCP transport".to_string());
                }
                if *port == 0 {
                    errors.push("Port must be greater than 0".to_string());
                }
            }
        }
        
        // Validate authentication configuration
        match &config.authentication.auth_type {
            AuthenticationType::ApiKey => {
                if config.authentication.credentials.is_empty() {
                    errors.push("API key cannot be empty".to_string());
                }
            }
            AuthenticationType::Bearer => {
                if config.authentication.credentials.is_empty() {
                    errors.push("Bearer token cannot be empty".to_string());
                }
            }
            AuthenticationType::Basic => {
                // Validate basic auth format
                if !self.is_valid_basic_auth(&config.authentication.credentials) {
                    errors.push("Invalid basic authentication format".to_string());
                }
            }
            _ => {}
        }
        
        if !errors.is_empty() {
            return Err(ValidationError::MultipleErrors(errors));
        }
        
        Ok(())
    }
    
    fn is_valid_websocket_url(&self, url: &str) -> bool {
        url.starts_with("ws://") || url.starts_with("wss://")
    }
    
    fn is_valid_http_url(&self, url: &str) -> bool {
        url.starts_with("http://") || url.starts_with("https://")
    }
    
    fn is_valid_basic_auth(&self, credentials: &str) -> bool {
        // Basic validation for base64 encoded username:password
        !credentials.is_empty() && credentials.contains(':')
    }
}

// MCP Connection Manager
pub struct MCPConnectionManager {
    active_connections: Arc<RwLock<HashMap<Uuid, MCPConnection>>>,
    connection_pool: Arc<ConnectionPool>,
}

#[derive(Debug)]
pub struct MCPConnection {
    pub server_id: Uuid,
    pub client: Box<dyn MCPClient>,
    pub connected_at: chrono::DateTime<chrono::Utc>,
    pub last_activity: chrono::DateTime<chrono::Utc>,
    pub health_status: ConnectionHealth,
}

#[derive(Debug)]
pub enum ConnectionHealth {
    Healthy,
    Degraded,
    Unhealthy,
    Unknown,
}

impl MCPConnectionManager {
    pub async fn connect_to_server(&self, config: &MCPServerConfig) -> Result<MCPConnection, ConnectionError> {
        // Create appropriate client based on transport type
        let client = self.create_client(&config.connection_config.transport).await?;
        
        // Establish connection
        client.connect().await?;
        
        // Create connection object
        let connection = MCPConnection {
            server_id: config.id,
            client,
            connected_at: chrono::Utc::now(),
            last_activity: chrono::Utc::now(),
            health_status: ConnectionHealth::Healthy,
        };
        
        // Store active connection
        {
            let mut connections = self.active_connections.write().await;
            connections.insert(config.id, connection);
        }
        
        Ok(connection)
    }
    
    pub async fn disconnect_server(&self, server_id: Uuid) -> Result<(), ConnectionError> {
        let mut connections = self.active_connections.write().await;
        
        if let Some(connection) = connections.remove(&server_id) {
            connection.client.disconnect().await?;
        }
        
        Ok(())
    }
    
    pub async fn test_connection(&self, config: &MCPServerConfig) -> Result<MCPServerInfo, ConnectionError> {
        let client = self.create_client(&config.connection_config.transport).await?;
        
        // Test connection
        client.connect().await?;
        
        // Get server information
        let server_info = client.get_server_info().await?;
        
        // Disconnect
        client.disconnect().await?;
        
        Ok(server_info)
    }
    
    async fn create_client(&self, transport: &TransportConfig) -> Result<Box<dyn MCPClient>, ConnectionError> {
        match transport {
            TransportConfig::Stdio { command, args, env, working_dir } => {
                Ok(Box::new(StdioMCPClient::new(
                    command.clone(),
                    args.clone(),
                    env.clone(),
                    working_dir.clone(),
                )))
            }
            TransportConfig::WebSocket { url, headers, protocols } => {
                Ok(Box::new(WebSocketMCPClient::new(
                    url.clone(),
                    headers.clone(),
                    protocols.clone(),
                )))
            }
            TransportConfig::Http { base_url, headers, tls_config } => {
                Ok(Box::new(HttpMCPClient::new(
                    base_url.clone(),
                    headers.clone(),
                    tls_config.clone(),
                )))
            }
            TransportConfig::Tcp { host, port, tls_config } => {
                Ok(Box::new(TcpMCPClient::new(
                    host.clone(),
                    *port,
                    tls_config.clone(),
                )))
            }
        }
    }
}
```

#### Frontend MCP Configuration Interface
```typescript
// MCP Configuration Management Interface
export interface MCPConfigurationManager {
  // Configuration management
  createMCPServer(config: CreateMCPServerRequest): Promise<MCPServerConfig>;
  updateMCPServer(serverId: string, updates: UpdateMCPServerRequest): Promise<MCPServerConfig>;
  deleteMCPServer(serverId: string): Promise<void>;
  getMCPServers(): Promise<MCPServerConfig[]>;
  getMCPServer(serverId: string): Promise<MCPServerConfig>;
  
  // Capability management
  discoverCapabilities(serverId: string): Promise<MCPCapability[]>;
  getServerCapabilities(serverId: string): Promise<MCPCapability[]>;
  
  // Connection testing
  testConnection(serverId: string): Promise<ConnectionTestResult>;
  
  // Real-time updates
  subscribeToConfigUpdates(callback: (update: MCPConfigUpdate) => void): () => void;
}

// React components for MCP configuration
export const MCPConfigurationDashboard: React.FC = () => {
  const [mcpServers, setMcpServers] = useState<MCPServerConfig[]>([]);
  const [selectedServer, setSelectedServer] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [capabilities, setCapabilities] = useState<Map<string, MCPCapability[]>>(new Map());
  
  useEffect(() => {
    loadMCPServers();
    
    // Subscribe to configuration updates
    const unsubscribe = mcpConfigManager.subscribeToConfigUpdates((update) => {
      switch (update.type) {
        case 'server_created':
          setMcpServers(prev => [...prev, update.server]);
          break;
        case 'server_updated':
          setMcpServers(prev => prev.map(s => 
            s.id === update.serverId ? update.server : s
          ));
          break;
        case 'server_deleted':
          setMcpServers(prev => prev.filter(s => s.id !== update.serverId));
          setCapabilities(prev => {
            const newMap = new Map(prev);
            newMap.delete(update.serverId);
            return newMap;
          });
          break;
        case 'capabilities_discovered':
          setCapabilities(prev => new Map(prev.set(update.serverId, update.capabilities)));
          break;
      }
    });
    
    return unsubscribe;
  }, []);
  
  const loadMCPServers = async () => {
    try {
      const servers = await mcpConfigManager.getMCPServers();
      setMcpServers(servers);
      
      // Load capabilities for each server
      for (const server of servers) {
        if (server.status === 'Connected') {
          const serverCapabilities = await mcpConfigManager.getServerCapabilities(server.id);
          setCapabilities(prev => new Map(prev.set(server.id, serverCapabilities)));
        }
      }
    } catch (error) {
      console.error('Failed to load MCP servers:', error);
    }
  };
  
  return (
    <div className="mcp-configuration-dashboard">
      <div className="dashboard-header">
        <h1>MCP Configuration</h1>
        <button
          onClick={() => setShowCreateForm(true)}
          className="create-server-btn"
        >
          Add MCP Server
        </button>
      </div>
      
      <div className="dashboard-content">
        <div className="servers-panel">
          <MCPServerList
            servers={mcpServers}
            selectedServer={selectedServer}
            onServerSelect={setSelectedServer}
            onServerAction={(serverId, action) => handleServerAction(serverId, action)}
          />
        </div>
        
        <div className="details-panel">
          {selectedServer && (
            <MCPServerDetails
              serverId={selectedServer}
              server={mcpServers.find(s => s.id === selectedServer)!}
              capabilities={capabilities.get(selectedServer) || []}
              onUpdate={(updates) => handleServerUpdate(selectedServer, updates)}
            />
          )}
        </div>
        
        <div className="capabilities-panel">
          <MCPCapabilitiesOverview
            servers={mcpServers}
            capabilities={capabilities}
          />
        </div>
      </div>
      
      {showCreateForm && (
        <CreateMCPServerModal
          onCreate={(config) => handleCreateServer(config)}
          onClose={() => setShowCreateForm(false)}
        />
      )}
    </div>
  );
};

export const MCPServerList: React.FC<{
  servers: MCPServerConfig[];
  selectedServer: string | null;
  onServerSelect: (serverId: string) => void;
  onServerAction: (serverId: string, action: string) => void;
}> = ({ servers, selectedServer, onServerSelect, onServerAction }) => {
  const getStatusColor = (status: MCPServerStatus) => {
    switch (status) {
      case 'Connected': return 'text-green-600 bg-green-100';
      case 'Connecting': return 'text-yellow-600 bg-yellow-100';
      case 'Disconnected': return 'text-gray-600 bg-gray-100';
      case 'Error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };
  
  const getServerTypeIcon = (serverType: MCPServerType) => {
    switch (serverType) {
      case 'Stdio': return '⚡';
      case 'WebSocket': return '🌐';
      case 'Http': return '🔗';
      case 'Tcp': return '🔌';
      default: return '📦';
    }
  };
  
  return (
    <div className="mcp-server-list">
      <h3>MCP Servers ({servers.length})</h3>
      
      <div className="server-items">
        {servers.map(server => (
          <div
            key={server.id}
            className={`server-item p-4 border rounded cursor-pointer ${
              selectedServer === server.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
            }`}
            onClick={() => onServerSelect(server.id)}
          >
            <div className="server-header flex justify-between items-start">
              <div className="server-info">
                <div className="server-name flex items-center space-x-2">
                  <span className="server-type-icon">{getServerTypeIcon(server.serverType)}</span>
                  <span className="font-medium">{server.name}</span>
                </div>
                <div className="server-description text-sm text-gray-600 mt-1">
                  {server.description}
                </div>
              </div>
              
              <div className="server-status">
                <span className={`status-badge px-2 py-1 text-xs rounded ${getStatusColor(server.status)}`}>
                  {server.status}
                </span>
              </div>
            </div>
            
            <div className="server-meta mt-3 flex items-center justify-between text-sm text-gray-500">
              <div className="server-stats">
                <span>{server.capabilities.length} capabilities</span>
                {server.lastSeen && (
                  <span className="ml-2">
                    Last seen: {formatRelativeTime(server.lastSeen)}
                  </span>
                )}
              </div>
              
              <div className="server-actions">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onServerAction(server.id, 'test');
                  }}
                  className="action-btn text-blue-600 hover:text-blue-800"
                >
                  Test
                </button>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onServerAction(server.id, 'discover');
                  }}
                  className="action-btn ml-2 text-green-600 hover:text-green-800"
                >
                  Discover
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const CreateMCPServerModal: React.FC<{
  onCreate: (config: CreateMCPServerRequest) => void;
  onClose: () => void;
}> = ({ onCreate, onClose }) => {
  const [formData, setFormData] = useState<CreateMCPServerRequest>({
    name: '',
    description: '',
    serverType: 'Stdio',
    connectionConfig: {
      endpoint: '',
      transport: {
        command: '',
        args: [],
        env: {},
        workingDir: null,
      },
      timeoutSeconds: 30,
      retryConfig: {
        maxAttempts: 3,
        backoffSeconds: 1,
      },
      connectionPool: {
        maxConnections: 10,
        minConnections: 1,
      },
    },
    authentication: {
      authType: 'None',
      credentials: '',
      tokenRefresh: null,
    },
    metadata: {},
  });
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isValidating, setIsValidating] = useState(false);
  
  const steps = [
    'Basic Information',
    'Connection Configuration',
    'Authentication',
    'Review & Create',
  ];
  
  const handleSubmit = async () => {
    setIsValidating(true);
    try {
      await onCreate(formData);
      onClose();
    } catch (error) {
      console.error('Failed to create MCP server:', error);
    } finally {
      setIsValidating(false);
    }
  };
  
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <BasicInformationStep
            data={formData}
            onChange={(updates) => setFormData(prev => ({ ...prev, ...updates }))}
          />
        );
      case 1:
        return (
          <ConnectionConfigurationStep
            data={formData}
            onChange={(updates) => setFormData(prev => ({ ...prev, ...updates }))}
          />
        );
      case 2:
        return (
          <AuthenticationConfigurationStep
            data={formData}
            onChange={(updates) => setFormData(prev => ({ ...prev, ...updates }))}
          />
        );
      case 3:
        return (
          <ReviewConfigurationStep
            data={formData}
            onChange={(updates) => setFormData(prev => ({ ...prev, ...updates }))}
          />
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="modal-content bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="modal-header">
          <h2 className="text-xl font-semibold">Create MCP Server</h2>
          <button onClick={onClose} className="close-btn">×</button>
        </div>
        
        <div className="step-progress mt-4">
          <div className="steps flex justify-between">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`step ${index <= currentStep ? 'active' : ''} ${
                  index === currentStep ? 'current' : ''
                }`}
              >
                <div className="step-number">{index + 1}</div>
                <div className="step-label">{step}</div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="step-content mt-6">
          {renderStepContent()}
        </div>
        
        <div className="modal-actions mt-6 flex justify-between">
          <button
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
            className="prev-btn"
          >
            Previous
          </button>
          
          <div className="next-actions">
            {currentStep < steps.length - 1 ? (
              <button
                onClick={() => setCurrentStep(currentStep + 1)}
                className="next-btn"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isValidating}
                className="create-btn"
              >
                {isValidating ? 'Creating...' : 'Create Server'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
```

### Performance Requirements

#### Configuration Performance
- **Configuration Loading**: <1 second for configuration retrieval
- **Capability Discovery**: <5 seconds for server capability discovery
- **Connection Testing**: <3 seconds for connection validation
- **Real-time Updates**: <500ms for configuration change propagation

#### Scalability Requirements
- **MCP Servers**: Support 100+ configured MCP servers
- **Capability Volume**: Handle 10,000+ total capabilities across servers
- **Concurrent Connections**: Maintain 50+ simultaneous MCP connections
- **Configuration Changes**: Process 1000+ configuration updates per hour

### Security Requirements

#### Configuration Security
- ✅ Encrypted storage of sensitive configuration data
- ✅ Secure credential management and rotation
- ✅ Role-based access control for configuration management
- ✅ Audit trail for all configuration changes

#### Communication Security
- ✅ Secure MCP protocol communication
- ✅ Certificate validation for TLS connections
- ✅ Authentication token management
- ✅ Protection against configuration injection attacks

## Quality Gates

### Definition of Done

#### Configuration Management
- ✅ MCP servers can be created, updated, and deleted successfully
- ✅ Dynamic capability discovery works for all transport types
- ✅ Configuration validation prevents invalid configurations
- ✅ Real-time updates reflect configuration changes

#### Security Validation
- ✅ Sensitive data is properly encrypted and protected
- ✅ Authentication mechanisms work correctly
- ✅ Access controls enforce proper permissions
- ✅ Audit trail captures all configuration activities

#### Performance Validation
- ✅ Configuration operations meet performance targets
- ✅ Capability discovery completes within time limits
- ✅ Connection testing provides timely feedback
- ✅ System scales to required server volumes

### Testing Requirements

#### Unit Tests
- Configuration validation logic
- Encryption and credential management
- MCP client implementations
- Capability discovery algorithms

#### Integration Tests
- End-to-end MCP server configuration workflow
- Multi-transport type support
- Real-time configuration updates
- Security and access control

#### Performance Tests
- Large-scale configuration management
- Concurrent capability discovery
- High-volume configuration changes
- Connection pool management

## Implementation Timeline

### Week 1: Core Configuration
- **Days 1-2**: MCP configuration data models and storage system
- **Days 3-4**: Basic configuration CRUD operations and validation
- **Day 5**: Initial capability discovery implementation

### Week 2: Advanced Features
- **Days 1-2**: Multi-transport support and connection management
- **Days 3-4**: Frontend configuration interface and dashboard
- **Day 5**: Testing, security hardening, and documentation

## Follow-up Stories

### Immediate Next Stories
- **O3.2a**: Tool Integration Framework (uses MCP configurations for tool access)
- **O3.3a**: Service Discovery (builds on configuration management)
- **O3.4a**: Performance Optimization (optimizes MCP server performance)

### Future Enhancements
- **Configuration Templates**: Pre-built configurations for common MCP servers
- **Bulk Configuration**: Import/export and bulk management capabilities
- **Health Monitoring**: Comprehensive MCP server health and performance monitoring
- **Auto-Discovery**: Automatic discovery of MCP servers on the network

This comprehensive MCP configuration management system provides the foundation for integrating Model Context Protocol servers into the agent orchestration platform, enabling agents to access and utilize external tools and resources.