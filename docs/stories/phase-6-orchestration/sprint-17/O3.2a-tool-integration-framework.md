# Story O3.2a: Tool Integration Framework

## Story Overview

**Epic**: O3 - MCP Integration  
**Story ID**: O3.2a  
**Title**: Tool Integration Framework for Agent-MCP Tool Coordination  
**Priority**: Critical  
**Effort**: 8 story points  
**Sprint**: Sprint 17 (Week 33-34)  
**Phase**: Agent Orchestration  

## Dependencies

### Prerequisites
- ✅ F1.1a: Monorepo Architecture (Completed in Phase 1)
- ✅ F2.1a: Multi-Provider AI System (Completed in Phase 1)
- ✅ O1.1a: Agent Registry & Discovery
- ✅ O1.2a: Agent Communication Protocol
- ✅ O1.3a: Task Distribution System
- ✅ O3.1a: MCP Configuration Management

### Enables
- O3.3a: Service Discovery
- O3.4a: Performance Optimization
- O4.2a: Decision Making Engine
- All agent tool execution capabilities

### Blocks Until Complete
- Agent access to MCP tools and resources
- Dynamic tool execution and result handling
- Tool capability matching and validation

## Sub-Agent Assignments

### Primary Agent: BE (Backend Agent)
**Responsibilities**:
- Implement tool integration engine
- Design tool execution orchestration
- Create tool result processing system
- Implement tool performance monitoring

**Deliverables**:
- Tool integration engine
- Tool execution orchestrator
- Result processing system
- Performance monitoring infrastructure

### Supporting Agent: AI (AI Integration Agent)
**Responsibilities**:
- Design intelligent tool selection algorithms
- Implement tool capability matching
- Create tool execution optimization
- Develop tool recommendation engine

**Deliverables**:
- AI-powered tool selection
- Capability matching algorithms
- Execution optimization system
- Tool recommendation engine

### Supporting Agent: FE (Frontend Agent)
**Responsibilities**:
- Create tool integration monitoring interface
- Design tool execution visualization
- Implement tool management dashboard
- Build tool performance analytics

**Deliverables**:
- Tool integration dashboard
- Execution visualization interface
- Tool management UI
- Performance analytics display

## Acceptance Criteria

### Functional Requirements

#### O3.2a.1: Dynamic Tool Discovery and Integration
**GIVEN** a need for flexible tool integration
**WHEN** integrating MCP tools with agents
**THEN** it should:
- ✅ Automatically discover available tools from configured MCP servers
- ✅ Dynamically register and update tool capabilities in agent registry
- ✅ Support tool capability filtering and searching by agents
- ✅ Handle tool versioning and compatibility validation
- ✅ Enable hot-swapping of tool implementations without agent restart

#### O3.2a.2: Tool Execution Orchestration
**GIVEN** requirements for seamless tool execution
**WHEN** agents execute tools through the framework
**THEN** it should:
- ✅ Route tool execution requests to appropriate MCP servers
- ✅ Handle tool parameter validation and transformation
- ✅ Support both synchronous and asynchronous tool execution
- ✅ Manage tool execution timeouts and error handling
- ✅ Provide tool execution result caching and optimization

#### O3.2a.3: Tool Performance and Monitoring
**GIVEN** need for tool execution visibility and optimization
**WHEN** monitoring tool usage and performance
**THEN** it should:
- ✅ Track tool execution metrics and performance statistics
- ✅ Monitor tool availability and health status
- ✅ Provide tool usage analytics and optimization recommendations
- ✅ Support tool execution debugging and troubleshooting
- ✅ Generate alerts for tool failures and performance degradation

### Technical Requirements

#### Tool Integration Engine
```rust
// Core tool integration system
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::{RwLock, Mutex};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ToolDefinition {
    pub id: Uuid,
    pub name: String,
    pub version: String,
    pub description: String,
    pub server_id: Uuid,
    pub tool_type: ToolType,
    pub input_schema: serde_json::Value,
    pub output_schema: serde_json::Value,
    pub parameters: Vec<ToolParameter>,
    pub capabilities: Vec<String>,
    pub metadata: ToolMetadata,
    pub performance_hints: PerformanceHints,
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ToolType {
    Function,
    Resource,
    Service,
    Prompt,
    Completion,
    Custom(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ToolParameter {
    pub name: String,
    pub parameter_type: String,
    pub required: bool,
    pub description: String,
    pub default_value: Option<serde_json::Value>,
    pub validation: ParameterValidation,
    pub examples: Vec<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ParameterValidation {
    pub min_length: Option<usize>,
    pub max_length: Option<usize>,
    pub pattern: Option<String>,
    pub enum_values: Option<Vec<serde_json::Value>>,
    pub custom_rules: Vec<ValidationRule>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ToolMetadata {
    pub tags: Vec<String>,
    pub category: String,
    pub documentation_url: Option<String>,
    pub examples: Vec<ToolExample>,
    pub rate_limits: Option<RateLimits>,
    pub cost_info: Option<CostInfo>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PerformanceHints {
    pub average_execution_time_ms: Option<u64>,
    pub max_execution_time_ms: Option<u64>,
    pub memory_usage_mb: Option<u64>,
    pub cpu_intensive: bool,
    pub network_bound: bool,
    pub cacheable: bool,
    pub cache_ttl_seconds: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ToolExecution {
    pub id: Uuid,
    pub tool_id: Uuid,
    pub agent_id: Uuid,
    pub parameters: serde_json::Value,
    pub execution_mode: ExecutionMode,
    pub context: ExecutionContext,
    pub status: ExecutionStatus,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub result: Option<ToolResult>,
    pub error: Option<ToolError>,
    pub performance_metrics: ExecutionMetrics,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ExecutionMode {
    Synchronous,
    Asynchronous,
    Streaming,
    Batch,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ExecutionStatus {
    Pending,
    Validating,
    Executing,
    Completed,
    Failed,
    Cancelled,
    Timeout,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ToolResult {
    pub content: serde_json::Value,
    pub metadata: ResultMetadata,
    pub artifacts: Vec<ResultArtifact>,
    pub cached: bool,
    pub cache_key: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ExecutionMetrics {
    pub execution_time_ms: u64,
    pub memory_usage_bytes: u64,
    pub network_requests: u32,
    pub cache_hits: u32,
    pub cache_misses: u32,
    pub error_count: u32,
}

pub struct ToolIntegrationFramework {
    tool_registry: Arc<ToolRegistry>,
    execution_engine: Arc<ToolExecutionEngine>,
    result_processor: Arc<ResultProcessor>,
    performance_monitor: Arc<ToolPerformanceMonitor>,
    cache_manager: Arc<ToolCacheManager>,
    mcp_manager: Arc<MCPConfigurationManager>,
}

impl ToolIntegrationFramework {
    pub fn new(
        mcp_manager: Arc<MCPConfigurationManager>,
        db: Arc<Database>,
    ) -> Self {
        let tool_registry = Arc::new(ToolRegistry::new(db.clone()));
        let cache_manager = Arc::new(ToolCacheManager::new(db.clone()));
        let performance_monitor = Arc::new(ToolPerformanceMonitor::new(db.clone()));
        let result_processor = Arc::new(ResultProcessor::new());
        let execution_engine = Arc::new(ToolExecutionEngine::new(
            mcp_manager.clone(),
            cache_manager.clone(),
            performance_monitor.clone(),
        ));
        
        Self {
            tool_registry,
            execution_engine,
            result_processor,
            performance_monitor,
            cache_manager,
            mcp_manager,
        }
    }
    
    pub async fn discover_and_register_tools(&self) -> Result<Vec<ToolDefinition>, ToolIntegrationError> {
        let mut discovered_tools = Vec::new();
        
        // Get all configured MCP servers
        let mcp_servers = self.mcp_manager.get_all_configurations().await?;
        
        for server in mcp_servers {
            if server.status == MCPServerStatus::Connected {
                match self.discover_server_tools(&server).await {
                    Ok(tools) => {
                        for tool in tools {
                            // Register tool in registry
                            self.tool_registry.register_tool(&tool).await?;
                            discovered_tools.push(tool);
                        }
                    }
                    Err(e) => {
                        eprintln!("Failed to discover tools from server {}: {:?}", server.name, e);
                    }
                }
            }
        }
        
        Ok(discovered_tools)
    }
    
    async fn discover_server_tools(&self, server: &MCPServerConfig) -> Result<Vec<ToolDefinition>, ToolIntegrationError> {
        let mut tools = Vec::new();
        
        // Connect to MCP server
        let connection = self.mcp_manager.get_connection(server.id).await?;
        
        // Discover tools
        let mcp_tools = connection.list_tools().await?;
        
        for mcp_tool in mcp_tools {
            let tool = ToolDefinition {
                id: Uuid::new_v4(),
                name: mcp_tool.name,
                version: mcp_tool.version.unwrap_or("1.0.0".to_string()),
                description: mcp_tool.description.unwrap_or_default(),
                server_id: server.id,
                tool_type: ToolType::Function,
                input_schema: mcp_tool.input_schema,
                output_schema: serde_json::json!({}), // MCP doesn't provide output schema
                parameters: self.parse_tool_parameters(&mcp_tool.input_schema)?,
                capabilities: vec!["execution".to_string()],
                metadata: ToolMetadata {
                    tags: vec!["mcp".to_string()],
                    category: "general".to_string(),
                    documentation_url: None,
                    examples: vec![],
                    rate_limits: None,
                    cost_info: None,
                },
                performance_hints: PerformanceHints {
                    average_execution_time_ms: None,
                    max_execution_time_ms: Some(30000), // 30 seconds default
                    memory_usage_mb: None,
                    cpu_intensive: false,
                    network_bound: true,
                    cacheable: false,
                    cache_ttl_seconds: None,
                },
                last_updated: chrono::Utc::now(),
            };
            
            tools.push(tool);
        }
        
        Ok(tools)
    }
    
    pub async fn execute_tool(
        &self,
        agent_id: Uuid,
        tool_name: &str,
        parameters: serde_json::Value,
        execution_mode: ExecutionMode,
    ) -> Result<ToolExecution, ToolIntegrationError> {
        // Find tool definition
        let tool = self.tool_registry.find_tool_by_name(tool_name).await?
            .ok_or(ToolIntegrationError::ToolNotFound(tool_name.to_string()))?;
        
        // Validate parameters
        self.validate_tool_parameters(&tool, &parameters).await?;
        
        // Create execution context
        let execution = ToolExecution {
            id: Uuid::new_v4(),
            tool_id: tool.id,
            agent_id,
            parameters,
            execution_mode,
            context: ExecutionContext {
                trace_id: Uuid::new_v4(),
                parent_execution_id: None,
                metadata: HashMap::new(),
            },
            status: ExecutionStatus::Pending,
            started_at: chrono::Utc::now(),
            completed_at: None,
            result: None,
            error: None,
            performance_metrics: ExecutionMetrics::default(),
        };
        
        // Execute tool
        let result = self.execution_engine.execute_tool(execution.clone()).await?;
        
        Ok(result)
    }
    
    pub async fn get_available_tools(
        &self,
        agent_id: Uuid,
        capabilities: &[String],
    ) -> Result<Vec<ToolDefinition>, ToolIntegrationError> {
        // Get agent permissions
        let agent_permissions = self.get_agent_tool_permissions(agent_id).await?;
        
        // Filter tools based on capabilities and permissions
        let all_tools = self.tool_registry.get_all_tools().await?;
        
        let filtered_tools = all_tools.into_iter()
            .filter(|tool| {
                // Check if agent has permission to use this tool
                agent_permissions.can_use_tool(&tool.name) &&
                // Check if tool capabilities match agent requirements
                capabilities.iter().any(|cap| tool.capabilities.contains(cap))
            })
            .collect();
        
        Ok(filtered_tools)
    }
    
    pub async fn get_tool_execution_history(
        &self,
        agent_id: Option<Uuid>,
        tool_id: Option<Uuid>,
        limit: Option<usize>,
    ) -> Result<Vec<ToolExecution>, ToolIntegrationError> {
        self.execution_engine.get_execution_history(agent_id, tool_id, limit).await
    }
    
    async fn validate_tool_parameters(
        &self,
        tool: &ToolDefinition,
        parameters: &serde_json::Value,
    ) -> Result<(), ToolIntegrationError> {
        // Validate against JSON schema
        let validator = jsonschema::JSONSchema::compile(&tool.input_schema)
            .map_err(|e| ToolIntegrationError::SchemaValidation(e.to_string()))?;
        
        if let Err(errors) = validator.validate(parameters) {
            let error_messages: Vec<String> = errors.map(|e| e.to_string()).collect();
            return Err(ToolIntegrationError::ParameterValidation(error_messages));
        }
        
        // Additional parameter validation
        for parameter in &tool.parameters {
            if parameter.required {
                if !parameters.as_object()
                    .map(|obj| obj.contains_key(&parameter.name))
                    .unwrap_or(false) {
                    return Err(ToolIntegrationError::MissingRequiredParameter(parameter.name.clone()));
                }
            }
            
            // Validate parameter-specific rules
            if let Some(value) = parameters.get(&parameter.name) {
                self.validate_parameter_value(parameter, value).await?;
            }
        }
        
        Ok(())
    }
    
    async fn validate_parameter_value(
        &self,
        parameter: &ToolParameter,
        value: &serde_json::Value,
    ) -> Result<(), ToolIntegrationError> {
        // String validation
        if let Some(string_value) = value.as_str() {
            if let Some(min_length) = parameter.validation.min_length {
                if string_value.len() < min_length {
                    return Err(ToolIntegrationError::ParameterValidation(vec![
                        format!("Parameter '{}' must be at least {} characters", parameter.name, min_length)
                    ]));
                }
            }
            
            if let Some(max_length) = parameter.validation.max_length {
                if string_value.len() > max_length {
                    return Err(ToolIntegrationError::ParameterValidation(vec![
                        format!("Parameter '{}' must be at most {} characters", parameter.name, max_length)
                    ]));
                }
            }
            
            if let Some(pattern) = &parameter.validation.pattern {
                let regex = regex::Regex::new(pattern)
                    .map_err(|e| ToolIntegrationError::ParameterValidation(vec![e.to_string()]))?;
                
                if !regex.is_match(string_value) {
                    return Err(ToolIntegrationError::ParameterValidation(vec![
                        format!("Parameter '{}' does not match required pattern", parameter.name)
                    ]));
                }
            }
        }
        
        // Enum validation
        if let Some(enum_values) = &parameter.validation.enum_values {
            if !enum_values.contains(value) {
                return Err(ToolIntegrationError::ParameterValidation(vec![
                    format!("Parameter '{}' must be one of the allowed values", parameter.name)
                ]));
            }
        }
        
        Ok(())
    }
}

// Tool execution engine
pub struct ToolExecutionEngine {
    mcp_manager: Arc<MCPConfigurationManager>,
    cache_manager: Arc<ToolCacheManager>,
    performance_monitor: Arc<ToolPerformanceMonitor>,
    active_executions: Arc<RwLock<HashMap<Uuid, ToolExecution>>>,
}

impl ToolExecutionEngine {
    pub async fn execute_tool(
        &self,
        mut execution: ToolExecution,
    ) -> Result<ToolExecution, ToolIntegrationError> {
        // Check cache first
        if let Some(cached_result) = self.check_cache(&execution).await? {
            execution.result = Some(cached_result);
            execution.status = ExecutionStatus::Completed;
            execution.completed_at = Some(chrono::Utc::now());
            return Ok(execution);
        }
        
        // Store active execution
        {
            let mut active = self.active_executions.write().await;
            active.insert(execution.id, execution.clone());
        }
        
        // Update status
        execution.status = ExecutionStatus::Validating;
        
        // Get tool definition
        let tool = self.get_tool_definition(execution.tool_id).await?;
        
        // Get MCP connection
        let connection = self.mcp_manager.get_connection(tool.server_id).await?;
        
        // Start performance monitoring
        let execution_start = std::time::Instant::now();
        
        // Update status
        execution.status = ExecutionStatus::Executing;
        
        // Execute tool based on mode
        let result = match execution.execution_mode {
            ExecutionMode::Synchronous => {
                self.execute_synchronous(&connection, &tool, &execution).await?
            }
            ExecutionMode::Asynchronous => {
                self.execute_asynchronous(&connection, &tool, &execution).await?
            }
            ExecutionMode::Streaming => {
                self.execute_streaming(&connection, &tool, &execution).await?
            }
            ExecutionMode::Batch => {
                self.execute_batch(&connection, &tool, &execution).await?
            }
        };
        
        // Calculate performance metrics
        let execution_duration = execution_start.elapsed();
        execution.performance_metrics.execution_time_ms = execution_duration.as_millis() as u64;
        
        // Update execution with result
        execution.result = Some(result.clone());
        execution.status = ExecutionStatus::Completed;
        execution.completed_at = Some(chrono::Utc::now());
        
        // Cache result if applicable
        if tool.performance_hints.cacheable {
            self.cache_manager.cache_result(&execution, &result).await?;
        }
        
        // Record performance metrics
        self.performance_monitor.record_execution(&execution).await?;
        
        // Remove from active executions
        {
            let mut active = self.active_executions.write().await;
            active.remove(&execution.id);
        }
        
        Ok(execution)
    }
    
    async fn execute_synchronous(
        &self,
        connection: &MCPConnection,
        tool: &ToolDefinition,
        execution: &ToolExecution,
    ) -> Result<ToolResult, ToolIntegrationError> {
        // Call MCP tool
        let mcp_result = connection.call_tool(&tool.name, &execution.parameters).await?;
        
        // Process result
        let processed_result = self.process_mcp_result(mcp_result)?;
        
        Ok(ToolResult {
            content: processed_result.content,
            metadata: ResultMetadata {
                execution_id: execution.id,
                tool_version: tool.version.clone(),
                timestamp: chrono::Utc::now(),
                processing_time_ms: 0, // TODO: measure processing time
            },
            artifacts: processed_result.artifacts,
            cached: false,
            cache_key: None,
        })
    }
    
    async fn execute_asynchronous(
        &self,
        connection: &MCPConnection,
        tool: &ToolDefinition,
        execution: &ToolExecution,
    ) -> Result<ToolResult, ToolIntegrationError> {
        // Start async execution
        let job_id = connection.start_async_tool(&tool.name, &execution.parameters).await?;
        
        // Poll for completion
        let mut attempts = 0;
        let max_attempts = 120; // 10 minutes with 5-second intervals
        
        loop {
            tokio::time::sleep(std::time::Duration::from_secs(5)).await;
            
            let status = connection.get_async_status(&job_id).await?;
            
            match status.state.as_str() {
                "completed" => {
                    let mcp_result = connection.get_async_result(&job_id).await?;
                    let processed_result = self.process_mcp_result(mcp_result)?;
                    
                    return Ok(ToolResult {
                        content: processed_result.content,
                        metadata: ResultMetadata {
                            execution_id: execution.id,
                            tool_version: tool.version.clone(),
                            timestamp: chrono::Utc::now(),
                            processing_time_ms: 0,
                        },
                        artifacts: processed_result.artifacts,
                        cached: false,
                        cache_key: None,
                    });
                }
                "failed" => {
                    return Err(ToolIntegrationError::ExecutionFailed(
                        status.error.unwrap_or("Unknown error".to_string())
                    ));
                }
                "running" | "pending" => {
                    attempts += 1;
                    if attempts >= max_attempts {
                        return Err(ToolIntegrationError::ExecutionTimeout);
                    }
                    continue;
                }
                _ => {
                    return Err(ToolIntegrationError::UnknownExecutionState(status.state));
                }
            }
        }
    }
    
    async fn check_cache(&self, execution: &ToolExecution) -> Result<Option<ToolResult>, ToolIntegrationError> {
        // Generate cache key
        let cache_key = self.generate_cache_key(execution.tool_id, &execution.parameters);
        
        // Check cache
        self.cache_manager.get_cached_result(&cache_key).await
    }
    
    fn generate_cache_key(&self, tool_id: Uuid, parameters: &serde_json::Value) -> String {
        use sha2::{Sha256, Digest};
        
        let mut hasher = Sha256::new();
        hasher.update(tool_id.as_bytes());
        hasher.update(serde_json::to_string(parameters).unwrap_or_default().as_bytes());
        
        format!("{:x}", hasher.finalize())
    }
}

// Tool cache manager
pub struct ToolCacheManager {
    cache_store: Arc<CacheStore>,
    cache_policies: HashMap<String, CachePolicy>,
}

#[derive(Debug, Clone)]
pub struct CachePolicy {
    pub ttl_seconds: u64,
    pub max_size_bytes: u64,
    pub eviction_strategy: EvictionStrategy,
}

#[derive(Debug, Clone)]
pub enum EvictionStrategy {
    LRU,
    LFU,
    TTL,
    Custom(String),
}

impl ToolCacheManager {
    pub async fn cache_result(
        &self,
        execution: &ToolExecution,
        result: &ToolResult,
    ) -> Result<(), CacheError> {
        let cache_key = self.generate_cache_key(execution.tool_id, &execution.parameters);
        
        // Get cache policy for tool
        let policy = self.get_cache_policy(execution.tool_id).await?;
        
        // Store in cache with TTL
        self.cache_store.set(
            &cache_key,
            result,
            std::time::Duration::from_secs(policy.ttl_seconds),
        ).await?;
        
        Ok(())
    }
    
    pub async fn get_cached_result(&self, cache_key: &str) -> Result<Option<ToolResult>, ToolIntegrationError> {
        match self.cache_store.get(cache_key).await {
            Ok(Some(mut result)) => {
                result.cached = true;
                result.cache_key = Some(cache_key.to_string());
                Ok(Some(result))
            }
            Ok(None) => Ok(None),
            Err(e) => Err(ToolIntegrationError::CacheError(e.to_string())),
        }
    }
}
```

#### Frontend Tool Integration Interface
```typescript
// Tool integration monitoring and management interface
export interface ToolIntegrationManager {
  // Tool discovery and management
  discoverTools(): Promise<ToolDefinition[]>;
  getAvailableTools(agentId: string, capabilities: string[]): Promise<ToolDefinition[]>;
  getToolDefinition(toolId: string): Promise<ToolDefinition>;
  
  // Tool execution
  executeTool(agentId: string, toolName: string, parameters: any, mode: ExecutionMode): Promise<ToolExecution>;
  getExecutionStatus(executionId: string): Promise<ToolExecution>;
  cancelExecution(executionId: string): Promise<void>;
  
  // Performance and monitoring
  getToolMetrics(toolId?: string, timeRange?: TimeRange): Promise<ToolMetrics>;
  getExecutionHistory(filters: ExecutionFilters): Promise<ToolExecution[]>;
  
  // Real-time updates
  subscribeToToolUpdates(callback: (update: ToolUpdate) => void): () => void;
  subscribeToExecutionUpdates(callback: (update: ExecutionUpdate) => void): () => void;
}

// React components for tool integration
export const ToolIntegrationDashboard: React.FC = () => {
  const [tools, setTools] = useState<ToolDefinition[]>([]);
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [executions, setExecutions] = useState<ToolExecution[]>([]);
  const [toolMetrics, setToolMetrics] = useState<Map<string, ToolMetrics>>(new Map());
  
  useEffect(() => {
    loadTools();
    loadExecutions();
    
    // Subscribe to real-time updates
    const unsubscribeTools = toolIntegrationManager.subscribeToToolUpdates((update) => {
      switch (update.type) {
        case 'tool_discovered':
          setTools(prev => [...prev, update.tool]);
          break;
        case 'tool_updated':
          setTools(prev => prev.map(t => 
            t.id === update.toolId ? update.tool : t
          ));
          break;
        case 'tool_removed':
          setTools(prev => prev.filter(t => t.id !== update.toolId));
          break;
      }
    });
    
    const unsubscribeExecutions = toolIntegrationManager.subscribeToExecutionUpdates((update) => {
      switch (update.type) {
        case 'execution_started':
          setExecutions(prev => [update.execution, ...prev]);
          break;
        case 'execution_completed':
        case 'execution_failed':
          setExecutions(prev => prev.map(e => 
            e.id === update.executionId ? update.execution : e
          ));
          break;
      }
    });
    
    return () => {
      unsubscribeTools();
      unsubscribeExecutions();
    };
  }, []);
  
  const loadTools = async () => {
    try {
      const discoveredTools = await toolIntegrationManager.discoverTools();
      setTools(discoveredTools);
      
      // Load metrics for each tool
      for (const tool of discoveredTools) {
        const metrics = await toolIntegrationManager.getToolMetrics(tool.id);
        setToolMetrics(prev => new Map(prev.set(tool.id, metrics)));
      }
    } catch (error) {
      console.error('Failed to load tools:', error);
    }
  };
  
  return (
    <div className="tool-integration-dashboard">
      <div className="dashboard-header">
        <h1>Tool Integration</h1>
        <ToolIntegrationOverview tools={tools} metrics={toolMetrics} />
      </div>
      
      <div className="dashboard-content">
        <div className="tools-panel">
          <ToolRegistry
            tools={tools}
            selectedTool={selectedTool}
            onToolSelect={setSelectedTool}
            onToolAction={(toolId, action) => handleToolAction(toolId, action)}
          />
        </div>
        
        <div className="execution-panel">
          <ToolExecutionMonitor
            executions={executions}
            selectedTool={selectedTool}
          />
        </div>
        
        <div className="details-panel">
          {selectedTool && (
            <ToolDetailsPanel
              toolId={selectedTool}
              tool={tools.find(t => t.id === selectedTool)!}
              metrics={toolMetrics.get(selectedTool)}
              onExecute={(parameters, mode) => 
                executeToolFromDashboard(selectedTool, parameters, mode)
              }
            />
          )}
        </div>
      </div>
    </div>
  );
};

export const ToolRegistry: React.FC<{
  tools: ToolDefinition[];
  selectedTool: string | null;
  onToolSelect: (toolId: string) => void;
  onToolAction: (toolId: string, action: string) => void;
}> = ({ tools, selectedTool, onToolSelect, onToolAction }) => {
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredTools = useMemo(() => {
    return tools.filter(tool => {
      if (filterCategory !== 'all' && tool.metadata.category !== filterCategory) {
        return false;
      }
      
      if (searchTerm && !tool.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !tool.description.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }
      
      return true;
    });
  }, [tools, filterCategory, searchTerm]);
  
  const categories = useMemo(() => {
    const cats = new Set(tools.map(t => t.metadata.category));
    return ['all', ...Array.from(cats)];
  }, [tools]);
  
  return (
    <div className="tool-registry">
      <div className="registry-header">
        <h3>Available Tools ({filteredTools.length})</h3>
        
        <div className="registry-filters">
          <input
            type="text"
            placeholder="Search tools..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="category-filter"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="tool-list">
        {filteredTools.map(tool => (
          <div
            key={tool.id}
            className={`tool-item p-4 border rounded cursor-pointer ${
              selectedTool === tool.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
            }`}
            onClick={() => onToolSelect(tool.id)}
          >
            <div className="tool-header">
              <div className="tool-name font-medium">{tool.name}</div>
              <div className="tool-version text-sm text-gray-500">v{tool.version}</div>
            </div>
            
            <div className="tool-description text-sm text-gray-600 mt-1">
              {tool.description}
            </div>
            
            <div className="tool-meta mt-2 flex items-center justify-between">
              <div className="tool-tags">
                {tool.metadata.tags.slice(0, 3).map(tag => (
                  <span key={tag} className="tag text-xs px-2 py-1 bg-gray-100 rounded mr-1">
                    {tag}
                  </span>
                ))}
              </div>
              
              <div className="tool-type text-xs text-gray-500">
                {tool.toolType}
              </div>
            </div>
            
            <div className="tool-performance mt-2">
              {tool.performanceHints.averageExecutionTimeMs && (
                <div className="performance-hint text-xs text-gray-500">
                  Avg: {tool.performanceHints.averageExecutionTimeMs}ms
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const ToolExecutionMonitor: React.FC<{
  executions: ToolExecution[];
  selectedTool: string | null;
}> = ({ executions, selectedTool }) => {
  const [statusFilter, setStatusFilter] = useState<ExecutionStatus | 'all'>('all');
  
  const filteredExecutions = useMemo(() => {
    return executions.filter(execution => {
      if (selectedTool && execution.toolId !== selectedTool) {
        return false;
      }
      
      if (statusFilter !== 'all' && execution.status !== statusFilter) {
        return false;
      }
      
      return true;
    });
  }, [executions, selectedTool, statusFilter]);
  
  const getStatusColor = (status: ExecutionStatus) => {
    switch (status) {
      case 'Completed': return 'text-green-600 bg-green-100';
      case 'Executing': return 'text-blue-600 bg-blue-100';
      case 'Failed': return 'text-red-600 bg-red-100';
      case 'Pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };
  
  return (
    <div className="tool-execution-monitor">
      <div className="monitor-header">
        <h3>Tool Executions</h3>
        
        <div className="execution-filters">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
          >
            <option value="all">All Statuses</option>
            <option value="Pending">Pending</option>
            <option value="Executing">Executing</option>
            <option value="Completed">Completed</option>
            <option value="Failed">Failed</option>
          </select>
        </div>
      </div>
      
      <div className="execution-list">
        {filteredExecutions.map(execution => (
          <div key={execution.id} className="execution-item p-3 border-b">
            <div className="execution-header flex justify-between items-start">
              <div className="execution-info">
                <div className="execution-id text-sm font-medium">
                  {execution.id.substring(0, 8)}...
                </div>
                <div className="tool-name text-xs text-gray-500">
                  Tool: {execution.toolId}
                </div>
              </div>
              
              <div className="execution-status">
                <span className={`status-badge px-2 py-1 text-xs rounded ${getStatusColor(execution.status)}`}>
                  {execution.status}
                </span>
              </div>
            </div>
            
            <div className="execution-timing text-xs text-gray-500 mt-2">
              Started: {formatTime(execution.startedAt)}
              {execution.completedAt && (
                <span className="ml-2">
                  Completed: {formatTime(execution.completedAt)}
                </span>
              )}
            </div>
            
            {execution.performanceMetrics.executionTimeMs > 0 && (
              <div className="execution-metrics text-xs text-gray-500 mt-1">
                Duration: {execution.performanceMetrics.executionTimeMs}ms
              </div>
            )}
            
            {execution.error && (
              <div className="execution-error text-xs text-red-600 mt-2">
                Error: {execution.error.message}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Performance Requirements

#### Tool Integration Performance
- **Tool Discovery**: <5 seconds for discovering all tools across MCP servers
- **Tool Execution**: <100ms overhead for tool routing and validation
- **Result Processing**: <200ms for result transformation and caching
- **Cache Retrieval**: <50ms for cached result lookup

#### Scalability Requirements
- **Tool Volume**: Support 1,000+ tools across all MCP servers
- **Concurrent Executions**: Handle 500+ simultaneous tool executions
- **Execution History**: Maintain 1 million+ execution records efficiently
- **Cache Management**: Support 10GB+ of cached tool results

### Security Requirements

#### Tool Execution Security
- ✅ Secure parameter validation and sanitization
- ✅ Tool access control based on agent permissions
- ✅ Encrypted communication with MCP servers
- ✅ Audit trail for all tool executions

#### Data Protection
- ✅ Secure handling of tool parameters and results
- ✅ Protection of sensitive tool outputs
- ✅ Secure caching with encryption
- ✅ Compliance with data retention policies

## Quality Gates

### Definition of Done

#### Integration Functionality
- ✅ Tools are automatically discovered from MCP servers
- ✅ Tool execution works across all supported modes
- ✅ Parameter validation prevents invalid executions
- ✅ Result caching improves performance for repeated operations

#### Performance Validation
- ✅ Tool integration meets performance targets
- ✅ Concurrent execution scales to required levels
- ✅ Cache system operates efficiently
- ✅ Tool discovery completes within time limits

#### Security Validation
- ✅ Tool access controls enforce proper permissions
- ✅ Parameter validation prevents injection attacks
- ✅ Secure communication protects sensitive data
- ✅ Audit trail captures all tool activities

### Testing Requirements

#### Unit Tests
- Tool discovery and registration logic
- Parameter validation algorithms
- Execution orchestration workflows
- Cache management operations

#### Integration Tests
- End-to-end tool execution scenarios
- Multi-MCP server tool coordination
- Performance monitoring and metrics collection
- Error handling and recovery

#### Performance Tests
- Large-scale tool discovery
- High-volume concurrent executions
- Cache performance under load
- Tool execution latency measurement

## Implementation Timeline

### Week 1: Core Integration
- **Days 1-2**: Tool discovery and registration system
- **Days 3-4**: Basic tool execution engine and parameter validation
- **Day 5**: Tool caching and performance monitoring

### Week 2: Advanced Features
- **Days 1-2**: Multi-mode execution support and result processing
- **Days 3-4**: Frontend tool integration dashboard and monitoring
- **Day 5**: Testing, optimization, and documentation

## Follow-up Stories

### Immediate Next Stories
- **O3.3a**: Service Discovery (builds on tool integration for dynamic service location)
- **O3.4a**: Performance Optimization (optimizes tool execution performance)
- **O4.2a**: Decision Making Engine (uses tool integration for decision support)

### Future Enhancements
- **Tool Composition**: Chaining multiple tools into complex workflows
- **Smart Caching**: ML-based cache optimization and prefetching
- **Tool Marketplace**: Community-driven tool sharing and discovery
- **Custom Tool Development**: Framework for creating custom MCP tools

This comprehensive tool integration framework enables agents to seamlessly access and execute tools from MCP servers, providing the foundation for sophisticated agent capabilities and tool-assisted workflows.