# Story V1.1b: Advanced Canvas Operations

## Story Overview

**Epic**: V1 - Canvas Graphics Engine  
**Story ID**: V1.1b  
**Title**: Advanced Canvas Operations with Professional Design Tools  
**Priority**: High  
**Effort**: 9 story points  
**Sprint**: Sprint 10 (Week 19-20)  
**Phase**: Visual Design Studio  

## Dependencies

### Prerequisites
- ✅ V1.1a: Fabric.js Integration (Completed in Sprint 9)
- ✅ V1.2a: PIXI.js Performance Layer (Completed in Sprint 9)
- ✅ V1.3a: Layer Management System (Completed in Sprint 9)
- ✅ V1.4a: Canvas State Management (Parallel development in Sprint 10)

### Enables
- V3.1a: Vector Graphics Tools
- V3.2a: Filter & Effects Pipeline
- V4.1a: Component Library
- Professional design workflows

### Blocks Until Complete
- Advanced object manipulation and transformation
- Professional alignment and distribution tools
- Complex selection and grouping operations
- Multi-object editing capabilities

## Sub-Agent Assignments

### Primary Agent: FE (Frontend Agent)
**Responsibilities**:
- Implement advanced object manipulation and transformation tools
- Design professional alignment, distribution, and spacing systems
- Create advanced selection tools (lasso, magic wand, etc.)
- Implement multi-object editing and bulk operations

**Deliverables**:
- Advanced transformation and manipulation tools
- Professional alignment and distribution system
- Advanced selection tools and interfaces
- Multi-object editing capabilities

### Supporting Agent: ARCH (Architecture Agent)
**Responsibilities**:
- Design extensible operation framework for complex canvas operations
- Plan performance optimization for multi-object operations
- Create operation pipeline and command pattern architecture
- Define operation history and undo/redo integration

**Deliverables**:
- Advanced operation framework documentation
- Performance optimization strategies
- Command pattern implementation
- Operation history integration design

### Supporting Agent: UI (UI/UX Agent)
**Responsibilities**:
- Design intuitive interfaces for complex operations
- Create visual feedback systems for operations
- Implement contextual menus and operation shortcuts
- Design operation preview and confirmation systems

**Deliverables**:
- Advanced operation UI components
- Visual feedback and preview systems
- Contextual interaction patterns
- Operation confirmation interfaces

## Acceptance Criteria

### Functional Requirements

#### V1.1b.1: Advanced Object Transformation
**GIVEN** complex design requirements requiring precise object manipulation
**WHEN** users perform advanced transformation operations
**THEN** it should:
- ✅ Support precise numerical transformation with input fields
- ✅ Enable free transform with corner, edge, and center handles
- ✅ Provide skew, perspective, and 3D rotation transforms
- ✅ Support transform origin point adjustment
- ✅ Enable relative and absolute transformation modes

#### V1.1b.2: Professional Alignment and Distribution
**GIVEN** multiple objects requiring precise positioning
**WHEN** aligning and distributing objects on the canvas
**THEN** it should:
- ✅ Align objects to each other (left, center, right, top, middle, bottom)
- ✅ Align objects to canvas or selection bounds
- ✅ Distribute objects evenly by spacing or centers
- ✅ Support smart guides and snapping during alignment
- ✅ Enable batch alignment operations for efficiency

#### V1.1b.3: Advanced Selection Systems
**GIVEN** complex canvas compositions requiring sophisticated selection
**WHEN** selecting objects using various selection methods
**THEN** it should:
- ✅ Support lasso selection for irregular shapes
- ✅ Provide magic wand selection based on color/properties
- ✅ Enable selection by layer, type, or attributes
- ✅ Support additive and subtractive selection modes
- ✅ Provide selection preview and refinement tools

### Technical Requirements

#### Advanced Operations Architecture
```typescript
interface CanvasOperation {
  id: string;
  type: OperationType;
  targets: string[]; // Object IDs
  parameters: OperationParameters;
  preview?: boolean;
  batch?: boolean;
  undoable: boolean;
  timestamp: number;
}

interface TransformOperation extends CanvasOperation {
  type: 'transform';
  parameters: {
    mode: TransformMode;
    values: TransformValues;
    origin: Point;
    constrainProportions: boolean;
    snapToGrid: boolean;
    snapToObjects: boolean;
  };
}

interface AlignmentOperation extends CanvasOperation {
  type: 'alignment';
  parameters: {
    alignmentType: AlignmentType;
    distributionType?: DistributionType;
    referenceTarget: 'canvas' | 'selection' | 'object';
    referenceObjectId?: string;
    spacing?: number;
  };
}

interface SelectionOperation extends CanvasOperation {
  type: 'selection';
  parameters: {
    selectionMode: SelectionMode;
    criteria: SelectionCriteria;
    additive: boolean;
    area?: SelectionArea;
  };
}

enum TransformMode {
  FREE_TRANSFORM = 'free_transform',
  SCALE = 'scale',
  ROTATE = 'rotate',
  SKEW = 'skew',
  PERSPECTIVE = 'perspective',
  TRANSLATE = 'translate',
  NUMERIC = 'numeric'
}

enum AlignmentType {
  LEFT = 'left',
  CENTER_HORIZONTAL = 'center_horizontal',
  RIGHT = 'right',
  TOP = 'top',
  CENTER_VERTICAL = 'center_vertical',
  BOTTOM = 'bottom',
  CENTER_BOTH = 'center_both'
}

enum DistributionType {
  HORIZONTAL_CENTERS = 'horizontal_centers',
  HORIZONTAL_SPACING = 'horizontal_spacing',
  VERTICAL_CENTERS = 'vertical_centers',
  VERTICAL_SPACING = 'vertical_spacing'
}

enum SelectionMode {
  RECTANGLE = 'rectangle',
  LASSO = 'lasso',
  MAGIC_WAND = 'magic_wand',
  BY_ATTRIBUTE = 'by_attribute',
  BY_LAYER = 'by_layer',
  BY_TYPE = 'by_type'
}
```

#### Advanced Canvas Operations Manager
```typescript
// Comprehensive canvas operations management system
export class AdvancedCanvasOperationsManager {
  private canvas: fabric.Canvas;
  private operationHistory: OperationHistory;
  private transformationEngine: TransformationEngine;
  private alignmentEngine: AlignmentEngine;
  private selectionEngine: SelectionEngine;
  private snapManager: SnapManager;
  private guidesManager: GuidesManager;
  private operationPreview: OperationPreview;

  constructor(canvas: fabric.Canvas, options: OperationsManagerOptions) {
    this.canvas = canvas;
    this.initializeEngines();
    this.setupEventHandlers();
    this.initializeKeyboardShortcuts();
  }

  // Advanced object transformation
  async executeTransformation(operation: TransformOperation): Promise<TransformationResult> {
    try {
      // Validate operation
      this.validateTransformOperation(operation);
      
      // Get target objects
      const targetObjects = this.getTargetObjects(operation.targets);
      if (targetObjects.length === 0) {
        return { success: false, reason: 'No valid targets found' };
      }

      // Show preview if requested
      if (operation.preview) {
        return this.previewTransformation(operation, targetObjects);
      }

      // Execute transformation
      const result = await this.transformationEngine.execute(operation, targetObjects);
      
      // Update canvas and history
      this.canvas.requestRenderAll();
      this.operationHistory.addOperation(operation);
      
      return {
        success: true,
        transformedObjects: result.transformedObjects,
        operationId: operation.id,
        metadata: result.metadata
      };
    } catch (error) {
      return {
        success: false,
        reason: error.message,
        operation: operation
      };
    }
  }

  // Free transform with advanced handles
  enableFreeTransform(objectIds: string[]): FreeTransformController {
    const objects = this.getTargetObjects(objectIds);
    const selectionBounds = this.calculateSelectionBounds(objects);
    
    const controller = new FreeTransformController({
      objects: objects,
      bounds: selectionBounds,
      canvas: this.canvas,
      onTransform: (transform: TransformValues) => {
        this.applyFreeTransform(objects, transform);
      },
      onComplete: (finalTransform: TransformValues) => {
        this.completeFreeTransform(objects, finalTransform);
      },
      onCancel: () => {
        this.cancelFreeTransform(objects);
      }
    });

    return controller;
  }

  private applyFreeTransform(objects: fabric.Object[], transform: TransformValues): void {
    objects.forEach(obj => {
      // Apply transformation relative to selection center
      const relativeTransform = this.calculateRelativeTransform(obj, transform);
      
      obj.set({
        left: relativeTransform.left,
        top: relativeTransform.top,
        scaleX: relativeTransform.scaleX,
        scaleY: relativeTransform.scaleY,
        angle: relativeTransform.angle,
        skewX: relativeTransform.skewX,
        skewY: relativeTransform.skewY
      });
      
      obj.setCoords();
    });
    
    this.canvas.requestRenderAll();
  }

  // Numerical transformation with precision
  async executeNumericalTransform(
    objectIds: string[],
    transformData: NumericalTransformData
  ): Promise<TransformationResult> {
    const objects = this.getTargetObjects(objectIds);
    
    try {
      for (const obj of objects) {
        // Apply numerical transformations
        if (transformData.position) {
          obj.set({
            left: transformData.position.x,
            top: transformData.position.y
          });
        }
        
        if (transformData.size) {
          const scaleX = transformData.size.width / obj.width!;
          const scaleY = transformData.size.height / obj.height!;
          
          if (transformData.constrainProportions) {
            const uniformScale = Math.min(scaleX, scaleY);
            obj.set({ scaleX: uniformScale, scaleY: uniformScale });
          } else {
            obj.set({ scaleX, scaleY });
          }
        }
        
        if (transformData.rotation !== undefined) {
          obj.set({ angle: transformData.rotation });
        }
        
        if (transformData.skew) {
          obj.set({
            skewX: transformData.skew.x,
            skewY: transformData.skew.y
          });
        }
        
        obj.setCoords();
      }
      
      // Record operation
      const operation: TransformOperation = {
        id: generateUniqueId(),
        type: 'transform',
        targets: objectIds,
        parameters: {
          mode: TransformMode.NUMERIC,
          values: transformData,
          origin: { x: 0, y: 0 },
          constrainProportions: transformData.constrainProportions || false,
          snapToGrid: false,
          snapToObjects: false
        },
        undoable: true,
        timestamp: Date.now()
      };
      
      this.operationHistory.addOperation(operation);
      this.canvas.requestRenderAll();
      
      return {
        success: true,
        transformedObjects: objects,
        operationId: operation.id
      };
    } catch (error) {
      return {
        success: false,
        reason: error.message
      };
    }
  }

  // Professional alignment operations
  async alignObjects(operation: AlignmentOperation): Promise<AlignmentResult> {
    try {
      const targetObjects = this.getTargetObjects(operation.targets);
      if (targetObjects.length < 2 && operation.parameters.referenceTarget !== 'canvas') {
        return { success: false, reason: 'At least 2 objects required for object alignment' };
      }

      const result = await this.alignmentEngine.execute(operation, targetObjects);
      
      this.canvas.requestRenderAll();
      this.operationHistory.addOperation(operation);
      
      return {
        success: true,
        alignedObjects: result.alignedObjects,
        alignmentType: operation.parameters.alignmentType,
        operationId: operation.id
      };
    } catch (error) {
      return {
        success: false,
        reason: error.message,
        operation: operation
      };
    }
  }

  // Distribution operations
  async distributeObjects(
    objectIds: string[],
    distributionType: DistributionType,
    spacing?: number
  ): Promise<DistributionResult> {
    const objects = this.getTargetObjects(objectIds);
    
    if (objects.length < 3) {
      return { success: false, reason: 'At least 3 objects required for distribution' };
    }

    try {
      const result = await this.alignmentEngine.distribute(
        objects,
        distributionType,
        spacing
      );
      
      this.canvas.requestRenderAll();
      
      // Record operation
      const operation: AlignmentOperation = {
        id: generateUniqueId(),
        type: 'alignment',
        targets: objectIds,
        parameters: {
          alignmentType: AlignmentType.CENTER_BOTH, // Not used for distribution
          distributionType: distributionType,
          referenceTarget: 'selection',
          spacing: spacing
        },
        undoable: true,
        timestamp: Date.now()
      };
      
      this.operationHistory.addOperation(operation);
      
      return {
        success: true,
        distributedObjects: result.distributedObjects,
        distributionType: distributionType,
        operationId: operation.id
      };
    } catch (error) {
      return {
        success: false,
        reason: error.message
      };
    }
  }

  // Advanced selection operations
  async executeSelection(operation: SelectionOperation): Promise<SelectionResult> {
    try {
      let selectedObjects: fabric.Object[] = [];
      
      switch (operation.parameters.selectionMode) {
        case SelectionMode.LASSO:
          selectedObjects = await this.executeSelectionLasso(operation);
          break;
        case SelectionMode.MAGIC_WAND:
          selectedObjects = await this.executeSelectionMagicWand(operation);
          break;
        case SelectionMode.BY_ATTRIBUTE:
          selectedObjects = await this.executeSelectionByAttribute(operation);
          break;
        case SelectionMode.BY_LAYER:
          selectedObjects = await this.executeSelectionByLayer(operation);
          break;
        case SelectionMode.BY_TYPE:
          selectedObjects = await this.executeSelectionByType(operation);
          break;
        default:
          return { success: false, reason: 'Unsupported selection mode' };
      }

      // Apply selection
      if (operation.parameters.additive) {
        this.addToSelection(selectedObjects);
      } else {
        this.setSelection(selectedObjects);
      }
      
      return {
        success: true,
        selectedObjects: selectedObjects,
        selectionMode: operation.parameters.selectionMode,
        operationId: operation.id
      };
    } catch (error) {
      return {
        success: false,
        reason: error.message,
        operation: operation
      };
    }
  }

  private async executeSelectionLasso(operation: SelectionOperation): Promise<fabric.Object[]> {
    const lassoPath = operation.parameters.area!.path;
    const selectedObjects: fabric.Object[] = [];
    
    this.canvas.forEachObject((obj) => {
      if (this.isObjectInLassoPath(obj, lassoPath)) {
        selectedObjects.push(obj);
      }
    });
    
    return selectedObjects;
  }

  private async executeSelectionMagicWand(operation: SelectionOperation): Promise<fabric.Object[]> {
    const criteria = operation.parameters.criteria;
    const tolerance = criteria.tolerance || 0.1;
    const selectedObjects: fabric.Object[] = [];
    
    this.canvas.forEachObject((obj) => {
      if (this.matchesMagicWandCriteria(obj, criteria, tolerance)) {
        selectedObjects.push(obj);
      }
    });
    
    return selectedObjects;
  }

  private async executeSelectionByAttribute(operation: SelectionOperation): Promise<fabric.Object[]> {
    const criteria = operation.parameters.criteria;
    const selectedObjects: fabric.Object[] = [];
    
    this.canvas.forEachObject((obj) => {
      if (this.matchesAttributeCriteria(obj, criteria)) {
        selectedObjects.push(obj);
      }
    });
    
    return selectedObjects;
  }

  private matchesAttributeCriteria(obj: fabric.Object, criteria: SelectionCriteria): boolean {
    // Check fill color
    if (criteria.fillColor && obj.fill !== criteria.fillColor) {
      return false;
    }
    
    // Check stroke color
    if (criteria.strokeColor && obj.stroke !== criteria.strokeColor) {
      return false;
    }
    
    // Check opacity
    if (criteria.opacity !== undefined && Math.abs((obj.opacity || 1) - criteria.opacity) > 0.05) {
      return false;
    }
    
    // Check size range
    if (criteria.sizeRange) {
      const objSize = (obj.width || 0) * (obj.height || 0);
      if (objSize < criteria.sizeRange.min || objSize > criteria.sizeRange.max) {
        return false;
      }
    }
    
    return true;
  }

  // Batch operations for multiple objects
  async executeBatchOperation(
    operationType: string,
    objectIds: string[],
    parameters: any
  ): Promise<BatchOperationResult> {
    const results: OperationResult[] = [];
    const errors: BatchOperationError[] = [];
    
    try {
      for (const objectId of objectIds) {
        try {
          let result: OperationResult;
          
          switch (operationType) {
            case 'transform':
              result = await this.executeTransformation({
                id: generateUniqueId(),
                type: 'transform',
                targets: [objectId],
                parameters: parameters,
                undoable: true,
                timestamp: Date.now()
              });
              break;
            case 'style':
              result = await this.applyStyle(objectId, parameters);
              break;
            case 'duplicate':
              result = await this.duplicateObject(objectId, parameters);
              break;
            default:
              throw new Error(`Unsupported batch operation: ${operationType}`);
          }
          
          results.push(result);
        } catch (error) {
          errors.push({
            objectId: objectId,
            error: error.message,
            operation: operationType
          });
        }
      }
      
      this.canvas.requestRenderAll();
      
      return {
        success: true,
        totalProcessed: results.length,
        totalErrors: errors.length,
        results: results,
        errors: errors
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        results: results,
        errors: errors
      };
    }
  }

  // Smart guides and snapping
  enableSmartGuides(options: SmartGuidesOptions = {}): void {
    this.guidesManager.enable({
      snapToObjects: options.snapToObjects !== false,
      snapToGrid: options.snapToGrid || false,
      snapTolerance: options.snapTolerance || 10,
      showDistanceGuides: options.showDistanceGuides !== false,
      showAlignmentGuides: options.showAlignmentGuides !== false,
      showCenterGuides: options.showCenterGuides !== false
    });
  }

  private setupSmartSnapping(): void {
    this.canvas.on('object:moving', (e) => {
      const obj = e.target!;
      const snapResult = this.snapManager.calculateSnap(obj, {
        snapToObjects: true,
        snapToGrid: false,
        tolerance: 10
      });
      
      if (snapResult.snapped) {
        obj.set({
          left: snapResult.position.x,
          top: snapResult.position.y
        });
        
        // Show snap guides
        this.guidesManager.showSnapGuides(snapResult.guides);
      }
    });
    
    this.canvas.on('object:moved', () => {
      this.guidesManager.hideSnapGuides();
    });
  }

  // Multi-object editing
  enableMultiObjectEdit(objectIds: string[]): MultiObjectEditor {
    const objects = this.getTargetObjects(objectIds);
    
    return new MultiObjectEditor({
      objects: objects,
      canvas: this.canvas,
      onPropertyChange: (property: string, value: any) => {
        this.applyPropertyToMultipleObjects(objects, property, value);
      },
      onBulkOperation: (operation: string, parameters: any) => {
        this.executeBatchOperation(operation, objectIds, parameters);
      }
    });
  }

  private applyPropertyToMultipleObjects(
    objects: fabric.Object[],
    property: string,
    value: any
  ): void {
    const operation: CanvasOperation = {
      id: generateUniqueId(),
      type: 'property_change',
      targets: objects.map(obj => obj.id!),
      parameters: { property, value },
      undoable: true,
      timestamp: Date.now()
    };
    
    objects.forEach(obj => {
      obj.set(property, value);
      obj.setCoords();
    });
    
    this.canvas.requestRenderAll();
    this.operationHistory.addOperation(operation);
  }
}
```

#### Transformation Engine Implementation
```typescript
// Advanced transformation engine with mathematical precision
export class TransformationEngine {
  private canvas: fabric.Canvas;
  private transformationMatrix: Matrix3x3;
  private transformationHistory: TransformationRecord[];

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.transformationMatrix = new Matrix3x3();
    this.transformationHistory = [];
  }

  async execute(
    operation: TransformOperation,
    targetObjects: fabric.Object[]
  ): Promise<TransformationExecutionResult> {
    const results: ObjectTransformationResult[] = [];
    
    for (const obj of targetObjects) {
      const result = await this.transformObject(obj, operation);
      results.push(result);
    }
    
    return {
      transformedObjects: results.map(r => r.object),
      results: results,
      metadata: {
        operationId: operation.id,
        transformationType: operation.parameters.mode,
        executionTime: Date.now()
      }
    };
  }

  private async transformObject(
    obj: fabric.Object,
    operation: TransformOperation
  ): Promise<ObjectTransformationResult> {
    const originalState = this.captureObjectState(obj);
    
    try {
      switch (operation.parameters.mode) {
        case TransformMode.SCALE:
          await this.applyScale(obj, operation.parameters);
          break;
        case TransformMode.ROTATE:
          await this.applyRotation(obj, operation.parameters);
          break;
        case TransformMode.SKEW:
          await this.applySkew(obj, operation.parameters);
          break;
        case TransformMode.PERSPECTIVE:
          await this.applyPerspective(obj, operation.parameters);
          break;
        case TransformMode.TRANSLATE:
          await this.applyTranslation(obj, operation.parameters);
          break;
        case TransformMode.FREE_TRANSFORM:
          await this.applyFreeTransform(obj, operation.parameters);
          break;
        default:
          throw new Error(`Unsupported transformation mode: ${operation.parameters.mode}`);
      }
      
      obj.setCoords();
      
      return {
        success: true,
        object: obj,
        originalState: originalState,
        newState: this.captureObjectState(obj),
        transformationType: operation.parameters.mode
      };
    } catch (error) {
      // Restore original state on error
      this.restoreObjectState(obj, originalState);
      
      return {
        success: false,
        object: obj,
        originalState: originalState,
        error: error.message,
        transformationType: operation.parameters.mode
      };
    }
  }

  private async applyScale(obj: fabric.Object, parameters: any): Promise<void> {
    const { scaleX, scaleY, origin, constrainProportions } = parameters.values;
    
    if (constrainProportions) {
      const uniformScale = Math.min(scaleX, scaleY);
      obj.set({ scaleX: uniformScale, scaleY: uniformScale });
    } else {
      obj.set({ scaleX, scaleY });
    }
    
    // Adjust position based on transform origin
    if (origin && (origin.x !== 0.5 || origin.y !== 0.5)) {
      const adjustedPosition = this.calculateOriginAdjustedPosition(obj, origin);
      obj.set({ left: adjustedPosition.x, top: adjustedPosition.y });
    }
  }

  private async applyRotation(obj: fabric.Object, parameters: any): Promise<void> {
    const { angle, origin } = parameters.values;
    
    if (origin && (origin.x !== 0.5 || origin.y !== 0.5)) {
      // Rotate around custom origin point
      const rotationMatrix = this.createRotationMatrix(angle, origin);
      const newPosition = this.applyMatrixToPoint(
        { x: obj.left!, y: obj.top! },
        rotationMatrix
      );
      
      obj.set({
        angle: (obj.angle! + angle) % 360,
        left: newPosition.x,
        top: newPosition.y
      });
    } else {
      // Simple rotation around object center
      obj.set({ angle: (obj.angle! + angle) % 360 });
    }
  }

  private async applyPerspective(obj: fabric.Object, parameters: any): Promise<void> {
    // Implementation of perspective transformation using transformation matrices
    const perspectiveMatrix = this.createPerspectiveMatrix(parameters.values);
    
    // Apply perspective transformation to object corners
    const corners = this.getObjectCorners(obj);
    const transformedCorners = corners.map(corner => 
      this.applyMatrixToPoint(corner, perspectiveMatrix)
    );
    
    // Update object to match perspective transformation
    this.updateObjectFromCorners(obj, transformedCorners);
  }

  private createRotationMatrix(angle: number, origin: Point): Matrix3x3 {
    const rad = (angle * Math.PI) / 180;
    const cos = Math.cos(rad);
    const sin = Math.sin(rad);
    
    // Translation to origin, rotation, translation back
    return new Matrix3x3([
      [cos, -sin, origin.x * (1 - cos) + origin.y * sin],
      [sin, cos, origin.y * (1 - cos) - origin.x * sin],
      [0, 0, 1]
    ]);
  }

  private createPerspectiveMatrix(perspectiveParams: any): Matrix3x3 {
    // Create perspective transformation matrix based on corner positions
    const { topLeft, topRight, bottomLeft, bottomRight } = perspectiveParams;
    
    // Calculate perspective transformation using bilinear interpolation
    return this.calculatePerspectiveMatrix(
      topLeft, topRight, bottomLeft, bottomRight
    );
  }
}
```

#### Alignment Engine Implementation
```typescript
// Professional alignment and distribution engine
export class AlignmentEngine {
  private canvas: fabric.Canvas;
  private snapTolerance: number = 1;

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
  }

  async execute(
    operation: AlignmentOperation,
    targetObjects: fabric.Object[]
  ): Promise<AlignmentExecutionResult> {
    const results: ObjectAlignmentResult[] = [];
    
    // Calculate reference bounds
    const referenceBounds = this.calculateReferenceBounds(
      operation.parameters.referenceTarget,
      targetObjects,
      operation.parameters.referenceObjectId
    );
    
    for (const obj of targetObjects) {
      const result = await this.alignObject(
        obj,
        operation.parameters.alignmentType,
        referenceBounds
      );
      results.push(result);
    }
    
    return {
      alignedObjects: results.map(r => r.object),
      results: results,
      alignmentType: operation.parameters.alignmentType,
      referenceBounds: referenceBounds
    };
  }

  private async alignObject(
    obj: fabric.Object,
    alignmentType: AlignmentType,
    referenceBounds: Bounds
  ): Promise<ObjectAlignmentResult> {
    const originalPosition = { x: obj.left!, y: obj.top! };
    const objectBounds = this.getObjectBounds(obj);
    
    let newPosition = { ...originalPosition };
    
    switch (alignmentType) {
      case AlignmentType.LEFT:
        newPosition.x = referenceBounds.left;
        break;
      case AlignmentType.CENTER_HORIZONTAL:
        newPosition.x = referenceBounds.left + (referenceBounds.width - objectBounds.width) / 2;
        break;
      case AlignmentType.RIGHT:
        newPosition.x = referenceBounds.left + referenceBounds.width - objectBounds.width;
        break;
      case AlignmentType.TOP:
        newPosition.y = referenceBounds.top;
        break;
      case AlignmentType.CENTER_VERTICAL:
        newPosition.y = referenceBounds.top + (referenceBounds.height - objectBounds.height) / 2;
        break;
      case AlignmentType.BOTTOM:
        newPosition.y = referenceBounds.top + referenceBounds.height - objectBounds.height;
        break;
      case AlignmentType.CENTER_BOTH:
        newPosition.x = referenceBounds.left + (referenceBounds.width - objectBounds.width) / 2;
        newPosition.y = referenceBounds.top + (referenceBounds.height - objectBounds.height) / 2;
        break;
    }
    
    obj.set({ left: newPosition.x, top: newPosition.y });
    obj.setCoords();
    
    return {
      success: true,
      object: obj,
      originalPosition: originalPosition,
      newPosition: newPosition,
      alignmentType: alignmentType
    };
  }

  async distribute(
    objects: fabric.Object[],
    distributionType: DistributionType,
    spacing?: number
  ): Promise<DistributionResult> {
    if (objects.length < 3) {
      throw new Error('At least 3 objects required for distribution');
    }
    
    // Sort objects based on distribution type
    const sortedObjects = this.sortObjectsForDistribution(objects, distributionType);
    const bounds = sortedObjects.map(obj => this.getObjectBounds(obj));
    
    if (spacing !== undefined) {
      // Distribute with fixed spacing
      return this.distributeWithSpacing(sortedObjects, bounds, distributionType, spacing);
    } else {
      // Distribute evenly across available space
      return this.distributeEvenly(sortedObjects, bounds, distributionType);
    }
  }

  private distributeEvenly(
    objects: fabric.Object[],
    bounds: Bounds[],
    distributionType: DistributionType
  ): DistributionResult {
    const firstBounds = bounds[0];
    const lastBounds = bounds[bounds.length - 1];
    
    let totalSpace: number;
    let positions: number[];
    
    if (distributionType === DistributionType.HORIZONTAL_CENTERS) {
      totalSpace = (lastBounds.left + lastBounds.width / 2) - (firstBounds.left + firstBounds.width / 2);
      positions = this.calculateEvenDistribution(objects.length, totalSpace);
      
      objects.forEach((obj, index) => {
        if (index > 0 && index < objects.length - 1) {
          const newLeft = firstBounds.left + firstBounds.width / 2 + positions[index] - bounds[index].width / 2;
          obj.set({ left: newLeft });
          obj.setCoords();
        }
      });
    } else if (distributionType === DistributionType.VERTICAL_CENTERS) {
      totalSpace = (lastBounds.top + lastBounds.height / 2) - (firstBounds.top + firstBounds.height / 2);
      positions = this.calculateEvenDistribution(objects.length, totalSpace);
      
      objects.forEach((obj, index) => {
        if (index > 0 && index < objects.length - 1) {
          const newTop = firstBounds.top + firstBounds.height / 2 + positions[index] - bounds[index].height / 2;
          obj.set({ top: newTop });
          obj.setCoords();
        }
      });
    }
    // Additional distribution types...
    
    return {
      success: true,
      distributedObjects: objects,
      distributionType: distributionType,
      spacing: totalSpace / (objects.length - 1)
    };
  }
}
```

### Performance Requirements

#### Operation Performance
- **Transformation Operations**: <50ms for single object operations
- **Batch Operations**: <200ms for 10+ object operations
- **Alignment Operations**: <100ms for complex alignments
- **Selection Operations**: <150ms for magic wand/lasso selection

#### Visual Feedback Performance
- **Preview Updates**: <16ms for real-time transformation previews
- **Smart Guides**: <32ms for snap guide calculations
- **Selection Feedback**: <50ms for selection highlight updates
- **Operation Confirmation**: <100ms for operation result display

### Security Requirements

#### Operation Security
- ✅ Validate operation parameters and prevent malicious inputs
- ✅ Limit operation complexity to prevent performance attacks
- ✅ Secure handling of operation history and undo data
- ✅ User permission validation for canvas modifications

#### Data Protection
- ✅ Secure storage of operation history and preferences
- ✅ Privacy-compliant analytics for operation usage
- ✅ Encrypted transmission of operation data
- ✅ User consent for operation analytics and improvement

## Technical Specifications

### Implementation Details

#### Free Transform Controller
```typescript
// Advanced free transform controller with professional handles
export class FreeTransformController {
  private objects: fabric.Object[];
  private canvas: fabric.Canvas;
  private transformBounds: Bounds;
  private handles: TransformHandle[];
  private isActive: boolean = false;
  private transformMode: TransformMode = TransformMode.FREE_TRANSFORM;

  constructor(options: FreeTransformOptions) {
    this.objects = options.objects;
    this.canvas = options.canvas;
    this.transformBounds = this.calculateCombinedBounds();
    this.createTransformHandles();
    this.setupEventHandlers();
  }

  private createTransformHandles(): void {
    this.handles = [
      // Corner handles for scaling
      new CornerHandle('top-left', this.transformBounds.topLeft, {
        cursor: 'nw-resize',
        onDrag: (delta) => this.handleCornerDrag('top-left', delta)
      }),
      new CornerHandle('top-right', this.transformBounds.topRight, {
        cursor: 'ne-resize',
        onDrag: (delta) => this.handleCornerDrag('top-right', delta)
      }),
      new CornerHandle('bottom-left', this.transformBounds.bottomLeft, {
        cursor: 'sw-resize',
        onDrag: (delta) => this.handleCornerDrag('bottom-left', delta)
      }),
      new CornerHandle('bottom-right', this.transformBounds.bottomRight, {
        cursor: 'se-resize',
        onDrag: (delta) => this.handleCornerDrag('bottom-right', delta)
      }),
      
      // Edge handles for scaling along one axis
      new EdgeHandle('top', this.transformBounds.topCenter, {
        cursor: 'n-resize',
        onDrag: (delta) => this.handleEdgeDrag('top', delta)
      }),
      new EdgeHandle('right', this.transformBounds.rightCenter, {
        cursor: 'e-resize',
        onDrag: (delta) => this.handleEdgeDrag('right', delta)
      }),
      new EdgeHandle('bottom', this.transformBounds.bottomCenter, {
        cursor: 's-resize',
        onDrag: (delta) => this.handleEdgeDrag('bottom', delta)
      }),
      new EdgeHandle('left', this.transformBounds.leftCenter, {
        cursor: 'w-resize',
        onDrag: (delta) => this.handleEdgeDrag('left', delta)
      }),
      
      // Rotation handle
      new RotationHandle(this.transformBounds.topCenter.offset(0, -30), {
        cursor: 'alias',
        onDrag: (delta) => this.handleRotationDrag(delta)
      }),
      
      // Center handle for moving
      new CenterHandle(this.transformBounds.center, {
        cursor: 'move',
        onDrag: (delta) => this.handleCenterDrag(delta)
      })
    ];
  }

  private handleCornerDrag(corner: string, delta: Point): void {
    const scaleFactors = this.calculateCornerScale(corner, delta);
    
    if (this.isShiftPressed()) {
      // Constrain proportions
      const uniformScale = Math.min(Math.abs(scaleFactors.x), Math.abs(scaleFactors.y));
      scaleFactors.x = scaleFactors.x < 0 ? -uniformScale : uniformScale;
      scaleFactors.y = scaleFactors.y < 0 ? -uniformScale : uniformScale;
    }
    
    this.applyScaleTransform(scaleFactors);
  }

  private handleRotationDrag(delta: Point): void {
    const center = this.transformBounds.center;
    const angle = Math.atan2(delta.y - center.y, delta.x - center.x);
    const previousAngle = this.getPreviousRotationAngle();
    const deltaAngle = angle - previousAngle;
    
    // Snap to 15-degree increments if Shift is pressed
    const finalAngle = this.isShiftPressed() 
      ? Math.round(deltaAngle * 180 / Math.PI / 15) * 15
      : deltaAngle * 180 / Math.PI;
    
    this.applyRotationTransform(finalAngle);
  }

  private applyScaleTransform(scaleFactors: Point): void {
    const center = this.transformBounds.center;
    
    this.objects.forEach(obj => {
      const objCenter = this.getObjectCenter(obj);
      const relativePosition = {
        x: objCenter.x - center.x,
        y: objCenter.y - center.y
      };
      
      // Scale the object
      obj.set({
        scaleX: (obj.scaleX || 1) * scaleFactors.x,
        scaleY: (obj.scaleY || 1) * scaleFactors.y
      });
      
      // Adjust position to maintain relative positioning
      const newRelativePosition = {
        x: relativePosition.x * scaleFactors.x,
        y: relativePosition.y * scaleFactors.y
      };
      
      obj.set({
        left: center.x + newRelativePosition.x - obj.width! * obj.scaleX! / 2,
        top: center.y + newRelativePosition.y - obj.height! * obj.scaleY! / 2
      });
      
      obj.setCoords();
    });
    
    this.updateTransformBounds();
    this.canvas.requestRenderAll();
  }

  private applyRotationTransform(angle: number): void {
    const center = this.transformBounds.center;
    const rad = angle * Math.PI / 180;
    
    this.objects.forEach(obj => {
      const objCenter = this.getObjectCenter(obj);
      
      // Rotate position around center
      const relativeX = objCenter.x - center.x;
      const relativeY = objCenter.y - center.y;
      
      const newX = relativeX * Math.cos(rad) - relativeY * Math.sin(rad);
      const newY = relativeX * Math.sin(rad) + relativeY * Math.cos(rad);
      
      // Update object position and rotation
      obj.set({
        left: center.x + newX - obj.width! * obj.scaleX! / 2,
        top: center.y + newY - obj.height! * obj.scaleY! / 2,
        angle: (obj.angle || 0) + angle
      });
      
      obj.setCoords();
    });
    
    this.updateTransformBounds();
    this.canvas.requestRenderAll();
  }
}
```

## Quality Gates

### Definition of Done

#### Advanced Operations Validation
- ✅ All transformation modes work correctly with precise mathematical accuracy
- ✅ Professional alignment and distribution tools function as expected
- ✅ Advanced selection tools provide accurate and efficient selection
- ✅ Multi-object editing enables bulk operations and property changes

#### Performance Validation
- ✅ Operations meet performance requirements for responsiveness
- ✅ Visual feedback systems provide real-time updates
- ✅ Batch operations handle multiple objects efficiently
- ✅ Memory usage optimized for complex operations

#### Quality Validation
- ✅ Mathematical precision maintained in all transformations
- ✅ Smart guides and snapping enhance user experience
- ✅ Error handling provides graceful degradation
- ✅ Operation history integration supports undo/redo

### Testing Requirements

#### Unit Tests
- Transformation algorithms and mathematical precision
- Alignment and distribution calculations
- Selection algorithms and criteria matching
- Batch operation processing and error handling

#### Integration Tests
- Canvas integration with advanced operations
- State management integration with operations
- Performance testing under load
- Multi-object operation workflows

#### E2E Tests
- Complete professional design workflows
- Complex transformation and alignment scenarios
- Advanced selection and editing workflows
- Performance validation with large numbers of objects

## Risk Assessment

### High Risk Areas

#### Mathematical Precision
- **Risk**: Floating-point precision errors in complex transformations
- **Mitigation**: Use high-precision mathematics libraries and validation
- **Contingency**: Fallback to simpler transformation methods

#### Performance with Many Objects
- **Risk**: Operations may become slow with hundreds of objects
- **Mitigation**: Optimize algorithms and implement progressive operations
- **Contingency**: Limit operation complexity and provide progress feedback

### Medium Risk Areas

#### Complex Selection Accuracy
- **Risk**: Advanced selection tools may produce unexpected results
- **Mitigation**: Comprehensive testing with diverse content types
- **Contingency**: Provide selection refinement and correction tools

## Success Metrics

### Technical Metrics
- **Operation Performance**: <50ms for 95% of single-object operations
- **Batch Operation Efficiency**: <200ms for 10-object operations
- **Mathematical Precision**: <0.001 pixel accuracy for transformations
- **Selection Accuracy**: >95% correct object identification

### User Experience Metrics
- **Professional Adoption**: 90% of users utilize advanced operations
- **Operation Frequency**: Average 50+ operations per design session
- **Feature Discovery**: 85% adoption of professional alignment tools
- **Satisfaction**: >4.7/5 for advanced operation capabilities

## Implementation Timeline

### Week 1: Core Advanced Operations
- **Days 1-2**: Advanced transformation engine and free transform
- **Days 3-4**: Professional alignment and distribution systems
- **Day 5**: Advanced selection tools and methods

### Week 2: Integration and Polish
- **Days 1-2**: Multi-object editing and batch operations
- **Days 3-4**: Smart guides, snapping, and visual feedback
- **Day 5**: Testing, optimization, and documentation

## Follow-up Stories

### Immediate Next Stories
- **V3.1a**: Vector Graphics Tools (uses advanced operations for vector editing)
- **V3.2a**: Filter & Effects Pipeline (leverages transformation system)
- **V4.1a**: Component Library (uses advanced operations for component management)

### Future Enhancements
- **3D Transformations**: Extending operations to 3D space
- **Animation Operations**: Time-based transformations and tweening
- **Procedural Operations**: Rule-based and algorithmic operations
- **Custom Operation Scripts**: User-defined transformation workflows

This comprehensive advanced canvas operations system provides the professional-grade tools necessary for sophisticated design work while maintaining performance and precision standards essential for a modern design studio platform.