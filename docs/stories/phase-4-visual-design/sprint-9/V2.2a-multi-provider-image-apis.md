# Story V2.2a: Multi-provider Image APIs

## Story Overview

**Epic**: V2 - AI Image Generation  
**Story ID**: V2.2a  
**Title**: Multi-provider AI Image API Integration Platform  
**Priority**: High  
**Effort**: 8 story points  
**Sprint**: Sprint 9 (Week 17-18)  
**Phase**: Visual Design Studio  

## Dependencies

### Prerequisites
- ✅ V2.1a: Replicate Integration (Parallel development)
- ✅ F2.1a: Multi-Provider AI System (Completed in Phase 1)
- ✅ foto-fun existing AI infrastructure

### Enables
- V2.3a: Style Transfer System
- V2.4a: Image Enhancement Tools
- V2.1b: AI Image Workflows
- V4.4a: Design Templates

### Blocks Until Complete
- Multi-model AI workflows
- Provider failover and optimization
- Cost optimization across providers
- Professional AI model ecosystem

## Sub-Agent Assignments

### Primary Agent: AI (AI Integration Agent)
**Responsibilities**:
- Design unified multi-provider AI architecture
- Implement provider abstraction and routing
- Create intelligent model selection algorithms
- Design cross-provider workflow orchestration

**Deliverables**:
- Multi-provider AI integration platform
- Provider abstraction layer
- Intelligent routing system
- Cross-provider workflow engine

### Supporting Agent: BE (Backend Agent)
**Responsibilities**:
- Implement scalable API integration infrastructure
- Create provider monitoring and health checks
- Design distributed caching and optimization
- Implement cost tracking across providers

**Deliverables**:
- Scalable multi-provider API layer
- Provider health monitoring system
- Distributed caching infrastructure
- Cross-provider cost analytics

### Supporting Agent: SEC (Security Agent)
**Responsibilities**:
- Secure multi-provider API key management
- Implement provider-specific security protocols
- Design secure data handling across providers
- Create audit trails for multi-provider usage

**Deliverables**:
- Secure multi-provider credential management
- Provider security compliance system
- Secure data handling protocols
- Comprehensive audit logging

## Acceptance Criteria

### Functional Requirements

#### V2.2a.1: Multi-provider Integration Platform
**GIVEN** the need for diverse AI model capabilities
**WHEN** integrating multiple AI providers
**THEN** it should:
- ✅ Support Replicate, Stability AI, OpenAI DALL-E, and Midjourney APIs
- ✅ Provide unified interface for all providers
- ✅ Enable seamless switching between providers for similar tasks
- ✅ Support provider-specific features and capabilities
- ✅ Maintain consistent response formats across providers

#### V2.2a.2: Intelligent Provider Selection
**GIVEN** multiple providers offering similar capabilities
**WHEN** selecting optimal provider for a task
**THEN** it should:
- ✅ Analyze task requirements and match with provider strengths
- ✅ Consider cost, speed, and quality factors
- ✅ Monitor provider availability and performance
- ✅ Automatically failover to alternative providers
- ✅ Learn from user preferences and optimize selection

#### V2.2a.3: Cross-provider Workflow Orchestration
**GIVEN** complex workflows requiring multiple AI models
**WHEN** orchestrating cross-provider workflows
**THEN** it should:
- ✅ Support workflows spanning multiple providers
- ✅ Handle provider-specific data formats and conversions
- ✅ Maintain state consistency across provider boundaries
- ✅ Optimize data transfer and processing efficiency
- ✅ Provide workflow visualization and monitoring

### Technical Requirements

#### Provider Integration Architecture
```typescript
interface AIProvider {
  id: string;
  name: string;
  baseUrl: string;
  authentication: AuthenticationConfig;
  capabilities: ProviderCapabilities;
  pricing: PricingModel;
  limits: RateLimits;
  health: ProviderHealth;
}

interface ProviderCapabilities {
  textToImage: boolean;
  imageToImage: boolean;
  imageUpscaling: boolean;
  imageInpainting: boolean;
  backgroundRemoval: boolean;
  styleTransfer: boolean;
  faceRestoration: boolean;
  objectDetection: boolean;
  customModels: boolean;
  batchProcessing: boolean;
}

interface UnifiedAIRequest {
  task: string;
  inputs: Record<string, any>;
  parameters: Record<string, any>;
  preferences: UserPreferences;
  constraints: RequestConstraints;
}

interface UnifiedAIResponse {
  providerId: string;
  modelId: string;
  outputs: Record<string, any>;
  metadata: ResponseMetadata;
  performance: PerformanceMetrics;
  cost: CostBreakdown;
}
```

#### Multi-provider Service Implementation
```typescript
// Multi-provider AI service with intelligent routing
export class MultiProviderAIService {
  private providers: Map<string, AIProviderAdapter>;
  private routingEngine: ProviderRoutingEngine;
  private costOptimizer: CrossProviderCostOptimizer;
  private healthMonitor: ProviderHealthMonitor;
  private workflowOrchestrator: CrossProviderOrchestrator;

  constructor(config: MultiProviderConfig) {
    this.initializeProviders(config);
    this.setupRouting();
    this.startHealthMonitoring();
  }

  private initializeProviders(config: MultiProviderConfig): void {
    // Initialize Stability AI adapter
    this.providers.set('stability', new StabilityAIAdapter({
      apiKey: config.stabilityApiKey,
      baseUrl: 'https://api.stability.ai',
      capabilities: {
        textToImage: true,
        imageToImage: true,
        imageUpscaling: true,
        imageInpainting: true,
        styleTransfer: false,
        batchProcessing: true
      },
      pricing: {
        textToImage: 0.05,
        imageToImage: 0.03,
        imageUpscaling: 0.02
      }
    }));

    // Initialize OpenAI DALL-E adapter
    this.providers.set('openai', new OpenAIAdapter({
      apiKey: config.openaiApiKey,
      baseUrl: 'https://api.openai.com',
      capabilities: {
        textToImage: true,
        imageToImage: true,
        imageUpscaling: false,
        imageInpainting: true,
        styleTransfer: false,
        batchProcessing: false
      },
      pricing: {
        textToImage: 0.08,
        imageToImage: 0.06
      }
    }));

    // Initialize Midjourney adapter (via unofficial API)
    this.providers.set('midjourney', new MidjourneyAdapter({
      apiKey: config.midjourneyApiKey,
      baseUrl: 'https://api.midjourney.com',
      capabilities: {
        textToImage: true,
        imageToImage: true,
        imageUpscaling: true,
        styleTransfer: true,
        batchProcessing: false
      },
      pricing: {
        textToImage: 0.10,
        imageToImage: 0.08,
        imageUpscaling: 0.05
      }
    }));

    // Replicate already initialized in V2.1a
    this.providers.set('replicate', this.getReplicateAdapter());
  }

  // Intelligent provider selection
  async selectOptimalProvider(
    request: UnifiedAIRequest
  ): Promise<{ provider: AIProviderAdapter; confidence: number }> {
    // Get providers that support the requested task
    const capableProviders = Array.from(this.providers.values())
      .filter(provider => this.supportsTask(provider, request.task));

    if (capableProviders.length === 0) {
      throw new Error(`No providers support task: ${request.task}`);
    }

    // Score providers based on multiple factors
    const scoredProviders = await Promise.all(
      capableProviders.map(async provider => ({
        provider,
        score: await this.scoreProvider(provider, request),
        confidence: await this.calculateConfidence(provider, request)
      }))
    );

    // Sort by score and return best provider
    const bestProvider = scoredProviders
      .sort((a, b) => b.score - a.score)[0];

    return {
      provider: bestProvider.provider,
      confidence: bestProvider.confidence
    };
  }

  private async scoreProvider(
    provider: AIProviderAdapter, 
    request: UnifiedAIRequest
  ): Promise<number> {
    let score = 0;

    // Quality factor (40% weight)
    const qualityScore = await this.getProviderQualityScore(provider.id, request.task);
    score += qualityScore * 0.4;

    // Speed factor (30% weight)
    const speedScore = await this.getProviderSpeedScore(provider.id, request.task);
    score += speedScore * 0.3;

    // Cost factor (20% weight)
    const costScore = this.getCostScore(provider, request);
    score += costScore * 0.2;

    // Availability factor (10% weight)
    const availabilityScore = this.healthMonitor.getAvailabilityScore(provider.id);
    score += availabilityScore * 0.1;

    // User preference bonus
    if (request.preferences.preferredProvider === provider.id) {
      score += 10;
    }

    // Apply constraints penalties
    if (!this.meetsConstraints(provider, request.constraints)) {
      score *= 0.5;
    }

    return score;
  }

  // Unified generation interface
  async generate(request: UnifiedAIRequest): Promise<UnifiedAIResponse> {
    const startTime = Date.now();
    
    try {
      // Select optimal provider
      const { provider, confidence } = await this.selectOptimalProvider(request);
      
      // Convert request to provider-specific format
      const providerRequest = await this.convertRequest(provider, request);
      
      // Execute generation
      const providerResponse = await provider.generate(providerRequest);
      
      // Convert response to unified format
      const unifiedResponse = await this.convertResponse(
        provider, 
        providerResponse, 
        request
      );
      
      // Track performance and cost
      this.trackGeneration(provider, request, unifiedResponse, Date.now() - startTime);
      
      return unifiedResponse;
      
    } catch (error) {
      // Implement intelligent fallback
      return this.handleGenerationError(request, error);
    }
  }

  private async handleGenerationError(
    request: UnifiedAIRequest, 
    error: Error
  ): Promise<UnifiedAIResponse> {
    // Try alternative providers
    const fallbackProviders = await this.getFallbackProviders(request);
    
    for (const provider of fallbackProviders) {
      try {
        const providerRequest = await this.convertRequest(provider, request);
        const response = await provider.generate(providerRequest);
        return this.convertResponse(provider, response, request);
      } catch (fallbackError) {
        console.warn(`Fallback provider ${provider.id} also failed:`, fallbackError);
      }
    }
    
    throw new Error(`All providers failed for request: ${error.message}`);
  }

  // Cross-provider workflow execution
  async executeWorkflow(workflow: CrossProviderWorkflow): Promise<WorkflowResult> {
    const results: StepResult[] = [];
    let currentData = workflow.initialData;

    for (const step of workflow.steps) {
      try {
        // Select provider for this step
        const provider = await this.selectProviderForStep(step, currentData);
        
        // Execute step with selected provider
        const stepResult = await this.executeWorkflowStep(
          provider, 
          step, 
          currentData
        );
        
        results.push(stepResult);
        currentData = stepResult.output;
        
      } catch (error) {
        // Handle step failure with provider alternatives
        const recoveryResult = await this.recoverWorkflowStep(step, currentData, error);
        if (recoveryResult) {
          results.push(recoveryResult);
          currentData = recoveryResult.output;
        } else {
          throw new Error(`Workflow step failed: ${error.message}`);
        }
      }
    }

    return {
      workflowId: workflow.id,
      steps: results,
      finalOutput: currentData,
      totalCost: results.reduce((sum, step) => sum + step.cost, 0),
      executionTime: results.reduce((sum, step) => sum + step.executionTime, 0)
    };
  }

  // Batch processing across providers
  async processBatch(requests: UnifiedAIRequest[]): Promise<UnifiedAIResponse[]> {
    // Group requests by optimal provider
    const providerGroups = await this.groupRequestsByProvider(requests);
    const results: UnifiedAIResponse[] = [];

    // Process each provider group
    for (const [providerId, providerRequests] of providerGroups) {
      const provider = this.providers.get(providerId)!;
      
      // Respect provider rate limits
      const batchSize = provider.getBatchSize();
      const batches = this.chunkArray(providerRequests, batchSize);
      
      for (const batch of batches) {
        const batchResults = await this.processBatchWithProvider(provider, batch);
        results.push(...batchResults);
        
        // Wait between batches to respect rate limits
        await this.waitForRateLimit(provider);
      }
    }

    return results;
  }

  // Cost optimization across providers
  async optimizeCosts(
    requests: UnifiedAIRequest[], 
    budget: number
  ): Promise<CostOptimizationPlan> {
    const plan: CostOptimizationPlan = {
      totalEstimatedCost: 0,
      assignments: [],
      savings: 0,
      recommendations: []
    };

    // Calculate optimal provider assignment for cost
    for (const request of requests) {
      const costAnalysis = await this.analyzeCosts(request);
      const optimalAssignment = this.selectCostOptimalProvider(costAnalysis, budget);
      
      plan.assignments.push(optimalAssignment);
      plan.totalEstimatedCost += optimalAssignment.estimatedCost;
      
      // Add recommendations for further savings
      if (optimalAssignment.savings > 0) {
        plan.recommendations.push({
          type: 'provider_switch',
          description: `Switch to ${optimalAssignment.providerId} for ${optimalAssignment.savings.toFixed(2)} savings`,
          impact: optimalAssignment.savings
        });
      }
    }

    // Calculate total savings
    const originalCost = requests.reduce((sum, req) => 
      sum + this.calculateOriginalCost(req), 0
    );
    plan.savings = originalCost - plan.totalEstimatedCost;

    return plan;
  }
}
```

#### Provider Adapters
```typescript
// Base provider adapter interface
export abstract class AIProviderAdapter {
  abstract id: string;
  abstract name: string;
  protected apiKey: string;
  protected baseUrl: string;
  protected capabilities: ProviderCapabilities;
  protected rateLimits: RateLimits;

  constructor(config: ProviderConfig) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl;
    this.capabilities = config.capabilities;
    this.rateLimits = config.rateLimits;
  }

  abstract async generate(request: ProviderSpecificRequest): Promise<ProviderSpecificResponse>;
  abstract convertFromUnified(request: UnifiedAIRequest): ProviderSpecificRequest;
  abstract convertToUnified(response: ProviderSpecificResponse): UnifiedAIResponse;
  abstract getBatchSize(): number;
  abstract supportsTask(task: string): boolean;
}

// Stability AI adapter implementation
export class StabilityAIAdapter extends AIProviderAdapter {
  id = 'stability';
  name = 'Stability AI';

  async generate(request: StabilityAIRequest): Promise<StabilityAIResponse> {
    const response = await fetch(`${this.baseUrl}/v1/generation/${request.engine}/text-to-image`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        text_prompts: [{ text: request.prompt }],
        cfg_scale: request.guidance_scale || 7,
        height: request.height || 512,
        width: request.width || 512,
        samples: request.samples || 1,
        steps: request.steps || 30
      })
    });

    if (!response.ok) {
      throw new Error(`Stability AI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      artifacts: data.artifacts,
      metadata: {
        model: request.engine,
        processingTime: Date.now() - request.startTime
      }
    };
  }

  convertFromUnified(request: UnifiedAIRequest): StabilityAIRequest {
    return {
      engine: 'stable-diffusion-xl-1024-v1-0',
      prompt: request.inputs.prompt,
      negative_prompt: request.inputs.negative_prompt,
      width: request.parameters.width || 1024,
      height: request.parameters.height || 1024,
      guidance_scale: request.parameters.guidance_scale || 7,
      steps: request.parameters.steps || 30,
      samples: request.parameters.samples || 1,
      startTime: Date.now()
    };
  }

  convertToUnified(response: StabilityAIResponse): UnifiedAIResponse {
    return {
      providerId: this.id,
      modelId: response.metadata.model,
      outputs: {
        images: response.artifacts.map(artifact => ({
          url: `data:image/png;base64,${artifact.base64}`,
          seed: artifact.seed,
          finish_reason: artifact.finishReason
        }))
      },
      metadata: {
        processingTime: response.metadata.processingTime,
        provider: this.name
      },
      performance: {
        responseTime: response.metadata.processingTime,
        quality: 'high'
      },
      cost: {
        amount: 0.05, // Per image
        currency: 'USD',
        breakdown: {
          generation: 0.05
        }
      }
    };
  }

  getBatchSize(): number {
    return 10; // Stability AI supports batch processing
  }

  supportsTask(task: string): boolean {
    const supportedTasks = ['text-to-image', 'image-to-image', 'image-upscaling'];
    return supportedTasks.includes(task);
  }
}

// OpenAI DALL-E adapter implementation
export class OpenAIAdapter extends AIProviderAdapter {
  id = 'openai';
  name = 'OpenAI DALL-E';

  async generate(request: OpenAIRequest): Promise<OpenAIResponse> {
    const response = await fetch(`${this.baseUrl}/v1/images/generations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: request.model || 'dall-e-3',
        prompt: request.prompt,
        n: request.n || 1,
        size: request.size || '1024x1024',
        quality: request.quality || 'standard',
        style: request.style || 'vivid'
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    return await response.json();
  }

  convertFromUnified(request: UnifiedAIRequest): OpenAIRequest {
    const size = `${request.parameters.width || 1024}x${request.parameters.height || 1024}`;
    
    return {
      model: 'dall-e-3',
      prompt: request.inputs.prompt,
      n: request.parameters.samples || 1,
      size: size as '1024x1024' | '1792x1024' | '1024x1792',
      quality: request.parameters.quality || 'standard',
      style: request.parameters.style || 'vivid'
    };
  }

  convertToUnified(response: OpenAIResponse): UnifiedAIResponse {
    return {
      providerId: this.id,
      modelId: 'dall-e-3',
      outputs: {
        images: response.data.map(item => ({
          url: item.url,
          revised_prompt: item.revised_prompt
        }))
      },
      metadata: {
        provider: this.name,
        created: response.created
      },
      performance: {
        quality: 'high'
      },
      cost: {
        amount: 0.08, // Per image
        currency: 'USD',
        breakdown: {
          generation: 0.08
        }
      }
    };
  }

  getBatchSize(): number {
    return 1; // DALL-E doesn't support batch processing
  }

  supportsTask(task: string): boolean {
    return task === 'text-to-image';
  }
}
```

### Performance Requirements

#### Multi-provider Performance
- **Provider Selection**: <500ms for optimal provider selection
- **Cross-provider Workflows**: <10% overhead vs single provider
- **Failover Time**: <5 seconds for provider failover
- **Batch Processing**: 20+ requests processed concurrently

#### Cost Optimization
- **Cost Savings**: 25% average cost reduction through optimization
- **Budget Tracking**: Real-time cost monitoring across providers
- **Efficiency Gains**: 30% improvement in cost per generation
- **Resource Utilization**: >85% optimal provider utilization

### Security Requirements

#### Multi-provider Security
- ✅ Secure credential management for all providers
- ✅ Provider-specific security protocol compliance
- ✅ Encrypted data transmission between providers
- ✅ Audit trails for cross-provider operations

#### Data Protection
- ✅ Provider data isolation and separation
- ✅ Secure handling of provider-specific responses
- ✅ Privacy-compliant data sharing controls
- ✅ User consent management for provider selection

## Quality Gates

### Definition of Done

#### Integration Validation
- ✅ All major AI providers integrate seamlessly
- ✅ Unified interface works consistently across providers
- ✅ Provider selection algorithms optimize for requirements
- ✅ Cross-provider workflows execute reliably

#### Performance Validation
- ✅ Provider failover mechanisms work under load
- ✅ Cost optimization delivers measurable savings
- ✅ Batch processing scales across providers
- ✅ Health monitoring accurately tracks provider status

#### Quality Validation
- ✅ Type safety across all provider integrations
- ✅ Error handling covers all provider failure modes
- ✅ Security measures protect credentials and data
- ✅ Analytics provide actionable insights

### Testing Requirements

#### Unit Tests
- Provider adapter implementations
- Routing and selection algorithms
- Cost optimization calculations
- Cross-provider workflow logic

#### Integration Tests
- Real provider API integrations
- Failover and fallback mechanisms
- Batch processing across providers
- Cost tracking accuracy

#### E2E Tests
- Complete multi-provider workflows
- Professional use case scenarios
- Performance under provider outages
- Cost optimization effectiveness

## Risk Assessment

### High Risk Areas

#### Provider Dependencies
- **Risk**: Multiple external API dependencies create failure points
- **Mitigation**: Robust failover and redundancy systems
- **Contingency**: Local model support and offline capabilities

#### Cost Management
- **Risk**: Multiple providers could lead to unexpected costs
- **Mitigation**: Real-time cost tracking and budget controls
- **Contingency**: Automatic provider throttling and alerts

### Medium Risk Areas

#### API Consistency
- **Risk**: Provider API changes could break integrations
- **Mitigation**: Versioned adapters and automated testing
- **Contingency**: Rapid adapter updates and fallback versions

## Success Metrics

### Technical Metrics
- **Provider Coverage**: Support for 5+ major AI providers
- **Failover Success Rate**: >99% successful failovers
- **Cost Optimization**: 25% average cost reduction
- **Response Time**: <2 seconds for provider selection

### User Experience Metrics
- **Provider Satisfaction**: >4.7/5 for AI generation quality
- **Feature Adoption**: 70% of users utilize multiple providers
- **Workflow Success**: 95% successful cross-provider workflows
- **Cost Awareness**: 80% of users engage with cost optimization

## Implementation Timeline

### Week 1: Provider Integration
- **Days 1-2**: Provider adapter implementations
- **Days 3-4**: Unified interface and routing engine
- **Day 5**: Provider selection and optimization algorithms

### Week 2: Orchestration and Testing
- **Days 1-2**: Cross-provider workflow orchestration
- **Days 3-4**: Cost optimization and monitoring
- **Day 5**: Testing, validation, and documentation

## Follow-up Stories

### Immediate Next Stories
- **V2.3a**: Style Transfer System (uses multi-provider capabilities)
- **V2.4a**: Image Enhancement Tools (leverages provider diversity)
- **V2.1b**: AI Image Workflows (builds on orchestration platform)

### Future Enhancements
- **Custom Provider Integration**: Community provider support
- **Hybrid Cloud/Local**: Mixed cloud and local model deployment
- **AI Provider Marketplace**: Community-driven provider ecosystem
- **Advanced Cost Analytics**: ML-powered cost optimization

This multi-provider platform creates a robust, optimized AI ecosystem that leverages the strengths of different providers while minimizing costs and maximizing reliability.