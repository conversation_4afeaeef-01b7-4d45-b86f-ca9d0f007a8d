# Story D2.2a: File System Management

## Story Overview

**Epic**: D2 - Development Tools Platform  
**Story ID**: D2.2a  
**Title**: Web-Based File System Management and Operations  
**Priority**: High  
**Effort**: 8 story points  
**Sprint**: Sprint 7 (Week 13-14)  
**Phase**: Development Environment  

## Dependencies

### Prerequisites
- ✅ D1.1a: Monaco Editor Integration (File operations foundation)
- ✅ D2.1a: Web Terminal XTerm Integration (Command-line file operations)
- File System Access API browser support

### Enables
- D2.3a: Git Integration
- D2.4a: Project Management
- D3.2a: Code Analysis and Review
- Advanced file-based workflows

### Blocks Until Complete
- Project file organization
- Source code management
- File-based development operations
- Workspace management

## Sub-Agent Assignments

### Primary Agent: BE (Backend Agent)
**Responsibilities**:
- Design secure file system abstraction layer
- Implement file operation APIs with sandboxing
- Create file watching and synchronization system
- Build file system security and access controls

**Deliverables**:
- File system service architecture
- Secure file operation APIs
- File watching and sync system
- Access control and security framework

### Supporting Agent: FE (Frontend Agent)
**Responsibilities**:
- Design file explorer interface and navigation
- Implement file operation UI components
- Create file upload/download interfaces
- Build file search and filtering capabilities

**Deliverables**:
- File explorer component system
- File operation UI workflows
- Drag-and-drop file handling
- Search and filtering interfaces

### Supporting Agent: SEC (Security Agent)
**Responsibilities**:
- Design file system security policies
- Implement file access validation
- Create file content scanning and validation
- Build audit trails for file operations

**Deliverables**:
- File security policy framework
- Access validation system
- Content scanning and validation
- File operation audit system

## Acceptance Criteria

### Functional Requirements

#### D2.2a.1: Core File System Operations
**GIVEN** developers need to manage project files
**WHEN** performing file operations
**THEN** it should:
- ✅ Support file creation, reading, updating, and deletion (CRUD)
- ✅ Handle directory creation and management operations
- ✅ Provide file copying, moving, and renaming functionality
- ✅ Support bulk file operations and batch processing
- ✅ Enable file property viewing and metadata management

#### D2.2a.2: File Explorer Interface
**GIVEN** users need intuitive file navigation
**WHEN** browsing project structure
**THEN** it should:
- ✅ Display hierarchical file tree with expand/collapse
- ✅ Support multiple view modes (tree, list, grid)
- ✅ Provide context menus for file operations
- ✅ Enable drag-and-drop file organization
- ✅ Support file selection (single, multiple, range)

#### D2.2a.3: File Upload and Download
**GIVEN** users need to transfer files to/from the web environment
**WHEN** importing or exporting files
**THEN** it should:
- ✅ Support individual file upload via file picker
- ✅ Enable bulk file upload with folder structure preservation
- ✅ Provide drag-and-drop file upload interface
- ✅ Support file download with original structure
- ✅ Handle large file transfers with progress indication

#### D2.2a.4: Advanced File Operations
**GIVEN** developers need sophisticated file management
**WHEN** working with complex project structures
**THEN** it should:
- ✅ Provide file search across project with filters
- ✅ Support file content search with regex patterns
- ✅ Enable file comparison and diff visualization
- ✅ Provide file history and version tracking
- ✅ Support file templates and scaffolding

### Technical Requirements

#### File System Architecture
```typescript
interface FileSystemAPI {
  // Basic operations
  readFile(path: string): Promise<Uint8Array>;
  writeFile(path: string, data: Uint8Array): Promise<void>;
  deleteFile(path: string): Promise<void>;
  renameFile(oldPath: string, newPath: string): Promise<void>;
  copyFile(sourcePath: string, targetPath: string): Promise<void>;
  
  // Directory operations
  createDirectory(path: string): Promise<void>;
  removeDirectory(path: string, recursive?: boolean): Promise<void>;
  listDirectory(path: string): Promise<FileSystemEntry[]>;
  
  // Metadata operations
  getFileInfo(path: string): Promise<FileInfo>;
  setFilePermissions(path: string, permissions: FilePermissions): Promise<void>;
  
  // Advanced operations
  watchFile(path: string, callback: FileWatchCallback): Promise<FileWatcher>;
  searchFiles(query: SearchQuery): Promise<SearchResult[]>;
  bulkOperation(operations: FileOperation[]): Promise<OperationResult[]>;
}

interface FileSystemEntry {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  modified: Date;
  created: Date;
  permissions: FilePermissions;
  isHidden: boolean;
  isSymlink: boolean;
  mimeType?: string;
}

interface FileInfo extends FileSystemEntry {
  encoding?: string;
  lineCount?: number;
  hash: string;
  version: number;
  metadata: Record<string, any>;
}

interface SearchQuery {
  path: string;
  pattern: string;
  includeContent: boolean;
  fileTypes: string[];
  excludePatterns: string[];
  maxResults: number;
  caseSensitive: boolean;
  useRegex: boolean;
}
```

#### File System Service Implementation
```typescript
class WebFileSystemService implements FileSystemAPI {
  private rootHandle: FileSystemDirectoryHandle | null = null;
  private cache: Map<string, CachedEntry> = new Map();
  private watchers: Map<string, FileWatcher[]> = new Map();
  private securityValidator: FileSecurityValidator;
  
  async initialize(): Promise<void> {
    // Request directory access permission
    if ('showDirectoryPicker' in window) {
      try {
        this.rootHandle = await window.showDirectoryPicker();
        await this.validateRootDirectory();
        await this.buildInitialCache();
      } catch (error) {
        // Fall back to virtual file system
        await this.initializeVirtualFileSystem();
      }
    } else {
      await this.initializeVirtualFileSystem();
    }
  }
  
  async readFile(path: string): Promise<Uint8Array> {
    await this.validatePath(path);
    
    const cachedEntry = this.cache.get(path);
    if (cachedEntry && !cachedEntry.isExpired()) {
      return cachedEntry.content;
    }
    
    const fileHandle = await this.getFileHandle(path);
    const file = await fileHandle.getFile();
    const content = new Uint8Array(await file.arrayBuffer());
    
    // Update cache
    this.cache.set(path, {
      content,
      lastModified: file.lastModified,
      size: file.size,
      cachedAt: Date.now()
    });
    
    return content;
  }
  
  async writeFile(path: string, data: Uint8Array): Promise<void> {
    await this.validatePath(path);
    await this.validateFileWrite(path, data);
    
    const fileHandle = await this.getFileHandle(path, { create: true });
    const writable = await fileHandle.createWritable();
    
    try {
      await writable.write(data);
      await writable.close();
      
      // Update cache
      this.cache.set(path, {
        content: data,
        lastModified: Date.now(),
        size: data.length,
        cachedAt: Date.now()
      });
      
      // Notify watchers
      this.notifyWatchers(path, 'modified');
      
      // Log operation
      await this.logFileOperation('write', path, data.length);
      
    } catch (error) {
      await writable.abort();
      throw new FileSystemError(`Failed to write file: ${error.message}`);
    }
  }
  
  async listDirectory(path: string): Promise<FileSystemEntry[]> {
    await this.validatePath(path);
    
    const dirHandle = await this.getDirectoryHandle(path);
    const entries: FileSystemEntry[] = [];
    
    for await (const [name, handle] of dirHandle.entries()) {
      const entry = await this.createFileSystemEntry(handle, path + '/' + name);
      entries.push(entry);
    }
    
    return entries.sort((a, b) => {
      // Directories first, then files
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });
  }
  
  async searchFiles(query: SearchQuery): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    
    await this.searchDirectory(query.path, query, results);
    
    return results
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, query.maxResults);
  }
  
  private async searchDirectory(
    dirPath: string,
    query: SearchQuery,
    results: SearchResult[]
  ): Promise<void> {
    const entries = await this.listDirectory(dirPath);
    
    for (const entry of entries) {
      if (this.shouldSkipEntry(entry, query)) continue;
      
      if (entry.type === 'directory') {
        await this.searchDirectory(entry.path, query, results);
      } else {
        const relevance = await this.calculateRelevance(entry, query);
        if (relevance > 0) {
          results.push({
            entry,
            relevance,
            matches: await this.findMatches(entry, query)
          });
        }
      }
    }
  }
  
  private async validateFileWrite(
    path: string,
    data: Uint8Array
  ): Promise<void> {
    // Check file size limits
    if (data.length > this.maxFileSize) {
      throw new FileSystemError(`File size exceeds limit: ${data.length} bytes`);
    }
    
    // Validate file extension
    const extension = path.split('.').pop()?.toLowerCase();
    if (extension && this.blockedExtensions.includes(extension)) {
      throw new FileSystemError(`File type not allowed: ${extension}`);
    }
    
    // Scan for malicious content
    await this.securityValidator.scanContent(data);
  }
}
```

#### File Explorer Component
```typescript
interface FileExplorerProps {
  rootPath: string;
  selectedFiles: string[];
  onFileSelect: (files: string[]) => void;
  onFileOpen: (path: string) => void;
  onFileOperation: (operation: FileOperation) => Promise<void>;
  viewMode: 'tree' | 'list' | 'grid';
  showHidden: boolean;
}

export const FileExplorer: React.FC<FileExplorerProps> = ({
  rootPath,
  selectedFiles,
  onFileSelect,
  onFileOpen,
  onFileOperation,
  viewMode,
  showHidden
}) => {
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  
  const fileSystemService = useFileSystemService();
  
  useEffect(() => {
    loadFileTree();
  }, [rootPath, showHidden]);
  
  const loadFileTree = async () => {
    setLoading(true);
    try {
      const entries = await fileSystemService.listDirectory(rootPath);
      const tree = await buildFileTree(entries, showHidden);
      setFileTree(tree);
    } catch (error) {
      console.error('Failed to load file tree:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleDrop = async (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    
    const files = Array.from(event.dataTransfer.files);
    if (files.length === 0) return;
    
    try {
      await uploadFiles(files, rootPath);
      await loadFileTree();
    } catch (error) {
      console.error('Failed to upload files:', error);
    }
  };
  
  const handleFileContextMenu = (
    event: React.MouseEvent,
    file: FileSystemEntry
  ) => {
    event.preventDefault();
    
    const contextMenu = createContextMenu([
      {
        label: 'Open',
        action: () => onFileOpen(file.path),
        disabled: file.type === 'directory'
      },
      { type: 'separator' },
      {
        label: 'Copy',
        action: () => copyToClipboard(file.path)
      },
      {
        label: 'Cut',
        action: () => cutToClipboard(file.path)
      },
      {
        label: 'Paste',
        action: () => pasteFromClipboard(file.path),
        disabled: !hasClipboardContent()
      },
      { type: 'separator' },
      {
        label: 'Rename',
        action: () => startRename(file.path)
      },
      {
        label: 'Delete',
        action: () => deleteFile(file.path),
        className: 'text-red-600'
      },
      { type: 'separator' },
      {
        label: 'Properties',
        action: () => showProperties(file.path)
      }
    ]);
    
    showContextMenu(contextMenu, event.clientX, event.clientY);
  };
  
  return (
    <div
      className={`file-explorer ${viewMode}`}
      onDrop={handleDrop}
      onDragOver={(e) => { e.preventDefault(); setDragOver(true); }}
      onDragLeave={() => setDragOver(false)}
    >
      <FileExplorerHeader
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        onRefresh={loadFileTree}
        onUpload={() => triggerFileUpload()}
        onNewFile={() => createNewFile()}
        onNewFolder={() => createNewFolder()}
      />
      
      {loading ? (
        <FileExplorerSkeleton />
      ) : (
        <FileExplorerContent
          fileTree={fileTree}
          viewMode={viewMode}
          selectedFiles={selectedFiles}
          expandedPaths={expandedPaths}
          onFileSelect={onFileSelect}
          onFileOpen={onFileOpen}
          onFileContextMenu={handleFileContextMenu}
          onPathExpand={handlePathExpand}
        />
      )}
      
      {dragOver && (
        <div className="drag-overlay">
          <div className="drag-message">
            Drop files here to upload
          </div>
        </div>
      )}
    </div>
  );
};
```

### Performance Requirements

#### File Operation Performance
- **File Reading**: <200ms for files up to 10MB
- **File Writing**: <500ms for files up to 10MB
- **Directory Listing**: <300ms for directories with 1000+ files
- **File Search**: <2 seconds across 10,000 files

#### UI Performance
- **File Tree Rendering**: <100ms for 500 visible nodes
- **File Selection**: <50ms response time
- **Drag and Drop**: <16ms frame rate during operations
- **Context Menu**: <100ms display time

### Security Requirements

#### File System Security
- ✅ Sandbox all file operations within allowed directories
- ✅ Validate all file paths to prevent directory traversal
- ✅ Scan uploaded files for malicious content
- ✅ Enforce file size and type restrictions

#### Access Control
- ✅ User-based file access permissions
- ✅ Role-based operation restrictions
- ✅ Secure handling of sensitive files
- ✅ Audit logging for all file operations

## Technical Specifications

### Implementation Details

#### File Watcher System
```typescript
class FileWatcherService {
  private watchers: Map<string, FileWatcher[]> = new Map();
  private pollInterval: number = 1000;
  private checksums: Map<string, string> = new Map();
  
  async watchFile(
    path: string,
    callback: FileWatchCallback,
    options: WatchOptions = {}
  ): Promise<FileWatcher> {
    const watcher: FileWatcher = {
      id: generateId(),
      path,
      callback,
      options,
      isActive: true
    };
    
    const existingWatchers = this.watchers.get(path) || [];
    existingWatchers.push(watcher);
    this.watchers.set(path, existingWatchers);
    
    // Start watching if this is the first watcher for this path
    if (existingWatchers.length === 1) {
      await this.startWatching(path);
    }
    
    return watcher;
  }
  
  private async startWatching(path: string): Promise<void> {
    if ('FileSystemObserver' in window) {
      // Use native file system observer when available
      await this.watchWithObserver(path);
    } else {
      // Fall back to polling
      await this.watchWithPolling(path);
    }
  }
  
  private async watchWithPolling(path: string): Promise<void> {
    const poll = async () => {
      try {
        const fileInfo = await this.fileSystemService.getFileInfo(path);
        const currentChecksum = await this.calculateChecksum(path);
        const previousChecksum = this.checksums.get(path);
        
        if (previousChecksum && currentChecksum !== previousChecksum) {
          await this.notifyWatchers(path, 'modified', fileInfo);
        }
        
        this.checksums.set(path, currentChecksum);
      } catch (error) {
        if (error.code === 'FILE_NOT_FOUND') {
          await this.notifyWatchers(path, 'deleted');
          this.stopWatching(path);
        }
      }
      
      // Schedule next poll
      if (this.watchers.has(path)) {
        setTimeout(poll, this.pollInterval);
      }
    };
    
    setTimeout(poll, this.pollInterval);
  }
  
  private async notifyWatchers(
    path: string,
    event: FileWatchEvent,
    fileInfo?: FileInfo
  ): Promise<void> {
    const watchers = this.watchers.get(path) || [];
    
    for (const watcher of watchers) {
      if (watcher.isActive) {
        try {
          await watcher.callback(event, path, fileInfo);
        } catch (error) {
          console.error(`File watcher callback error:`, error);
        }
      }
    }
  }
}
```

#### Database Schema
```sql
CREATE TABLE file_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  file_path TEXT NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type VARCHAR(100),
  file_hash VARCHAR(64),
  encoding VARCHAR(50),
  line_count INTEGER,
  created_at TIMESTAMP DEFAULT NOW(),
  modified_at TIMESTAMP DEFAULT NOW(),
  accessed_at TIMESTAMP DEFAULT NOW(),
  is_deleted BOOLEAN DEFAULT FALSE,
  
  UNIQUE(user_id, file_path)
);

CREATE TABLE file_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  file_metadata_id UUID NOT NULL REFERENCES file_metadata(id),
  user_id UUID REFERENCES users(id),
  role_id UUID REFERENCES roles(id),
  permission_type VARCHAR(20) NOT NULL CHECK (permission_type IN ('read', 'write', 'delete', 'execute')),
  granted_by UUID NOT NULL REFERENCES users(id),
  granted_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  
  UNIQUE(file_metadata_id, user_id, role_id, permission_type)
);

CREATE TABLE file_operations_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  operation_type VARCHAR(50) NOT NULL,
  file_path TEXT NOT NULL,
  target_path TEXT,
  file_size BIGINT,
  success BOOLEAN NOT NULL,
  error_message TEXT,
  duration_ms INTEGER,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE file_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  file_metadata_id UUID NOT NULL REFERENCES file_metadata(id),
  version_number INTEGER NOT NULL,
  file_hash VARCHAR(64) NOT NULL,
  file_size BIGINT NOT NULL,
  changes_summary TEXT,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(file_metadata_id, version_number)
);

CREATE INDEX idx_file_metadata_user_path ON file_metadata(user_id, file_path);
CREATE INDEX idx_file_operations_log_user_time ON file_operations_log(user_id, created_at);
CREATE INDEX idx_file_permissions_file_user ON file_permissions(file_metadata_id, user_id);
```

#### API Endpoints
```typescript
// File operations
GET /api/files/{path}
PUT /api/files/{path}
DELETE /api/files/{path}
POST /api/files/{path}/copy
POST /api/files/{path}/move

// Directory operations  
GET /api/directories/{path}
POST /api/directories/{path}
DELETE /api/directories/{path}

// File metadata and info
GET /api/files/{path}/info
PUT /api/files/{path}/permissions
GET /api/files/{path}/versions
POST /api/files/{path}/version

// File search and operations
POST /api/files/search
{
  path: string;
  query: string;
  includeContent: boolean;
  fileTypes: string[];
  maxResults: number;
}

POST /api/files/bulk-operation
{
  operations: FileOperation[];
}

// File upload/download
POST /api/files/upload
GET /api/files/{path}/download
POST /api/files/bulk-download
```

## Quality Gates

### Definition of Done

#### File System Validation
- ✅ All file operations work correctly across supported browsers
- ✅ File system security policies are enforced
- ✅ File watching and synchronization function properly
- ✅ Large file handling meets performance requirements

#### UI/UX Validation
- ✅ File explorer is intuitive and responsive
- ✅ Drag-and-drop file operations work smoothly
- ✅ Context menus provide appropriate actions
- ✅ File search returns relevant results quickly

#### Security Validation
- ✅ File access controls prevent unauthorized operations
- ✅ Malicious file detection blocks dangerous uploads
- ✅ Directory traversal attacks are prevented
- ✅ All file operations are properly logged

### Testing Requirements

#### Unit Tests
- File system service operations
- File validation and security checks
- File watcher functionality
- Search algorithm accuracy

#### Integration Tests
- End-to-end file management workflows
- File system API integration
- File explorer UI interactions
- Multi-user file access scenarios

#### Performance Tests
- Large file handling benchmarks
- Concurrent file operation testing
- File search performance evaluation
- UI responsiveness under load

## Risk Assessment

### High Risk Areas

#### Browser API Limitations
- **Risk**: File System Access API not available in all browsers
- **Mitigation**: Progressive enhancement with fallbacks to file input/output
- **Contingency**: Virtual file system implementation for unsupported browsers

#### Security Vulnerabilities
- **Risk**: File system access could lead to security breaches
- **Mitigation**: Strict sandboxing, validation, and access controls
- **Contingency**: Emergency file operation blocking, security audit

#### Performance Issues
- **Risk**: Large files or many files could impact performance
- **Mitigation**: Chunked loading, virtual scrolling, background processing
- **Contingency**: File size limits, operation throttling

### Medium Risk Areas

#### Data Loss
- **Risk**: File operations could result in data loss
- **Mitigation**: File versioning, operation confirmation dialogs, undo functionality
- **Contingency**: Backup systems, file recovery tools

## Success Metrics

### Technical Metrics
- **File Operation Speed**: 90% of operations complete within target times
- **System Reliability**: >99.9% successful file operations
- **Search Accuracy**: >95% relevant results in file searches
- **Security Incidents**: Zero successful security breaches

### User Experience Metrics
- **File Manager Usage**: >80% of users regularly use file management features
- **Workflow Efficiency**: 50% faster file organization workflows
- **Feature Discovery**: >70% of users discover advanced file features
- **User Satisfaction**: >4.4/5 rating for file management experience

### Business Metrics
- **Developer Productivity**: 35% improvement in file-based development tasks
- **Platform Completeness**: File management enables full development workflows
- **User Engagement**: File features increase session duration
- **Support Reduction**: Fewer file-related support requests

## Implementation Timeline

### Week 1: Core File System
- **Days 1-2**: File system service architecture and basic operations
- **Days 3-4**: File security validation and access control
- **Day 5**: File watcher system and synchronization

### Week 2: UI and Advanced Features  
- **Days 1-2**: File explorer interface and basic operations
- **Days 3-4**: Advanced features (search, bulk operations, upload/download)
- **Day 5**: Testing, optimization, and performance tuning

## Follow-up Stories

### Immediate Next Stories
- **D2.3a**: Git Integration (builds on file system for version control)
- **D2.4a**: Project Management (uses file system for project organization)
- **D3.2a**: Code Analysis (analyzes files managed by this system)

### Future Enhancements
- **Advanced File Operations**: File compression, encryption, synchronization
- **Cloud Storage Integration**: Support for cloud storage providers
- **Collaborative File Editing**: Real-time collaborative file operations
- **File System Extensions**: Plugin system for custom file handlers

This file system management foundation enables all file-based development workflows in the platform.