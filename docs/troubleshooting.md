# Troubleshooting Guide

This guide helps resolve common issues with the unified AI assistant platform development setup.

## Quick Fixes

### 1. Development Setup Issues

#### Problem: `./scripts/dev-setup.sh` fails with workspace errors
```bash
# Solution: Ensure pnpm-workspace.yaml exists and is properly configured
ls -la pnpm-workspace.yaml
cat pnpm-workspace.yaml
```

#### Problem: pnpm version mismatch warnings
```bash
# Solution: Update to the correct pnpm version
npm install -g pnpm@10.13.1
# Or use corepack (recommended)
corepack enable
corepack prepare pnpm@10.13.1 --activate
```

#### Problem: ESLint configuration errors with version 9
```bash
# Solution: Ensure eslint.config.js exists (not .eslintrc.json)
ls -la eslint.config.js
# If missing, the setup script should have created it
```

### 2. Build Issues

#### Problem: `pnpm run build:packages` fails
```bash
# Solution 1: Clean and reinstall dependencies
pnpm clean
rm -rf node_modules
rm pnpm-lock.yaml
pnpm install

# Solution 2: Build packages individually
cd packages/types && pnpm run build
cd packages/events && pnpm run build
cd packages/ai && pnpm run build
```

#### Problem: TypeScript compilation errors
```bash
# Solution: Check TypeScript configuration
pnpm run type-check
# Fix any type errors in the output
```

### 3. Turbo Issues

#### Problem: Turbo pipeline/tasks configuration error
```bash
# Solution: Ensure turbo.json uses "tasks" not "pipeline"
grep -n "tasks\|pipeline" turbo.json
# Should show "tasks" field, not "pipeline"
```

#### Problem: Turbo cache issues
```bash
# Solution: Clear Turbo cache
pnpm turbo clean
rm -rf .turbo
```

### 4. Workspace Issues

#### Problem: Package not found in workspace
```bash
# Solution: Check workspace configuration
pnpm list --depth=0
# Verify all packages are listed

# Check pnpm-workspace.yaml
cat pnpm-workspace.yaml
```

#### Problem: Dependency resolution errors
```bash
# Solution: Use workspace protocol for internal dependencies
# In package.json, use: "@unified-assistant/types": "workspace:*"
```

## Common Error Messages

### Error: "workspaces field is not supported"
**Cause**: Using workspaces field in package.json instead of pnpm-workspace.yaml
**Solution**: Remove workspaces field from package.json, ensure pnpm-workspace.yaml exists

### Error: "Cannot find module '@unified-assistant/types'"
**Cause**: Package not built or workspace not properly configured
**Solution**: 
```bash
cd packages/types && pnpm run build
pnpm install
```

### Error: "ESLint configuration is invalid"
**Cause**: Using old ESLint configuration format
**Solution**: Ensure eslint.config.js exists with flat config format

### Error: "Turbo could not find a task"
**Cause**: Task not defined in turbo.json
**Solution**: Add missing task to turbo.json tasks section

## Development Environment Issues

### Node.js Version Issues
```bash
# Check Node.js version
node --version
# Should be 18.0.0 or higher

# Use nvm to manage Node.js versions
nvm install 18
nvm use 18
```

### Port Conflicts
```bash
# Check what's running on ports
lsof -i :3000
lsof -i :3001

# Kill processes if needed
kill -9 <PID>
```

### Permission Issues
```bash
# Fix script permissions
chmod +x scripts/dev-setup.sh
chmod +x scripts/dev-start.sh
```

## Module-Specific Issues

### Agent-Inbox Module
```bash
# Check if module exists and has package.json
ls -la agent-inbox/package.json

# Install dependencies if missing
cd agent-inbox && pnpm install
```

### Foto-Fun Module
```bash
# Check module structure
ls -la foto-fun/

# Ensure dependencies are installed
cd foto-fun && pnpm install
```

### Shared Packages
```bash
# Verify all shared packages have package.json
ls -la packages/*/package.json

# Build all packages
pnpm run build:packages
```

## Performance Issues

### Slow Build Times
```bash
# Use Turbo cache
pnpm turbo build --cache-dir=.turbo

# Parallel builds
pnpm turbo build --parallel
```

### Memory Issues
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
```

## Testing Issues

### Integration Tests Failing
```bash
# Check test setup
ls -la tests/setup/integration-setup.ts

# Run tests with verbose output
pnpm run test:integration --verbose
```

### Jest Configuration Issues
```bash
# Check Jest config
ls -la jest.integration.config.js

# Clear Jest cache
pnpm jest --clearCache
```

## Environment Variables

### Missing Environment Variables
```bash
# Copy example environment file
cp .env.example .env.local

# Check required variables
grep -v "^#" .env.example | grep "="
```

### API Key Issues
```bash
# Verify API keys are set
echo $OPENAI_API_KEY
echo $ANTHROPIC_API_KEY

# Check .env.local file
cat .env.local | grep API_KEY
```

## Getting Help

### Debug Information
When reporting issues, include:

```bash
# System information
node --version
pnpm --version
npm --version

# Project information
cat package.json | grep version
cat pnpm-workspace.yaml

# Error logs
pnpm run build:packages 2>&1 | tee build.log
```

### Log Files
Check these locations for detailed logs:
- `logs/` directory (created by dev scripts)
- `.turbo/` directory for Turbo logs
- Individual module logs in their directories

### Support Channels
1. Check GitHub Issues: https://github.com/PMStander/assistant/issues
2. Review documentation in `docs/` directory
3. Check FINAL_IMPLEMENTATION_SUMMARY.md for current status

## Reset Everything

If all else fails, complete reset:

```bash
# Stop all processes
pnpm run dev:stop

# Clean everything
pnpm clean
rm -rf node_modules
rm -rf .turbo
rm -rf packages/*/node_modules
rm -rf packages/*/dist
rm pnpm-lock.yaml

# Reinstall and setup
pnpm install
./scripts/dev-setup.sh
```

## Verification Checklist

After fixing issues, verify:

- [ ] `pnpm --version` shows 10.13.1+
- [ ] `pnpm-workspace.yaml` exists and is valid
- [ ] `eslint.config.js` exists (not .eslintrc.json)
- [ ] `turbo.json` uses "tasks" field
- [ ] All packages have package.json files
- [ ] `pnpm install` completes without errors
- [ ] `pnpm run build:packages` succeeds
- [ ] `./scripts/dev-setup.sh` completes successfully
- [ ] `pnpm run dev` starts all modules
