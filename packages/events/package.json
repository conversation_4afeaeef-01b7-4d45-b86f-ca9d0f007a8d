{"name": "@unified-assistant/events", "version": "0.1.0", "description": "Event-driven communication system for the unified AI assistant platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"@unified-assistant/types": "workspace:*", "uuid": "^11.0.3", "eventemitter3": "^5.0.1"}, "devDependencies": {"@types/node": "^20", "@types/uuid": "^10.0.0", "typescript": "^5", "eslint": "^8", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.8.1", "jest": "^29"}, "peerDependencies": {"react": "^18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./bus": {"types": "./dist/bus/index.d.ts", "import": "./dist/bus/index.js", "require": "./dist/bus/index.js"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "import": "./dist/hooks/index.js", "require": "./dist/hooks/index.js"}}, "files": ["dist", "README.md"]}