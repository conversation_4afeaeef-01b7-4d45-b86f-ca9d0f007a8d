import EventEmitter from 'eventemitter3';
import {
  EventBus,
  PlatformEvent,
  EventHandler,
  EventSubscription,
  EventBusMetrics,
  EventStore,
  EventMiddleware
} from '@unified-assistant/types';
import { EventRouter } from './event-router';
import { v4 as uuidv4 } from 'uuid';

export class PlatformEventBus implements EventBus {
  private emitter: EventEmitter;
  private subscriptions: Map<string, EventSubscription>;
  private eventHistory: PlatformEvent[];
  private middleware: EventMiddleware[];
  private router: EventRouter;
  private store?: EventStore;
  private metrics: EventBusMetrics;
  private paused: boolean = false;

  constructor(store?: EventStore) {
    this.emitter = new EventEmitter();
    this.subscriptions = new Map();
    this.eventHistory = [];
    this.middleware = [];
    this.router = new EventRouter(this);
    this.store = store;
    this.metrics = {
      totalEvents: 0,
      eventsPerSecond: 0,
      activeSubscriptions: 0,
      eventTypes: [],
      errorRate: 0,
      averageProcessingTime: 0
    };

    // Track metrics
    this.startMetricsTracking();
  }

  /**
   * Emit an event asynchronously
   */
  async emit<T = any>(event: PlatformEvent): Promise<void> {
    if (this.paused) {
      return;
    }

    const startTime = Date.now();

    try {
      // Process through middleware
      await this.processMiddleware(event);

      // Store event if persistence is enabled
      if (this.store && event.persistent) {
        await this.store.save(event);
      }

      // Add to history
      this.addToHistory(event);

      // Route event to appropriate targets
      await this.router.routeEvent(event);

      // Emit to subscribers
      this.emitter.emit(event.type, event);
      this.emitter.emit('*', event); // Wildcard listeners

      // Update metrics
      this.updateMetrics(event, Date.now() - startTime);

    } catch (error) {
      this.metrics.errorRate++;
      throw error;
    }
  }

  /**
   * Emit an event synchronously
   */
  emitSync<T = any>(event: PlatformEvent): void {
    if (this.paused) {
      return;
    }

    try {
      // Add to history
      this.addToHistory(event);

      // Emit to subscribers
      this.emitter.emit(event.type, event);
      this.emitter.emit('*', event);

      // Update metrics
      this.updateMetrics(event, 0);

    } catch (error) {
      this.metrics.errorRate++;
      throw error;
    }
  }

  /**
   * Subscribe to events
   */
  on<T = any>(eventType: string, handler: EventHandler<T>): EventSubscription {
    const subscription: EventSubscription = {
      id: uuidv4(),
      eventType,
      handler,
      active: true,
      createdAt: new Date(),
      triggerCount: 0
    };

    // Wrap handler to track metrics
    const wrappedHandler = async (event: PlatformEvent) => {
      if (!subscription.active) return;

      try {
        // Apply filter if provided
        if (handler.options?.filter && !handler.options.filter(event)) {
          return;
        }

        await handler.handler(event);
        
        subscription.triggerCount++;
        subscription.lastTriggered = new Date();

        // Remove if once option is set
        if (handler.options?.once) {
          this.off(subscription.id);
        }

      } catch (error) {
        console.error(`Error in event handler ${handler.id}:`, error);
      }
    };

    this.emitter.on(eventType, wrappedHandler);
    this.subscriptions.set(subscription.id, subscription);
    this.metrics.activeSubscriptions++;

    return subscription;
  }

  /**
   * Subscribe to events (one-time)
   */
  once<T = any>(eventType: string, handler: EventHandler<T>): EventSubscription {
    const onceHandler: EventHandler<T> = {
      ...handler,
      options: { ...handler.options, once: true }
    };

    return this.on(eventType, onceHandler);
  }

  /**
   * Unsubscribe from events
   */
  off(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.active = false;
      this.emitter.removeAllListeners(subscription.eventType);
      this.subscriptions.delete(subscriptionId);
      this.metrics.activeSubscriptions--;
    }
  }

  /**
   * Get active subscriptions
   */
  getSubscriptions(eventType?: string): EventSubscription[] {
    const subscriptions = Array.from(this.subscriptions.values());
    
    if (eventType) {
      return subscriptions.filter(sub => sub.eventType === eventType);
    }
    
    return subscriptions;
  }

  /**
   * Get event history
   */
  getEventHistory(eventType?: string, limit: number = 100): PlatformEvent[] {
    let events = this.eventHistory;
    
    if (eventType) {
      events = events.filter(event => event.type === eventType);
    }
    
    return events.slice(-limit);
  }

  /**
   * Clear all subscriptions and history
   */
  clear(): void {
    this.emitter.removeAllListeners();
    this.subscriptions.clear();
    this.eventHistory = [];
    this.metrics.activeSubscriptions = 0;
  }

  /**
   * Pause event processing
   */
  pause(): void {
    this.paused = true;
  }

  /**
   * Resume event processing
   */
  resume(): void {
    this.paused = false;
  }

  /**
   * Get event bus metrics
   */
  getMetrics(): EventBusMetrics {
    return { ...this.metrics };
  }

  /**
   * Add middleware
   */
  addMiddleware(middleware: EventMiddleware): void {
    this.middleware.push(middleware);
    this.middleware.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Remove middleware
   */
  removeMiddleware(name: string): void {
    this.middleware = this.middleware.filter(m => m.name !== name);
  }

  /**
   * Get event router
   */
  getRouter(): EventRouter {
    return this.router;
  }

  /**
   * Add route to event router
   */
  addRoute(pattern: string | RegExp, target: string, options?: {
    transform?: (event: PlatformEvent) => PlatformEvent;
    condition?: (event: PlatformEvent) => boolean;
  }): void {
    this.router.addRoute({
      pattern,
      target,
      transform: options?.transform,
      condition: options?.condition
    });
  }

  /**
   * Process event through middleware chain
   */
  private async processMiddleware(event: PlatformEvent): Promise<void> {
    let index = 0;

    const next = async (): Promise<void> => {
      if (index >= this.middleware.length) return;
      
      const middleware = this.middleware[index++];
      await middleware.process(event, next);
    };

    await next();
  }

  /**
   * Add event to history with TTL cleanup
   */
  private addToHistory(event: PlatformEvent): void {
    this.eventHistory.push(event);

    // Cleanup old events (keep last 1000)
    if (this.eventHistory.length > 1000) {
      this.eventHistory = this.eventHistory.slice(-1000);
    }

    // Cleanup expired events
    if (event.ttl) {
      setTimeout(() => {
        this.eventHistory = this.eventHistory.filter(e => e.id !== event.id);
      }, event.ttl);
    }
  }

  /**
   * Update metrics
   */
  private updateMetrics(event: PlatformEvent, processingTime: number): void {
    this.metrics.totalEvents++;
    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime + processingTime) / 2;

    if (!this.metrics.eventTypes.includes(event.type)) {
      this.metrics.eventTypes.push(event.type);
    }
  }

  /**
   * Start metrics tracking
   */
  private startMetricsTracking(): void {
    let lastEventCount = 0;
    
    setInterval(() => {
      const currentEventCount = this.metrics.totalEvents;
      this.metrics.eventsPerSecond = currentEventCount - lastEventCount;
      lastEventCount = currentEventCount;
    }, 1000);
  }
}
