import { 
  PlatformEvent, 
  EventRoute, 
  EventBus 
} from '@unified-assistant/types';

/**
 * Event Router
 * 
 * LEVER approach implementation:
 * L - Leverage existing event patterns from modules
 * E - Extend with intelligent routing and transformation
 * V - Verify through route validation and error handling
 * E - Eliminate duplicate routing logic across modules
 * R - Reduce complexity through declarative route configuration
 */
export class EventRouter {
  private routes: EventRoute[] = [];
  private eventBus: EventBus;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.setupDefaultRoutes();
  }

  /**
   * Add a route for event routing
   */
  addRoute(route: EventRoute): void {
    this.validateRoute(route);
    this.routes.push(route);
    
    // Sort routes by specificity (more specific patterns first)
    this.routes.sort((a, b) => {
      const aSpecificity = this.getRouteSpecificity(a);
      const bSpecificity = this.getRouteSpecificity(b);
      return bSpecificity - aSpecificity;
    });
  }

  /**
   * Remove a route
   */
  removeRoute(pattern: string | RegExp): void {
    this.routes = this.routes.filter(route => 
      route.pattern !== pattern
    );
  }

  /**
   * Route an event to appropriate targets
   */
  async routeEvent(event: PlatformEvent): Promise<PlatformEvent[]> {
    const routedEvents: PlatformEvent[] = [];

    for (const route of this.routes) {
      if (this.matchesRoute(event, route)) {
        try {
          // Check condition if provided
          if (route.condition && !route.condition(event)) {
            continue;
          }

          // Transform event if transformer provided
          let routedEvent = event;
          if (route.transform) {
            routedEvent = route.transform(event);
          }

          // Set target if not already set
          if (!routedEvent.target) {
            routedEvent = { ...routedEvent, target: route.target };
          }

          routedEvents.push(routedEvent);

          // Emit routed event
          await this.eventBus.emit(routedEvent);

        } catch (error) {
          console.error(`Error routing event ${event.type}:`, error);
        }
      }
    }

    return routedEvents;
  }

  /**
   * Get all routes
   */
  getRoutes(): EventRoute[] {
    return [...this.routes];
  }

  /**
   * Get routes for specific pattern
   */
  getRoutesForPattern(pattern: string): EventRoute[] {
    return this.routes.filter(route => {
      if (typeof route.pattern === 'string') {
        return route.pattern === pattern;
      }
      return route.pattern.test(pattern);
    });
  }

  /**
   * Setup default cross-module routes
   */
  private setupDefaultRoutes(): void {
    // AI request routing
    this.addRoute({
      pattern: /^ai:request/,
      target: 'ai-orchestrator',
      transform: (event) => ({
        ...event,
        type: 'ai:process_request',
        metadata: { ...event.metadata, routed: true }
      })
    });

    // Module communication routing
    this.addRoute({
      pattern: /^module:message/,
      target: 'module-manager',
      condition: (event) => !!event.target
    });

    // Context sharing routing
    this.addRoute({
      pattern: /^context:(share|request)/,
      target: 'context-manager',
      transform: (event) => ({
        ...event,
        metadata: { ...event.metadata, contextRoute: true }
      })
    });

    // Workflow orchestration routing
    this.addRoute({
      pattern: /^workflow:/,
      target: 'workflow-orchestrator',
      transform: (event) => ({
        ...event,
        priority: 'high' as const,
        metadata: { ...event.metadata, workflowEvent: true }
      })
    });

    // Error handling routing
    this.addRoute({
      pattern: /^error:|:error$/,
      target: 'error-handler',
      transform: (event) => ({
        ...event,
        priority: 'critical' as const,
        persistent: true
      })
    });

    // Metrics and monitoring routing
    this.addRoute({
      pattern: /^metrics:|:metrics$/,
      target: 'metrics-collector',
      condition: (event) => event.source !== 'metrics-collector' // Prevent loops
    });

    // User action routing
    this.addRoute({
      pattern: /^user:/,
      target: 'user-manager',
      transform: (event) => ({
        ...event,
        metadata: { 
          ...event.metadata, 
          timestamp: event.timestamp,
          userAction: true 
        }
      })
    });

    // Cross-module workflow routing
    this.addRoute({
      pattern: 'workflow:content-creation',
      target: 'open-canvas',
      transform: (event) => ({
        ...event,
        type: 'module:message',
        payload: {
          action: 'create-document',
          ...event.payload
        }
      })
    });

    this.addRoute({
      pattern: 'workflow:image-generation',
      target: 'foto-fun',
      transform: (event) => ({
        ...event,
        type: 'module:message',
        payload: {
          action: 'generate-image',
          ...event.payload
        }
      })
    });

    this.addRoute({
      pattern: 'workflow:task-management',
      target: 'vibe-kanban',
      transform: (event) => ({
        ...event,
        type: 'module:message',
        payload: {
          action: 'create-task',
          ...event.payload
        }
      })
    });

    this.addRoute({
      pattern: 'workflow:code-generation',
      target: 'vcode',
      transform: (event) => ({
        ...event,
        type: 'module:message',
        payload: {
          action: 'generate-code',
          ...event.payload
        }
      })
    });
  }

  /**
   * Check if event matches route pattern
   */
  private matchesRoute(event: PlatformEvent, route: EventRoute): boolean {
    if (typeof route.pattern === 'string') {
      return event.type === route.pattern;
    }
    
    if (route.pattern instanceof RegExp) {
      return route.pattern.test(event.type);
    }
    
    return false;
  }

  /**
   * Get route specificity for sorting
   */
  private getRouteSpecificity(route: EventRoute): number {
    if (typeof route.pattern === 'string') {
      return route.pattern.length; // Longer strings are more specific
    }
    
    if (route.pattern instanceof RegExp) {
      return route.pattern.source.length; // Longer regex patterns are more specific
    }
    
    return 0;
  }

  /**
   * Validate route configuration
   */
  private validateRoute(route: EventRoute): void {
    if (!route.pattern) {
      throw new Error('Route pattern is required');
    }
    
    if (!route.target) {
      throw new Error('Route target is required');
    }
    
    if (typeof route.pattern !== 'string' && !(route.pattern instanceof RegExp)) {
      throw new Error('Route pattern must be string or RegExp');
    }
  }

  /**
   * Create route builder for fluent API
   */
  static builder(): RouteBuilder {
    return new RouteBuilder();
  }
}

/**
 * Route Builder for fluent API
 */
export class RouteBuilder {
  private route: Partial<EventRoute> = {};

  pattern(pattern: string | RegExp): RouteBuilder {
    this.route.pattern = pattern;
    return this;
  }

  target(target: string): RouteBuilder {
    this.route.target = target;
    return this;
  }

  transform(transformer: (event: PlatformEvent) => PlatformEvent): RouteBuilder {
    this.route.transform = transformer;
    return this;
  }

  condition(condition: (event: PlatformEvent) => boolean): RouteBuilder {
    this.route.condition = condition;
    return this;
  }

  build(): EventRoute {
    if (!this.route.pattern || !this.route.target) {
      throw new Error('Pattern and target are required');
    }
    
    return this.route as EventRoute;
  }
}

/**
 * Pre-built route configurations for common patterns
 */
export const CommonRoutes = {
  /**
   * Route AI requests to orchestrator
   */
  aiRequests: (): EventRoute => 
    EventRouter.builder()
      .pattern(/^ai:/)
      .target('ai-orchestrator')
      .build(),

  /**
   * Route module messages to target modules
   */
  moduleMessages: (): EventRoute =>
    EventRouter.builder()
      .pattern('module:message')
      .target('module-manager')
      .condition(event => !!event.target)
      .build(),

  /**
   * Route errors to error handler
   */
  errorHandling: (): EventRoute =>
    EventRouter.builder()
      .pattern(/error/)
      .target('error-handler')
      .transform(event => ({ ...event, priority: 'critical' as const }))
      .build(),

  /**
   * Route workflow events to orchestrator
   */
  workflowOrchestration: (): EventRoute =>
    EventRouter.builder()
      .pattern(/^workflow:/)
      .target('workflow-orchestrator')
      .transform(event => ({ ...event, priority: 'high' as const }))
      .build()
};
