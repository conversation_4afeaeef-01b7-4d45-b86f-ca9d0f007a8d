import { EventMiddleware, PlatformEvent, EventSchema } from '@unified-assistant/types';
import { z } from 'zod';

/**
 * Validation Middleware
 * 
 * LEVER approach implementation:
 * L - Leverage existing Zod validation patterns
 * E - Extend with comprehensive event validation and sanitization
 * V - Verify through schema validation and type checking
 * E - Eliminate invalid events before processing
 * R - Reduce errors through early validation
 */
export class ValidationMiddleware implements EventMiddleware {
  public readonly name = 'validation';
  public readonly priority = 200; // Very high priority to validate early

  private strictMode: boolean;
  private customSchemas: Map<string, z.ZodSchema> = new Map();
  private validationRules: Map<string, ValidationRule[]> = new Map();

  constructor(options: {
    strictMode?: boolean;
    customSchemas?: Record<string, z.ZodSchema>;
    validationRules?: Record<string, ValidationRule[]>;
  } = {}) {
    this.strictMode = options.strictMode || false;
    
    if (options.customSchemas) {
      for (const [eventType, schema] of Object.entries(options.customSchemas)) {
        this.customSchemas.set(eventType, schema);
      }
    }

    if (options.validationRules) {
      for (const [eventType, rules] of Object.entries(options.validationRules)) {
        this.validationRules.set(eventType, rules);
      }
    }

    this.setupDefaultValidationRules();
  }

  /**
   * Process event through validation middleware
   */
  async process(event: PlatformEvent, next: () => Promise<void>): Promise<void> {
    try {
      // Validate basic event structure
      this.validateEventStructure(event);

      // Validate event payload
      await this.validateEventPayload(event);

      // Apply custom validation rules
      await this.applyValidationRules(event);

      // Sanitize event data
      this.sanitizeEvent(event);

      // Continue to next middleware
      await next();

    } catch (error) {
      const validationError = new Error(
        `Event validation failed for ${event.type}: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      
      if (this.strictMode) {
        throw validationError;
      } else {
        console.warn(validationError.message);
        // Continue processing in non-strict mode
        await next();
      }
    }
  }

  /**
   * Validate basic event structure using Zod schema
   */
  private validateEventStructure(event: PlatformEvent): void {
    try {
      EventSchema.parse(event);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const issues = error.issues.map(issue => 
          `${issue.path.join('.')}: ${issue.message}`
        ).join(', ');
        throw new Error(`Schema validation failed: ${issues}`);
      }
      throw error;
    }
  }

  /**
   * Validate event payload using custom schemas
   */
  private async validateEventPayload(event: PlatformEvent): Promise<void> {
    const schema = this.getSchemaForEventType(event.type);
    if (!schema) {
      return; // No custom schema defined
    }

    try {
      schema.parse(event.payload);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const issues = error.issues.map(issue => 
          `payload.${issue.path.join('.')}: ${issue.message}`
        ).join(', ');
        throw new Error(`Payload validation failed: ${issues}`);
      }
      throw error;
    }
  }

  /**
   * Apply custom validation rules
   */
  private async applyValidationRules(event: PlatformEvent): Promise<void> {
    const rules = this.getValidationRulesForEventType(event.type);
    
    for (const rule of rules) {
      try {
        const isValid = await rule.validate(event);
        if (!isValid) {
          throw new Error(rule.message || `Validation rule '${rule.name}' failed`);
        }
      } catch (error) {
        throw new Error(`Rule '${rule.name}' failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }

  /**
   * Sanitize event data
   */
  private sanitizeEvent(event: PlatformEvent): void {
    // Remove potentially dangerous properties
    if (event.payload && typeof event.payload === 'object') {
      this.sanitizeObject(event.payload);
    }

    // Ensure required fields are present
    if (!event.timestamp) {
      event.timestamp = new Date();
    }

    if (!event.priority) {
      event.priority = 'normal';
    }

    if (event.persistent === undefined) {
      event.persistent = false;
    }
  }

  /**
   * Recursively sanitize object properties
   */
  private sanitizeObject(obj: any): void {
    if (!obj || typeof obj !== 'object') {
      return;
    }

    // Remove dangerous properties
    const dangerousProps = ['__proto__', 'constructor', 'prototype'];
    for (const prop of dangerousProps) {
      if (prop in obj) {
        delete obj[prop];
      }
    }

    // Recursively sanitize nested objects
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        this.sanitizeObject(value);
      }
      
      // Sanitize string values
      if (typeof value === 'string') {
        obj[key] = this.sanitizeString(value);
      }
    }
  }

  /**
   * Sanitize string values
   */
  private sanitizeString(str: string): string {
    // Remove potential script tags and dangerous content
    return str
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  /**
   * Get schema for event type
   */
  private getSchemaForEventType(eventType: string): z.ZodSchema | undefined {
    // Check for exact match first
    if (this.customSchemas.has(eventType)) {
      return this.customSchemas.get(eventType);
    }

    // Check for pattern matches
    for (const [pattern, schema] of this.customSchemas.entries()) {
      if (pattern.includes('*') || pattern.includes(':')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/:/g, ':'));
        if (regex.test(eventType)) {
          return schema;
        }
      }
    }

    return undefined;
  }

  /**
   * Get validation rules for event type
   */
  private getValidationRulesForEventType(eventType: string): ValidationRule[] {
    const rules: ValidationRule[] = [];

    // Get exact matches
    const exactRules = this.validationRules.get(eventType);
    if (exactRules) {
      rules.push(...exactRules);
    }

    // Get pattern matches
    for (const [pattern, patternRules] of this.validationRules.entries()) {
      if (pattern.includes('*') || pattern.includes(':')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/:/g, ':'));
        if (regex.test(eventType)) {
          rules.push(...patternRules);
        }
      }
    }

    return rules;
  }

  /**
   * Setup default validation rules
   */
  private setupDefaultValidationRules(): void {
    // AI request validation
    this.addValidationRule('ai:*', {
      name: 'ai-request-validation',
      message: 'AI requests must have valid request data',
      validate: async (event) => {
        if (event.type.startsWith('ai:request')) {
          return !!(event.payload?.request || event.payload?.messages || event.payload?.prompt);
        }
        return true;
      }
    });

    // Module message validation
    this.addValidationRule('module:message', {
      name: 'module-message-validation',
      message: 'Module messages must have target and action',
      validate: async (event) => {
        return !!(event.target && event.payload?.action);
      }
    });

    // Context validation
    this.addValidationRule('context:*', {
      name: 'context-validation',
      message: 'Context events must have conversationId',
      validate: async (event) => {
        return !!(event.payload?.conversationId || event.payload?.context?.conversationId);
      }
    });

    // Workflow validation
    this.addValidationRule('workflow:*', {
      name: 'workflow-validation',
      message: 'Workflow events must have workflowId',
      validate: async (event) => {
        return !!(event.payload?.workflowId || event.payload?.context?.workflowId);
      }
    });

    // Error event validation
    this.addValidationRule('*:error', {
      name: 'error-event-validation',
      message: 'Error events must have error information',
      validate: async (event) => {
        return !!(event.payload?.error || event.payload?.message);
      }
    });
  }

  /**
   * Add custom validation rule
   */
  addValidationRule(eventType: string, rule: ValidationRule): void {
    const rules = this.validationRules.get(eventType) || [];
    rules.push(rule);
    this.validationRules.set(eventType, rules);
  }

  /**
   * Add custom schema
   */
  addCustomSchema(eventType: string, schema: z.ZodSchema): void {
    this.customSchemas.set(eventType, schema);
  }

  /**
   * Remove validation rule
   */
  removeValidationRule(eventType: string, ruleName: string): void {
    const rules = this.validationRules.get(eventType);
    if (rules) {
      const filteredRules = rules.filter(rule => rule.name !== ruleName);
      this.validationRules.set(eventType, filteredRules);
    }
  }

  /**
   * Get validation statistics
   */
  getValidationStats(): {
    totalSchemas: number;
    totalRules: number;
    strictMode: boolean;
  } {
    let totalRules = 0;
    for (const rules of this.validationRules.values()) {
      totalRules += rules.length;
    }

    return {
      totalSchemas: this.customSchemas.size,
      totalRules,
      strictMode: this.strictMode
    };
  }
}

/**
 * Validation Rule Interface
 */
export interface ValidationRule {
  name: string;
  message?: string;
  validate: (event: PlatformEvent) => Promise<boolean> | boolean;
}

/**
 * Pre-configured validation middleware instances
 */
export const ValidationPresets = {
  /**
   * Strict validation - fails on any validation error
   */
  strict: () => new ValidationMiddleware({
    strictMode: true
  }),

  /**
   * Lenient validation - warns but continues on validation errors
   */
  lenient: () => new ValidationMiddleware({
    strictMode: false
  }),

  /**
   * AI-focused validation - strict validation for AI events
   */
  aiStrict: () => {
    const middleware = new ValidationMiddleware({ strictMode: true });
    
    // Add AI-specific schemas
    middleware.addCustomSchema('ai:request', z.object({
      request: z.object({
        type: z.enum(['chat', 'completion', 'embedding', 'image']),
        messages: z.array(z.object({
          role: z.enum(['user', 'assistant', 'system']),
          content: z.string()
        })).optional(),
        prompt: z.string().optional()
      })
    }));

    return middleware;
  }
};
