import { EventMiddleware, PlatformEvent } from '@unified-assistant/types';

/**
 * Logging Middleware
 * 
 * LEVER approach implementation:
 * L - Leverage existing logging patterns
 * E - Extend with structured event logging and metrics
 * V - Verify through comprehensive event tracking
 * E - Eliminate duplicate logging across modules
 * R - Reduce complexity through centralized event logging
 */
export class LoggingMiddleware implements EventMiddleware {
  public readonly name = 'logging';
  public readonly priority = 100; // High priority to log early

  private logLevel: 'debug' | 'info' | 'warn' | 'error';
  private enabledEventTypes: Set<string>;
  private excludedEventTypes: Set<string>;
  private logToConsole: boolean;
  private logToFile: boolean;
  private logFilePath?: string;

  constructor(options: {
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    enabledEventTypes?: string[];
    excludedEventTypes?: string[];
    logToConsole?: boolean;
    logToFile?: boolean;
    logFilePath?: string;
  } = {}) {
    this.logLevel = options.logLevel || 'info';
    this.enabledEventTypes = new Set(options.enabledEventTypes || []);
    this.excludedEventTypes = new Set(options.excludedEventTypes || []);
    this.logToConsole = options.logToConsole !== false;
    this.logToFile = options.logToFile || false;
    this.logFilePath = options.logFilePath;
  }

  /**
   * Process event through logging middleware
   */
  async process(event: PlatformEvent, next: () => Promise<void>): Promise<void> {
    const startTime = Date.now();

    try {
      // Log event start
      this.logEvent('start', event);

      // Continue to next middleware
      await next();

      // Log successful completion
      const duration = Date.now() - startTime;
      this.logEvent('complete', event, { duration });

    } catch (error) {
      // Log error
      const duration = Date.now() - startTime;
      this.logEvent('error', event, { 
        duration, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      
      // Re-throw error to maintain error flow
      throw error;
    }
  }

  /**
   * Log event with appropriate level and format
   */
  private logEvent(
    stage: 'start' | 'complete' | 'error',
    event: PlatformEvent,
    metadata?: Record<string, any>
  ): void {
    // Check if event type should be logged
    if (!this.shouldLogEvent(event)) {
      return;
    }

    const logEntry = this.formatLogEntry(stage, event, metadata);

    // Determine log level based on event and stage
    const level = this.getLogLevel(stage, event);

    // Log to console if enabled
    if (this.logToConsole) {
      this.logToConsoleWithLevel(level, logEntry);
    }

    // Log to file if enabled
    if (this.logToFile && this.logFilePath) {
      this.logToFileSystem(level, logEntry);
    }
  }

  /**
   * Check if event should be logged
   */
  private shouldLogEvent(event: PlatformEvent): boolean {
    // If specific event types are enabled, only log those
    if (this.enabledEventTypes.size > 0) {
      return this.enabledEventTypes.has(event.type);
    }

    // If event type is excluded, don't log
    if (this.excludedEventTypes.has(event.type)) {
      return false;
    }

    return true;
  }

  /**
   * Format log entry
   */
  private formatLogEntry(
    stage: 'start' | 'complete' | 'error',
    event: PlatformEvent,
    metadata?: Record<string, any>
  ): string {
    const timestamp = new Date().toISOString();
    const eventInfo = {
      timestamp,
      stage,
      eventId: event.id,
      eventType: event.type,
      source: event.source,
      target: event.target,
      priority: event.priority,
      persistent: event.persistent,
      ...metadata
    };

    // Create structured log message
    const logMessage = `[${timestamp}] EVENT_${stage.toUpperCase()}: ${event.type}`;
    const logDetails = JSON.stringify(eventInfo, null, 2);

    return `${logMessage}\n${logDetails}`;
  }

  /**
   * Determine appropriate log level
   */
  private getLogLevel(
    stage: 'start' | 'complete' | 'error',
    event: PlatformEvent
  ): 'debug' | 'info' | 'warn' | 'error' {
    // Error stage always logs as error
    if (stage === 'error') {
      return 'error';
    }

    // Critical priority events log as warn
    if (event.priority === 'critical') {
      return 'warn';
    }

    // High priority events log as info
    if (event.priority === 'high') {
      return 'info';
    }

    // Error events log as warn
    if (event.type.includes('error')) {
      return 'warn';
    }

    // Default to debug for start, info for complete
    return stage === 'start' ? 'debug' : 'info';
  }

  /**
   * Log to console with appropriate level
   */
  private logToConsoleWithLevel(
    level: 'debug' | 'info' | 'warn' | 'error',
    message: string
  ): void {
    // Check if current log level allows this message
    const levelPriority = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };

    if (levelPriority[level] < levelPriority[this.logLevel]) {
      return;
    }

    // Log with appropriate console method
    switch (level) {
      case 'debug':
        console.debug(message);
        break;
      case 'info':
        console.info(message);
        break;
      case 'warn':
        console.warn(message);
        break;
      case 'error':
        console.error(message);
        break;
    }
  }

  /**
   * Log to file system (placeholder implementation)
   */
  private logToFileSystem(
    level: 'debug' | 'info' | 'warn' | 'error',
    message: string
  ): void {
    // In a real implementation, this would write to a file
    // For now, we'll just add a file prefix to console output
    if (this.logToConsole) {
      console.log(`[FILE:${this.logFilePath}] ${message}`);
    }
  }

  /**
   * Update logging configuration
   */
  updateConfig(options: {
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    enabledEventTypes?: string[];
    excludedEventTypes?: string[];
    logToConsole?: boolean;
    logToFile?: boolean;
    logFilePath?: string;
  }): void {
    if (options.logLevel) this.logLevel = options.logLevel;
    if (options.enabledEventTypes) this.enabledEventTypes = new Set(options.enabledEventTypes);
    if (options.excludedEventTypes) this.excludedEventTypes = new Set(options.excludedEventTypes);
    if (options.logToConsole !== undefined) this.logToConsole = options.logToConsole;
    if (options.logToFile !== undefined) this.logToFile = options.logToFile;
    if (options.logFilePath) this.logFilePath = options.logFilePath;
  }

  /**
   * Get current configuration
   */
  getConfig(): {
    logLevel: string;
    enabledEventTypes: string[];
    excludedEventTypes: string[];
    logToConsole: boolean;
    logToFile: boolean;
    logFilePath?: string;
  } {
    return {
      logLevel: this.logLevel,
      enabledEventTypes: Array.from(this.enabledEventTypes),
      excludedEventTypes: Array.from(this.excludedEventTypes),
      logToConsole: this.logToConsole,
      logToFile: this.logToFile,
      logFilePath: this.logFilePath
    };
  }
}

/**
 * Create pre-configured logging middleware instances
 */
export const LoggingPresets = {
  /**
   * Development logging - verbose console output
   */
  development: () => new LoggingMiddleware({
    logLevel: 'debug',
    logToConsole: true,
    logToFile: false,
    excludedEventTypes: ['metrics:heartbeat', 'system:ping']
  }),

  /**
   * Production logging - structured file output
   */
  production: () => new LoggingMiddleware({
    logLevel: 'info',
    logToConsole: false,
    logToFile: true,
    logFilePath: '/var/log/unified-assistant/events.log',
    excludedEventTypes: ['metrics:heartbeat', 'system:ping', 'debug:']
  }),

  /**
   * Error-only logging - minimal output
   */
  errorOnly: () => new LoggingMiddleware({
    logLevel: 'error',
    logToConsole: true,
    logToFile: true,
    enabledEventTypes: ['error:', ':error', 'critical:']
  }),

  /**
   * AI-focused logging - AI events only
   */
  aiEvents: () => new LoggingMiddleware({
    logLevel: 'info',
    logToConsole: true,
    logToFile: true,
    enabledEventTypes: ['ai:', 'context:', 'workflow:']
  })
};
