import { EventMiddleware, PlatformEvent } from '@unified-assistant/types';

/**
 * Rate Limit Middleware
 * 
 * LEVER approach implementation:
 * L - Leverage existing rate limiting patterns
 * E - Extend with event-specific rate limiting and throttling
 * V - Verify through comprehensive rate tracking and enforcement
 * E - Eliminate event flooding and resource exhaustion
 * R - Reduce system load through intelligent rate limiting
 */
export class RateLimitMiddleware implements EventMiddleware {
  public readonly name = 'rate-limit';
  public readonly priority = 150; // High priority to limit early

  private globalLimits: RateLimit;
  private eventTypeLimits: Map<string, RateLimit> = new Map();
  private sourceLimits: Map<string, RateLimit> = new Map();
  private rateLimitStore: Map<string, RateLimitEntry> = new Map();
  private cleanupInterval?: NodeJS.Timeout;

  constructor(options: {
    globalLimits?: RateLimit;
    eventTypeLimits?: Record<string, RateLimit>;
    sourceLimits?: Record<string, RateLimit>;
    cleanupIntervalMs?: number;
  } = {}) {
    this.globalLimits = options.globalLimits || {
      maxRequests: 1000,
      windowMs: 60000, // 1 minute
      burstLimit: 100
    };

    if (options.eventTypeLimits) {
      for (const [eventType, limit] of Object.entries(options.eventTypeLimits)) {
        this.eventTypeLimits.set(eventType, limit);
      }
    }

    if (options.sourceLimits) {
      for (const [source, limit] of Object.entries(options.sourceLimits)) {
        this.sourceLimits.set(source, limit);
      }
    }

    this.setupDefaultLimits();
    this.startCleanup(options.cleanupIntervalMs || 60000);
  }

  /**
   * Process event through rate limiting middleware
   */
  async process(event: PlatformEvent, next: () => Promise<void>): Promise<void> {
    // Check global rate limit
    await this.checkRateLimit('global', this.globalLimits, event);

    // Check event type specific rate limit
    const eventTypeLimit = this.getEventTypeLimit(event.type);
    if (eventTypeLimit) {
      await this.checkRateLimit(`event:${event.type}`, eventTypeLimit, event);
    }

    // Check source specific rate limit
    const sourceLimit = this.getSourceLimit(event.source);
    if (sourceLimit) {
      await this.checkRateLimit(`source:${event.source}`, sourceLimit, event);
    }

    // Record the event
    this.recordEvent(event);

    // Continue to next middleware
    await next();
  }

  /**
   * Check rate limit for a specific key
   */
  private async checkRateLimit(
    key: string,
    limit: RateLimit,
    event: PlatformEvent
  ): Promise<void> {
    const entry = this.getRateLimitEntry(key, limit);
    const now = Date.now();

    // Clean up old requests outside the window
    entry.requests = entry.requests.filter(
      timestamp => now - timestamp < limit.windowMs
    );

    // Check if we're within the rate limit
    if (entry.requests.length >= limit.maxRequests) {
      // Check if this is a burst that might be allowed
      if (limit.burstLimit && entry.requests.length < limit.burstLimit) {
        // Allow burst but add delay
        await this.addDelay(this.calculateBurstDelay(entry.requests.length, limit));
      } else {
        throw new RateLimitError(
          `Rate limit exceeded for ${key}: ${entry.requests.length}/${limit.maxRequests} requests in ${limit.windowMs}ms`,
          {
            key,
            limit,
            currentCount: entry.requests.length,
            resetTime: entry.requests[0] + limit.windowMs
          }
        );
      }
    }

    // Add current request timestamp
    entry.requests.push(now);
    entry.lastRequest = now;
  }

  /**
   * Get or create rate limit entry
   */
  private getRateLimitEntry(key: string, limit: RateLimit): RateLimitEntry {
    let entry = this.rateLimitStore.get(key);
    if (!entry) {
      entry = {
        requests: [],
        lastRequest: 0,
        limit
      };
      this.rateLimitStore.set(key, entry);
    }
    return entry;
  }

  /**
   * Record event for tracking
   */
  private recordEvent(event: PlatformEvent): void {
    // This could be extended to track additional metrics
    // For now, the rate limiting itself handles the recording
  }

  /**
   * Get rate limit for event type
   */
  private getEventTypeLimit(eventType: string): RateLimit | undefined {
    // Check for exact match
    if (this.eventTypeLimits.has(eventType)) {
      return this.eventTypeLimits.get(eventType);
    }

    // Check for pattern matches
    for (const [pattern, limit] of this.eventTypeLimits.entries()) {
      if (this.matchesPattern(eventType, pattern)) {
        return limit;
      }
    }

    return undefined;
  }

  /**
   * Get rate limit for source
   */
  private getSourceLimit(source: string): RateLimit | undefined {
    return this.sourceLimits.get(source);
  }

  /**
   * Check if event type matches pattern
   */
  private matchesPattern(eventType: string, pattern: string): boolean {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(eventType);
    }
    return eventType === pattern;
  }

  /**
   * Calculate delay for burst requests
   */
  private calculateBurstDelay(currentCount: number, limit: RateLimit): number {
    if (!limit.burstLimit) return 0;
    
    const burstRatio = currentCount / limit.burstLimit;
    return Math.min(burstRatio * 1000, 5000); // Max 5 second delay
  }

  /**
   * Add delay to slow down processing
   */
  private addDelay(ms: number): Promise<void> {
    if (ms <= 0) return Promise.resolve();
    
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Setup default rate limits
   */
  private setupDefaultLimits(): void {
    // AI request limits
    this.eventTypeLimits.set('ai:*', {
      maxRequests: 100,
      windowMs: 60000, // 1 minute
      burstLimit: 10
    });

    // Error event limits (prevent error spam)
    this.eventTypeLimits.set('*:error', {
      maxRequests: 50,
      windowMs: 60000,
      burstLimit: 5
    });

    // High-frequency event limits
    this.eventTypeLimits.set('metrics:*', {
      maxRequests: 500,
      windowMs: 60000,
      burstLimit: 50
    });

    // User action limits
    this.eventTypeLimits.set('user:*', {
      maxRequests: 200,
      windowMs: 60000,
      burstLimit: 20
    });

    // Module communication limits
    this.eventTypeLimits.set('module:*', {
      maxRequests: 300,
      windowMs: 60000,
      burstLimit: 30
    });
  }

  /**
   * Start cleanup of old rate limit entries
   */
  private startCleanup(intervalMs: number): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldEntries();
    }, intervalMs);
  }

  /**
   * Clean up old rate limit entries
   */
  private cleanupOldEntries(): void {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    for (const [key, entry] of this.rateLimitStore.entries()) {
      // Remove entries that haven't been used recently
      if (now - entry.lastRequest > maxAge) {
        this.rateLimitStore.delete(key);
        continue;
      }

      // Clean up old requests within the entry
      entry.requests = entry.requests.filter(
        timestamp => now - timestamp < entry.limit.windowMs
      );
    }
  }

  /**
   * Get current rate limit status
   */
  getRateLimitStatus(): Record<string, {
    currentCount: number;
    maxRequests: number;
    windowMs: number;
    resetTime: number;
  }> {
    const status: Record<string, any> = {};
    const now = Date.now();

    for (const [key, entry] of this.rateLimitStore.entries()) {
      // Clean up old requests for accurate count
      entry.requests = entry.requests.filter(
        timestamp => now - timestamp < entry.limit.windowMs
      );

      status[key] = {
        currentCount: entry.requests.length,
        maxRequests: entry.limit.maxRequests,
        windowMs: entry.limit.windowMs,
        resetTime: entry.requests.length > 0 ? entry.requests[0] + entry.limit.windowMs : now
      };
    }

    return status;
  }

  /**
   * Update rate limits
   */
  updateRateLimit(key: string, limit: RateLimit): void {
    if (key === 'global') {
      this.globalLimits = limit;
    } else if (key.startsWith('event:')) {
      const eventType = key.substring(6);
      this.eventTypeLimits.set(eventType, limit);
    } else if (key.startsWith('source:')) {
      const source = key.substring(7);
      this.sourceLimits.set(source, limit);
    }
  }

  /**
   * Reset rate limit for a key
   */
  resetRateLimit(key: string): void {
    this.rateLimitStore.delete(key);
  }

  /**
   * Destroy middleware and cleanup
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}

/**
 * Rate Limit Configuration
 */
export interface RateLimit {
  maxRequests: number;
  windowMs: number;
  burstLimit?: number;
}

/**
 * Rate Limit Entry
 */
interface RateLimitEntry {
  requests: number[];
  lastRequest: number;
  limit: RateLimit;
}

/**
 * Rate Limit Error
 */
export class RateLimitError extends Error {
  constructor(
    message: string,
    public readonly details: {
      key: string;
      limit: RateLimit;
      currentCount: number;
      resetTime: number;
    }
  ) {
    super(message);
    this.name = 'RateLimitError';
  }
}

/**
 * Pre-configured rate limit middleware instances
 */
export const RateLimitPresets = {
  /**
   * Conservative rate limiting
   */
  conservative: () => new RateLimitMiddleware({
    globalLimits: {
      maxRequests: 500,
      windowMs: 60000,
      burstLimit: 50
    }
  }),

  /**
   * Aggressive rate limiting
   */
  aggressive: () => new RateLimitMiddleware({
    globalLimits: {
      maxRequests: 100,
      windowMs: 60000,
      burstLimit: 10
    }
  }),

  /**
   * Development rate limiting (very permissive)
   */
  development: () => new RateLimitMiddleware({
    globalLimits: {
      maxRequests: 10000,
      windowMs: 60000,
      burstLimit: 1000
    }
  })
};
