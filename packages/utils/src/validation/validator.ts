/**
 * Comprehensive validation utilities
 */

export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'email' | 'url' | 'uuid' | 'date';
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  enum?: any[];
  custom?: (value: any) => boolean | string;
  transform?: (value: any) => any;
}

export interface ValidationSchema {
  [key: string]: ValidationRule | ValidationSchema;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  data?: any;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  rule?: string;
}

export class Validator {
  private static emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private static urlRegex = /^https?:\/\/.+/;
  private static uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  /**
   * Validate a single value against a rule
   */
  static validateValue(value: any, rule: ValidationRule, fieldName: string = 'field'): ValidationResult {
    const errors: ValidationError[] = [];
    let transformedValue = value;

    // Apply transformation first
    if (rule.transform) {
      try {
        transformedValue = rule.transform(value);
      } catch (error) {
        errors.push({
          field: fieldName,
          message: `Transformation failed: ${error.message}`,
          value,
          rule: 'transform'
        });
        return { valid: false, errors };
      }
    }

    // Check required
    if (rule.required && (transformedValue === undefined || transformedValue === null || transformedValue === '')) {
      errors.push({
        field: fieldName,
        message: `${fieldName} is required`,
        value: transformedValue,
        rule: 'required'
      });
      return { valid: false, errors };
    }

    // Skip other validations if value is empty and not required
    if (transformedValue === undefined || transformedValue === null || transformedValue === '') {
      return { valid: true, errors: [], data: transformedValue };
    }

    // Type validation
    if (rule.type) {
      const typeError = this.validateType(transformedValue, rule.type, fieldName);
      if (typeError) {
        errors.push(typeError);
      }
    }

    // Min/Max validation for numbers
    if (typeof transformedValue === 'number') {
      if (rule.min !== undefined && transformedValue < rule.min) {
        errors.push({
          field: fieldName,
          message: `${fieldName} must be at least ${rule.min}`,
          value: transformedValue,
          rule: 'min'
        });
      }

      if (rule.max !== undefined && transformedValue > rule.max) {
        errors.push({
          field: fieldName,
          message: `${fieldName} must be at most ${rule.max}`,
          value: transformedValue,
          rule: 'max'
        });
      }
    }

    // Length validation for strings and arrays
    if (typeof transformedValue === 'string' || Array.isArray(transformedValue)) {
      const length = transformedValue.length;

      if (rule.minLength !== undefined && length < rule.minLength) {
        errors.push({
          field: fieldName,
          message: `${fieldName} must be at least ${rule.minLength} characters long`,
          value: transformedValue,
          rule: 'minLength'
        });
      }

      if (rule.maxLength !== undefined && length > rule.maxLength) {
        errors.push({
          field: fieldName,
          message: `${fieldName} must be at most ${rule.maxLength} characters long`,
          value: transformedValue,
          rule: 'maxLength'
        });
      }
    }

    // Pattern validation
    if (rule.pattern && typeof transformedValue === 'string') {
      if (!rule.pattern.test(transformedValue)) {
        errors.push({
          field: fieldName,
          message: `${fieldName} does not match the required pattern`,
          value: transformedValue,
          rule: 'pattern'
        });
      }
    }

    // Enum validation
    if (rule.enum && !rule.enum.includes(transformedValue)) {
      errors.push({
        field: fieldName,
        message: `${fieldName} must be one of: ${rule.enum.join(', ')}`,
        value: transformedValue,
        rule: 'enum'
      });
    }

    // Custom validation
    if (rule.custom) {
      const customResult = rule.custom(transformedValue);
      if (customResult !== true) {
        errors.push({
          field: fieldName,
          message: typeof customResult === 'string' ? customResult : `${fieldName} failed custom validation`,
          value: transformedValue,
          rule: 'custom'
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      data: transformedValue
    };
  }

  /**
   * Validate an object against a schema
   */
  static validateObject(data: any, schema: ValidationSchema): ValidationResult {
    const errors: ValidationError[] = [];
    const validatedData: any = {};

    if (typeof data !== 'object' || data === null) {
      return {
        valid: false,
        errors: [{
          field: 'root',
          message: 'Data must be an object',
          value: data,
          rule: 'type'
        }]
      };
    }

    // Validate each field in schema
    for (const [fieldName, rule] of Object.entries(schema)) {
      const fieldValue = data[fieldName];

      if (this.isValidationRule(rule)) {
        const result = this.validateValue(fieldValue, rule, fieldName);
        errors.push(...result.errors);
        
        if (result.valid) {
          validatedData[fieldName] = result.data;
        }
      } else {
        // Nested object validation
        if (fieldValue !== undefined && fieldValue !== null) {
          const nestedResult = this.validateObject(fieldValue, rule as ValidationSchema);
          
          // Prefix nested errors with field name
          const nestedErrors = nestedResult.errors.map(error => ({
            ...error,
            field: `${fieldName}.${error.field}`
          }));
          
          errors.push(...nestedErrors);
          
          if (nestedResult.valid) {
            validatedData[fieldName] = nestedResult.data;
          }
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      data: validatedData
    };
  }

  /**
   * Validate type
   */
  private static validateType(value: any, type: string, fieldName: string): ValidationError | null {
    switch (type) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            field: fieldName,
            message: `${fieldName} must be a string`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be a number`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean') {
          return {
            field: fieldName,
            message: `${fieldName} must be a boolean`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be an array`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'object':
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be an object`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'email':
        if (typeof value !== 'string' || !this.emailRegex.test(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be a valid email address`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'url':
        if (typeof value !== 'string' || !this.urlRegex.test(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be a valid URL`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'uuid':
        if (typeof value !== 'string' || !this.uuidRegex.test(value)) {
          return {
            field: fieldName,
            message: `${fieldName} must be a valid UUID`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'date':
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return {
            field: fieldName,
            message: `${fieldName} must be a valid date`,
            value,
            rule: 'type'
          };
        }
        break;
    }

    return null;
  }

  /**
   * Check if object is a validation rule
   */
  private static isValidationRule(obj: any): obj is ValidationRule {
    if (typeof obj !== 'object' || obj === null) {
      return false;
    }

    const ruleKeys = ['required', 'type', 'min', 'max', 'minLength', 'maxLength', 'pattern', 'enum', 'custom', 'transform'];
    return Object.keys(obj).some(key => ruleKeys.includes(key));
  }

  /**
   * Sanitize input by removing dangerous characters
   */
  static sanitizeString(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove HTML tags
      .replace(/['"]/g, '') // Remove quotes
      .replace(/[&]/g, '&amp;') // Escape ampersands
      .trim();
  }

  /**
   * Validate and sanitize email
   */
  static validateEmail(email: string): { valid: boolean; sanitized?: string; error?: string } {
    if (typeof email !== 'string') {
      return { valid: false, error: 'Email must be a string' };
    }

    const sanitized = email.toLowerCase().trim();
    
    if (!this.emailRegex.test(sanitized)) {
      return { valid: false, error: 'Invalid email format' };
    }

    return { valid: true, sanitized };
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string, options: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
  } = {}): { valid: boolean; score: number; errors: string[] } {
    const {
      minLength = 8,
      requireUppercase = true,
      requireLowercase = true,
      requireNumbers = true,
      requireSpecialChars = true
    } = options;

    const errors: string[] = [];
    let score = 0;

    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    } else {
      score += 20;
    }

    if (requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else if (/[A-Z]/.test(password)) {
      score += 20;
    }

    if (requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else if (/[a-z]/.test(password)) {
      score += 20;
    }

    if (requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    } else if (/\d/.test(password)) {
      score += 20;
    }

    if (requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    } else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      score += 20;
    }

    return {
      valid: errors.length === 0,
      score: Math.min(score, 100),
      errors
    };
  }

  /**
   * Validate phone number (basic international format)
   */
  static validatePhoneNumber(phone: string): { valid: boolean; formatted?: string; error?: string } {
    if (typeof phone !== 'string') {
      return { valid: false, error: 'Phone number must be a string' };
    }

    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');
    
    // Check length (7-15 digits for international numbers)
    if (digits.length < 7 || digits.length > 15) {
      return { valid: false, error: 'Phone number must be between 7 and 15 digits' };
    }

    // Format as international number
    const formatted = `+${digits}`;

    return { valid: true, formatted };
  }

  /**
   * Validate credit card number using Luhn algorithm
   */
  static validateCreditCard(cardNumber: string): { valid: boolean; type?: string; error?: string } {
    if (typeof cardNumber !== 'string') {
      return { valid: false, error: 'Card number must be a string' };
    }

    // Remove spaces and dashes
    const digits = cardNumber.replace(/[\s-]/g, '');
    
    // Check if all characters are digits
    if (!/^\d+$/.test(digits)) {
      return { valid: false, error: 'Card number must contain only digits' };
    }

    // Check length
    if (digits.length < 13 || digits.length > 19) {
      return { valid: false, error: 'Card number must be between 13 and 19 digits' };
    }

    // Luhn algorithm
    let sum = 0;
    let isEven = false;

    for (let i = digits.length - 1; i >= 0; i--) {
      let digit = parseInt(digits[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    const valid = sum % 10 === 0;
    
    if (!valid) {
      return { valid: false, error: 'Invalid card number' };
    }

    // Determine card type
    let type = 'Unknown';
    if (/^4/.test(digits)) type = 'Visa';
    else if (/^5[1-5]/.test(digits)) type = 'MasterCard';
    else if (/^3[47]/.test(digits)) type = 'American Express';
    else if (/^6/.test(digits)) type = 'Discover';

    return { valid: true, type };
  }
}
