/**
 * Comprehensive logging system
 */

import { EventEmitter } from 'events';

export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'trace';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  metadata?: Record<string, any>;
  context?: string;
  requestId?: string;
  userId?: string;
  error?: Error;
}

export interface LogTransport {
  name: string;
  log(entry: LogEntry): Promise<void>;
  close?(): Promise<void>;
}

export interface LoggerConfig {
  level: LogLevel;
  transports: LogTransport[];
  context?: string;
  enableMetadata?: boolean;
  enableStackTrace?: boolean;
  maxMetadataDepth?: number;
}

export class Logger extends EventEmitter {
  private config: LoggerConfig;
  private static instance: Logger;
  private static logLevels: Record<LogLevel, number> = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
    trace: 4
  };

  constructor(config: LoggerConfig) {
    super();
    this.config = {
      enableMetadata: true,
      enableStackTrace: true,
      maxMetadataDepth: 5,
      ...config
    };
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: LoggerConfig): Logger {
    if (!Logger.instance && config) {
      Logger.instance = new Logger(config);
    }
    return Logger.instance;
  }

  /**
   * Set global logger instance
   */
  static setInstance(logger: Logger): void {
    Logger.instance = logger;
  }

  /**
   * Log error message
   */
  error(message: string, metadata?: Record<string, any>, error?: Error): void {
    this.log('error', message, metadata, error);
  }

  /**
   * Log warning message
   */
  warn(message: string, metadata?: Record<string, any>): void {
    this.log('warn', message, metadata);
  }

  /**
   * Log info message
   */
  info(message: string, metadata?: Record<string, any>): void {
    this.log('info', message, metadata);
  }

  /**
   * Log debug message
   */
  debug(message: string, metadata?: Record<string, any>): void {
    this.log('debug', message, metadata);
  }

  /**
   * Log trace message
   */
  trace(message: string, metadata?: Record<string, any>): void {
    this.log('trace', message, metadata);
  }

  /**
   * Core logging method
   */
  private async log(
    level: LogLevel,
    message: string,
    metadata?: Record<string, any>,
    error?: Error
  ): Promise<void> {
    // Check if log level is enabled
    if (!this.isLevelEnabled(level)) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context: this.config.context
    };

    // Add metadata if enabled
    if (this.config.enableMetadata && metadata) {
      entry.metadata = this.sanitizeMetadata(metadata);
    }

    // Add error information
    if (error) {
      entry.error = error;
      if (this.config.enableStackTrace) {
        entry.metadata = {
          ...entry.metadata,
          stack: error.stack,
          errorName: error.name,
          errorMessage: error.message
        };
      }
    }

    // Extract request/user context from metadata
    if (metadata) {
      if (metadata.requestId) entry.requestId = metadata.requestId;
      if (metadata.userId) entry.userId = metadata.userId;
    }

    // Emit log event
    this.emit('log', entry);

    // Send to all transports
    const transportPromises = this.config.transports.map(transport => 
      this.safeTransportLog(transport, entry)
    );

    await Promise.allSettled(transportPromises);
  }

  /**
   * Safely log to transport with error handling
   */
  private async safeTransportLog(transport: LogTransport, entry: LogEntry): Promise<void> {
    try {
      await transport.log(entry);
    } catch (error) {
      this.emit('transportError', { transport: transport.name, error, entry });
    }
  }

  /**
   * Check if log level is enabled
   */
  private isLevelEnabled(level: LogLevel): boolean {
    return Logger.logLevels[level] <= Logger.logLevels[this.config.level];
  }

  /**
   * Sanitize metadata to prevent circular references and limit depth
   */
  private sanitizeMetadata(metadata: any, depth: number = 0): any {
    if (depth > this.config.maxMetadataDepth!) {
      return '[Max Depth Exceeded]';
    }

    if (metadata === null || metadata === undefined) {
      return metadata;
    }

    if (typeof metadata === 'string' || typeof metadata === 'number' || typeof metadata === 'boolean') {
      return metadata;
    }

    if (metadata instanceof Date) {
      return metadata.toISOString();
    }

    if (metadata instanceof Error) {
      return {
        name: metadata.name,
        message: metadata.message,
        stack: metadata.stack
      };
    }

    if (Array.isArray(metadata)) {
      return metadata.map(item => this.sanitizeMetadata(item, depth + 1));
    }

    if (typeof metadata === 'object') {
      const sanitized: Record<string, any> = {};
      
      for (const [key, value] of Object.entries(metadata)) {
        try {
          sanitized[key] = this.sanitizeMetadata(value, depth + 1);
        } catch (error) {
          sanitized[key] = '[Serialization Error]';
        }
      }
      
      return sanitized;
    }

    return String(metadata);
  }

  /**
   * Create child logger with additional context
   */
  child(context: string, additionalMetadata?: Record<string, any>): Logger {
    const childConfig = {
      ...this.config,
      context: this.config.context ? `${this.config.context}.${context}` : context
    };

    const childLogger = new Logger(childConfig);

    // Override log method to include additional metadata
    if (additionalMetadata) {
      const originalLog = childLogger.log.bind(childLogger);
      childLogger.log = async (level: LogLevel, message: string, metadata?: Record<string, any>, error?: Error) => {
        const mergedMetadata = { ...additionalMetadata, ...metadata };
        return originalLog(level, message, mergedMetadata, error);
      };
    }

    return childLogger;
  }

  /**
   * Set log level
   */
  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  /**
   * Get current log level
   */
  getLevel(): LogLevel {
    return this.config.level;
  }

  /**
   * Add transport
   */
  addTransport(transport: LogTransport): void {
    this.config.transports.push(transport);
  }

  /**
   * Remove transport
   */
  removeTransport(transportName: string): void {
    this.config.transports = this.config.transports.filter(
      transport => transport.name !== transportName
    );
  }

  /**
   * Close all transports
   */
  async close(): Promise<void> {
    const closePromises = this.config.transports
      .filter(transport => transport.close)
      .map(transport => transport.close!());

    await Promise.allSettled(closePromises);
  }

  /**
   * Flush all transports (if supported)
   */
  async flush(): Promise<void> {
    // Emit flush event for transports that support it
    this.emit('flush');
  }

  /**
   * Get logger statistics
   */
  getStats(): {
    level: LogLevel;
    transports: string[];
    context?: string;
  } {
    return {
      level: this.config.level,
      transports: this.config.transports.map(t => t.name),
      context: this.config.context
    };
  }
}

/**
 * Console transport for logging to console
 */
export class ConsoleTransport implements LogTransport {
  public readonly name = 'console';
  private colors: Record<LogLevel, string> = {
    error: '\x1b[31m',   // Red
    warn: '\x1b[33m',    // Yellow
    info: '\x1b[36m',    // Cyan
    debug: '\x1b[35m',   // Magenta
    trace: '\x1b[37m'    // White
  };
  private reset = '\x1b[0m';

  async log(entry: LogEntry): Promise<void> {
    const color = this.colors[entry.level];
    const timestamp = entry.timestamp.toISOString();
    const context = entry.context ? `[${entry.context}]` : '';
    const level = entry.level.toUpperCase().padEnd(5);
    
    let message = `${color}${timestamp} ${level}${this.reset} ${context} ${entry.message}`;
    
    if (entry.metadata && Object.keys(entry.metadata).length > 0) {
      message += `\n  Metadata: ${JSON.stringify(entry.metadata, null, 2)}`;
    }
    
    if (entry.error && entry.error.stack) {
      message += `\n  Stack: ${entry.error.stack}`;
    }

    console.log(message);
  }
}

/**
 * File transport for logging to files
 */
export class FileTransport implements LogTransport {
  public readonly name = 'file';
  private filePath: string;
  private maxSize: number;
  private maxFiles: number;

  constructor(filePath: string, maxSize: number = 10 * 1024 * 1024, maxFiles: number = 5) {
    this.filePath = filePath;
    this.maxSize = maxSize;
    this.maxFiles = maxFiles;
  }

  async log(entry: LogEntry): Promise<void> {
    const logLine = JSON.stringify({
      timestamp: entry.timestamp.toISOString(),
      level: entry.level,
      message: entry.message,
      context: entry.context,
      metadata: entry.metadata,
      requestId: entry.requestId,
      userId: entry.userId,
      error: entry.error ? {
        name: entry.error.name,
        message: entry.error.message,
        stack: entry.error.stack
      } : undefined
    }) + '\n';

    // In a real implementation, this would use fs.appendFile with rotation logic
    console.log(`[FILE LOG] ${logLine.trim()}`);
  }
}

/**
 * Create default logger instance
 */
export function createLogger(config?: Partial<LoggerConfig>): Logger {
  const defaultConfig: LoggerConfig = {
    level: 'info',
    transports: [new ConsoleTransport()],
    enableMetadata: true,
    enableStackTrace: true,
    maxMetadataDepth: 5,
    ...config
  };

  return new Logger(defaultConfig);
}

/**
 * Get or create global logger
 */
export function getLogger(): Logger {
  if (!Logger.getInstance()) {
    Logger.setInstance(createLogger());
  }
  return Logger.getInstance();
}
