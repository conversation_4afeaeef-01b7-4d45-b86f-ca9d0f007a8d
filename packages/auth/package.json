{"name": "@unified-assistant/auth", "version": "0.1.0", "description": "Authentication and authorization system for the unified AI assistant platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"@unified-assistant/types": "workspace:*", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.7", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.10.2", "typescript": "^5.7.2", "jest": "^29.7.0"}, "files": ["dist", "README.md"]}