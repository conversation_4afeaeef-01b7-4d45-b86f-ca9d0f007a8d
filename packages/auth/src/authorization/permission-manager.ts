/**
 * Permission management system
 */

import { Permission, User } from '@unified-assistant/types';

export interface PermissionRepository {
  create(permission: Omit<Permission, 'id'>): Promise<Permission>;
  findById(id: string): Promise<Permission | null>;
  findByName(name: string): Promise<Permission | null>;
  list(): Promise<Permission[]>;
  update(id: string, updates: Partial<Permission>): Promise<Permission>;
  delete(id: string): Promise<void>;
}

export class PermissionManager {
  private repository: PermissionRepository;
  private permissionCache: Map<string, Permission> = new Map();

  constructor(repository: PermissionRepository) {
    this.repository = repository;
  }

  /**
   * Create a new permission
   */
  async createPermission(permission: Omit<Permission, 'id'>): Promise<Permission> {
    try {
      // Check if permission already exists
      const existing = await this.repository.findByName(permission.name);
      if (existing) {
        throw new Error('Permission already exists');
      }

      const newPermission = await this.repository.create(permission);
      this.permissionCache.set(newPermission.id, newPermission);
      
      return newPermission;
    } catch (error) {
      throw new Error(`Permission creation failed: ${error.message}`);
    }
  }

  /**
   * Get permission by ID
   */
  async getPermission(id: string): Promise<Permission | null> {
    try {
      // Check cache first
      if (this.permissionCache.has(id)) {
        return this.permissionCache.get(id)!;
      }

      const permission = await this.repository.findById(id);
      if (permission) {
        this.permissionCache.set(id, permission);
      }

      return permission;
    } catch (error) {
      throw new Error(`Failed to get permission: ${error.message}`);
    }
  }

  /**
   * Get permission by name
   */
  async getPermissionByName(name: string): Promise<Permission | null> {
    try {
      // Check cache first
      for (const permission of this.permissionCache.values()) {
        if (permission.name === name) {
          return permission;
        }
      }

      const permission = await this.repository.findByName(name);
      if (permission) {
        this.permissionCache.set(permission.id, permission);
      }

      return permission;
    } catch (error) {
      throw new Error(`Failed to get permission by name: ${error.message}`);
    }
  }

  /**
   * List all permissions
   */
  async listPermissions(): Promise<Permission[]> {
    try {
      const permissions = await this.repository.list();
      
      // Update cache
      permissions.forEach(permission => {
        this.permissionCache.set(permission.id, permission);
      });

      return permissions;
    } catch (error) {
      throw new Error(`Failed to list permissions: ${error.message}`);
    }
  }

  /**
   * Update permission
   */
  async updatePermission(id: string, updates: Partial<Permission>): Promise<Permission> {
    try {
      const permission = await this.repository.update(id, updates);
      this.permissionCache.set(id, permission);
      
      return permission;
    } catch (error) {
      throw new Error(`Permission update failed: ${error.message}`);
    }
  }

  /**
   * Delete permission
   */
  async deletePermission(id: string): Promise<void> {
    try {
      await this.repository.delete(id);
      this.permissionCache.delete(id);
    } catch (error) {
      throw new Error(`Permission deletion failed: ${error.message}`);
    }
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(user: User, permissionName: string): Promise<boolean> {
    try {
      // Check direct permissions
      const hasDirectPermission = user.permissions.some(p => p.name === permissionName);
      if (hasDirectPermission) {
        return true;
      }

      // Check role permissions
      const hasRolePermission = user.role.permissions.some(p => p.name === permissionName);
      return hasRolePermission;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(user: User, permissionNames: string[]): Promise<boolean> {
    try {
      for (const permissionName of permissionNames) {
        if (await this.hasPermission(user, permissionName)) {
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if user has all of the specified permissions
   */
  async hasAllPermissions(user: User, permissionNames: string[]): Promise<boolean> {
    try {
      for (const permissionName of permissionNames) {
        if (!(await this.hasPermission(user, permissionName))) {
          return false;
        }
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get effective permissions for user (direct + role permissions)
   */
  async getEffectivePermissions(user: User): Promise<Permission[]> {
    try {
      const permissions = new Map<string, Permission>();

      // Add direct permissions
      user.permissions.forEach(permission => {
        permissions.set(permission.id, permission);
      });

      // Add role permissions
      user.role.permissions.forEach(permission => {
        permissions.set(permission.id, permission);
      });

      return Array.from(permissions.values());
    } catch (error) {
      throw new Error(`Failed to get effective permissions: ${error.message}`);
    }
  }

  /**
   * Check permission with conditions
   */
  async checkPermissionWithConditions(
    user: User,
    permissionName: string,
    context: Record<string, any>
  ): Promise<boolean> {
    try {
      const permission = await this.getPermissionByName(permissionName);
      if (!permission) {
        return false;
      }

      // Check if user has the permission
      if (!(await this.hasPermission(user, permissionName))) {
        return false;
      }

      // Check conditions if they exist
      if (permission.conditions) {
        return this.evaluateConditions(permission.conditions, context, user);
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Evaluate permission conditions
   */
  private evaluateConditions(
    conditions: Record<string, any>,
    context: Record<string, any>,
    user: User
  ): boolean {
    try {
      // Simple condition evaluation - can be extended for complex rules
      for (const [key, expectedValue] of Object.entries(conditions)) {
        if (key === 'userId') {
          if (context.userId !== user.id) {
            return false;
          }
        } else if (key === 'role') {
          if (user.role.name !== expectedValue) {
            return false;
          }
        } else if (key === 'resource') {
          if (context.resource !== expectedValue) {
            return false;
          }
        } else {
          // Generic context check
          if (context[key] !== expectedValue) {
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Clear permission cache
   */
  clearCache(): void {
    this.permissionCache.clear();
  }

  /**
   * Warm up permission cache
   */
  async warmUpCache(): Promise<void> {
    try {
      await this.listPermissions();
    } catch (error) {
      console.error('Failed to warm up permission cache:', error);
    }
  }
}
