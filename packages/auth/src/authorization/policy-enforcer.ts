/**
 * Policy enforcement system for authorization
 */

import { User, Permission } from '@unified-assistant/types';
import { PermissionManager } from './permission-manager';
import { RoleManager } from './role-manager';

export interface PolicyRule {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  effect: 'allow' | 'deny';
  conditions?: Record<string, any>;
  priority: number;
}

export interface PolicyContext {
  user: User;
  resource: string;
  action: string;
  data?: Record<string, any>;
  environment?: Record<string, any>;
}

export interface PolicyDecision {
  allowed: boolean;
  reason: string;
  appliedRules: PolicyRule[];
}

export class PolicyEnforcer {
  private permissionManager: PermissionManager;
  private roleManager: RoleManager;
  private policies: Map<string, PolicyRule> = new Map();

  constructor(permissionManager: PermissionManager, roleManager: RoleManager) {
    this.permissionManager = permissionManager;
    this.roleManager = roleManager;
  }

  /**
   * Add a policy rule
   */
  addPolicy(rule: PolicyRule): void {
    this.policies.set(rule.id, rule);
  }

  /**
   * Remove a policy rule
   */
  removePolicy(ruleId: string): void {
    this.policies.delete(ruleId);
  }

  /**
   * Evaluate authorization request
   */
  async evaluate(context: PolicyContext): Promise<PolicyDecision> {
    try {
      const appliedRules: PolicyRule[] = [];
      let finalDecision = false;
      let reason = 'No applicable rules found';

      // Get applicable rules for this resource and action
      const applicableRules = this.getApplicableRules(context.resource, context.action);
      
      // Sort rules by priority (higher priority first)
      applicableRules.sort((a, b) => b.priority - a.priority);

      // Evaluate each rule
      for (const rule of applicableRules) {
        if (await this.evaluateRule(rule, context)) {
          appliedRules.push(rule);
          
          if (rule.effect === 'deny') {
            // Deny rules take precedence
            finalDecision = false;
            reason = `Access denied by rule: ${rule.name}`;
            break;
          } else if (rule.effect === 'allow') {
            finalDecision = true;
            reason = `Access allowed by rule: ${rule.name}`;
            // Continue to check for deny rules
          }
        }
      }

      // If no explicit rules, check basic permissions
      if (appliedRules.length === 0) {
        const hasPermission = await this.checkBasicPermission(context);
        finalDecision = hasPermission;
        reason = hasPermission ? 'Access allowed by basic permissions' : 'Access denied - insufficient permissions';
      }

      return {
        allowed: finalDecision,
        reason,
        appliedRules
      };
    } catch (error) {
      return {
        allowed: false,
        reason: `Policy evaluation error: ${error.message}`,
        appliedRules: []
      };
    }
  }

  /**
   * Check if user can perform action on resource
   */
  async canPerform(user: User, resource: string, action: string, data?: Record<string, any>): Promise<boolean> {
    const context: PolicyContext = {
      user,
      resource,
      action,
      data,
      environment: {
        timestamp: new Date().toISOString(),
        userAgent: 'policy-enforcer'
      }
    };

    const decision = await this.evaluate(context);
    return decision.allowed;
  }

  /**
   * Batch authorization check
   */
  async canPerformBatch(
    user: User,
    requests: Array<{ resource: string; action: string; data?: Record<string, any> }>
  ): Promise<Array<{ allowed: boolean; reason: string }>> {
    const results = [];

    for (const request of requests) {
      const decision = await this.evaluate({
        user,
        resource: request.resource,
        action: request.action,
        data: request.data,
        environment: {
          timestamp: new Date().toISOString(),
          userAgent: 'policy-enforcer-batch'
        }
      });

      results.push({
        allowed: decision.allowed,
        reason: decision.reason
      });
    }

    return results;
  }

  /**
   * Get applicable rules for resource and action
   */
  private getApplicableRules(resource: string, action: string): PolicyRule[] {
    const rules: PolicyRule[] = [];

    for (const rule of this.policies.values()) {
      if (this.matchesResourcePattern(rule.resource, resource) &&
          this.matchesActionPattern(rule.action, action)) {
        rules.push(rule);
      }
    }

    return rules;
  }

  /**
   * Evaluate a single rule against context
   */
  private async evaluateRule(rule: PolicyRule, context: PolicyContext): Promise<boolean> {
    try {
      // Check if user has required permissions for this rule
      const permissionName = `${rule.resource}:${rule.action}`;
      const hasPermission = await this.permissionManager.hasPermission(context.user, permissionName);
      
      if (!hasPermission) {
        return false;
      }

      // Evaluate conditions if they exist
      if (rule.conditions) {
        return this.evaluateConditions(rule.conditions, context);
      }

      return true;
    } catch (error) {
      console.error(`Error evaluating rule ${rule.id}:`, error);
      return false;
    }
  }

  /**
   * Evaluate rule conditions
   */
  private evaluateConditions(conditions: Record<string, any>, context: PolicyContext): boolean {
    try {
      for (const [key, expectedValue] of Object.entries(conditions)) {
        switch (key) {
          case 'userId':
            if (context.user.id !== expectedValue) return false;
            break;
          case 'role':
            if (context.user.role.name !== expectedValue) return false;
            break;
          case 'timeRange':
            if (!this.isWithinTimeRange(expectedValue)) return false;
            break;
          case 'ipAddress':
            if (!this.matchesIpPattern(expectedValue, context.environment?.ipAddress)) return false;
            break;
          case 'resourceOwner':
            if (context.data?.ownerId !== context.user.id) return false;
            break;
          default:
            // Generic data condition
            if (context.data?.[key] !== expectedValue) return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error evaluating conditions:', error);
      return false;
    }
  }

  /**
   * Check basic permission without policy rules
   */
  private async checkBasicPermission(context: PolicyContext): Promise<boolean> {
    const permissionName = `${context.resource}:${context.action}`;
    return await this.permissionManager.hasPermission(context.user, permissionName);
  }

  /**
   * Check if resource matches pattern
   */
  private matchesResourcePattern(pattern: string, resource: string): boolean {
    if (pattern === '*') return true;
    if (pattern === resource) return true;
    
    // Support wildcard patterns like "user:*" or "*/read"
    const regexPattern = pattern.replace(/\*/g, '.*');
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(resource);
  }

  /**
   * Check if action matches pattern
   */
  private matchesActionPattern(pattern: string, action: string): boolean {
    if (pattern === '*') return true;
    if (pattern === action) return true;
    
    // Support wildcard patterns
    const regexPattern = pattern.replace(/\*/g, '.*');
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(action);
  }

  /**
   * Check if current time is within allowed range
   */
  private isWithinTimeRange(timeRange: { start: string; end: string }): boolean {
    try {
      const now = new Date();
      const start = new Date(timeRange.start);
      const end = new Date(timeRange.end);
      
      return now >= start && now <= end;
    } catch {
      return false;
    }
  }

  /**
   * Check if IP address matches pattern
   */
  private matchesIpPattern(pattern: string, ipAddress?: string): boolean {
    if (!ipAddress) return false;
    if (pattern === '*') return true;
    if (pattern === ipAddress) return true;
    
    // Support CIDR notation and wildcards
    // This is a simplified implementation
    return pattern.includes('*') ? 
      new RegExp(pattern.replace(/\*/g, '.*')).test(ipAddress) : 
      ipAddress.startsWith(pattern);
  }

  /**
   * Load default policies
   */
  loadDefaultPolicies(): void {
    const defaultPolicies: PolicyRule[] = [
      {
        id: 'admin-full-access',
        name: 'Admin Full Access',
        description: 'Administrators have full access to all resources',
        resource: '*',
        action: '*',
        effect: 'allow',
        conditions: { role: 'admin' },
        priority: 100
      },
      {
        id: 'user-own-profile',
        name: 'User Own Profile Access',
        description: 'Users can access their own profile',
        resource: 'user',
        action: '*',
        effect: 'allow',
        conditions: { resourceOwner: true },
        priority: 50
      },
      {
        id: 'guest-read-only',
        name: 'Guest Read Only',
        description: 'Guests can only read public resources',
        resource: 'public/*',
        action: 'read',
        effect: 'allow',
        conditions: { role: 'guest' },
        priority: 10
      }
    ];

    defaultPolicies.forEach(policy => this.addPolicy(policy));
  }

  /**
   * Clear all policies
   */
  clearPolicies(): void {
    this.policies.clear();
  }

  /**
   * Get all policies
   */
  getPolicies(): PolicyRule[] {
    return Array.from(this.policies.values());
  }
}
