/**
 * Role management system
 */

import { UserRole, Permission } from '@unified-assistant/types';

export interface RoleRepository {
  create(role: Omit<UserRole, 'id'>): Promise<UserRole>;
  findById(id: string): Promise<UserRole | null>;
  findByName(name: string): Promise<UserRole | null>;
  list(): Promise<UserRole[]>;
  update(id: string, updates: Partial<UserRole>): Promise<UserRole>;
  delete(id: string): Promise<void>;
  addPermission(roleId: string, permissionId: string): Promise<void>;
  removePermission(roleId: string, permissionId: string): Promise<void>;
}

export class RoleManager {
  private repository: RoleRepository;
  private roleCache: Map<string, UserRole> = new Map();

  constructor(repository: RoleRepository) {
    this.repository = repository;
  }

  /**
   * Create a new role
   */
  async createRole(role: Omit<UserRole, 'id'>): Promise<UserRole> {
    try {
      // Check if role already exists
      const existing = await this.repository.findByName(role.name);
      if (existing) {
        throw new Error('Role already exists');
      }

      const newRole = await this.repository.create(role);
      this.roleCache.set(newRole.id, newRole);
      
      return newRole;
    } catch (error) {
      throw new Error(`Role creation failed: ${error.message}`);
    }
  }

  /**
   * Get role by ID
   */
  async getRole(id: string): Promise<UserRole | null> {
    try {
      // Check cache first
      if (this.roleCache.has(id)) {
        return this.roleCache.get(id)!;
      }

      const role = await this.repository.findById(id);
      if (role) {
        this.roleCache.set(id, role);
      }

      return role;
    } catch (error) {
      throw new Error(`Failed to get role: ${error.message}`);
    }
  }

  /**
   * Get role by name
   */
  async getRoleByName(name: string): Promise<UserRole | null> {
    try {
      // Check cache first
      for (const role of this.roleCache.values()) {
        if (role.name === name) {
          return role;
        }
      }

      const role = await this.repository.findByName(name);
      if (role) {
        this.roleCache.set(role.id, role);
      }

      return role;
    } catch (error) {
      throw new Error(`Failed to get role by name: ${error.message}`);
    }
  }

  /**
   * List all roles
   */
  async listRoles(): Promise<UserRole[]> {
    try {
      const roles = await this.repository.list();
      
      // Update cache
      roles.forEach(role => {
        this.roleCache.set(role.id, role);
      });

      return roles;
    } catch (error) {
      throw new Error(`Failed to list roles: ${error.message}`);
    }
  }

  /**
   * Update role
   */
  async updateRole(id: string, updates: Partial<UserRole>): Promise<UserRole> {
    try {
      const role = await this.repository.update(id, updates);
      this.roleCache.set(id, role);
      
      return role;
    } catch (error) {
      throw new Error(`Role update failed: ${error.message}`);
    }
  }

  /**
   * Delete role
   */
  async deleteRole(id: string): Promise<void> {
    try {
      const role = await this.getRole(id);
      if (role?.isSystem) {
        throw new Error('Cannot delete system role');
      }

      await this.repository.delete(id);
      this.roleCache.delete(id);
    } catch (error) {
      throw new Error(`Role deletion failed: ${error.message}`);
    }
  }

  /**
   * Add permission to role
   */
  async addPermissionToRole(roleId: string, permissionId: string): Promise<void> {
    try {
      await this.repository.addPermission(roleId, permissionId);
      
      // Invalidate cache for this role
      this.roleCache.delete(roleId);
    } catch (error) {
      throw new Error(`Failed to add permission to role: ${error.message}`);
    }
  }

  /**
   * Remove permission from role
   */
  async removePermissionFromRole(roleId: string, permissionId: string): Promise<void> {
    try {
      await this.repository.removePermission(roleId, permissionId);
      
      // Invalidate cache for this role
      this.roleCache.delete(roleId);
    } catch (error) {
      throw new Error(`Failed to remove permission from role: ${error.message}`);
    }
  }

  /**
   * Check if role has permission
   */
  async roleHasPermission(roleId: string, permissionName: string): Promise<boolean> {
    try {
      const role = await this.getRole(roleId);
      if (!role) {
        return false;
      }

      return role.permissions.some(p => p.name === permissionName);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get role hierarchy (if roles have parent-child relationships)
   */
  async getRoleHierarchy(roleId: string): Promise<UserRole[]> {
    try {
      const hierarchy: UserRole[] = [];
      const role = await this.getRole(roleId);
      
      if (role) {
        hierarchy.push(role);
        // Add logic for parent roles if implemented
      }

      return hierarchy;
    } catch (error) {
      throw new Error(`Failed to get role hierarchy: ${error.message}`);
    }
  }

  /**
   * Create default system roles
   */
  async createDefaultRoles(): Promise<void> {
    try {
      const defaultRoles = [
        {
          name: 'admin',
          description: 'System administrator with full access',
          permissions: [],
          isSystem: true
        },
        {
          name: 'user',
          description: 'Regular user with basic access',
          permissions: [],
          isSystem: true
        },
        {
          name: 'moderator',
          description: 'Moderator with limited administrative access',
          permissions: [],
          isSystem: true
        },
        {
          name: 'guest',
          description: 'Guest user with read-only access',
          permissions: [],
          isSystem: true
        }
      ];

      for (const roleData of defaultRoles) {
        const existing = await this.getRoleByName(roleData.name);
        if (!existing) {
          await this.createRole(roleData);
        }
      }
    } catch (error) {
      throw new Error(`Failed to create default roles: ${error.message}`);
    }
  }

  /**
   * Get roles by permission
   */
  async getRolesByPermission(permissionName: string): Promise<UserRole[]> {
    try {
      const allRoles = await this.listRoles();
      return allRoles.filter(role => 
        role.permissions.some(p => p.name === permissionName)
      );
    } catch (error) {
      throw new Error(`Failed to get roles by permission: ${error.message}`);
    }
  }

  /**
   * Check role compatibility (for role changes)
   */
  async isRoleChangeAllowed(fromRoleId: string, toRoleId: string): Promise<boolean> {
    try {
      const fromRole = await this.getRole(fromRoleId);
      const toRole = await this.getRole(toRoleId);

      if (!fromRole || !toRole) {
        return false;
      }

      // System roles have special rules
      if (fromRole.isSystem && toRole.isSystem) {
        // Allow changes between system roles
        return true;
      }

      if (fromRole.isSystem && !toRole.isSystem) {
        // Allow downgrade from system to custom role
        return true;
      }

      if (!fromRole.isSystem && toRole.isSystem) {
        // Require special permission for upgrade to system role
        return false;
      }

      // Custom role to custom role is allowed
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Clear role cache
   */
  clearCache(): void {
    this.roleCache.clear();
  }

  /**
   * Warm up role cache
   */
  async warmUpCache(): Promise<void> {
    try {
      await this.listRoles();
    } catch (error) {
      console.error('Failed to warm up role cache:', error);
    }
  }
}
