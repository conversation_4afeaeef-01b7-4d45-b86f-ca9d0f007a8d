/**
 * Token utility functions
 */

import crypto from 'crypto';

export class TokenUtils {
  /**
   * Generate a secure random token
   */
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate a URL-safe random token
   */
  static generateUrlSafeToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('base64url');
  }

  /**
   * Generate a numeric token (for SMS/email verification)
   */
  static generateNumericToken(length: number = 6): string {
    const digits = '0123456789';
    let token = '';
    
    for (let i = 0; i < length; i++) {
      token += digits[crypto.randomInt(0, digits.length)];
    }
    
    return token;
  }

  /**
   * Generate an alphanumeric token
   */
  static generateAlphanumericToken(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    
    for (let i = 0; i < length; i++) {
      token += chars[crypto.randomInt(0, chars.length)];
    }
    
    return token;
  }

  /**
   * Hash a token for secure storage
   */
  static hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Verify a token against its hash
   */
  static verifyToken(token: string, hash: string): boolean {
    const tokenHash = this.hashToken(token);
    return crypto.timingSafeEqual(Buffer.from(tokenHash), Buffer.from(hash));
  }

  /**
   * Generate a token with expiration
   */
  static generateTokenWithExpiration(length: number = 32, expiresInMs: number = 3600000): {
    token: string;
    hash: string;
    expiresAt: Date;
  } {
    const token = this.generateSecureToken(length);
    const hash = this.hashToken(token);
    const expiresAt = new Date(Date.now() + expiresInMs);

    return { token, hash, expiresAt };
  }

  /**
   * Check if a token is expired
   */
  static isTokenExpired(expiresAt: Date): boolean {
    return new Date() > expiresAt;
  }

  /**
   * Generate a CSRF token
   */
  static generateCSRFToken(): string {
    return this.generateUrlSafeToken(32);
  }

  /**
   * Generate an API key
   */
  static generateAPIKey(prefix: string = 'ua'): string {
    const randomPart = this.generateAlphanumericToken(32);
    return `${prefix}_${randomPart}`;
  }

  /**
   * Mask a token for logging (show only first and last few characters)
   */
  static maskToken(token: string, visibleChars: number = 4): string {
    if (token.length <= visibleChars * 2) {
      return '*'.repeat(token.length);
    }

    const start = token.substring(0, visibleChars);
    const end = token.substring(token.length - visibleChars);
    const middle = '*'.repeat(token.length - visibleChars * 2);

    return `${start}${middle}${end}`;
  }

  /**
   * Validate token format
   */
  static validateTokenFormat(token: string, expectedLength?: number): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // Check if token contains only valid characters (hex, base64url, etc.)
    const hexRegex = /^[a-f0-9]+$/i;
    const base64UrlRegex = /^[A-Za-z0-9_-]+$/;
    const alphanumericRegex = /^[A-Za-z0-9]+$/;

    const isValidFormat = hexRegex.test(token) || 
                         base64UrlRegex.test(token) || 
                         alphanumericRegex.test(token);

    if (!isValidFormat) {
      return false;
    }

    if (expectedLength && token.length !== expectedLength) {
      return false;
    }

    return true;
  }

  /**
   * Generate a device fingerprint
   */
  static generateDeviceFingerprint(userAgent: string, ipAddress: string): string {
    const data = `${userAgent}:${ipAddress}`;
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * Generate a session ID
   */
  static generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = this.generateAlphanumericToken(16);
    return `${timestamp}_${randomPart}`;
  }

  /**
   * Extract timestamp from session ID
   */
  static extractTimestampFromSessionId(sessionId: string): Date | null {
    try {
      const parts = sessionId.split('_');
      if (parts.length !== 2) {
        return null;
      }

      const timestamp = parseInt(parts[0], 36);
      return new Date(timestamp);
    } catch {
      return null;
    }
  }
}
