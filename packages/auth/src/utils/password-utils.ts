/**
 * Password utility functions
 */

import crypto from 'crypto';

export class PasswordUtils {
  /**
   * Generate a secure random password
   */
  static generateSecurePassword(options: {
    length?: number;
    includeUppercase?: boolean;
    includeLowercase?: boolean;
    includeNumbers?: boolean;
    includeSymbols?: boolean;
    excludeSimilar?: boolean;
    excludeAmbiguous?: boolean;
  } = {}): string {
    const {
      length = 16,
      includeUppercase = true,
      includeLowercase = true,
      includeNumbers = true,
      includeSymbols = true,
      excludeSimilar = false,
      excludeAmbiguous = false
    } = options;

    let charset = '';
    
    if (includeUppercase) {
      charset += excludeSimilar ? 'ABCDEFGHJKLMNPQRSTUVWXYZ' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    }
    
    if (includeLowercase) {
      charset += excludeSimilar ? 'abcdefghjkmnpqrstuvwxyz' : 'abcdefghijklmnopqrstuvwxyz';
    }
    
    if (includeNumbers) {
      charset += excludeSimilar ? '23456789' : '0123456789';
    }
    
    if (includeSymbols) {
      charset += excludeAmbiguous ? '!@#$%^&*()_+-=[]{}|;:,.<>?' : '!@#$%^&*()_+-=[]{}|;:,.<>?`~';
    }

    if (!charset) {
      throw new Error('At least one character type must be included');
    }

    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset[crypto.randomInt(0, charset.length)];
    }

    return password;
  }

  /**
   * Calculate password entropy
   */
  static calculateEntropy(password: string): number {
    const charsetSize = this.getCharsetSize(password);
    return Math.log2(Math.pow(charsetSize, password.length));
  }

  /**
   * Get password strength score (0-100)
   */
  static getStrengthScore(password: string): {
    score: number;
    feedback: string[];
    level: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';
  } {
    const feedback: string[] = [];
    let score = 0;

    // Length scoring
    if (password.length >= 8) score += 10;
    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 10;
    if (password.length < 8) feedback.push('Use at least 8 characters');

    // Character variety
    if (/[a-z]/.test(password)) score += 5;
    else feedback.push('Add lowercase letters');

    if (/[A-Z]/.test(password)) score += 5;
    else feedback.push('Add uppercase letters');

    if (/\d/.test(password)) score += 5;
    else feedback.push('Add numbers');

    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 10;
    else feedback.push('Add special characters');

    // Pattern penalties
    if (/(.)\1{2,}/.test(password)) {
      score -= 10;
      feedback.push('Avoid repeated characters');
    }

    if (/123|abc|qwe/i.test(password)) {
      score -= 10;
      feedback.push('Avoid sequential patterns');
    }

    if (this.isCommonPassword(password)) {
      score -= 20;
      feedback.push('Avoid common passwords');
    }

    // Entropy bonus
    const entropy = this.calculateEntropy(password);
    if (entropy >= 60) score += 15;
    else if (entropy >= 40) score += 10;
    else if (entropy >= 25) score += 5;

    score = Math.max(0, Math.min(100, score));

    let level: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';
    if (score < 20) level = 'very-weak';
    else if (score < 40) level = 'weak';
    else if (score < 60) level = 'fair';
    else if (score < 80) level = 'good';
    else level = 'strong';

    return { score, feedback, level };
  }

  /**
   * Check if password meets minimum requirements
   */
  static meetsRequirements(password: string, requirements: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSymbols?: boolean;
    maxLength?: number;
  } = {}): { valid: boolean; errors: string[] } {
    const {
      minLength = 8,
      requireUppercase = true,
      requireLowercase = true,
      requireNumbers = true,
      requireSymbols = false,
      maxLength = 128
    } = requirements;

    const errors: string[] = [];

    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }

    if (password.length > maxLength) {
      errors.push(`Password must be no more than ${maxLength} characters long`);
    }

    if (requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (requireSymbols && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate password hash with salt
   */
  static generateHash(password: string, saltRounds: number = 12): Promise<string> {
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(password, crypto.randomBytes(16), saltRounds * 1000, 64, 'sha512', (err, derivedKey) => {
        if (err) reject(err);
        else resolve(derivedKey.toString('hex'));
      });
    });
  }

  /**
   * Time password hashing to determine optimal salt rounds
   */
  static async benchmarkHashing(targetTimeMs: number = 100): Promise<number> {
    const testPassword = 'test-password-for-benchmarking';
    let saltRounds = 10;
    
    while (saltRounds < 20) {
      const start = Date.now();
      await this.generateHash(testPassword, saltRounds);
      const duration = Date.now() - start;
      
      if (duration >= targetTimeMs) {
        return saltRounds;
      }
      
      saltRounds++;
    }
    
    return saltRounds;
  }

  /**
   * Check if password is in common password list
   */
  private static isCommonPassword(password: string): boolean {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'superman', 'michael',
      'football', 'baseball', 'liverpool', 'jordan', 'princess',
      'password1', '12345678', 'iloveyou', 'rockyou', 'daniel',
      'babygirl', 'lovely', 'jessica', 'ashley', 'michelle'
    ];

    return commonPasswords.includes(password.toLowerCase());
  }

  /**
   * Get charset size for entropy calculation
   */
  private static getCharsetSize(password: string): number {
    let size = 0;
    
    if (/[a-z]/.test(password)) size += 26;
    if (/[A-Z]/.test(password)) size += 26;
    if (/\d/.test(password)) size += 10;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) size += 32;
    
    return size;
  }

  /**
   * Generate memorable password using word list
   */
  static generateMemorablePassword(wordCount: number = 4, separator: string = '-'): string {
    const words = [
      'apple', 'brave', 'chair', 'dance', 'eagle', 'flame', 'grace', 'house',
      'image', 'juice', 'knife', 'light', 'music', 'night', 'ocean', 'peace',
      'quiet', 'river', 'stone', 'table', 'unity', 'voice', 'water', 'youth',
      'zebra', 'magic', 'power', 'dream', 'smile', 'heart', 'storm', 'cloud'
    ];

    const selectedWords: string[] = [];
    for (let i = 0; i < wordCount; i++) {
      const randomIndex = crypto.randomInt(0, words.length);
      selectedWords.push(words[randomIndex]);
    }

    // Add random numbers to increase entropy
    const randomNum = crypto.randomInt(10, 999);
    selectedWords.push(randomNum.toString());

    return selectedWords.join(separator);
  }
}
