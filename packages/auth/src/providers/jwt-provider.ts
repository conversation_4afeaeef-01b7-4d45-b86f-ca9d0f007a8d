/**
 * JWT token provider for authentication
 */

import jwt from 'jsonwebtoken';
import { TokenPayload } from '@unified-assistant/types';

export interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
  issuer: string;
  audience: string;
}

export class JW<PERSON>rovider {
  private secret: string;
  private expiresIn: string;
  private refreshExpiresIn: string;
  private issuer: string;
  private audience: string;

  constructor(
    secret: string,
    expiresIn: string = '1h',
    refreshExpiresIn: string = '7d',
    issuer: string = 'unified-assistant',
    audience: string = 'unified-assistant-users'
  ) {
    this.secret = secret;
    this.expiresIn = expiresIn;
    this.refreshExpiresIn = refreshExpiresIn;
    this.issuer = issuer;
    this.audience = audience;
  }

  /**
   * Generate access token
   */
  async generateAccessToken(payload: Partial<TokenPayload>): Promise<string> {
    const tokenPayload: TokenPayload = {
      sub: payload.sub!,
      email: payload.email!,
      username: payload.username!,
      role: payload.role!,
      permissions: payload.permissions || [],
      sessionId: payload.sessionId!,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.parseExpiresIn(this.expiresIn),
      iss: this.issuer,
      aud: this.audience
    };

    return jwt.sign(tokenPayload, this.secret, {
      algorithm: 'HS256',
      expiresIn: this.expiresIn,
      issuer: this.issuer,
      audience: this.audience
    });
  }

  /**
   * Generate refresh token
   */
  async generateRefreshToken(payload: { sub: string; sessionId: string }): Promise<string> {
    const tokenPayload = {
      sub: payload.sub,
      sessionId: payload.sessionId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.parseExpiresIn(this.refreshExpiresIn),
      iss: this.issuer,
      aud: this.audience
    };

    return jwt.sign(tokenPayload, this.secret, {
      algorithm: 'HS256',
      expiresIn: this.refreshExpiresIn,
      issuer: this.issuer,
      audience: this.audience
    });
  }

  /**
   * Generate password reset token
   */
  async generatePasswordResetToken(userId: string): Promise<string> {
    const tokenPayload = {
      sub: userId,
      type: 'password-reset',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
      iss: this.issuer,
      aud: this.audience
    };

    return jwt.sign(tokenPayload, this.secret, {
      algorithm: 'HS256',
      expiresIn: '1h',
      issuer: this.issuer,
      audience: this.audience
    });
  }

  /**
   * Verify access token
   */
  async verifyAccessToken(token: string): Promise<TokenPayload> {
    try {
      const decoded = jwt.verify(token, this.secret, {
        algorithms: ['HS256'],
        issuer: this.issuer,
        audience: this.audience
      }) as TokenPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      } else {
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * Verify refresh token
   */
  async verifyRefreshToken(token: string): Promise<any> {
    try {
      const decoded = jwt.verify(token, this.secret, {
        algorithms: ['HS256'],
        issuer: this.issuer,
        audience: this.audience
      }) as any;

      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid refresh token');
      } else {
        throw new Error('Refresh token verification failed');
      }
    }
  }

  /**
   * Verify password reset token
   */
  async verifyPasswordResetToken(token: string): Promise<any> {
    try {
      const decoded = jwt.verify(token, this.secret, {
        algorithms: ['HS256'],
        issuer: this.issuer,
        audience: this.audience
      }) as any;

      if (decoded.type !== 'password-reset') {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Password reset token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid password reset token');
      } else {
        throw new Error('Password reset token verification failed');
      }
    }
  }

  /**
   * Decode token without verification (for debugging)
   */
  decodeToken(token: string): any {
    return jwt.decode(token);
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as any;
      if (!decoded || !decoded.exp) {
        return true;
      }
      return Date.now() >= decoded.exp * 1000;
    } catch {
      return true;
    }
  }

  /**
   * Parse expires in string to seconds
   */
  private parseExpiresIn(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) return 3600; // default 1 hour

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 3600;
      case 'd': return value * 86400;
      default: return 3600;
    }
  }
}
