/**
 * Two-factor authentication provider
 */

import crypto from 'crypto';
import { TokenUtils } from '../utils/token-utils';

export interface TwoFactorConfig {
  issuer: string;
  digits: number;
  period: number;
  algorithm: 'sha1' | 'sha256' | 'sha512';
}

export interface TwoFactorSetup {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

export class TwoFactorProvider {
  private config: TwoFactorConfig;

  constructor(config?: Partial<TwoFactorConfig>) {
    this.config = {
      issuer: 'Unified Assistant',
      digits: 6,
      period: 30,
      algorithm: 'sha1',
      ...config
    };
  }

  /**
   * Generate TOTP secret for user
   */
  generateSecret(): string {
    return crypto.randomBytes(20).toString('base32');
  }

  /**
   * Setup TOTP for user
   */
  setupTOTP(userEmail: string, secret?: string): TwoFactorSetup {
    const totpSecret = secret || this.generateSecret();
    const qrCodeUrl = this.generateQRCodeUrl(userEmail, totpSecret);
    const backupCodes = this.generateBackupCodes();

    return {
      secret: totpSecret,
      qrCodeUrl,
      backupCodes
    };
  }

  /**
   * Generate TOTP code for current time
   */
  generateTOTP(secret: string, timeStep?: number): string {
    const time = timeStep || Math.floor(Date.now() / 1000 / this.config.period);
    const timeBuffer = Buffer.alloc(8);
    timeBuffer.writeUInt32BE(Math.floor(time / 0x100000000), 0);
    timeBuffer.writeUInt32BE(time & 0xffffffff, 4);

    const hmac = crypto.createHmac(this.config.algorithm, Buffer.from(secret, 'base32'));
    hmac.update(timeBuffer);
    const hash = hmac.digest();

    const offset = hash[hash.length - 1] & 0xf;
    const code = ((hash[offset] & 0x7f) << 24) |
                 ((hash[offset + 1] & 0xff) << 16) |
                 ((hash[offset + 2] & 0xff) << 8) |
                 (hash[offset + 3] & 0xff);

    return (code % Math.pow(10, this.config.digits)).toString().padStart(this.config.digits, '0');
  }

  /**
   * Verify TOTP code
   */
  verifyTOTP(secret: string, token: string, window: number = 1): boolean {
    const currentTime = Math.floor(Date.now() / 1000 / this.config.period);
    
    // Check current time and surrounding windows
    for (let i = -window; i <= window; i++) {
      const timeStep = currentTime + i;
      const expectedToken = this.generateTOTP(secret, timeStep);
      
      if (this.constantTimeCompare(token, expectedToken)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Generate backup codes
   */
  generateBackupCodes(count: number = 10): string[] {
    const codes: string[] = [];
    
    for (let i = 0; i < count; i++) {
      codes.push(TokenUtils.generateNumericToken(8));
    }
    
    return codes;
  }

  /**
   * Verify backup code
   */
  verifyBackupCode(providedCode: string, validCodes: string[]): boolean {
    return validCodes.some(code => this.constantTimeCompare(providedCode, code));
  }

  /**
   * Generate QR code URL for TOTP setup
   */
  private generateQRCodeUrl(userEmail: string, secret: string): string {
    const params = new URLSearchParams({
      secret,
      issuer: this.config.issuer,
      algorithm: this.config.algorithm.toUpperCase(),
      digits: this.config.digits.toString(),
      period: this.config.period.toString()
    });

    const label = encodeURIComponent(`${this.config.issuer}:${userEmail}`);
    return `otpauth://totp/${label}?${params.toString()}`;
  }

  /**
   * Constant time string comparison to prevent timing attacks
   */
  private constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }
}

/**
 * SMS-based two-factor authentication
 */
export class SMSTwoFactorProvider {
  private codeLength: number;
  private codeExpiry: number;

  constructor(codeLength: number = 6, codeExpiryMinutes: number = 5) {
    this.codeLength = codeLength;
    this.codeExpiry = codeExpiryMinutes * 60 * 1000;
  }

  /**
   * Generate SMS verification code
   */
  generateSMSCode(): { code: string; expiresAt: Date } {
    const code = TokenUtils.generateNumericToken(this.codeLength);
    const expiresAt = new Date(Date.now() + this.codeExpiry);

    return { code, expiresAt };
  }

  /**
   * Verify SMS code
   */
  verifySMSCode(providedCode: string, expectedCode: string, expiresAt: Date): boolean {
    if (new Date() > expiresAt) {
      return false;
    }

    return this.constantTimeCompare(providedCode, expectedCode);
  }

  /**
   * Constant time string comparison
   */
  private constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }
}

/**
 * Email-based two-factor authentication
 */
export class EmailTwoFactorProvider {
  private codeLength: number;
  private codeExpiry: number;

  constructor(codeLength: number = 8, codeExpiryMinutes: number = 10) {
    this.codeLength = codeLength;
    this.codeExpiry = codeExpiryMinutes * 60 * 1000;
  }

  /**
   * Generate email verification code
   */
  generateEmailCode(): { code: string; expiresAt: Date } {
    const code = TokenUtils.generateAlphanumericToken(this.codeLength);
    const expiresAt = new Date(Date.now() + this.codeExpiry);

    return { code, expiresAt };
  }

  /**
   * Verify email code
   */
  verifyEmailCode(providedCode: string, expectedCode: string, expiresAt: Date): boolean {
    if (new Date() > expiresAt) {
      return false;
    }

    return this.constantTimeCompare(providedCode.toLowerCase(), expectedCode.toLowerCase());
  }

  /**
   * Constant time string comparison
   */
  private constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }
}
