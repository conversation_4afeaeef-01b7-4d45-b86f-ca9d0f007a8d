/**
 * Authentication middleware for the unified AI assistant platform
 */

import { Request, Response, NextFunction } from 'express';
import { TokenPayload } from '@unified-assistant/types';
import { JWTProvider } from '../providers/jwt-provider';
import { SessionService } from '../services/session-service';
import { UserService } from '../services/user-service';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    username: string;
    role: string;
    permissions: string[];
    sessionId: string;
  };
  token?: TokenPayload;
}

export interface AuthMiddlewareConfig {
  jwtSecret: string;
  skipPaths?: string[];
  requireVerification?: boolean;
}

export class AuthMiddleware {
  private jwtProvider: JWTProvider;
  private sessionService: SessionService;
  private userService: UserService;
  private config: AuthMiddlewareConfig;

  constructor(
    config: AuthMiddlewareConfig,
    sessionService: SessionService,
    userService: UserService
  ) {
    this.config = config;
    this.sessionService = sessionService;
    this.userService = userService;
    this.jwtProvider = new JWTProvider(config.jwtSecret);
  }

  /**
   * Main authentication middleware
   */
  authenticate() {
    return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      try {
        // Skip authentication for certain paths
        if (this.shouldSkipAuth(req.path)) {
          return next();
        }

        // Extract token from request
        const token = this.extractToken(req);
        if (!token) {
          return this.sendUnauthorized(res, 'No authentication token provided');
        }

        // Verify token
        const payload = await this.jwtProvider.verifyAccessToken(token);
        
        // Verify session
        const session = await this.sessionService.findById(payload.sessionId);
        if (!session || !session.isActive) {
          return this.sendUnauthorized(res, 'Invalid session');
        }

        // Get user details
        const user = await this.userService.findById(payload.sub);
        if (!user || !user.isActive) {
          return this.sendUnauthorized(res, 'User not found or inactive');
        }

        // Check if user verification is required
        if (this.config.requireVerification && !user.isVerified) {
          return this.sendForbidden(res, 'Email verification required');
        }

        // Touch session to update last accessed time
        await this.sessionService.touch(session.id);

        // Attach user info to request
        req.user = {
          id: user.id,
          email: user.email,
          username: user.username,
          role: user.role.name,
          permissions: user.permissions.map(p => p.name),
          sessionId: session.id
        };
        req.token = payload;

        next();
      } catch (error) {
        if (error.message.includes('expired')) {
          return this.sendUnauthorized(res, 'Token expired');
        } else if (error.message.includes('invalid')) {
          return this.sendUnauthorized(res, 'Invalid token');
        } else {
          console.error('Authentication error:', error);
          return this.sendUnauthorized(res, 'Authentication failed');
        }
      }
    };
  }

  /**
   * Authorization middleware for specific permissions
   */
  requirePermission(permission: string) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return this.sendUnauthorized(res, 'Authentication required');
      }

      if (!req.user.permissions.includes(permission)) {
        return this.sendForbidden(res, `Permission required: ${permission}`);
      }

      next();
    };
  }

  /**
   * Authorization middleware for specific roles
   */
  requireRole(role: string) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return this.sendUnauthorized(res, 'Authentication required');
      }

      if (req.user.role !== role) {
        return this.sendForbidden(res, `Role required: ${role}`);
      }

      next();
    };
  }

  /**
   * Authorization middleware for multiple roles (OR logic)
   */
  requireAnyRole(roles: string[]) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return this.sendUnauthorized(res, 'Authentication required');
      }

      if (!roles.includes(req.user.role)) {
        return this.sendForbidden(res, `One of these roles required: ${roles.join(', ')}`);
      }

      next();
    };
  }

  /**
   * Authorization middleware for multiple permissions (AND logic)
   */
  requireAllPermissions(permissions: string[]) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return this.sendUnauthorized(res, 'Authentication required');
      }

      const hasAllPermissions = permissions.every(permission => 
        req.user!.permissions.includes(permission)
      );

      if (!hasAllPermissions) {
        return this.sendForbidden(res, `All permissions required: ${permissions.join(', ')}`);
      }

      next();
    };
  }

  /**
   * Authorization middleware for multiple permissions (OR logic)
   */
  requireAnyPermission(permissions: string[]) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return this.sendUnauthorized(res, 'Authentication required');
      }

      const hasAnyPermission = permissions.some(permission => 
        req.user!.permissions.includes(permission)
      );

      if (!hasAnyPermission) {
        return this.sendForbidden(res, `One of these permissions required: ${permissions.join(', ')}`);
      }

      next();
    };
  }

  /**
   * Optional authentication middleware (doesn't fail if no token)
   */
  optionalAuth() {
    return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      try {
        const token = this.extractToken(req);
        if (!token) {
          return next();
        }

        const payload = await this.jwtProvider.verifyAccessToken(token);
        const session = await this.sessionService.findById(payload.sessionId);
        
        if (session && session.isActive) {
          const user = await this.userService.findById(payload.sub);
          if (user && user.isActive) {
            req.user = {
              id: user.id,
              email: user.email,
              username: user.username,
              role: user.role.name,
              permissions: user.permissions.map(p => p.name),
              sessionId: session.id
            };
            req.token = payload;
            
            await this.sessionService.touch(session.id);
          }
        }

        next();
      } catch (error) {
        // Ignore authentication errors in optional auth
        next();
      }
    };
  }

  /**
   * Extract token from request headers
   */
  private extractToken(req: Request): string | null {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Also check for token in cookies
    const cookieToken = req.cookies?.accessToken;
    if (cookieToken) {
      return cookieToken;
    }

    return null;
  }

  /**
   * Check if authentication should be skipped for this path
   */
  private shouldSkipAuth(path: string): boolean {
    if (!this.config.skipPaths) {
      return false;
    }

    return this.config.skipPaths.some(skipPath => {
      if (skipPath.endsWith('*')) {
        return path.startsWith(skipPath.slice(0, -1));
      }
      return path === skipPath;
    });
  }

  /**
   * Send unauthorized response
   */
  private sendUnauthorized(res: Response, message: string): void {
    res.status(401).json({
      error: 'Unauthorized',
      message,
      code: 'AUTH_REQUIRED'
    });
  }

  /**
   * Send forbidden response
   */
  private sendForbidden(res: Response, message: string): void {
    res.status(403).json({
      error: 'Forbidden',
      message,
      code: 'INSUFFICIENT_PERMISSIONS'
    });
  }
}
