/**
 * Core authentication service for the unified AI assistant platform
 */

import {
  User,
  Session,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  LogoutRequest,
  PasswordResetRequest,
  PasswordResetConfirmRequest,
  ChangePasswordRequest,
  CreateUserRequest,
  AuthToken
} from '@unified-assistant/types';

import { JWTProvider } from '../providers/jwt-provider';
import { PasswordProvider } from '../providers/password-provider';
import { UserService } from './user-service';
import { SessionService } from './session-service';

export interface AuthServiceConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  refreshTokenExpiresIn: string;
  passwordResetExpiresIn: string;
  maxLoginAttempts: number;
  lockoutDuration: number;
}

export class AuthService {
  private jwtProvider: JWTProvider;
  private passwordProvider: PasswordProvider;
  private userService: UserService;
  private sessionService: SessionService;
  private config: AuthServiceConfig;

  constructor(
    config: AuthServiceConfig,
    userService: UserService,
    sessionService: SessionService
  ) {
    this.config = config;
    this.userService = userService;
    this.sessionService = sessionService;
    this.jwtProvider = new JWTProvider(config.jwtSecret, config.jwtExpiresIn);
    this.passwordProvider = new PasswordProvider();
  }

  /**
   * Authenticate user with email and password
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      // Find user by email
      const user = await this.userService.findByEmail(request.email);
      if (!user) {
        throw new Error('Invalid credentials');
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('Account is deactivated');
      }

      // Verify password
      const isValidPassword = await this.passwordProvider.verify(
        request.password,
        user.password
      );
      if (!isValidPassword) {
        await this.handleFailedLogin(user.id);
        throw new Error('Invalid credentials');
      }

      // Create session
      const session = await this.sessionService.create({
        userId: user.id,
        ipAddress: request.deviceInfo?.deviceId || 'unknown',
        userAgent: request.deviceInfo?.browser || 'unknown',
        expiresAt: new Date(Date.now() + this.parseExpiresIn(this.config.refreshTokenExpiresIn))
      });

      // Generate tokens
      const tokens = await this.generateTokens(user, session.id);

      // Update last login
      await this.userService.updateLastLogin(user.id);

      return {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          displayName: user.displayName,
          role: user.role.name,
          permissions: user.permissions.map(p => p.name)
        },
        session: tokens
      };
    } catch (error) {
      throw new Error(`Login failed: ${error.message}`);
    }
  }

  /**
   * Refresh authentication tokens
   */
  async refreshToken(request: RefreshTokenRequest): Promise<AuthToken> {
    try {
      // Verify refresh token
      const payload = await this.jwtProvider.verifyRefreshToken(request.refreshToken);
      
      // Get session
      const session = await this.sessionService.findById(payload.sessionId);
      if (!session || !session.isActive) {
        throw new Error('Invalid session');
      }

      // Get user
      const user = await this.userService.findById(session.userId);
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      // Generate new tokens
      return await this.generateTokens(user, session.id);
    } catch (error) {
      throw new Error(`Token refresh failed: ${error.message}`);
    }
  }

  /**
   * Logout user and invalidate session
   */
  async logout(request: LogoutRequest, userId: string): Promise<void> {
    try {
      if (request.allSessions) {
        await this.sessionService.invalidateAllForUser(userId);
      } else if (request.sessionId) {
        await this.sessionService.invalidate(request.sessionId);
      }
    } catch (error) {
      throw new Error(`Logout failed: ${error.message}`);
    }
  }

  /**
   * Register new user
   */
  async register(request: CreateUserRequest): Promise<User> {
    try {
      // Check if user already exists
      const existingUser = await this.userService.findByEmail(request.email);
      if (existingUser) {
        throw new Error('User already exists');
      }

      // Hash password
      const hashedPassword = await this.passwordProvider.hash(request.password);

      // Create user
      return await this.userService.create({
        ...request,
        password: hashedPassword
      });
    } catch (error) {
      throw new Error(`Registration failed: ${error.message}`);
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(request: PasswordResetRequest): Promise<void> {
    try {
      const user = await this.userService.findByEmail(request.email);
      if (!user) {
        // Don't reveal if user exists
        return;
      }

      // Generate reset token
      const resetToken = await this.jwtProvider.generatePasswordResetToken(user.id);
      
      // Store reset token (implementation depends on storage strategy)
      await this.userService.storePasswordResetToken(user.id, resetToken);

      // Send reset email (implementation depends on email service)
      // await this.emailService.sendPasswordReset(user.email, resetToken);
    } catch (error) {
      throw new Error(`Password reset request failed: ${error.message}`);
    }
  }

  /**
   * Confirm password reset
   */
  async confirmPasswordReset(request: PasswordResetConfirmRequest): Promise<void> {
    try {
      // Verify reset token
      const payload = await this.jwtProvider.verifyPasswordResetToken(request.token);
      
      // Get user
      const user = await this.userService.findById(payload.sub);
      if (!user) {
        throw new Error('Invalid reset token');
      }

      // Hash new password
      const hashedPassword = await this.passwordProvider.hash(request.newPassword);

      // Update password
      await this.userService.updatePassword(user.id, hashedPassword);

      // Invalidate all sessions
      await this.sessionService.invalidateAllForUser(user.id);
    } catch (error) {
      throw new Error(`Password reset failed: ${error.message}`);
    }
  }

  /**
   * Change user password
   */
  async changePassword(request: ChangePasswordRequest, userId: string): Promise<void> {
    try {
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isValidPassword = await this.passwordProvider.verify(
        request.currentPassword,
        user.password
      );
      if (!isValidPassword) {
        throw new Error('Invalid current password');
      }

      // Hash new password
      const hashedPassword = await this.passwordProvider.hash(request.newPassword);

      // Update password
      await this.userService.updatePassword(userId, hashedPassword);
    } catch (error) {
      throw new Error(`Password change failed: ${error.message}`);
    }
  }

  /**
   * Generate access and refresh tokens
   */
  private async generateTokens(user: User, sessionId: string): Promise<AuthToken> {
    const accessToken = await this.jwtProvider.generateAccessToken({
      sub: user.id,
      email: user.email,
      username: user.username,
      role: user.role.name,
      permissions: user.permissions.map(p => p.name),
      sessionId
    });

    const refreshToken = await this.jwtProvider.generateRefreshToken({
      sub: user.id,
      sessionId
    });

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: this.parseExpiresIn(this.config.jwtExpiresIn)
    };
  }

  /**
   * Handle failed login attempt
   */
  private async handleFailedLogin(userId: string): Promise<void> {
    // Implementation for tracking failed login attempts
    // This could involve incrementing a counter and locking the account
  }

  /**
   * Parse expires in string to seconds
   */
  private parseExpiresIn(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) return 3600; // default 1 hour

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 3600;
      case 'd': return value * 86400;
      default: return 3600;
    }
  }
}
