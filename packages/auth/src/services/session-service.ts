/**
 * Session management service
 */

import { Session, SessionQuery } from '@unified-assistant/types';

export interface CreateSessionRequest {
  userId: string;
  ipAddress: string;
  userAgent: string;
  expiresAt: Date;
  metadata?: Record<string, any>;
}

export interface SessionRepository {
  create(request: CreateSessionRequest): Promise<Session>;
  findById(id: string): Promise<Session | null>;
  findByUserId(userId: string): Promise<Session[]>;
  update(id: string, updates: Partial<Session>): Promise<Session>;
  delete(id: string): Promise<void>;
  deleteByUserId(userId: string): Promise<void>;
  list(query: SessionQuery): Promise<{ sessions: Session[]; total: number }>;
  cleanup(): Promise<number>; // Remove expired sessions
}

export class SessionService {
  private repository: SessionRepository;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(repository: SessionRepository) {
    this.repository = repository;
    this.startCleanupTimer();
  }

  /**
   * Create a new session
   */
  async create(request: CreateSessionRequest): Promise<Session> {
    try {
      // Validate session data
      if (!request.userId || !request.ipAddress || !request.userAgent) {
        throw new Error('Missing required session data');
      }

      if (request.expiresAt <= new Date()) {
        throw new Error('Session expiration date must be in the future');
      }

      return await this.repository.create(request);
    } catch (error) {
      throw new Error(`Session creation failed: ${error.message}`);
    }
  }

  /**
   * Find session by ID
   */
  async findById(id: string): Promise<Session | null> {
    try {
      const session = await this.repository.findById(id);
      
      // Check if session is expired
      if (session && this.isExpired(session)) {
        await this.invalidate(id);
        return null;
      }

      return session;
    } catch (error) {
      throw new Error(`Failed to find session: ${error.message}`);
    }
  }

  /**
   * Find all sessions for a user
   */
  async findByUserId(userId: string): Promise<Session[]> {
    try {
      const sessions = await this.repository.findByUserId(userId);
      
      // Filter out expired sessions
      const activeSessions = sessions.filter(session => !this.isExpired(session));
      
      // Clean up expired sessions
      const expiredSessions = sessions.filter(session => this.isExpired(session));
      for (const session of expiredSessions) {
        await this.invalidate(session.id);
      }

      return activeSessions;
    } catch (error) {
      throw new Error(`Failed to find user sessions: ${error.message}`);
    }
  }

  /**
   * Update session
   */
  async update(id: string, updates: Partial<Session>): Promise<Session> {
    try {
      const session = await this.repository.findById(id);
      if (!session) {
        throw new Error('Session not found');
      }

      if (this.isExpired(session)) {
        throw new Error('Cannot update expired session');
      }

      return await this.repository.update(id, updates);
    } catch (error) {
      throw new Error(`Session update failed: ${error.message}`);
    }
  }

  /**
   * Invalidate a session
   */
  async invalidate(id: string): Promise<void> {
    try {
      await this.repository.delete(id);
    } catch (error) {
      throw new Error(`Session invalidation failed: ${error.message}`);
    }
  }

  /**
   * Invalidate all sessions for a user
   */
  async invalidateAllForUser(userId: string): Promise<void> {
    try {
      await this.repository.deleteByUserId(userId);
    } catch (error) {
      throw new Error(`Failed to invalidate user sessions: ${error.message}`);
    }
  }

  /**
   * Extend session expiration
   */
  async extend(id: string, additionalTime: number): Promise<Session> {
    try {
      const session = await this.repository.findById(id);
      if (!session) {
        throw new Error('Session not found');
      }

      if (this.isExpired(session)) {
        throw new Error('Cannot extend expired session');
      }

      const newExpiresAt = new Date(session.expiresAt.getTime() + additionalTime);
      
      return await this.repository.update(id, {
        expiresAt: newExpiresAt,
        lastAccessedAt: new Date()
      });
    } catch (error) {
      throw new Error(`Session extension failed: ${error.message}`);
    }
  }

  /**
   * Touch session (update last accessed time)
   */
  async touch(id: string): Promise<void> {
    try {
      const session = await this.repository.findById(id);
      if (!session) {
        return; // Session doesn't exist, nothing to touch
      }

      if (this.isExpired(session)) {
        await this.invalidate(id);
        return;
      }

      await this.repository.update(id, {
        lastAccessedAt: new Date()
      });
    } catch (error) {
      throw new Error(`Session touch failed: ${error.message}`);
    }
  }

  /**
   * List sessions with pagination and filtering
   */
  async list(query: SessionQuery = {}): Promise<{ sessions: Session[]; total: number }> {
    try {
      return await this.repository.list(query);
    } catch (error) {
      throw new Error(`Failed to list sessions: ${error.message}`);
    }
  }

  /**
   * Get session statistics
   */
  async getStatistics(): Promise<{
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
    uniqueUsers: number;
  }> {
    try {
      const { sessions, total } = await this.repository.list({});
      
      const activeSessions = sessions.filter(s => !this.isExpired(s));
      const expiredSessions = sessions.filter(s => this.isExpired(s));
      const uniqueUsers = new Set(sessions.map(s => s.userId)).size;

      return {
        totalSessions: total,
        activeSessions: activeSessions.length,
        expiredSessions: expiredSessions.length,
        uniqueUsers
      };
    } catch (error) {
      throw new Error(`Failed to get session statistics: ${error.message}`);
    }
  }

  /**
   * Cleanup expired sessions
   */
  async cleanup(): Promise<number> {
    try {
      return await this.repository.cleanup();
    } catch (error) {
      throw new Error(`Session cleanup failed: ${error.message}`);
    }
  }

  /**
   * Check if session is expired
   */
  private isExpired(session: Session): boolean {
    return session.expiresAt <= new Date();
  }

  /**
   * Start automatic cleanup timer
   */
  private startCleanupTimer(): void {
    // Run cleanup every hour
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanup();
      } catch (error) {
        console.error('Session cleanup error:', error);
      }
    }, 60 * 60 * 1000);
  }

  /**
   * Stop cleanup timer
   */
  stopCleanupTimer(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Destroy service and cleanup resources
   */
  destroy(): void {
    this.stopCleanupTimer();
  }
}
