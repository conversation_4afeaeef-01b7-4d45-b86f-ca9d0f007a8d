/**
 * User management service
 */

import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserQuery,
  UserRole,
  Permission
} from '@unified-assistant/types';

export interface UserRepository {
  create(user: CreateUserRequest & { password: string }): Promise<User>;
  findById(id: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findByUsername(username: string): Promise<User | null>;
  update(id: string, updates: UpdateUserRequest): Promise<User>;
  delete(id: string): Promise<void>;
  list(query: UserQuery): Promise<{ users: User[]; total: number }>;
  updatePassword(id: string, hashedPassword: string): Promise<void>;
  updateLastLogin(id: string): Promise<void>;
  storePasswordResetToken(id: string, token: string): Promise<void>;
  getPasswordResetToken(id: string): Promise<string | null>;
  clearPasswordResetToken(id: string): Promise<void>;
}

export class UserService {
  private repository: UserRepository;

  constructor(repository: UserRepository) {
    this.repository = repository;
  }

  /**
   * Create a new user
   */
  async create(request: CreateUserRequest & { password: string }): Promise<User> {
    try {
      // Validate email format
      if (!this.isValidEmail(request.email)) {
        throw new Error('Invalid email format');
      }

      // Validate username format
      if (!this.isValidUsername(request.username)) {
        throw new Error('Invalid username format');
      }

      // Check if email already exists
      const existingEmail = await this.repository.findByEmail(request.email);
      if (existingEmail) {
        throw new Error('Email already exists');
      }

      // Check if username already exists
      const existingUsername = await this.repository.findByUsername(request.username);
      if (existingUsername) {
        throw new Error('Username already exists');
      }

      return await this.repository.create(request);
    } catch (error) {
      throw new Error(`User creation failed: ${error.message}`);
    }
  }

  /**
   * Find user by ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      return await this.repository.findById(id);
    } catch (error) {
      throw new Error(`Failed to find user by ID: ${error.message}`);
    }
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      return await this.repository.findByEmail(email);
    } catch (error) {
      throw new Error(`Failed to find user by email: ${error.message}`);
    }
  }

  /**
   * Find user by username
   */
  async findByUsername(username: string): Promise<User | null> {
    try {
      return await this.repository.findByUsername(username);
    } catch (error) {
      throw new Error(`Failed to find user by username: ${error.message}`);
    }
  }

  /**
   * Update user
   */
  async update(id: string, updates: UpdateUserRequest): Promise<User> {
    try {
      const user = await this.repository.findById(id);
      if (!user) {
        throw new Error('User not found');
      }

      return await this.repository.update(id, updates);
    } catch (error) {
      throw new Error(`User update failed: ${error.message}`);
    }
  }

  /**
   * Delete user
   */
  async delete(id: string): Promise<void> {
    try {
      const user = await this.repository.findById(id);
      if (!user) {
        throw new Error('User not found');
      }

      await this.repository.delete(id);
    } catch (error) {
      throw new Error(`User deletion failed: ${error.message}`);
    }
  }

  /**
   * List users with pagination and filtering
   */
  async list(query: UserQuery = {}): Promise<{ users: User[]; total: number }> {
    try {
      return await this.repository.list(query);
    } catch (error) {
      throw new Error(`Failed to list users: ${error.message}`);
    }
  }

  /**
   * Update user password
   */
  async updatePassword(id: string, hashedPassword: string): Promise<void> {
    try {
      const user = await this.repository.findById(id);
      if (!user) {
        throw new Error('User not found');
      }

      await this.repository.updatePassword(id, hashedPassword);
    } catch (error) {
      throw new Error(`Password update failed: ${error.message}`);
    }
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(id: string): Promise<void> {
    try {
      await this.repository.updateLastLogin(id);
    } catch (error) {
      throw new Error(`Failed to update last login: ${error.message}`);
    }
  }

  /**
   * Store password reset token
   */
  async storePasswordResetToken(id: string, token: string): Promise<void> {
    try {
      await this.repository.storePasswordResetToken(id, token);
    } catch (error) {
      throw new Error(`Failed to store password reset token: ${error.message}`);
    }
  }

  /**
   * Get password reset token
   */
  async getPasswordResetToken(id: string): Promise<string | null> {
    try {
      return await this.repository.getPasswordResetToken(id);
    } catch (error) {
      throw new Error(`Failed to get password reset token: ${error.message}`);
    }
  }

  /**
   * Clear password reset token
   */
  async clearPasswordResetToken(id: string): Promise<void> {
    try {
      await this.repository.clearPasswordResetToken(id);
    } catch (error) {
      throw new Error(`Failed to clear password reset token: ${error.message}`);
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate username format
   */
  private isValidUsername(username: string): boolean {
    // Username should be 3-30 characters, alphanumeric and underscores only
    const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
    return usernameRegex.test(username);
  }

  /**
   * Check if user has permission
   */
  async hasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const user = await this.repository.findById(userId);
      if (!user) {
        return false;
      }

      return user.permissions.some(p => p.name === permission);
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if user has role
   */
  async hasRole(userId: string, roleName: string): Promise<boolean> {
    try {
      const user = await this.repository.findById(userId);
      if (!user) {
        return false;
      }

      return user.role.name === roleName;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get user permissions
   */
  async getPermissions(userId: string): Promise<Permission[]> {
    try {
      const user = await this.repository.findById(userId);
      if (!user) {
        return [];
      }

      return user.permissions;
    } catch (error) {
      return [];
    }
  }
}
