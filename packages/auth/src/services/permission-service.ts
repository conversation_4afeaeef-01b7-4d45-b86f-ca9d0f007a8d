/**
 * Permission service for managing user permissions
 */

import { User, Permission } from '@unified-assistant/types';
import { PermissionManager } from '../authorization/permission-manager';
import { RoleManager } from '../authorization/role-manager';
import { PolicyEnforcer } from '../authorization/policy-enforcer';

export class PermissionService {
  private permissionManager: PermissionManager;
  private roleManager: RoleManager;
  private policyEnforcer: PolicyEnforcer;

  constructor(
    permissionManager: PermissionManager,
    roleManager: RoleManager,
    policyEnforcer: PolicyEnforcer
  ) {
    this.permissionManager = permissionManager;
    this.roleManager = roleManager;
    this.policyEnforcer = policyEnforcer;
  }

  /**
   * Check if user has permission
   */
  async hasPermission(user: User, permission: string): Promise<boolean> {
    try {
      return await this.permissionManager.hasPermission(user, permission);
    } catch (error) {
      console.error('Permission check error:', error);
      return false;
    }
  }

  /**
   * Check if user can perform action on resource
   */
  async canPerform(user: User, resource: string, action: string, data?: Record<string, any>): Promise<boolean> {
    try {
      return await this.policyEnforcer.canPerform(user, resource, action, data);
    } catch (error) {
      console.error('Authorization check error:', error);
      return false;
    }
  }

  /**
   * Get user's effective permissions
   */
  async getUserPermissions(user: User): Promise<Permission[]> {
    try {
      return await this.permissionManager.getEffectivePermissions(user);
    } catch (error) {
      console.error('Get user permissions error:', error);
      return [];
    }
  }

  /**
   * Check multiple permissions at once
   */
  async hasAnyPermission(user: User, permissions: string[]): Promise<boolean> {
    try {
      return await this.permissionManager.hasAnyPermission(user, permissions);
    } catch (error) {
      console.error('Multiple permission check error:', error);
      return false;
    }
  }

  /**
   * Check if user has all permissions
   */
  async hasAllPermissions(user: User, permissions: string[]): Promise<boolean> {
    try {
      return await this.permissionManager.hasAllPermissions(user, permissions);
    } catch (error) {
      console.error('All permissions check error:', error);
      return false;
    }
  }

  /**
   * Evaluate authorization with detailed response
   */
  async evaluateAuthorization(user: User, resource: string, action: string, data?: Record<string, any>) {
    try {
      const context = {
        user,
        resource,
        action,
        data,
        environment: {
          timestamp: new Date().toISOString(),
          userAgent: 'permission-service'
        }
      };

      return await this.policyEnforcer.evaluate(context);
    } catch (error) {
      return {
        allowed: false,
        reason: `Authorization evaluation error: ${error.message}`,
        appliedRules: []
      };
    }
  }

  /**
   * Batch authorization check
   */
  async batchAuthorization(
    user: User,
    requests: Array<{ resource: string; action: string; data?: Record<string, any> }>
  ) {
    try {
      return await this.policyEnforcer.canPerformBatch(user, requests);
    } catch (error) {
      console.error('Batch authorization error:', error);
      return requests.map(() => ({
        allowed: false,
        reason: 'Authorization check failed'
      }));
    }
  }
}
