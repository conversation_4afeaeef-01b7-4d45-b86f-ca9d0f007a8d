/**
 * Authentication and authorization system for the unified AI assistant platform
 */

// Core authentication services
export * from './services/auth-service';
export * from './services/user-service';
export * from './services/session-service';
export * from './services/permission-service';

// Authentication providers
export * from './providers/jwt-provider';
export * from './providers/oauth-provider';
export * from './providers/password-provider';
export * from './providers/two-factor-provider';

// Authorization components
export * from './authorization/permission-manager';
export * from './authorization/role-manager';
export * from './authorization/policy-enforcer';

// Middleware and utilities
export * from './middleware/auth-middleware';
export * from './utils/password-utils';
export * from './utils/token-utils';
