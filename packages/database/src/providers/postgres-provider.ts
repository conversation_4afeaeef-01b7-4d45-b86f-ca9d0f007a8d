/**
 * PostgreSQL database provider
 */

import { Pool, PoolClient, PoolConfig } from 'pg';
import { DatabaseProvider, DatabaseConfig } from '../services/database-service';

export class PostgresProvider implements DatabaseProvider {
  public readonly name = 'postgres';
  private pool: Pool | null = null;
  private config: DatabaseConfig | null = null;

  /**
   * Connect to PostgreSQL database
   */
  async connect(config: DatabaseConfig): Promise<void> {
    try {
      this.config = config;
      
      const poolConfig: PoolConfig = {
        host: config.host || 'localhost',
        port: config.port || 5432,
        database: config.database,
        user: config.username,
        password: config.password,
        ssl: config.ssl ? { rejectUnauthorized: false } : false,
        connectionTimeoutMillis: config.connectionTimeout || 10000,
        max: config.maxConnections || 20,
        min: config.minConnections || 2,
        idleTimeoutMillis: config.idleTimeout || 30000,
        acquireTimeoutMillis: config.acquireTimeout || 60000
      };

      this.pool = new Pool(poolConfig);

      // Test connection
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();

      // Set up error handling
      this.pool.on('error', (err) => {
        console.error('PostgreSQL pool error:', err);
      });

    } catch (error) {
      throw new Error(`PostgreSQL connection failed: ${error.message}`);
    }
  }

  /**
   * Disconnect from database
   */
  async disconnect(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
    }
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.pool !== null && !this.pool.ended;
  }

  /**
   * Execute a query
   */
  async query(sql: string, params?: any[]): Promise<any> {
    if (!this.pool) {
      throw new Error('Database not connected');
    }

    try {
      const result = await this.pool.query(sql, params);
      return {
        rows: result.rows,
        rowCount: result.rowCount,
        fields: result.fields
      };
    } catch (error) {
      throw new Error(`Query execution failed: ${error.message}`);
    }
  }

  /**
   * Execute a transaction
   */
  async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    if (!this.pool) {
      throw new Error('Database not connected');
    }

    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get raw connection pool
   */
  getConnection(): Pool {
    if (!this.pool) {
      throw new Error('Database not connected');
    }
    return this.pool;
  }

  /**
   * Perform health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.pool) {
        return false;
      }

      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Create database schema
   */
  async createSchema(schemaName: string): Promise<void> {
    await this.query(`CREATE SCHEMA IF NOT EXISTS ${schemaName}`);
  }

  /**
   * Drop database schema
   */
  async dropSchema(schemaName: string, cascade: boolean = false): Promise<void> {
    const cascadeClause = cascade ? 'CASCADE' : 'RESTRICT';
    await this.query(`DROP SCHEMA IF EXISTS ${schemaName} ${cascadeClause}`);
  }

  /**
   * List all tables
   */
  async listTables(schema: string = 'public'): Promise<string[]> {
    const result = await this.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = $1 AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `, [schema]);
    
    return result.rows.map((row: any) => row.table_name);
  }

  /**
   * Check if table exists
   */
  async tableExists(tableName: string, schema: string = 'public'): Promise<boolean> {
    const result = await this.query(`
      SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = $1 AND table_name = $2
      )
    `, [schema, tableName]);
    
    return result.rows[0].exists;
  }

  /**
   * Get table schema information
   */
  async getTableSchema(tableName: string, schema: string = 'public'): Promise<any[]> {
    const result = await this.query(`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale
      FROM information_schema.columns
      WHERE table_schema = $1 AND table_name = $2
      ORDER BY ordinal_position
    `, [schema, tableName]);
    
    return result.rows;
  }

  /**
   * Create index
   */
  async createIndex(
    tableName: string, 
    columns: string[], 
    options: {
      name?: string;
      unique?: boolean;
      concurrent?: boolean;
      where?: string;
    } = {}
  ): Promise<void> {
    const indexName = options.name || `idx_${tableName}_${columns.join('_')}`;
    const uniqueClause = options.unique ? 'UNIQUE' : '';
    const concurrentClause = options.concurrent ? 'CONCURRENTLY' : '';
    const whereClause = options.where ? `WHERE ${options.where}` : '';
    
    const sql = `
      CREATE ${uniqueClause} INDEX ${concurrentClause} ${indexName}
      ON ${tableName} (${columns.join(', ')})
      ${whereClause}
    `.trim();
    
    await this.query(sql);
  }

  /**
   * Drop index
   */
  async dropIndex(indexName: string, concurrent: boolean = false): Promise<void> {
    const concurrentClause = concurrent ? 'CONCURRENTLY' : '';
    await this.query(`DROP INDEX ${concurrentClause} IF EXISTS ${indexName}`);
  }

  /**
   * Analyze table statistics
   */
  async analyzeTable(tableName: string): Promise<void> {
    await this.query(`ANALYZE ${tableName}`);
  }

  /**
   * Vacuum table
   */
  async vacuumTable(tableName: string, full: boolean = false): Promise<void> {
    const fullClause = full ? 'FULL' : '';
    await this.query(`VACUUM ${fullClause} ${tableName}`);
  }

  /**
   * Get database size
   */
  async getDatabaseSize(): Promise<string> {
    if (!this.config) {
      throw new Error('Database config not available');
    }

    const result = await this.query(`
      SELECT pg_size_pretty(pg_database_size($1)) as size
    `, [this.config.database]);
    
    return result.rows[0].size;
  }

  /**
   * Get table size
   */
  async getTableSize(tableName: string): Promise<string> {
    const result = await this.query(`
      SELECT pg_size_pretty(pg_total_relation_size($1)) as size
    `, [tableName]);
    
    return result.rows[0].size;
  }

  /**
   * Get active connections count
   */
  async getActiveConnections(): Promise<number> {
    if (!this.config) {
      throw new Error('Database config not available');
    }

    const result = await this.query(`
      SELECT count(*) as count
      FROM pg_stat_activity
      WHERE datname = $1 AND state = 'active'
    `, [this.config.database]);
    
    return parseInt(result.rows[0].count);
  }

  /**
   * Kill long running queries
   */
  async killLongRunningQueries(maxDurationMinutes: number = 30): Promise<number> {
    const result = await this.query(`
      SELECT pg_terminate_backend(pid)
      FROM pg_stat_activity
      WHERE state = 'active'
        AND query_start < NOW() - INTERVAL '${maxDurationMinutes} minutes'
        AND pid != pg_backend_pid()
    `);
    
    return result.rowCount || 0;
  }
}
