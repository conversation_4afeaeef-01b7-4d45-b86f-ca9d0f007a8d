/**
 * Database utility functions
 */

export class DatabaseUtils {
  /**
   * Escape SQL identifier (table name, column name, etc.)
   */
  static escapeIdentifier(identifier: string): string {
    return `"${identifier.replace(/"/g, '""')}"`;
  }

  /**
   * Escape SQL string value
   */
  static escapeString(value: string): string {
    return `'${value.replace(/'/g, "''")}'`;
  }

  /**
   * Build WHERE clause from object
   */
  static buildWhereClause(
    conditions: Record<string, any>,
    startIndex: number = 1
  ): { clause: string; params: any[] } {
    const params: any[] = [];
    const clauses: string[] = [];
    let paramIndex = startIndex;

    for (const [key, value] of Object.entries(conditions)) {
      if (value === null) {
        clauses.push(`${key} IS NULL`);
      } else if (value === undefined) {
        continue; // Skip undefined values
      } else if (Array.isArray(value)) {
        if (value.length === 0) {
          clauses.push('FALSE'); // Empty array means no matches
        } else {
          const placeholders = value.map(() => `$${paramIndex++}`);
          clauses.push(`${key} IN (${placeholders.join(', ')})`);
          params.push(...value);
        }
      } else if (typeof value === 'object' && value.operator) {
        // Support for complex operators like { operator: '>', value: 10 }
        clauses.push(`${key} ${value.operator} $${paramIndex++}`);
        params.push(value.value);
      } else {
        clauses.push(`${key} = $${paramIndex++}`);
        params.push(value);
      }
    }

    return {
      clause: clauses.length > 0 ? clauses.join(' AND ') : '',
      params
    };
  }

  /**
   * Build ORDER BY clause from object
   */
  static buildOrderByClause(orderBy: Record<string, 'ASC' | 'DESC'>): string {
    const clauses = Object.entries(orderBy).map(([column, direction]) => 
      `${column} ${direction}`
    );
    
    return clauses.length > 0 ? clauses.join(', ') : '';
  }

  /**
   * Build INSERT query
   */
  static buildInsertQuery(
    tableName: string,
    data: Record<string, any>,
    returning: string[] = ['*']
  ): { sql: string; params: any[] } {
    const columns = Object.keys(data);
    const values = Object.values(data);
    const placeholders = values.map((_, index) => `$${index + 1}`);

    const sql = `
      INSERT INTO ${tableName} (${columns.join(', ')})
      VALUES (${placeholders.join(', ')})
      RETURNING ${returning.join(', ')}
    `.trim();

    return { sql, params: values };
  }

  /**
   * Build UPDATE query
   */
  static buildUpdateQuery(
    tableName: string,
    data: Record<string, any>,
    where: Record<string, any>,
    returning: string[] = ['*']
  ): { sql: string; params: any[] } {
    const setColumns = Object.keys(data);
    const setValues = Object.values(data);
    const setClause = setColumns.map((col, index) => `${col} = $${index + 1}`);

    const { clause: whereClause, params: whereParams } = this.buildWhereClause(
      where,
      setValues.length + 1
    );

    const sql = `
      UPDATE ${tableName}
      SET ${setClause.join(', ')}
      ${whereClause ? `WHERE ${whereClause}` : ''}
      RETURNING ${returning.join(', ')}
    `.trim();

    return { sql, params: [...setValues, ...whereParams] };
  }

  /**
   * Build DELETE query
   */
  static buildDeleteQuery(
    tableName: string,
    where: Record<string, any>
  ): { sql: string; params: any[] } {
    const { clause: whereClause, params } = this.buildWhereClause(where);

    if (!whereClause) {
      throw new Error('DELETE query requires WHERE clause for safety');
    }

    const sql = `DELETE FROM ${tableName} WHERE ${whereClause}`;

    return { sql, params };
  }

  /**
   * Build SELECT query
   */
  static buildSelectQuery(
    tableName: string,
    options: {
      select?: string[];
      where?: Record<string, any>;
      orderBy?: Record<string, 'ASC' | 'DESC'>;
      limit?: number;
      offset?: number;
      joins?: Array<{
        type: 'INNER' | 'LEFT' | 'RIGHT' | 'FULL';
        table: string;
        on: string;
      }>;
    } = {}
  ): { sql: string; params: any[] } {
    let sql = 'SELECT ';
    
    // Select clause
    if (options.select && options.select.length > 0) {
      sql += options.select.join(', ');
    } else {
      sql += '*';
    }
    
    sql += ` FROM ${tableName}`;
    
    // Joins
    if (options.joins) {
      for (const join of options.joins) {
        sql += ` ${join.type} JOIN ${join.table} ON ${join.on}`;
      }
    }
    
    // Where clause
    let params: any[] = [];
    if (options.where) {
      const { clause: whereClause, params: whereParams } = this.buildWhereClause(options.where);
      if (whereClause) {
        sql += ` WHERE ${whereClause}`;
        params = whereParams;
      }
    }
    
    // Order by clause
    if (options.orderBy) {
      const orderByClause = this.buildOrderByClause(options.orderBy);
      if (orderByClause) {
        sql += ` ORDER BY ${orderByClause}`;
      }
    }
    
    // Limit and offset
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`;
    }
    
    if (options.offset) {
      sql += ` OFFSET ${options.offset}`;
    }

    return { sql, params };
  }

  /**
   * Convert camelCase to snake_case
   */
  static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Convert snake_case to camelCase
   */
  static snakeToCamel(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Convert object keys from camelCase to snake_case
   */
  static objectKeysToSnake(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      result[this.camelToSnake(key)] = value;
    }
    
    return result;
  }

  /**
   * Convert object keys from snake_case to camelCase
   */
  static objectKeysToCamel(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      result[this.snakeToCamel(key)] = value;
    }
    
    return result;
  }

  /**
   * Generate UUID v4
   */
  static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Validate UUID format
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Sanitize table/column names
   */
  static sanitizeIdentifier(identifier: string): string {
    // Remove any characters that aren't alphanumeric or underscore
    return identifier.replace(/[^a-zA-Z0-9_]/g, '');
  }

  /**
   * Parse connection string
   */
  static parseConnectionString(connectionString: string): {
    type: string;
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl: boolean;
  } {
    const url = new URL(connectionString);
    
    return {
      type: url.protocol.slice(0, -1), // Remove trailing ':'
      host: url.hostname,
      port: parseInt(url.port) || this.getDefaultPort(url.protocol.slice(0, -1)),
      database: url.pathname.slice(1), // Remove leading '/'
      username: url.username,
      password: url.password,
      ssl: url.searchParams.get('ssl') === 'true'
    };
  }

  /**
   * Get default port for database type
   */
  static getDefaultPort(type: string): number {
    switch (type) {
      case 'postgres':
      case 'postgresql':
        return 5432;
      case 'mysql':
        return 3306;
      case 'mongodb':
        return 27017;
      case 'redis':
        return 6379;
      default:
        return 5432;
    }
  }

  /**
   * Format bytes to human readable string
   */
  static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Format duration in milliseconds to human readable string
   */
  static formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(2)}m`;
    return `${(ms / 3600000).toFixed(2)}h`;
  }

  /**
   * Deep clone object
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepClone(item)) as unknown as T;
    }

    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key]);
      }
    }

    return cloned;
  }

  /**
   * Check if value is empty (null, undefined, empty string, empty array, empty object)
   */
  static isEmpty(value: any): boolean {
    if (value === null || value === undefined) {
      return true;
    }
    
    if (typeof value === 'string') {
      return value.trim() === '';
    }
    
    if (Array.isArray(value)) {
      return value.length === 0;
    }
    
    if (typeof value === 'object') {
      return Object.keys(value).length === 0;
    }
    
    return false;
  }

  /**
   * Retry function with exponential backoff
   */
  static async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxAttempts) {
          throw lastError;
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }
}
