/**
 * Database migration service
 */

import { promises as fs } from 'fs';
import path from 'path';
import { DatabaseService } from './database-service';

export interface Migration {
  id: string;
  name: string;
  timestamp: number;
  up: (db: DatabaseService) => Promise<void>;
  down: (db: DatabaseService) => Promise<void>;
}

export interface MigrationRecord {
  id: string;
  name: string;
  executed_at: Date;
  batch: number;
}

export class MigrationService {
  private database: DatabaseService;
  private migrationsDirectory: string;
  private migrationsTable: string;

  constructor(
    database: DatabaseService,
    migrationsDirectory: string = './migrations',
    migrationsTable: string = 'migrations'
  ) {
    this.database = database;
    this.migrationsDirectory = migrationsDirectory;
    this.migrationsTable = migrationsTable;
  }

  /**
   * Initialize migrations table
   */
  async initializeMigrationsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS ${this.migrationsTable} (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        batch INTEGER NOT NULL
      )
    `;
    
    await this.database.query(sql);
  }

  /**
   * Get all migration files
   */
  async getMigrationFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.migrationsDirectory);
      return files
        .filter(file => file.endsWith('.js') || file.endsWith('.ts'))
        .sort();
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        return [];
      }
      throw error;
    }
  }

  /**
   * Load migration from file
   */
  async loadMigration(filename: string): Promise<Migration> {
    const filePath = path.join(this.migrationsDirectory, filename);
    
    try {
      // Dynamic import to support both JS and TS files
      const migrationModule = await import(filePath);
      const migration = migrationModule.default || migrationModule;
      
      if (!migration.up || !migration.down) {
        throw new Error(`Migration ${filename} must export 'up' and 'down' functions`);
      }
      
      // Extract timestamp and name from filename
      const match = filename.match(/^(\d+)_(.+)\.(js|ts)$/);
      if (!match) {
        throw new Error(`Invalid migration filename format: ${filename}`);
      }
      
      const [, timestamp, name] = match;
      
      return {
        id: `${timestamp}_${name}`,
        name,
        timestamp: parseInt(timestamp),
        up: migration.up,
        down: migration.down
      };
    } catch (error) {
      throw new Error(`Failed to load migration ${filename}: ${error.message}`);
    }
  }

  /**
   * Get executed migrations from database
   */
  async getExecutedMigrations(): Promise<MigrationRecord[]> {
    await this.initializeMigrationsTable();
    
    const result = await this.database.query(`
      SELECT * FROM ${this.migrationsTable}
      ORDER BY executed_at ASC
    `);
    
    return result.rows;
  }

  /**
   * Get pending migrations
   */
  async getPendingMigrations(): Promise<Migration[]> {
    const migrationFiles = await this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();
    const executedIds = new Set(executedMigrations.map(m => m.id));
    
    const pendingMigrations: Migration[] = [];
    
    for (const filename of migrationFiles) {
      const migration = await this.loadMigration(filename);
      if (!executedIds.has(migration.id)) {
        pendingMigrations.push(migration);
      }
    }
    
    return pendingMigrations.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Run a single migration
   */
  async runMigration(migration: Migration, batch: number): Promise<void> {
    await this.database.transaction(async (trx) => {
      // Execute migration
      await migration.up(this.database);
      
      // Record migration execution
      await this.database.query(`
        INSERT INTO ${this.migrationsTable} (id, name, executed_at, batch)
        VALUES ($1, $2, CURRENT_TIMESTAMP, $3)
      `, [migration.id, migration.name, batch]);
    });
  }

  /**
   * Run all pending migrations
   */
  async runPendingMigrations(): Promise<void> {
    const pendingMigrations = await this.getPendingMigrations();
    
    if (pendingMigrations.length === 0) {
      console.log('No pending migrations');
      return;
    }
    
    // Get next batch number
    const lastBatchResult = await this.database.query(`
      SELECT COALESCE(MAX(batch), 0) as last_batch FROM ${this.migrationsTable}
    `);
    const nextBatch = lastBatchResult.rows[0].last_batch + 1;
    
    console.log(`Running ${pendingMigrations.length} pending migrations...`);
    
    for (const migration of pendingMigrations) {
      try {
        console.log(`Running migration: ${migration.id}`);
        await this.runMigration(migration, nextBatch);
        console.log(`✓ Migration ${migration.id} completed`);
      } catch (error) {
        console.error(`✗ Migration ${migration.id} failed:`, error);
        throw error;
      }
    }
    
    console.log('All migrations completed successfully');
  }

  /**
   * Rollback last batch of migrations
   */
  async rollbackLastBatch(): Promise<void> {
    // Get last batch
    const lastBatchResult = await this.database.query(`
      SELECT MAX(batch) as last_batch FROM ${this.migrationsTable}
    `);
    
    const lastBatch = lastBatchResult.rows[0].last_batch;
    if (!lastBatch) {
      console.log('No migrations to rollback');
      return;
    }
    
    // Get migrations from last batch
    const migrationsToRollback = await this.database.query(`
      SELECT * FROM ${this.migrationsTable}
      WHERE batch = $1
      ORDER BY executed_at DESC
    `, [lastBatch]);
    
    console.log(`Rolling back ${migrationsToRollback.rows.length} migrations from batch ${lastBatch}...`);
    
    for (const migrationRecord of migrationsToRollback.rows) {
      try {
        // Load migration file
        const migrationFiles = await this.getMigrationFiles();
        const filename = migrationFiles.find(f => f.includes(migrationRecord.id));
        
        if (!filename) {
          throw new Error(`Migration file not found for ${migrationRecord.id}`);
        }
        
        const migration = await this.loadMigration(filename);
        
        console.log(`Rolling back migration: ${migration.id}`);
        
        await this.database.transaction(async (trx) => {
          // Execute rollback
          await migration.down(this.database);
          
          // Remove migration record
          await this.database.query(`
            DELETE FROM ${this.migrationsTable} WHERE id = $1
          `, [migration.id]);
        });
        
        console.log(`✓ Migration ${migration.id} rolled back`);
      } catch (error) {
        console.error(`✗ Rollback failed for ${migrationRecord.id}:`, error);
        throw error;
      }
    }
    
    console.log('Rollback completed successfully');
  }

  /**
   * Rollback to specific migration
   */
  async rollbackTo(targetMigrationId: string): Promise<void> {
    const executedMigrations = await this.getExecutedMigrations();
    const targetIndex = executedMigrations.findIndex(m => m.id === targetMigrationId);
    
    if (targetIndex === -1) {
      throw new Error(`Migration ${targetMigrationId} not found in executed migrations`);
    }
    
    // Get migrations to rollback (all after target)
    const migrationsToRollback = executedMigrations
      .slice(targetIndex + 1)
      .reverse();
    
    if (migrationsToRollback.length === 0) {
      console.log('No migrations to rollback');
      return;
    }
    
    console.log(`Rolling back ${migrationsToRollback.length} migrations to ${targetMigrationId}...`);
    
    for (const migrationRecord of migrationsToRollback) {
      try {
        // Load migration file
        const migrationFiles = await this.getMigrationFiles();
        const filename = migrationFiles.find(f => f.includes(migrationRecord.id));
        
        if (!filename) {
          throw new Error(`Migration file not found for ${migrationRecord.id}`);
        }
        
        const migration = await this.loadMigration(filename);
        
        console.log(`Rolling back migration: ${migration.id}`);
        
        await this.database.transaction(async (trx) => {
          // Execute rollback
          await migration.down(this.database);
          
          // Remove migration record
          await this.database.query(`
            DELETE FROM ${this.migrationsTable} WHERE id = $1
          `, [migration.id]);
        });
        
        console.log(`✓ Migration ${migration.id} rolled back`);
      } catch (error) {
        console.error(`✗ Rollback failed for ${migrationRecord.id}:`, error);
        throw error;
      }
    }
    
    console.log('Rollback completed successfully');
  }

  /**
   * Get migration status
   */
  async getStatus(): Promise<{
    executed: MigrationRecord[];
    pending: Migration[];
    total: number;
  }> {
    const executed = await this.getExecutedMigrations();
    const pending = await this.getPendingMigrations();
    
    return {
      executed,
      pending,
      total: executed.length + pending.length
    };
  }

  /**
   * Reset all migrations (dangerous!)
   */
  async reset(): Promise<void> {
    console.log('WARNING: This will rollback ALL migrations!');
    
    const executedMigrations = await this.getExecutedMigrations();
    const migrationsToRollback = [...executedMigrations].reverse();
    
    for (const migrationRecord of migrationsToRollback) {
      try {
        // Load migration file
        const migrationFiles = await this.getMigrationFiles();
        const filename = migrationFiles.find(f => f.includes(migrationRecord.id));
        
        if (!filename) {
          console.warn(`Migration file not found for ${migrationRecord.id}, skipping...`);
          continue;
        }
        
        const migration = await this.loadMigration(filename);
        
        console.log(`Rolling back migration: ${migration.id}`);
        
        await this.database.transaction(async (trx) => {
          // Execute rollback
          await migration.down(this.database);
          
          // Remove migration record
          await this.database.query(`
            DELETE FROM ${this.migrationsTable} WHERE id = $1
          `, [migration.id]);
        });
        
        console.log(`✓ Migration ${migration.id} rolled back`);
      } catch (error) {
        console.error(`✗ Rollback failed for ${migrationRecord.id}:`, error);
        throw error;
      }
    }
    
    console.log('All migrations have been reset');
  }
}
