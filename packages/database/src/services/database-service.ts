/**
 * Core database service for the unified AI assistant platform
 */

import { EventEmitter } from 'events';

export interface DatabaseConfig {
  type: 'postgres' | 'mysql' | 'sqlite' | 'mongodb';
  host?: string;
  port?: number;
  database: string;
  username?: string;
  password?: string;
  ssl?: boolean;
  connectionTimeout?: number;
  maxConnections?: number;
  minConnections?: number;
  idleTimeout?: number;
  acquireTimeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  logging?: boolean;
  migrations?: {
    directory: string;
    tableName?: string;
    autoRun?: boolean;
  };
}

export interface DatabaseProvider {
  name: string;
  connect(config: DatabaseConfig): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
  query(sql: string, params?: any[]): Promise<any>;
  transaction<T>(callback: (trx: any) => Promise<T>): Promise<T>;
  getConnection(): any;
  healthCheck(): Promise<boolean>;
}

export class DatabaseService extends EventEmitter {
  private provider: DatabaseProvider | null = null;
  private config: DatabaseConfig;
  private isInitialized = false;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor(config: DatabaseConfig) {
    super();
    this.config = config;
  }

  /**
   * Initialize database connection
   */
  async initialize(provider: DatabaseProvider): Promise<void> {
    try {
      this.provider = provider;
      
      await this.provider.connect(this.config);
      
      // Run migrations if configured
      if (this.config.migrations?.autoRun) {
        await this.runMigrations();
      }

      // Start health monitoring
      this.startHealthMonitoring();

      this.isInitialized = true;
      this.emit('connected', { provider: this.provider.name });
    } catch (error) {
      this.emit('error', error);
      throw new Error(`Database initialization failed: ${error.message}`);
    }
  }

  /**
   * Execute a query
   */
  async query(sql: string, params?: any[]): Promise<any> {
    this.ensureInitialized();
    
    try {
      const startTime = Date.now();
      const result = await this.provider!.query(sql, params);
      const duration = Date.now() - startTime;
      
      this.emit('query', { sql, params, duration, rowCount: result?.rowCount });
      
      return result;
    } catch (error) {
      this.emit('queryError', { sql, params, error });
      throw error;
    }
  }

  /**
   * Execute a transaction
   */
  async transaction<T>(callback: (trx: any) => Promise<T>): Promise<T> {
    this.ensureInitialized();
    
    try {
      const startTime = Date.now();
      const result = await this.provider!.transaction(callback);
      const duration = Date.now() - startTime;
      
      this.emit('transaction', { duration, success: true });
      
      return result;
    } catch (error) {
      this.emit('transaction', { duration: Date.now(), success: false, error });
      throw error;
    }
  }

  /**
   * Get raw database connection
   */
  getConnection(): any {
    this.ensureInitialized();
    return this.provider!.getConnection();
  }

  /**
   * Check if database is connected
   */
  isConnected(): boolean {
    return this.isInitialized && this.provider?.isConnected() || false;
  }

  /**
   * Perform health check
   */
  async healthCheck(): Promise<boolean> {
    if (!this.isInitialized || !this.provider) {
      return false;
    }

    try {
      return await this.provider.healthCheck();
    } catch (error) {
      this.emit('healthCheckFailed', error);
      return false;
    }
  }

  /**
   * Disconnect from database
   */
  async disconnect(): Promise<void> {
    try {
      this.stopHealthMonitoring();
      
      if (this.provider) {
        await this.provider.disconnect();
      }
      
      this.isInitialized = false;
      this.provider = null;
      
      this.emit('disconnected');
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Run database migrations
   */
  private async runMigrations(): Promise<void> {
    if (!this.config.migrations) {
      return;
    }

    try {
      // Import migration service dynamically to avoid circular dependencies
      const { MigrationService } = await import('./migration-service');
      const migrationService = new MigrationService(this);
      
      await migrationService.runPendingMigrations();
      this.emit('migrationsCompleted');
    } catch (error) {
      this.emit('migrationError', error);
      throw new Error(`Migration failed: ${error.message}`);
    }
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Check health every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      const isHealthy = await this.healthCheck();
      if (!isHealthy) {
        this.emit('unhealthy');
      }
    }, 30000);
  }

  /**
   * Stop health monitoring
   */
  private stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }
  }

  /**
   * Ensure database is initialized
   */
  private ensureInitialized(): void {
    if (!this.isInitialized || !this.provider) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
  }

  /**
   * Get database statistics
   */
  async getStatistics(): Promise<{
    connectionCount: number;
    activeQueries: number;
    totalQueries: number;
    averageQueryTime: number;
  }> {
    // This would be implemented based on the specific database provider
    return {
      connectionCount: 0,
      activeQueries: 0,
      totalQueries: 0,
      averageQueryTime: 0
    };
  }

  /**
   * Create database backup
   */
  async createBackup(options: {
    path: string;
    compress?: boolean;
    includeData?: boolean;
  }): Promise<void> {
    this.ensureInitialized();
    
    // Implementation would depend on database type
    throw new Error('Backup functionality not implemented for this database type');
  }

  /**
   * Restore database from backup
   */
  async restoreBackup(backupPath: string): Promise<void> {
    this.ensureInitialized();
    
    // Implementation would depend on database type
    throw new Error('Restore functionality not implemented for this database type');
  }

  /**
   * Optimize database performance
   */
  async optimize(): Promise<void> {
    this.ensureInitialized();
    
    try {
      // Run database-specific optimization commands
      switch (this.config.type) {
        case 'postgres':
          await this.query('VACUUM ANALYZE');
          break;
        case 'mysql':
          await this.query('OPTIMIZE TABLE');
          break;
        case 'sqlite':
          await this.query('VACUUM');
          break;
        default:
          // No optimization for other types
          break;
      }
      
      this.emit('optimized');
    } catch (error) {
      this.emit('optimizationError', error);
      throw error;
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): DatabaseConfig {
    return { ...this.config };
  }

  /**
   * Update configuration (requires reconnection)
   */
  async updateConfig(newConfig: Partial<DatabaseConfig>): Promise<void> {
    const wasConnected = this.isConnected();
    
    if (wasConnected) {
      await this.disconnect();
    }
    
    this.config = { ...this.config, ...newConfig };
    
    if (wasConnected && this.provider) {
      await this.initialize(this.provider);
    }
  }
}
