/**
 * Base model class for ORM
 */

import { DatabaseService } from '../services/database-service';

export interface ModelConfig {
  tableName: string;
  primaryKey?: string;
  timestamps?: boolean;
  softDeletes?: boolean;
  schema?: string;
}

export interface QueryOptions {
  select?: string[];
  where?: Record<string, any>;
  orderBy?: Record<string, 'ASC' | 'DESC'>;
  limit?: number;
  offset?: number;
  include?: string[];
}

export abstract class BaseModel {
  protected static database: DatabaseService;
  protected static config: ModelConfig;
  
  // Instance properties
  public id?: any;
  public createdAt?: Date;
  public updatedAt?: Date;
  public deletedAt?: Date;

  constructor(attributes: Record<string, any> = {}) {
    this.fill(attributes);
  }

  /**
   * Set database service
   */
  static setDatabase(database: DatabaseService): void {
    this.database = database;
  }

  /**
   * Get model configuration
   */
  static getConfig(): ModelConfig {
    return this.config;
  }

  /**
   * Get table name
   */
  static getTableName(): string {
    return this.config.tableName;
  }

  /**
   * Get primary key column name
   */
  static getPrimaryKey(): string {
    return this.config.primaryKey || 'id';
  }

  /**
   * Find record by ID
   */
  static async find<T extends BaseModel>(this: new() => T, id: any): Promise<T | null> {
    const ModelClass = this as any;
    const config = ModelClass.getConfig();
    const primaryKey = ModelClass.getPrimaryKey();
    
    const sql = `SELECT * FROM ${config.tableName} WHERE ${primaryKey} = $1`;
    const result = await ModelClass.database.query(sql, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new ModelClass(result.rows[0]);
  }

  /**
   * Find all records
   */
  static async findAll<T extends BaseModel>(
    this: new() => T, 
    options: QueryOptions = {}
  ): Promise<T[]> {
    const ModelClass = this as any;
    const config = ModelClass.getConfig();
    
    let sql = `SELECT `;
    
    // Select clause
    if (options.select && options.select.length > 0) {
      sql += options.select.join(', ');
    } else {
      sql += '*';
    }
    
    sql += ` FROM ${config.tableName}`;
    
    // Where clause
    const params: any[] = [];
    if (options.where) {
      const whereConditions = Object.keys(options.where).map((key, index) => {
        params.push(options.where![key]);
        return `${key} = $${index + 1}`;
      });
      
      if (whereConditions.length > 0) {
        sql += ` WHERE ${whereConditions.join(' AND ')}`;
      }
    }
    
    // Soft deletes
    if (config.softDeletes) {
      const whereClause = options.where ? ' AND ' : ' WHERE ';
      sql += `${whereClause}deleted_at IS NULL`;
    }
    
    // Order by clause
    if (options.orderBy) {
      const orderConditions = Object.keys(options.orderBy).map(key => 
        `${key} ${options.orderBy![key]}`
      );
      sql += ` ORDER BY ${orderConditions.join(', ')}`;
    }
    
    // Limit and offset
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`;
    }
    
    if (options.offset) {
      sql += ` OFFSET ${options.offset}`;
    }
    
    const result = await ModelClass.database.query(sql, params);
    
    return result.rows.map((row: any) => new ModelClass(row));
  }

  /**
   * Find first record matching conditions
   */
  static async findFirst<T extends BaseModel>(
    this: new() => T,
    options: QueryOptions = {}
  ): Promise<T | null> {
    const results = await this.findAll({ ...options, limit: 1 });
    return results.length > 0 ? results[0] : null;
  }

  /**
   * Create new record
   */
  static async create<T extends BaseModel>(
    this: new() => T,
    attributes: Record<string, any>
  ): Promise<T> {
    const ModelClass = this as any;
    const config = ModelClass.getConfig();
    
    // Add timestamps if enabled
    if (config.timestamps) {
      attributes.createdAt = new Date();
      attributes.updatedAt = new Date();
    }
    
    const columns = Object.keys(attributes);
    const values = Object.values(attributes);
    const placeholders = values.map((_, index) => `$${index + 1}`);
    
    const sql = `
      INSERT INTO ${config.tableName} (${columns.join(', ')})
      VALUES (${placeholders.join(', ')})
      RETURNING *
    `;
    
    const result = await ModelClass.database.query(sql, values);
    
    return new ModelClass(result.rows[0]);
  }

  /**
   * Update record
   */
  async update(attributes: Record<string, any>): Promise<this> {
    const ModelClass = this.constructor as any;
    const config = ModelClass.getConfig();
    const primaryKey = ModelClass.getPrimaryKey();
    
    if (!this[primaryKey as keyof this]) {
      throw new Error('Cannot update record without primary key');
    }
    
    // Add updated timestamp if enabled
    if (config.timestamps) {
      attributes.updatedAt = new Date();
    }
    
    const columns = Object.keys(attributes);
    const values = Object.values(attributes);
    const setClause = columns.map((col, index) => `${col} = $${index + 1}`);
    
    const sql = `
      UPDATE ${config.tableName}
      SET ${setClause.join(', ')}
      WHERE ${primaryKey} = $${values.length + 1}
      RETURNING *
    `;
    
    const result = await ModelClass.database.query(sql, [...values, this[primaryKey as keyof this]]);
    
    this.fill(result.rows[0]);
    return this;
  }

  /**
   * Save record (create or update)
   */
  async save(): Promise<this> {
    const ModelClass = this.constructor as any;
    const primaryKey = ModelClass.getPrimaryKey();
    
    if (this[primaryKey as keyof this]) {
      // Update existing record
      const attributes = this.getAttributes();
      delete attributes[primaryKey];
      return await this.update(attributes);
    } else {
      // Create new record
      const attributes = this.getAttributes();
      const newRecord = await ModelClass.create(attributes);
      this.fill(newRecord.getAttributes());
      return this;
    }
  }

  /**
   * Delete record
   */
  async delete(): Promise<boolean> {
    const ModelClass = this.constructor as any;
    const config = ModelClass.getConfig();
    const primaryKey = ModelClass.getPrimaryKey();
    
    if (!this[primaryKey as keyof this]) {
      throw new Error('Cannot delete record without primary key');
    }
    
    if (config.softDeletes) {
      // Soft delete
      await this.update({ deletedAt: new Date() });
      return true;
    } else {
      // Hard delete
      const sql = `DELETE FROM ${config.tableName} WHERE ${primaryKey} = $1`;
      const result = await ModelClass.database.query(sql, [this[primaryKey as keyof this]]);
      return result.rowCount > 0;
    }
  }

  /**
   * Restore soft deleted record
   */
  async restore(): Promise<this> {
    const ModelClass = this.constructor as any;
    const config = ModelClass.getConfig();
    
    if (!config.softDeletes) {
      throw new Error('Restore is only available for models with soft deletes enabled');
    }
    
    return await this.update({ deletedAt: null });
  }

  /**
   * Fill model with attributes
   */
  fill(attributes: Record<string, any>): this {
    for (const [key, value] of Object.entries(attributes)) {
      (this as any)[key] = value;
    }
    return this;
  }

  /**
   * Get all attributes as object
   */
  getAttributes(): Record<string, any> {
    const attributes: Record<string, any> = {};
    
    for (const key in this) {
      if (this.hasOwnProperty(key) && typeof this[key] !== 'function') {
        attributes[key] = this[key];
      }
    }
    
    return attributes;
  }

  /**
   * Convert to JSON
   */
  toJSON(): Record<string, any> {
    return this.getAttributes();
  }

  /**
   * Convert to string
   */
  toString(): string {
    return JSON.stringify(this.toJSON());
  }

  /**
   * Count records
   */
  static async count(options: Pick<QueryOptions, 'where'> = {}): Promise<number> {
    const ModelClass = this as any;
    const config = ModelClass.getConfig();
    
    let sql = `SELECT COUNT(*) as count FROM ${config.tableName}`;
    const params: any[] = [];
    
    // Where clause
    if (options.where) {
      const whereConditions = Object.keys(options.where).map((key, index) => {
        params.push(options.where![key]);
        return `${key} = $${index + 1}`;
      });
      
      if (whereConditions.length > 0) {
        sql += ` WHERE ${whereConditions.join(' AND ')}`;
      }
    }
    
    // Soft deletes
    if (config.softDeletes) {
      const whereClause = options.where ? ' AND ' : ' WHERE ';
      sql += `${whereClause}deleted_at IS NULL`;
    }
    
    const result = await ModelClass.database.query(sql, params);
    return parseInt(result.rows[0].count);
  }

  /**
   * Check if record exists
   */
  static async exists(options: Pick<QueryOptions, 'where'>): Promise<boolean> {
    const count = await this.count(options);
    return count > 0;
  }
}
