/**
 * Database layer with ORM, migrations, and connection management for the unified AI assistant platform
 */

// Core database services
export * from './services/database-service';
export * from './services/migration-service';
export * from './services/connection-manager';
export * from './services/query-builder';

// ORM components
export * from './orm/base-model';
export * from './orm/repository';
export * from './orm/entity-manager';
export * from './orm/schema-builder';

// Database providers
export * from './providers/postgres-provider';
export * from './providers/mysql-provider';
export * from './providers/sqlite-provider';
export * from './providers/mongodb-provider';

// Migration system
export * from './migrations/migration-runner';
export * from './migrations/migration-generator';

// Utilities
export * from './utils/database-utils';
export * from './utils/query-utils';
