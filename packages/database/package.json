{"name": "@unified-assistant/database", "version": "0.1.0", "description": "Database layer and ORM for the unified AI assistant platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"@unified-assistant/types": "workspace:*", "prisma": "^6.1.0", "@prisma/client": "^6.1.0", "drizzle-orm": "^0.36.4", "drizzle-kit": "^0.30.1"}, "devDependencies": {"@types/node": "^22.10.2", "typescript": "^5.7.2", "jest": "^29.7.0"}, "files": ["dist", "README.md"]}