import { ChatAnthropic } from '@langchain/anthropic';
import { 
  AIRequest, 
  AIResponse, 
  AIProviderConfig 
} from '@unified-assistant/types';
import { BaseAIProvider } from './base-provider';

/**
 * Anthropic Provider Implementation
 * 
 * LEVER approach implementation:
 * L - Leverage existing LangChain Anthropic integration
 * E - Extend with unified interface and Claude-specific optimizations
 * V - Verify through comprehensive request validation and error handling
 * E - Eliminate duplicate Anthropic configurations across modules
 * R - Reduce complexity through standardized response format
 */
export class AnthropicProvider extends BaseAIProvider {
  private chatModel?: ChatAnthropic;

  constructor(config: AIProviderConfig) {
    super(config);
  }

  /**
   * Initialize Anthropic models
   */
  protected async initializeProvider(): Promise<void> {
    const apiKey = this.config.config.apiKey;
    if (!apiKey) {
      throw new Error('Anthropic API key is required');
    }

    const baseConfig = {
      anthropicApiKey: apiKey,
      modelName: this.config.config.model || 'claude-3-sonnet-20240229',
      temperature: this.config.config.temperature || 0.7,
      maxTokens: this.config.config.maxTokens || 4000,
      timeout: this.config.config.timeout || 30000,
    };

    // Initialize chat model
    if (this.supports('chat')) {
      this.chatModel = new ChatAnthropic({
        ...baseConfig,
        streaming: this.supports('streaming'),
      });
    }
  }

  /**
   * Perform health check
   */
  protected async performHealthCheck(): Promise<boolean> {
    try {
      if (this.chatModel) {
        await this.chatModel.invoke([{ role: 'user', content: 'test' }]);
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Process provider-specific request
   */
  protected async processProviderRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      let content: string;
      let usage: AIResponse['usage'];

      switch (request.type) {
        case 'chat':
          const result = await this.processChatRequest(request);
          content = result.content;
          usage = result.usage;
          break;

        case 'completion':
          // Anthropic doesn't have separate completion endpoint, use chat
          const completionResult = await this.processCompletionAsChatRequest(request);
          content = completionResult.content;
          usage = completionResult.usage;
          break;

        default:
          throw new Error(`Unsupported request type: ${request.type}`);
      }

      const response = this.createResponse(
        request,
        content,
        'stop',
        usage,
        { model: this.config.config.model }
      );

      response.latency = Date.now() - startTime;
      return response;

    } catch (error) {
      throw this.handleProviderError(error);
    }
  }

  /**
   * Process chat request
   */
  private async processChatRequest(request: AIRequest): Promise<{
    content: string;
    usage: AIResponse['usage'];
  }> {
    if (!this.chatModel) {
      throw new Error('Chat model not initialized');
    }

    const messages = request.messages!.map(msg => ({
      role: msg.role === 'assistant' ? 'assistant' : 'user', // Anthropic uses user/assistant
      content: msg.content
    }));

    const result = await this.chatModel.invoke(messages);
    
    // Estimate usage (Anthropic provides usage info in response metadata)
    const promptTokens = this.estimateTokens({ ...request, prompt: messages.map(m => m.content).join(' ') });
    const completionTokens = Math.ceil(result.content.length / 4);
    const totalTokens = promptTokens + completionTokens;

    return {
      content: result.content,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, this.config.config.model || 'claude-3-sonnet-20240229')
      }
    };
  }

  /**
   * Process completion request as chat (Anthropic doesn't have separate completion)
   */
  private async processCompletionAsChatRequest(request: AIRequest): Promise<{
    content: string;
    usage: AIResponse['usage'];
  }> {
    if (!this.chatModel) {
      throw new Error('Chat model not initialized');
    }

    const messages = [{ role: 'user', content: request.prompt! }];
    const result = await this.chatModel.invoke(messages);
    
    const promptTokens = this.estimateTokens(request);
    const completionTokens = Math.ceil(result.content.length / 4);
    const totalTokens = promptTokens + completionTokens;

    return {
      content: result.content,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, this.config.config.model || 'claude-3-sonnet-20240229')
      }
    };
  }

  /**
   * Process stream request
   */
  protected async *processStreamRequest(request: AIRequest): AsyncIterable<Partial<AIResponse>> {
    if (!this.chatModel || request.type !== 'chat') {
      throw new Error('Streaming only supported for chat requests');
    }

    const messages = request.messages!.map(msg => ({
      role: msg.role === 'assistant' ? 'assistant' : 'user',
      content: msg.content
    }));

    const stream = await this.chatModel.stream(messages);
    
    let fullContent = '';
    for await (const chunk of stream) {
      fullContent += chunk.content;
      
      yield {
        id: `${this.id}-stream-${Date.now()}`,
        requestId: request.id,
        provider: this.id,
        content: chunk.content,
        finishReason: chunk.response_metadata?.finish_reason as any || undefined,
        timestamp: new Date()
      };
    }

    // Final chunk with usage information
    const promptTokens = this.estimateTokens({ ...request, prompt: messages.map(m => m.content).join(' ') });
    const completionTokens = Math.ceil(fullContent.length / 4);
    const totalTokens = promptTokens + completionTokens;

    yield {
      id: `${this.id}-stream-final`,
      requestId: request.id,
      provider: this.id,
      content: '',
      finishReason: 'stop',
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, this.config.config.model || 'claude-3-sonnet-20240229')
      },
      timestamp: new Date()
    };
  }

  /**
   * Get available models
   */
  async getModels(): Promise<string[]> {
    return [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307',
      'claude-2.1',
      'claude-2.0',
      'claude-instant-1.2'
    ];
  }

  /**
   * Calculate cost based on model and tokens
   */
  private calculateCost(tokens: number, model: string): number {
    const pricing: Record<string, { input: number; output: number }> = {
      'claude-3-opus-20240229': { input: 0.015, output: 0.075 },
      'claude-3-sonnet-20240229': { input: 0.003, output: 0.015 },
      'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
      'claude-2.1': { input: 0.008, output: 0.024 },
      'claude-2.0': { input: 0.008, output: 0.024 },
      'claude-instant-1.2': { input: 0.0008, output: 0.0024 }
    };

    const modelPricing = pricing[model] || pricing['claude-3-sonnet-20240229'];
    return (tokens / 1000) * modelPricing.input; // Simplified cost calculation
  }

  /**
   * Handle Anthropic-specific errors
   */
  protected handleProviderError(error: any): Error {
    // Anthropic-specific error handling
    if (error.message?.includes('rate_limit')) {
      return new Error('Anthropic rate limit exceeded');
    }
    
    if (error.message?.includes('invalid_api_key')) {
      return new Error('Invalid Anthropic API key');
    }
    
    if (error.message?.includes('overloaded')) {
      return new Error('Anthropic service overloaded');
    }
    
    return super.handleProviderError(error);
  }
}
