import { 
  AIProvider, 
  AIProviderConfig, 
  AIRequest, 
  AIResponse, 
  AIProviderMetrics 
} from '@unified-assistant/types';

/**
 * Base AI Provider Implementation
 * 
 * Provides common functionality for all AI providers following LEVER approach:
 * L - Leverage common patterns across providers
 * E - Extend with provider-specific capabilities
 * V - Verify through comprehensive error handling
 * E - Eliminate duplicate initialization logic
 * R - Reduce complexity through shared base class
 */
export abstract class BaseAIProvider implements AIProvider {
  public readonly id: string;
  public readonly name: string;
  public readonly config: AIProviderConfig;
  
  protected initialized: boolean = false;
  protected metrics: AIProviderMetrics;
  protected startTime: Date;

  constructor(config: AIProviderConfig) {
    this.id = config.id;
    this.name = config.name;
    this.config = config;
    this.startTime = new Date();
    
    this.metrics = {
      requests: 0,
      errors: 0,
      averageLatency: 0,
      tokensUsed: 0,
      cost: 0,
      uptime: 0
    };
  }

  /**
   * Initialize the provider
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      await this.initializeProvider();
      this.initialized = true;
      this.startMetricsTracking();
    } catch (error) {
      throw new Error(`Failed to initialize ${this.name} provider: ${error}`);
    }
  }

  /**
   * Provider-specific initialization
   */
  protected abstract initializeProvider(): Promise<void>;

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    if (!this.initialized) {
      return false;
    }

    try {
      return await this.performHealthCheck();
    } catch (error) {
      return false;
    }
  }

  /**
   * Provider-specific health check
   */
  protected abstract performHealthCheck(): Promise<boolean>;

  /**
   * Process AI request
   */
  async processRequest(request: AIRequest): Promise<AIResponse> {
    if (!this.initialized) {
      throw new Error(`Provider ${this.name} not initialized`);
    }

    const startTime = Date.now();
    this.metrics.requests++;

    try {
      // Validate request
      this.validateRequest(request);

      // Process request with provider-specific logic
      const response = await this.processProviderRequest(request);

      // Update metrics
      const latency = Date.now() - startTime;
      this.updateMetrics(response, latency);

      return response;

    } catch (error) {
      this.metrics.errors++;
      throw new Error(`${this.name} request failed: ${error}`);
    }
  }

  /**
   * Stream AI request
   */
  async *streamRequest(request: AIRequest): AsyncIterable<Partial<AIResponse>> {
    if (!this.initialized) {
      throw new Error(`Provider ${this.name} not initialized`);
    }

    if (!this.supports('streaming')) {
      throw new Error(`Provider ${this.name} does not support streaming`);
    }

    const startTime = Date.now();
    this.metrics.requests++;

    try {
      this.validateRequest(request);
      
      yield* this.processStreamRequest(request);
      
      const latency = Date.now() - startTime;
      this.metrics.averageLatency = (this.metrics.averageLatency + latency) / 2;

    } catch (error) {
      this.metrics.errors++;
      throw new Error(`${this.name} stream failed: ${error}`);
    }
  }

  /**
   * Provider-specific request processing
   */
  protected abstract processProviderRequest(request: AIRequest): Promise<AIResponse>;

  /**
   * Provider-specific stream processing
   */
  protected abstract processStreamRequest(request: AIRequest): AsyncIterable<Partial<AIResponse>>;

  /**
   * Check if provider supports capability
   */
  supports(capability: string): boolean {
    return this.config.capabilities.includes(capability as any);
  }

  /**
   * Get available models
   */
  abstract getModels(): Promise<string[]>;

  /**
   * Get provider metrics
   */
  async getMetrics(): Promise<AIProviderMetrics> {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime.getTime()
    };
  }

  /**
   * Validate AI request
   */
  protected validateRequest(request: AIRequest): void {
    if (!request.id) {
      throw new Error('Request ID is required');
    }

    if (!request.type) {
      throw new Error('Request type is required');
    }

    // Type-specific validation
    switch (request.type) {
      case 'chat':
        if (!request.messages || request.messages.length === 0) {
          throw new Error('Chat request requires messages');
        }
        break;
      case 'completion':
        if (!request.prompt) {
          throw new Error('Completion request requires prompt');
        }
        break;
      case 'embedding':
        if (!request.prompt) {
          throw new Error('Embedding request requires prompt');
        }
        break;
    }

    // Check token limits
    if (this.config.limits?.tokensPerRequest) {
      const estimatedTokens = this.estimateTokens(request);
      if (estimatedTokens > this.config.limits.tokensPerRequest) {
        throw new Error(`Request exceeds token limit: ${estimatedTokens} > ${this.config.limits.tokensPerRequest}`);
      }
    }
  }

  /**
   * Estimate token count for request
   */
  protected estimateTokens(request: AIRequest): number {
    // Simple estimation - can be improved with actual tokenizer
    let text = '';
    
    if (request.messages) {
      text = request.messages.map(m => m.content).join(' ');
    } else if (request.prompt) {
      text = request.prompt;
    }
    
    return Math.ceil(text.length / 4); // Rough estimation: 4 chars per token
  }

  /**
   * Update provider metrics
   */
  protected updateMetrics(response: AIResponse, latency: number): void {
    this.metrics.averageLatency = (this.metrics.averageLatency + latency) / 2;
    this.metrics.tokensUsed += response.usage.totalTokens;
    this.metrics.cost += response.usage.cost || 0;
  }

  /**
   * Start metrics tracking
   */
  protected startMetricsTracking(): void {
    // Update uptime every second
    setInterval(() => {
      this.metrics.uptime = Date.now() - this.startTime.getTime();
    }, 1000);
  }

  /**
   * Create standardized response
   */
  protected createResponse(
    request: AIRequest,
    content: string,
    finishReason: AIResponse['finishReason'],
    usage: AIResponse['usage'],
    metadata?: Record<string, any>
  ): AIResponse {
    return {
      id: `${this.id}-${Date.now()}`,
      requestId: request.id,
      provider: this.id,
      model: request.model || this.config.config.model || 'default',
      content,
      finishReason,
      usage,
      metadata,
      timestamp: new Date(),
      latency: 0 // Will be set by caller
    };
  }

  /**
   * Handle provider-specific errors
   */
  protected handleProviderError(error: any): Error {
    if (error.response?.status === 429) {
      return new Error('Rate limit exceeded');
    }
    
    if (error.response?.status === 401) {
      return new Error('Authentication failed');
    }
    
    if (error.response?.status === 403) {
      return new Error('Access forbidden');
    }
    
    return error instanceof Error ? error : new Error('Unknown provider error');
  }
}
