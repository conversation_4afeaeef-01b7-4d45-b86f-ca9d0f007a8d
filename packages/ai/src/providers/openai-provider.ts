import { OpenAI } from '@langchain/openai';
import { ChatOpenAI } from '@langchain/openai';
import { OpenAIEmbeddings } from '@langchain/openai';
import { 
  AIRequest, 
  AIResponse, 
  AIProviderConfig 
} from '@unified-assistant/types';
import { BaseAIProvider } from './base-provider';

/**
 * OpenAI Provider Implementation
 * 
 * LEVER approach implementation:
 * L - Leverage existing LangChain OpenAI integration
 * E - Extend with unified interface and error handling
 * V - Verify through comprehensive request validation
 * E - Eliminate duplicate OpenAI configurations across modules
 * R - Reduce complexity through standardized response format
 */
export class OpenAIProvider extends BaseAIProvider {
  private chatModel?: ChatOpenAI;
  private completionModel?: OpenAI;
  private embeddingModel?: OpenAIEmbeddings;

  constructor(config: AIProviderConfig) {
    super(config);
  }

  /**
   * Initialize OpenAI models
   */
  protected async initializeProvider(): Promise<void> {
    const apiKey = this.config.config.apiKey;
    if (!apiKey) {
      throw new Error('OpenAI API key is required');
    }

    const baseConfig = {
      openAIApiKey: apiKey,
      modelName: this.config.config.model || 'gpt-4',
      temperature: this.config.config.temperature || 0.7,
      maxTokens: this.config.config.maxTokens || 2000,
      timeout: this.config.config.timeout || 30000,
    };

    // Initialize chat model
    if (this.supports('chat')) {
      this.chatModel = new ChatOpenAI({
        ...baseConfig,
        streaming: this.supports('streaming'),
      });
    }

    // Initialize completion model
    if (this.supports('completion')) {
      this.completionModel = new OpenAI({
        ...baseConfig,
      });
    }

    // Initialize embedding model
    if (this.supports('embedding')) {
      this.embeddingModel = new OpenAIEmbeddings({
        openAIApiKey: apiKey,
        modelName: 'text-embedding-ada-002',
      });
    }
  }

  /**
   * Perform health check
   */
  protected async performHealthCheck(): Promise<boolean> {
    try {
      if (this.chatModel) {
        await this.chatModel.invoke([{ role: 'user', content: 'test' }]);
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Process provider-specific request
   */
  protected async processProviderRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      let content: string;
      let usage: AIResponse['usage'];

      switch (request.type) {
        case 'chat':
          const result = await this.processChatRequest(request);
          content = result.content;
          usage = result.usage;
          break;

        case 'completion':
          const completionResult = await this.processCompletionRequest(request);
          content = completionResult.content;
          usage = completionResult.usage;
          break;

        case 'embedding':
          const embeddingResult = await this.processEmbeddingRequest(request);
          content = JSON.stringify(embeddingResult.embedding);
          usage = embeddingResult.usage;
          break;

        default:
          throw new Error(`Unsupported request type: ${request.type}`);
      }

      const response = this.createResponse(
        request,
        content,
        'stop',
        usage,
        { model: this.config.config.model }
      );

      response.latency = Date.now() - startTime;
      return response;

    } catch (error) {
      throw this.handleProviderError(error);
    }
  }

  /**
   * Process chat request
   */
  private async processChatRequest(request: AIRequest): Promise<{
    content: string;
    usage: AIResponse['usage'];
  }> {
    if (!this.chatModel) {
      throw new Error('Chat model not initialized');
    }

    const messages = request.messages!.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    const result = await this.chatModel.invoke(messages);
    
    // Estimate usage (OpenAI doesn't always provide exact counts via LangChain)
    const promptTokens = this.estimateTokens({ ...request, prompt: messages.map(m => m.content).join(' ') });
    const completionTokens = Math.ceil(result.content.length / 4);
    const totalTokens = promptTokens + completionTokens;

    return {
      content: result.content,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, 'gpt-4')
      }
    };
  }

  /**
   * Process completion request
   */
  private async processCompletionRequest(request: AIRequest): Promise<{
    content: string;
    usage: AIResponse['usage'];
  }> {
    if (!this.completionModel) {
      throw new Error('Completion model not initialized');
    }

    const result = await this.completionModel.invoke(request.prompt!);
    
    const promptTokens = this.estimateTokens(request);
    const completionTokens = Math.ceil(result.length / 4);
    const totalTokens = promptTokens + completionTokens;

    return {
      content: result,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, 'gpt-3.5-turbo')
      }
    };
  }

  /**
   * Process embedding request
   */
  private async processEmbeddingRequest(request: AIRequest): Promise<{
    embedding: number[];
    usage: AIResponse['usage'];
  }> {
    if (!this.embeddingModel) {
      throw new Error('Embedding model not initialized');
    }

    const embedding = await this.embeddingModel.embedQuery(request.prompt!);
    
    const promptTokens = this.estimateTokens(request);

    return {
      embedding,
      usage: {
        promptTokens,
        completionTokens: 0,
        totalTokens: promptTokens,
        cost: this.calculateCost(promptTokens, 'text-embedding-ada-002')
      }
    };
  }

  /**
   * Process stream request
   */
  protected async *processStreamRequest(request: AIRequest): AsyncIterable<Partial<AIResponse>> {
    if (!this.chatModel || request.type !== 'chat') {
      throw new Error('Streaming only supported for chat requests');
    }

    const messages = request.messages!.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    const stream = await this.chatModel.stream(messages);
    
    let fullContent = '';
    for await (const chunk of stream) {
      fullContent += chunk.content;
      
      yield {
        id: `${this.id}-stream-${Date.now()}`,
        requestId: request.id,
        provider: this.id,
        content: chunk.content,
        finishReason: chunk.response_metadata?.finish_reason as any || undefined,
        timestamp: new Date()
      };
    }

    // Final chunk with usage information
    const promptTokens = this.estimateTokens({ ...request, prompt: messages.map(m => m.content).join(' ') });
    const completionTokens = Math.ceil(fullContent.length / 4);
    const totalTokens = promptTokens + completionTokens;

    yield {
      id: `${this.id}-stream-final`,
      requestId: request.id,
      provider: this.id,
      content: '',
      finishReason: 'stop',
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, 'gpt-4')
      },
      timestamp: new Date()
    };
  }

  /**
   * Get available models
   */
  async getModels(): Promise<string[]> {
    return [
      'gpt-4',
      'gpt-4-turbo',
      'gpt-3.5-turbo',
      'gpt-3.5-turbo-16k',
      'text-embedding-ada-002'
    ];
  }

  /**
   * Calculate cost based on model and tokens
   */
  private calculateCost(tokens: number, model: string): number {
    const pricing: Record<string, { input: number; output: number }> = {
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
      'text-embedding-ada-002': { input: 0.0001, output: 0 }
    };

    const modelPricing = pricing[model] || pricing['gpt-3.5-turbo'];
    return (tokens / 1000) * modelPricing.input; // Simplified cost calculation
  }
}
