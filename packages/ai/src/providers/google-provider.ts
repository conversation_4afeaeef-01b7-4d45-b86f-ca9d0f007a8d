import { ChatGoogleGenerative<PERSON><PERSON>, GoogleGenerativeAIEmbeddings } from '@langchain/google-genai';
import { 
  AIRequest, 
  AIResponse, 
  AIProviderConfig 
} from '@unified-assistant/types';
import { BaseAIProvider } from './base-provider';

/**
 * Google AI Provider Implementation
 * 
 * LEVER approach implementation:
 * L - Leverage existing LangChain Google Generative AI integration
 * E - Extend with unified interface and Gemini-specific optimizations
 * V - Verify through comprehensive request validation and error handling
 * E - Eliminate duplicate Google AI configurations across modules
 * R - Reduce complexity through standardized response format
 */
export class GoogleProvider extends BaseAIProvider {
  private chatModel?: ChatGoogleGenerativeAI;
  private embeddingModel?: GoogleGenerativeAIEmbeddings;

  constructor(config: AIProviderConfig) {
    super(config);
  }

  /**
   * Initialize Google AI models
   */
  protected async initializeProvider(): Promise<void> {
    const apiKey = this.config.config.apiKey;
    if (!apiKey) {
      throw new Error('Google AI API key is required');
    }

    const baseConfig = {
      apiKey,
      modelName: this.config.config.model || 'gemini-pro',
      temperature: this.config.config.temperature || 0.7,
      maxOutputTokens: this.config.config.maxTokens || 2048,
      timeout: this.config.config.timeout || 30000,
    };

    // Initialize chat model
    if (this.supports('chat')) {
      this.chatModel = new ChatGoogleGenerativeAI({
        ...baseConfig,
        streaming: this.supports('streaming'),
      });
    }

    // Initialize embedding model
    if (this.supports('embedding')) {
      this.embeddingModel = new GoogleGenerativeAIEmbeddings({
        apiKey,
        modelName: 'embedding-001',
      });
    }
  }

  /**
   * Perform health check
   */
  protected async performHealthCheck(): Promise<boolean> {
    try {
      if (this.chatModel) {
        await this.chatModel.invoke([{ role: 'user', content: 'test' }]);
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Process provider-specific request
   */
  protected async processProviderRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      let content: string;
      let usage: AIResponse['usage'];

      switch (request.type) {
        case 'chat':
          const result = await this.processChatRequest(request);
          content = result.content;
          usage = result.usage;
          break;

        case 'completion':
          // Google AI doesn't have separate completion endpoint, use chat
          const completionResult = await this.processCompletionAsChatRequest(request);
          content = completionResult.content;
          usage = completionResult.usage;
          break;

        case 'embedding':
          const embeddingResult = await this.processEmbeddingRequest(request);
          content = JSON.stringify(embeddingResult.embedding);
          usage = embeddingResult.usage;
          break;

        default:
          throw new Error(`Unsupported request type: ${request.type}`);
      }

      const response = this.createResponse(
        request,
        content,
        'stop',
        usage,
        { model: this.config.config.model }
      );

      response.latency = Date.now() - startTime;
      return response;

    } catch (error) {
      throw this.handleProviderError(error);
    }
  }

  /**
   * Process chat request
   */
  private async processChatRequest(request: AIRequest): Promise<{
    content: string;
    usage: AIResponse['usage'];
  }> {
    if (!this.chatModel) {
      throw new Error('Chat model not initialized');
    }

    const messages = request.messages!.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user', // Google uses user/model
      content: msg.content
    }));

    const result = await this.chatModel.invoke(messages);
    
    // Estimate usage (Google AI provides usage info in response metadata)
    const promptTokens = this.estimateTokens({ ...request, prompt: messages.map(m => m.content).join(' ') });
    const completionTokens = Math.ceil(result.content.length / 4);
    const totalTokens = promptTokens + completionTokens;

    return {
      content: result.content,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, this.config.config.model || 'gemini-pro')
      }
    };
  }

  /**
   * Process completion request as chat (Google AI doesn't have separate completion)
   */
  private async processCompletionAsChatRequest(request: AIRequest): Promise<{
    content: string;
    usage: AIResponse['usage'];
  }> {
    if (!this.chatModel) {
      throw new Error('Chat model not initialized');
    }

    const messages = [{ role: 'user', content: request.prompt! }];
    const result = await this.chatModel.invoke(messages);
    
    const promptTokens = this.estimateTokens(request);
    const completionTokens = Math.ceil(result.content.length / 4);
    const totalTokens = promptTokens + completionTokens;

    return {
      content: result.content,
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, this.config.config.model || 'gemini-pro')
      }
    };
  }

  /**
   * Process embedding request
   */
  private async processEmbeddingRequest(request: AIRequest): Promise<{
    embedding: number[];
    usage: AIResponse['usage'];
  }> {
    if (!this.embeddingModel) {
      throw new Error('Embedding model not initialized');
    }

    const embedding = await this.embeddingModel.embedQuery(request.prompt!);
    
    const promptTokens = this.estimateTokens(request);

    return {
      embedding,
      usage: {
        promptTokens,
        completionTokens: 0,
        totalTokens: promptTokens,
        cost: this.calculateCost(promptTokens, 'embedding-001')
      }
    };
  }

  /**
   * Process stream request
   */
  protected async *processStreamRequest(request: AIRequest): AsyncIterable<Partial<AIResponse>> {
    if (!this.chatModel || request.type !== 'chat') {
      throw new Error('Streaming only supported for chat requests');
    }

    const messages = request.messages!.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      content: msg.content
    }));

    const stream = await this.chatModel.stream(messages);
    
    let fullContent = '';
    for await (const chunk of stream) {
      fullContent += chunk.content;
      
      yield {
        id: `${this.id}-stream-${Date.now()}`,
        requestId: request.id,
        provider: this.id,
        content: chunk.content,
        finishReason: chunk.response_metadata?.finish_reason as any || undefined,
        timestamp: new Date()
      };
    }

    // Final chunk with usage information
    const promptTokens = this.estimateTokens({ ...request, prompt: messages.map(m => m.content).join(' ') });
    const completionTokens = Math.ceil(fullContent.length / 4);
    const totalTokens = promptTokens + completionTokens;

    yield {
      id: `${this.id}-stream-final`,
      requestId: request.id,
      provider: this.id,
      content: '',
      finishReason: 'stop',
      usage: {
        promptTokens,
        completionTokens,
        totalTokens,
        cost: this.calculateCost(totalTokens, this.config.config.model || 'gemini-pro')
      },
      timestamp: new Date()
    };
  }

  /**
   * Get available models
   */
  async getModels(): Promise<string[]> {
    return [
      'gemini-pro',
      'gemini-pro-vision',
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'embedding-001'
    ];
  }

  /**
   * Calculate cost based on model and tokens
   */
  private calculateCost(tokens: number, model: string): number {
    const pricing: Record<string, { input: number; output: number }> = {
      'gemini-pro': { input: 0.0005, output: 0.0015 },
      'gemini-pro-vision': { input: 0.0005, output: 0.0015 },
      'gemini-1.5-pro': { input: 0.0035, output: 0.0105 },
      'gemini-1.5-flash': { input: 0.00035, output: 0.00105 },
      'embedding-001': { input: 0.0001, output: 0 }
    };

    const modelPricing = pricing[model] || pricing['gemini-pro'];
    return (tokens / 1000) * modelPricing.input; // Simplified cost calculation
  }

  /**
   * Handle Google AI-specific errors
   */
  protected handleProviderError(error: any): Error {
    // Google AI-specific error handling
    if (error.message?.includes('quota')) {
      return new Error('Google AI quota exceeded');
    }
    
    if (error.message?.includes('API_KEY_INVALID')) {
      return new Error('Invalid Google AI API key');
    }
    
    if (error.message?.includes('SAFETY')) {
      return new Error('Content blocked by Google AI safety filters');
    }
    
    return super.handleProviderError(error);
  }
}
