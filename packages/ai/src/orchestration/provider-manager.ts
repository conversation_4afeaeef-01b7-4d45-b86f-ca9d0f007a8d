import { 
  AIProvider, 
  AIProviderConfig, 
  AIProviderMetrics 
} from '@unified-assistant/types';
import { OpenAIProvider } from '../providers/openai-provider';
import { AnthropicProvider } from '../providers/anthropic-provider';
import { GoogleProvider } from '../providers/google-provider';

/**
 * AI Provider Manager
 * 
 * LEVER approach implementation:
 * L - Leverage existing provider implementations
 * E - Extend with dynamic provider registration and management
 * V - Verify through health checks and error handling
 * E - Eliminate duplicate provider initialization across modules
 * R - Reduce complexity through centralized provider lifecycle management
 */
export class ProviderManager {
  private providers: Map<string, AIProvider> = new Map();
  private configs: Map<string, AIProviderConfig> = new Map();
  private healthCheckInterval?: NodeJS.Timeout;

  constructor() {
    this.startHealthChecking();
  }

  /**
   * Register a provider with configuration
   */
  async registerProvider(config: AIProviderConfig): Promise<void> {
    if (this.providers.has(config.id)) {
      throw new Error(`Provider ${config.id} already registered`);
    }

    const provider = this.createProvider(config);
    
    try {
      await provider.initialize();
      this.providers.set(config.id, provider);
      this.configs.set(config.id, config);
    } catch (error) {
      throw new Error(`Failed to register provider ${config.id}: ${error}`);
    }
  }

  /**
   * Unregister a provider
   */
  async unregisterProvider(providerId: string): Promise<void> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    this.providers.delete(providerId);
    this.configs.delete(providerId);
  }

  /**
   * Get a provider by ID
   */
  async getProvider(providerId: string): Promise<AIProvider | undefined> {
    return this.providers.get(providerId);
  }

  /**
   * Get all registered providers
   */
  async getAllProviders(): Promise<AIProvider[]> {
    return Array.from(this.providers.values());
  }

  /**
   * Get providers by capability
   */
  async getProvidersByCapability(capability: string): Promise<AIProvider[]> {
    const providers: AIProvider[] = [];
    
    for (const provider of this.providers.values()) {
      if (provider.supports(capability)) {
        providers.push(provider);
      }
    }
    
    return providers;
  }

  /**
   * Get healthy providers
   */
  async getHealthyProviders(): Promise<AIProvider[]> {
    const healthyProviders: AIProvider[] = [];
    
    for (const provider of this.providers.values()) {
      try {
        const isHealthy = await provider.healthCheck();
        if (isHealthy) {
          healthyProviders.push(provider);
        }
      } catch (error) {
        // Provider is unhealthy, skip it
      }
    }
    
    return healthyProviders;
  }

  /**
   * Get provider metrics for all providers
   */
  async getAllMetrics(): Promise<Record<string, AIProviderMetrics>> {
    const metrics: Record<string, AIProviderMetrics> = {};
    
    for (const [id, provider] of this.providers.entries()) {
      try {
        metrics[id] = await provider.getMetrics();
      } catch (error) {
        // If metrics fail, provide default values
        metrics[id] = {
          requests: 0,
          errors: 0,
          averageLatency: 0,
          tokensUsed: 0,
          cost: 0,
          uptime: 0
        };
      }
    }
    
    return metrics;
  }

  /**
   * Get best provider for request type
   */
  async getBestProvider(
    requestType: string,
    capability?: string
  ): Promise<AIProvider | undefined> {
    let candidates = await this.getHealthyProviders();
    
    // Filter by capability if specified
    if (capability) {
      candidates = candidates.filter(provider => provider.supports(capability));
    }
    
    if (candidates.length === 0) {
      return undefined;
    }
    
    // Simple selection based on priority and performance
    // In a real implementation, this could be more sophisticated
    const configs = candidates.map(provider => this.configs.get(provider.id)!);
    const sortedConfigs = configs.sort((a, b) => b.priority - a.priority);
    
    const bestProviderId = sortedConfigs[0].id;
    return this.providers.get(bestProviderId);
  }

  /**
   * Update provider configuration
   */
  async updateProviderConfig(
    providerId: string, 
    updates: Partial<AIProviderConfig>
  ): Promise<void> {
    const currentConfig = this.configs.get(providerId);
    if (!currentConfig) {
      throw new Error(`Provider ${providerId} not found`);
    }

    const newConfig = { ...currentConfig, ...updates };
    
    // Unregister and re-register with new config
    await this.unregisterProvider(providerId);
    await this.registerProvider(newConfig);
  }

  /**
   * Enable/disable provider
   */
  async setProviderEnabled(providerId: string, enabled: boolean): Promise<void> {
    const config = this.configs.get(providerId);
    if (!config) {
      throw new Error(`Provider ${providerId} not found`);
    }

    await this.updateProviderConfig(providerId, { enabled });
  }

  /**
   * Get provider status
   */
  async getProviderStatus(): Promise<Record<string, {
    enabled: boolean;
    healthy: boolean;
    lastCheck: Date;
    metrics: AIProviderMetrics;
  }>> {
    const status: Record<string, any> = {};
    
    for (const [id, provider] of this.providers.entries()) {
      const config = this.configs.get(id)!;
      const healthy = await provider.healthCheck();
      const metrics = await provider.getMetrics();
      
      status[id] = {
        enabled: config.enabled,
        healthy,
        lastCheck: new Date(),
        metrics
      };
    }
    
    return status;
  }

  /**
   * Create provider instance based on type
   */
  private createProvider(config: AIProviderConfig): AIProvider {
    switch (config.type) {
      case 'openai':
        return new OpenAIProvider(config);
      case 'anthropic':
        return new AnthropicProvider(config);
      case 'google':
        return new GoogleProvider(config);
      default:
        throw new Error(`Unsupported provider type: ${config.type}`);
    }
  }

  /**
   * Start periodic health checking
   */
  private startHealthChecking(): void {
    this.healthCheckInterval = setInterval(async () => {
      for (const [id, provider] of this.providers.entries()) {
        try {
          const isHealthy = await provider.healthCheck();
          if (!isHealthy) {
            console.warn(`Provider ${id} health check failed`);
          }
        } catch (error) {
          console.error(`Health check error for provider ${id}:`, error);
        }
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Stop health checking
   */
  destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
  }

  /**
   * Register default providers with environment variables
   */
  async registerDefaultProviders(): Promise<void> {
    const defaultConfigs: AIProviderConfig[] = [];

    // OpenAI
    if (process.env.OPENAI_API_KEY) {
      defaultConfigs.push({
        id: 'openai',
        name: 'OpenAI',
        type: 'openai',
        enabled: true,
        priority: 1,
        config: {
          apiKey: process.env.OPENAI_API_KEY,
          model: 'gpt-4',
          temperature: 0.7,
          maxTokens: 2000
        },
        capabilities: ['chat', 'completion', 'embedding', 'streaming', 'function-calling'],
        limits: {
          requestsPerMinute: 60,
          tokensPerRequest: 4000
        }
      });
    }

    // Anthropic
    if (process.env.ANTHROPIC_API_KEY) {
      defaultConfigs.push({
        id: 'anthropic',
        name: 'Anthropic',
        type: 'anthropic',
        enabled: true,
        priority: 2,
        config: {
          apiKey: process.env.ANTHROPIC_API_KEY,
          model: 'claude-3-sonnet-20240229',
          temperature: 0.7,
          maxTokens: 4000
        },
        capabilities: ['chat', 'completion', 'streaming'],
        limits: {
          requestsPerMinute: 50,
          tokensPerRequest: 8000
        }
      });
    }

    // Google AI
    if (process.env.GOOGLE_AI_API_KEY) {
      defaultConfigs.push({
        id: 'google',
        name: 'Google AI',
        type: 'google',
        enabled: true,
        priority: 3,
        config: {
          apiKey: process.env.GOOGLE_AI_API_KEY,
          model: 'gemini-pro',
          temperature: 0.7,
          maxTokens: 2048
        },
        capabilities: ['chat', 'completion', 'embedding', 'streaming', 'vision'],
        limits: {
          requestsPerMinute: 60,
          tokensPerRequest: 4000
        }
      });
    }

    // Register all available providers
    for (const config of defaultConfigs) {
      try {
        await this.registerProvider(config);
        console.log(`✅ Registered ${config.name} provider`);
      } catch (error) {
        console.error(`❌ Failed to register ${config.name} provider:`, error);
      }
    }
  }
}
