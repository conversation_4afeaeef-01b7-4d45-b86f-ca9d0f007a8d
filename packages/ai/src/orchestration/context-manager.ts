import { 
  AIContext, 
  AIMessage, 
  AIPreferences,
  EventBus,
  PlatformEvent
} from '@unified-assistant/types';
import { v4 as uuidv4 } from 'uuid';

/**
 * AI Context Manager
 * 
 * LEVER approach implementation:
 * L - Leverage existing conversation patterns from agent-inbox
 * E - Extend with cross-module context sharing and persistence
 * V - Verify through context validation and consistency checks
 * E - Eliminate duplicate context management across modules
 * R - Reduce complexity through centralized context lifecycle management
 */
export class ContextManager {
  private contexts: Map<string, AIContext> = new Map();
  private contextHistory: Map<string, AIMessage[]> = new Map();
  private contextSubscriptions: Map<string, Set<string>> = new Map(); // contextId -> moduleIds
  private eventBus?: EventBus;
  private cleanupInterval?: NodeJS.Timeout;

  constructor(eventBus?: EventBus) {
    this.eventBus = eventBus;
    this.startContextCleanup();
    this.setupEventHandlers();
  }

  /**
   * Create a new AI context
   */
  createContext(
    moduleId: string,
    userId?: string,
    sessionId?: string,
    preferences?: AIPreferences
  ): AIContext {
    const context: AIContext = {
      conversationId: uuidv4(),
      sessionId: sessionId || uuidv4(),
      userId,
      moduleId,
      history: [],
      variables: {},
      preferences: preferences || this.getDefaultPreferences()
    };

    this.contexts.set(context.conversationId!, context);
    this.contextHistory.set(context.conversationId!, []);
    
    // Subscribe module to context updates
    this.subscribeModuleToContext(context.conversationId!, moduleId);

    this.emitContextEvent('context:created', context);
    
    return context;
  }

  /**
   * Get context by conversation ID
   */
  getContext(conversationId: string): AIContext | undefined {
    return this.contexts.get(conversationId);
  }

  /**
   * Update context
   */
  updateContext(conversationId: string, updates: Partial<AIContext>): AIContext {
    const context = this.contexts.get(conversationId);
    if (!context) {
      throw new Error(`Context ${conversationId} not found`);
    }

    const updatedContext = { ...context, ...updates };
    this.contexts.set(conversationId, updatedContext);

    this.emitContextEvent('context:updated', updatedContext);
    
    return updatedContext;
  }

  /**
   * Add message to context history
   */
  addMessage(conversationId: string, message: AIMessage): void {
    const context = this.contexts.get(conversationId);
    if (!context) {
      throw new Error(`Context ${conversationId} not found`);
    }

    // Add to context history
    const history = this.contextHistory.get(conversationId) || [];
    history.push(message);
    this.contextHistory.set(conversationId, history);

    // Update context with recent history (keep last 20 messages)
    const recentHistory = history.slice(-20);
    this.updateContext(conversationId, { history: recentHistory });

    this.emitContextEvent('context:message_added', { conversationId, message });
  }

  /**
   * Get conversation history
   */
  getHistory(conversationId: string, limit?: number): AIMessage[] {
    const history = this.contextHistory.get(conversationId) || [];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Share context with another module
   */
  async shareContext(
    conversationId: string,
    targetModuleId: string,
    sourceModuleId: string
  ): Promise<void> {
    const context = this.contexts.get(conversationId);
    if (!context) {
      throw new Error(`Context ${conversationId} not found`);
    }

    // Subscribe target module to context updates
    this.subscribeModuleToContext(conversationId, targetModuleId);

    // Create shared context for target module
    const sharedContext: AIContext = {
      ...context,
      moduleId: targetModuleId,
      // Include relevant history but not sensitive variables
      variables: this.filterSensitiveVariables(context.variables || {})
    };

    this.emitContextEvent('context:shared', {
      conversationId,
      sourceModule: sourceModuleId,
      targetModule: targetModuleId,
      context: sharedContext
    });
  }

  /**
   * Set context variable
   */
  setVariable(conversationId: string, key: string, value: any): void {
    const context = this.contexts.get(conversationId);
    if (!context) {
      throw new Error(`Context ${conversationId} not found`);
    }

    const variables = { ...context.variables, [key]: value };
    this.updateContext(conversationId, { variables });
  }

  /**
   * Get context variable
   */
  getVariable(conversationId: string, key: string): any {
    const context = this.contexts.get(conversationId);
    return context?.variables?.[key];
  }

  /**
   * Update user preferences
   */
  updatePreferences(conversationId: string, preferences: Partial<AIPreferences>): void {
    const context = this.contexts.get(conversationId);
    if (!context) {
      throw new Error(`Context ${conversationId} not found`);
    }

    const updatedPreferences = { ...context.preferences, ...preferences };
    this.updateContext(conversationId, { preferences: updatedPreferences });
  }

  /**
   * Get contexts for a specific module
   */
  getModuleContexts(moduleId: string): AIContext[] {
    const contexts: AIContext[] = [];
    
    for (const context of this.contexts.values()) {
      if (context.moduleId === moduleId) {
        contexts.push(context);
      }
    }
    
    return contexts;
  }

  /**
   * Get contexts for a specific user
   */
  getUserContexts(userId: string): AIContext[] {
    const contexts: AIContext[] = [];
    
    for (const context of this.contexts.values()) {
      if (context.userId === userId) {
        contexts.push(context);
      }
    }
    
    return contexts;
  }

  /**
   * Delete context
   */
  deleteContext(conversationId: string): void {
    const context = this.contexts.get(conversationId);
    if (!context) {
      return;
    }

    this.contexts.delete(conversationId);
    this.contextHistory.delete(conversationId);
    this.contextSubscriptions.delete(conversationId);

    this.emitContextEvent('context:deleted', { conversationId });
  }

  /**
   * Clean up expired contexts
   */
  cleanupExpiredContexts(maxAge: number = 24 * 60 * 60 * 1000): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [conversationId, context] of this.contexts.entries()) {
      // Check if context has been inactive for too long
      const lastActivity = this.getLastActivityTime(conversationId);
      if (now - lastActivity > maxAge) {
        this.deleteContext(conversationId);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  /**
   * Get context statistics
   */
  getStatistics(): {
    totalContexts: number;
    activeContexts: number;
    totalMessages: number;
    contextsByModule: Record<string, number>;
  } {
    const stats = {
      totalContexts: this.contexts.size,
      activeContexts: 0,
      totalMessages: 0,
      contextsByModule: {} as Record<string, number>
    };

    const recentThreshold = Date.now() - (60 * 60 * 1000); // 1 hour

    for (const [conversationId, context] of this.contexts.entries()) {
      // Count messages
      const history = this.contextHistory.get(conversationId) || [];
      stats.totalMessages += history.length;

      // Count active contexts (with recent activity)
      const lastActivity = this.getLastActivityTime(conversationId);
      if (lastActivity > recentThreshold) {
        stats.activeContexts++;
      }

      // Count by module
      const moduleId = context.moduleId || 'unknown';
      stats.contextsByModule[moduleId] = (stats.contextsByModule[moduleId] || 0) + 1;
    }

    return stats;
  }

  /**
   * Subscribe module to context updates
   */
  private subscribeModuleToContext(conversationId: string, moduleId: string): void {
    const subscribers = this.contextSubscriptions.get(conversationId) || new Set();
    subscribers.add(moduleId);
    this.contextSubscriptions.set(conversationId, subscribers);
  }

  /**
   * Get last activity time for context
   */
  private getLastActivityTime(conversationId: string): number {
    const history = this.contextHistory.get(conversationId) || [];
    if (history.length === 0) {
      return 0;
    }

    // Find the most recent message timestamp
    const lastMessage = history[history.length - 1];
    return new Date(lastMessage.timestamp || Date.now()).getTime();
  }

  /**
   * Filter sensitive variables from context
   */
  private filterSensitiveVariables(variables: Record<string, any>): Record<string, any> {
    const filtered: Record<string, any> = {};
    const sensitiveKeys = ['apiKey', 'password', 'token', 'secret', 'credential'];

    for (const [key, value] of Object.entries(variables)) {
      const isSensitive = sensitiveKeys.some(sensitiveKey => 
        key.toLowerCase().includes(sensitiveKey)
      );
      
      if (!isSensitive) {
        filtered[key] = value;
      }
    }

    return filtered;
  }

  /**
   * Get default preferences
   */
  private getDefaultPreferences(): AIPreferences {
    return {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2000,
      language: 'en',
      tone: 'normal',
      verbosity: 'normal'
    };
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    if (!this.eventBus) return;

    // Handle context sharing requests
    this.eventBus.on('context:share_request', {
      id: 'context-manager-share-handler',
      eventType: 'context:share_request',
      handler: async (event: PlatformEvent) => {
        const { conversationId, targetModule, sourceModule } = event.payload;
        await this.shareContext(conversationId, targetModule, sourceModule);
      }
    });

    // Handle context cleanup requests
    this.eventBus.on('context:cleanup_request', {
      id: 'context-manager-cleanup-handler',
      eventType: 'context:cleanup_request',
      handler: (event: PlatformEvent) => {
        const { maxAge } = event.payload;
        this.cleanupExpiredContexts(maxAge);
      }
    });
  }

  /**
   * Emit context event
   */
  private emitContextEvent(type: string, payload: any): void {
    if (!this.eventBus) return;

    const event: PlatformEvent = {
      id: uuidv4(),
      type,
      source: 'context-manager',
      payload,
      timestamp: new Date(),
      priority: 'normal',
      persistent: false
    };

    this.eventBus.emit(event);
  }

  /**
   * Start periodic context cleanup
   */
  private startContextCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      const cleaned = this.cleanupExpiredContexts();
      if (cleaned > 0) {
        console.log(`Cleaned up ${cleaned} expired contexts`);
      }
    }, 60 * 60 * 1000); // Run every hour
  }

  /**
   * Destroy context manager
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}
