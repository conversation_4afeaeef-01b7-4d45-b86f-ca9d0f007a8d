{"name": "@unified-assistant/ai", "version": "0.1.0", "description": "Unified AI orchestration system for the AI assistant platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"@unified-assistant/types": "workspace:*", "@langchain/core": "^0.3.14", "@langchain/openai": "^0.3.11", "@langchain/anthropic": "^0.3.6", "@langchain/google-genai": "^0.1.2", "zod": "^3.23.8", "uuid": "^11.0.3"}, "devDependencies": {"@types/node": "^20", "@types/uuid": "^10.0.0", "typescript": "^5", "eslint": "^8", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.8.1", "jest": "^29"}, "peerDependencies": {"react": "^18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./providers": {"types": "./dist/providers/index.d.ts", "import": "./dist/providers/index.js", "require": "./dist/providers/index.js"}, "./orchestration": {"types": "./dist/orchestration/index.d.ts", "import": "./dist/orchestration/index.js", "require": "./dist/orchestration/index.js"}}, "files": ["dist", "README.md"]}