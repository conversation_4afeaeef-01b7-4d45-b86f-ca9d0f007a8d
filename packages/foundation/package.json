{"name": "@unified-assistant/foundation", "version": "0.1.0", "description": "Foundation integration layer for the unified AI assistant platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"@unified-assistant/types": "workspace:*", "@unified-assistant/ai": "workspace:*", "@unified-assistant/events": "workspace:*", "@unified-assistant/ui": "workspace:*", "zustand": "^4.4.7", "immer": "^10.0.3", "uuid": "^11.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/uuid": "^10.0.0", "typescript": "^5", "eslint": "^8", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.8.1", "jest": "^29"}, "peerDependencies": {"react": "^18", "react-dom": "^18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./platform": {"types": "./dist/platform/index.d.ts", "import": "./dist/platform/index.js", "require": "./dist/platform/index.js"}, "./modules": {"types": "./dist/modules/index.d.ts", "import": "./dist/modules/index.js", "require": "./dist/modules/index.js"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "import": "./dist/hooks/index.js", "require": "./dist/hooks/index.js"}}, "files": ["dist", "README.md"]}