import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { 
  PlatformConfig, 
  PlatformStatus, 
  ModuleStatus,
  EventBus 
} from '@unified-assistant/types';

/**
 * Platform Store
 * 
 * LEVER approach implementation:
 * L - Leverage foto-fun's proven Zustand + Immer patterns
 * E - Extend with platform-wide state management
 * V - Verify through comprehensive state validation
 * E - Eliminate duplicate platform state across modules
 * R - Reduce complexity through centralized platform state
 */

export interface PlatformState {
  // Configuration
  config: PlatformConfig | null;
  
  // Status
  status: PlatformStatus;
  
  // Modules
  modules: Record<string, ModuleStatus>;
  
  // Features
  features: Record<string, boolean>;
  
  // Metrics
  metrics: {
    uptime: number;
    activeUsers: number;
    requestsPerMinute: number;
    errorRate: number;
    memoryUsage: number;
  };
  
  // Event bus reference
  eventBus: EventBus | null;
  
  // Loading states
  loading: {
    initialization: boolean;
    moduleRegistration: boolean;
    configUpdate: boolean;
  };
  
  // Errors
  errors: Array<{
    id: string;
    message: string;
    timestamp: Date;
    module?: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;
}

export interface PlatformActions {
  // Configuration actions
  setConfig: (config: PlatformConfig) => void;
  updateConfig: (updates: Partial<PlatformConfig>) => void;
  
  // Status actions
  setStatus: (status: PlatformStatus['status']) => void;
  updateHealth: (component: keyof PlatformStatus['health'], healthy: boolean) => void;
  
  // Module actions
  registerModule: (moduleId: string, status: ModuleStatus) => void;
  updateModuleStatus: (moduleId: string, updates: Partial<ModuleStatus>) => void;
  unregisterModule: (moduleId: string) => void;
  
  // Feature actions
  toggleFeature: (feature: string, enabled?: boolean) => void;
  setFeatures: (features: Record<string, boolean>) => void;
  
  // Metrics actions
  updateMetrics: (metrics: Partial<PlatformState['metrics']>) => void;
  incrementRequests: () => void;
  incrementErrors: () => void;
  
  // Event bus actions
  setEventBus: (eventBus: EventBus) => void;
  
  // Loading actions
  setLoading: (key: keyof PlatformState['loading'], loading: boolean) => void;
  
  // Error actions
  addError: (error: Omit<PlatformState['errors'][0], 'id' | 'timestamp'>) => void;
  removeError: (errorId: string) => void;
  clearErrors: () => void;
  
  // Utility actions
  reset: () => void;
  initialize: (config: PlatformConfig, eventBus: EventBus) => void;
}

export type PlatformStore = PlatformState & PlatformActions;

const initialState: PlatformState = {
  config: null,
  status: {
    status: 'initializing',
    modules: [],
    health: {
      database: false,
      ai: false,
      auth: false
    },
    metrics: {
      uptime: 0,
      activeUsers: 0,
      requestsPerMinute: 0
    }
  },
  modules: {},
  features: {},
  metrics: {
    uptime: 0,
    activeUsers: 0,
    requestsPerMinute: 0,
    errorRate: 0,
    memoryUsage: 0
  },
  eventBus: null,
  loading: {
    initialization: false,
    moduleRegistration: false,
    configUpdate: false
  },
  errors: []
};

export const usePlatformStore = create<PlatformStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      // Configuration actions
      setConfig: (config) => set((state) => {
        state.config = config;
        state.features = config.features;
      }),

      updateConfig: (updates) => set((state) => {
        if (state.config) {
          Object.assign(state.config, updates);
          if (updates.features) {
            state.features = { ...state.features, ...updates.features };
          }
        }
      }),

      // Status actions
      setStatus: (status) => set((state) => {
        state.status.status = status;
      }),

      updateHealth: (component, healthy) => set((state) => {
        state.status.health[component] = healthy;
      }),

      // Module actions
      registerModule: (moduleId, moduleStatus) => set((state) => {
        state.modules[moduleId] = moduleStatus;
        state.status.modules = Object.values(state.modules);
      }),

      updateModuleStatus: (moduleId, updates) => set((state) => {
        if (state.modules[moduleId]) {
          Object.assign(state.modules[moduleId], updates);
          state.status.modules = Object.values(state.modules);
        }
      }),

      unregisterModule: (moduleId) => set((state) => {
        delete state.modules[moduleId];
        state.status.modules = Object.values(state.modules);
      }),

      // Feature actions
      toggleFeature: (feature, enabled) => set((state) => {
        state.features[feature] = enabled ?? !state.features[feature];
        if (state.config) {
          state.config.features[feature] = state.features[feature];
        }
      }),

      setFeatures: (features) => set((state) => {
        state.features = features;
        if (state.config) {
          state.config.features = features;
        }
      }),

      // Metrics actions
      updateMetrics: (metrics) => set((state) => {
        Object.assign(state.metrics, metrics);
        Object.assign(state.status.metrics, metrics);
      }),

      incrementRequests: () => set((state) => {
        state.metrics.requestsPerMinute += 1;
        state.status.metrics.requestsPerMinute = state.metrics.requestsPerMinute;
      }),

      incrementErrors: () => set((state) => {
        const totalRequests = state.metrics.requestsPerMinute || 1;
        const errorCount = state.errors.length;
        state.metrics.errorRate = errorCount / totalRequests;
      }),

      // Event bus actions
      setEventBus: (eventBus) => set((state) => {
        state.eventBus = eventBus;
      }),

      // Loading actions
      setLoading: (key, loading) => set((state) => {
        state.loading[key] = loading;
      }),

      // Error actions
      addError: (error) => set((state) => {
        const newError = {
          ...error,
          id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date()
        };
        state.errors.push(newError);
        
        // Keep only last 100 errors
        if (state.errors.length > 100) {
          state.errors = state.errors.slice(-100);
        }
      }),

      removeError: (errorId) => set((state) => {
        state.errors = state.errors.filter(error => error.id !== errorId);
      }),

      clearErrors: () => set((state) => {
        state.errors = [];
      }),

      // Utility actions
      reset: () => set(() => ({ ...initialState })),

      initialize: (config, eventBus) => set((state) => {
        state.config = config;
        state.features = config.features;
        state.eventBus = eventBus;
        state.status.status = 'ready';
        state.loading.initialization = false;
      })
    }))
  )
);

// Selectors for common state access patterns
export const platformSelectors = {
  isInitialized: (state: PlatformStore) => state.config !== null && state.eventBus !== null,
  isHealthy: (state: PlatformStore) => Object.values(state.status.health).every(Boolean),
  getModuleCount: (state: PlatformStore) => Object.keys(state.modules).length,
  getActiveModules: (state: PlatformStore) => 
    Object.values(state.modules).filter(module => module.status === 'ready'),
  getErrorsByModule: (state: PlatformStore, moduleId: string) =>
    state.errors.filter(error => error.module === moduleId),
  getCriticalErrors: (state: PlatformStore) =>
    state.errors.filter(error => error.severity === 'critical'),
  isFeatureEnabled: (state: PlatformStore, feature: string) => 
    state.features[feature] === true,
  getUptime: (state: PlatformStore) => state.metrics.uptime
};

// Subscribe to platform events and update store accordingly
export const setupPlatformStoreEventSync = (eventBus: EventBus) => {
  const store = usePlatformStore.getState();
  
  // Set event bus reference
  store.setEventBus(eventBus);
  
  // Subscribe to platform events
  eventBus.on('platform:initialized', {
    id: 'platform-store-init-sync',
    eventType: 'platform:initialized',
    handler: (event) => {
      const { config } = event.payload;
      store.setConfig(config);
      store.setStatus('ready');
    }
  });
  
  eventBus.on('module:registered', {
    id: 'platform-store-module-sync',
    eventType: 'module:registered',
    handler: (event) => {
      const registration = event.payload;
      store.registerModule(registration.id, {
        id: registration.id,
        name: registration.name,
        status: 'ready',
        health: true,
        lastUpdate: new Date()
      });
    }
  });
  
  eventBus.on('platform:error', {
    id: 'platform-store-error-sync',
    eventType: 'platform:error',
    handler: (event) => {
      const { error, module } = event.payload;
      store.addError({
        message: error,
        module,
        severity: 'high'
      });
    }
  });
};
