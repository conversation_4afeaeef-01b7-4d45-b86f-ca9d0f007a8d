import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { 
  AIRequest, 
  AIResponse, 
  AIContext, 
  AIProviderConfig,
  AIProviderMetrics,
  AIOrchestrationStrategy 
} from '@unified-assistant/types';

/**
 * AI Store
 * 
 * LEVER approach implementation:
 * L - Leverage foto-fun's Zustand patterns for AI state
 * E - Extend with unified AI orchestration state management
 * V - Verify through comprehensive AI state validation
 * E - Eliminate duplicate AI state across modules
 * R - Reduce complexity through centralized AI state
 */

export interface AIState {
  // Providers
  providers: Record<string, AIProviderConfig>;
  providerMetrics: Record<string, AIProviderMetrics>;
  activeProvider: string | null;
  
  // Requests and responses
  activeRequests: Record<string, AIRequest>;
  requestHistory: AIResponse[];
  
  // Contexts
  contexts: Record<string, AIContext>;
  activeContext: string | null;
  
  // Orchestration
  defaultStrategy: AIOrchestrationStrategy;
  strategies: Record<string, AIOrchestrationStrategy>;
  
  // Configuration
  globalSettings: {
    temperature: number;
    maxTokens: number;
    timeout: number;
    retryAttempts: number;
  };
  
  // Loading states
  loading: {
    providers: boolean;
    request: boolean;
    context: boolean;
  };
  
  // Errors
  errors: Array<{
    id: string;
    type: 'provider' | 'request' | 'context' | 'orchestration';
    message: string;
    timestamp: Date;
    providerId?: string;
    requestId?: string;
  }>;
}

export interface AIActions {
  // Provider actions
  addProvider: (config: AIProviderConfig) => void;
  updateProvider: (providerId: string, updates: Partial<AIProviderConfig>) => void;
  removeProvider: (providerId: string) => void;
  setActiveProvider: (providerId: string) => void;
  updateProviderMetrics: (providerId: string, metrics: AIProviderMetrics) => void;
  
  // Request actions
  addRequest: (request: AIRequest) => void;
  updateRequest: (requestId: string, updates: Partial<AIRequest>) => void;
  removeRequest: (requestId: string) => void;
  addResponse: (response: AIResponse) => void;
  
  // Context actions
  addContext: (context: AIContext) => void;
  updateContext: (contextId: string, updates: Partial<AIContext>) => void;
  removeContext: (contextId: string) => void;
  setActiveContext: (contextId: string) => void;
  
  // Orchestration actions
  setDefaultStrategy: (strategy: AIOrchestrationStrategy) => void;
  addStrategy: (name: string, strategy: AIOrchestrationStrategy) => void;
  removeStrategy: (name: string) => void;
  
  // Settings actions
  updateGlobalSettings: (settings: Partial<AIState['globalSettings']>) => void;
  
  // Loading actions
  setLoading: (key: keyof AIState['loading'], loading: boolean) => void;
  
  // Error actions
  addError: (error: Omit<AIState['errors'][0], 'id' | 'timestamp'>) => void;
  removeError: (errorId: string) => void;
  clearErrors: () => void;
  
  // Utility actions
  reset: () => void;
  getProviderHealth: () => Record<string, boolean>;
}

export type AIStore = AIState & AIActions;

const initialState: AIState = {
  providers: {},
  providerMetrics: {},
  activeProvider: null,
  activeRequests: {},
  requestHistory: [],
  contexts: {},
  activeContext: null,
  defaultStrategy: {
    type: 'fallback',
    providers: ['openai', 'anthropic'],
    config: { maxRetries: 2, timeout: 30000 }
  },
  strategies: {},
  globalSettings: {
    temperature: 0.7,
    maxTokens: 2000,
    timeout: 30000,
    retryAttempts: 3
  },
  loading: {
    providers: false,
    request: false,
    context: false
  },
  errors: []
};

export const useAIStore = create<AIStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      // Provider actions
      addProvider: (config) => set((state) => {
        state.providers[config.id] = config;
        if (!state.activeProvider) {
          state.activeProvider = config.id;
        }
      }),

      updateProvider: (providerId, updates) => set((state) => {
        if (state.providers[providerId]) {
          Object.assign(state.providers[providerId], updates);
        }
      }),

      removeProvider: (providerId) => set((state) => {
        delete state.providers[providerId];
        delete state.providerMetrics[providerId];
        if (state.activeProvider === providerId) {
          const remainingProviders = Object.keys(state.providers);
          state.activeProvider = remainingProviders.length > 0 ? remainingProviders[0] : null;
        }
      }),

      setActiveProvider: (providerId) => set((state) => {
        if (state.providers[providerId]) {
          state.activeProvider = providerId;
        }
      }),

      updateProviderMetrics: (providerId, metrics) => set((state) => {
        state.providerMetrics[providerId] = metrics;
      }),

      // Request actions
      addRequest: (request) => set((state) => {
        state.activeRequests[request.id] = request;
      }),

      updateRequest: (requestId, updates) => set((state) => {
        if (state.activeRequests[requestId]) {
          Object.assign(state.activeRequests[requestId], updates);
        }
      }),

      removeRequest: (requestId) => set((state) => {
        delete state.activeRequests[requestId];
      }),

      addResponse: (response) => set((state) => {
        state.requestHistory.unshift(response);
        
        // Keep only last 1000 responses
        if (state.requestHistory.length > 1000) {
          state.requestHistory = state.requestHistory.slice(0, 1000);
        }
        
        // Remove completed request
        delete state.activeRequests[response.requestId];
      }),

      // Context actions
      addContext: (context) => set((state) => {
        const contextId = context.conversationId || `context-${Date.now()}`;
        state.contexts[contextId] = context;
        if (!state.activeContext) {
          state.activeContext = contextId;
        }
      }),

      updateContext: (contextId, updates) => set((state) => {
        if (state.contexts[contextId]) {
          Object.assign(state.contexts[contextId], updates);
        }
      }),

      removeContext: (contextId) => set((state) => {
        delete state.contexts[contextId];
        if (state.activeContext === contextId) {
          const remainingContexts = Object.keys(state.contexts);
          state.activeContext = remainingContexts.length > 0 ? remainingContexts[0] : null;
        }
      }),

      setActiveContext: (contextId) => set((state) => {
        if (state.contexts[contextId]) {
          state.activeContext = contextId;
        }
      }),

      // Orchestration actions
      setDefaultStrategy: (strategy) => set((state) => {
        state.defaultStrategy = strategy;
      }),

      addStrategy: (name, strategy) => set((state) => {
        state.strategies[name] = strategy;
      }),

      removeStrategy: (name) => set((state) => {
        delete state.strategies[name];
      }),

      // Settings actions
      updateGlobalSettings: (settings) => set((state) => {
        Object.assign(state.globalSettings, settings);
      }),

      // Loading actions
      setLoading: (key, loading) => set((state) => {
        state.loading[key] = loading;
      }),

      // Error actions
      addError: (error) => set((state) => {
        const newError = {
          ...error,
          id: `ai-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date()
        };
        state.errors.push(newError);
        
        // Keep only last 50 errors
        if (state.errors.length > 50) {
          state.errors = state.errors.slice(-50);
        }
      }),

      removeError: (errorId) => set((state) => {
        state.errors = state.errors.filter(error => error.id !== errorId);
      }),

      clearErrors: () => set((state) => {
        state.errors = [];
      }),

      // Utility actions
      reset: () => set(() => ({ ...initialState })),

      getProviderHealth: () => {
        const state = get();
        const health: Record<string, boolean> = {};
        
        for (const [providerId, metrics] of Object.entries(state.providerMetrics)) {
          // Consider provider healthy if error rate < 10% and has recent activity
          const errorRate = metrics.errors / (metrics.requests || 1);
          const hasRecentActivity = metrics.uptime > 0;
          health[providerId] = errorRate < 0.1 && hasRecentActivity;
        }
        
        return health;
      }
    }))
  )
);

// Selectors for common AI state access patterns
export const aiSelectors = {
  getActiveProvider: (state: AIStore) => 
    state.activeProvider ? state.providers[state.activeProvider] : null,
  
  getHealthyProviders: (state: AIStore) => {
    const health = state.getProviderHealth();
    return Object.entries(state.providers)
      .filter(([id]) => health[id])
      .map(([, provider]) => provider);
  },
  
  getActiveContext: (state: AIStore) =>
    state.activeContext ? state.contexts[state.activeContext] : null,
  
  getRequestsInProgress: (state: AIStore) => Object.values(state.activeRequests),
  
  getRecentResponses: (state: AIStore, limit = 10) =>
    state.requestHistory.slice(0, limit),
  
  getTotalTokensUsed: (state: AIStore) =>
    Object.values(state.providerMetrics).reduce((total, metrics) => total + metrics.tokensUsed, 0),
  
  getTotalCost: (state: AIStore) =>
    Object.values(state.providerMetrics).reduce((total, metrics) => total + metrics.cost, 0),
  
  getErrorsByType: (state: AIStore, type: AIState['errors'][0]['type']) =>
    state.errors.filter(error => error.type === type),
  
  getProviderByCapability: (state: AIStore, capability: string) =>
    Object.values(state.providers).filter(provider => 
      provider.capabilities.includes(capability as any)
    )
};

// Subscribe to AI events and update store accordingly
export const setupAIStoreEventSync = (eventBus: any) => {
  const store = useAIStore.getState();
  
  // Subscribe to AI events
  eventBus.on('ai:request:start', {
    id: 'ai-store-request-start-sync',
    eventType: 'ai:request:start',
    handler: (event: any) => {
      const { requestId, request } = event.payload;
      store.addRequest({ ...request, id: requestId });
      store.setLoading('request', true);
    }
  });
  
  eventBus.on('ai:request:success', {
    id: 'ai-store-request-success-sync',
    eventType: 'ai:request:success',
    handler: (event: any) => {
      const { result } = event.payload;
      store.addResponse(result.primary);
      store.setLoading('request', false);
    }
  });
  
  eventBus.on('ai:request:error', {
    id: 'ai-store-request-error-sync',
    eventType: 'ai:request:error',
    handler: (event: any) => {
      const { requestId, error } = event.payload;
      store.addError({
        type: 'request',
        message: error,
        requestId
      });
      store.removeRequest(requestId);
      store.setLoading('request', false);
    }
  });
  
  eventBus.on('context:created', {
    id: 'ai-store-context-created-sync',
    eventType: 'context:created',
    handler: (event: any) => {
      const context = event.payload;
      store.addContext(context);
    }
  });
  
  eventBus.on('context:updated', {
    id: 'ai-store-context-updated-sync',
    eventType: 'context:updated',
    handler: (event: any) => {
      const context = event.payload;
      if (context.conversationId) {
        store.updateContext(context.conversationId, context);
      }
    }
  });
};
