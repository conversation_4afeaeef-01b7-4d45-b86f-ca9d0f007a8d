import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { 
  ModuleRegistration, 
  ModuleStatus, 
  ModuleConfig,
  ModuleContext 
} from '@unified-assistant/types';

/**
 * Module Store
 * 
 * LEVER approach implementation:
 * L - Leverage foto-fun's Zustand patterns for module state
 * E - Extend with cross-module state management and communication
 * V - Verify through comprehensive module state validation
 * E - Eliminate duplicate module state across the platform
 * R - Reduce complexity through centralized module state
 */

export interface ModuleState {
  // Module registry
  registrations: Record<string, ModuleRegistration>;
  statuses: Record<string, ModuleStatus>;
  configs: Record<string, ModuleConfig>;
  contexts: Record<string, ModuleContext>;
  
  // Active module
  activeModule: string | null;
  
  // Module communication
  messages: Array<{
    id: string;
    from: string;
    to: string;
    type: string;
    payload: any;
    timestamp: Date;
    status: 'pending' | 'delivered' | 'failed';
  }>;
  
  // Module dependencies
  dependencies: Record<string, string[]>; // moduleId -> dependencies
  dependents: Record<string, string[]>;   // moduleId -> dependents
  
  // Module capabilities
  capabilities: Record<string, string[]>; // moduleId -> capabilities
  
  // Loading states
  loading: {
    registration: boolean;
    initialization: boolean;
    communication: boolean;
  };
  
  // Errors
  errors: Array<{
    id: string;
    moduleId: string;
    type: 'registration' | 'initialization' | 'communication' | 'runtime';
    message: string;
    timestamp: Date;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;
}

export interface ModuleActions {
  // Registration actions
  registerModule: (registration: ModuleRegistration) => void;
  unregisterModule: (moduleId: string) => void;
  updateRegistration: (moduleId: string, updates: Partial<ModuleRegistration>) => void;
  
  // Status actions
  updateModuleStatus: (moduleId: string, status: ModuleStatus) => void;
  setModuleHealth: (moduleId: string, healthy: boolean) => void;
  
  // Configuration actions
  updateModuleConfig: (moduleId: string, config: ModuleConfig) => void;
  updateModuleContext: (moduleId: string, context: ModuleContext) => void;
  
  // Active module actions
  setActiveModule: (moduleId: string) => void;
  
  // Communication actions
  sendMessage: (from: string, to: string, type: string, payload: any) => string;
  updateMessageStatus: (messageId: string, status: 'delivered' | 'failed') => void;
  clearMessages: (olderThan?: Date) => void;
  
  // Dependency actions
  addDependency: (moduleId: string, dependencyId: string) => void;
  removeDependency: (moduleId: string, dependencyId: string) => void;
  updateDependencies: (moduleId: string, dependencies: string[]) => void;
  
  // Capability actions
  updateCapabilities: (moduleId: string, capabilities: string[]) => void;
  
  // Loading actions
  setLoading: (key: keyof ModuleState['loading'], loading: boolean) => void;
  
  // Error actions
  addError: (error: Omit<ModuleState['errors'][0], 'id' | 'timestamp'>) => void;
  removeError: (errorId: string) => void;
  clearErrors: (moduleId?: string) => void;
  
  // Utility actions
  reset: () => void;
  getModuleDependencyTree: (moduleId: string) => string[];
  canUnregisterModule: (moduleId: string) => boolean;
}

export type ModuleStore = ModuleState & ModuleActions;

const initialState: ModuleState = {
  registrations: {},
  statuses: {},
  configs: {},
  contexts: {},
  activeModule: null,
  messages: [],
  dependencies: {},
  dependents: {},
  capabilities: {},
  loading: {
    registration: false,
    initialization: false,
    communication: false
  },
  errors: []
};

export const useModuleStore = create<ModuleStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      // Registration actions
      registerModule: (registration) => set((state) => {
        state.registrations[registration.id] = registration;
        state.statuses[registration.id] = {
          id: registration.id,
          name: registration.name,
          status: 'loading',
          health: false,
          lastUpdate: new Date()
        };
        
        // Set capabilities
        state.capabilities[registration.id] = registration.capabilities || [];
        
        // Set dependencies
        if (registration.dependencies) {
          state.dependencies[registration.id] = registration.dependencies;
          
          // Update dependents
          for (const depId of registration.dependencies) {
            if (!state.dependents[depId]) {
              state.dependents[depId] = [];
            }
            if (!state.dependents[depId].includes(registration.id)) {
              state.dependents[depId].push(registration.id);
            }
          }
        }
        
        // Set as active if first module
        if (!state.activeModule) {
          state.activeModule = registration.id;
        }
      }),

      unregisterModule: (moduleId) => set((state) => {
        // Remove from all records
        delete state.registrations[moduleId];
        delete state.statuses[moduleId];
        delete state.configs[moduleId];
        delete state.contexts[moduleId];
        delete state.capabilities[moduleId];
        
        // Remove dependencies
        const dependencies = state.dependencies[moduleId] || [];
        for (const depId of dependencies) {
          if (state.dependents[depId]) {
            state.dependents[depId] = state.dependents[depId].filter(id => id !== moduleId);
          }
        }
        delete state.dependencies[moduleId];
        delete state.dependents[moduleId];
        
        // Update active module if needed
        if (state.activeModule === moduleId) {
          const remainingModules = Object.keys(state.registrations);
          state.activeModule = remainingModules.length > 0 ? remainingModules[0] : null;
        }
        
        // Remove related messages
        state.messages = state.messages.filter(msg => msg.from !== moduleId && msg.to !== moduleId);
        
        // Remove related errors
        state.errors = state.errors.filter(error => error.moduleId !== moduleId);
      }),

      updateRegistration: (moduleId, updates) => set((state) => {
        if (state.registrations[moduleId]) {
          Object.assign(state.registrations[moduleId], updates);
        }
      }),

      // Status actions
      updateModuleStatus: (moduleId, status) => set((state) => {
        state.statuses[moduleId] = status;
      }),

      setModuleHealth: (moduleId, healthy) => set((state) => {
        if (state.statuses[moduleId]) {
          state.statuses[moduleId].health = healthy;
          state.statuses[moduleId].lastUpdate = new Date();
        }
      }),

      // Configuration actions
      updateModuleConfig: (moduleId, config) => set((state) => {
        state.configs[moduleId] = config;
      }),

      updateModuleContext: (moduleId, context) => set((state) => {
        state.contexts[moduleId] = context;
      }),

      // Active module actions
      setActiveModule: (moduleId) => set((state) => {
        if (state.registrations[moduleId]) {
          state.activeModule = moduleId;
        }
      }),

      // Communication actions
      sendMessage: (from, to, type, payload) => {
        const messageId = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        set((state) => {
          state.messages.push({
            id: messageId,
            from,
            to,
            type,
            payload,
            timestamp: new Date(),
            status: 'pending'
          });
          
          // Keep only last 1000 messages
          if (state.messages.length > 1000) {
            state.messages = state.messages.slice(-1000);
          }
        });
        
        return messageId;
      },

      updateMessageStatus: (messageId, status) => set((state) => {
        const message = state.messages.find(msg => msg.id === messageId);
        if (message) {
          message.status = status;
        }
      }),

      clearMessages: (olderThan) => set((state) => {
        if (olderThan) {
          state.messages = state.messages.filter(msg => msg.timestamp > olderThan);
        } else {
          state.messages = [];
        }
      }),

      // Dependency actions
      addDependency: (moduleId, dependencyId) => set((state) => {
        if (!state.dependencies[moduleId]) {
          state.dependencies[moduleId] = [];
        }
        if (!state.dependencies[moduleId].includes(dependencyId)) {
          state.dependencies[moduleId].push(dependencyId);
        }
        
        // Update dependents
        if (!state.dependents[dependencyId]) {
          state.dependents[dependencyId] = [];
        }
        if (!state.dependents[dependencyId].includes(moduleId)) {
          state.dependents[dependencyId].push(moduleId);
        }
      }),

      removeDependency: (moduleId, dependencyId) => set((state) => {
        if (state.dependencies[moduleId]) {
          state.dependencies[moduleId] = state.dependencies[moduleId].filter(id => id !== dependencyId);
        }
        
        if (state.dependents[dependencyId]) {
          state.dependents[dependencyId] = state.dependents[dependencyId].filter(id => id !== moduleId);
        }
      }),

      updateDependencies: (moduleId, dependencies) => set((state) => {
        // Remove old dependencies
        const oldDeps = state.dependencies[moduleId] || [];
        for (const depId of oldDeps) {
          if (state.dependents[depId]) {
            state.dependents[depId] = state.dependents[depId].filter(id => id !== moduleId);
          }
        }
        
        // Set new dependencies
        state.dependencies[moduleId] = dependencies;
        
        // Update dependents
        for (const depId of dependencies) {
          if (!state.dependents[depId]) {
            state.dependents[depId] = [];
          }
          if (!state.dependents[depId].includes(moduleId)) {
            state.dependents[depId].push(moduleId);
          }
        }
      }),

      // Capability actions
      updateCapabilities: (moduleId, capabilities) => set((state) => {
        state.capabilities[moduleId] = capabilities;
      }),

      // Loading actions
      setLoading: (key, loading) => set((state) => {
        state.loading[key] = loading;
      }),

      // Error actions
      addError: (error) => set((state) => {
        const newError = {
          ...error,
          id: `module-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date()
        };
        state.errors.push(newError);
        
        // Keep only last 100 errors
        if (state.errors.length > 100) {
          state.errors = state.errors.slice(-100);
        }
      }),

      removeError: (errorId) => set((state) => {
        state.errors = state.errors.filter(error => error.id !== errorId);
      }),

      clearErrors: (moduleId) => set((state) => {
        if (moduleId) {
          state.errors = state.errors.filter(error => error.moduleId !== moduleId);
        } else {
          state.errors = [];
        }
      }),

      // Utility actions
      reset: () => set(() => ({ ...initialState })),

      getModuleDependencyTree: (moduleId) => {
        const state = get();
        const visited = new Set<string>();
        const tree: string[] = [];
        
        const traverse = (id: string) => {
          if (visited.has(id)) return;
          visited.add(id);
          
          const deps = state.dependencies[id] || [];
          for (const depId of deps) {
            traverse(depId);
            tree.push(depId);
          }
        };
        
        traverse(moduleId);
        return [...new Set(tree)]; // Remove duplicates
      },

      canUnregisterModule: (moduleId) => {
        const state = get();
        const dependents = state.dependents[moduleId] || [];
        return dependents.length === 0;
      }
    }))
  )
);

// Selectors for common module state access patterns
export const moduleSelectors = {
  getActiveModule: (state: ModuleStore) =>
    state.activeModule ? state.registrations[state.activeModule] : null,
  
  getHealthyModules: (state: ModuleStore) =>
    Object.values(state.statuses).filter(status => status.health),
  
  getModulesByCapability: (state: ModuleStore, capability: string) =>
    Object.entries(state.capabilities)
      .filter(([, caps]) => caps.includes(capability))
      .map(([moduleId]) => state.registrations[moduleId])
      .filter(Boolean),
  
  getModuleMessages: (state: ModuleStore, moduleId: string) =>
    state.messages.filter(msg => msg.from === moduleId || msg.to === moduleId),
  
  getPendingMessages: (state: ModuleStore) =>
    state.messages.filter(msg => msg.status === 'pending'),
  
  getModuleErrors: (state: ModuleStore, moduleId: string) =>
    state.errors.filter(error => error.moduleId === moduleId),
  
  getCriticalErrors: (state: ModuleStore) =>
    state.errors.filter(error => error.severity === 'critical'),
  
  getModuleDependencies: (state: ModuleStore, moduleId: string) =>
    state.dependencies[moduleId] || [],
  
  getModuleDependents: (state: ModuleStore, moduleId: string) =>
    state.dependents[moduleId] || []
};
