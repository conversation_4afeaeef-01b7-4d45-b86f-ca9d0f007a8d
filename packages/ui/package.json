{"name": "@unified-assistant/ui", "version": "0.1.0", "description": "Shared UI components for the unified AI assistant platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@unified-assistant/types": "workspace:*", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-button": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5", "eslint": "^8", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.8.1", "jest": "^29", "@storybook/react": "^7", "@storybook/react-vite": "^7", "storybook": "^7"}, "peerDependencies": {"react": "^18", "react-dom": "^18", "tailwindcss": "^3.4.1"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./components": {"types": "./dist/components/index.d.ts", "import": "./dist/components/index.js", "require": "./dist/components/index.js"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "import": "./dist/hooks/index.js", "require": "./dist/hooks/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.js", "require": "./dist/utils/index.js"}}, "files": ["dist", "README.md"]}