import React, { useState, useRef, useEffect } from 'react';
import { 
  AIRequest, 
  AIResponse, 
  AIMessage, 
  AIContext,
  ModuleContext 
} from '@unified-assistant/types';
import { Button } from '../button';
import { Input } from '../input';
import { Avatar } from '../avatar';
import { Badge } from '../badge';
import { Card } from '../card';
import { cn } from '../../utils/cn';
import { MessageSquare, Send, Bot, User, Loader2 } from 'lucide-react';

export interface AIChatProps {
  moduleContext?: ModuleContext;
  onMessage?: (message: string, context?: AIContext) => Promise<AIResponse>;
  onProviderChange?: (providerId: string) => void;
  className?: string;
  variant?: 'full' | 'compact' | 'sidebar';
  placeholder?: string;
  disabled?: boolean;
  showProviderSelector?: boolean;
  showMetrics?: boolean;
}

export function AIChat({
  moduleContext,
  onMessage,
  onProviderChange,
  className,
  variant = 'full',
  placeholder = 'Ask me anything...',
  disabled = false,
  showProviderSelector = true,
  showMetrics = false
}: AIChatProps) {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentProvider, setCurrentProvider] = useState('openai');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading || !onMessage) return;

    const userMessage: AIMessage = {
      role: 'user',
      content: input
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const context: AIContext = {
        conversationId: 'chat-' + Date.now(),
        moduleId: moduleContext?.moduleId,
        history: messages,
        preferences: {
          provider: currentProvider
        }
      };

      const response = await onMessage(input, context);
      
      const assistantMessage: AIMessage = {
        role: 'assistant',
        content: response.content
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      const errorMessage: AIMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error processing your request.'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const renderMessage = (message: AIMessage, index: number) => (
    <div
      key={index}
      className={cn(
        'flex gap-3 p-4',
        message.role === 'user' ? 'justify-end' : 'justify-start'
      )}
    >
      {message.role === 'assistant' && (
        <Avatar className="h-8 w-8">
          <Bot className="h-4 w-4" />
        </Avatar>
      )}
      
      <div
        className={cn(
          'max-w-[80%] rounded-lg px-4 py-2',
          message.role === 'user'
            ? 'bg-primary text-primary-foreground ml-auto'
            : 'bg-muted'
        )}
      >
        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
      </div>

      {message.role === 'user' && (
        <Avatar className="h-8 w-8">
          <User className="h-4 w-4" />
        </Avatar>
      )}
    </div>
  );

  return (
    <Card className={cn(
      'flex flex-col',
      variant === 'full' && 'h-[600px]',
      variant === 'compact' && 'h-[400px]',
      variant === 'sidebar' && 'h-full',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          <span className="font-semibold">AI Assistant</span>
          {moduleContext && (
            <Badge variant="secondary">{moduleContext.moduleId}</Badge>
          )}
        </div>
        
        {showProviderSelector && (
          <select
            value={currentProvider}
            onChange={(e) => {
              setCurrentProvider(e.target.value);
              onProviderChange?.(e.target.value);
            }}
            className="text-sm border rounded px-2 py-1"
          >
            <option value="openai">OpenAI</option>
            <option value="anthropic">Anthropic</option>
            <option value="google">Google</option>
          </select>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Start a conversation with the AI assistant</p>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {messages.map(renderMessage)}
            {isLoading && (
              <div className="flex gap-3 p-4">
                <Avatar className="h-8 w-8">
                  <Bot className="h-4 w-4" />
                </Avatar>
                <div className="bg-muted rounded-lg px-4 py-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t">
        <div className="flex gap-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled || isLoading}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={disabled || isLoading || !input.trim()}
            size="icon"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Metrics */}
      {showMetrics && (
        <div className="px-4 py-2 border-t bg-muted/50 text-xs text-muted-foreground">
          Provider: {currentProvider} | Messages: {messages.length}
        </div>
      )}
    </Card>
  );
}
