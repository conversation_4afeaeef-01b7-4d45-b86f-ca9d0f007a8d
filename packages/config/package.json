{"name": "@unified-assistant/config", "version": "0.1.0", "description": "Configuration management for the unified AI assistant platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"@unified-assistant/types": "workspace:*", "dotenv": "^16.4.7", "joi": "^17.13.3"}, "devDependencies": {"@types/node": "^22.10.2", "typescript": "^5.7.2", "jest": "^29.7.0"}, "files": ["dist", "README.md"]}