/**
 * Configuration merging utilities
 */

export type MergeStrategy = 'replace' | 'merge' | 'append' | 'prepend';

export interface MergeOptions {
  strategy?: MergeStrategy;
  arrayMergeStrategy?: 'replace' | 'concat' | 'unique';
  customMergers?: Record<string, (target: any, source: any) => any>;
  skipKeys?: string[];
  onlyKeys?: string[];
}

export class MergeUtils {
  /**
   * Deep merge multiple objects with configurable strategies
   */
  static deepMerge(target: any, ...sources: any[]): any;
  static deepMerge(target: any, source: any, options?: MergeOptions): any;
  static deepMerge(target: any, sourceOrOptions?: any, options?: MergeOptions): any {
    if (arguments.length === 2 && typeof sourceOrOptions === 'object' && !Array.isArray(sourceOrOptions)) {
      // Check if second argument is options (no source provided)
      const hasSourceProperties = Object.keys(sourceOrOptions).some(key => 
        !['strategy', 'arrayMergeStrategy', 'customMergers', 'skipKeys', 'onlyKeys'].includes(key)
      );
      
      if (!hasSourceProperties) {
        return target; // Only options provided, no source
      }
    }

    const mergeOptions: MergeOptions = {
      strategy: 'merge',
      arrayMergeStrategy: 'replace',
      customMergers: {},
      skipKeys: [],
      onlyKeys: [],
      ...options
    };

    if (arguments.length > 3 || (arguments.length === 3 && options)) {
      // Multiple sources
      const sources = Array.from(arguments).slice(1, options ? -1 : undefined);
      return sources.reduce((acc, source) => this.mergeTwo(acc, source, mergeOptions), target);
    } else {
      // Single source
      return this.mergeTwo(target, sourceOrOptions, mergeOptions);
    }
  }

  /**
   * Merge two objects
   */
  private static mergeTwo(target: any, source: any, options: MergeOptions): any {
    if (source === null || source === undefined) {
      return target;
    }

    if (target === null || target === undefined) {
      return this.deepClone(source);
    }

    // Handle primitive types
    if (typeof source !== 'object' || Array.isArray(source)) {
      return options.strategy === 'replace' ? source : target;
    }

    const result = this.deepClone(target);

    for (const key in source) {
      if (!source.hasOwnProperty(key)) continue;

      // Skip keys if specified
      if (options.skipKeys && options.skipKeys.includes(key)) {
        continue;
      }

      // Only include specific keys if specified
      if (options.onlyKeys && options.onlyKeys.length > 0 && !options.onlyKeys.includes(key)) {
        continue;
      }

      // Use custom merger if available
      if (options.customMergers && options.customMergers[key]) {
        result[key] = options.customMergers[key](result[key], source[key]);
        continue;
      }

      const targetValue = result[key];
      const sourceValue = source[key];

      if (Array.isArray(sourceValue)) {
        result[key] = this.mergeArrays(targetValue, sourceValue, options.arrayMergeStrategy!);
      } else if (typeof sourceValue === 'object' && sourceValue !== null) {
        if (typeof targetValue === 'object' && targetValue !== null && !Array.isArray(targetValue)) {
          result[key] = this.mergeTwo(targetValue, sourceValue, options);
        } else {
          result[key] = this.deepClone(sourceValue);
        }
      } else {
        result[key] = sourceValue;
      }
    }

    return result;
  }

  /**
   * Merge arrays based on strategy
   */
  private static mergeArrays(target: any, source: any[], strategy: string): any[] {
    if (!Array.isArray(target)) {
      return [...source];
    }

    switch (strategy) {
      case 'replace':
        return [...source];
      
      case 'concat':
        return [...target, ...source];
      
      case 'unique':
        const combined = [...target, ...source];
        return Array.from(new Set(combined));
      
      default:
        return [...source];
    }
  }

  /**
   * Deep clone an object
   */
  private static deepClone(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepClone(item));
    }

    const cloned: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key]);
      }
    }

    return cloned;
  }

  /**
   * Merge configurations with environment-specific overrides
   */
  static mergeWithEnvironment(
    baseConfig: Record<string, any>,
    envConfig: Record<string, any>,
    environment: string
  ): Record<string, any> {
    const options: MergeOptions = {
      strategy: 'merge',
      arrayMergeStrategy: 'replace',
      customMergers: {
        // Special handling for environment-specific arrays
        features: (target, source) => {
          if (Array.isArray(source)) {
            return source;
          }
          return target;
        }
      }
    };

    return this.deepMerge(baseConfig, envConfig, options);
  }

  /**
   * Merge configurations with priority-based strategy
   */
  static mergeWithPriority(configs: Array<{ config: Record<string, any>; priority: number }>): Record<string, any> {
    // Sort by priority (lower number = higher priority)
    const sortedConfigs = configs.sort((a, b) => a.priority - b.priority);
    
    return sortedConfigs.reduce((merged, { config }) => {
      return this.deepMerge(merged, config, {
        strategy: 'merge',
        arrayMergeStrategy: 'replace'
      });
    }, {});
  }

  /**
   * Merge with conditional logic
   */
  static conditionalMerge(
    target: Record<string, any>,
    source: Record<string, any>,
    condition: (key: string, targetValue: any, sourceValue: any) => boolean
  ): Record<string, any> {
    const result = this.deepClone(target);

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (condition(key, result[key], source[key])) {
          if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
            if (typeof result[key] === 'object' && result[key] !== null && !Array.isArray(result[key])) {
              result[key] = this.conditionalMerge(result[key], source[key], condition);
            } else {
              result[key] = this.deepClone(source[key]);
            }
          } else {
            result[key] = source[key];
          }
        }
      }
    }

    return result;
  }

  /**
   * Merge with transformation
   */
  static mergeWithTransform(
    target: Record<string, any>,
    source: Record<string, any>,
    transformer: (key: string, value: any) => any
  ): Record<string, any> {
    const transformedSource: Record<string, any> = {};

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        transformedSource[key] = transformer(key, source[key]);
      }
    }

    return this.deepMerge(target, transformedSource);
  }

  /**
   * Selective merge - only merge specified paths
   */
  static selectiveMerge(
    target: Record<string, any>,
    source: Record<string, any>,
    paths: string[]
  ): Record<string, any> {
    const result = this.deepClone(target);

    for (const path of paths) {
      const sourceValue = this.getNestedValue(source, path);
      if (sourceValue !== undefined) {
        this.setNestedValue(result, path, this.deepClone(sourceValue));
      }
    }

    return result;
  }

  /**
   * Get nested value using dot notation
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Set nested value using dot notation
   */
  private static setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }

  /**
   * Diff two configurations
   */
  static diff(
    original: Record<string, any>,
    modified: Record<string, any>
  ): { added: string[]; removed: string[]; changed: string[] } {
    const originalFlat = this.flatten(original);
    const modifiedFlat = this.flatten(modified);
    
    const originalKeys = new Set(Object.keys(originalFlat));
    const modifiedKeys = new Set(Object.keys(modifiedFlat));
    
    const added = Array.from(modifiedKeys).filter(key => !originalKeys.has(key));
    const removed = Array.from(originalKeys).filter(key => !modifiedKeys.has(key));
    const changed = Array.from(originalKeys)
      .filter(key => modifiedKeys.has(key))
      .filter(key => originalFlat[key] !== modifiedFlat[key]);

    return { added, removed, changed };
  }

  /**
   * Flatten object to dot notation
   */
  private static flatten(obj: Record<string, any>, prefix: string = ''): Record<string, any> {
    const flattened: Record<string, any> = {};

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          Object.assign(flattened, this.flatten(obj[key], newKey));
        } else {
          flattened[newKey] = obj[key];
        }
      }
    }

    return flattened;
  }
}
