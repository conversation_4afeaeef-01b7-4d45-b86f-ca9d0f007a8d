/**
 * Configuration utility functions
 */

import { promises as fs } from 'fs';
import path from 'path';

export class ConfigUtils {
  /**
   * Deep clone an object
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepClone(item)) as unknown as T;
    }

    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key]);
      }
    }

    return cloned;
  }

  /**
   * Flatten nested object to dot notation
   */
  static flatten(obj: Record<string, any>, prefix: string = ''): Record<string, any> {
    const flattened: Record<string, any> = {};

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          Object.assign(flattened, this.flatten(obj[key], newKey));
        } else {
          flattened[newKey] = obj[key];
        }
      }
    }

    return flattened;
  }

  /**
   * Unflatten dot notation object to nested structure
   */
  static unflatten(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        this.setNestedValue(result, key, obj[key]);
      }
    }

    return result;
  }

  /**
   * Set nested value using dot notation
   */
  static setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }

  /**
   * Get nested value using dot notation
   */
  static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Check if nested path exists
   */
  static hasNestedPath(obj: any, path: string): boolean {
    return this.getNestedValue(obj, path) !== undefined;
  }

  /**
   * Remove nested path
   */
  static removeNestedPath(obj: any, path: string): boolean {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const parent = keys.reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
    
    if (parent && parent.hasOwnProperty(lastKey)) {
      delete parent[lastKey];
      return true;
    }
    
    return false;
  }

  /**
   * Resolve environment variables in string values
   */
  static resolveEnvironmentVariables(obj: any): any {
    if (typeof obj === 'string') {
      return obj.replace(/\$\{([^}]+)\}/g, (match, varName) => {
        const [name, defaultValue] = varName.split(':-');
        return process.env[name] || defaultValue || match;
      });
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.resolveEnvironmentVariables(item));
    }

    if (typeof obj === 'object' && obj !== null) {
      const resolved: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          resolved[key] = this.resolveEnvironmentVariables(obj[key]);
        }
      }
      return resolved;
    }

    return obj;
  }

  /**
   * Mask sensitive values in configuration
   */
  static maskSensitiveValues(obj: any, sensitiveKeys: string[] = []): any {
    const defaultSensitiveKeys = [
      'password', 'secret', 'key', 'token', 'apikey', 'api_key',
      'private_key', 'privatekey', 'auth', 'credential', 'credentials'
    ];
    
    const allSensitiveKeys = [...defaultSensitiveKeys, ...sensitiveKeys];
    
    return this.maskValues(obj, allSensitiveKeys);
  }

  /**
   * Recursively mask values based on key patterns
   */
  private static maskValues(obj: any, sensitiveKeys: string[]): any {
    if (typeof obj === 'string') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.maskValues(item, sensitiveKeys));
    }

    if (typeof obj === 'object' && obj !== null) {
      const masked: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const lowerKey = key.toLowerCase();
          const isSensitive = sensitiveKeys.some(sensitiveKey => 
            lowerKey.includes(sensitiveKey.toLowerCase())
          );
          
          if (isSensitive && typeof obj[key] === 'string') {
            masked[key] = this.maskString(obj[key]);
          } else {
            masked[key] = this.maskValues(obj[key], sensitiveKeys);
          }
        }
      }
      return masked;
    }

    return obj;
  }

  /**
   * Mask a string value
   */
  private static maskString(value: string): string {
    if (value.length <= 4) {
      return '*'.repeat(value.length);
    }
    
    const visibleChars = 2;
    const start = value.substring(0, visibleChars);
    const end = value.substring(value.length - visibleChars);
    const middle = '*'.repeat(value.length - visibleChars * 2);
    
    return `${start}${middle}${end}`;
  }

  /**
   * Validate configuration file exists and is readable
   */
  static async validateConfigFile(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath, fs.constants.R_OK);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get configuration file paths in order of precedence
   */
  static getConfigFilePaths(baseName: string = 'config'): string[] {
    const extensions = ['json', 'yaml', 'yml', 'toml'];
    const environments = ['local', process.env.NODE_ENV, 'default'].filter(Boolean);
    const paths: string[] = [];

    // Add environment-specific configs first (higher precedence)
    for (const env of environments) {
      for (const ext of extensions) {
        paths.push(path.join(process.cwd(), `${baseName}.${env}.${ext}`));
        paths.push(path.join(process.cwd(), 'config', `${baseName}.${env}.${ext}`));
      }
    }

    // Add base config files
    for (const ext of extensions) {
      paths.push(path.join(process.cwd(), `${baseName}.${ext}`));
      paths.push(path.join(process.cwd(), 'config', `${baseName}.${ext}`));
    }

    return paths;
  }

  /**
   * Find existing configuration files
   */
  static async findConfigFiles(baseName: string = 'config'): Promise<string[]> {
    const possiblePaths = this.getConfigFilePaths(baseName);
    const existingPaths: string[] = [];

    for (const filePath of possiblePaths) {
      if (await this.validateConfigFile(filePath)) {
        existingPaths.push(filePath);
      }
    }

    return existingPaths;
  }

  /**
   * Parse duration string to milliseconds
   */
  static parseDuration(duration: string): number {
    const match = duration.match(/^(\d+)([smhd])$/);
    if (!match) {
      throw new Error(`Invalid duration format: ${duration}`);
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value * 1000;
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: throw new Error(`Invalid duration unit: ${unit}`);
    }
  }

  /**
   * Format milliseconds to duration string
   */
  static formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${Math.floor(ms / 1000)}s`;
    if (ms < 3600000) return `${Math.floor(ms / 60000)}m`;
    if (ms < 86400000) return `${Math.floor(ms / 3600000)}h`;
    return `${Math.floor(ms / 86400000)}d`;
  }

  /**
   * Parse size string to bytes
   */
  static parseSize(size: string): number {
    const match = size.match(/^(\d+(?:\.\d+)?)(b|kb|mb|gb|tb)?$/i);
    if (!match) {
      throw new Error(`Invalid size format: ${size}`);
    }

    const value = parseFloat(match[1]);
    const unit = (match[2] || 'b').toLowerCase();

    switch (unit) {
      case 'b': return value;
      case 'kb': return value * 1024;
      case 'mb': return value * 1024 * 1024;
      case 'gb': return value * 1024 * 1024 * 1024;
      case 'tb': return value * 1024 * 1024 * 1024 * 1024;
      default: throw new Error(`Invalid size unit: ${unit}`);
    }
  }

  /**
   * Format bytes to size string
   */
  static formatSize(bytes: number): string {
    if (bytes < 1024) return `${bytes}b`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}kb`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)}mb`;
    if (bytes < 1024 * 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)}gb`;
    return `${(bytes / (1024 * 1024 * 1024 * 1024)).toFixed(1)}tb`;
  }
}
