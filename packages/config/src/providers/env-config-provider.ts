/**
 * Environment variables configuration provider
 */

import { ConfigProvider } from '../services/config-service';

export interface EnvConfigOptions {
  prefix?: string;
  separator?: string;
  parseNumbers?: boolean;
  parseBooleans?: boolean;
  parseArrays?: boolean;
  arrayDelimiter?: string;
  transformKeys?: (key: string) => string;
}

export class EnvConfigProvider implements ConfigProvider {
  public readonly name = 'environment';
  public readonly priority: number;
  private options: Required<EnvConfigOptions>;

  constructor(priority: number = 50, options: EnvConfigOptions = {}) {
    this.priority = priority;
    this.options = {
      prefix: '',
      separator: '_',
      parseNumbers: true,
      parseBooleans: true,
      parseArrays: true,
      arrayDelimiter: ',',
      transformKeys: (key: string) => key.toLowerCase(),
      ...options
    };
  }

  /**
   * Load configuration from environment variables
   */
  async load(): Promise<Record<string, any>> {
    const config: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(process.env)) {
      if (value === undefined) continue;
      
      // Skip if prefix is set and key doesn't start with it
      if (this.options.prefix && !key.startsWith(this.options.prefix)) {
        continue;
      }

      // Remove prefix if set
      let configKey = this.options.prefix ? 
        key.substring(this.options.prefix.length) : key;

      // Transform key
      configKey = this.options.transformKeys(configKey);

      // Convert to nested object structure
      const nestedKey = this.convertToNestedKey(configKey);
      const parsedValue = this.parseValue(value);

      this.setNestedValue(config, nestedKey, parsedValue);
    }

    return config;
  }

  /**
   * Parse environment variable value
   */
  private parseValue(value: string): any {
    // Parse arrays
    if (this.options.parseArrays && value.includes(this.options.arrayDelimiter)) {
      return value.split(this.options.arrayDelimiter).map(item => this.parseScalarValue(item.trim()));
    }

    return this.parseScalarValue(value);
  }

  /**
   * Parse scalar value (string, number, boolean)
   */
  private parseScalarValue(value: string): any {
    // Parse booleans
    if (this.options.parseBooleans) {
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true') return true;
      if (lowerValue === 'false') return false;
    }

    // Parse numbers
    if (this.options.parseNumbers) {
      const numValue = Number(value);
      if (!isNaN(numValue) && isFinite(numValue)) {
        return numValue;
      }
    }

    // Return as string
    return value;
  }

  /**
   * Convert underscore-separated key to dot notation
   */
  private convertToNestedKey(key: string): string {
    return key.replace(new RegExp(this.options.separator, 'g'), '.');
  }

  /**
   * Set nested value using dot notation
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }
}

/**
 * Specialized provider for common environment patterns
 */
export class StandardEnvProvider extends EnvConfigProvider {
  constructor() {
    super(50, {
      prefix: 'UA_', // Unified Assistant prefix
      separator: '_',
      parseNumbers: true,
      parseBooleans: true,
      parseArrays: true,
      arrayDelimiter: ',',
      transformKeys: (key: string) => key.toLowerCase()
    });
  }
}

/**
 * Provider for database configuration from environment
 */
export class DatabaseEnvProvider extends EnvConfigProvider {
  constructor() {
    super(60, {
      prefix: 'DB_',
      separator: '_',
      parseNumbers: true,
      parseBooleans: true,
      parseArrays: false,
      transformKeys: (key: string) => `database.${key.toLowerCase()}`
    });
  }
}

/**
 * Provider for AI configuration from environment
 */
export class AIEnvProvider extends EnvConfigProvider {
  constructor() {
    super(60, {
      prefix: 'AI_',
      separator: '_',
      parseNumbers: true,
      parseBooleans: true,
      parseArrays: true,
      arrayDelimiter: ',',
      transformKeys: (key: string) => `ai.${key.toLowerCase()}`
    });
  }
}

/**
 * Provider for authentication configuration from environment
 */
export class AuthEnvProvider extends EnvConfigProvider {
  constructor() {
    super(60, {
      prefix: 'AUTH_',
      separator: '_',
      parseNumbers: true,
      parseBooleans: true,
      parseArrays: true,
      arrayDelimiter: ',',
      transformKeys: (key: string) => `auth.${key.toLowerCase()}`
    });
  }
}
