/**
 * File-based configuration provider
 */

import { promises as fs } from 'fs';
import { watch } from 'fs';
import path from 'path';
import { ConfigProvider } from '../services/config-service';

export interface FileConfigOptions {
  filePath: string;
  format?: 'json' | 'yaml' | 'toml' | 'auto';
  encoding?: BufferEncoding;
  optional?: boolean;
  watchFile?: boolean;
}

export class FileConfigProvider implements ConfigProvider {
  public readonly name: string;
  public readonly priority: number;
  private options: Required<Omit<FileConfigOptions, 'optional'>> & { optional: boolean };
  private watchCallback?: (changes: Record<string, any>) => void;
  private watcher?: ReturnType<typeof watch>;

  constructor(priority: number, options: FileConfigOptions) {
    this.priority = priority;
    this.options = {
      format: 'auto',
      encoding: 'utf8',
      optional: false,
      watchFile: true,
      ...options
    };
    this.name = `file:${path.basename(options.filePath)}`;
  }

  /**
   * Load configuration from file
   */
  async load(): Promise<Record<string, any>> {
    try {
      const content = await fs.readFile(this.options.filePath, this.options.encoding);
      const format = this.options.format === 'auto' ? 
        this.detectFormat(this.options.filePath) : this.options.format;
      
      return this.parseContent(content, format);
    } catch (error) {
      if (this.options.optional && (error as any).code === 'ENOENT') {
        return {};
      }
      throw new Error(`Failed to load config file ${this.options.filePath}: ${error.message}`);
    }
  }

  /**
   * Watch file for changes
   */
  watch(callback: (changes: Record<string, any>) => void): void {
    if (!this.options.watchFile) return;

    this.watchCallback = callback;
    
    try {
      this.watcher = watch(this.options.filePath, async (eventType) => {
        if (eventType === 'change') {
          try {
            const newConfig = await this.load();
            callback(newConfig);
          } catch (error) {
            console.error(`Error reloading config file ${this.options.filePath}:`, error);
          }
        }
      });
    } catch (error) {
      console.error(`Failed to watch config file ${this.options.filePath}:`, error);
    }
  }

  /**
   * Stop watching file
   */
  unwatch(): void {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = undefined;
    }
    this.watchCallback = undefined;
  }

  /**
   * Detect file format from extension
   */
  private detectFormat(filePath: string): 'json' | 'yaml' | 'toml' {
    const ext = path.extname(filePath).toLowerCase();
    
    switch (ext) {
      case '.json':
        return 'json';
      case '.yaml':
      case '.yml':
        return 'yaml';
      case '.toml':
        return 'toml';
      default:
        return 'json'; // Default to JSON
    }
  }

  /**
   * Parse file content based on format
   */
  private parseContent(content: string, format: 'json' | 'yaml' | 'toml'): Record<string, any> {
    try {
      switch (format) {
        case 'json':
          return JSON.parse(content);
        
        case 'yaml':
          return this.parseYAML(content);
        
        case 'toml':
          return this.parseTOML(content);
        
        default:
          throw new Error(`Unsupported format: ${format}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse ${format} content: ${error.message}`);
    }
  }

  /**
   * Parse YAML content (basic implementation)
   */
  private parseYAML(content: string): Record<string, any> {
    // This is a basic YAML parser - in production, use a library like 'yaml'
    try {
      // Try to use yaml library if available
      const yaml = require('yaml');
      return yaml.parse(content);
    } catch {
      // Fallback to basic parsing for simple YAML
      return this.parseSimpleYAML(content);
    }
  }

  /**
   * Simple YAML parser for basic key-value pairs
   */
  private parseSimpleYAML(content: string): Record<string, any> {
    const result: Record<string, any> = {};
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;
      
      const colonIndex = trimmed.indexOf(':');
      if (colonIndex === -1) continue;
      
      const key = trimmed.substring(0, colonIndex).trim();
      const value = trimmed.substring(colonIndex + 1).trim();
      
      result[key] = this.parseYAMLValue(value);
    }
    
    return result;
  }

  /**
   * Parse YAML value
   */
  private parseYAMLValue(value: string): any {
    // Remove quotes
    if ((value.startsWith('"') && value.endsWith('"')) ||
        (value.startsWith("'") && value.endsWith("'"))) {
      return value.slice(1, -1);
    }
    
    // Parse booleans
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value === 'null') return null;
    
    // Parse numbers
    const numValue = Number(value);
    if (!isNaN(numValue) && isFinite(numValue)) {
      return numValue;
    }
    
    return value;
  }

  /**
   * Parse TOML content (basic implementation)
   */
  private parseTOML(content: string): Record<string, any> {
    // This is a basic TOML parser - in production, use a library like '@iarna/toml'
    try {
      // Try to use toml library if available
      const toml = require('@iarna/toml');
      return toml.parse(content);
    } catch {
      // Fallback to basic parsing
      return this.parseSimpleTOML(content);
    }
  }

  /**
   * Simple TOML parser for basic key-value pairs
   */
  private parseSimpleTOML(content: string): Record<string, any> {
    const result: Record<string, any> = {};
    const lines = content.split('\n');
    let currentSection = '';
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;
      
      // Section headers
      if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
        currentSection = trimmed.slice(1, -1);
        if (!result[currentSection]) {
          result[currentSection] = {};
        }
        continue;
      }
      
      // Key-value pairs
      const equalIndex = trimmed.indexOf('=');
      if (equalIndex === -1) continue;
      
      const key = trimmed.substring(0, equalIndex).trim();
      const value = trimmed.substring(equalIndex + 1).trim();
      
      const target = currentSection ? result[currentSection] : result;
      target[key] = this.parseTOMLValue(value);
    }
    
    return result;
  }

  /**
   * Parse TOML value
   */
  private parseTOMLValue(value: string): any {
    // Remove quotes
    if ((value.startsWith('"') && value.endsWith('"')) ||
        (value.startsWith("'") && value.endsWith("'"))) {
      return value.slice(1, -1);
    }
    
    // Parse booleans
    if (value === 'true') return true;
    if (value === 'false') return false;
    
    // Parse numbers
    const numValue = Number(value);
    if (!isNaN(numValue) && isFinite(numValue)) {
      return numValue;
    }
    
    return value;
  }
}

/**
 * JSON configuration file provider
 */
export class JSONConfigProvider extends FileConfigProvider {
  constructor(priority: number, filePath: string, optional: boolean = false) {
    super(priority, {
      filePath,
      format: 'json',
      optional,
      watchFile: true
    });
  }
}

/**
 * YAML configuration file provider
 */
export class YAMLConfigProvider extends FileConfigProvider {
  constructor(priority: number, filePath: string, optional: boolean = false) {
    super(priority, {
      filePath,
      format: 'yaml',
      optional,
      watchFile: true
    });
  }
}
