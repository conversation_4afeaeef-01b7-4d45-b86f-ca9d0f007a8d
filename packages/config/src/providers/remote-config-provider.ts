/**
 * Remote configuration provider for fetching config from external sources
 */

import { ConfigProvider } from '../services/config-service';

export interface RemoteConfigOptions {
  url: string;
  method?: 'GET' | 'POST';
  headers?: Record<string, string>;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  pollInterval?: number;
  authToken?: string;
  validateResponse?: (data: any) => boolean;
}

export class RemoteConfigProvider implements ConfigProvider {
  public readonly name: string;
  public readonly priority: number;
  private options: Required<Omit<RemoteConfigOptions, 'authToken' | 'validateResponse'>> & 
    Pick<RemoteConfigOptions, 'authToken' | 'validateResponse'>;
  private pollTimer?: NodeJS.Timeout;
  private watchCallback?: (changes: Record<string, any>) => void;

  constructor(priority: number, options: RemoteConfigOptions) {
    this.priority = priority;
    this.options = {
      method: 'GET',
      headers: {},
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000,
      pollInterval: 60000, // 1 minute
      ...options
    };
    this.name = `remote:${new URL(options.url).hostname}`;
  }

  /**
   * Load configuration from remote source
   */
  async load(): Promise<Record<string, any>> {
    return await this.fetchWithRetry();
  }

  /**
   * Watch for configuration changes by polling
   */
  watch(callback: (changes: Record<string, any>) => void): void {
    this.watchCallback = callback;
    
    if (this.options.pollInterval > 0) {
      this.pollTimer = setInterval(async () => {
        try {
          const newConfig = await this.load();
          callback(newConfig);
        } catch (error) {
          console.error(`Error polling remote config from ${this.options.url}:`, error);
        }
      }, this.options.pollInterval);
    }
  }

  /**
   * Stop watching for changes
   */
  unwatch(): void {
    if (this.pollTimer) {
      clearInterval(this.pollTimer);
      this.pollTimer = undefined;
    }
    this.watchCallback = undefined;
  }

  /**
   * Fetch configuration with retry logic
   */
  private async fetchWithRetry(): Promise<Record<string, any>> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.options.retryAttempts; attempt++) {
      try {
        return await this.fetchConfig();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.options.retryAttempts) {
          await this.delay(this.options.retryDelay * attempt);
        }
      }
    }

    throw new Error(`Failed to fetch remote config after ${this.options.retryAttempts} attempts: ${lastError?.message}`);
  }

  /**
   * Fetch configuration from remote source
   */
  private async fetchConfig(): Promise<Record<string, any>> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.options.timeout);

    try {
      const headers = { ...this.options.headers };
      
      // Add authorization header if token is provided
      if (this.options.authToken) {
        headers['Authorization'] = `Bearer ${this.options.authToken}`;
      }

      const response = await fetch(this.options.url, {
        method: this.options.method,
        headers,
        signal: controller.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Validate response if validator is provided
      if (this.options.validateResponse && !this.options.validateResponse(data)) {
        throw new Error('Remote configuration validation failed');
      }

      return data;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout after ${this.options.timeout}ms`);
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * Delay helper for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Consul configuration provider
 */
export class ConsulConfigProvider extends RemoteConfigProvider {
  constructor(priority: number, consulUrl: string, keyPath: string, token?: string) {
    super(priority, {
      url: `${consulUrl}/v1/kv/${keyPath}?raw`,
      headers: {
        'Accept': 'application/json'
      },
      authToken: token,
      pollInterval: 30000, // 30 seconds
      validateResponse: (data) => typeof data === 'object' && data !== null
    });
  }
}

/**
 * etcd configuration provider
 */
export class EtcdConfigProvider extends RemoteConfigProvider {
  constructor(priority: number, etcdUrl: string, keyPath: string) {
    super(priority, {
      url: `${etcdUrl}/v2/keys/${keyPath}`,
      headers: {
        'Accept': 'application/json'
      },
      pollInterval: 30000, // 30 seconds
      validateResponse: (data) => data && data.node && data.node.value
    });
  }

  /**
   * Override load to handle etcd response format
   */
  async load(): Promise<Record<string, any>> {
    const response = await this.fetchWithRetry();
    
    // etcd returns data in a specific format
    if (response.node && response.node.value) {
      try {
        return JSON.parse(response.node.value);
      } catch {
        // If not JSON, return as string value
        return { value: response.node.value };
      }
    }
    
    return {};
  }

  private async fetchWithRetry(): Promise<any> {
    // Use parent's fetchWithRetry method
    return super['fetchWithRetry']();
  }
}

/**
 * AWS Parameter Store configuration provider
 */
export class ParameterStoreConfigProvider extends RemoteConfigProvider {
  constructor(priority: number, region: string, parameterPath: string, accessKeyId?: string, secretAccessKey?: string) {
    const url = `https://ssm.${region}.amazonaws.com/`;
    
    super(priority, {
      url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-amz-json-1.1',
        'X-Amz-Target': 'AWSSimpleSystemsManagement.GetParametersByPath'
      },
      pollInterval: 300000, // 5 minutes
      validateResponse: (data) => data && data.Parameters
    });
  }

  /**
   * Override load to handle AWS Parameter Store API
   */
  async load(): Promise<Record<string, any>> {
    // This would need proper AWS SDK integration in production
    // For now, return empty config
    console.warn('AWS Parameter Store provider requires AWS SDK integration');
    return {};
  }
}
