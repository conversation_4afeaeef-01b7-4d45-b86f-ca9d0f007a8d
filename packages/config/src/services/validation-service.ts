/**
 * Configuration validation service
 */

export interface ValidationRule {
  path: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: any[];
  custom?: (value: any) => boolean | string;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  path: string;
  message: string;
  value?: any;
}

export class ValidationService {
  private rules: Map<string, ValidationRule[]> = new Map();

  /**
   * Add validation rules for a configuration namespace
   */
  addRules(namespace: string, rules: ValidationRule[]): void {
    this.rules.set(namespace, rules);
  }

  /**
   * Validate configuration object
   */
  validate(config: Record<string, any>, namespace?: string): ValidationResult {
    const errors: ValidationError[] = [];
    
    if (namespace) {
      const rules = this.rules.get(namespace);
      if (rules) {
        this.validateWithRules(config, rules, errors);
      }
    } else {
      // Validate against all rules
      for (const [ns, rules] of this.rules.entries()) {
        this.validateWithRules(config, rules, errors, ns);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate specific configuration path
   */
  validatePath(config: Record<string, any>, path: string): ValidationResult {
    const errors: ValidationError[] = [];
    const value = this.getNestedValue(config, path);
    
    // Find rules that match this path
    for (const rules of this.rules.values()) {
      const matchingRule = rules.find(rule => rule.path === path);
      if (matchingRule) {
        this.validateValue(value, matchingRule, errors);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate with specific rules
   */
  private validateWithRules(
    config: Record<string, any>, 
    rules: ValidationRule[], 
    errors: ValidationError[],
    namespace?: string
  ): void {
    for (const rule of rules) {
      const fullPath = namespace ? `${namespace}.${rule.path}` : rule.path;
      const value = this.getNestedValue(config, fullPath);
      
      this.validateValue(value, { ...rule, path: fullPath }, errors);
    }
  }

  /**
   * Validate a single value against a rule
   */
  private validateValue(value: any, rule: ValidationRule, errors: ValidationError[]): void {
    // Check if required
    if (rule.required && (value === undefined || value === null)) {
      errors.push({
        path: rule.path,
        message: `Required field '${rule.path}' is missing`,
        value
      });
      return;
    }

    // Skip validation if value is undefined/null and not required
    if (value === undefined || value === null) {
      return;
    }

    // Type validation
    if (!this.validateType(value, rule.type)) {
      errors.push({
        path: rule.path,
        message: `Field '${rule.path}' must be of type ${rule.type}`,
        value
      });
      return;
    }

    // Min/Max validation
    if (rule.min !== undefined || rule.max !== undefined) {
      this.validateRange(value, rule, errors);
    }

    // Pattern validation
    if (rule.pattern && typeof value === 'string') {
      if (!rule.pattern.test(value)) {
        errors.push({
          path: rule.path,
          message: `Field '${rule.path}' does not match required pattern`,
          value
        });
      }
    }

    // Enum validation
    if (rule.enum && !rule.enum.includes(value)) {
      errors.push({
        path: rule.path,
        message: `Field '${rule.path}' must be one of: ${rule.enum.join(', ')}`,
        value
      });
    }

    // Custom validation
    if (rule.custom) {
      const result = rule.custom(value);
      if (result !== true) {
        errors.push({
          path: rule.path,
          message: typeof result === 'string' ? result : `Custom validation failed for '${rule.path}'`,
          value
        });
      }
    }
  }

  /**
   * Validate type
   */
  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * Validate range (min/max)
   */
  private validateRange(value: any, rule: ValidationRule, errors: ValidationError[]): void {
    let length: number;

    if (typeof value === 'string' || Array.isArray(value)) {
      length = value.length;
    } else if (typeof value === 'number') {
      length = value;
    } else {
      return; // Can't validate range for other types
    }

    if (rule.min !== undefined && length < rule.min) {
      const type = typeof value === 'number' ? 'value' : 'length';
      errors.push({
        path: rule.path,
        message: `Field '${rule.path}' ${type} must be at least ${rule.min}`,
        value
      });
    }

    if (rule.max !== undefined && length > rule.max) {
      const type = typeof value === 'number' ? 'value' : 'length';
      errors.push({
        path: rule.path,
        message: `Field '${rule.path}' ${type} must be at most ${rule.max}`,
        value
      });
    }
  }

  /**
   * Get nested value using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get all validation rules
   */
  getRules(): Map<string, ValidationRule[]> {
    return new Map(this.rules);
  }

  /**
   * Remove rules for namespace
   */
  removeRules(namespace: string): void {
    this.rules.delete(namespace);
  }

  /**
   * Clear all rules
   */
  clearRules(): void {
    this.rules.clear();
  }

  /**
   * Load default validation rules for common configurations
   */
  loadDefaultRules(): void {
    // Database configuration rules
    this.addRules('database', [
      { path: 'host', type: 'string', required: true },
      { path: 'port', type: 'number', required: true, min: 1, max: 65535 },
      { path: 'database', type: 'string', required: true, min: 1 },
      { path: 'username', type: 'string', required: true },
      { path: 'password', type: 'string', required: true },
      { path: 'ssl', type: 'boolean' },
      { path: 'connectionTimeout', type: 'number', min: 1000 },
      { path: 'maxConnections', type: 'number', min: 1, max: 1000 }
    ]);

    // Authentication configuration rules
    this.addRules('auth', [
      { path: 'jwtSecret', type: 'string', required: true, min: 32 },
      { path: 'jwtExpiresIn', type: 'string', required: true },
      { path: 'refreshTokenExpiresIn', type: 'string', required: true },
      { path: 'maxLoginAttempts', type: 'number', min: 1, max: 10 },
      { path: 'lockoutDuration', type: 'number', min: 60000 }, // 1 minute minimum
      { path: 'enableTwoFactor', type: 'boolean' }
    ]);

    // AI configuration rules
    this.addRules('ai', [
      { path: 'defaultProvider', type: 'string', required: true, enum: ['openai', 'anthropic', 'google'] },
      { path: 'openai.apiKey', type: 'string' },
      { path: 'anthropic.apiKey', type: 'string' },
      { path: 'google.apiKey', type: 'string' },
      { path: 'maxTokens', type: 'number', min: 1, max: 100000 },
      { path: 'temperature', type: 'number', min: 0, max: 2 },
      { path: 'timeout', type: 'number', min: 1000 }
    ]);

    // Server configuration rules
    this.addRules('server', [
      { path: 'port', type: 'number', required: true, min: 1, max: 65535 },
      { path: 'host', type: 'string', required: true },
      { path: 'cors.enabled', type: 'boolean' },
      { path: 'cors.origins', type: 'array' },
      { path: 'rateLimit.windowMs', type: 'number', min: 1000 },
      { path: 'rateLimit.max', type: 'number', min: 1 }
    ]);

    // Logging configuration rules
    this.addRules('logging', [
      { path: 'level', type: 'string', required: true, enum: ['error', 'warn', 'info', 'debug', 'trace'] },
      { path: 'format', type: 'string', enum: ['json', 'text'] },
      { path: 'file.enabled', type: 'boolean' },
      { path: 'file.path', type: 'string' },
      { path: 'file.maxSize', type: 'string' },
      { path: 'console.enabled', type: 'boolean' }
    ]);
  }
}
