/**
 * Environment management service
 */

export type Environment = 'development' | 'staging' | 'production' | 'test';

export interface EnvironmentConfig {
  name: Environment;
  debug: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  features: Record<string, boolean>;
  limits: {
    maxRequestSize: number;
    maxConcurrentRequests: number;
    rateLimitPerMinute: number;
  };
  monitoring: {
    enabled: boolean;
    sampleRate: number;
    errorReporting: boolean;
  };
}

export class EnvironmentService {
  private currentEnvironment: Environment;
  private environmentConfigs: Map<Environment, EnvironmentConfig> = new Map();

  constructor(environment?: Environment) {
    this.currentEnvironment = environment || this.detectEnvironment();
    this.initializeDefaultConfigs();
  }

  /**
   * Get current environment
   */
  getEnvironment(): Environment {
    return this.currentEnvironment;
  }

  /**
   * Set current environment
   */
  setEnvironment(environment: Environment): void {
    this.currentEnvironment = environment;
  }

  /**
   * Check if running in development
   */
  isDevelopment(): boolean {
    return this.currentEnvironment === 'development';
  }

  /**
   * Check if running in production
   */
  isProduction(): boolean {
    return this.currentEnvironment === 'production';
  }

  /**
   * Check if running in test
   */
  isTest(): boolean {
    return this.currentEnvironment === 'test';
  }

  /**
   * Check if running in staging
   */
  isStaging(): boolean {
    return this.currentEnvironment === 'staging';
  }

  /**
   * Get environment configuration
   */
  getConfig(environment?: Environment): EnvironmentConfig {
    const env = environment || this.currentEnvironment;
    const config = this.environmentConfigs.get(env);
    
    if (!config) {
      throw new Error(`No configuration found for environment: ${env}`);
    }
    
    return config;
  }

  /**
   * Set environment configuration
   */
  setConfig(environment: Environment, config: EnvironmentConfig): void {
    this.environmentConfigs.set(environment, config);
  }

  /**
   * Check if feature is enabled
   */
  isFeatureEnabled(feature: string, environment?: Environment): boolean {
    const config = this.getConfig(environment);
    return config.features[feature] || false;
  }

  /**
   * Enable feature for environment
   */
  enableFeature(feature: string, environment?: Environment): void {
    const env = environment || this.currentEnvironment;
    const config = this.getConfig(env);
    config.features[feature] = true;
    this.setConfig(env, config);
  }

  /**
   * Disable feature for environment
   */
  disableFeature(feature: string, environment?: Environment): void {
    const env = environment || this.currentEnvironment;
    const config = this.getConfig(env);
    config.features[feature] = false;
    this.setConfig(env, config);
  }

  /**
   * Get environment-specific limits
   */
  getLimits(environment?: Environment): EnvironmentConfig['limits'] {
    return this.getConfig(environment).limits;
  }

  /**
   * Get monitoring configuration
   */
  getMonitoringConfig(environment?: Environment): EnvironmentConfig['monitoring'] {
    return this.getConfig(environment).monitoring;
  }

  /**
   * Get log level for environment
   */
  getLogLevel(environment?: Environment): EnvironmentConfig['logLevel'] {
    return this.getConfig(environment).logLevel;
  }

  /**
   * Check if debug mode is enabled
   */
  isDebugEnabled(environment?: Environment): boolean {
    return this.getConfig(environment).debug;
  }

  /**
   * Detect environment from NODE_ENV or other indicators
   */
  private detectEnvironment(): Environment {
    const nodeEnv = process.env.NODE_ENV?.toLowerCase();
    
    switch (nodeEnv) {
      case 'production':
      case 'prod':
        return 'production';
      case 'staging':
      case 'stage':
        return 'staging';
      case 'test':
      case 'testing':
        return 'test';
      case 'development':
      case 'dev':
      default:
        return 'development';
    }
  }

  /**
   * Initialize default configurations for all environments
   */
  private initializeDefaultConfigs(): void {
    // Development configuration
    this.environmentConfigs.set('development', {
      name: 'development',
      debug: true,
      logLevel: 'debug',
      features: {
        hotReload: true,
        debugPanel: true,
        mockData: true,
        verboseLogging: true
      },
      limits: {
        maxRequestSize: 50 * 1024 * 1024, // 50MB
        maxConcurrentRequests: 100,
        rateLimitPerMinute: 1000
      },
      monitoring: {
        enabled: false,
        sampleRate: 1.0,
        errorReporting: false
      }
    });

    // Test configuration
    this.environmentConfigs.set('test', {
      name: 'test',
      debug: false,
      logLevel: 'error',
      features: {
        hotReload: false,
        debugPanel: false,
        mockData: true,
        verboseLogging: false
      },
      limits: {
        maxRequestSize: 10 * 1024 * 1024, // 10MB
        maxConcurrentRequests: 50,
        rateLimitPerMinute: 500
      },
      monitoring: {
        enabled: false,
        sampleRate: 0,
        errorReporting: false
      }
    });

    // Staging configuration
    this.environmentConfigs.set('staging', {
      name: 'staging',
      debug: false,
      logLevel: 'info',
      features: {
        hotReload: false,
        debugPanel: true,
        mockData: false,
        verboseLogging: false
      },
      limits: {
        maxRequestSize: 25 * 1024 * 1024, // 25MB
        maxConcurrentRequests: 200,
        rateLimitPerMinute: 2000
      },
      monitoring: {
        enabled: true,
        sampleRate: 0.1,
        errorReporting: true
      }
    });

    // Production configuration
    this.environmentConfigs.set('production', {
      name: 'production',
      debug: false,
      logLevel: 'warn',
      features: {
        hotReload: false,
        debugPanel: false,
        mockData: false,
        verboseLogging: false
      },
      limits: {
        maxRequestSize: 10 * 1024 * 1024, // 10MB
        maxConcurrentRequests: 500,
        rateLimitPerMinute: 5000
      },
      monitoring: {
        enabled: true,
        sampleRate: 0.01,
        errorReporting: true
      }
    });
  }

  /**
   * Validate environment configuration
   */
  validateConfig(config: EnvironmentConfig): boolean {
    try {
      // Check required fields
      if (!config.name || !config.logLevel) {
        return false;
      }

      // Validate log level
      const validLogLevels = ['error', 'warn', 'info', 'debug', 'trace'];
      if (!validLogLevels.includes(config.logLevel)) {
        return false;
      }

      // Validate limits
      if (!config.limits || 
          config.limits.maxRequestSize <= 0 ||
          config.limits.maxConcurrentRequests <= 0 ||
          config.limits.rateLimitPerMinute <= 0) {
        return false;
      }

      // Validate monitoring
      if (!config.monitoring ||
          config.monitoring.sampleRate < 0 ||
          config.monitoring.sampleRate > 1) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get all environment configurations
   */
  getAllConfigs(): Map<Environment, EnvironmentConfig> {
    return new Map(this.environmentConfigs);
  }

  /**
   * Reset to default configurations
   */
  resetToDefaults(): void {
    this.environmentConfigs.clear();
    this.initializeDefaultConfigs();
  }
}
