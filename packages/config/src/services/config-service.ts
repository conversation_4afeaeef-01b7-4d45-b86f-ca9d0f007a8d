/**
 * Core configuration service for the unified AI assistant platform
 */

import { EventEmitter } from 'events';

export interface ConfigProvider {
  name: string;
  priority: number;
  load(): Promise<Record<string, any>>;
  watch?(callback: (changes: Record<string, any>) => void): void;
  unwatch?(): void;
}

export interface ConfigServiceOptions {
  providers: ConfigProvider[];
  enableWatch?: boolean;
  validateOnLoad?: boolean;
  cacheConfig?: boolean;
  reloadInterval?: number;
}

export class ConfigService extends EventEmitter {
  private providers: ConfigProvider[] = [];
  private config: Record<string, any> = {};
  private cache: Map<string, any> = new Map();
  private options: ConfigServiceOptions;
  private reloadTimer?: NodeJS.Timeout;
  private isLoaded = false;

  constructor(options: ConfigServiceOptions) {
    super();
    this.options = {
      enableWatch: true,
      validateOnLoad: true,
      cacheConfig: true,
      ...options
    };
    
    this.providers = options.providers.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Load configuration from all providers
   */
  async load(): Promise<void> {
    try {
      const configs: Record<string, any>[] = [];

      // Load from all providers
      for (const provider of this.providers) {
        try {
          const providerConfig = await provider.load();
          configs.push(providerConfig);
          
          // Set up watching if supported and enabled
          if (this.options.enableWatch && provider.watch) {
            provider.watch((changes) => {
              this.handleConfigChange(provider.name, changes);
            });
          }
        } catch (error) {
          console.error(`Failed to load config from provider ${provider.name}:`, error);
          // Continue with other providers
        }
      }

      // Merge configurations (higher priority providers override lower priority)
      this.config = this.mergeConfigs(configs);

      // Validate configuration if enabled
      if (this.options.validateOnLoad) {
        await this.validateConfig();
      }

      // Cache configuration if enabled
      if (this.options.cacheConfig) {
        this.updateCache();
      }

      // Set up periodic reload if configured
      if (this.options.reloadInterval) {
        this.setupPeriodicReload();
      }

      this.isLoaded = true;
      this.emit('loaded', this.config);
    } catch (error) {
      this.emit('error', error);
      throw new Error(`Configuration loading failed: ${error.message}`);
    }
  }

  /**
   * Get configuration value by key
   */
  get<T = any>(key: string, defaultValue?: T): T {
    if (!this.isLoaded) {
      throw new Error('Configuration not loaded. Call load() first.');
    }

    // Check cache first if enabled
    if (this.options.cacheConfig && this.cache.has(key)) {
      return this.cache.get(key);
    }

    const value = this.getNestedValue(this.config, key);
    const result = value !== undefined ? value : defaultValue;

    // Cache the result if enabled
    if (this.options.cacheConfig && result !== undefined) {
      this.cache.set(key, result);
    }

    return result;
  }

  /**
   * Set configuration value
   */
  set(key: string, value: any): void {
    this.setNestedValue(this.config, key, value);
    
    // Update cache if enabled
    if (this.options.cacheConfig) {
      this.cache.set(key, value);
    }

    this.emit('changed', { key, value, config: this.config });
  }

  /**
   * Check if configuration key exists
   */
  has(key: string): boolean {
    return this.getNestedValue(this.config, key) !== undefined;
  }

  /**
   * Get all configuration
   */
  getAll(): Record<string, any> {
    if (!this.isLoaded) {
      throw new Error('Configuration not loaded. Call load() first.');
    }
    return { ...this.config };
  }

  /**
   * Reload configuration from all providers
   */
  async reload(): Promise<void> {
    this.isLoaded = false;
    this.cache.clear();
    await this.load();
    this.emit('reloaded', this.config);
  }

  /**
   * Get configuration for a specific namespace
   */
  getNamespace<T = Record<string, any>>(namespace: string): T {
    return this.get(namespace, {} as T);
  }

  /**
   * Merge multiple configuration objects
   */
  private mergeConfigs(configs: Record<string, any>[]): Record<string, any> {
    let merged = {};
    
    // Merge in reverse order (lower priority first)
    for (let i = configs.length - 1; i >= 0; i--) {
      merged = this.deepMerge(merged, configs[i]);
    }
    
    return merged;
  }

  /**
   * Deep merge two objects
   */
  private deepMerge(target: any, source: any): any {
    if (source === null || source === undefined) {
      return target;
    }

    if (typeof source !== 'object' || Array.isArray(source)) {
      return source;
    }

    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && !Array.isArray(source[key]) && source[key] !== null) {
          result[key] = this.deepMerge(result[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * Get nested value using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Set nested value using dot notation
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }

  /**
   * Handle configuration changes from providers
   */
  private async handleConfigChange(providerName: string, changes: Record<string, any>): Promise<void> {
    try {
      // Reload configuration to incorporate changes
      await this.reload();
      this.emit('providerChanged', { provider: providerName, changes });
    } catch (error) {
      this.emit('error', error);
    }
  }

  /**
   * Validate configuration
   */
  private async validateConfig(): Promise<void> {
    // Basic validation - can be extended with schema validation
    if (!this.config || typeof this.config !== 'object') {
      throw new Error('Invalid configuration: must be an object');
    }

    // Emit validation event for custom validators
    this.emit('validate', this.config);
  }

  /**
   * Update cache with current configuration
   */
  private updateCache(): void {
    this.cache.clear();
    // Pre-populate cache with flattened config for faster access
    this.flattenConfig(this.config, '', this.cache);
  }

  /**
   * Flatten configuration for caching
   */
  private flattenConfig(obj: any, prefix: string, cache: Map<string, any>): void {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          this.flattenConfig(obj[key], fullKey, cache);
        } else {
          cache.set(fullKey, obj[key]);
        }
      }
    }
  }

  /**
   * Setup periodic configuration reload
   */
  private setupPeriodicReload(): void {
    if (this.reloadTimer) {
      clearInterval(this.reloadTimer);
    }

    this.reloadTimer = setInterval(async () => {
      try {
        await this.reload();
      } catch (error) {
        this.emit('error', error);
      }
    }, this.options.reloadInterval);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    // Stop periodic reload
    if (this.reloadTimer) {
      clearInterval(this.reloadTimer);
      this.reloadTimer = undefined;
    }

    // Stop watching providers
    this.providers.forEach(provider => {
      if (provider.unwatch) {
        provider.unwatch();
      }
    });

    // Clear cache and config
    this.cache.clear();
    this.config = {};
    this.isLoaded = false;

    // Remove all listeners
    this.removeAllListeners();
  }
}
