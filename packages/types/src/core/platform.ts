import { z } from 'zod';

// Platform configuration schema
export const PlatformConfigSchema = z.object({
  name: z.string(),
  version: z.string(),
  environment: z.enum(['development', 'staging', 'production']),
  modules: z.array(z.string()),
  features: z.record(z.boolean()),
  ai: z.object({
    providers: z.array(z.string()),
    defaultProvider: z.string(),
    fallbackEnabled: z.boolean(),
  }),
  database: z.object({
    url: z.string(),
    maxConnections: z.number().optional(),
  }),
  auth: z.object({
    enabled: z.boolean(),
    providers: z.array(z.string()),
  }),
});

export type PlatformConfig = z.infer<typeof PlatformConfigSchema>;

// Module registration schema
export const ModuleRegistrationSchema = z.object({
  id: z.string(),
  name: z.string(),
  version: z.string(),
  description: z.string(),
  capabilities: z.array(z.string()),
  dependencies: z.array(z.string()).optional(),
  routes: z.array(z.object({
    path: z.string(),
    component: z.string(),
  })).optional(),
  api: z.object({
    endpoints: z.array(z.string()),
    baseUrl: z.string().optional(),
  }).optional(),
  events: z.object({
    emits: z.array(z.string()),
    listens: z.array(z.string()),
  }).optional(),
});

export type ModuleRegistration = z.infer<typeof ModuleRegistrationSchema>;

// Platform status
export interface PlatformStatus {
  status: 'initializing' | 'ready' | 'error' | 'maintenance';
  modules: ModuleStatus[];
  health: {
    database: boolean;
    ai: boolean;
    auth: boolean;
  };
  metrics: {
    uptime: number;
    activeUsers: number;
    requestsPerMinute: number;
  };
}

export interface ModuleStatus {
  id: string;
  name: string;
  status: 'loading' | 'ready' | 'error' | 'disabled';
  health: boolean;
  lastUpdate: Date;
  metrics?: {
    requests: number;
    errors: number;
    responseTime: number;
  };
}

// Error types
export interface PlatformError {
  code: string;
  message: string;
  module?: string;
  timestamp: Date;
  details?: Record<string, any>;
}

// Feature flags
export interface FeatureFlags {
  [key: string]: boolean | string | number;
}

// Platform events
export type PlatformEvent = 
  | { type: 'platform:initialized'; payload: PlatformConfig }
  | { type: 'platform:error'; payload: PlatformError }
  | { type: 'module:registered'; payload: ModuleRegistration }
  | { type: 'module:status_changed'; payload: ModuleStatus }
  | { type: 'feature:toggled'; payload: { feature: string; enabled: boolean } };
