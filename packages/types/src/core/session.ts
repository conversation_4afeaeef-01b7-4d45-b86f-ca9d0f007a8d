/**
 * Session and authentication session types for the unified AI assistant platform
 */

export interface Session {
  id: string;
  userId: string;
  token: string;
  refreshToken: string;
  expiresAt: Date;
  createdAt: Date;
  lastAccessedAt: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  metadata: Record<string, any>;
}

export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  tokenType: 'Bearer';
  expiresIn: number;
  scope?: string[];
}

export interface TokenPayload {
  sub: string; // user id
  email: string;
  username: string;
  role: string;
  permissions: string[];
  sessionId: string;
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
  deviceInfo?: DeviceInfo;
}

export interface LoginResponse {
  user: {
    id: string;
    email: string;
    username: string;
    displayName: string;
    role: string;
    permissions: string[];
  };
  session: AuthToken;
  requiresPasswordChange?: boolean;
  requiresTwoFactor?: boolean;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface LogoutRequest {
  sessionId?: string;
  allSessions?: boolean;
}

export interface DeviceInfo {
  deviceId: string;
  deviceName: string;
  platform: string;
  browser?: string;
  version?: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirmRequest {
  token: string;
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface TwoFactorSetupRequest {
  method: 'totp' | 'sms' | 'email';
}

export interface TwoFactorVerifyRequest {
  code: string;
  method: 'totp' | 'sms' | 'email';
}

export interface SessionQuery {
  userId?: string;
  isActive?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'lastAccessedAt';
  sortOrder?: 'asc' | 'desc';
}
