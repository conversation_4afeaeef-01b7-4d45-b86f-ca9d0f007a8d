/**
 * User and authentication related types for the unified AI assistant platform
 */

export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  avatar?: string;
  role: UserRole;
  permissions: Permission[];
  preferences: UserPreferences;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  isActive: boolean;
  isVerified: boolean;
}

export interface UserRole {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isSystem: boolean;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: NotificationPreferences;
  ai: AIPreferences;
  modules: ModulePreferences;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  inApp: boolean;
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
}

export interface AIPreferences {
  defaultProvider: string;
  temperature: number;
  maxTokens: number;
  enableContextSharing: boolean;
  enableLearning: boolean;
}

export interface ModulePreferences {
  enabledModules: string[];
  defaultModule: string;
  moduleSettings: Record<string, any>;
}

export interface CreateUserRequest {
  email: string;
  username: string;
  displayName: string;
  password: string;
  role?: string;
  preferences?: Partial<UserPreferences>;
}

export interface UpdateUserRequest {
  displayName?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
  metadata?: Record<string, any>;
}

export interface UserQuery {
  search?: string;
  role?: string;
  isActive?: boolean;
  isVerified?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'lastLoginAt' | 'displayName';
  sortOrder?: 'asc' | 'desc';
}
