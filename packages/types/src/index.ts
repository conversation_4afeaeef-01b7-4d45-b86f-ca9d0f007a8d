// Core platform types
export * from './core/platform';
export * from './core/user';
export * from './core/session';

// AI integration types
export * from './ai/providers';
export * from './ai/orchestration';
export * from './ai/context';

// Module integration types
export * from './modules/base';
export * from './modules/communication';
export * from './modules/state';

// Event system types
export * from './events/base';
export * from './events/module-events';
export * from './events/ai-events';

// Utility types
export * from './utils/common';
export * from './utils/validation';
