import { z } from 'zod';

// Base event interface
export interface BaseEvent {
  id: string;
  type: string;
  source: string;
  target?: string;
  payload: any;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Event priority levels
export type EventPriority = 'low' | 'normal' | 'high' | 'critical';

// Event with priority and routing
export interface PlatformEvent extends BaseEvent {
  priority: EventPriority;
  persistent: boolean;
  ttl?: number;
  retryCount?: number;
  maxRetries?: number;
}

// Event handler interface
export interface EventHandler<T = any> {
  id: string;
  eventType: string;
  handler: (event: T) => Promise<void> | void;
  options?: {
    once?: boolean;
    priority?: number;
    filter?: (event: T) => boolean;
  };
}

// Event subscription
export interface EventSubscription {
  id: string;
  eventType: string;
  handler: EventHandler;
  active: boolean;
  createdAt: Date;
  lastTriggered?: Date;
  triggerCount: number;
}

// Event bus interface
export interface EventBus {
  // Event emission
  emit<T = any>(event: PlatformEvent): Promise<void>;
  emitSync<T = any>(event: PlatformEvent): void;
  
  // Event subscription
  on<T = any>(eventType: string, handler: EventHandler<T>): EventSubscription;
  once<T = any>(eventType: string, handler: EventHandler<T>): EventSubscription;
  off(subscriptionId: string): void;
  
  // Event querying
  getSubscriptions(eventType?: string): EventSubscription[];
  getEventHistory(eventType?: string, limit?: number): PlatformEvent[];
  
  // Event bus management
  clear(): void;
  pause(): void;
  resume(): void;
  getMetrics(): EventBusMetrics;
}

export interface EventBusMetrics {
  totalEvents: number;
  eventsPerSecond: number;
  activeSubscriptions: number;
  eventTypes: string[];
  errorRate: number;
  averageProcessingTime: number;
}

// Event routing
export interface EventRoute {
  pattern: string | RegExp;
  target: string;
  transform?: (event: PlatformEvent) => PlatformEvent;
  condition?: (event: PlatformEvent) => boolean;
}

// Event middleware
export interface EventMiddleware {
  name: string;
  priority: number;
  process: (event: PlatformEvent, next: () => Promise<void>) => Promise<void>;
}

// Event store for persistence
export interface EventStore {
  save(event: PlatformEvent): Promise<void>;
  get(eventId: string): Promise<PlatformEvent | null>;
  query(criteria: EventQueryCriteria): Promise<PlatformEvent[]>;
  delete(eventId: string): Promise<void>;
  cleanup(olderThan: Date): Promise<number>;
}

export interface EventQueryCriteria {
  eventType?: string;
  source?: string;
  target?: string;
  fromDate?: Date;
  toDate?: Date;
  limit?: number;
  offset?: number;
}

// Event validation schema
export const EventSchema = z.object({
  id: z.string(),
  type: z.string(),
  source: z.string(),
  target: z.string().optional(),
  payload: z.any(),
  timestamp: z.date(),
  priority: z.enum(['low', 'normal', 'high', 'critical']).default('normal'),
  persistent: z.boolean().default(false),
  ttl: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

// Common event types
export type SystemEvent = 
  | { type: 'system:startup'; payload: { version: string; environment: string } }
  | { type: 'system:shutdown'; payload: { reason: string } }
  | { type: 'system:error'; payload: { error: Error; context?: any } }
  | { type: 'system:health_check'; payload: { status: 'healthy' | 'unhealthy'; details: any } };

export type UserEvent = 
  | { type: 'user:login'; payload: { userId: string; sessionId: string } }
  | { type: 'user:logout'; payload: { userId: string; sessionId: string } }
  | { type: 'user:action'; payload: { userId: string; action: string; data?: any } };

// Event factory
export class EventFactory {
  static create<T = any>(
    type: string,
    source: string,
    payload: T,
    options?: {
      target?: string;
      priority?: EventPriority;
      persistent?: boolean;
      ttl?: number;
      metadata?: Record<string, any>;
    }
  ): PlatformEvent {
    return {
      id: crypto.randomUUID(),
      type,
      source,
      target: options?.target,
      payload,
      timestamp: new Date(),
      priority: options?.priority || 'normal',
      persistent: options?.persistent || false,
      ttl: options?.ttl,
      metadata: options?.metadata,
    };
  }
}
