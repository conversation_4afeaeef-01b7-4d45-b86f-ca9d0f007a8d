import { z } from 'zod';

// Base module interface that all modules must implement
export interface BaseModule {
  id: string;
  name: string;
  version: string;
  status: ModuleStatus;
  
  // Lifecycle methods
  initialize(): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  destroy(): Promise<void>;
  
  // Health check
  healthCheck(): Promise<boolean>;
  
  // Configuration
  configure(config: ModuleConfig): Promise<void>;
  getConfig(): ModuleConfig;
  
  // Event handling
  emit(event: ModuleEvent): void;
  on(eventType: string, handler: (event: ModuleEvent) => void): void;
  off(eventType: string, handler: (event: ModuleEvent) => void): void;
}

// Module status
export type ModuleStatus = 'uninitialized' | 'initializing' | 'ready' | 'error' | 'stopping' | 'stopped';

// Module configuration
export const ModuleConfigSchema = z.object({
  enabled: z.boolean().default(true),
  settings: z.record(z.any()).optional(),
  dependencies: z.array(z.string()).optional(),
  routes: z.array(z.object({
    path: z.string(),
    component: z.string(),
    protected: z.boolean().optional(),
  })).optional(),
  api: z.object({
    enabled: z.boolean().default(false),
    prefix: z.string().optional(),
    endpoints: z.array(z.string()).optional(),
  }).optional(),
});

export type ModuleConfig = z.infer<typeof ModuleConfigSchema>;

// Module capabilities
export interface ModuleCapabilities {
  ai: boolean;
  realtime: boolean;
  storage: boolean;
  auth: boolean;
  api: boolean;
  ui: boolean;
  background: boolean;
}

// Module metadata
export interface ModuleMetadata {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  license: string;
  homepage?: string;
  repository?: string;
  keywords: string[];
  capabilities: ModuleCapabilities;
  dependencies: string[];
  peerDependencies?: string[];
}

// Module event
export interface ModuleEvent {
  type: string;
  source: string;
  target?: string;
  payload: any;
  timestamp: Date;
  id: string;
}

// Module registry entry
export interface ModuleRegistryEntry {
  metadata: ModuleMetadata;
  config: ModuleConfig;
  status: ModuleStatus;
  instance?: BaseModule;
  lastUpdate: Date;
  health: boolean;
  metrics: {
    startTime?: Date;
    requests: number;
    errors: number;
    averageResponseTime: number;
  };
}

// Module communication interface
export interface ModuleCommunication {
  sendMessage(targetModule: string, message: any): Promise<void>;
  broadcastMessage(message: any, excludeModules?: string[]): Promise<void>;
  subscribeToMessages(handler: (message: any) => void): () => void;
  requestResponse(targetModule: string, request: any): Promise<any>;
}

// Module state interface
export interface ModuleState {
  [key: string]: any;
}

// Module context
export interface ModuleContext {
  moduleId: string;
  config: ModuleConfig;
  communication: ModuleCommunication;
  state: ModuleState;
  platform: {
    version: string;
    environment: string;
    features: Record<string, boolean>;
  };
}
