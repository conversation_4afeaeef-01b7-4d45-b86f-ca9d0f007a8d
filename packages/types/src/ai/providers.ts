import { z } from 'zod';

// AI Provider configuration
export const AIProviderConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['openai', 'anthropic', 'google', 'azure', 'local', 'custom']),
  enabled: z.boolean().default(true),
  priority: z.number().default(1),
  config: z.object({
    apiKey: z.string().optional(),
    baseUrl: z.string().optional(),
    model: z.string().optional(),
    maxTokens: z.number().optional(),
    temperature: z.number().optional(),
    timeout: z.number().optional(),
  }),
  capabilities: z.array(z.enum([
    'chat', 'completion', 'embedding', 'image-generation', 
    'image-analysis', 'code-generation', 'function-calling',
    'streaming', 'vision', 'audio'
  ])),
  limits: z.object({
    requestsPerMinute: z.number().optional(),
    requestsPerDay: z.number().optional(),
    tokensPerRequest: z.number().optional(),
    costPerToken: z.number().optional(),
  }).optional(),
});

export type AIProviderConfig = z.infer<typeof AIProviderConfigSchema>;

// AI Request/Response types
export interface AIRequest {
  id: string;
  type: 'chat' | 'completion' | 'embedding' | 'image' | 'function';
  provider?: string;
  model?: string;
  messages?: AIMessage[];
  prompt?: string;
  context?: AIContext;
  options?: AIRequestOptions;
  metadata?: Record<string, any>;
}

export interface AIMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string | AIMessageContent[];
  name?: string;
  function_call?: {
    name: string;
    arguments: string;
  };
}

export interface AIMessageContent {
  type: 'text' | 'image' | 'audio' | 'file';
  text?: string;
  image_url?: {
    url: string;
    detail?: 'low' | 'high' | 'auto';
  };
  audio_url?: string;
  file_url?: string;
}

export interface AIRequestOptions {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
  stream?: boolean;
  functions?: AIFunction[];
  functionCall?: 'auto' | 'none' | { name: string };
}

export interface AIFunction {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

export interface AIResponse {
  id: string;
  requestId: string;
  provider: string;
  model: string;
  content: string;
  finishReason: 'stop' | 'length' | 'function_call' | 'content_filter' | 'error';
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    cost?: number;
  };
  metadata?: Record<string, any>;
  timestamp: Date;
  latency: number;
}

// AI Context for cross-module communication
export interface AIContext {
  conversationId?: string;
  sessionId?: string;
  userId?: string;
  moduleId?: string;
  workflowId?: string;
  history?: AIMessage[];
  variables?: Record<string, any>;
  preferences?: AIPreferences;
}

export interface AIPreferences {
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  language?: string;
  tone?: 'formal' | 'casual' | 'technical' | 'creative';
  verbosity?: 'concise' | 'normal' | 'detailed';
}

// AI Provider interface
export interface AIProvider {
  id: string;
  name: string;
  config: AIProviderConfig;
  
  // Core methods
  initialize(): Promise<void>;
  healthCheck(): Promise<boolean>;
  
  // Request handling
  processRequest(request: AIRequest): Promise<AIResponse>;
  streamRequest(request: AIRequest): AsyncIterable<Partial<AIResponse>>;
  
  // Capabilities
  supports(capability: string): boolean;
  getModels(): Promise<string[]>;
  
  // Metrics
  getMetrics(): Promise<AIProviderMetrics>;
}

export interface AIProviderMetrics {
  requests: number;
  errors: number;
  averageLatency: number;
  tokensUsed: number;
  cost: number;
  uptime: number;
  lastRequest?: Date;
}

// AI Orchestration types
export interface AIOrchestrationStrategy {
  type: 'single' | 'fallback' | 'parallel' | 'consensus' | 'routing';
  providers: string[];
  config: Record<string, any>;
}

export interface AIOrchestrationResult {
  primary: AIResponse;
  alternatives?: AIResponse[];
  strategy: AIOrchestrationStrategy;
  totalLatency: number;
  totalCost: number;
}
