{"name": "@unified-assistant/types", "version": "0.1.0", "description": "Shared TypeScript types for the unified AI assistant platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "typescript": "^5", "eslint": "^8", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.8.1", "jest": "^29"}, "peerDependencies": {"react": "^18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./ai": {"types": "./dist/ai/index.d.ts", "import": "./dist/ai/index.js", "require": "./dist/ai/index.js"}, "./modules": {"types": "./dist/modules/index.d.ts", "import": "./dist/modules/index.js", "require": "./dist/modules/index.js"}, "./events": {"types": "./dist/events/index.d.ts", "import": "./dist/events/index.js", "require": "./dist/events/index.js"}}, "files": ["dist", "README.md"]}