#!/bin/bash

# Build Chrome Extension Script
cd /Users/<USER>/Projects/own/assistant/apps/chrome-extension

echo "🚀 Building Chrome Extension..."

# Check if node is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js first."
    exit 1
fi

# Try to run the simple build
if [ -f "simple-build.js" ]; then
    echo "📦 Using simple build script..."
    node simple-build.js
elif [ -f "quick-build.js" ]; then
    echo "📦 Using quick build script..."
    node quick-build.js
else
    echo "❌ No build script found."
    exit 1
fi

echo "✅ Build completed!"