#!/usr/bin/env node

/**
 * Build script for Chrome Extension
 * Builds the extension and optionally packages it for distribution
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ExtensionBuilder {
  constructor() {
    this.projectRoot = __dirname;
    this.distDir = path.join(this.projectRoot, 'dist');
    this.srcDir = path.join(this.projectRoot, 'src');
  }

  async build() {
    console.log('🚀 Building Chrome Extension...');
    
    try {
      // Clean dist directory
      this.cleanDist();
      
      // Run webpack build
      this.runWebpack();
      
      // Validate build output
      this.validateBuild();
      
      // Report success
      this.reportSuccess();
      
    } catch (error) {
      console.error('❌ Build failed:', error.message);
      process.exit(1);
    }
  }

  cleanDist() {
    console.log('🧹 Cleaning dist directory...');
    
    if (fs.existsSync(this.distDir)) {
      fs.rmSync(this.distDir, { recursive: true, force: true });
    }
    
    fs.mkdirSync(this.distDir, { recursive: true });
  }

  runWebpack() {
    console.log('📦 Running webpack build...');
    
    try {
      // Try different webpack paths
      const webpackPaths = [
        'node_modules/.bin/webpack',
        './node_modules/.bin/webpack',
        'npx webpack'
      ];
      
      let webpackCommand = null;
      for (const webpackPath of webpackPaths) {
        try {
          if (webpackPath.startsWith('node_modules') || webpackPath.startsWith('./node_modules')) {
            const fullPath = path.join(this.projectRoot, webpackPath);
            if (fs.existsSync(fullPath)) {
              webpackCommand = webpackPath;
              break;
            }
          } else {
            webpackCommand = webpackPath;
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (!webpackCommand) {
        throw new Error('Webpack not found. Please run npm install first.');
      }
      
      console.log(`Using webpack command: ${webpackCommand}`);
      execSync(`${webpackCommand} --mode production`, {
        cwd: this.projectRoot,
        stdio: 'inherit'
      });
    } catch (error) {
      throw new Error(`Webpack build failed: ${error.message}`);
    }
  }

  validateBuild() {
    console.log('✅ Validating build output...');
    
    const requiredFiles = [
      'manifest.json',
      'background/service-worker.js',
      'content/github-analyzer.js',
      'content/github-analyzer.css',
      'popup/popup.html',
      'popup/popup.js',
      'popup/popup.css'
    ];

    const missingFiles = [];
    
    requiredFiles.forEach(file => {
      const filePath = path.join(this.distDir, file);
      if (!fs.existsSync(filePath)) {
        missingFiles.push(file);
      }
    });

    if (missingFiles.length > 0) {
      throw new Error(`Missing required files: ${missingFiles.join(', ')}`);
    }

    // Validate manifest.json
    this.validateManifest();
    
    // Check file sizes
    this.checkFileSizes();
  }

  validateManifest() {
    const manifestPath = path.join(this.distDir, 'manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    const requiredFields = ['name', 'version', 'manifest_version', 'description'];
    const missingFields = requiredFields.filter(field => !manifest[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`Manifest missing required fields: ${missingFields.join(', ')}`);
    }
    
    if (manifest.manifest_version !== 3) {
      throw new Error('Manifest version must be 3 for Chrome Extensions');
    }
    
    console.log(`📋 Manifest validated - ${manifest.name} v${manifest.version}`);
  }

  checkFileSizes() {
    const files = this.getAllFiles(this.distDir);
    const largeFiles = [];
    const maxSize = 2 * 1024 * 1024; // 2MB
    
    files.forEach(file => {
      const stats = fs.statSync(file);
      if (stats.size > maxSize) {
        largeFiles.push({
          file: path.relative(this.distDir, file),
          size: this.formatBytes(stats.size)
        });
      }
    });
    
    if (largeFiles.length > 0) {
      console.warn('⚠️  Large files detected:');
      largeFiles.forEach(({ file, size }) => {
        console.warn(`  - ${file}: ${size}`);
      });
    }
    
    const totalSize = files.reduce((total, file) => {
      return total + fs.statSync(file).size;
    }, 0);
    
    console.log(`📊 Total build size: ${this.formatBytes(totalSize)}`);
  }

  getAllFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        files.push(...this.getAllFiles(itemPath));
      } else {
        files.push(itemPath);
      }
    });
    
    return files;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  reportSuccess() {
    console.log('\n🎉 Build completed successfully!');
    console.log('\nNext steps:');
    console.log('  1. Load the extension in Chrome:');
    console.log('     - Open chrome://extensions/');
    console.log('     - Enable "Developer mode"');
    console.log('     - Click "Load unpacked"');
    console.log(`     - Select the "dist" folder: ${this.distDir}`);
    console.log('  2. Test the extension on GitHub repositories');
    console.log('  3. Package for distribution: npm run package');
    console.log('\n📁 Build output location:', this.distDir);
  }

  async package() {
    console.log('📦 Packaging extension for distribution...');
    
    if (!fs.existsSync(this.distDir)) {
      throw new Error('Build directory not found. Run build first.');
    }
    
    const packagePath = path.join(this.projectRoot, 'extension.zip');
    
    try {
      // Remove existing package
      if (fs.existsSync(packagePath)) {
        fs.unlinkSync(packagePath);
      }
      
      // Create zip archive
      execSync(`cd "${this.distDir}" && zip -r "../extension.zip" .`, {
        cwd: this.projectRoot,
        stdio: 'inherit'
      });
      
      console.log(`📦 Extension packaged: ${packagePath}`);
      
    } catch (error) {
      throw new Error(`Packaging failed: ${error.message}`);
    }
  }
}

// Main execution
async function main() {
  const builder = new ExtensionBuilder();
  const args = process.argv.slice(2);
  
  if (args.includes('--package')) {
    await builder.build();
    await builder.package();
  } else {
    await builder.build();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Build script failed:', error);
    process.exit(1);
  });
}

module.exports = ExtensionBuilder;