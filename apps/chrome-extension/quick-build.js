#!/usr/bin/env node

/**
 * Quick build script for Chrome Extension
 * Bypasses shell issues and builds the extension directly
 */

const fs = require('fs');
const path = require('path');

const projectRoot = '/Users/<USER>/Projects/own/assistant/apps/chrome-extension';
const srcDir = path.join(projectRoot, 'src');
const distDir = path.join(projectRoot, 'dist');

console.log('🚀 Starting quick build...');

// Clean and create dist directory
if (fs.existsSync(distDir)) {
  fs.rmSync(distDir, { recursive: true, force: true });
}
fs.mkdirSync(distDir, { recursive: true });

// Helper function to ensure directory exists
function ensureDir(dirName) {
  const dirPath = path.join(distDir, dirName);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Helper function to copy file
function copyFile(srcPath, destPath) {
  const srcFullPath = path.join(projectRoot, srcPath);
  const destFullPath = path.join(distDir, destPath);
  
  if (fs.existsSync(srcFullPath)) {
    fs.copyFileSync(srcFullPath, destFullPath);
    console.log(`  ✓ ${srcPath} → ${destPath}`);
  } else {
    console.warn(`  ⚠ Source file not found: ${srcPath}`);
  }
}

// Helper function to copy directory recursively
function copyDirectoryRecursive(srcDir, destDir) {
  if (!fs.existsSync(srcDir)) {
    console.warn(`  ⚠ Source directory not found: ${srcDir}`);
    return;
  }
  
  const files = fs.readdirSync(srcDir);
  
  files.forEach(file => {
    const srcFile = path.join(srcDir, file);
    const destFile = path.join(destDir, file);
    const stats = fs.statSync(srcFile);
    
    if (stats.isDirectory()) {
      if (!fs.existsSync(destFile)) {
        fs.mkdirSync(destFile, { recursive: true });
      }
      copyDirectoryRecursive(srcFile, destFile);
    } else {
      fs.copyFileSync(srcFile, destFile);
      console.log(`  ✓ ${path.relative(projectRoot, srcFile)} → ${path.relative(projectRoot, destFile)}`);
    }
  });
}

console.log('📁 Copying files...');

// Copy manifest.json
copyFile('src/manifest.json', 'manifest.json');

// Copy background
ensureDir('background');
copyFile('src/background/service-worker.js', 'background/service-worker.js');

// Copy content
ensureDir('content');
copyFile('src/content/github-analyzer.js', 'content/github-analyzer.js');
copyFile('src/content/github-analyzer.css', 'content/github-analyzer.css');

// Copy popup
ensureDir('popup');
copyFile('src/popup/popup.html', 'popup/popup.html');
copyFile('src/popup/popup.js', 'popup/popup.js');
copyFile('src/popup/popup.css', 'popup/popup.css');

// Copy shared directory
ensureDir('shared');
const sharedSrcDir = path.join(srcDir, 'shared');
const sharedDestDir = path.join(distDir, 'shared');
copyDirectoryRecursive(sharedSrcDir, sharedDestDir);

// Copy icons if they exist
const iconsSrcDir = path.join(srcDir, 'icons');
if (fs.existsSync(iconsSrcDir)) {
  ensureDir('icons');
  const iconsDestDir = path.join(distDir, 'icons');
  copyDirectoryRecursive(iconsSrcDir, iconsDestDir);
}

console.log('✅ Quick build completed successfully!');
console.log('\nTo load the extension:');
console.log('1. Open chrome://extensions/');
console.log('2. Enable Developer mode');
console.log('3. Click "Load unpacked"');
console.log(`4. Select: ${distDir}`);
console.log('\n📁 Build output location:', distDir);