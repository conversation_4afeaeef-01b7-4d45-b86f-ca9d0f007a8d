{"name": "github-analyzer-chrome-extension", "version": "1.0.0", "description": "Chrome extension for analyzing GitHub repositories and generating comprehensive documentation", "main": "src/background/service-worker.js", "scripts": {"build": "node build.js", "build:simple": "node simple-build.js", "build:extension": "node_modules/.bin/webpack --mode production", "build:dev": "node_modules/.bin/webpack --mode development", "watch": "node_modules/.bin/webpack --mode development --watch", "package": "node build.js --package", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .js,.ts,.jsx,.tsx", "lint:fix": "eslint src --ext .js,.ts,.jsx,.tsx --fix", "clean": "rm -rf dist", "dev": "npm run build:dev && npm run watch"}, "dependencies": {"chrome-types": "^0.1.0", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@types/chrome": "^0.0.270", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/webextension-polyfill": "^0.10.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "babel-loader": "^9.1.0", "copy-webpack-plugin": "^12.0.0", "css-loader": "^6.8.0", "eslint": "^8.0.0", "jest": "^29.5.0", "playwright": "^1.40.0", "style-loader": "^3.3.0", "ts-jest": "^29.1.0", "ts-loader": "^9.5.0", "typescript": "^5.0.0", "webpack": "^5.90.0", "webpack-cli": "^5.1.4"}, "keywords": ["chrome-extension", "github", "documentation", "analysis", "pib-method", "gemini-ai", "ai"], "author": "PIB-METHOD Assistant", "license": "MIT", "engines": {"node": ">=18.0.0"}}