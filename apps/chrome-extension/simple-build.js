#!/usr/bin/env node

/**
 * Simple build script that copies files without webpack
 * Use this as a fallback if webpack has issues
 */

const fs = require('fs');
const path = require('path');

class SimpleBuild {
  constructor() {
    this.projectRoot = __dirname;
    this.srcDir = path.join(this.projectRoot, 'src');
    this.distDir = path.join(this.projectRoot, 'dist');
  }

  build() {
    console.log('🚀 Starting simple build...');
    
    // Clean and create dist directory
    this.cleanDist();
    
    // Copy all files
    this.copyFiles();
    
    console.log('✅ Simple build completed!');
    console.log('\nTo load the extension:');
    console.log('1. Open chrome://extensions/');
    console.log('2. Enable Developer mode');
    console.log('3. Click "Load unpacked"');
    console.log(`4. Select: ${this.distDir}`);
  }

  cleanDist() {
    if (fs.existsSync(this.distDir)) {
      fs.rmSync(this.distDir, { recursive: true, force: true });
    }
    fs.mkdirSync(this.distDir, { recursive: true });
  }

  copyFiles() {
    console.log('📁 Copying files...');
    
    // Copy manifest.json
    this.copyFile('src/manifest.json', 'manifest.json');
    
    // Copy background
    this.ensureDir('background');
    this.copyFile('src/background/service-worker.js', 'background/service-worker.js');
    
    // Copy content
    this.ensureDir('content');
    this.copyFile('src/content/github-analyzer.js', 'content/github-analyzer.js');
    this.copyFile('src/content/github-analyzer.css', 'content/github-analyzer.css');
    
    // Copy popup
    this.ensureDir('popup');
    this.copyFile('src/popup/popup.html', 'popup/popup.html');
    this.copyFile('src/popup/popup.js', 'popup/popup.js');
    this.copyFile('src/popup/popup.css', 'popup/popup.css');
    
    // Copy shared (these will be imported by other scripts)
    this.ensureDir('shared');
    this.copyDirectory('src/shared', 'shared');
    
    // Copy icons if they exist
    if (fs.existsSync(path.join(this.srcDir, 'icons'))) {
      this.ensureDir('icons');
      this.copyDirectory('src/icons', 'icons');
    }
    
    console.log('📁 All files copied successfully!');
  }

  ensureDir(dirName) {
    const dirPath = path.join(this.distDir, dirName);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  copyFile(srcPath, destPath) {
    const srcFullPath = path.join(this.projectRoot, srcPath);
    const destFullPath = path.join(this.distDir, destPath);
    
    if (fs.existsSync(srcFullPath)) {
      fs.copyFileSync(srcFullPath, destFullPath);
      console.log(`  ✓ ${srcPath} → ${destPath}`);
    } else {
      console.warn(`  ⚠ Source file not found: ${srcPath}`);
    }
  }

  copyDirectory(srcDir, destDir) {
    const srcFullPath = path.join(this.projectRoot, srcDir);
    const destFullPath = path.join(this.distDir, destDir);
    
    if (!fs.existsSync(srcFullPath)) {
      console.warn(`  ⚠ Source directory not found: ${srcDir}`);
      return;
    }
    
    const files = fs.readdirSync(srcFullPath);
    
    files.forEach(file => {
      const srcFile = path.join(srcFullPath, file);
      const destFile = path.join(destFullPath, file);
      const stats = fs.statSync(srcFile);
      
      if (stats.isDirectory()) {
        if (!fs.existsSync(destFile)) {
          fs.mkdirSync(destFile, { recursive: true });
        }
        this.copyDirectoryRecursive(srcFile, destFile);
      } else {
        fs.copyFileSync(srcFile, destFile);
        console.log(`  ✓ ${path.join(srcDir, file)} → ${path.join(destDir, file)}`);
      }
    });
  }

  copyDirectoryRecursive(srcDir, destDir) {
    const files = fs.readdirSync(srcDir);
    
    files.forEach(file => {
      const srcFile = path.join(srcDir, file);
      const destFile = path.join(destDir, file);
      const stats = fs.statSync(srcFile);
      
      if (stats.isDirectory()) {
        if (!fs.existsSync(destFile)) {
          fs.mkdirSync(destFile, { recursive: true });
        }
        this.copyDirectoryRecursive(srcFile, destFile);
      } else {
        fs.copyFileSync(srcFile, destFile);
      }
    });
  }
}

// Run the build
const builder = new SimpleBuild();
builder.build();