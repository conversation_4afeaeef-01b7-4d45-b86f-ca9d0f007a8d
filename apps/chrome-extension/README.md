# GitHub Repository Analyzer Chrome Extension

A Chrome extension that analyzes GitHub repositories and generates comprehensive documentation using AI agents and the PIB-METHOD framework.

## Features

- **Automatic GitHub Detection**: Detects when you're on a GitHub repository page
- **AI-Powered Analysis**: Uses Perplexity and Context7 for research and technical analysis
- **PIB-METHOD Integration**: Leverages architect, analyst, PM, and developer agents
- **Gemini AI Workflows**: Orchestrates complex analysis workflows using Google's Gemini API
- **Comprehensive Documentation**: Generates project briefs, PRDs, architecture docs, and implementation guides
- **LLM Prompts**: Creates optimized prompts for building the analyzed system

## Installation

1. Clone the repository
2. Install dependencies: `npm install`
3. Build the extension: `npm run build`
4. Load the extension in Chrome:
   - Open Chrome Extensions (`chrome://extensions/`)
   - Enable "Developer mode"
   - Click "Load unpacked" and select the `dist` folder

## Configuration

1. Click the extension icon in Chrome
2. Go to Settings tab
3. Configure your API keys:
   - Gemini API Key (required for AI document generation)
   - Perplexity API Key (required for market research)
   - Context7 API Key (optional, for technical documentation research)
   - GitHub Personal Access Token (optional, for higher rate limits)

## Usage

1. Navigate to any GitHub repository
2. Click the "Analyze Repository" button that appears
3. Wait for the analysis to complete
4. View the generated documentation in the modal
5. Access analysis history through the extension popup

## Project Structure

```
src/
├── manifest.json              # Chrome extension manifest
├── background/
│   └── service-worker.js      # Background script for Chrome extension
├── content/
│   ├── github-analyzer.js     # Content script for GitHub pages
│   └── github-analyzer.css    # Styles for injected UI
├── popup/
│   ├── popup.html            # Extension popup interface
│   ├── popup.css             # Popup styles
│   └── popup.js              # Popup functionality
└── shared/
    ├── agent-orchestrator.js  # Main orchestration logic
    ├── pib-method-adapter.js   # PIB-METHOD agent integration
    ├── github-client.js        # GitHub API client
    ├── perplexity-client.js    # Perplexity API client
    ├── context7-client.js      # Context7 API client
    ├── gemini-workflow.js      # Gemini AI workflow management
    ├── storage-manager.js      # Chrome storage management
    ├── security-manager.js     # Security and encryption
    ├── github-page-detector.js # GitHub page detection
    ├── analysis-ui.js          # UI components for analysis
    └── message-bridge.js       # Message passing between scripts
```

## Architecture

The extension uses a multi-agent architecture based on the PIB-METHOD framework:

1. **Service Worker**: Manages background tasks and API communication
2. **Content Script**: Detects GitHub pages and injects analysis UI
3. **Agent Orchestrator**: Coordinates multiple AI agents for analysis
4. **PIB Agents**: Specialized agents for different analysis aspects
5. **External APIs**: Integrates with Perplexity, Context7, and GitHub APIs

## Analysis Workflow

1. **Repository Detection**: Identifies GitHub repository and extracts metadata
2. **Structure Analysis**: Analyzes codebase structure and dependencies
3. **Market Research**: Uses Perplexity for competitive analysis and trends
4. **Technical Analysis**: Uses Context7 for framework and technology research
5. **Architecture Design**: Gemini AI designs optimal system architecture
6. **Documentation Generation**: Gemini AI creates comprehensive documentation with full context
7. **Project Brief**: Gemini AI creates project management documentation
8. **Implementation Guide**: Gemini AI creates build instructions and optimized LLM prompts

Each step builds upon previous findings using Gemini's 1M token context window for comprehensive analysis.

## Development

### Prerequisites

- Node.js 18+
- Chrome browser
- API keys for Gemini and Perplexity
- Optional: Context7 API key for enhanced technical research

### Scripts

- `npm run build` - Build the extension
- `npm run build:dev` - Build for development
- `npm run watch` - Watch for changes and rebuild
- `npm run test` - Run tests
- `npm run lint` - Run linting

### Testing

The extension includes comprehensive testing:

- Unit tests for individual components
- Integration tests for API clients
- E2E tests using Playwright for Chrome extension testing

## API Integration

### Gemini API
Used for AI-powered document generation and analysis:
- Comprehensive documentation generation
- Architecture design and recommendations
- Implementation guide creation with optimized LLM prompts
- Context-aware analysis using 1M token context window
- Project brief and technical specification generation

### Perplexity API
Used for market research and competitive analysis:
- Market trends and opportunities
- Competitor analysis
- Technology trend research

### Context7 API
Used for technical documentation and framework research:
- Library and framework documentation
- API reference lookup
- Best practices research

### GitHub API
Used for repository analysis:
- Repository metadata and statistics
- File structure analysis
- Dependency extraction
- Contributor and activity data

## Security

- API keys are encrypted using Chrome's storage encryption
- All communication uses HTTPS
- No sensitive data is stored locally
- Rate limiting prevents API abuse

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run linting and tests
6. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Open an issue on GitHub
- Check the troubleshooting guide
- Review the API documentation

## Roadmap

- [ ] Support for more version control platforms
- [ ] Advanced analysis customization
- [ ] Team collaboration features
- [ ] Integration with development tools
- [ ] Custom agent configurations
- [ ] Offline analysis capabilities