/**
 * Chrome Storage Manager
 * Handles persistent storage for the extension using Chrome's storage API
 */

export class StorageManager {
  constructor() {
    this.encryptionKey = null;
    this.storageType = 'sync'; // 'sync' or 'local'
    this.defaultSettings = {
      perplexityKey: '',
      githubToken: '',
      openaiKey: '',
      autoAnalysis: true,
      showNotifications: true,
      analysisDepth: 'standard',
      theme: 'auto',
      language: 'en',
      maxHistoryItems: 100,
      cacheExpiry: 24 * 60 * 60 * 1000, // 24 hours
      enableAnalytics: true,
      enableErrorReporting: true
    };
  }

  async initialize() {
    // Generate or retrieve encryption key
    this.encryptionKey = await this.getOrCreateEncryptionKey();
    console.log('Storage manager initialized');
  }

  async getOrCreateEncryptionKey() {
    try {
      const result = await chrome.storage.local.get(['encryptionKey']);
      
      if (result.encryptionKey) {
        return result.encryptionKey;
      }

      // Generate new encryption key
      const key = await this.generateEncryptionKey();
      await chrome.storage.local.set({ encryptionKey: key });
      return key;
    } catch (error) {
      console.error('Error managing encryption key:', error);
      // Fallback to session-based key
      return await this.generateEncryptionKey();
    }
  }

  async generateEncryptionKey() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  async encrypt(data) {
    if (!this.encryptionKey) {
      await this.initialize();
    }

    try {
      const encoder = new TextEncoder();
      const dataArray = encoder.encode(JSON.stringify(data));
      
      const key = await crypto.subtle.importKey(
        'raw',
        new Uint8Array(this.encryptionKey.match(/.{2}/g).map(byte => parseInt(byte, 16))),
        { name: 'AES-GCM' },
        false,
        ['encrypt']
      );

      const iv = crypto.getRandomValues(new Uint8Array(12));
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        key,
        dataArray
      );

      return {
        iv: Array.from(iv),
        data: Array.from(new Uint8Array(encrypted))
      };
    } catch (error) {
      console.error('Encryption error:', error);
      throw error;
    }
  }

  async decrypt(encryptedData) {
    if (!this.encryptionKey) {
      await this.initialize();
    }

    try {
      const key = await crypto.subtle.importKey(
        'raw',
        new Uint8Array(this.encryptionKey.match(/.{2}/g).map(byte => parseInt(byte, 16))),
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );

      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv: new Uint8Array(encryptedData.iv) },
        key,
        new Uint8Array(encryptedData.data)
      );

      const decoder = new TextDecoder();
      return JSON.parse(decoder.decode(decrypted));
    } catch (error) {
      console.error('Decryption error:', error);
      throw error;
    }
  }

  async getSettings() {
    try {
      const result = await chrome.storage.sync.get(Object.keys(this.defaultSettings));
      
      // Merge with defaults
      const settings = { ...this.defaultSettings, ...result };
      
      // Decrypt sensitive settings
      if (settings.perplexityKey) {
        try {
          settings.perplexityKey = await this.decrypt(settings.perplexityKey);
        } catch (error) {
          console.warn('Failed to decrypt Perplexity key:', error);
          settings.perplexityKey = '';
        }
      }
      
      if (settings.githubToken) {
        try {
          settings.githubToken = await this.decrypt(settings.githubToken);
        } catch (error) {
          console.warn('Failed to decrypt GitHub token:', error);
          settings.githubToken = '';
        }
      }
      
      if (settings.openaiKey) {
        try {
          settings.openaiKey = await this.decrypt(settings.openaiKey);
        } catch (error) {
          console.warn('Failed to decrypt OpenAI key:', error);
          settings.openaiKey = '';
        }
      }

      return settings;
    } catch (error) {
      console.error('Error getting settings:', error);
      return this.defaultSettings;
    }
  }

  async updateSettings(newSettings) {
    try {
      const settingsToSave = { ...newSettings };
      
      // Encrypt sensitive settings
      if (settingsToSave.perplexityKey) {
        settingsToSave.perplexityKey = await this.encrypt(settingsToSave.perplexityKey);
      }
      
      if (settingsToSave.githubToken) {
        settingsToSave.githubToken = await this.encrypt(settingsToSave.githubToken);
      }
      
      if (settingsToSave.openaiKey) {
        settingsToSave.openaiKey = await this.encrypt(settingsToSave.openaiKey);
      }

      await chrome.storage.sync.set(settingsToSave);
      console.log('Settings updated successfully');
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  }

  async resetSettings() {
    try {
      await chrome.storage.sync.clear();
      await chrome.storage.local.remove(['encryptionKey']);
      console.log('Settings reset successfully');
    } catch (error) {
      console.error('Error resetting settings:', error);
      throw error;
    }
  }

  async saveAnalysis(repositoryInfo, analysis) {
    try {
      const analysisData = {
        id: this.generateAnalysisId(repositoryInfo),
        repository: repositoryInfo,
        analysis,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };

      // Get existing history
      const history = await this.getAnalysisHistory();
      
      // Add new analysis to the beginning
      history.unshift(analysisData);
      
      // Limit history size
      const maxItems = (await this.getSettings()).maxHistoryItems;
      if (history.length > maxItems) {
        history.splice(maxItems);
      }

      // Save updated history
      await chrome.storage.local.set({ analysisHistory: history });
      
      // Also save as cache for quick access
      await this.cacheAnalysis(repositoryInfo, analysis);
      
      console.log('Analysis saved successfully');
    } catch (error) {
      console.error('Error saving analysis:', error);
      throw error;
    }
  }

  async getAnalysisHistory() {
    try {
      const result = await chrome.storage.local.get(['analysisHistory']);
      return result.analysisHistory || [];
    } catch (error) {
      console.error('Error getting analysis history:', error);
      return [];
    }
  }

  async getAnalysis(repositoryInfo) {
    try {
      const analysisId = this.generateAnalysisId(repositoryInfo);
      const history = await this.getAnalysisHistory();
      
      return history.find(analysis => analysis.id === analysisId);
    } catch (error) {
      console.error('Error getting analysis:', error);
      return null;
    }
  }

  async deleteAnalysis(repositoryInfo) {
    try {
      const analysisId = this.generateAnalysisId(repositoryInfo);
      const history = await this.getAnalysisHistory();
      
      const updatedHistory = history.filter(analysis => analysis.id !== analysisId);
      await chrome.storage.local.set({ analysisHistory: updatedHistory });
      
      // Also remove from cache
      await this.removeCachedAnalysis(repositoryInfo);
      
      console.log('Analysis deleted successfully');
    } catch (error) {
      console.error('Error deleting analysis:', error);
      throw error;
    }
  }

  async clearHistory() {
    try {
      await chrome.storage.local.remove(['analysisHistory']);
      await this.clearCache();
      console.log('History cleared successfully');
    } catch (error) {
      console.error('Error clearing history:', error);
      throw error;
    }
  }

  async cacheAnalysis(repositoryInfo, analysis) {
    try {
      const cacheKey = `cache_${this.generateAnalysisId(repositoryInfo)}`;
      const cacheData = {
        data: analysis,
        timestamp: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (await this.getSettings()).cacheExpiry).toISOString()
      };

      await chrome.storage.local.set({ [cacheKey]: cacheData });
    } catch (error) {
      console.error('Error caching analysis:', error);
    }
  }

  async getCachedAnalysis(repositoryInfo) {
    try {
      const cacheKey = `cache_${this.generateAnalysisId(repositoryInfo)}`;
      const result = await chrome.storage.local.get([cacheKey]);
      const cacheData = result[cacheKey];

      if (!cacheData) {
        return null;
      }

      // Check if cache is expired
      if (new Date(cacheData.expiresAt) < new Date()) {
        await this.removeCachedAnalysis(repositoryInfo);
        return null;
      }

      return cacheData.data;
    } catch (error) {
      console.error('Error getting cached analysis:', error);
      return null;
    }
  }

  async removeCachedAnalysis(repositoryInfo) {
    try {
      const cacheKey = `cache_${this.generateAnalysisId(repositoryInfo)}`;
      await chrome.storage.local.remove([cacheKey]);
    } catch (error) {
      console.error('Error removing cached analysis:', error);
    }
  }

  async clearCache() {
    try {
      const result = await chrome.storage.local.get(null);
      const cacheKeys = Object.keys(result).filter(key => key.startsWith('cache_'));
      
      if (cacheKeys.length > 0) {
        await chrome.storage.local.remove(cacheKeys);
      }
      
      console.log('Cache cleared successfully');
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  async cleanupExpiredCache() {
    try {
      const result = await chrome.storage.local.get(null);
      const now = new Date();
      const expiredKeys = [];

      Object.keys(result).forEach(key => {
        if (key.startsWith('cache_') && result[key].expiresAt) {
          if (new Date(result[key].expiresAt) < now) {
            expiredKeys.push(key);
          }
        }
      });

      if (expiredKeys.length > 0) {
        await chrome.storage.local.remove(expiredKeys);
        console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
      }
    } catch (error) {
      console.error('Error cleaning up expired cache:', error);
    }
  }

  generateAnalysisId(repositoryInfo) {
    return `${repositoryInfo.owner}-${repositoryInfo.repo}`.toLowerCase();
  }

  async exportData() {
    try {
      const settings = await this.getSettings();
      const history = await this.getAnalysisHistory();
      
      // Remove sensitive data for export
      const exportSettings = { ...settings };
      delete exportSettings.perplexityKey;
      delete exportSettings.githubToken;
      delete exportSettings.openaiKey;

      const exportData = {
        version: '1.0',
        exportedAt: new Date().toISOString(),
        settings: exportSettings,
        history: history.map(item => ({
          id: item.id,
          repository: item.repository,
          timestamp: item.timestamp,
          // Include analysis summary but not full details
          analysisSummary: {
            hasMarketResearch: !!item.analysis?.marketResearch,
            hasTechnicalAnalysis: !!item.analysis?.technicalAnalysis,
            hasArchitectureDesign: !!item.analysis?.architectureDesign,
            hasDocumentation: !!item.analysis?.documentation,
            hasProjectBrief: !!item.analysis?.projectBrief,
            hasImplementationGuide: !!item.analysis?.implementationGuide
          }
        }))
      };

      return exportData;
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  async importData(importData) {
    try {
      if (!importData.version || importData.version !== '1.0') {
        throw new Error('Unsupported import data version');
      }

      // Import settings (excluding sensitive data)
      const currentSettings = await this.getSettings();
      const importSettings = {
        ...currentSettings,
        ...importData.settings,
        // Keep current API keys
        perplexityKey: currentSettings.perplexityKey,
        githubToken: currentSettings.githubToken,
        openaiKey: currentSettings.openaiKey
      };

      await this.updateSettings(importSettings);
      
      // Note: We don't import analysis history for privacy/security reasons
      // Users would need to re-analyze repositories
      
      console.log('Data imported successfully');
    } catch (error) {
      console.error('Error importing data:', error);
      throw error;
    }
  }

  async getStorageUsage() {
    try {
      const syncUsage = await chrome.storage.sync.getBytesInUse();
      const localUsage = await chrome.storage.local.getBytesInUse();
      
      return {
        sync: {
          used: syncUsage,
          quota: chrome.storage.sync.QUOTA_BYTES,
          percentage: (syncUsage / chrome.storage.sync.QUOTA_BYTES) * 100
        },
        local: {
          used: localUsage,
          quota: chrome.storage.local.QUOTA_BYTES,
          percentage: (localUsage / chrome.storage.local.QUOTA_BYTES) * 100
        }
      };
    } catch (error) {
      console.error('Error getting storage usage:', error);
      return null;
    }
  }

  async optimizeStorage() {
    try {
      // Clean up expired cache
      await this.cleanupExpiredCache();
      
      // Limit history size
      const settings = await this.getSettings();
      const history = await this.getAnalysisHistory();
      
      if (history.length > settings.maxHistoryItems) {
        const trimmedHistory = history.slice(0, settings.maxHistoryItems);
        await chrome.storage.local.set({ analysisHistory: trimmedHistory });
      }
      
      console.log('Storage optimized successfully');
    } catch (error) {
      console.error('Error optimizing storage:', error);
    }
  }

  // Event handlers for storage changes
  onSettingsChanged(callback) {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'sync') {
        const settingsChanges = {};
        Object.keys(changes).forEach(key => {
          if (key in this.defaultSettings) {
            settingsChanges[key] = changes[key].newValue;
          }
        });
        
        if (Object.keys(settingsChanges).length > 0) {
          callback(settingsChanges);
        }
      }
    });
  }

  onHistoryChanged(callback) {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local' && changes.analysisHistory) {
        callback(changes.analysisHistory.newValue || []);
      }
    });
  }

  // Utility methods
  async migrate(fromVersion, toVersion) {
    try {
      console.log(`Migrating storage from version ${fromVersion} to ${toVersion}`);
      
      // Add migration logic here as needed
      // For example, changing data structures, adding new fields, etc.
      
      console.log('Storage migration completed successfully');
    } catch (error) {
      console.error('Error during storage migration:', error);
      throw error;
    }
  }

  async backup() {
    try {
      const backup = await this.exportData();
      const backupKey = `backup_${new Date().toISOString()}`;
      
      await chrome.storage.local.set({ [backupKey]: backup });
      
      // Keep only last 5 backups
      const result = await chrome.storage.local.get(null);
      const backupKeys = Object.keys(result)
        .filter(key => key.startsWith('backup_'))
        .sort()
        .reverse();
      
      if (backupKeys.length > 5) {
        await chrome.storage.local.remove(backupKeys.slice(5));
      }
      
      console.log('Backup created successfully');
      return backupKey;
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  async restore(backupKey) {
    try {
      const result = await chrome.storage.local.get([backupKey]);
      const backup = result[backupKey];
      
      if (!backup) {
        throw new Error('Backup not found');
      }
      
      await this.importData(backup);
      console.log('Backup restored successfully');
    } catch (error) {
      console.error('Error restoring backup:', error);
      throw error;
    }
  }

  async listBackups() {
    try {
      const result = await chrome.storage.local.get(null);
      const backups = Object.keys(result)
        .filter(key => key.startsWith('backup_'))
        .map(key => ({
          key,
          timestamp: key.replace('backup_', ''),
          size: JSON.stringify(result[key]).length
        }))
        .sort((a, b) => b.timestamp.localeCompare(a.timestamp));
      
      return backups;
    } catch (error) {
      console.error('Error listing backups:', error);
      return [];
    }
  }
}