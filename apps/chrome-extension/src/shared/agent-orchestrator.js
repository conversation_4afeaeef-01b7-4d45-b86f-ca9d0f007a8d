/**
 * Agent Orchestrator
 * Manages PIB-METHOD agents and LangGraph workflows for repository analysis
 */

import { PIBMethodAdapter } from './pib-method-adapter.js';
import { GeminiWorkflow } from './langgraph-workflow.js';
import { PerplexityClient } from './perplexity-client.js';
import { Context7Client } from './context7-client.js';
import { GitHubClient } from './github-client.js';

export class AgentOrchestrator {
  constructor() {
    this.pibAdapter = new PIBMethodAdapter();
    this.langGraphWorkflow = new GeminiWorkflow();
    this.perplexityClient = new PerplexityClient();
    this.context7Client = new Context7Client();
    this.githubClient = new GitHubClient();
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Load API keys from storage
      const settings = await this.loadSettings();
      
      // Initialize clients with API keys
      await this.perplexityClient.initialize(settings.perplexityKey);
      await this.context7Client.initialize();
      await this.githubClient.initialize(settings.githubToken);
      
      // Initialize PIB-METHOD adapter
      await this.pibAdapter.initialize();
      
      // Initialize LangGraph workflow
      await this.langGraphWorkflow.initialize();
      
      this.isInitialized = true;
      console.log('Agent orchestrator initialized successfully');
    } catch (error) {
      console.error('Failed to initialize agent orchestrator:', error);
      throw error;
    }
  }

  async analyzeRepository(repositoryInfo) {
    await this.initialize();

    try {
      console.log('Starting repository analysis:', repositoryInfo);

      // Create analysis context
      const analysisContext = {
        repository: repositoryInfo,
        timestamp: new Date().toISOString(),
        workflow: {
          currentStep: 'initialization',
          totalSteps: 7,
          completedSteps: 0,
          errors: []
        },
        results: {
          repositoryDetails: null,
          marketResearch: null,
          technicalAnalysis: null,
          architectureDesign: null,
          documentation: null,
          projectBrief: null,
          implementationGuide: null
        }
      };

      // Step 1: Repository Analysis
      analysisContext.workflow.currentStep = 'repository_analysis';
      analysisContext.results.repositoryDetails = await this.analyzeRepositoryStructure(repositoryInfo);
      analysisContext.workflow.completedSteps++;

      // Step 2: Market Research (Perplexity)
      analysisContext.workflow.currentStep = 'market_research';
      analysisContext.results.marketResearch = await this.conductMarketResearch(repositoryInfo, analysisContext.results.repositoryDetails);
      analysisContext.workflow.completedSteps++;

      // Step 3: Technical Analysis (Context7)
      analysisContext.workflow.currentStep = 'technical_analysis';
      analysisContext.results.technicalAnalysis = await this.conductTechnicalAnalysis(repositoryInfo, analysisContext.results.repositoryDetails);
      analysisContext.workflow.completedSteps++;

      // Step 4: Architecture Design (PIB Architect)
      analysisContext.workflow.currentStep = 'architecture_design';
      analysisContext.results.architectureDesign = await this.designArchitecture(analysisContext);
      analysisContext.workflow.completedSteps++;

      // Step 5: Documentation Generation (PIB Analyst)
      analysisContext.workflow.currentStep = 'documentation_generation';
      analysisContext.results.documentation = await this.generateDocumentation(analysisContext);
      analysisContext.workflow.completedSteps++;

      // Step 6: Project Brief Creation (PIB PM)
      analysisContext.workflow.currentStep = 'project_brief';
      analysisContext.results.projectBrief = await this.createProjectBrief(analysisContext);
      analysisContext.workflow.completedSteps++;

      // Step 7: Implementation Guide (PIB Developer)
      analysisContext.workflow.currentStep = 'implementation_guide';
      analysisContext.results.implementationGuide = await this.createImplementationGuide(analysisContext);
      analysisContext.workflow.completedSteps++;

      // Finalize analysis
      analysisContext.workflow.currentStep = 'completed';
      analysisContext.completedAt = new Date().toISOString();

      console.log('Repository analysis completed successfully');
      return analysisContext;

    } catch (error) {
      console.error('Error during repository analysis:', error);
      throw error;
    }
  }

  async analyzeRepositoryStructure(repositoryInfo) {
    try {
      // Use GitHub API to get detailed repository information
      const repoDetails = await this.githubClient.getRepositoryDetails(repositoryInfo.owner, repositoryInfo.repo);
      const fileStructure = await this.githubClient.getFileStructure(repositoryInfo.owner, repositoryInfo.repo);
      const dependencies = await this.githubClient.getPackageFiles(repositoryInfo.owner, repositoryInfo.repo);
      const readme = await this.githubClient.getReadme(repositoryInfo.owner, repositoryInfo.repo);

      return {
        basicInfo: {
          name: repoDetails.name,
          fullName: repoDetails.full_name,
          description: repoDetails.description,
          language: repoDetails.language,
          size: repoDetails.size,
          stars: repoDetails.stargazers_count,
          forks: repoDetails.forks_count,
          issues: repoDetails.open_issues_count,
          license: repoDetails.license?.name,
          createdAt: repoDetails.created_at,
          updatedAt: repoDetails.updated_at,
          topics: repoDetails.topics || []
        },
        structure: {
          files: fileStructure,
          dependencies: dependencies,
          readme: readme
        },
        metrics: {
          codeComplexity: await this.calculateCodeComplexity(fileStructure),
          projectMaturity: await this.assessProjectMaturity(repoDetails),
          maintainabilityIndex: await this.calculateMaintainabilityIndex(repoDetails, fileStructure)
        }
      };
    } catch (error) {
      console.error('Error analyzing repository structure:', error);
      throw error;
    }
  }

  async conductMarketResearch(repositoryInfo, repositoryDetails) {
    try {
      const projectType = this.identifyProjectType(repositoryDetails);
      const marketContext = await this.perplexityClient.search({
        query: `${projectType} market analysis trends 2024 competitive landscape`,
        focus: 'market_research'
      });

      const competitorAnalysis = await this.perplexityClient.search({
        query: `${repositoryDetails.basicInfo.language} ${projectType} popular alternatives similar projects`,
        focus: 'competitors'
      });

      const technologyTrends = await this.perplexityClient.search({
        query: `${repositoryDetails.basicInfo.language} technology trends best practices 2024`,
        focus: 'technology_trends'
      });

      return {
        projectType,
        marketContext,
        competitorAnalysis,
        technologyTrends,
        opportunities: await this.identifyMarketOpportunities(marketContext, competitorAnalysis),
        risks: await this.identifyMarketRisks(marketContext, repositoryDetails)
      };
    } catch (error) {
      console.error('Error conducting market research:', error);
      throw error;
    }
  }

  async conductTechnicalAnalysis(repositoryInfo, repositoryDetails) {
    try {
      const primaryLanguage = repositoryDetails.basicInfo.language;
      const frameworks = await this.identifyFrameworks(repositoryDetails.structure.dependencies);

      // Get technical documentation for identified technologies
      const languageAnalysis = await this.context7Client.getDocumentation(primaryLanguage);
      const frameworkAnalysis = await Promise.all(
        frameworks.map(framework => this.context7Client.getDocumentation(framework))
      );

      return {
        primaryLanguage,
        frameworks,
        languageAnalysis,
        frameworkAnalysis,
        technicalDebt: await this.assessTechnicalDebt(repositoryDetails),
        scalabilityAssessment: await this.assessScalability(repositoryDetails),
        securityAnalysis: await this.performSecurityAnalysis(repositoryDetails),
        performanceAnalysis: await this.analyzePerformance(repositoryDetails)
      };
    } catch (error) {
      console.error('Error conducting technical analysis:', error);
      throw error;
    }
  }

  async designArchitecture(analysisContext) {
    try {
      // Use PIB Architect agent to design system architecture
      const architecturalRequirements = {
        projectType: analysisContext.results.marketResearch.projectType,
        scalabilityNeeds: analysisContext.results.technicalAnalysis.scalabilityAssessment,
        securityRequirements: analysisContext.results.technicalAnalysis.securityAnalysis,
        performanceRequirements: analysisContext.results.technicalAnalysis.performanceAnalysis
      };

      const architectureDesign = await this.pibAdapter.architect.designArchitecture(
        architecturalRequirements,
        analysisContext.results.repositoryDetails,
        analysisContext.results.technicalAnalysis
      );

      return {
        systemArchitecture: architectureDesign.systemArchitecture,
        componentDesign: architectureDesign.componentDesign,
        dataArchitecture: architectureDesign.dataArchitecture,
        integrationPatterns: architectureDesign.integrationPatterns,
        deploymentStrategy: architectureDesign.deploymentStrategy,
        recommendations: architectureDesign.recommendations
      };
    } catch (error) {
      console.error('Error designing architecture:', error);
      throw error;
    }
  }

  async generateDocumentation(analysisContext) {
    try {
      // Use PIB Analyst agent to generate comprehensive documentation
      const documentationRequirements = {
        projectType: analysisContext.results.marketResearch.projectType,
        architecture: analysisContext.results.architectureDesign,
        technicalSpecs: analysisContext.results.technicalAnalysis
      };

      const documentation = await this.pibAdapter.analyst.generateDocumentation(
        documentationRequirements,
        analysisContext.results.repositoryDetails
      );

      return {
        userGuide: documentation.userGuide,
        apiDocumentation: documentation.apiDocumentation,
        developmentGuide: documentation.developmentGuide,
        deploymentGuide: documentation.deploymentGuide,
        troubleshootingGuide: documentation.troubleshootingGuide
      };
    } catch (error) {
      console.error('Error generating documentation:', error);
      throw error;
    }
  }

  async createProjectBrief(analysisContext) {
    try {
      // Use PIB PM agent to create project brief
      const projectContext = {
        repository: analysisContext.results.repositoryDetails,
        market: analysisContext.results.marketResearch,
        technical: analysisContext.results.technicalAnalysis,
        architecture: analysisContext.results.architectureDesign
      };

      const projectBrief = await this.pibAdapter.projectManager.createProjectBrief(projectContext);

      return {
        executiveSummary: projectBrief.executiveSummary,
        problemStatement: projectBrief.problemStatement,
        solutionOverview: projectBrief.solutionOverview,
        targetAudience: projectBrief.targetAudience,
        businessValue: projectBrief.businessValue,
        timeline: projectBrief.timeline,
        resources: projectBrief.resources,
        riskAssessment: projectBrief.riskAssessment,
        successMetrics: projectBrief.successMetrics
      };
    } catch (error) {
      console.error('Error creating project brief:', error);
      throw error;
    }
  }

  async createImplementationGuide(analysisContext) {
    try {
      // Use PIB Developer agent to create implementation guide
      const implementationContext = {
        architecture: analysisContext.results.architectureDesign,
        technical: analysisContext.results.technicalAnalysis,
        projectBrief: analysisContext.results.projectBrief
      };

      const implementationGuide = await this.pibAdapter.developer.createImplementationGuide(implementationContext);

      return {
        setupInstructions: implementationGuide.setupInstructions,
        developmentWorkflow: implementationGuide.developmentWorkflow,
        codingStandards: implementationGuide.codingStandards,
        testingStrategy: implementationGuide.testingStrategy,
        deploymentPipeline: implementationGuide.deploymentPipeline,
        maintenanceGuide: implementationGuide.maintenanceGuide,
        llmPrompts: implementationGuide.llmPrompts
      };
    } catch (error) {
      console.error('Error creating implementation guide:', error);
      throw error;
    }
  }

  // Helper methods
  async loadSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['perplexityKey', 'githubToken', 'openaiKey'], (result) => {
        resolve(result);
      });
    });
  }

  identifyProjectType(repositoryDetails) {
    const { language, description, topics } = repositoryDetails.basicInfo;
    const files = repositoryDetails.structure.files;

    // Simple project type identification logic
    if (language === 'JavaScript' || language === 'TypeScript') {
      if (files.some(f => f.name === 'package.json')) {
        const packageJson = files.find(f => f.name === 'package.json');
        // Further analysis would be needed to determine exact type
        return 'Web Application';
      }
    } else if (language === 'Python') {
      if (files.some(f => f.name === 'requirements.txt') || files.some(f => f.name === 'setup.py')) {
        return 'Python Application';
      }
    } else if (language === 'Java') {
      return 'Java Application';
    } else if (language === 'Rust') {
      return 'Rust Application';
    }

    return 'Software Project';
  }

  async identifyFrameworks(dependencies) {
    // Analyze dependencies to identify frameworks
    const frameworks = [];
    
    if (dependencies.package_json) {
      const deps = { ...dependencies.package_json.dependencies, ...dependencies.package_json.devDependencies };
      
      if (deps.react) frameworks.push('React');
      if (deps.vue) frameworks.push('Vue.js');
      if (deps.angular) frameworks.push('Angular');
      if (deps.express) frameworks.push('Express.js');
      if (deps.nextjs || deps.next) frameworks.push('Next.js');
      if (deps.nuxt) frameworks.push('Nuxt.js');
    }
    
    return frameworks;
  }

  async calculateCodeComplexity(fileStructure) {
    // Simple complexity calculation based on file count and structure
    const fileCount = fileStructure.length;
    const directoryCount = fileStructure.filter(f => f.type === 'directory').length;
    const nestingLevel = Math.max(...fileStructure.map(f => f.path.split('/').length));

    return {
      fileCount,
      directoryCount,
      nestingLevel,
      complexityScore: Math.min(100, (fileCount * 0.5) + (directoryCount * 2) + (nestingLevel * 5))
    };
  }

  async assessProjectMaturity(repoDetails) {
    const age = new Date() - new Date(repoDetails.created_at);
    const ageInDays = Math.floor(age / (1000 * 60 * 60 * 24));
    
    return {
      ageInDays,
      stars: repoDetails.stargazers_count,
      forks: repoDetails.forks_count,
      issues: repoDetails.open_issues_count,
      maturityScore: Math.min(100, (ageInDays * 0.1) + (repoDetails.stargazers_count * 0.5) + (repoDetails.forks_count * 2))
    };
  }

  async calculateMaintainabilityIndex(repoDetails, fileStructure) {
    // Simple maintainability index calculation
    const hasReadme = fileStructure.some(f => f.name.toLowerCase().includes('readme'));
    const hasLicense = repoDetails.license !== null;
    const hasTests = fileStructure.some(f => f.name.includes('test') || f.path.includes('test'));
    const hasDocumentation = fileStructure.some(f => f.name.includes('docs') || f.path.includes('docs'));

    let score = 0;
    if (hasReadme) score += 25;
    if (hasLicense) score += 20;
    if (hasTests) score += 30;
    if (hasDocumentation) score += 25;

    return {
      score,
      hasReadme,
      hasLicense,
      hasTests,
      hasDocumentation
    };
  }

  async identifyMarketOpportunities(marketContext, competitorAnalysis) {
    // Analyze market context to identify opportunities
    return {
      gaps: ['Identified gap 1', 'Identified gap 2'],
      trends: ['Trend 1', 'Trend 2'],
      differentiators: ['Unique feature 1', 'Unique feature 2']
    };
  }

  async identifyMarketRisks(marketContext, repositoryDetails) {
    // Analyze potential risks
    return {
      technical: ['Technical risk 1', 'Technical risk 2'],
      market: ['Market risk 1', 'Market risk 2'],
      competitive: ['Competitive risk 1', 'Competitive risk 2']
    };
  }

  async assessTechnicalDebt(repositoryDetails) {
    // Simple technical debt assessment
    return {
      score: 30, // 0-100 scale
      issues: ['Legacy code patterns', 'Missing tests', 'Outdated dependencies'],
      recommendations: ['Refactor module X', 'Add unit tests', 'Update dependencies']
    };
  }

  async assessScalability(repositoryDetails) {
    // Simple scalability assessment
    return {
      score: 70, // 0-100 scale
      strengths: ['Modular architecture', 'Good separation of concerns'],
      concerns: ['Potential database bottlenecks', 'Limited caching'],
      recommendations: ['Implement caching layer', 'Consider database optimization']
    };
  }

  async performSecurityAnalysis(repositoryDetails) {
    // Simple security analysis
    return {
      score: 75, // 0-100 scale
      vulnerabilities: ['Potential XSS in component X', 'Missing input validation'],
      recommendations: ['Implement input sanitization', 'Add security headers']
    };
  }

  async analyzePerformance(repositoryDetails) {
    // Simple performance analysis
    return {
      score: 80, // 0-100 scale
      bottlenecks: ['Large bundle size', 'Unoptimized images'],
      recommendations: ['Implement code splitting', 'Optimize images', 'Add performance monitoring']
    };
  }
}