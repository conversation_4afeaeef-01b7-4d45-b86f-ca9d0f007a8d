/**
 * Security Manager
 * Handles security operations, API key management, and secure communications
 */

export class SecurityManager {
  constructor() {
    this.encryptionKey = null;
    this.allowedOrigins = [
      'https://api.perplexity.ai',
      'https://api.context7.com',
      'https://api.github.com',
      'https://api.openai.com'
    ];
    this.rateLimits = new Map();
    this.securityHeaders = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
    };
  }

  async initialize() {
    try {
      // Initialize encryption key
      this.encryptionKey = await this.generateOrRetrieveKey();
      
      // Set up CSP monitoring
      this.setupCSPMonitoring();
      
      // Initialize rate limiting
      this.setupRateLimiting();
      
      console.log('Security Manager initialized successfully');
    } catch (error) {
      console.error('Error initializing Security Manager:', error);
      throw error;
    }
  }

  async generateOrRetrieveKey() {
    try {
      // Try to get existing key from Chrome storage
      const result = await chrome.storage.local.get(['securityKey']);
      
      if (result.securityKey) {
        return await this.importKey(result.securityKey);
      }
      
      // Generate new key
      const key = await this.generateEncryptionKey();
      const exportedKey = await this.exportKey(key);
      
      // Store the key securely
      await chrome.storage.local.set({ securityKey: exportedKey });
      
      return key;
    } catch (error) {
      console.error('Error managing encryption key:', error);
      throw error;
    }
  }

  async generateEncryptionKey() {
    return await crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  async exportKey(key) {
    const exported = await crypto.subtle.exportKey('raw', key);
    return Array.from(new Uint8Array(exported));
  }

  async importKey(keyData) {
    const keyBytes = new Uint8Array(keyData);
    return await crypto.subtle.importKey(
      'raw',
      keyBytes,
      { name: 'AES-GCM' },
      false,
      ['encrypt', 'decrypt']
    );
  }

  async encryptData(data) {
    try {
      if (!this.encryptionKey) {
        await this.initialize();
      }

      const encoder = new TextEncoder();
      const dataBytes = encoder.encode(JSON.stringify(data));
      
      const iv = crypto.getRandomValues(new Uint8Array(12));
      
      const encrypted = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        this.encryptionKey,
        dataBytes
      );

      return {
        iv: Array.from(iv),
        data: Array.from(new Uint8Array(encrypted))
      };
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  async decryptData(encryptedData) {
    try {
      if (!this.encryptionKey) {
        await this.initialize();
      }

      const iv = new Uint8Array(encryptedData.iv);
      const data = new Uint8Array(encryptedData.data);

      const decrypted = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        this.encryptionKey,
        data
      );

      const decoder = new TextDecoder();
      return JSON.parse(decoder.decode(decrypted));
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  validateAPIKey(apiKey, service) {
    if (!apiKey || typeof apiKey !== 'string') {
      return { valid: false, error: 'API key must be a non-empty string' };
    }

    const validations = {
      perplexity: {
        pattern: /^pplx-[a-zA-Z0-9]{32,}$/,
        minLength: 37,
        maxLength: 100
      },
      github: {
        pattern: /^(ghp_[a-zA-Z0-9]{36}|gho_[a-zA-Z0-9]{36}|ghu_[a-zA-Z0-9]{36}|ghs_[a-zA-Z0-9]{36}|ghr_[a-zA-Z0-9]{36})$/,
        minLength: 40,
        maxLength: 40
      },
      openai: {
        pattern: /^sk-[a-zA-Z0-9]{32,}$/,
        minLength: 35,
        maxLength: 100
      }
    };

    const validation = validations[service];
    if (!validation) {
      return { valid: false, error: `Unknown service: ${service}` };
    }

    if (apiKey.length < validation.minLength || apiKey.length > validation.maxLength) {
      return { 
        valid: false, 
        error: `API key length must be between ${validation.minLength} and ${validation.maxLength} characters` 
      };
    }

    if (!validation.pattern.test(apiKey)) {
      return { 
        valid: false, 
        error: `API key format is invalid for ${service}` 
      };
    }

    return { valid: true };
  }

  sanitizeInput(input) {
    if (typeof input !== 'string') {
      return input;
    }

    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  validateURL(url) {
    try {
      const urlObj = new URL(url);
      
      // Check if it's a valid HTTP/HTTPS URL
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return { valid: false, error: 'URL must use HTTP or HTTPS protocol' };
      }

      // Check against allowed origins for API calls
      const isAllowedOrigin = this.allowedOrigins.some(origin => 
        urlObj.origin === origin
      );

      if (!isAllowedOrigin) {
        return { valid: false, error: 'URL origin not in allowed list' };
      }

      return { valid: true, url: urlObj };
    } catch (error) {
      return { valid: false, error: 'Invalid URL format' };
    }
  }

  async secureAPICall(url, options = {}) {
    // Validate URL
    const urlValidation = this.validateURL(url);
    if (!urlValidation.valid) {
      throw new Error(`Invalid URL: ${urlValidation.error}`);
    }

    // Check rate limits
    const rateLimitCheck = this.checkRateLimit(urlValidation.url.origin);
    if (!rateLimitCheck.allowed) {
      throw new Error(`Rate limit exceeded for ${urlValidation.url.origin}. Try again in ${rateLimitCheck.resetIn}ms`);
    }

    // Apply security headers
    const secureOptions = {
      ...options,
      headers: {
        ...this.securityHeaders,
        ...options.headers
      }
    };

    try {
      // Make the API call
      const response = await fetch(url, secureOptions);
      
      // Update rate limit tracking
      this.updateRateLimit(urlValidation.url.origin, response.headers);
      
      // Validate response
      if (!response.ok) {
        throw new Error(`API call failed: ${response.status} ${response.statusText}`);
      }

      // Parse and validate response
      const data = await response.json();
      return this.validateAPIResponse(data);
    } catch (error) {
      console.error('Secure API call error:', error);
      throw error;
    }
  }

  validateAPIResponse(data) {
    // Basic validation to prevent malicious responses
    if (typeof data !== 'object' || data === null) {
      throw new Error('Invalid API response format');
    }

    // Remove any potentially dangerous properties
    const sanitizedData = this.sanitizeObject(data);
    
    return sanitizedData;
  }

  sanitizeObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      // Skip potentially dangerous keys
      if (key.startsWith('__') || key.includes('constructor') || key.includes('prototype')) {
        continue;
      }

      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeInput(value);
      } else if (typeof value === 'object') {
        sanitized[key] = this.sanitizeObject(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  setupCSPMonitoring() {
    // Monitor for CSP violations
    document.addEventListener('securitypolicyviolation', (event) => {
      console.warn('CSP Violation:', {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy
      });
      
      // Report to background script
      chrome.runtime.sendMessage({
        type: 'CSP_VIOLATION',
        data: {
          blockedURI: event.blockedURI,
          violatedDirective: event.violatedDirective,
          timestamp: new Date().toISOString()
        }
      });
    });
  }

  setupRateLimiting() {
    // Initialize rate limits for each allowed origin
    this.allowedOrigins.forEach(origin => {
      this.rateLimits.set(origin, {
        requests: 0,
        windowStart: Date.now(),
        limit: 100, // requests per hour
        window: 60 * 60 * 1000 // 1 hour
      });
    });
  }

  checkRateLimit(origin) {
    const rateLimit = this.rateLimits.get(origin);
    if (!rateLimit) {
      return { allowed: false, error: 'Unknown origin' };
    }

    const now = Date.now();
    
    // Reset window if expired
    if (now - rateLimit.windowStart > rateLimit.window) {
      rateLimit.requests = 0;
      rateLimit.windowStart = now;
    }

    if (rateLimit.requests >= rateLimit.limit) {
      const resetIn = rateLimit.window - (now - rateLimit.windowStart);
      return { allowed: false, resetIn };
    }

    return { allowed: true };
  }

  updateRateLimit(origin, responseHeaders) {
    const rateLimit = this.rateLimits.get(origin);
    if (!rateLimit) return;

    // Increment request count
    rateLimit.requests++;

    // Update based on server-provided rate limit headers
    const remaining = responseHeaders.get('x-ratelimit-remaining');
    const resetTime = responseHeaders.get('x-ratelimit-reset');
    
    if (remaining && resetTime) {
      const resetTimeMs = parseInt(resetTime) * 1000;
      const now = Date.now();
      
      if (resetTimeMs > now) {
        rateLimit.limit = Math.max(rateLimit.requests, parseInt(remaining));
        rateLimit.windowStart = now;
        rateLimit.window = resetTimeMs - now;
      }
    }
  }

  async hashData(data) {
    const encoder = new TextEncoder();
    const dataBytes = encoder.encode(JSON.stringify(data));
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBytes);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  async verifyIntegrity(data, expectedHash) {
    const actualHash = await this.hashData(data);
    return actualHash === expectedHash;
  }

  generateNonce() {
    return crypto.getRandomValues(new Uint8Array(16));
  }

  async secureStorage(key, value, encrypt = true) {
    try {
      const dataToStore = encrypt ? await this.encryptData(value) : value;
      
      // Add integrity check
      const hash = await this.hashData(dataToStore);
      
      await chrome.storage.local.set({
        [key]: dataToStore,
        [`${key}_hash`]: hash
      });
      
      return true;
    } catch (error) {
      console.error('Secure storage error:', error);
      return false;
    }
  }

  async secureRetrieve(key, decrypt = true) {
    try {
      const result = await chrome.storage.local.get([key, `${key}_hash`]);
      
      if (!result[key]) {
        return null;
      }

      // Verify integrity
      const expectedHash = result[`${key}_hash`];
      if (expectedHash) {
        const isValid = await this.verifyIntegrity(result[key], expectedHash);
        if (!isValid) {
          console.warn('Data integrity check failed for key:', key);
          return null;
        }
      }

      return decrypt ? await this.decryptData(result[key]) : result[key];
    } catch (error) {
      console.error('Secure retrieve error:', error);
      return null;
    }
  }

  async clearSecureStorage() {
    try {
      const result = await chrome.storage.local.get(null);
      const keysToRemove = [];
      
      // Find all security-related keys
      Object.keys(result).forEach(key => {
        if (key === 'securityKey' || key.endsWith('_hash')) {
          keysToRemove.push(key);
        }
      });
      
      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
      }
      
      // Reset encryption key
      this.encryptionKey = null;
      
      return true;
    } catch (error) {
      console.error('Clear secure storage error:', error);
      return false;
    }
  }

  generateSecureToken(length = 32) {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  async validateSessionToken(token) {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // Basic token validation
    if (token.length < 32 || token.length > 128) {
      return false;
    }

    // Check against stored tokens
    const storedTokens = await this.secureRetrieve('sessionTokens', true) || [];
    return storedTokens.includes(token);
  }

  async createSessionToken() {
    const token = this.generateSecureToken();
    
    // Store token securely
    const storedTokens = await this.secureRetrieve('sessionTokens', true) || [];
    storedTokens.push(token);
    
    // Keep only last 10 tokens
    if (storedTokens.length > 10) {
      storedTokens.splice(0, storedTokens.length - 10);
    }
    
    await this.secureStorage('sessionTokens', storedTokens, true);
    
    return token;
  }

  async revokeSessionToken(token) {
    const storedTokens = await this.secureRetrieve('sessionTokens', true) || [];
    const filteredTokens = storedTokens.filter(t => t !== token);
    
    await this.secureStorage('sessionTokens', filteredTokens, true);
    
    return true;
  }

  getSecurityReport() {
    const report = {
      timestamp: new Date().toISOString(),
      rateLimits: {},
      securityMetrics: {
        encryptionEnabled: !!this.encryptionKey,
        allowedOrigins: this.allowedOrigins.length,
        securityHeaders: Object.keys(this.securityHeaders).length
      }
    };

    // Add rate limit information
    this.rateLimits.forEach((limit, origin) => {
      report.rateLimits[origin] = {
        requests: limit.requests,
        limit: limit.limit,
        windowStart: new Date(limit.windowStart).toISOString(),
        resetIn: limit.window - (Date.now() - limit.windowStart)
      };
    });

    return report;
  }

  async audit() {
    const auditResults = {
      timestamp: new Date().toISOString(),
      checks: []
    };

    // Check encryption key
    auditResults.checks.push({
      name: 'Encryption Key',
      status: this.encryptionKey ? 'PASS' : 'FAIL',
      details: this.encryptionKey ? 'Encryption key is present' : 'Encryption key is missing'
    });

    // Check storage integrity
    try {
      const testData = { test: 'data' };
      await this.secureStorage('audit_test', testData, true);
      const retrieved = await this.secureRetrieve('audit_test', true);
      
      auditResults.checks.push({
        name: 'Storage Integrity',
        status: JSON.stringify(retrieved) === JSON.stringify(testData) ? 'PASS' : 'FAIL',
        details: 'Storage encryption and integrity check'
      });
      
      // Cleanup test data
      await chrome.storage.local.remove(['audit_test', 'audit_test_hash']);
    } catch (error) {
      auditResults.checks.push({
        name: 'Storage Integrity',
        status: 'FAIL',
        details: `Storage test failed: ${error.message}`
      });
    }

    // Check rate limits
    const rateLimitHealthy = Array.from(this.rateLimits.values())
      .every(limit => limit.requests < limit.limit);
    
    auditResults.checks.push({
      name: 'Rate Limits',
      status: rateLimitHealthy ? 'PASS' : 'WARN',
      details: rateLimitHealthy ? 'All rate limits healthy' : 'Some rate limits approaching threshold'
    });

    return auditResults;
  }
}