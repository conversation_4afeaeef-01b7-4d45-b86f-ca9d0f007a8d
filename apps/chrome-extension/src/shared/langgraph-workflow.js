/**
 * Gemini AI Workflow Manager
 * Manages complex AI workflows using Gemini API for repository analysis
 * Browser-compatible implementation for Chrome extensions
 */

export class GeminiWorkflow {
  constructor(apiKey = null) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1/models';
    this.model = 'gemini-2.0-flash';
    this.currentState = null;
    this.workflowSteps = [];
    this.isInitialized = false;
    this.requestCount = 0;
    this.lastRequestTime = 0;
    this.rateLimitDelay = 1000; // 1 second between requests
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Validate API key
      if (!this.apiKey) {
        throw new Error('Gemini API key is required for workflow initialization');
      }

      // Test API connectivity
      await this.testApiConnection();
      
      // Initialize the workflow structure
      this.graph = await this.createRepositoryAnalysisWorkflow();
      this.isInitialized = true;
      console.log('Gemini AI workflow initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Gemini AI workflow:', error);
      throw error;
    }
  }

  async testApiConnection() {
    try {
      const response = await this.callGeminiAPI('Test connection', { maxTokens: 10 });
      console.log('Gemini API connection successful');
      return true;
    } catch (error) {
      throw new Error(`Gemini API connection failed: ${error.message}`);
    }
  }

  async callGeminiAPI(prompt, options = {}) {
    // Rate limiting
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.rateLimitDelay) {
      await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay - timeSinceLastRequest));
    }

    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: options.temperature || 0.7,
        topK: options.topK || 40,
        topP: options.topP || 0.95,
        maxOutputTokens: options.maxTokens || 8192,
      }
    };

    try {
      const response = await fetch(`${this.baseUrl}/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response from Gemini API');
      }

      this.requestCount++;
      this.lastRequestTime = Date.now();

      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('Gemini API call failed:', error);
      throw error;
    }
  }

  async createRepositoryAnalysisWorkflow() {
    // Gemini AI-powered workflow implementation
    const workflow = {
      steps: [
        'initialize',
        'analyze_repository',
        'conduct_market_research',
        'perform_technical_analysis',
        'design_architecture',
        'generate_documentation',
        'create_project_brief',
        'create_implementation_guide',
        'finalize'
      ],
      currentStepIndex: 0,
      
      async invoke(initialState) {
        let state = { ...initialState };
        
        // Add context accumulation for Gemini's large context window
        state.cumulativeContext = this.context.buildInitialContext(state.repository);
        
        for (let i = 0; i < this.steps.length; i++) {
          const stepName = this.steps[i];
          const stepMethod = this.getStepMethod(stepName);
          
          if (stepMethod) {
            try {
              state = await stepMethod.call(this.context, state);
              state.currentStepIndex = i + 1;
              state.progress = ((i + 1) / this.steps.length) * 100;
              
              // Update cumulative context for next steps
              state.cumulativeContext = this.context.updateCumulativeContext(state, stepName);
              
              // Emit progress event
              this.context.emitEvent('step_complete', { step: stepName, progress: state.progress });
            } catch (error) {
              console.error(`Error in step ${stepName}:`, error);
              state.errors.push({ step: stepName, error: error.message });
              this.context.emitEvent('step_error', { step: stepName, error: error.message });
              
              // Continue with next step despite error
              continue;
            }
          }
        }
        
        return state;
      },
      
      getStepMethod(stepName) {
        const methodMap = {
          'initialize': this.context.initializeNode,
          'analyze_repository': this.context.analyzeRepositoryNode,
          'conduct_market_research': this.context.conductMarketResearchNode,
          'perform_technical_analysis': this.context.performTechnicalAnalysisNode,
          'design_architecture': this.context.designArchitectureNode,
          'generate_documentation': this.context.generateDocumentationNode,
          'create_project_brief': this.context.createProjectBriefNode,
          'create_implementation_guide': this.context.createImplementationGuideNode,
          'finalize': this.context.finalizeNode
        };
        
        return methodMap[stepName];
      },
      
      context: this
    };
    
    return workflow;
  }

  buildInitialContext(repository) {
    return `
# Repository Analysis Context

**Repository:** ${repository.owner}/${repository.repo}
**Analysis Date:** ${new Date().toISOString()}

This is a comprehensive analysis of the GitHub repository. Each step will build upon previous findings to create complete documentation.

## Analysis Goals:
1. Understand the repository structure and purpose
2. Research market context and competitive landscape
3. Analyze technical implementation and architecture
4. Design optimal system architecture
5. Generate comprehensive documentation
6. Create project management briefs
7. Provide implementation guidance with LLM prompts

---
`;
  }

  updateCumulativeContext(state, completedStep) {
    let context = state.cumulativeContext + `\n## ${completedStep.toUpperCase()} COMPLETED\n`;
    
    // Add relevant findings to context
    switch (completedStep) {
      case 'analyze_repository':
        if (state.repositoryDetails) {
          context += `**Repository Analysis:**\n${JSON.stringify(state.repositoryDetails, null, 2)}\n\n`;
        }
        break;
      case 'conduct_market_research':
        if (state.marketResearch) {
          context += `**Market Research:**\n${JSON.stringify(state.marketResearch, null, 2)}\n\n`;
        }
        break;
      case 'perform_technical_analysis':
        if (state.technicalAnalysis) {
          context += `**Technical Analysis:**\n${JSON.stringify(state.technicalAnalysis, null, 2)}\n\n`;
        }
        break;
      case 'design_architecture':
        if (state.architectureDesign) {
          context += `**Architecture Design:**\n${JSON.stringify(state.architectureDesign, null, 2)}\n\n`;
        }
        break;
    }
    
    // Keep context under 500k characters to stay within Gemini limits
    if (context.length > 500000) {
      context = context.substring(context.length - 500000);
    }
    
    return context;
  }

  async executeWorkflow(repository, options = {}) {
    await this.initialize();

    try {
      const initialState = {
        repository,
        repositoryDetails: null,
        marketResearch: null,
        technicalAnalysis: null,
        architectureDesign: null,
        documentation: null,
        projectBrief: null,
        implementationGuide: null,
        currentStep: 'initialization',
        completedSteps: [],
        errors: [],
        progress: 0,
        agents: options.agents || {},
        options: options
      };

      const result = await this.graph.invoke(initialState);
      return result;
    } catch (error) {
      console.error('Error executing workflow:', error);
      throw error;
    }
  }

  // Workflow node implementations
  async initializeNode(state) {
    console.log('Initializing workflow...');
    
    return {
      ...state,
      currentStep: 'initialization',
      progress: 5,
      completedSteps: ['initialization'],
      startTime: new Date().toISOString()
    };
  }

  async analyzeRepositoryNode(state) {
    console.log('Analyzing repository structure...');
    
    try {
      const { repository, agents } = state;
      
      // Simulate repository analysis
      const repositoryDetails = await this.simulateRepositoryAnalysis(repository);
      
      return {
        ...state,
        repositoryDetails,
        currentStep: 'repository_analysis',
        progress: 15,
        completedSteps: [...state.completedSteps, 'repository_analysis']
      };
    } catch (error) {
      console.error('Error in repository analysis:', error);
      return {
        ...state,
        errors: [...state.errors, { step: 'repository_analysis', error: error.message }],
        currentStep: 'repository_analysis_error'
      };
    }
  }

  async conductMarketResearchNode(state) {
    console.log('Conducting market research...');
    
    try {
      const { repository, repositoryDetails, agents } = state;
      
      // Simulate market research
      const marketResearch = await this.simulateMarketResearch(repository, repositoryDetails);
      
      return {
        ...state,
        marketResearch,
        currentStep: 'market_research',
        progress: 30,
        completedSteps: [...state.completedSteps, 'market_research']
      };
    } catch (error) {
      console.error('Error in market research:', error);
      return {
        ...state,
        errors: [...state.errors, { step: 'market_research', error: error.message }],
        currentStep: 'market_research_error'
      };
    }
  }

  async performTechnicalAnalysisNode(state) {
    console.log('Performing technical analysis...');
    
    try {
      const { repository, repositoryDetails, agents } = state;
      
      // Simulate technical analysis
      const technicalAnalysis = await this.simulateTechnicalAnalysis(repository, repositoryDetails);
      
      return {
        ...state,
        technicalAnalysis,
        currentStep: 'technical_analysis',
        progress: 45,
        completedSteps: [...state.completedSteps, 'technical_analysis']
      };
    } catch (error) {
      console.error('Error in technical analysis:', error);
      return {
        ...state,
        errors: [...state.errors, { step: 'technical_analysis', error: error.message }],
        currentStep: 'technical_analysis_error'
      };
    }
  }

  async designArchitectureNode(state) {
    console.log('Designing architecture...');
    
    try {
      const { repository, repositoryDetails, marketResearch, technicalAnalysis, agents } = state;
      
      // Simulate architecture design
      const architectureDesign = await this.simulateArchitectureDesign(
        repository, 
        repositoryDetails, 
        marketResearch, 
        technicalAnalysis
      );
      
      return {
        ...state,
        architectureDesign,
        currentStep: 'architecture_design',
        progress: 60,
        completedSteps: [...state.completedSteps, 'architecture_design']
      };
    } catch (error) {
      console.error('Error in architecture design:', error);
      return {
        ...state,
        errors: [...state.errors, { step: 'architecture_design', error: error.message }],
        currentStep: 'architecture_design_error'
      };
    }
  }

  async generateDocumentationNode(state) {
    console.log('Generating documentation with AI...');
    
    try {
      const { repository, repositoryDetails, marketResearch, technicalAnalysis, architectureDesign, cumulativeContext } = state;
      
      // Generate comprehensive documentation using Gemini AI
      const documentation = await this.generateDocumentationWithAI(
        repository, 
        repositoryDetails, 
        marketResearch, 
        technicalAnalysis, 
        architectureDesign,
        cumulativeContext
      );
      
      return {
        ...state,
        documentation,
        currentStep: 'documentation_generation',
        progress: 75,
        completedSteps: [...state.completedSteps, 'documentation_generation']
      };
    } catch (error) {
      console.error('Error in documentation generation:', error);
      return {
        ...state,
        errors: [...state.errors, { step: 'documentation_generation', error: error.message }],
        currentStep: 'documentation_generation_error'
      };
    }
  }

  async createProjectBriefNode(state) {
    console.log('Creating project brief...');
    
    try {
      const { repository, repositoryDetails, marketResearch, technicalAnalysis, architectureDesign, agents } = state;
      
      // Simulate project brief creation
      const projectBrief = await this.simulateProjectBriefCreation(
        repository, 
        repositoryDetails, 
        marketResearch, 
        technicalAnalysis, 
        architectureDesign
      );
      
      return {
        ...state,
        projectBrief,
        currentStep: 'project_brief',
        progress: 85,
        completedSteps: [...state.completedSteps, 'project_brief']
      };
    } catch (error) {
      console.error('Error in project brief creation:', error);
      return {
        ...state,
        errors: [...state.errors, { step: 'project_brief', error: error.message }],
        currentStep: 'project_brief_error'
      };
    }
  }

  async createImplementationGuideNode(state) {
    console.log('Creating implementation guide with AI...');
    
    try {
      const { repository, repositoryDetails, marketResearch, technicalAnalysis, architectureDesign, projectBrief, cumulativeContext } = state;
      
      // Generate implementation guide with optimized LLM prompts using Gemini AI
      const implementationGuide = await this.generateImplementationGuideWithAI(
        repository, 
        repositoryDetails, 
        marketResearch, 
        technicalAnalysis, 
        architectureDesign, 
        projectBrief,
        cumulativeContext
      );
      
      return {
        ...state,
        implementationGuide,
        currentStep: 'implementation_guide',
        progress: 95,
        completedSteps: [...state.completedSteps, 'implementation_guide']
      };
    } catch (error) {
      console.error('Error in implementation guide creation:', error);
      return {
        ...state,
        errors: [...state.errors, { step: 'implementation_guide', error: error.message }],
        currentStep: 'implementation_guide_error'
      };
    }
  }

  async finalizeNode(state) {
    console.log('Finalizing workflow...');
    
    return {
      ...state,
      currentStep: 'completed',
      progress: 100,
      completedSteps: [...state.completedSteps, 'finalization'],
      completedAt: new Date().toISOString(),
      duration: new Date() - new Date(state.startTime)
    };
  }

  // Simulation methods for workflow steps
  async simulateRepositoryAnalysis(repository) {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      basicInfo: {
        name: repository.repo,
        fullName: `${repository.owner}/${repository.repo}`,
        description: 'Repository analysis completed',
        language: 'JavaScript',
        size: 1024,
        stars: 100,
        forks: 25,
        issues: 5,
        license: 'MIT',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        topics: ['javascript', 'web', 'development']
      },
      structure: {
        files: [
          { name: 'package.json', type: 'file', path: 'package.json' },
          { name: 'src', type: 'directory', path: 'src' },
          { name: 'README.md', type: 'file', path: 'README.md' }
        ],
        dependencies: {
          production: ['react', 'express', 'lodash'],
          development: ['jest', 'eslint', 'webpack']
        }
      },
      metrics: {
        codeComplexity: { score: 65, level: 'moderate' },
        projectMaturity: { score: 75, level: 'mature' },
        maintainabilityIndex: { score: 80, level: 'good' }
      }
    };
  }

  async simulateMarketResearch(repository, repositoryDetails) {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    return {
      projectType: 'Web Application',
      marketContext: {
        size: '$50 billion',
        growth: '15% annually',
        trends: ['Progressive Web Apps', 'Microservices', 'AI Integration']
      },
      competitorAnalysis: {
        directCompetitors: ['Competitor A', 'Competitor B', 'Competitor C'],
        indirectCompetitors: ['Alternative X', 'Alternative Y'],
        marketPosition: 'Emerging player with unique features'
      },
      technologyTrends: {
        adoption: 'Growing rapidly',
        ecosystem: 'Mature and stable',
        futureOutlook: 'Positive with continued growth'
      },
      opportunities: {
        gaps: ['Better developer experience', 'Improved performance'],
        trends: ['Serverless adoption', 'Edge computing'],
        differentiators: ['Unique architecture', 'Superior documentation']
      },
      risks: {
        technical: ['Technology obsolescence', 'Scalability challenges'],
        market: ['Increased competition', 'Market saturation'],
        competitive: ['Large player entry', 'Price competition']
      }
    };
  }

  async simulateTechnicalAnalysis(repository, repositoryDetails) {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 1200));
    
    return {
      primaryLanguage: 'JavaScript',
      frameworks: ['React', 'Express', 'Node.js'],
      languageAnalysis: {
        popularity: 'Very high',
        ecosystem: 'Extensive',
        learning: 'Moderate curve',
        support: 'Excellent'
      },
      frameworkAnalysis: [
        {
          name: 'React',
          version: '18.x',
          maturity: 'Mature',
          community: 'Large',
          recommendation: 'Highly recommended'
        },
        {
          name: 'Express',
          version: '4.x',
          maturity: 'Mature',
          community: 'Large',
          recommendation: 'Solid choice'
        }
      ],
      technicalDebt: {
        score: 25,
        issues: ['Legacy code patterns', 'Missing tests'],
        recommendations: ['Refactor core modules', 'Add comprehensive tests']
      },
      scalabilityAssessment: {
        score: 75,
        strengths: ['Modular architecture', 'Efficient data flow'],
        concerns: ['Database bottlenecks', 'Limited caching'],
        recommendations: ['Implement caching', 'Optimize database queries']
      },
      securityAnalysis: {
        score: 80,
        vulnerabilities: ['Minor XSS risks', 'Outdated dependencies'],
        recommendations: ['Update dependencies', 'Add security headers']
      },
      performanceAnalysis: {
        score: 70,
        bottlenecks: ['Large bundle size', 'Unoptimized images'],
        recommendations: ['Code splitting', 'Image optimization']
      }
    };
  }

  async simulateArchitectureDesign(repository, repositoryDetails, marketResearch, technicalAnalysis) {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 1800));
    
    return {
      systemArchitecture: {
        pattern: 'Microservices',
        layers: ['API Gateway', 'Service Layer', 'Data Layer'],
        components: ['Frontend', 'Backend Services', 'Database', 'Cache'],
        scalability: 'Horizontal scaling with load balancers'
      },
      componentDesign: {
        core: ['User Service', 'API Gateway', 'Data Service'],
        supporting: ['Auth Service', 'Logging', 'Monitoring'],
        external: ['Database', 'Cache', 'CDN']
      },
      dataArchitecture: {
        storage: {
          primary: 'PostgreSQL',
          cache: 'Redis',
          search: 'Elasticsearch'
        },
        patterns: {
          access: 'Repository Pattern',
          consistency: 'Eventual Consistency',
          backup: 'Automated Backups'
        }
      },
      integrationPatterns: {
        api: 'RESTful APIs',
        messaging: 'Event-driven',
        authentication: 'JWT tokens'
      },
      deploymentStrategy: {
        containerization: 'Docker',
        orchestration: 'Kubernetes',
        cicd: 'GitHub Actions',
        monitoring: 'Prometheus'
      },
      recommendations: [
        'Use microservices for scalability',
        'Implement proper caching strategy',
        'Add comprehensive monitoring',
        'Plan for disaster recovery'
      ]
    };
  }

  async generateDocumentationWithAI(repository, repositoryDetails, marketResearch, technicalAnalysis, architectureDesign, cumulativeContext) {
    console.log('Generating comprehensive documentation with Gemini AI...');
    
    const prompt = `${cumulativeContext}

# DOCUMENTATION GENERATION TASK

You are an expert technical writer creating comprehensive documentation for the ${repository.owner}/${repository.repo} repository.

Based on all the previous analysis, generate detailed documentation including:

## Required Documentation Types:

### 1. User Guide
- Getting Started section with clear setup instructions
- Feature documentation with examples
- Common use cases and workflows
- FAQ addressing typical user questions
- Troubleshooting common issues

### 2. API Documentation
- Complete API reference with endpoints
- Request/response examples for each endpoint
- Authentication and authorization details
- Error codes and handling
- SDK examples in multiple languages

### 3. Development Guide
- Development environment setup
- Code organization and architecture
- Coding standards and best practices
- Testing strategy and guidelines
- Contribution guidelines

### 4. Deployment Guide
- Infrastructure requirements
- Deployment strategies (Docker, Kubernetes, Cloud)
- Environment configuration
- Monitoring and logging setup
- Backup and disaster recovery

### 5. Architecture Documentation
- System architecture diagrams (describe in text)
- Component interactions
- Data flow and storage patterns
- Security considerations
- Scalability and performance

Return your response as a JSON object with the following structure:
{
  "userGuide": {
    "title": "User Guide",
    "content": "Full markdown content here",
    "sections": ["Getting Started", "Features", "Examples", "FAQ"],
    "completeness": 95
  },
  "apiDocumentation": {
    "title": "API Documentation", 
    "content": "Full markdown content here",
    "endpoints": 15,
    "examples": 30,
    "completeness": 90
  },
  "developmentGuide": {
    "title": "Development Guide",
    "content": "Full markdown content here", 
    "sections": ["Setup", "Architecture", "Testing", "Deployment"],
    "completeness": 85
  },
  "deploymentGuide": {
    "title": "Deployment Guide",
    "content": "Full markdown content here",
    "platforms": ["Docker", "Kubernetes", "Cloud"],
    "completeness": 80
  },
  "architectureDocumentation": {
    "title": "Architecture Documentation",
    "content": "Full markdown content here",
    "diagrams": ["System Overview", "Component Diagram", "Data Flow"],
    "completeness": 88
  }
}

Make the documentation comprehensive, professional, and actionable. Include specific examples based on the repository analysis.`;

    try {
      const response = await this.callGeminiAPI(prompt, { 
        maxTokens: 8192,
        temperature: 0.3  // Lower temperature for more consistent documentation
      });
      
      // Parse JSON response
      const documentation = JSON.parse(response);
      return documentation;
      
    } catch (error) {
      console.error('Error generating documentation with AI:', error);
      
      // Fallback to structured template if AI fails
      return {
        userGuide: {
          title: 'User Guide',
          content: `# User Guide for ${repository.repo}\n\n## Getting Started\n\nDetailed setup instructions will be provided based on repository analysis.\n\n## Features\n\nKey features and functionality overview.\n\n## Examples\n\nPractical examples and use cases.\n\n## FAQ\n\nCommon questions and answers.`,
          sections: ['Getting Started', 'Features', 'Examples', 'FAQ'],
          completeness: 75
        },
        apiDocumentation: {
          title: 'API Documentation',
          content: `# API Documentation\n\nComprehensive API reference and examples.`,
          endpoints: 0,
          examples: 0,
          completeness: 60
        },
        developmentGuide: {
          title: 'Development Guide', 
          content: `# Development Guide\n\nDevelopment setup and contribution guidelines.`,
          sections: ['Setup', 'Architecture', 'Testing', 'Deployment'],
          completeness: 65
        },
        deploymentGuide: {
          title: 'Deployment Guide',
          content: `# Deployment Guide\n\nDeployment instructions and best practices.`,
          platforms: ['Docker', 'Kubernetes', 'Cloud'],
          completeness: 70
        },
        architectureDocumentation: {
          title: 'Architecture Documentation',
          content: `# Architecture Documentation\n\nSystem architecture and design decisions.`,
          diagrams: ['System Overview', 'Component Diagram'],
          completeness: 60
        }
      };
    }
  }

  async simulateProjectBriefCreation(repository, repositoryDetails, marketResearch, technicalAnalysis, architectureDesign) {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 1600));
    
    return {
      executiveSummary: {
        title: `${repository.repo} Project Brief`,
        overview: 'Comprehensive project analysis and recommendations',
        keyPoints: ['Strong market opportunity', 'Solid technical foundation', 'Clear implementation path']
      },
      problemStatement: {
        problem: 'Market gap in developer-friendly solutions',
        impact: 'Reduced productivity and adoption',
        opportunity: 'Capture underserved market segment'
      },
      solutionOverview: {
        approach: 'Modern, scalable architecture',
        features: ['High performance', 'Easy integration', 'Comprehensive docs'],
        differentiators: ['Superior UX', 'Strong community', 'Proven reliability']
      },
      targetAudience: {
        primary: 'Software developers',
        secondary: 'Technical teams',
        size: 'Large and growing'
      },
      businessValue: {
        metrics: ['User growth', 'Community engagement', 'Market share'],
        benefits: ['Increased productivity', 'Better user experience', 'Market leadership']
      },
      timeline: {
        phases: ['Foundation', 'Development', 'Testing', 'Launch'],
        duration: '16 weeks',
        milestones: 12
      },
      resources: {
        team: 5,
        budget: 'Medium',
        infrastructure: 'Cloud-based'
      },
      riskAssessment: {
        technical: 'Low to medium',
        market: 'Medium',
        operational: 'Low'
      },
      successMetrics: {
        adoption: 'User growth rate',
        quality: 'User satisfaction',
        technical: 'Performance metrics'
      }
    };
  }

  async generateImplementationGuideWithAI(repository, repositoryDetails, marketResearch, technicalAnalysis, architectureDesign, projectBrief, cumulativeContext) {
    console.log('Generating implementation guide with optimized LLM prompts...');
    
    const prompt = `${cumulativeContext}

# IMPLEMENTATION GUIDE & LLM PROMPTS GENERATION

You are a senior technical lead creating a comprehensive implementation guide for ${repository.owner}/${repository.repo}.

Based on all previous analysis, create a detailed implementation guide that includes:

## 1. Setup Instructions
- Complete development environment setup
- Prerequisites and dependencies
- Step-by-step installation guide
- Environment configuration
- Verification steps

## 2. Development Workflow
- Git workflow and branching strategy
- Code review process
- Testing approach and requirements
- Continuous integration setup

## 3. Technical Implementation Strategy
- Architecture implementation approach
- Technology stack setup and configuration
- Database design and migration strategy
- API design and implementation
- Frontend development approach

## 4. LLM PROMPTS FOR BUILDING THIS PROJECT
Create optimized prompts that a developer could use with AI assistants to build this exact project:

### System Prompt (for AI assistant context)
### Implementation Prompts (specific tasks)
- Setup and infrastructure prompts
- Backend development prompts  
- Frontend development prompts
- Database and API prompts
- Testing and deployment prompts

Each prompt should be:
- Specific and actionable
- Include relevant context from the analysis
- Reference the exact technologies identified
- Include architecture decisions made
- Specify coding standards and patterns

## 5. Quality Assurance
- Testing strategy and frameworks
- Code quality standards
- Performance benchmarks
- Security requirements

Return as JSON with this structure:
{
  "setupInstructions": {
    "prerequisites": ["detailed list"],
    "steps": ["step by step instructions"],
    "verification": ["how to verify setup"]
  },
  "developmentWorkflow": {
    "gitFlow": "description",
    "codeReview": "process",
    "testing": "approach",
    "deployment": "strategy"
  },
  "technicalStrategy": {
    "architecture": "implementation approach",
    "technologies": "setup and configuration", 
    "database": "design and migration strategy",
    "apis": "design and implementation",
    "frontend": "development approach"
  },
  "llmPrompts": {
    "systemPrompt": "Comprehensive system prompt for AI assistant",
    "setupPrompts": ["infrastructure and setup prompts"],
    "backendPrompts": ["backend development prompts"],
    "frontendPrompts": ["frontend development prompts"],
    "databasePrompts": ["database and API prompts"],
    "testingPrompts": ["testing and deployment prompts"],
    "implementationPrompts": ["additional specific implementation tasks"]
  },
  "qualityAssurance": {
    "testing": "strategy and frameworks",
    "codeQuality": "standards and tools",
    "performance": "benchmarks and monitoring",
    "security": "requirements and practices"
  },
  "deploymentPipeline": {
    "stages": ["pipeline stages"],
    "tools": ["deployment tools"],
    "monitoring": "monitoring and alerting"
  }
}

Make the prompts highly specific to this project's requirements and architecture.`;

    try {
      const response = await this.callGeminiAPI(prompt, { 
        maxTokens: 8192,
        temperature: 0.4  // Slightly higher for creative prompt generation
      });
      
      const implementationGuide = JSON.parse(response);
      return implementationGuide;
      
    } catch (error) {
      console.error('Error generating implementation guide with AI:', error);
      
      // Fallback implementation guide
      return {
        setupInstructions: {
          prerequisites: ['Node.js 18+', 'Docker', 'Git'],
          steps: ['Clone repository', 'Install dependencies', 'Configure environment', 'Start services'],
          verification: ['Tests pass', 'Services running', 'Health checks ok']
        },
        developmentWorkflow: {
          gitFlow: 'Feature branches',
          codeReview: 'Required PR reviews',
          testing: 'TDD approach',
          deployment: 'Automated CI/CD'
        },
        technicalStrategy: {
          architecture: 'Implement modular architecture based on analysis',
          technologies: 'Set up tech stack identified in technical analysis',
          database: 'Design schema based on requirements',
          apis: 'Create RESTful APIs following best practices',
          frontend: 'Build responsive UI with modern frameworks'
        },
        llmPrompts: {
          systemPrompt: `You are an expert developer building ${repository.repo}. Use the following context: Repository focuses on [describe main purpose]. Key technologies: [list from analysis]. Architecture: [describe from analysis]. Follow best practices for security, performance, and maintainability.`,
          setupPrompts: [
            'Set up development environment for this project with all required dependencies',
            'Create Docker configuration for consistent development environment',
            'Configure CI/CD pipeline for automated testing and deployment'
          ],
          backendPrompts: [
            'Implement backend API with authentication and authorization',
            'Set up database schema and migrations',
            'Create comprehensive error handling and logging'
          ],
          frontendPrompts: [
            'Build responsive frontend interface following modern design patterns',
            'Implement state management and data fetching',
            'Add comprehensive form validation and error handling'
          ],
          databasePrompts: [
            'Design optimized database schema for the requirements',
            'Implement data access layer with proper abstractions',
            'Add database migrations and seeders'
          ],
          testingPrompts: [
            'Create comprehensive test suite with unit, integration, and e2e tests',
            'Set up test data and fixtures',
            'Configure code coverage reporting'
          ],
          implementationPrompts: [
            'Create project structure following best practices',
            'Implement core business logic',
            'Add security measures and input validation',
            'Optimize for performance and scalability'
          ]
        },
        qualityAssurance: {
          testing: 'Jest for unit tests, Playwright for e2e testing',
          codeQuality: 'ESLint + Prettier for code formatting',
          performance: 'Performance monitoring and optimization',
          security: 'Security best practices and vulnerability scanning'
        },
        deploymentPipeline: {
          stages: ['Build', 'Test', 'Deploy Staging', 'Deploy Production'],
          tools: ['GitHub Actions', 'Docker', 'Kubernetes'],
          monitoring: 'Application and infrastructure monitoring'
        }
      };
    }
  }

  // Utility methods
  async pauseWorkflow(reason) {
    console.log(`Workflow paused: ${reason}`);
    this.currentState = { ...this.currentState, paused: true, pauseReason: reason };
  }

  async resumeWorkflow() {
    console.log('Workflow resumed');
    this.currentState = { ...this.currentState, paused: false, pauseReason: null };
  }

  async retryStep(stepName) {
    console.log(`Retrying step: ${stepName}`);
    // Implementation for retrying a specific step
  }

  getWorkflowStatus() {
    return {
      isRunning: this.currentState !== null,
      currentStep: this.currentState?.currentStep || 'not_started',
      progress: this.currentState?.progress || 0,
      completedSteps: this.currentState?.completedSteps || [],
      errors: this.currentState?.errors || [],
      duration: this.currentState?.startTime 
        ? new Date() - new Date(this.currentState.startTime)
        : 0
    };
  }

  async cancelWorkflow() {
    console.log('Workflow cancelled');
    this.currentState = null;
  }

  // Event handling for workflow monitoring
  onStepComplete(callback) {
    this.stepCompleteCallback = callback;
  }

  onStepError(callback) {
    this.stepErrorCallback = callback;
  }

  onWorkflowComplete(callback) {
    this.workflowCompleteCallback = callback;
  }

  // Helper method to emit events
  emitEvent(eventType, data) {
    if (eventType === 'step_complete' && this.stepCompleteCallback) {
      this.stepCompleteCallback(data);
    } else if (eventType === 'step_error' && this.stepErrorCallback) {
      this.stepErrorCallback(data);
    } else if (eventType === 'workflow_complete' && this.workflowCompleteCallback) {
      this.workflowCompleteCallback(data);
    }
  }
}