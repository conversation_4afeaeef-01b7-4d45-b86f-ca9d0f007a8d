/**
 * Message Bridge
 * Handles secure communication between extension components
 */

export class MessageBridge {
  constructor() {
    this.messageHandlers = new Map();
    this.pendingRequests = new Map();
    this.sessionId = this.generateSessionId();
    this.messageQueue = [];
    this.isConnected = false;
    this.retryAttempts = 3;
    this.retryDelay = 1000;
  }

  initialize() {
    this.setupMessageListener();
    this.setupConnectionHandling();
    this.isConnected = true;
    
    // Process any queued messages
    this.processMessageQueue();
    
    console.log('Message Bridge initialized with session:', this.sessionId);
  }

  setupMessageListener() {
    // Listen for messages from other extension components
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep the message channel open for async responses
    });

    // Listen for connection events
    chrome.runtime.onConnect.addListener((port) => {
      this.handleConnection(port);
    });
  }

  setupConnectionHandling() {
    // Handle extension context invalidation
    chrome.runtime.onSuspend?.addListener(() => {
      console.log('Extension context being suspended');
      this.isConnected = false;
    });

    // Handle extension startup
    chrome.runtime.onStartup?.addListener(() => {
      console.log('Extension context restored');
      this.isConnected = true;
      this.processMessageQueue();
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      // Validate message structure
      if (!this.validateMessage(message)) {
        sendResponse({ error: 'Invalid message format' });
        return;
      }

      // Security check
      if (!this.isValidSender(sender)) {
        sendResponse({ error: 'Unauthorized sender' });
        return;
      }

      const { type, data, id, sessionId } = message;

      // Handle different message types
      switch (type) {
        case 'PING':
          sendResponse({ type: 'PONG', timestamp: Date.now() });
          break;

        case 'REQUEST':
          await this.handleRequest(data, id, sendResponse);
          break;

        case 'RESPONSE':
          this.handleResponse(data, id);
          break;

        case 'NOTIFICATION':
          this.handleNotification(data);
          break;

        case 'ERROR':
          this.handleError(data, id);
          break;

        default:
          // Check for registered handlers
          if (this.messageHandlers.has(type)) {
            const handler = this.messageHandlers.get(type);
            const result = await handler(data, sender);
            sendResponse({ type: 'RESPONSE', data: result, id });
          } else {
            sendResponse({ error: `Unknown message type: ${type}` });
          }
      }
    } catch (error) {
      console.error('Message handling error:', error);
      sendResponse({ error: error.message });
    }
  }

  handleConnection(port) {
    console.log('New connection established:', port.name);
    
    port.onMessage.addListener((message) => {
      console.log('Port message received:', message);
      // Handle port-specific messages
    });

    port.onDisconnect.addListener(() => {
      console.log('Port disconnected:', port.name);
      // Handle disconnection
    });
  }

  async handleRequest(data, requestId, sendResponse) {
    try {
      const { action, params } = data;
      
      // Route request to appropriate handler
      const handler = this.getRequestHandler(action);
      if (!handler) {
        sendResponse({ error: `No handler for action: ${action}` });
        return;
      }

      const result = await handler(params);
      sendResponse({ type: 'RESPONSE', data: result, id: requestId });
    } catch (error) {
      console.error('Request handling error:', error);
      sendResponse({ type: 'ERROR', data: { message: error.message }, id: requestId });
    }
  }

  handleResponse(data, requestId) {
    const pendingRequest = this.pendingRequests.get(requestId);
    if (pendingRequest) {
      clearTimeout(pendingRequest.timeout);
      pendingRequest.resolve(data);
      this.pendingRequests.delete(requestId);
    }
  }

  handleNotification(data) {
    // Broadcast notification to all registered handlers
    this.messageHandlers.forEach((handler, type) => {
      if (type.startsWith('NOTIFICATION_')) {
        try {
          handler(data);
        } catch (error) {
          console.error('Notification handler error:', error);
        }
      }
    });
  }

  handleError(data, requestId) {
    const pendingRequest = this.pendingRequests.get(requestId);
    if (pendingRequest) {
      clearTimeout(pendingRequest.timeout);
      pendingRequest.reject(new Error(data.message || 'Unknown error'));
      this.pendingRequests.delete(requestId);
    }
  }

  validateMessage(message) {
    return message && 
           typeof message === 'object' && 
           typeof message.type === 'string' &&
           message.type.length > 0;
  }

  isValidSender(sender) {
    // Basic sender validation
    if (!sender) return false;
    
    // Check if sender is from our extension
    if (sender.id && sender.id === chrome.runtime.id) {
      return true;
    }
    
    // Check if sender is from a valid tab
    if (sender.tab && sender.tab.url && sender.tab.url.startsWith('https://github.com/')) {
      return true;
    }
    
    return false;
  }

  async sendMessage(recipient, type, data, options = {}) {
    const { timeout = 10000, retries = this.retryAttempts } = options;
    
    const message = {
      type,
      data,
      id: this.generateMessageId(),
      sessionId: this.sessionId,
      timestamp: Date.now()
    };

    // Queue message if not connected
    if (!this.isConnected) {
      this.messageQueue.push({ recipient, message, options });
      return Promise.reject(new Error('Extension not connected'));
    }

    return this.sendMessageWithRetry(recipient, message, timeout, retries);
  }

  async sendMessageWithRetry(recipient, message, timeout, retries) {
    let lastError;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await this.sendMessageInternal(recipient, message, timeout);
      } catch (error) {
        lastError = error;
        
        if (attempt < retries) {
          await this.delay(this.retryDelay * Math.pow(2, attempt)); // Exponential backoff
          console.log(`Retrying message send (attempt ${attempt + 1}/${retries + 1})`);
        }
      }
    }
    
    throw lastError;
  }

  async sendMessageInternal(recipient, message, timeout) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(message.id);
        reject(new Error('Message timeout'));
      }, timeout);

      // Store pending request
      this.pendingRequests.set(message.id, {
        resolve,
        reject,
        timeout: timeoutId
      });

      // Send message based on recipient type
      if (recipient === 'background') {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            this.pendingRequests.delete(message.id);
            clearTimeout(timeoutId);
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            this.handleResponse(response, message.id);
          }
        });
      } else if (typeof recipient === 'number') {
        // Send to specific tab
        chrome.tabs.sendMessage(recipient, message, (response) => {
          if (chrome.runtime.lastError) {
            this.pendingRequests.delete(message.id);
            clearTimeout(timeoutId);
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            this.handleResponse(response, message.id);
          }
        });
      } else {
        // Send to all tabs
        this.broadcastMessage(message).then(resolve).catch(reject);
      }
    });
  }

  async broadcastMessage(message) {
    const tabs = await chrome.tabs.query({});
    const promises = tabs.map(tab => 
      new Promise((resolve) => {
        chrome.tabs.sendMessage(tab.id, message, (response) => {
          if (chrome.runtime.lastError) {
            resolve({ tabId: tab.id, error: chrome.runtime.lastError.message });
          } else {
            resolve({ tabId: tab.id, response });
          }
        });
      })
    );

    return Promise.all(promises);
  }

  processMessageQueue() {
    if (!this.isConnected) return;

    while (this.messageQueue.length > 0) {
      const { recipient, message, options } = this.messageQueue.shift();
      
      this.sendMessage(recipient, message.type, message.data, options)
        .catch(error => {
          console.error('Failed to send queued message:', error);
        });
    }
  }

  registerHandler(type, handler) {
    this.messageHandlers.set(type, handler);
  }

  unregisterHandler(type) {
    this.messageHandlers.delete(type);
  }

  getRequestHandler(action) {
    // Map actions to handlers
    const handlers = {
      'ANALYZE_REPOSITORY': this.handleAnalyzeRepository.bind(this),
      'GET_SETTINGS': this.handleGetSettings.bind(this),
      'UPDATE_SETTINGS': this.handleUpdateSettings.bind(this),
      'GET_ANALYSIS_HISTORY': this.handleGetAnalysisHistory.bind(this),
      'CACHE_ANALYSIS': this.handleCacheAnalysis.bind(this),
      'CLEAR_CACHE': this.handleClearCache.bind(this),
      'EXPORT_DATA': this.handleExportData.bind(this),
      'IMPORT_DATA': this.handleImportData.bind(this),
      'SECURITY_AUDIT': this.handleSecurityAudit.bind(this)
    };

    return handlers[action];
  }

  // Request handlers
  async handleAnalyzeRepository(params) {
    // Forward to background script for processing
    return await this.sendMessage('background', 'ANALYZE_REPOSITORY', params);
  }

  async handleGetSettings(params) {
    return await this.sendMessage('background', 'GET_SETTINGS', params);
  }

  async handleUpdateSettings(params) {
    return await this.sendMessage('background', 'UPDATE_SETTINGS', params);
  }

  async handleGetAnalysisHistory(params) {
    return await this.sendMessage('background', 'GET_ANALYSIS_HISTORY', params);
  }

  async handleCacheAnalysis(params) {
    return await this.sendMessage('background', 'CACHE_ANALYSIS', params);
  }

  async handleClearCache(params) {
    return await this.sendMessage('background', 'CLEAR_CACHE', params);
  }

  async handleExportData(params) {
    return await this.sendMessage('background', 'EXPORT_DATA', params);
  }

  async handleImportData(params) {
    return await this.sendMessage('background', 'IMPORT_DATA', params);
  }

  async handleSecurityAudit(params) {
    return await this.sendMessage('background', 'SECURITY_AUDIT', params);
  }

  // Notification methods
  notifyAnalysisProgress(progress) {
    this.broadcast('NOTIFICATION', {
      type: 'ANALYSIS_PROGRESS',
      progress
    });
  }

  notifyAnalysisComplete(result) {
    this.broadcast('NOTIFICATION', {
      type: 'ANALYSIS_COMPLETE',
      result
    });
  }

  notifyAnalysisError(error) {
    this.broadcast('NOTIFICATION', {
      type: 'ANALYSIS_ERROR',
      error: error.message
    });
  }

  notifySettingsChanged(settings) {
    this.broadcast('NOTIFICATION', {
      type: 'SETTINGS_CHANGED',
      settings
    });
  }

  async broadcast(type, data) {
    try {
      await this.broadcastMessage({
        type,
        data,
        id: this.generateMessageId(),
        sessionId: this.sessionId,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Broadcast error:', error);
    }
  }

  // Utility methods
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Health check methods
  async ping(recipient = 'background') {
    try {
      const response = await this.sendMessage(recipient, 'PING', {}, { timeout: 5000 });
      return response.type === 'PONG';
    } catch (error) {
      console.error('Ping failed:', error);
      return false;
    }
  }

  async healthCheck() {
    const results = {
      timestamp: Date.now(),
      sessionId: this.sessionId,
      connected: this.isConnected,
      queueSize: this.messageQueue.length,
      pendingRequests: this.pendingRequests.size,
      handlers: this.messageHandlers.size
    };

    // Test communication with background script
    results.backgroundPing = await this.ping('background');

    // Test communication with active tabs
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      results.activeTabs = tabs.length;
      
      if (tabs.length > 0) {
        results.tabPing = await this.ping(tabs[0].id);
      }
    } catch (error) {
      results.tabPingError = error.message;
    }

    return results;
  }

  // Debug methods
  getDebugInfo() {
    return {
      sessionId: this.sessionId,
      isConnected: this.isConnected,
      messageQueue: this.messageQueue.length,
      pendingRequests: Array.from(this.pendingRequests.keys()),
      handlers: Array.from(this.messageHandlers.keys())
    };
  }

  // Cleanup
  cleanup() {
    // Clear pending requests
    this.pendingRequests.forEach(request => {
      clearTimeout(request.timeout);
      request.reject(new Error('Message bridge cleanup'));
    });
    this.pendingRequests.clear();

    // Clear message queue
    this.messageQueue = [];

    // Clear handlers
    this.messageHandlers.clear();

    // Mark as disconnected
    this.isConnected = false;

    console.log('Message Bridge cleaned up');
  }
}