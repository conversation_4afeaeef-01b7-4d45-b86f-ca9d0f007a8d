/**
 * Perplexity API Client
 * Handles communication with Perplexity API for market research and competitive analysis
 */

export class PerplexityClient {
  constructor() {
    this.baseUrl = 'https://api.perplexity.ai';
    this.apiKey = null;
    this.rateLimitRemaining = 50;
    this.rateLimitReset = null;
    this.models = {
      search: 'llama-3.1-sonar-small-128k-online',
      chat: 'llama-3.1-sonar-large-128k-online',
      analysis: 'llama-3.1-sonar-huge-128k-online'
    };
  }

  async initialize(apiKey) {
    this.apiKey = apiKey;
    if (apiKey) {
      await this.verifyApiKey();
    }
  }

  async verifyApiKey() {
    try {
      const response = await this.makeRequest('/chat/completions', {
        method: 'POST',
        body: {
          model: this.models.search,
          messages: [
            { role: 'user', content: 'Hello, testing API connection' }
          ],
          max_tokens: 10
        }
      });
      
      console.log('Perplexity API key verified successfully');
      return true;
    } catch (error) {
      console.error('Perplexity API key verification failed:', error);
      return false;
    }
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    const requestOptions = {
      method: options.method || 'GET',
      headers,
      ...options
    };

    if (options.body) {
      requestOptions.body = JSON.stringify(options.body);
    }

    try {
      const response = await fetch(url, requestOptions);
      
      // Update rate limit info from headers
      this.rateLimitRemaining = parseInt(response.headers.get('x-ratelimit-remaining') || '50');
      this.rateLimitReset = new Date(response.headers.get('x-ratelimit-reset-time') || Date.now() + 3600000);

      if (!response.ok) {
        if (response.status === 429) {
          throw new Error(`Perplexity API rate limit exceeded. Resets at ${this.rateLimitReset.toLocaleString()}`);
        }
        throw new Error(`Perplexity API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Perplexity API request error:', error);
      throw error;
    }
  }

  async search(options = {}) {
    const {
      query,
      focus = 'general',
      model = this.models.search,
      maxTokens = 2048,
      temperature = 0.3
    } = options;

    const systemPrompt = this.getSystemPrompt(focus);
    const userPrompt = this.formatSearchQuery(query, focus);

    try {
      const response = await this.makeRequest('/chat/completions', {
        method: 'POST',
        body: {
          model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: maxTokens,
          temperature,
          top_p: 0.9,
          stream: false
        }
      });

      return this.parseSearchResponse(response, focus);
    } catch (error) {
      console.error('Error performing Perplexity search:', error);
      throw error;
    }
  }

  async conductMarketResearch(projectType, language, description) {
    try {
      const marketAnalysis = await this.search({
        query: `${projectType} ${language} market analysis 2024 trends opportunities challenges`,
        focus: 'market_analysis',
        model: this.models.analysis,
        maxTokens: 3000
      });

      const competitorAnalysis = await this.search({
        query: `${projectType} ${language} popular alternatives competitors similar projects comparison`,
        focus: 'competitor_analysis',
        model: this.models.analysis,
        maxTokens: 2500
      });

      const technologyTrends = await this.search({
        query: `${language} technology trends 2024 best practices frameworks libraries`,
        focus: 'technology_trends',
        model: this.models.search,
        maxTokens: 2000
      });

      return {
        marketAnalysis,
        competitorAnalysis,
        technologyTrends,
        summary: await this.synthesizeMarketInsights(marketAnalysis, competitorAnalysis, technologyTrends)
      };
    } catch (error) {
      console.error('Error conducting market research:', error);
      throw error;
    }
  }

  async analyzeCompetitors(projectType, language, repositoryName) {
    try {
      const directCompetitors = await this.search({
        query: `${projectType} ${language} similar to ${repositoryName} alternatives competitors`,
        focus: 'competitor_analysis',
        model: this.models.analysis,
        maxTokens: 2500
      });

      const marketPosition = await this.search({
        query: `${projectType} market landscape positioning opportunities differentiation`,
        focus: 'market_positioning',
        model: this.models.analysis,
        maxTokens: 2000
      });

      const competitiveAdvantages = await this.search({
        query: `${projectType} ${language} competitive advantages unique features differentiators`,
        focus: 'competitive_advantages',
        model: this.models.search,
        maxTokens: 1500
      });

      return {
        directCompetitors,
        marketPosition,
        competitiveAdvantages,
        recommendations: await this.generateCompetitiveRecommendations(directCompetitors, marketPosition, competitiveAdvantages)
      };
    } catch (error) {
      console.error('Error analyzing competitors:', error);
      throw error;
    }
  }

  async researchTechnologyTrends(language, frameworks) {
    try {
      const languageTrends = await this.search({
        query: `${language} programming language trends 2024 adoption statistics future outlook`,
        focus: 'technology_trends',
        model: this.models.search,
        maxTokens: 2000
      });

      const frameworkTrends = await Promise.all(
        frameworks.map(framework => this.search({
          query: `${framework} framework trends 2024 adoption best practices ecosystem`,
          focus: 'framework_analysis',
          model: this.models.search,
          maxTokens: 1500
        }))
      );

      const emergingTechnologies = await this.search({
        query: `${language} emerging technologies 2024 new frameworks libraries tools`,
        focus: 'emerging_tech',
        model: this.models.search,
        maxTokens: 2000
      });

      return {
        languageTrends,
        frameworkTrends,
        emergingTechnologies,
        insights: await this.synthesizeTechnologyInsights(languageTrends, frameworkTrends, emergingTechnologies)
      };
    } catch (error) {
      console.error('Error researching technology trends:', error);
      throw error;
    }
  }

  async generateBusinessInsights(projectType, marketResearch, competitorAnalysis) {
    try {
      const businessOpportunities = await this.search({
        query: `${projectType} business opportunities market gaps revenue potential`,
        focus: 'business_analysis',
        model: this.models.analysis,
        maxTokens: 2500
      });

      const monetizationStrategies = await this.search({
        query: `${projectType} monetization strategies business models revenue streams`,
        focus: 'monetization',
        model: this.models.analysis,
        maxTokens: 2000
      });

      const marketValidation = await this.search({
        query: `${projectType} market validation user adoption strategies product-market fit`,
        focus: 'market_validation',
        model: this.models.search,
        maxTokens: 1500
      });

      return {
        businessOpportunities,
        monetizationStrategies,
        marketValidation,
        actionPlan: await this.generateBusinessActionPlan(businessOpportunities, monetizationStrategies, marketValidation)
      };
    } catch (error) {
      console.error('Error generating business insights:', error);
      throw error;
    }
  }

  getSystemPrompt(focus) {
    const prompts = {
      market_analysis: `You are an expert market research analyst. Provide comprehensive market analysis including:
- Current market size and growth trends
- Key market drivers and challenges
- Emerging opportunities and threats
- Target audience analysis
- Market segmentation insights
Focus on actionable insights with supporting data and statistics.`,

      competitor_analysis: `You are a competitive intelligence expert. Analyze competitors and market positioning:
- Identify direct and indirect competitors
- Compare features, pricing, and market position
- Analyze strengths and weaknesses
- Identify market gaps and opportunities
- Provide differentiation strategies
Present findings in a structured, comparative format.`,

      technology_trends: `You are a technology trend analyst. Research current and emerging technology trends:
- Current adoption rates and popularity
- Future growth projections
- Best practices and recommendations
- Ecosystem health and community support
- Integration possibilities and compatibility
Focus on practical implications for developers and businesses.`,

      business_analysis: `You are a business strategy consultant. Analyze business opportunities and strategies:
- Market opportunities and revenue potential
- Business model recommendations
- Go-to-market strategies
- Risk assessment and mitigation
- Success metrics and KPIs
Provide actionable business insights and recommendations.`,

      general: `You are a comprehensive research analyst. Provide thorough, well-researched information on the given topic with:
- Current state analysis
- Trends and future outlook
- Key insights and recommendations
- Supporting data and examples
- Practical implications
Focus on accuracy, relevance, and actionability.`
    };

    return prompts[focus] || prompts.general;
  }

  formatSearchQuery(query, focus) {
    const queryTemplates = {
      market_analysis: `Conduct a comprehensive market analysis for: ${query}

Please provide:
1. Market size and growth trends
2. Key market drivers and challenges
3. Target audience characteristics
4. Competitive landscape overview
5. Market opportunities and threats
6. Industry best practices and standards

Include recent data, statistics, and credible sources.`,

      competitor_analysis: `Analyze competitors and alternatives for: ${query}

Please provide:
1. Direct competitors (top 5-10)
2. Indirect competitors and substitutes
3. Feature comparison matrix
4. Pricing and business model analysis
5. Market positioning and differentiation
6. Competitive advantages and disadvantages
7. Market gaps and opportunities

Include specific examples and comparisons.`,

      technology_trends: `Research technology trends and ecosystem for: ${query}

Please provide:
1. Current adoption rates and popularity metrics
2. Recent developments and updates
3. Community size and activity
4. Best practices and recommendations
5. Integration ecosystem and compatibility
6. Future roadmap and projections
7. Learning resources and documentation quality

Include GitHub stars, NPM downloads, or similar metrics where applicable.`,

      business_analysis: `Analyze business opportunities and strategies for: ${query}

Please provide:
1. Business model recommendations
2. Revenue potential and monetization strategies
3. Market validation approaches
4. Go-to-market strategy
5. Risk assessment and mitigation
6. Success metrics and KPIs
7. Funding and investment considerations

Include case studies and real-world examples.`,

      general: `Research and analyze: ${query}

Please provide comprehensive information including current state, trends, opportunities, challenges, and recommendations.`
    };

    return queryTemplates[focus] || queryTemplates.general;
  }

  parseSearchResponse(response, focus) {
    const content = response.choices[0]?.message?.content || '';
    
    return {
      content,
      focus,
      timestamp: new Date().toISOString(),
      model: response.model,
      usage: response.usage,
      structured: this.extractStructuredData(content, focus)
    };
  }

  extractStructuredData(content, focus) {
    const structured = {
      keyPoints: [],
      insights: [],
      recommendations: [],
      data: {},
      sources: []
    };

    // Extract key points (lines starting with numbers, bullets, or dashes)
    const keyPointRegex = /(?:^|\n)(?:\d+\.|[-•*])\s*(.+)/g;
    let match;
    while ((match = keyPointRegex.exec(content)) !== null) {
      structured.keyPoints.push(match[1].trim());
    }

    // Extract insights and recommendations based on focus
    if (focus === 'market_analysis') {
      structured.data.marketSize = this.extractMarketSize(content);
      structured.data.growthRate = this.extractGrowthRate(content);
      structured.data.keyDrivers = this.extractKeyDrivers(content);
    } else if (focus === 'competitor_analysis') {
      structured.data.competitors = this.extractCompetitors(content);
      structured.data.marketPosition = this.extractMarketPosition(content);
    } else if (focus === 'technology_trends') {
      structured.data.adoptionRate = this.extractAdoptionRate(content);
      structured.data.ecosystem = this.extractEcosystem(content);
    }

    // Extract URLs and sources
    const urlRegex = /https?:\/\/[^\s]+/g;
    structured.sources = content.match(urlRegex) || [];

    return structured;
  }

  extractMarketSize(content) {
    const sizeRegex = /market size[^$]*\$?([0-9.]+\s*(?:billion|million|trillion))/i;
    const match = content.match(sizeRegex);
    return match ? match[1] : null;
  }

  extractGrowthRate(content) {
    const growthRegex = /growth rate[^%]*([0-9.]+%)/i;
    const match = content.match(growthRegex);
    return match ? match[1] : null;
  }

  extractKeyDrivers(content) {
    const driversRegex = /(?:key drivers?|driving factors?)[^:]*:?\s*(.+?)(?:\n\n|\.$)/i;
    const match = content.match(driversRegex);
    return match ? match[1].split(/[,;]/).map(d => d.trim()) : [];
  }

  extractCompetitors(content) {
    const competitors = [];
    const competitorRegex = /(?:competitors?|alternatives?)[^:]*:?\s*(.+?)(?:\n\n|\.$)/i;
    const match = content.match(competitorRegex);
    
    if (match) {
      const competitorText = match[1];
      const names = competitorText.split(/[,;]/).map(c => c.trim());
      competitors.push(...names);
    }

    return competitors;
  }

  extractMarketPosition(content) {
    const positionRegex = /(?:market position|positioning)[^:]*:?\s*(.+?)(?:\n\n|\.$)/i;
    const match = content.match(positionRegex);
    return match ? match[1].trim() : null;
  }

  extractAdoptionRate(content) {
    const adoptionRegex = /adoption rate[^%]*([0-9.]+%)/i;
    const match = content.match(adoptionRegex);
    return match ? match[1] : null;
  }

  extractEcosystem(content) {
    const ecosystemRegex = /ecosystem[^:]*:?\s*(.+?)(?:\n\n|\.$)/i;
    const match = content.match(ecosystemRegex);
    return match ? match[1].trim() : null;
  }

  async synthesizeMarketInsights(marketAnalysis, competitorAnalysis, technologyTrends) {
    const insights = {
      opportunities: [],
      threats: [],
      recommendations: [],
      keyFindings: []
    };

    // Extract opportunities from market analysis
    if (marketAnalysis.structured.keyPoints) {
      insights.opportunities = marketAnalysis.structured.keyPoints
        .filter(point => point.toLowerCase().includes('opportunity') || point.toLowerCase().includes('growth'))
        .slice(0, 3);
    }

    // Extract threats from competitor analysis
    if (competitorAnalysis.structured.keyPoints) {
      insights.threats = competitorAnalysis.structured.keyPoints
        .filter(point => point.toLowerCase().includes('threat') || point.toLowerCase().includes('challenge'))
        .slice(0, 3);
    }

    // Generate recommendations
    insights.recommendations = [
      'Focus on differentiation through unique features and better user experience',
      'Leverage current technology trends to stay competitive',
      'Monitor competitor developments and market changes regularly',
      'Build strong community engagement and developer relations'
    ];

    // Key findings summary
    insights.keyFindings = [
      `Market shows ${marketAnalysis.structured.data.growthRate || 'positive'} growth potential`,
      `${competitorAnalysis.structured.data.competitors?.length || 'Several'} direct competitors identified`,
      `Technology trends favor ${technologyTrends.structured.data.adoptionRate || 'current'} adoption patterns`
    ];

    return insights;
  }

  async synthesizeTechnologyInsights(languageTrends, frameworkTrends, emergingTechnologies) {
    return {
      languageOutlook: this.extractOutlook(languageTrends.content),
      frameworkRecommendations: frameworkTrends.map(trend => ({
        framework: this.extractFrameworkName(trend.content),
        recommendation: this.extractRecommendation(trend.content)
      })),
      emergingOpportunities: this.extractOpportunities(emergingTechnologies.content),
      technicalRecommendations: [
        'Stay updated with latest framework versions and best practices',
        'Consider emerging technologies for competitive advantage',
        'Focus on technologies with strong community support',
        'Implement modern development practices and tools'
      ]
    };
  }

  async generateCompetitiveRecommendations(directCompetitors, marketPosition, competitiveAdvantages) {
    return {
      positioning: 'Position as developer-friendly alternative with superior documentation',
      differentiation: [
        'Focus on ease of use and developer experience',
        'Provide comprehensive documentation and examples',
        'Build strong community and ecosystem',
        'Emphasize performance and reliability'
      ],
      tactics: [
        'Analyze competitor weaknesses and address them',
        'Highlight unique features and benefits',
        'Engage with developer communities',
        'Create comparison guides and migration tools'
      ]
    };
  }

  async generateBusinessActionPlan(businessOpportunities, monetizationStrategies, marketValidation) {
    return {
      immediate: [
        'Validate core value proposition with target users',
        'Build MVP with essential features',
        'Establish community presence and engagement',
        'Create comprehensive documentation'
      ],
      shortTerm: [
        'Implement user feedback and iterate',
        'Expand feature set based on user needs',
        'Build partnerships and integrations',
        'Develop monetization strategy'
      ],
      longTerm: [
        'Scale user base and community',
        'Explore enterprise opportunities',
        'Consider commercial licensing options',
        'Expand into adjacent markets'
      ]
    };
  }

  extractOutlook(content) {
    const outlookRegex = /outlook[^:]*:?\s*(.+?)(?:\n\n|\.$)/i;
    const match = content.match(outlookRegex);
    return match ? match[1].trim() : 'Positive outlook with continued growth expected';
  }

  extractFrameworkName(content) {
    const nameRegex = /^([A-Za-z.]+)/;
    const match = content.match(nameRegex);
    return match ? match[1] : 'Framework';
  }

  extractRecommendation(content) {
    const recRegex = /recommend[^:]*:?\s*(.+?)(?:\n\n|\.$)/i;
    const match = content.match(recRegex);
    return match ? match[1].trim() : 'Consider for project requirements';
  }

  extractOpportunities(content) {
    const opportunities = [];
    const oppRegex = /opportunit[^:]*:?\s*(.+?)(?:\n\n|\.$)/gi;
    let match;
    while ((match = oppRegex.exec(content)) !== null) {
      opportunities.push(match[1].trim());
    }
    return opportunities.slice(0, 3);
  }

  getRateLimitInfo() {
    return {
      remaining: this.rateLimitRemaining,
      reset: this.rateLimitReset,
      resetIn: this.rateLimitReset ? Math.max(0, this.rateLimitReset - new Date()) : null
    };
  }

  async waitForRateLimit() {
    const rateLimitInfo = this.getRateLimitInfo();
    
    if (rateLimitInfo.remaining <= 5 && rateLimitInfo.resetIn > 0) {
      console.log(`Perplexity rate limit low (${rateLimitInfo.remaining} remaining). Waiting ${rateLimitInfo.resetIn}ms...`);
      await new Promise(resolve => setTimeout(resolve, rateLimitInfo.resetIn));
    }
  }
}