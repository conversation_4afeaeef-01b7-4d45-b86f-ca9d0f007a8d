/**
 * GitHub API Client
 * Handles communication with GitHub's REST API for repository analysis
 */

export class GitHubClient {
  constructor() {
    this.baseUrl = 'https://api.github.com';
    this.token = null;
    this.rateLimitRemaining = 5000;
    this.rateLimitReset = null;
  }

  async initialize(token) {
    this.token = token;
    if (token) {
      await this.verifyToken();
    }
  }

  async verifyToken() {
    try {
      const response = await this.makeRequest('/user');
      console.log('GitHub API token verified for user:', response.login);
      return true;
    } catch (error) {
      console.error('GitHub API token verification failed:', error);
      return false;
    }
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Accept': 'application/vnd.github.v3+json',
      'User-Agent': 'GitHub-Analyzer-Extension/1.0.0',
      ...options.headers
    };

    if (this.token) {
      headers['Authorization'] = `token ${this.token}`;
    }

    const requestOptions = {
      method: options.method || 'GET',
      headers,
      ...options
    };

    if (options.body) {
      requestOptions.body = JSON.stringify(options.body);
      headers['Content-Type'] = 'application/json';
    }

    try {
      const response = await fetch(url, requestOptions);
      
      // Update rate limit info
      this.rateLimitRemaining = parseInt(response.headers.get('X-RateLimit-Remaining') || '0');
      this.rateLimitReset = new Date(parseInt(response.headers.get('X-RateLimit-Reset') || '0') * 1000);

      if (!response.ok) {
        if (response.status === 403 && this.rateLimitRemaining === 0) {
          throw new Error(`GitHub API rate limit exceeded. Resets at ${this.rateLimitReset.toLocaleString()}`);
        }
        throw new Error(`GitHub API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('GitHub API request error:', error);
      throw error;
    }
  }

  async getRepositoryDetails(owner, repo) {
    try {
      const repository = await this.makeRequest(`/repos/${owner}/${repo}`);
      
      // Get additional details
      const [contributors, languages, releases, branches] = await Promise.all([
        this.getContributors(owner, repo),
        this.getLanguages(owner, repo),
        this.getReleases(owner, repo),
        this.getBranches(owner, repo)
      ]);

      return {
        ...repository,
        contributors: contributors.slice(0, 10), // Top 10 contributors
        languages,
        releases: releases.slice(0, 5), // Latest 5 releases
        branches: branches.slice(0, 5) // Top 5 branches
      };
    } catch (error) {
      console.error('Error getting repository details:', error);
      throw error;
    }
  }

  async getFileStructure(owner, repo, path = '') {
    try {
      const contents = await this.makeRequest(`/repos/${owner}/${repo}/contents/${path}`);
      const files = [];

      for (const item of contents) {
        if (item.type === 'file') {
          files.push({
            name: item.name,
            path: item.path,
            type: 'file',
            size: item.size,
            sha: item.sha,
            download_url: item.download_url
          });
        } else if (item.type === 'dir') {
          files.push({
            name: item.name,
            path: item.path,
            type: 'directory',
            sha: item.sha
          });
          
          // Recursively get directory contents (limit depth to avoid rate limits)
          if (path.split('/').length < 3) {
            try {
              const subFiles = await this.getFileStructure(owner, repo, item.path);
              files.push(...subFiles);
            } catch (error) {
              console.warn(`Failed to get contents of directory ${item.path}:`, error);
            }
          }
        }
      }

      return files;
    } catch (error) {
      console.error('Error getting file structure:', error);
      throw error;
    }
  }

  async getPackageFiles(owner, repo) {
    try {
      const packageFiles = {};
      
      // Try to get package.json for Node.js projects
      try {
        const packageJson = await this.getFileContent(owner, repo, 'package.json');
        packageFiles.package_json = JSON.parse(packageJson);
      } catch (error) {
        // File doesn't exist, skip
      }

      // Try to get requirements.txt for Python projects
      try {
        const requirements = await this.getFileContent(owner, repo, 'requirements.txt');
        packageFiles.requirements_txt = requirements.split('\n').filter(line => line.trim());
      } catch (error) {
        // File doesn't exist, skip
      }

      // Try to get Cargo.toml for Rust projects
      try {
        const cargoToml = await this.getFileContent(owner, repo, 'Cargo.toml');
        packageFiles.cargo_toml = cargoToml;
      } catch (error) {
        // File doesn't exist, skip
      }

      // Try to get pom.xml for Java projects
      try {
        const pomXml = await this.getFileContent(owner, repo, 'pom.xml');
        packageFiles.pom_xml = pomXml;
      } catch (error) {
        // File doesn't exist, skip
      }

      // Try to get composer.json for PHP projects
      try {
        const composerJson = await this.getFileContent(owner, repo, 'composer.json');
        packageFiles.composer_json = JSON.parse(composerJson);
      } catch (error) {
        // File doesn't exist, skip
      }

      return packageFiles;
    } catch (error) {
      console.error('Error getting package files:', error);
      throw error;
    }
  }

  async getReadme(owner, repo) {
    try {
      const readmeFiles = ['README.md', 'README.rst', 'README.txt', 'README'];
      
      for (const filename of readmeFiles) {
        try {
          const content = await this.getFileContent(owner, repo, filename);
          return {
            filename,
            content
          };
        } catch (error) {
          // Try next filename
          continue;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error getting README:', error);
      return null;
    }
  }

  async getFileContent(owner, repo, path) {
    try {
      const response = await this.makeRequest(`/repos/${owner}/${repo}/contents/${path}`);
      
      if (response.encoding === 'base64') {
        return atob(response.content);
      } else {
        return response.content;
      }
    } catch (error) {
      console.error(`Error getting file content for ${path}:`, error);
      throw error;
    }
  }

  async getContributors(owner, repo) {
    try {
      return await this.makeRequest(`/repos/${owner}/${repo}/contributors`);
    } catch (error) {
      console.error('Error getting contributors:', error);
      return [];
    }
  }

  async getLanguages(owner, repo) {
    try {
      return await this.makeRequest(`/repos/${owner}/${repo}/languages`);
    } catch (error) {
      console.error('Error getting languages:', error);
      return {};
    }
  }

  async getReleases(owner, repo) {
    try {
      return await this.makeRequest(`/repos/${owner}/${repo}/releases`);
    } catch (error) {
      console.error('Error getting releases:', error);
      return [];
    }
  }

  async getBranches(owner, repo) {
    try {
      return await this.makeRequest(`/repos/${owner}/${repo}/branches`);
    } catch (error) {
      console.error('Error getting branches:', error);
      return [];
    }
  }

  async getCommits(owner, repo, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (options.since) queryParams.append('since', options.since);
      if (options.until) queryParams.append('until', options.until);
      if (options.per_page) queryParams.append('per_page', options.per_page);
      if (options.page) queryParams.append('page', options.page);

      const endpoint = `/repos/${owner}/${repo}/commits${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      return await this.makeRequest(endpoint);
    } catch (error) {
      console.error('Error getting commits:', error);
      return [];
    }
  }

  async getIssues(owner, repo, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (options.state) queryParams.append('state', options.state);
      if (options.labels) queryParams.append('labels', options.labels);
      if (options.sort) queryParams.append('sort', options.sort);
      if (options.direction) queryParams.append('direction', options.direction);
      if (options.per_page) queryParams.append('per_page', options.per_page);

      const endpoint = `/repos/${owner}/${repo}/issues${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      return await this.makeRequest(endpoint);
    } catch (error) {
      console.error('Error getting issues:', error);
      return [];
    }
  }

  async getPullRequests(owner, repo, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (options.state) queryParams.append('state', options.state);
      if (options.head) queryParams.append('head', options.head);
      if (options.base) queryParams.append('base', options.base);
      if (options.sort) queryParams.append('sort', options.sort);
      if (options.direction) queryParams.append('direction', options.direction);
      if (options.per_page) queryParams.append('per_page', options.per_page);

      const endpoint = `/repos/${owner}/${repo}/pulls${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      return await this.makeRequest(endpoint);
    } catch (error) {
      console.error('Error getting pull requests:', error);
      return [];
    }
  }

  async getRepoStats(owner, repo) {
    try {
      const [
        codeFrequency,
        commitActivity,
        contributors,
        participation
      ] = await Promise.all([
        this.makeRequest(`/repos/${owner}/${repo}/stats/code_frequency`).catch(() => null),
        this.makeRequest(`/repos/${owner}/${repo}/stats/commit_activity`).catch(() => null),
        this.makeRequest(`/repos/${owner}/${repo}/stats/contributors`).catch(() => null),
        this.makeRequest(`/repos/${owner}/${repo}/stats/participation`).catch(() => null)
      ]);

      return {
        codeFrequency,
        commitActivity,
        contributors,
        participation
      };
    } catch (error) {
      console.error('Error getting repository stats:', error);
      return {};
    }
  }

  async searchRepositories(query, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('q', query);
      
      if (options.sort) queryParams.append('sort', options.sort);
      if (options.order) queryParams.append('order', options.order);
      if (options.per_page) queryParams.append('per_page', options.per_page);
      if (options.page) queryParams.append('page', options.page);

      const endpoint = `/search/repositories?${queryParams.toString()}`;
      return await this.makeRequest(endpoint);
    } catch (error) {
      console.error('Error searching repositories:', error);
      throw error;
    }
  }

  async searchCode(query, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('q', query);
      
      if (options.sort) queryParams.append('sort', options.sort);
      if (options.order) queryParams.append('order', options.order);
      if (options.per_page) queryParams.append('per_page', options.per_page);
      if (options.page) queryParams.append('page', options.page);

      const endpoint = `/search/code?${queryParams.toString()}`;
      return await this.makeRequest(endpoint);
    } catch (error) {
      console.error('Error searching code:', error);
      throw error;
    }
  }

  getRateLimitInfo() {
    return {
      remaining: this.rateLimitRemaining,
      reset: this.rateLimitReset,
      resetIn: this.rateLimitReset ? Math.max(0, this.rateLimitReset - new Date()) : null
    };
  }

  async waitForRateLimit() {
    const rateLimitInfo = this.getRateLimitInfo();
    
    if (rateLimitInfo.remaining <= 10 && rateLimitInfo.resetIn > 0) {
      console.log(`Rate limit low (${rateLimitInfo.remaining} remaining). Waiting ${rateLimitInfo.resetIn}ms...`);
      await new Promise(resolve => setTimeout(resolve, rateLimitInfo.resetIn));
    }
  }

  async getTreeRecursive(owner, repo, treeSha, maxDepth = 3) {
    try {
      const tree = await this.makeRequest(`/repos/${owner}/${repo}/git/trees/${treeSha}?recursive=1`);
      
      // Filter and structure the tree data
      const files = tree.tree
        .filter(item => item.type === 'blob')
        .map(item => ({
          path: item.path,
          sha: item.sha,
          size: item.size,
          type: 'file'
        }));

      const directories = tree.tree
        .filter(item => item.type === 'tree')
        .map(item => ({
          path: item.path,
          sha: item.sha,
          type: 'directory'
        }));

      return {
        files,
        directories,
        truncated: tree.truncated
      };
    } catch (error) {
      console.error('Error getting recursive tree:', error);
      throw error;
    }
  }

  async analyzeDependencies(owner, repo) {
    try {
      const dependencies = await this.getPackageFiles(owner, repo);
      const analysis = {
        totalDependencies: 0,
        production: [],
        development: [],
        security: {
          vulnerabilities: [],
          outdated: []
        }
      };

      if (dependencies.package_json) {
        const pkg = dependencies.package_json;
        if (pkg.dependencies) {
          analysis.production = Object.keys(pkg.dependencies);
          analysis.totalDependencies += analysis.production.length;
        }
        if (pkg.devDependencies) {
          analysis.development = Object.keys(pkg.devDependencies);
          analysis.totalDependencies += analysis.development.length;
        }
      }

      // Additional analysis for other package managers...
      
      return analysis;
    } catch (error) {
      console.error('Error analyzing dependencies:', error);
      throw error;
    }
  }
}