/**
 * Analysis Results UI
 * Creates and manages the UI for displaying repository analysis results
 */

export class AnalysisResultsUI {
  constructor() {
    this.container = null;
    this.isVisible = false;
    this.currentAnalysis = null;
    this.animations = {
      fadeIn: 'fadeIn 0.3s ease-out',
      slideIn: 'slideIn 0.3s ease-out',
      pulse: 'pulse 1s ease-in-out infinite'
    };
  }

  initialize() {
    this.injectStyles();
    this.createContainer();
    console.log('Analysis Results UI initialized');
  }

  injectStyles() {
    if (document.querySelector('style[data-analysis-results]')) {
      return;
    }

    const style = document.createElement('style');
    style.setAttribute('data-analysis-results', 'true');
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      
      @keyframes slideIn {
        from { transform: translateY(-20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
      
      .analysis-results-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(4px);
      }
      
      .analysis-results-modal {
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        width: 90%;
        max-width: 1200px;
        max-height: 90vh;
        overflow: hidden;
        position: relative;
        animation: ${this.animations.slideIn};
      }
      
      .analysis-results-header {
        background: linear-gradient(135deg, #0969da 0%, #0550ae 100%);
        color: white;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .analysis-results-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
      }
      
      .analysis-results-close {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background 0.2s;
      }
      
      .analysis-results-close:hover {
        background: rgba(255, 255, 255, 0.2);
      }
      
      .analysis-results-content {
        padding: 0;
        overflow-y: auto;
        max-height: calc(90vh - 80px);
      }
      
      .analysis-results-tabs {
        display: flex;
        background: #f6f8fa;
        border-bottom: 1px solid #d1d9e0;
        padding: 0 20px;
      }
      
      .analysis-results-tab {
        background: none;
        border: none;
        padding: 12px 16px;
        margin-right: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        color: #656d76;
        border-radius: 8px 8px 0 0;
        transition: all 0.2s;
      }
      
      .analysis-results-tab.active {
        background: white;
        color: #0969da;
        border-bottom: 2px solid #0969da;
      }
      
      .analysis-results-tab:hover:not(.active) {
        color: #0969da;
        background: rgba(9, 105, 218, 0.1);
      }
      
      .analysis-results-tab-content {
        padding: 24px;
        display: none;
      }
      
      .analysis-results-tab-content.active {
        display: block;
        animation: ${this.animations.fadeIn};
      }
      
      .analysis-section {
        margin-bottom: 32px;
      }
      
      .analysis-section:last-child {
        margin-bottom: 0;
      }
      
      .analysis-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2328;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .analysis-section-content {
        background: #f6f8fa;
        border-radius: 8px;
        padding: 16px;
        border: 1px solid #d1d9e0;
      }
      
      .analysis-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e1e4e8;
      }
      
      .analysis-metric:last-child {
        border-bottom: none;
      }
      
      .analysis-metric-label {
        font-weight: 500;
        color: #1f2328;
      }
      
      .analysis-metric-value {
        font-weight: 600;
        color: #0969da;
      }
      
      .analysis-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }
      
      .analysis-list-item {
        padding: 8px 0;
        border-bottom: 1px solid #e1e4e8;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .analysis-list-item:last-child {
        border-bottom: none;
      }
      
      .analysis-list-item::before {
        content: "•";
        color: #0969da;
        font-weight: bold;
        font-size: 16px;
      }
      
      .analysis-code-block {
        background: #f6f8fa;
        border: 1px solid #d1d9e0;
        border-radius: 6px;
        padding: 16px;
        margin: 12px 0;
        overflow-x: auto;
      }
      
      .analysis-code-block pre {
        margin: 0;
        font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
        font-size: 12px;
        line-height: 1.4;
      }
      
      .analysis-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        margin-right: 8px;
        margin-bottom: 4px;
      }
      
      .analysis-badge.success {
        background: #dcfce7;
        color: #166534;
      }
      
      .analysis-badge.warning {
        background: #fef3c7;
        color: #92400e;
      }
      
      .analysis-badge.error {
        background: #fee2e2;
        color: #dc2626;
      }
      
      .analysis-badge.info {
        background: #dbeafe;
        color: #1e40af;
      }
      
      .analysis-progress {
        background: #f3f4f6;
        border-radius: 8px;
        padding: 12px;
        margin: 12px 0;
      }
      
      .analysis-progress-bar {
        background: #e5e7eb;
        border-radius: 4px;
        height: 8px;
        overflow: hidden;
        margin-bottom: 8px;
      }
      
      .analysis-progress-fill {
        background: linear-gradient(90deg, #0969da 0%, #0550ae 100%);
        height: 100%;
        transition: width 0.3s ease;
      }
      
      .analysis-progress-text {
        font-size: 12px;
        color: #6b7280;
        text-align: center;
      }
      
      .analysis-export-button {
        background: #0969da;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: background 0.2s;
        margin-right: 8px;
      }
      
      .analysis-export-button:hover {
        background: #0550ae;
      }
      
      .analysis-export-button.secondary {
        background: #f6f8fa;
        color: #1f2328;
        border: 1px solid #d1d9e0;
      }
      
      .analysis-export-button.secondary:hover {
        background: #e1e4e8;
      }
      
      .analysis-empty-state {
        text-align: center;
        padding: 40px;
        color: #6b7280;
      }
      
      .analysis-empty-state svg {
        width: 48px;
        height: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }
      
      .analysis-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40px;
      }
      
      .analysis-loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #f3f4f6;
        border-top: 3px solid #0969da;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .analysis-error {
        background: #fee2e2;
        border: 1px solid #fecaca;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        color: #dc2626;
      }
      
      .analysis-error-title {
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .analysis-mermaid {
        background: white;
        border: 1px solid #d1d9e0;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
      }
    `;
    
    document.head.appendChild(style);
  }

  createContainer() {
    this.container = document.createElement('div');
    this.container.className = 'analysis-results-overlay';
    this.container.style.display = 'none';
    
    this.container.innerHTML = `
      <div class="analysis-results-modal">
        <div class="analysis-results-header">
          <h2 class="analysis-results-title">Repository Analysis Results</h2>
          <button class="analysis-results-close" data-close>&times;</button>
        </div>
        <div class="analysis-results-content">
          <div class="analysis-results-tabs">
            <button class="analysis-results-tab active" data-tab="overview">Overview</button>
            <button class="analysis-results-tab" data-tab="market">Market Research</button>
            <button class="analysis-results-tab" data-tab="technical">Technical Analysis</button>
            <button class="analysis-results-tab" data-tab="architecture">Architecture</button>
            <button class="analysis-results-tab" data-tab="documentation">Documentation</button>
            <button class="analysis-results-tab" data-tab="project-brief">Project Brief</button>
            <button class="analysis-results-tab" data-tab="implementation">Implementation</button>
          </div>
          
          <div id="overview-tab" class="analysis-results-tab-content active">
            <div class="analysis-loading">
              <div class="analysis-loading-spinner"></div>
              <p>Loading analysis results...</p>
            </div>
          </div>
          
          <div id="market-tab" class="analysis-results-tab-content">
            <div class="analysis-empty-state">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
              </svg>
              <p>Market research data will appear here</p>
            </div>
          </div>
          
          <div id="technical-tab" class="analysis-results-tab-content">
            <div class="analysis-empty-state">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
              </svg>
              <p>Technical analysis will appear here</p>
            </div>
          </div>
          
          <div id="architecture-tab" class="analysis-results-tab-content">
            <div class="analysis-empty-state">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <p>Architecture design will appear here</p>
            </div>
          </div>
          
          <div id="documentation-tab" class="analysis-results-tab-content">
            <div class="analysis-empty-state">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z"/>
              </svg>
              <p>Documentation will appear here</p>
            </div>
          </div>
          
          <div id="project-brief-tab" class="analysis-results-tab-content">
            <div class="analysis-empty-state">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
              </svg>
              <p>Project brief will appear here</p>
            </div>
          </div>
          
          <div id="implementation-tab" class="analysis-results-tab-content">
            <div class="analysis-empty-state">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <p>Implementation guide will appear here</p>
            </div>
          </div>
        </div>
      </div>
    `;
    
    this.setupEventListeners();
    document.body.appendChild(this.container);
  }

  setupEventListeners() {
    // Close button
    this.container.querySelector('[data-close]').addEventListener('click', () => {
      this.hide();
    });
    
    // Click outside to close
    this.container.addEventListener('click', (e) => {
      if (e.target === this.container) {
        this.hide();
      }
    });
    
    // Tab switching
    this.container.querySelectorAll('[data-tab]').forEach(tab => {
      tab.addEventListener('click', () => {
        this.switchTab(tab.dataset.tab);
      });
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (this.isVisible && e.key === 'Escape') {
        this.hide();
      }
    });
  }

  show(analysis) {
    this.currentAnalysis = analysis;
    this.container.style.display = 'flex';
    this.isVisible = true;
    
    // Update title with repository name
    if (analysis.repository) {
      const title = this.container.querySelector('.analysis-results-title');
      title.textContent = `${analysis.repository.fullName} - Analysis Results`;
    }
    
    // Render analysis data
    this.renderAnalysis(analysis);
    
    // Focus management
    this.container.focus();
  }

  hide() {
    this.container.style.display = 'none';
    this.isVisible = false;
    this.currentAnalysis = null;
  }

  switchTab(tabName) {
    // Update tab buttons
    this.container.querySelectorAll('.analysis-results-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    this.container.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update tab content
    this.container.querySelectorAll('.analysis-results-tab-content').forEach(content => {
      content.classList.remove('active');
    });
    this.container.querySelector(`#${tabName}-tab`).classList.add('active');
  }

  renderAnalysis(analysis) {
    // Clear loading states
    this.container.querySelectorAll('.analysis-loading').forEach(loading => {
      loading.style.display = 'none';
    });
    
    // Render each section
    this.renderOverview(analysis);
    this.renderMarketResearch(analysis.marketResearch);
    this.renderTechnicalAnalysis(analysis.technicalAnalysis);
    this.renderArchitecture(analysis.architectureDesign);
    this.renderDocumentation(analysis.documentation);
    this.renderProjectBrief(analysis.projectBrief);
    this.renderImplementation(analysis.implementationGuide);
  }

  renderOverview(analysis) {
    const container = this.container.querySelector('#overview-tab');
    const repo = analysis.repository;
    
    container.innerHTML = `
      <div class="analysis-section">
        <h3 class="analysis-section-title">Repository Information</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Repository</span>
            <span class="analysis-metric-value">${repo.fullName}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Description</span>
            <span class="analysis-metric-value">${repo.description || 'No description'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Primary Language</span>
            <span class="analysis-metric-value">${repo.primaryLanguage || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Stars</span>
            <span class="analysis-metric-value">${repo.stars || 0}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Forks</span>
            <span class="analysis-metric-value">${repo.forks || 0}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Last Updated</span>
            <span class="analysis-metric-value">${repo.lastUpdated ? new Date(repo.lastUpdated).toLocaleDateString() : 'Unknown'}</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Analysis Summary</h3>
        <div class="analysis-section-content">
          <div class="analysis-progress">
            <div class="analysis-progress-bar">
              <div class="analysis-progress-fill" style="width: 100%"></div>
            </div>
            <div class="analysis-progress-text">Analysis Complete</div>
          </div>
          
          <div style="margin-top: 16px;">
            <span class="analysis-badge success">Market Research</span>
            <span class="analysis-badge success">Technical Analysis</span>
            <span class="analysis-badge success">Architecture Design</span>
            <span class="analysis-badge success">Documentation</span>
            <span class="analysis-badge success">Project Brief</span>
            <span class="analysis-badge success">Implementation Guide</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Quick Actions</h3>
        <div class="analysis-section-content">
          <button class="analysis-export-button" onclick="analysisResultsUI.exportAnalysis('json')">
            Export as JSON
          </button>
          <button class="analysis-export-button secondary" onclick="analysisResultsUI.exportAnalysis('markdown')">
            Export as Markdown
          </button>
          <button class="analysis-export-button secondary" onclick="analysisResultsUI.shareAnalysis()">
            Share Analysis
          </button>
        </div>
      </div>
    `;
  }

  renderMarketResearch(marketResearch) {
    if (!marketResearch) return;
    
    const container = this.container.querySelector('#market-tab');
    container.innerHTML = `
      <div class="analysis-section">
        <h3 class="analysis-section-title">Market Overview</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Market Size</span>
            <span class="analysis-metric-value">${marketResearch.marketContext?.size || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Growth Rate</span>
            <span class="analysis-metric-value">${marketResearch.marketContext?.growth || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Project Type</span>
            <span class="analysis-metric-value">${marketResearch.projectType || 'Not specified'}</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Market Trends</h3>
        <div class="analysis-section-content">
          <ul class="analysis-list">
            ${marketResearch.marketContext?.trends?.map(trend => `
              <li class="analysis-list-item">${trend}</li>
            `).join('') || '<li class="analysis-list-item">No trends identified</li>'}
          </ul>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Competitive Analysis</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Market Position</span>
            <span class="analysis-metric-value">${marketResearch.competitorAnalysis?.marketPosition || 'Not specified'}</span>
          </div>
          
          <h4 style="margin: 16px 0 8px 0;">Direct Competitors</h4>
          <ul class="analysis-list">
            ${marketResearch.competitorAnalysis?.directCompetitors?.map(competitor => `
              <li class="analysis-list-item">${competitor}</li>
            `).join('') || '<li class="analysis-list-item">No direct competitors identified</li>'}
          </ul>
          
          <h4 style="margin: 16px 0 8px 0;">Indirect Competitors</h4>
          <ul class="analysis-list">
            ${marketResearch.competitorAnalysis?.indirectCompetitors?.map(competitor => `
              <li class="analysis-list-item">${competitor}</li>
            `).join('') || '<li class="analysis-list-item">No indirect competitors identified</li>'}
          </ul>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Opportunities & Risks</h3>
        <div class="analysis-section-content">
          <h4 style="margin: 0 0 8px 0;">Opportunities</h4>
          <ul class="analysis-list">
            ${marketResearch.opportunities?.gaps?.map(gap => `
              <li class="analysis-list-item">${gap}</li>
            `).join('') || '<li class="analysis-list-item">No specific opportunities identified</li>'}
          </ul>
          
          <h4 style="margin: 16px 0 8px 0;">Risks</h4>
          <ul class="analysis-list">
            ${marketResearch.risks?.technical?.map(risk => `
              <li class="analysis-list-item">${risk}</li>
            `).join('') || '<li class="analysis-list-item">No specific risks identified</li>'}
          </ul>
        </div>
      </div>
    `;
  }

  renderTechnicalAnalysis(technicalAnalysis) {
    if (!technicalAnalysis) return;
    
    const container = this.container.querySelector('#technical-tab');
    container.innerHTML = `
      <div class="analysis-section">
        <h3 class="analysis-section-title">Technology Stack</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Primary Language</span>
            <span class="analysis-metric-value">${technicalAnalysis.primaryLanguage || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Frameworks</span>
            <span class="analysis-metric-value">${technicalAnalysis.frameworks?.join(', ') || 'None specified'}</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Framework Analysis</h3>
        <div class="analysis-section-content">
          ${technicalAnalysis.frameworkAnalysis?.map(framework => `
            <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid #e1e4e8;">
              <h4 style="margin: 0 0 8px 0;">${framework.name}</h4>
              <div class="analysis-metric">
                <span class="analysis-metric-label">Version</span>
                <span class="analysis-metric-value">${framework.version}</span>
              </div>
              <div class="analysis-metric">
                <span class="analysis-metric-label">Maturity</span>
                <span class="analysis-metric-value">${framework.maturity}</span>
              </div>
              <div class="analysis-metric">
                <span class="analysis-metric-label">Recommendation</span>
                <span class="analysis-metric-value">${framework.recommendation}</span>
              </div>
            </div>
          `).join('') || '<p>No framework analysis available</p>'}
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Quality Assessment</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Technical Debt Score</span>
            <span class="analysis-metric-value">${technicalAnalysis.technicalDebt?.score || 'Not assessed'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Scalability Score</span>
            <span class="analysis-metric-value">${technicalAnalysis.scalabilityAssessment?.score || 'Not assessed'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Security Score</span>
            <span class="analysis-metric-value">${technicalAnalysis.securityAnalysis?.score || 'Not assessed'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Performance Score</span>
            <span class="analysis-metric-value">${technicalAnalysis.performanceAnalysis?.score || 'Not assessed'}</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Recommendations</h3>
        <div class="analysis-section-content">
          <ul class="analysis-list">
            ${technicalAnalysis.technicalDebt?.recommendations?.map(rec => `
              <li class="analysis-list-item">${rec}</li>
            `).join('') || '<li class="analysis-list-item">No specific recommendations available</li>'}
          </ul>
        </div>
      </div>
    `;
  }

  renderArchitecture(architectureDesign) {
    if (!architectureDesign) return;
    
    const container = this.container.querySelector('#architecture-tab');
    container.innerHTML = `
      <div class="analysis-section">
        <h3 class="analysis-section-title">System Architecture</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Pattern</span>
            <span class="analysis-metric-value">${architectureDesign.systemArchitecture?.pattern || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Scalability</span>
            <span class="analysis-metric-value">${architectureDesign.systemArchitecture?.scalability || 'Not specified'}</span>
          </div>
          
          <h4 style="margin: 16px 0 8px 0;">Architecture Layers</h4>
          <ul class="analysis-list">
            ${architectureDesign.systemArchitecture?.layers?.map(layer => `
              <li class="analysis-list-item">${layer}</li>
            `).join('') || '<li class="analysis-list-item">No layers specified</li>'}
          </ul>
          
          <h4 style="margin: 16px 0 8px 0;">Components</h4>
          <ul class="analysis-list">
            ${architectureDesign.systemArchitecture?.components?.map(component => `
              <li class="analysis-list-item">${component}</li>
            `).join('') || '<li class="analysis-list-item">No components specified</li>'}
          </ul>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Data Architecture</h3>
        <div class="analysis-section-content">
          <h4 style="margin: 0 0 8px 0;">Storage</h4>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Primary</span>
            <span class="analysis-metric-value">${architectureDesign.dataArchitecture?.storage?.primary || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Cache</span>
            <span class="analysis-metric-value">${architectureDesign.dataArchitecture?.storage?.cache || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Search</span>
            <span class="analysis-metric-value">${architectureDesign.dataArchitecture?.storage?.search || 'Not specified'}</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Deployment Strategy</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Containerization</span>
            <span class="analysis-metric-value">${architectureDesign.deploymentStrategy?.containerization || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Orchestration</span>
            <span class="analysis-metric-value">${architectureDesign.deploymentStrategy?.orchestration || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">CI/CD</span>
            <span class="analysis-metric-value">${architectureDesign.deploymentStrategy?.cicd || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Monitoring</span>
            <span class="analysis-metric-value">${architectureDesign.deploymentStrategy?.monitoring || 'Not specified'}</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Recommendations</h3>
        <div class="analysis-section-content">
          <ul class="analysis-list">
            ${architectureDesign.recommendations?.map(rec => `
              <li class="analysis-list-item">${rec}</li>
            `).join('') || '<li class="analysis-list-item">No specific recommendations available</li>'}
          </ul>
        </div>
      </div>
    `;
  }

  renderDocumentation(documentation) {
    if (!documentation) return;
    
    const container = this.container.querySelector('#documentation-tab');
    container.innerHTML = `
      <div class="analysis-section">
        <h3 class="analysis-section-title">Documentation Assessment</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">User Guide</span>
            <span class="analysis-metric-value">${documentation.userGuide?.completeness || 0}% complete</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">API Documentation</span>
            <span class="analysis-metric-value">${documentation.apiDocumentation?.completeness || 0}% complete</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Development Guide</span>
            <span class="analysis-metric-value">${documentation.developmentGuide?.completeness || 0}% complete</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Deployment Guide</span>
            <span class="analysis-metric-value">${documentation.deploymentGuide?.completeness || 0}% complete</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Documentation Breakdown</h3>
        <div class="analysis-section-content">
          ${Object.entries(documentation).map(([key, value]) => `
            <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid #e1e4e8;">
              <h4 style="margin: 0 0 8px 0;">${value.title || key}</h4>
              <div class="analysis-metric">
                <span class="analysis-metric-label">Completeness</span>
                <span class="analysis-metric-value">${value.completeness || 0}%</span>
              </div>
              ${value.sections ? `
                <h5 style="margin: 12px 0 6px 0;">Sections</h5>
                <ul class="analysis-list">
                  ${value.sections.map(section => `
                    <li class="analysis-list-item">${section}</li>
                  `).join('')}
                </ul>
              ` : ''}
              ${value.endpoints ? `
                <div class="analysis-metric">
                  <span class="analysis-metric-label">Endpoints</span>
                  <span class="analysis-metric-value">${value.endpoints}</span>
                </div>
              ` : ''}
              ${value.examples ? `
                <div class="analysis-metric">
                  <span class="analysis-metric-label">Examples</span>
                  <span class="analysis-metric-value">${value.examples}</span>
                </div>
              ` : ''}
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  renderProjectBrief(projectBrief) {
    if (!projectBrief) return;
    
    const container = this.container.querySelector('#project-brief-tab');
    container.innerHTML = `
      <div class="analysis-section">
        <h3 class="analysis-section-title">Executive Summary</h3>
        <div class="analysis-section-content">
          <h4 style="margin: 0 0 8px 0;">${projectBrief.executiveSummary?.title || 'Project Brief'}</h4>
          <p style="margin: 0 0 12px 0;">${projectBrief.executiveSummary?.overview || 'No overview available'}</p>
          
          <h5 style="margin: 12px 0 6px 0;">Key Points</h5>
          <ul class="analysis-list">
            ${projectBrief.executiveSummary?.keyPoints?.map(point => `
              <li class="analysis-list-item">${point}</li>
            `).join('') || '<li class="analysis-list-item">No key points specified</li>'}
          </ul>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Problem & Solution</h3>
        <div class="analysis-section-content">
          <h4 style="margin: 0 0 8px 0;">Problem Statement</h4>
          <p style="margin: 0 0 12px 0;">${projectBrief.problemStatement?.problem || 'No problem statement available'}</p>
          
          <h4 style="margin: 16px 0 8px 0;">Solution Overview</h4>
          <p style="margin: 0 0 12px 0;">${projectBrief.solutionOverview?.approach || 'No solution overview available'}</p>
          
          <h5 style="margin: 12px 0 6px 0;">Key Features</h5>
          <ul class="analysis-list">
            ${projectBrief.solutionOverview?.features?.map(feature => `
              <li class="analysis-list-item">${feature}</li>
            `).join('') || '<li class="analysis-list-item">No features specified</li>'}
          </ul>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Business Value</h3>
        <div class="analysis-section-content">
          <h4 style="margin: 0 0 8px 0;">Target Audience</h4>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Primary</span>
            <span class="analysis-metric-value">${projectBrief.targetAudience?.primary || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Secondary</span>
            <span class="analysis-metric-value">${projectBrief.targetAudience?.secondary || 'Not specified'}</span>
          </div>
          
          <h4 style="margin: 16px 0 8px 0;">Success Metrics</h4>
          <ul class="analysis-list">
            ${projectBrief.businessValue?.benefits?.map(benefit => `
              <li class="analysis-list-item">${benefit}</li>
            `).join('') || '<li class="analysis-list-item">No benefits specified</li>'}
          </ul>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Timeline & Resources</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Duration</span>
            <span class="analysis-metric-value">${projectBrief.timeline?.duration || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Team Size</span>
            <span class="analysis-metric-value">${projectBrief.resources?.team || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Budget</span>
            <span class="analysis-metric-value">${projectBrief.resources?.budget || 'Not specified'}</span>
          </div>
          
          <h4 style="margin: 16px 0 8px 0;">Project Phases</h4>
          <ul class="analysis-list">
            ${projectBrief.timeline?.phases?.map(phase => `
              <li class="analysis-list-item">${phase}</li>
            `).join('') || '<li class="analysis-list-item">No phases specified</li>'}
          </ul>
        </div>
      </div>
    `;
  }

  renderImplementation(implementationGuide) {
    if (!implementationGuide) return;
    
    const container = this.container.querySelector('#implementation-tab');
    container.innerHTML = `
      <div class="analysis-section">
        <h3 class="analysis-section-title">Setup Instructions</h3>
        <div class="analysis-section-content">
          <h4 style="margin: 0 0 8px 0;">Prerequisites</h4>
          <ul class="analysis-list">
            ${implementationGuide.setupInstructions?.prerequisites?.map(prereq => `
              <li class="analysis-list-item">${prereq}</li>
            `).join('') || '<li class="analysis-list-item">No prerequisites specified</li>'}
          </ul>
          
          <h4 style="margin: 16px 0 8px 0;">Setup Steps</h4>
          <ol style="padding-left: 20px;">
            ${implementationGuide.setupInstructions?.steps?.map(step => `
              <li style="margin-bottom: 4px;">${step}</li>
            `).join('') || '<li>No setup steps specified</li>'}
          </ol>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Development Workflow</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Git Flow</span>
            <span class="analysis-metric-value">${implementationGuide.developmentWorkflow?.gitFlow || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Code Review</span>
            <span class="analysis-metric-value">${implementationGuide.developmentWorkflow?.codeReview || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Testing</span>
            <span class="analysis-metric-value">${implementationGuide.developmentWorkflow?.testing || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Deployment</span>
            <span class="analysis-metric-value">${implementationGuide.developmentWorkflow?.deployment || 'Not specified'}</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">Testing Strategy</h3>
        <div class="analysis-section-content">
          <div class="analysis-metric">
            <span class="analysis-metric-label">Unit Testing</span>
            <span class="analysis-metric-value">${implementationGuide.testingStrategy?.unit || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Integration Testing</span>
            <span class="analysis-metric-value">${implementationGuide.testingStrategy?.integration || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">E2E Testing</span>
            <span class="analysis-metric-value">${implementationGuide.testingStrategy?.e2e || 'Not specified'}</span>
          </div>
          <div class="analysis-metric">
            <span class="analysis-metric-label">Coverage Target</span>
            <span class="analysis-metric-value">${implementationGuide.testingStrategy?.coverage || 'Not specified'}</span>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <h3 class="analysis-section-title">LLM Prompts</h3>
        <div class="analysis-section-content">
          <h4 style="margin: 0 0 8px 0;">System Prompt</h4>
          <div class="analysis-code-block">
            <pre>${implementationGuide.llmPrompts?.systemPrompt || 'No system prompt available'}</pre>
          </div>
          
          <h4 style="margin: 16px 0 8px 0;">Implementation Prompts</h4>
          <ul class="analysis-list">
            ${implementationGuide.llmPrompts?.implementationPrompts?.map(prompt => `
              <li class="analysis-list-item">${prompt}</li>
            `).join('') || '<li class="analysis-list-item">No implementation prompts available</li>'}
          </ul>
        </div>
      </div>
    `;
  }

  showProgress(message, percentage) {
    const overviewTab = this.container.querySelector('#overview-tab');
    const progressContainer = overviewTab.querySelector('.analysis-progress');
    
    if (progressContainer) {
      const progressFill = progressContainer.querySelector('.analysis-progress-fill');
      const progressText = progressContainer.querySelector('.analysis-progress-text');
      
      if (progressFill) {
        progressFill.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
      }
      
      if (progressText) {
        progressText.textContent = message;
      }
    }
  }

  showError(error) {
    const activeTab = this.container.querySelector('.analysis-results-tab-content.active');
    if (activeTab) {
      activeTab.innerHTML = `
        <div class="analysis-error">
          <div class="analysis-error-title">Analysis Error</div>
          <div>${error.message || 'An error occurred during analysis'}</div>
        </div>
      `;
    }
  }

  exportAnalysis(format) {
    if (!this.currentAnalysis) {
      alert('No analysis data to export');
      return;
    }

    const filename = `${this.currentAnalysis.repository.repo}-analysis`;
    
    if (format === 'json') {
      this.downloadFile(
        JSON.stringify(this.currentAnalysis, null, 2),
        `${filename}.json`,
        'application/json'
      );
    } else if (format === 'markdown') {
      const markdown = this.generateMarkdownReport(this.currentAnalysis);
      this.downloadFile(markdown, `${filename}.md`, 'text/markdown');
    }
  }

  generateMarkdownReport(analysis) {
    // Basic markdown generation - could be expanded
    return `# ${analysis.repository.fullName} - Analysis Report

## Repository Information
- **Repository**: ${analysis.repository.fullName}
- **Description**: ${analysis.repository.description || 'No description'}
- **Primary Language**: ${analysis.repository.primaryLanguage || 'Not specified'}
- **Stars**: ${analysis.repository.stars || 0}
- **Forks**: ${analysis.repository.forks || 0}

## Analysis Summary
This analysis was completed on ${new Date().toLocaleDateString()}.

### Market Research
${analysis.marketResearch ? 'Market research completed' : 'No market research available'}

### Technical Analysis
${analysis.technicalAnalysis ? 'Technical analysis completed' : 'No technical analysis available'}

### Architecture Design
${analysis.architectureDesign ? 'Architecture design completed' : 'No architecture design available'}

### Documentation
${analysis.documentation ? 'Documentation analysis completed' : 'No documentation analysis available'}

### Project Brief
${analysis.projectBrief ? 'Project brief completed' : 'No project brief available'}

### Implementation Guide
${analysis.implementationGuide ? 'Implementation guide completed' : 'No implementation guide available'}
`;
  }

  downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  shareAnalysis() {
    if (!this.currentAnalysis) {
      alert('No analysis data to share');
      return;
    }

    const shareData = {
      title: `${this.currentAnalysis.repository.fullName} - Analysis Report`,
      text: `Repository analysis for ${this.currentAnalysis.repository.fullName}`,
      url: this.currentAnalysis.repository.url
    };

    if (navigator.share) {
      navigator.share(shareData).catch(err => {
        console.error('Error sharing:', err);
        this.fallbackShare(shareData);
      });
    } else {
      this.fallbackShare(shareData);
    }
  }

  fallbackShare(shareData) {
    const shareText = `${shareData.title}\n${shareData.text}\n${shareData.url}`;
    
    if (navigator.clipboard) {
      navigator.clipboard.writeText(shareText).then(() => {
        alert('Analysis summary copied to clipboard!');
      }).catch(err => {
        console.error('Error copying to clipboard:', err);
        prompt('Copy this text to share:', shareText);
      });
    } else {
      prompt('Copy this text to share:', shareText);
    }
  }

  cleanup() {
    if (this.container) {
      this.container.remove();
    }
    
    const styles = document.querySelector('style[data-analysis-results]');
    if (styles) {
      styles.remove();
    }
    
    console.log('Analysis Results UI cleaned up');
  }
}

// Global instance for button callbacks
window.analysisResultsUI = new AnalysisResultsUI();