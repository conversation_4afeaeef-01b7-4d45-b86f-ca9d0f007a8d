/**
 * GitHub Page Detector
 * Detects and extracts information from GitHub repository pages
 */

export class GitHubPageDetector {
  constructor() {
    this.repositoryPattern = /^https?:\/\/github\.com\/([^\/]+)\/([^\/]+)(?:\/.*)?$/;
    this.observers = new Map();
    this.currentRepository = null;
    this.pageChangeCallbacks = [];
  }

  initialize() {
    // Set up mutation observer for SPA navigation
    this.setupNavigationObserver();
    
    // Check current page on initialization
    this.checkCurrentPage();
    
    console.log('GitHub Page Detector initialized');
  }

  setupNavigationObserver() {
    // Observer for URL changes (GitHub uses pushState navigation)
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if the URL has changed
          const newRepository = this.extractRepositoryInfo();
          if (newRepository && this.hasRepositoryChanged(newRepository)) {
            this.handleRepositoryChange(newRepository);
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.observers.set('navigation', observer);

    // Also listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', () => {
      setTimeout(() => this.checkCurrentPage(), 100);
    });
  }

  checkCurrentPage() {
    const repositoryInfo = this.extractRepositoryInfo();
    
    if (repositoryInfo) {
      if (this.hasRepositoryChanged(repositoryInfo)) {
        this.handleRepositoryChange(repositoryInfo);
      }
    } else {
      // Not on a repository page
      if (this.currentRepository) {
        this.handleRepositoryChange(null);
      }
    }
  }

  extractRepositoryInfo() {
    const url = window.location.href;
    const match = url.match(this.repositoryPattern);
    
    if (!match) {
      return null;
    }

    const [, owner, repo] = match;
    
    // Extract additional information from the page
    const pageInfo = this.extractPageSpecificInfo();
    
    return {
      owner,
      repo,
      fullName: `${owner}/${repo}`,
      url: `https://github.com/${owner}/${repo}`,
      currentUrl: url,
      pageType: this.detectPageType(url),
      ...pageInfo
    };
  }

  extractPageSpecificInfo() {
    const info = {};
    
    // Extract repository description
    const descriptionElement = document.querySelector('[data-pjax="#repo-content-pjax-container"] p, .repository-content p');
    if (descriptionElement) {
      info.description = descriptionElement.textContent.trim();
    }

    // Extract repository stats
    const statsElements = document.querySelectorAll('[data-pjax="#repo-content-pjax-container"] .Counter, .social-count');
    if (statsElements.length > 0) {
      info.stars = this.extractStat('star', statsElements);
      info.forks = this.extractStat('fork', statsElements);
      info.watchers = this.extractStat('watch', statsElements);
    }

    // Extract primary language
    const languageElement = document.querySelector('[data-ga-click*="Language"] .color-fg-default');
    if (languageElement) {
      info.primaryLanguage = languageElement.textContent.trim();
    }

    // Extract topics/tags
    const topicElements = document.querySelectorAll('[data-ga-click*="topic"] .topic-tag');
    if (topicElements.length > 0) {
      info.topics = Array.from(topicElements).map(el => el.textContent.trim());
    }

    // Extract README availability
    info.hasReadme = !!document.querySelector('[data-testid="readme"]');

    // Extract license information
    const licenseElement = document.querySelector('[data-testid="license-link"]');
    if (licenseElement) {
      info.license = licenseElement.textContent.trim();
    }

    // Extract last updated date
    const updatedElement = document.querySelector('relative-time[datetime]');
    if (updatedElement) {
      info.lastUpdated = updatedElement.getAttribute('datetime');
    }

    return info;
  }

  extractStat(type, elements) {
    const element = Array.from(elements).find(el => {
      const parent = el.closest('a');
      return parent && parent.href.includes(type);
    });
    
    return element ? this.parseStatValue(element.textContent) : 0;
  }

  parseStatValue(text) {
    const cleanText = text.replace(/,/g, '').trim();
    const value = parseFloat(cleanText);
    
    if (cleanText.includes('k')) {
      return Math.round(value * 1000);
    } else if (cleanText.includes('m')) {
      return Math.round(value * 1000000);
    }
    
    return Math.round(value) || 0;
  }

  detectPageType(url) {
    if (url.includes('/issues')) {
      return 'issues';
    } else if (url.includes('/pull')) {
      return 'pulls';
    } else if (url.includes('/releases')) {
      return 'releases';
    } else if (url.includes('/commits')) {
      return 'commits';
    } else if (url.includes('/tree/')) {
      return 'tree';
    } else if (url.includes('/blob/')) {
      return 'blob';
    } else if (url.includes('/settings')) {
      return 'settings';
    } else if (url.includes('/actions')) {
      return 'actions';
    } else if (url.includes('/wiki')) {
      return 'wiki';
    } else if (url.includes('/projects')) {
      return 'projects';
    } else if (url.includes('/security')) {
      return 'security';
    } else if (url.includes('/pulse')) {
      return 'pulse';
    } else if (url.includes('/graphs')) {
      return 'graphs';
    } else if (url.match(/\/[^\/]+\/[^\/]+\/?$/)) {
      return 'repository';
    }
    
    return 'other';
  }

  hasRepositoryChanged(newRepository) {
    if (!this.currentRepository && !newRepository) {
      return false;
    }
    
    if (!this.currentRepository || !newRepository) {
      return true;
    }
    
    return this.currentRepository.fullName !== newRepository.fullName;
  }

  handleRepositoryChange(newRepository) {
    const previousRepository = this.currentRepository;
    this.currentRepository = newRepository;
    
    // Notify all callbacks
    this.pageChangeCallbacks.forEach(callback => {
      try {
        callback(newRepository, previousRepository);
      } catch (error) {
        console.error('Error in page change callback:', error);
      }
    });
  }

  isRepositoryPage() {
    return !!this.currentRepository;
  }

  getCurrentRepository() {
    return this.currentRepository;
  }

  isAnalyzableRepository() {
    if (!this.currentRepository) {
      return false;
    }

    // Check if this is a repository page (not organization profile, etc.)
    return this.currentRepository.pageType === 'repository' || 
           this.currentRepository.pageType === 'tree' ||
           this.currentRepository.pageType === 'blob';
  }

  getRepositoryLanguages() {
    const languageElements = document.querySelectorAll('.BorderGrid-row .text-bold');
    const languages = [];
    
    languageElements.forEach(element => {
      const language = element.textContent.trim();
      const percentageElement = element.nextElementSibling;
      
      if (percentageElement && percentageElement.textContent.includes('%')) {
        languages.push({
          name: language,
          percentage: parseFloat(percentageElement.textContent.replace('%', ''))
        });
      }
    });
    
    return languages;
  }

  getRepositoryContributors() {
    const contributorElements = document.querySelectorAll('.contrib-person');
    const contributors = [];
    
    contributorElements.forEach(element => {
      const link = element.querySelector('a');
      const img = element.querySelector('img');
      
      if (link && img) {
        contributors.push({
          username: link.getAttribute('href').replace('/', ''),
          avatar: img.getAttribute('src'),
          name: img.getAttribute('alt')
        });
      }
    });
    
    return contributors;
  }

  getRepositoryFileStructure() {
    const fileElements = document.querySelectorAll('.js-navigation-item');
    const files = [];
    
    fileElements.forEach(element => {
      const link = element.querySelector('a');
      const iconElement = element.querySelector('svg');
      
      if (link) {
        const name = link.textContent.trim();
        const isDirectory = iconElement && iconElement.classList.contains('octicon-file-directory');
        
        files.push({
          name,
          type: isDirectory ? 'directory' : 'file',
          path: link.getAttribute('href')
        });
      }
    });
    
    return files;
  }

  getRepositoryMetrics() {
    const metrics = {};
    
    // Extract commit count
    const commitElement = document.querySelector('[data-testid="commits-count"]');
    if (commitElement) {
      metrics.commits = this.parseStatValue(commitElement.textContent);
    }
    
    // Extract branch count
    const branchElement = document.querySelector('[data-testid="branches-count"]');
    if (branchElement) {
      metrics.branches = this.parseStatValue(branchElement.textContent);
    }
    
    // Extract release count
    const releaseElement = document.querySelector('[data-testid="releases-count"]');
    if (releaseElement) {
      metrics.releases = this.parseStatValue(releaseElement.textContent);
    }
    
    // Extract package count
    const packageElement = document.querySelector('[data-testid="packages-count"]');
    if (packageElement) {
      metrics.packages = this.parseStatValue(packageElement.textContent);
    }
    
    return metrics;
  }

  extractReadmeContent() {
    const readmeElement = document.querySelector('[data-testid="readme"] .markdown-body');
    
    if (readmeElement) {
      return {
        html: readmeElement.innerHTML,
        text: readmeElement.textContent,
        sections: this.extractReadmeSections(readmeElement)
      };
    }
    
    return null;
  }

  extractReadmeSections(readmeElement) {
    const sections = [];
    const headings = readmeElement.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    headings.forEach(heading => {
      sections.push({
        level: parseInt(heading.tagName.replace('H', '')),
        title: heading.textContent.trim(),
        id: heading.id || null
      });
    });
    
    return sections;
  }

  waitForPageLoad() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve, { once: true });
      }
    });
  }

  waitForRepositoryData() {
    return new Promise((resolve) => {
      const checkData = () => {
        const repo = this.extractRepositoryInfo();
        if (repo && repo.description) {
          resolve(repo);
        } else {
          setTimeout(checkData, 100);
        }
      };
      
      checkData();
    });
  }

  onPageChange(callback) {
    this.pageChangeCallbacks.push(callback);
  }

  removePageChangeCallback(callback) {
    const index = this.pageChangeCallbacks.indexOf(callback);
    if (index > -1) {
      this.pageChangeCallbacks.splice(index, 1);
    }
  }

  getInjectionPoints() {
    const injectionPoints = {};
    
    // Main repository header area
    const repoHeader = document.querySelector('[data-testid="repository-header"]');
    if (repoHeader) {
      injectionPoints.header = repoHeader;
    }
    
    // Repository navigation area
    const repoNav = document.querySelector('.UnderlineNav');
    if (repoNav) {
      injectionPoints.navigation = repoNav;
    }
    
    // Repository content area
    const repoContent = document.querySelector('#repo-content-pjax-container');
    if (repoContent) {
      injectionPoints.content = repoContent;
    }
    
    // Sidebar area
    const sidebar = document.querySelector('.Layout-sidebar');
    if (sidebar) {
      injectionPoints.sidebar = sidebar;
    }
    
    return injectionPoints;
  }

  createAnalysisButton() {
    const button = document.createElement('button');
    button.className = 'btn btn-sm btn-primary';
    button.style.marginLeft = '8px';
    button.innerHTML = `
      <svg class="octicon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0zM8 0a8 8 0 100 16A8 8 0 008 0zm.5 4.75a.75.75 0 00-1.5 0v3.5a.75.75 0 00.471.696l2.5 1a.75.75 0 00.557-1.392L8.5 7.742V4.75z"/>
      </svg>
      <span style="margin-left: 4px;">Analyze Repository</span>
    `;
    
    return button;
  }

  injectAnalysisButton(callback) {
    const injectionPoints = this.getInjectionPoints();
    
    if (injectionPoints.header) {
      // Try to find the repository actions area
      const actionsContainer = injectionPoints.header.querySelector('.d-flex.flex-wrap');
      
      if (actionsContainer) {
        const button = this.createAnalysisButton();
        button.addEventListener('click', callback);
        
        // Remove existing button if present
        const existingButton = actionsContainer.querySelector('[data-chrome-repo-analyzer]');
        if (existingButton) {
          existingButton.remove();
        }
        
        button.setAttribute('data-chrome-repo-analyzer', 'true');
        actionsContainer.appendChild(button);
        
        return button;
      }
    }
    
    return null;
  }

  createProgressIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'repo-analyzer-progress';
    indicator.style.cssText = `
      position: fixed;
      top: 60px;
      right: 16px;
      background: #0969da;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      font-size: 14px;
      max-width: 300px;
      display: none;
    `;
    
    indicator.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <div class="repo-analyzer-spinner" style="
          width: 16px;
          height: 16px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
        <div class="repo-analyzer-status">Analyzing repository...</div>
      </div>
      <div class="repo-analyzer-progress-bar" style="
        width: 100%;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        margin-top: 8px;
        overflow: hidden;
      ">
        <div class="repo-analyzer-progress-fill" style="
          width: 0%;
          height: 100%;
          background: white;
          transition: width 0.3s ease;
        "></div>
      </div>
    `;
    
    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    
    if (!document.head.querySelector('style[data-repo-analyzer]')) {
      style.setAttribute('data-repo-analyzer', 'true');
      document.head.appendChild(style);
    }
    
    document.body.appendChild(indicator);
    return indicator;
  }

  showProgressIndicator(message = 'Analyzing repository...') {
    let indicator = document.querySelector('.repo-analyzer-progress');
    
    if (!indicator) {
      indicator = this.createProgressIndicator();
    }
    
    const statusElement = indicator.querySelector('.repo-analyzer-status');
    if (statusElement) {
      statusElement.textContent = message;
    }
    
    indicator.style.display = 'block';
    return indicator;
  }

  updateProgress(percentage, message) {
    const indicator = document.querySelector('.repo-analyzer-progress');
    
    if (indicator) {
      const statusElement = indicator.querySelector('.repo-analyzer-status');
      const progressFill = indicator.querySelector('.repo-analyzer-progress-fill');
      
      if (statusElement && message) {
        statusElement.textContent = message;
      }
      
      if (progressFill) {
        progressFill.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
      }
    }
  }

  hideProgressIndicator() {
    const indicator = document.querySelector('.repo-analyzer-progress');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }

  cleanup() {
    // Remove all observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    
    // Remove event listeners
    this.pageChangeCallbacks = [];
    
    // Remove injected elements
    const injectedElements = document.querySelectorAll('[data-chrome-repo-analyzer]');
    injectedElements.forEach(element => element.remove());
    
    // Remove progress indicator
    const progressIndicator = document.querySelector('.repo-analyzer-progress');
    if (progressIndicator) {
      progressIndicator.remove();
    }
    
    // Remove styles
    const styles = document.querySelectorAll('style[data-repo-analyzer]');
    styles.forEach(style => style.remove());
    
    console.log('GitHub Page Detector cleaned up');
  }
}