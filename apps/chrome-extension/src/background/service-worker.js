/**
 * Chrome Extension Service Worker
 * Handles background tasks, storage, and communication with content scripts
 */

import { AgentOrchestrator } from '../shared/agent-orchestrator.js';
import { StorageManager } from '../shared/storage-manager.js';
import { SecurityManager } from '../shared/security-manager.js';

class GitHubAnalyzerServiceWorker {
  constructor() {
    this.orchestrator = new AgentOrchestrator();
    this.storageManager = new StorageManager();
    this.securityManager = new SecurityManager();
    this.setupMessageHandlers();
    this.setupContextMenus();
  }

  setupMessageHandlers() {
    // Handle messages from content scripts and popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep the message channel open for async responses
    });
  }

  setupContextMenus() {
    chrome.contextMenus.create({
      id: 'analyze-repository',
      title: 'Analyze GitHub Repository',
      contexts: ['page'],
      documentUrlPatterns: ['https://github.com/*']
    });

    chrome.contextMenus.onClicked.addListener((info, tab) => {
      if (info.menuItemId === 'analyze-repository') {
        this.analyzeRepository(tab);
      }
    });
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      const { action, data } = request;

      switch (action) {
        case 'ANALYZE_REPOSITORY':
          const analysis = await this.analyzeRepository(sender.tab, data);
          sendResponse({ success: true, data: analysis });
          break;

        case 'GET_SETTINGS':
          const settings = await this.storageManager.getSettings();
          sendResponse({ success: true, data: settings });
          break;

        case 'UPDATE_SETTINGS':
          await this.storageManager.updateSettings(data);
          sendResponse({ success: true });
          break;

        case 'GET_ANALYSIS_HISTORY':
          const history = await this.storageManager.getAnalysisHistory();
          sendResponse({ success: true, data: history });
          break;

        case 'CLEAR_HISTORY':
          await this.storageManager.clearHistory();
          sendResponse({ success: true });
          break;

        case 'CHECK_AUTH':
          const authStatus = await this.securityManager.checkAuthStatus();
          sendResponse({ success: true, data: authStatus });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Service worker error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async analyzeRepository(tab, repositoryData) {
    try {
      // Validate GitHub URL
      if (!this.isGitHubRepository(tab.url)) {
        throw new Error('Not a GitHub repository URL');
      }

      // Extract repository information
      const repoInfo = this.extractRepositoryInfo(tab.url, repositoryData);
      
      // Check authentication and API limits
      const authStatus = await this.securityManager.checkAuthStatus();
      if (!authStatus.isAuthenticated) {
        throw new Error('Please configure API keys in settings');
      }

      // Update extension badge to show analysis is in progress
      chrome.action.setBadgeText({
        text: '...',
        tabId: tab.id
      });
      chrome.action.setBadgeBackgroundColor({
        color: '#FFA500',
        tabId: tab.id
      });

      // Start analysis using agent orchestrator
      const analysis = await this.orchestrator.analyzeRepository(repoInfo);

      // Store analysis result
      await this.storageManager.saveAnalysis(repoInfo, analysis);

      // Update badge to show completion
      chrome.action.setBadgeText({
        text: '✓',
        tabId: tab.id
      });
      chrome.action.setBadgeBackgroundColor({
        color: '#4CAF50',
        tabId: tab.id
      });

      // Clear badge after 5 seconds
      setTimeout(() => {
        chrome.action.setBadgeText({
          text: '',
          tabId: tab.id
        });
      }, 5000);

      return analysis;
    } catch (error) {
      // Update badge to show error
      chrome.action.setBadgeText({
        text: '!',
        tabId: tab.id
      });
      chrome.action.setBadgeBackgroundColor({
        color: '#F44336',
        tabId: tab.id
      });

      // Clear badge after 5 seconds
      setTimeout(() => {
        chrome.action.setBadgeText({
          text: '',
          tabId: tab.id
        });
      }, 5000);

      throw error;
    }
  }

  isGitHubRepository(url) {
    return url && url.includes('github.com') && url.match(/github\.com\/[^\/]+\/[^\/]+/);
  }

  extractRepositoryInfo(url, additionalData = {}) {
    const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (!match) {
      throw new Error('Invalid GitHub repository URL');
    }

    return {
      owner: match[1],
      repo: match[2],
      url: url,
      timestamp: new Date().toISOString(),
      ...additionalData
    };
  }
}

// Initialize the service worker
const serviceWorker = new GitHubAnalyzerServiceWorker();

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('GitHub Analyzer extension installed');
    // Options page functionality would be implemented here
  }
});

// Handle tab updates to detect GitHub repository pages
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url && serviceWorker.isGitHubRepository(tab.url)) {
    // Clear any existing badge
    chrome.action.setBadgeText({
      text: '',
      tabId: tabId
    });
  }
});

// Keep service worker alive
chrome.runtime.onStartup.addListener(() => {
  console.log('GitHub Analyzer extension started');
});

export { GitHubAnalyzerServiceWorker };