/**
 * GitHub Analyzer Content Script Styles
 * Styles for the analysis UI injected into GitHub pages
 */

/* Analysis Button Styles */
.github-analyzer-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #0969da;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 8px;
  text-decoration: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
}

.github-analyzer-button:hover {
  background: #0860ca;
  transform: translateY(-1px);
}

.github-analyzer-button:active {
  transform: translateY(0);
}

.github-analyzer-button.analyzing {
  background: #fb8500;
  cursor: not-allowed;
}

.github-analyzer-button.completed {
  background: #2da44e;
}

.github-analyzer-button.error {
  background: #da3633;
}

/* <PERSON><PERSON> Icon */
.github-analyzer-button .icon {
  width: 16px;
  height: 16px;
  display: inline-block;
}

.github-analyzer-button .icon svg {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

/* Loading Spinner */
.github-analyzer-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Analysis Modal Styles */
.github-analyzer-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
}

.github-analyzer-modal-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 80vw;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
}

.github-analyzer-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e1e4e8;
}

.github-analyzer-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #24292f;
  margin: 0;
}

.github-analyzer-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #656d76;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.github-analyzer-modal-close:hover {
  background: #f6f8fa;
  color: #24292f;
}

/* Analysis Progress */
.github-analyzer-progress {
  margin-bottom: 20px;
}

.github-analyzer-progress-bar {
  width: 100%;
  height: 8px;
  background: #f6f8fa;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.github-analyzer-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0969da, #2da44e);
  transition: width 0.3s ease;
  border-radius: 4px;
}

.github-analyzer-progress-text {
  font-size: 14px;
  color: #656d76;
  text-align: center;
}

/* Analysis Results */
.github-analyzer-results {
  margin-top: 20px;
}

.github-analyzer-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  background: #f6f8fa;
}

.github-analyzer-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.github-analyzer-section-content {
  font-size: 14px;
  line-height: 1.6;
  color: #656d76;
}

.github-analyzer-section-content pre {
  background: white;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 13px;
  margin: 8px 0;
}

.github-analyzer-section-content code {
  background: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 13px;
}

/* Error States */
.github-analyzer-error {
  background: #ffeef0;
  border: 1px solid #ffc1c8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.github-analyzer-error-title {
  font-size: 14px;
  font-weight: 600;
  color: #cf222e;
  margin-bottom: 8px;
}

.github-analyzer-error-message {
  font-size: 13px;
  color: #82071e;
  line-height: 1.4;
}

/* Success States */
.github-analyzer-success {
  background: #dafbe1;
  border: 1px solid #a2eeaa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.github-analyzer-success-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a7f37;
  margin-bottom: 8px;
}

.github-analyzer-success-message {
  font-size: 13px;
  color: #116329;
  line-height: 1.4;
}

/* Action Buttons */
.github-analyzer-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e1e4e8;
}

.github-analyzer-action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.github-analyzer-action-btn.primary {
  background: #0969da;
  color: white;
}

.github-analyzer-action-btn.primary:hover {
  background: #0860ca;
}

.github-analyzer-action-btn.secondary {
  background: #f6f8fa;
  color: #24292f;
  border: 1px solid #e1e4e8;
}

.github-analyzer-action-btn.secondary:hover {
  background: #f3f4f6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .github-analyzer-modal-content {
    max-width: 95vw;
    max-height: 95vh;
    margin: 8px;
  }
  
  .github-analyzer-actions {
    flex-direction: column;
  }
  
  .github-analyzer-action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .github-analyzer-modal-content {
    background: #0d1117;
    color: #f0f6fc;
  }
  
  .github-analyzer-modal-title {
    color: #f0f6fc;
  }
  
  .github-analyzer-modal-header {
    border-bottom-color: #30363d;
  }
  
  .github-analyzer-section {
    background: #161b22;
    border-color: #30363d;
  }
  
  .github-analyzer-section-title {
    color: #f0f6fc;
  }
  
  .github-analyzer-section-content {
    color: #8b949e;
  }
  
  .github-analyzer-section-content pre,
  .github-analyzer-section-content code {
    background: #21262d;
    color: #f0f6fc;
  }
  
  .github-analyzer-action-btn.secondary {
    background: #21262d;
    color: #f0f6fc;
    border-color: #30363d;
  }
  
  .github-analyzer-action-btn.secondary:hover {
    background: #30363d;
  }
}

/* Animation Effects */
.github-analyzer-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.github-analyzer-slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}