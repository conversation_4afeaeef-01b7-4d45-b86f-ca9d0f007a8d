/**
 * GitHub Analyzer Content Script
 * Runs on GitHub pages to detect repositories and provide analysis UI
 */

class GitHubAnalyzer {
  constructor() {
    console.log('GitHubAnalyzer: Initializing content script');
    this.isAnalyzing = false;
    this.currentRepository = null;
    
    this.init();
  }

  async init() {
    console.log('GitHubAnalyzer: Starting initialization');
    
    // Wait for page to be ready
    if (document.readyState === 'loading') {
      console.log('GitHubAnalyzer: Waiting for DOM ready');
      document.addEventListener('DOMContentLoaded', () => this.setupAnalyzer());
    } else {
      console.log('GitHubAnalyzer: DOM already ready, setting up analyzer');
      this.setupAnalyzer();
    }
  }

  setupAnalyzer() {
    console.log('GitHubAnalyzer: Setting up analyzer');
    console.log('GitHubAnalyzer: Current URL:', window.location.href);
    
    // Detect if we're on a GitHub repository page
    if (this.isRepositoryPage()) {
      console.log('GitHubAnalyzer: GitHub repository page detected');
      this.currentRepository = this.extractRepositoryInfo();
      this.injectAnalysisButton();
      this.setupEventListeners();
    } else {
      console.log('GitHubAnalyzer: Not a GitHub repository page');
    }

    // Listen for navigation changes (GitHub uses pushState)
    this.setupNavigationListener();
    
    console.log('GitHubAnalyzer: Setup complete');
  }

  setupNavigationListener() {
    // GitHub uses pushState for navigation, so we need to listen for URL changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.target.tagName === 'TITLE') {
          // Page title changed, likely a navigation
          setTimeout(() => {
            this.handlePageChange();
          }, 500);
        }
      });
    });

    observer.observe(document.head, { childList: true, subtree: true });

    // Also listen for popstate events
    window.addEventListener('popstate', () => {
      setTimeout(() => {
        this.handlePageChange();
      }, 500);
    });
  }

  handlePageChange() {
    // Remove existing UI
    this.removeAnalysisButton();

    // Check if we're still on a repository page
    if (this.isRepositoryPage()) {
      this.currentRepository = this.extractRepositoryInfo();
      this.injectAnalysisButton();
      this.setupEventListeners();
    } else {
      this.currentRepository = null;
    }
  }

  injectAnalysisButton() {
    // Find the repository header actions area
    const actionsContainer = document.querySelector('.repository-content .file-navigation, .repository-content .js-repo-nav');
    
    if (actionsContainer) {
      const analysisButton = this.createAnalysisButton();
      analysisButton.addEventListener('click', () => this.handleAnalysisClick());
      
      // Insert the button in the actions area
      actionsContainer.appendChild(analysisButton);
    }
  }

  createAnalysisButton() {
    const button = document.createElement('button');
    button.id = 'github-analyzer-btn';
    button.className = 'btn btn-sm btn-outline ml-2';
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2L10.12 6.12L14 8L10.12 9.88L8 14L5.88 9.88L2 8L5.88 6.12L8 2Z" fill="currentColor"/>
      </svg>
      Analyze
    `;
    button.style.cssText = `
      display: inline-flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      padding: 4px 8px;
      border: 1px solid #d1d9e0;
      border-radius: 6px;
      background: white;
      cursor: pointer;
      margin-left: 8px;
    `;
    return button;
  }

  removeAnalysisButton() {
    const existingButton = document.getElementById('github-analyzer-btn');
    if (existingButton) {
      existingButton.remove();
    }
  }

  setupEventListeners() {
    // Listen for keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        this.handleAnalysisClick();
      }
    });

    // Listen for messages from the popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log('GitHubAnalyzer: Received message:', message);
      
      if (message.action === 'PING') {
        console.log('GitHubAnalyzer: Responding to PING');
        sendResponse({ status: 'PONG' });
        return true;
      }
      
      if (message.action === 'START_ANALYSIS') {
        console.log('GitHubAnalyzer: Starting analysis');
        this.handleAnalysisClick();
        sendResponse({ success: true });
        return true;
      }
      
      console.log('GitHubAnalyzer: Message not handled');
      return false; // Message not handled
    });
    
    console.log('GitHubAnalyzer: Message listener registered');
  }

  async handleAnalysisClick() {
    if (this.isAnalyzing) {
      return;
    }

    try {
      this.isAnalyzing = true;
      
      // Update button state
      this.updateButtonState('analyzing');

      // Collect repository data
      const repositoryData = await this.collectRepositoryData();

      // Send analysis request to background script
      const response = await chrome.runtime.sendMessage({
        action: 'ANALYZE_REPOSITORY',
        data: repositoryData
      });

      if (response && response.success) {
        this.updateButtonState('completed');
        console.log('Analysis completed successfully');
      } else {
        throw new Error(response?.error || 'Analysis failed');
      }
    } catch (error) {
      console.error('Analysis error:', error);
      this.updateButtonState('error');
    } finally {
      this.isAnalyzing = false;
      
      // Reset button state after 5 seconds
      setTimeout(() => {
        this.updateButtonState('ready');
      }, 5000);
    }
  }

  updateButtonState(state) {
    const button = document.getElementById('github-analyzer-btn');
    if (!button) return;

    switch (state) {
      case 'analyzing':
        button.disabled = true;
        button.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 2L10.12 6.12L14 8L10.12 9.88L8 14L5.88 9.88L2 8L5.88 6.12L8 2Z" fill="currentColor"/>
          </svg>
          Analyzing...
        `;
        break;
      case 'completed':
        button.style.background = '#28a745';
        button.style.color = 'white';
        button.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z" fill="currentColor"/>
          </svg>
          Complete
        `;
        break;
      case 'error':
        button.style.background = '#dc3545';
        button.style.color = 'white';
        button.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" fill="currentColor"/>
            <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z" fill="currentColor"/>
          </svg>
          Error
        `;
        break;
      default:
        button.disabled = false;
        button.style.background = 'white';
        button.style.color = 'inherit';
        button.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 2L10.12 6.12L14 8L10.12 9.88L8 14L5.88 9.88L2 8L5.88 6.12L8 2Z" fill="currentColor"/>
          </svg>
          Analyze
        `;
    }
  }

  async collectRepositoryData() {
    const data = {
      ...this.currentRepository,
      pageContent: {
        readme: this.extractReadmeContent(),
        fileTree: this.extractFileTree(),
        recentCommits: this.extractRecentCommits(),
        branches: this.extractBranches(),
        tags: this.extractTags(),
        contributors: this.extractContributors(),
        issues: this.extractIssuesInfo(),
        pullRequests: this.extractPullRequestsInfo(),
        releases: this.extractReleasesInfo(),
        topics: this.extractTopics(),
        about: this.extractAboutInfo()
      }
    };

    return data;
  }

  extractReadmeContent() {
    const readmeElement = document.querySelector('#readme article, .readme article');
    return readmeElement ? readmeElement.innerText : null;
  }

  extractFileTree() {
    const fileRows = document.querySelectorAll('.js-navigation-item[role=\"row\"]');
    return Array.from(fileRows).map(row => {
      const nameElement = row.querySelector('.js-navigation-open');
      const typeElement = row.querySelector('.octicon-file, .octicon-file-directory');
      
      return {
        name: nameElement?.textContent?.trim(),
        type: typeElement?.classList.contains('octicon-file-directory') ? 'directory' : 'file',
        path: nameElement?.href
      };
    });
  }

  extractRecentCommits() {
    const commitElements = document.querySelectorAll('.commit-message, .commit-title');
    return Array.from(commitElements).slice(0, 5).map(element => ({
      message: element.textContent.trim(),
      link: element.href
    }));
  }

  extractBranches() {
    const branchSelector = document.querySelector('.branch-select-menu summary');
    return branchSelector ? branchSelector.textContent.trim() : null;
  }

  extractTags() {
    const tagsLink = document.querySelector('a[href*=\"/tags\"]');
    return tagsLink ? tagsLink.textContent.trim() : null;
  }

  extractContributors() {
    const contributorsLink = document.querySelector('a[href*=\"/graphs/contributors\"]');
    return contributorsLink ? contributorsLink.textContent.trim() : null;
  }

  extractIssuesInfo() {
    const issuesLink = document.querySelector('a[href*=\"/issues\"]');
    return issuesLink ? issuesLink.textContent.trim() : null;
  }

  extractPullRequestsInfo() {
    const prLink = document.querySelector('a[href*=\"/pulls\"]');
    return prLink ? prLink.textContent.trim() : null;
  }

  extractReleasesInfo() {
    const releasesLink = document.querySelector('a[href*=\"/releases\"]');
    return releasesLink ? releasesLink.textContent.trim() : null;
  }

  extractTopics() {
    const topicElements = document.querySelectorAll('.topic-tag');
    return Array.from(topicElements).map(el => el.textContent.trim());
  }

  extractAboutInfo() {
    const aboutSection = document.querySelector('.repository-content .BorderGrid-cell p');
    return aboutSection ? aboutSection.textContent.trim() : null;
  }

  isRepositoryPage() {
    const url = window.location.href;
    return url.includes('github.com') && url.match(/github\.com\/[^\/]+\/[^\/]+/);
  }

  extractRepositoryInfo() {
    const url = window.location.href;
    const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    
    if (match) {
      return {
        owner: match[1],
        name: match[2],
        url: url,
        fullName: `${match[1]}/${match[2]}`
      };
    }
    
    return null;
  }
}

// The analyzer will be initialized at the end of the script

// Handle page visibility changes to pause/resume analysis
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    // Page is hidden, pause any ongoing analysis
    console.log('Page hidden, pausing analysis');
  } else {
    // Page is visible, resume analysis
    console.log('Page visible, resuming analysis');
  }
});

// Initialize the analyzer immediately
console.log('GitHubAnalyzer: Script loaded, creating analyzer instance');
const analyzer = new GitHubAnalyzer();

export { GitHubAnalyzer };