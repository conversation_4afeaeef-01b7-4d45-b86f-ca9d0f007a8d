<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub Repository Analyzer</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="popup-container">
        <!-- Header -->
        <div class="popup-header">
            <div class="popup-logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.58 20 4 16.42 4 12C4 7.58 7.58 4 12 4C16.42 4 20 7.58 20 12C20 16.42 16.42 20 12 20Z" fill="currentColor"/>
                    <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12C16 9.79 14.21 8 12 8Z" fill="currentColor"/>
                </svg>
                <span>GitHub Analyzer</span>
            </div>
            <div class="popup-version" id="version-display">v1.0.1</div>
        </div>

        <!-- Navigation Tabs -->
        <div class="popup-nav">
            <button class="nav-tab active" data-tab="overview">Overview</button>
            <button class="nav-tab" data-tab="settings">Settings</button>
            <button class="nav-tab" data-tab="history">History</button>
        </div>

        <!-- Tab Content -->
        <div class="popup-content">
            <!-- Overview Tab -->
            <div id="overview-tab" class="tab-content active">
                <div class="current-page">
                    <h3>Current Page</h3>
                    <div id="current-page-info" class="page-info">
                        <div class="page-status">Detecting...</div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Quick Actions</h3>
                    <button id="analyze-btn" class="action-btn primary" disabled>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 2L10.12 6.12L14 8L10.12 9.88L8 14L5.88 9.88L2 8L5.88 6.12L8 2Z" fill="currentColor"/>
                        </svg>
                        Analyze Repository
                    </button>
                    <button id="settings-btn" class="action-btn secondary">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 10.5C9.38 10.5 10.5 9.38 10.5 8C10.5 6.62 9.38 5.5 8 5.5C6.62 5.5 5.5 6.62 5.5 8C5.5 9.38 6.62 10.5 8 10.5Z" fill="currentColor"/>
                            <path d="M6.18 1.61C6.37 1.23 6.77 1 7.2 1H8.8C9.23 1 9.63 1.23 9.82 1.61L10.32 2.5C10.51 2.88 10.9 3.09 11.32 3.01L12.29 2.82C12.71 2.74 13.14 2.95 13.36 3.34L14.16 4.66C14.38 5.05 14.32 5.54 14.03 5.86L13.35 6.61C13.06 6.93 13.06 7.07 13.35 7.39L14.03 8.14C14.32 8.46 14.38 8.95 14.16 9.34L13.36 10.66C13.14 11.05 12.71 11.26 12.29 11.18L11.32 10.99C10.9 10.91 10.51 11.12 10.32 11.5L9.82 12.39C9.63 12.77 9.23 13 8.8 13H7.2C6.77 13 6.37 12.77 6.18 12.39L5.68 11.5C5.49 11.12 5.1 10.91 4.68 10.99L3.71 11.18C3.29 11.26 2.86 11.05 2.64 10.66L1.84 9.34C1.62 8.95 1.68 8.46 1.97 8.14L2.65 7.39C2.94 7.07 2.94 6.93 2.65 6.61L1.97 5.86C1.68 5.54 1.62 5.05 1.84 4.66L2.64 3.34C2.86 2.95 3.29 2.74 3.71 2.82L4.68 3.01C5.1 3.09 5.49 2.88 5.68 2.5L6.18 1.61Z" fill="currentColor"/>
                        </svg>
                        Settings
                    </button>
                </div>

                <div class="recent-analysis">
                    <h3>Recent Analysis</h3>
                    <div id="recent-list" class="analysis-list">
                        <div class="empty-state">
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 4C9.37 4 4 9.37 4 16C4 22.63 9.37 28 16 28C22.63 28 28 22.63 28 16C28 9.37 22.63 4 16 4Z" fill="currentColor" opacity="0.3"/>
                                <path d="M16 12C13.79 12 12 13.79 12 16C12 18.21 13.79 20 16 20C18.21 20 20 18.21 20 16C20 13.79 18.21 12 16 12Z" fill="currentColor"/>
                            </svg>
                            <p>No recent analysis</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="settings-section">
                    <h3>API Configuration</h3>
                    
                    <div class="setting-group">
                        <label for="perplexity-key">Perplexity API Key</label>
                        <div class="api-key-input">
                            <input type="password" id="perplexity-key" placeholder="Enter your Perplexity API key">
                            <button class="toggle-visibility" data-target="perplexity-key">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 3C11.87 3 15 8 15 8C15 8 11.87 13 8 13C4.13 13 1 8 1 8C1 8 4.13 3 8 3Z" fill="currentColor"/>
                                    <circle cx="8" cy="8" r="2" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                        <small class="setting-hint">Required for market research and competitive analysis</small>
                    </div>

                    <div class="setting-group">
                        <label for="github-token">GitHub Personal Access Token</label>
                        <div class="api-key-input">
                            <input type="password" id="github-token" placeholder="Enter your GitHub token">
                            <button class="toggle-visibility" data-target="github-token">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 3C11.87 3 15 8 15 8C15 8 11.87 13 8 13C4.13 13 1 8 1 8C1 8 4.13 3 8 3Z" fill="currentColor"/>
                                    <circle cx="8" cy="8" r="2" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                        <small class="setting-hint">Optional: Increases API rate limits and access to private repos</small>
                    </div>

                    <div class="setting-group">
                        <label for="openai-key">OpenAI API Key</label>
                        <div class="api-key-input">
                            <input type="password" id="openai-key" placeholder="Enter your OpenAI API key">
                            <button class="toggle-visibility" data-target="openai-key">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 3C11.87 3 15 8 15 8C15 8 11.87 13 8 13C4.13 13 1 8 1 8C1 8 4.13 3 8 3Z" fill="currentColor"/>
                                    <circle cx="8" cy="8" r="2" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                        <small class="setting-hint">Required for AI-powered analysis and documentation generation</small>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Analysis Preferences</h3>
                    
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-analysis" checked>
                            <span class="checkmark"></span>
                            Auto-analyze when visiting repositories
                        </label>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="show-notifications" checked>
                            <span class="checkmark"></span>
                            Show completion notifications
                        </label>
                    </div>

                    <div class="setting-group">
                        <label for="analysis-depth">Analysis Depth</label>
                        <select id="analysis-depth">
                            <option value="quick">Quick (Essential info only)</option>
                            <option value="standard" selected>Standard (Comprehensive)</option>
                            <option value="deep">Deep (Detailed analysis)</option>
                        </select>
                        <small class="setting-hint">Deeper analysis takes longer but provides more insights</small>
                    </div>
                </div>

                <div class="settings-actions">
                    <button id="save-settings" class="action-btn primary">Save Settings</button>
                    <button id="test-connection" class="action-btn secondary">Test Connection</button>
                    <button id="reset-settings" class="action-btn danger">Reset to Defaults</button>
                </div>
            </div>

            <!-- History Tab -->
            <div id="history-tab" class="tab-content">
                <div class="history-header">
                    <h3>Analysis History</h3>
                    <button id="clear-history" class="action-btn danger small">Clear All</button>
                </div>
                
                <div class="history-filters">
                    <input type="search" id="history-search" placeholder="Search repositories...">
                    <select id="history-sort">
                        <option value="recent">Most Recent</option>
                        <option value="alphabetical">Alphabetical</option>
                        <option value="rating">Best Rated</option>
                    </select>
                </div>

                <div id="history-list" class="history-list">
                    <div class="empty-state">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 4C9.37 4 4 9.37 4 16C4 22.63 9.37 28 16 28C22.63 28 28 22.63 28 16C28 9.37 22.63 4 16 4Z" fill="currentColor" opacity="0.3"/>
                            <path d="M16 12C13.79 12 12 13.79 12 16C12 18.21 13.79 20 16 20C18.21 20 20 18.21 20 16C20 13.79 18.21 12 16 12Z" fill="currentColor"/>
                        </svg>
                        <p>No analysis history</p>
                        <small>Start analyzing repositories to see your history here</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="popup-footer">
            <div class="status-indicator">
                <div class="status-dot" id="status-dot"></div>
                <span id="status-text">Ready</span>
            </div>
            <a href="#" id="help-link" class="help-link">Help</a>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>