/**
 * GitHub Analyzer Popup Script
 * Handles the popup interface functionality
 */

class PopupManager {
  constructor() {
    this.currentTab = 'overview';
    this.currentPageInfo = null;
    this.settings = {};
    this.history = [];
    
    this.init();
  }

  async init() {
    await this.loadSettings();
    await this.loadHistory();
    await this.checkCurrentPage();
    
    this.setupEventListeners();
    this.setupTabNavigation();
    this.setVersionDisplay();
    this.updateUI();
  }

  setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.nav-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // Quick actions
    document.getElementById('analyze-btn')?.addEventListener('click', () => {
      this.startAnalysis();
    });

    document.getElementById('settings-btn')?.addEventListener('click', () => {
      this.switchTab('settings');
    });

    // Settings
    document.getElementById('save-settings')?.addEventListener('click', () => {
      this.saveSettings();
    });

    document.getElementById('test-connection')?.addEventListener('click', () => {
      this.testConnection();
    });

    document.getElementById('reset-settings')?.addEventListener('click', () => {
      this.resetSettings();
    });

    // API key visibility toggles
    document.querySelectorAll('.toggle-visibility').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.togglePasswordVisibility(e.target.dataset.target);
      });
    });

    // History
    document.getElementById('clear-history')?.addEventListener('click', () => {
      this.clearHistory();
    });

    document.getElementById('history-search')?.addEventListener('input', (e) => {
      this.filterHistory(e.target.value);
    });

    document.getElementById('history-sort')?.addEventListener('change', (e) => {
      this.sortHistory(e.target.value);
    });

    // Help link
    document.getElementById('help-link')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.openHelp();
    });
  }

  setupTabNavigation() {
    // Handle keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        // Allow normal tab navigation
        return;
      }
      
      if (e.altKey) {
        const tabs = ['overview', 'settings', 'history'];
        const currentIndex = tabs.indexOf(this.currentTab);
        
        if (e.key === 'ArrowLeft' && currentIndex > 0) {
          e.preventDefault();
          this.switchTab(tabs[currentIndex - 1]);
        } else if (e.key === 'ArrowRight' && currentIndex < tabs.length - 1) {
          e.preventDefault();
          this.switchTab(tabs[currentIndex + 1]);
        }
      }
    });
  }

  switchTab(tabName) {
    // Update active tab
    document.querySelectorAll('.nav-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update active content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');

    this.currentTab = tabName;

    // Refresh data for specific tabs
    if (tabName === 'history') {
      this.refreshHistory();
    } else if (tabName === 'settings') {
      this.refreshSettings();
    }
  }

  async checkCurrentPage() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (tab && tab.url) {
        this.currentPageInfo = {
          url: tab.url,
          title: tab.title,
          isGitHub: tab.url.includes('github.com'),
          isRepository: this.isRepositoryPage(tab.url)
        };

        if (this.currentPageInfo.isRepository) {
          const match = tab.url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
          if (match) {
            this.currentPageInfo.owner = match[1];
            this.currentPageInfo.repo = match[2];
          }
        }
      }
    } catch (error) {
      console.error('Error checking current page:', error);
      this.currentPageInfo = null;
    }
  }

  isRepositoryPage(url) {
    return url && url.includes('github.com') && url.match(/github\.com\/[^\/]+\/[^\/]+/);
  }

  updateUI() {
    this.updateCurrentPageInfo();
    this.updateAnalyzeButton();
    this.updateStatusIndicator();
  }

  updateCurrentPageInfo() {
    const pageInfoElement = document.getElementById('current-page-info');
    if (!pageInfoElement) return;

    if (this.currentPageInfo && this.currentPageInfo.isRepository) {
      pageInfoElement.innerHTML = `
        <div class="page-status">GitHub Repository</div>
        <div class="page-repository">${this.currentPageInfo.owner}/${this.currentPageInfo.repo}</div>
        <div class="page-url">${this.currentPageInfo.url}</div>
      `;
    } else if (this.currentPageInfo && this.currentPageInfo.isGitHub) {
      pageInfoElement.innerHTML = `
        <div class="page-status">GitHub Page (Not a repository)</div>
        <div class="page-url">${this.currentPageInfo.url}</div>
      `;
    } else {
      pageInfoElement.innerHTML = `
        <div class="page-status">Not a GitHub page</div>
        <div class="page-url">${this.currentPageInfo?.url || 'Unknown'}</div>
      `;
    }
  }

  updateAnalyzeButton() {
    const analyzeBtn = document.getElementById('analyze-btn');
    if (!analyzeBtn) return;

    const isValid = this.currentPageInfo?.isRepository && this.hasValidApiKeys();
    analyzeBtn.disabled = !isValid;
    
    if (!this.currentPageInfo?.isRepository) {
      analyzeBtn.textContent = 'Not a Repository';
    } else if (!this.hasValidApiKeys()) {
      analyzeBtn.textContent = 'Configure API Keys';
    } else {
      analyzeBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 2L10.12 6.12L14 8L10.12 9.88L8 14L5.88 9.88L2 8L5.88 6.12L8 2Z" fill="currentColor"/>
        </svg>
        Analyze Repository
      `;
    }
  }

  updateStatusIndicator() {
    const statusDot = document.getElementById('status-dot');
    const statusText = document.getElementById('status-text');
    
    if (!statusDot || !statusText) return;

    if (this.hasValidApiKeys()) {
      statusDot.className = 'status-dot';
      statusText.textContent = 'Ready';
    } else {
      statusDot.className = 'status-dot warning';
      statusText.textContent = 'Setup Required';
    }
  }

  hasValidApiKeys() {
    return this.settings.perplexityKey && this.settings.openaiKey;
  }

  async startAnalysis() {
    if (!this.currentPageInfo?.isRepository) {
      this.showNotification('Please navigate to a GitHub repository', 'error');
      return;
    }

    if (!this.hasValidApiKeys()) {
      this.showNotification('Please configure API keys first', 'error');
      this.switchTab('settings');
      return;
    }

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab || !tab.id) {
        this.showNotification('No active tab found', 'error');
        return;
      }

      // Double-check we're on a GitHub page
      if (!this.isGitHubPage(tab.url)) {
        this.showNotification('Please navigate to a GitHub repository page', 'error');
        return;
      }

      // Ping content script to verify it's ready
      this.showNotification('Connecting to page...', 'info');
      
      const pingResponse = await this.sendMessageWithRetry(tab.id, { action: 'PING' });
      
      if (pingResponse && pingResponse.status === 'PONG') {
        // Content script is ready, send the analysis request
        await this.sendMessageWithRetry(tab.id, { action: 'START_ANALYSIS' });
        this.showNotification('Analysis started', 'success');
        window.close();
      } else {
        this.showNotification('Could not connect to page. Please refresh and try again.', 'error');
      }
    } catch (error) {
      console.error('Error starting analysis:', error);
      if (error.message.includes('Content script unreachable')) {
        this.showNotification('Page not ready. Please refresh and try again.', 'error');
      } else {
        this.showNotification('Failed to start analysis', 'error');
      }
    }
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get([
        'perplexityKey',
        'githubToken',
        'openaiKey',
        'autoAnalysis',
        'showNotifications',
        'analysisDepth'
      ]);

      this.settings = {
        perplexityKey: result.perplexityKey || '',
        githubToken: result.githubToken || '',
        openaiKey: result.openaiKey || '',
        autoAnalysis: result.autoAnalysis !== false,
        showNotifications: result.showNotifications !== false,
        analysisDepth: result.analysisDepth || 'standard'
      };
    } catch (error) {
      console.error('Error loading settings:', error);
      this.settings = {};
    }
  }

  async saveSettings() {
    try {
      const settings = {
        perplexityKey: document.getElementById('perplexity-key')?.value || '',
        githubToken: document.getElementById('github-token')?.value || '',
        openaiKey: document.getElementById('openai-key')?.value || '',
        autoAnalysis: document.getElementById('auto-analysis')?.checked,
        showNotifications: document.getElementById('show-notifications')?.checked,
        analysisDepth: document.getElementById('analysis-depth')?.value || 'standard'
      };

      await chrome.storage.sync.set(settings);
      this.settings = settings;
      
      this.showNotification('Settings saved successfully', 'success');
      this.updateUI();
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showNotification('Failed to save settings', 'error');
    }
  }

  async testConnection() {
    const testBtn = document.getElementById('test-connection');
    if (!testBtn) return;

    testBtn.disabled = true;
    testBtn.textContent = 'Testing...';

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'CHECK_AUTH'
      });

      if (response.success) {
        this.showNotification('API connections verified', 'success');
      } else {
        this.showNotification('API connection failed', 'error');
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      this.showNotification('Connection test failed', 'error');
    } finally {
      testBtn.disabled = false;
      testBtn.textContent = 'Test Connection';
    }
  }

  async resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
      try {
        await chrome.storage.sync.clear();
        await this.loadSettings();
        this.refreshSettings();
        this.showNotification('Settings reset to defaults', 'success');
        this.updateUI();
      } catch (error) {
        console.error('Error resetting settings:', error);
        this.showNotification('Failed to reset settings', 'error');
      }
    }
  }

  refreshSettings() {
    // Populate settings form with current values
    document.getElementById('perplexity-key').value = this.settings.perplexityKey || '';
    document.getElementById('github-token').value = this.settings.githubToken || '';
    document.getElementById('openai-key').value = this.settings.openaiKey || '';
    document.getElementById('auto-analysis').checked = this.settings.autoAnalysis !== false;
    document.getElementById('show-notifications').checked = this.settings.showNotifications !== false;
    document.getElementById('analysis-depth').value = this.settings.analysisDepth || 'standard';
  }

  togglePasswordVisibility(targetId) {
    const input = document.getElementById(targetId);
    if (!input) return;

    if (input.type === 'password') {
      input.type = 'text';
    } else {
      input.type = 'password';
    }
  }

  async loadHistory() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'GET_ANALYSIS_HISTORY'
      });

      if (response.success) {
        this.history = response.data || [];
      } else {
        this.history = [];
      }
    } catch (error) {
      console.error('Error loading history:', error);
      this.history = [];
    }
  }

  async clearHistory() {
    if (confirm('Are you sure you want to clear all analysis history?')) {
      try {
        await chrome.runtime.sendMessage({
          action: 'CLEAR_HISTORY'
        });

        this.history = [];
        this.refreshHistory();
        this.showNotification('History cleared', 'success');
      } catch (error) {
        console.error('Error clearing history:', error);
        this.showNotification('Failed to clear history', 'error');
      }
    }
  }

  refreshHistory() {
    const historyList = document.getElementById('history-list');
    if (!historyList) return;

    if (this.history.length === 0) {
      historyList.innerHTML = `
        <div class="empty-state">
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 4C9.37 4 4 9.37 4 16C4 22.63 9.37 28 16 28C22.63 28 28 22.63 28 16C28 9.37 22.63 4 16 4Z" fill="currentColor" opacity="0.3"/>
            <path d="M16 12C13.79 12 12 13.79 12 16C12 18.21 13.79 20 16 20C18.21 20 20 18.21 20 16C20 13.79 18.21 12 16 12Z" fill="currentColor"/>
          </svg>
          <p>No analysis history</p>
          <small>Start analyzing repositories to see your history here</small>
        </div>
      `;
      return;
    }

    historyList.innerHTML = this.history.map(item => `
      <div class="history-item" data-repo="${item.owner}/${item.repo}">
        <div class="history-item-info">
          <div class="history-item-repo">${item.owner}/${item.repo}</div>
          <div class="history-item-date">${new Date(item.timestamp).toLocaleString()}</div>
        </div>
        <div class="history-item-actions">
          <button class="history-item-action" onclick="popupManager.viewAnalysis('${item.owner}/${item.repo}')">View</button>
          <button class="history-item-action" onclick="popupManager.deleteHistoryItem('${item.owner}/${item.repo}')">Delete</button>
        </div>
      </div>
    `).join('');
  }

  filterHistory(query) {
    const items = document.querySelectorAll('.history-item');
    items.forEach(item => {
      const repo = item.dataset.repo;
      const matches = repo.toLowerCase().includes(query.toLowerCase());
      item.style.display = matches ? 'flex' : 'none';
    });
  }

  sortHistory(criteria) {
    // Implementation for sorting history
    let sortedHistory = [...this.history];
    
    switch (criteria) {
      case 'alphabetical':
        sortedHistory.sort((a, b) => `${a.owner}/${a.repo}`.localeCompare(`${b.owner}/${b.repo}`));
        break;
      case 'recent':
        sortedHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        break;
      case 'rating':
        // Implement rating sort if available
        break;
    }
    
    this.history = sortedHistory;
    this.refreshHistory();
  }

  viewAnalysis(repoName) {
    // Open analysis in a new tab or modal
    console.log('View analysis for:', repoName);
    // Implementation for viewing analysis
  }

  deleteHistoryItem(repoName) {
    if (confirm(`Delete analysis history for ${repoName}?`)) {
      this.history = this.history.filter(item => `${item.owner}/${item.repo}` !== repoName);
      this.refreshHistory();
      this.showNotification(`Deleted history for ${repoName}`, 'success');
    }
  }

  openHelp() {
    chrome.tabs.create({
      url: 'https://github.com/your-username/github-analyzer-extension/wiki'
    });
  }

  showNotification(message, type = 'info') {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      border-radius: 6px;
      color: white;
      font-size: 13px;
      z-index: 10000;
      background: ${type === 'success' ? '#2da44e' : type === 'error' ? '#b91c1c' : '#0969da'};
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      animation: slideIn 0.3s ease-out;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  setVersionDisplay() {
    const manifest = chrome.runtime.getManifest();
    const versionElement = document.getElementById('version-display');
    if (versionElement) {
      versionElement.textContent = `v${manifest.version}`;
    }
  }

  isGitHubPage(url) {
    return url && url.includes('github.com') && !url.startsWith('chrome://') && !url.startsWith('about:');
  }

  async ensureContentScriptInjected(tabId) {
    try {
      // Try programmatic injection as fallback
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content/github-analyzer.js']
      });
      console.log('Content script injected programmatically');
      // Give it a moment to initialize
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.log('Programmatic injection failed (might already be injected):', error.message);
      // This is okay - the script might already be injected via manifest
    }
  }

  async sendMessageWithRetry(tabId, message, maxRetries = 5, initialDelayMs = 200) {
    let delayMs = initialDelayMs;
    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await chrome.tabs.sendMessage(tabId, message);
        return response;
      } catch (error) {
        if (error.message.includes('Could not establish connection. Receiving end does not exist.')) {
          console.warn(`Attempt ${i + 1}/${maxRetries}: Content script not ready. Retrying in ${delayMs}ms...`);
          
          // On first retry, try to inject the content script programmatically
          if (i === 0) {
            console.log('Attempting programmatic content script injection...');
            await this.ensureContentScriptInjected(tabId);
          }
          
          await new Promise(resolve => setTimeout(resolve, delayMs));
          delayMs *= 2; // Exponential backoff
        } else {
          console.error('Unexpected error sending message:', error);
          throw error;
        }
      }
    }
    throw new Error('Content script unreachable after retries');
  }
}

// Initialize popup manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.popupManager = new PopupManager();
});

// Handle messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'ANALYSIS_COMPLETE') {
    window.popupManager?.showNotification('Analysis complete!', 'success');
    window.popupManager?.loadHistory();
  } else if (request.action === 'ANALYSIS_ERROR') {
    window.popupManager?.showNotification('Analysis failed', 'error');
  }
});

export { PopupManager };