/**
 * GitHub Analyzer Popup Styles
 * Styles for the extension popup interface
 */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #24292f;
  background: #ffffff;
  min-width: 380px;
  max-width: 400px;
  min-height: 500px;
  max-height: 600px;
  overflow: hidden;
}

/* Popup Container */
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* Header */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f6f8fa;
  border-bottom: 1px solid #e1e4e8;
}

.popup-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #0969da;
}

.popup-logo svg {
  color: #0969da;
}

.popup-version {
  font-size: 12px;
  color: #656d76;
  font-weight: 400;
}

/* Navigation */
.popup-nav {
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #e1e4e8;
}

.nav-tab {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #656d76;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.nav-tab:hover {
  color: #24292f;
  background: #f6f8fa;
}

.nav-tab.active {
  color: #0969da;
  border-bottom-color: #0969da;
}

/* Content Area */
.popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Section Titles */
h3 {
  font-size: 14px;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 12px;
}

/* Overview Tab */
.current-page {
  margin-bottom: 24px;
}

.page-info {
  padding: 12px;
  background: #f6f8fa;
  border-radius: 8px;
  border: 1px solid #e1e4e8;
}

.page-status {
  font-size: 13px;
  color: #656d76;
}

.page-repository {
  font-size: 13px;
  color: #24292f;
  font-weight: 500;
  margin-top: 4px;
}

.page-url {
  font-size: 11px;
  color: #656d76;
  margin-top: 2px;
  word-break: break-all;
}

.quick-actions {
  margin-bottom: 24px;
}

.recent-analysis {
  margin-bottom: 16px;
}

.analysis-list {
  max-height: 200px;
  overflow-y: auto;
}

.analysis-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f6f8fa;
  border-radius: 8px;
  border: 1px solid #e1e4e8;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.analysis-item:hover {
  background: #eaeef2;
}

.analysis-item-info {
  flex: 1;
}

.analysis-item-repo {
  font-size: 13px;
  font-weight: 500;
  color: #24292f;
}

.analysis-item-date {
  font-size: 11px;
  color: #656d76;
  margin-top: 2px;
}

.analysis-item-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.analysis-item-status.completed {
  background: #dafbe1;
  color: #1f883d;
}

.analysis-item-status.failed {
  background: #ffeef0;
  color: #cf222e;
}

/* Settings Tab */
.settings-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e1e4e8;
}

.settings-section:last-child {
  border-bottom: none;
}

.setting-group {
  margin-bottom: 16px;
}

.setting-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #24292f;
  margin-bottom: 6px;
}

.setting-group input[type="text"],
.setting-group input[type="password"],
.setting-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  font-size: 13px;
  transition: border-color 0.2s ease;
}

.setting-group input[type="text"]:focus,
.setting-group input[type="password"]:focus,
.setting-group select:focus {
  outline: none;
  border-color: #0969da;
  box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.1);
}

.api-key-input {
  position: relative;
}

.toggle-visibility {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #656d76;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.toggle-visibility:hover {
  color: #24292f;
}

.setting-hint {
  font-size: 11px;
  color: #656d76;
  margin-top: 4px;
  display: block;
}

/* Checkbox Styles */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  height: 16px;
  width: 16px;
  background-color: #ffffff;
  border: 1px solid #e1e4e8;
  border-radius: 3px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-label:hover input ~ .checkmark {
  border-color: #0969da;
}

.checkbox-label input:checked ~ .checkmark {
  background-color: #0969da;
  border-color: #0969da;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-label input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-label .checkmark:after {
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Action Buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  margin-right: 8px;
  margin-bottom: 8px;
}

.action-btn:last-child {
  margin-right: 0;
}

.action-btn.primary {
  background: #0969da;
  color: white;
}

.action-btn.primary:hover:not(:disabled) {
  background: #0860ca;
}

.action-btn.secondary {
  background: #f6f8fa;
  color: #24292f;
  border: 1px solid #e1e4e8;
}

.action-btn.secondary:hover {
  background: #f3f4f6;
}

.action-btn.danger {
  background: #da3633;
  color: white;
}

.action-btn.danger:hover {
  background: #c93c39;
}

.action-btn.small {
  padding: 4px 8px;
  font-size: 12px;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.settings-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
}

/* History Tab */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.history-filters {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.history-filters input {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  font-size: 13px;
}

.history-filters select {
  padding: 6px 12px;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  font-size: 13px;
  min-width: 120px;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f6f8fa;
  border-radius: 8px;
  border: 1px solid #e1e4e8;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  background: #eaeef2;
}

.history-item-info {
  flex: 1;
}

.history-item-repo {
  font-size: 13px;
  font-weight: 500;
  color: #24292f;
}

.history-item-date {
  font-size: 11px;
  color: #656d76;
  margin-top: 2px;
}

.history-item-actions {
  display: flex;
  gap: 4px;
}

.history-item-action {
  padding: 4px 8px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #656d76;
  transition: all 0.2s ease;
}

.history-item-action:hover {
  background: #ffffff;
  color: #24292f;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #656d76;
}

.empty-state svg {
  opacity: 0.5;
  margin-bottom: 12px;
}

.empty-state p {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.empty-state small {
  font-size: 12px;
  color: #8c959f;
}

/* Footer */
.popup-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f6f8fa;
  border-top: 1px solid #e1e4e8;
  font-size: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #2da44e;
  transition: background-color 0.2s ease;
}

.status-dot.warning {
  background: #fb8500;
}

.status-dot.error {
  background: #da3633;
}

.help-link {
  color: #0969da;
  text-decoration: none;
  font-weight: 500;
}

.help-link:hover {
  text-decoration: underline;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f6f8fa;
}

::-webkit-scrollbar-thumb {
  background: #c1c8ce;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8b3ba;
}

/* Responsive Adjustments */
@media (max-width: 380px) {
  body {
    min-width: 300px;
  }
  
  .popup-content {
    padding: 16px;
  }
  
  .settings-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
    margin-right: 0;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  body {
    background: #0d1117;
    color: #f0f6fc;
  }
  
  .popup-header {
    background: #161b22;
    border-bottom-color: #30363d;
  }
  
  .popup-nav {
    background: #0d1117;
    border-bottom-color: #30363d;
  }
  
  .nav-tab {
    color: #8b949e;
  }
  
  .nav-tab:hover {
    color: #f0f6fc;
    background: #21262d;
  }
  
  .nav-tab.active {
    color: #58a6ff;
    border-bottom-color: #58a6ff;
  }
  
  .page-info,
  .analysis-item,
  .history-item {
    background: #161b22;
    border-color: #30363d;
  }
  
  .analysis-item:hover,
  .history-item:hover {
    background: #21262d;
  }
  
  .settings-section {
    border-bottom-color: #30363d;
  }
  
  input[type="text"],
  input[type="password"],
  select {
    background: #0d1117;
    color: #f0f6fc;
    border-color: #30363d;
  }
  
  input[type="text"]:focus,
  input[type="password"]:focus,
  select:focus {
    border-color: #58a6ff;
    box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.1);
  }
  
  .checkmark {
    background-color: #0d1117;
    border-color: #30363d;
  }
  
  .checkbox-label input:checked ~ .checkmark {
    background-color: #58a6ff;
    border-color: #58a6ff;
  }
  
  .action-btn.primary {
    background: #238636;
  }
  
  .action-btn.primary:hover:not(:disabled) {
    background: #2ea043;
  }
  
  .action-btn.secondary {
    background: #21262d;
    color: #f0f6fc;
    border-color: #30363d;
  }
  
  .action-btn.secondary:hover {
    background: #30363d;
  }
  
  .popup-footer {
    background: #161b22;
    border-top-color: #30363d;
  }
  
  .help-link {
    color: #58a6ff;
  }
  
  ::-webkit-scrollbar-track {
    background: #161b22;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #484f58;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #6e7681;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading {
  opacity: 0.7;
  pointer-events: none;
}

.success {
  background: #dafbe1 !important;
  border-color: #a2eeaa !important;
}

.error {
  background: #ffeef0 !important;
  border-color: #ffc1c8 !important;
}