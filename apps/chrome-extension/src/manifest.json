{"manifest_version": 3, "name": "GitHub Repository Analyzer", "version": "1.0.4", "description": "Analyze GitHub repositories and generate comprehensive documentation using AI agents", "permissions": ["activeTab", "storage", "scripting", "contextMenus"], "host_permissions": ["https://github.com/*", "https://api.github.com/*", "https://api.perplexity.ai/*"], "background": {"service_worker": "background/service-worker.js"}, "content_scripts": [{"matches": ["https://github.com/*"], "js": ["content/github-analyzer.js"], "css": ["content/github-analyzer.css"], "run_at": "document_idle"}], "action": {"default_popup": "popup/popup.html", "default_title": "GitHub Repository Analyzer", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "options_ui": {"page": "options/options.html", "open_in_tab": true}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["content/*.js", "content/*.css", "shared/*.js"], "matches": ["https://github.com/*"]}], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';"}}