<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub Analyzer Options</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>GitHub Repository Analyzer</h1>
            <p>Configure your extension settings</p>
        </header>
        
        <main>
            <section class="settings-section">
                <h2>API Configuration</h2>
                <div class="setting-group">
                    <label for="github-token">GitHub Personal Access Token:</label>
                    <input type="password" id="github-token" placeholder="Enter your GitHub token">
                    <small>Required for analyzing private repositories and higher rate limits</small>
                </div>
                
                <div class="setting-group">
                    <label for="perplexity-token">Perplexity API Key:</label>
                    <input type="password" id="perplexity-token" placeholder="Enter your Perplexity API key">
                    <small>Required for enhanced analysis capabilities</small>
                </div>
            </section>
            
            <section class="settings-section">
                <h2>Analysis Settings</h2>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="auto-analyze" checked>
                        Enable automatic analysis on repository pages
                    </label>
                </div>
                
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="show-notifications" checked>
                        Show desktop notifications for analysis completion
                    </label>
                </div>
            </section>
            
            <section class="settings-section">
                <h2>Data & Privacy</h2>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="store-history" checked>
                        Store analysis history locally
                    </label>
                </div>
                
                <div class="setting-group">
                    <button id="clear-history" class="secondary-button">Clear Analysis History</button>
                </div>
            </section>
        </main>
        
        <footer>
            <button id="save-settings" class="primary-button">Save Settings</button>
            <button id="reset-settings" class="secondary-button">Reset to Defaults</button>
        </footer>
    </div>
    
    <script src="options.js"></script>
</body>
</html>