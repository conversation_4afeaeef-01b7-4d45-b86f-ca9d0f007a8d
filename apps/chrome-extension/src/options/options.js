/**
 * Options page for GitHub Repository Analyzer
 */

document.addEventListener('DOMContentLoaded', function() {
    const saveButton = document.getElementById('save-settings');
    const resetButton = document.getElementById('reset-settings');
    const clearHistoryButton = document.getElementById('clear-history');
    
    // Load saved settings
    loadSettings();
    
    // Event listeners
    saveButton.addEventListener('click', saveSettings);
    resetButton.addEventListener('click', resetSettings);
    clearHistoryButton.addEventListener('click', clearHistory);
});

async function loadSettings() {
    try {
        const result = await chrome.storage.local.get([
            'githubToken',
            'perplexityToken',
            'autoAnalyze',
            'showNotifications',
            'storeHistory'
        ]);
        
        // Populate form fields
        document.getElementById('github-token').value = result.githubToken || '';
        document.getElementById('perplexity-token').value = result.perplexityToken || '';
        document.getElementById('auto-analyze').checked = result.autoAnalyze !== false;
        document.getElementById('show-notifications').checked = result.showNotifications !== false;
        document.getElementById('store-history').checked = result.storeHistory !== false;
        
    } catch (error) {
        console.error('Error loading settings:', error);
        showMessage('Error loading settings', 'error');
    }
}

async function saveSettings() {
    try {
        const settings = {
            githubToken: document.getElementById('github-token').value,
            perplexityToken: document.getElementById('perplexity-token').value,
            autoAnalyze: document.getElementById('auto-analyze').checked,
            showNotifications: document.getElementById('show-notifications').checked,
            storeHistory: document.getElementById('store-history').checked
        };
        
        await chrome.storage.local.set(settings);
        showMessage('Settings saved successfully!', 'success');
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showMessage('Error saving settings', 'error');
    }
}

async function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        try {
            await chrome.storage.local.clear();
            loadSettings();
            showMessage('Settings reset to defaults', 'success');
        } catch (error) {
            console.error('Error resetting settings:', error);
            showMessage('Error resetting settings', 'error');
        }
    }
}

async function clearHistory() {
    if (confirm('Are you sure you want to clear all analysis history?')) {
        try {
            await chrome.storage.local.remove(['analysisHistory']);
            showMessage('Analysis history cleared', 'success');
        } catch (error) {
            console.error('Error clearing history:', error);
            showMessage('Error clearing history', 'error');
        }
    }
}

function showMessage(message, type) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.success-message, .error-message');
    existingMessages.forEach(msg => msg.remove());
    
    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `${type}-message`;
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';
    
    // Insert at the top of main content
    const main = document.querySelector('main');
    main.insertBefore(messageDiv, main.firstChild);
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}