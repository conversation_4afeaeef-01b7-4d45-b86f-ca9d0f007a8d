import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { editor } from 'monaco-editor';
import { 
  EditorConfiguration, 
  FileSystemState, 
  OpenFile, 
  FileNode, 
  Position, 
  Range 
} from '@/types/editor';
import { defaultEditorConfig } from '@/lib/monaco-config';
import { generateId, getFileExtension, getLanguageFromExtension } from '@/lib/utils';

interface EditorState extends FileSystemState {
  // Configuration
  configuration: EditorConfiguration;
  
  // Editor instances
  editors: Map<string, editor.IStandaloneCodeEditor>;
  
  // File operations
  createFile: (name: string, content?: string, path?: string) => Promise<void>;
  openFile: (path: string) => Promise<void>;
  closeFile: (fileId: string) => void;
  saveFile: (fileId: string) => Promise<void>;
  saveAllFiles: () => Promise<void>;
  updateFileContent: (fileId: string, content: string) => void;
  
  // File system operations
  loadDirectory: (path: string) => Promise<void>;
  refreshFileTree: () => Promise<void>;
  createDirectory: (path: string) => Promise<void>;
  deleteFile: (path: string) => Promise<void>;
  renameFile: (oldPath: string, newPath: string) => Promise<void>;
  
  // Navigation
  setActiveFile: (fileId: string | null) => void;
  switchToFile: (fileId: string) => void;
  
  // Configuration
  updateConfiguration: (config: Partial<EditorConfiguration>) => void;
  
  // Editor management
  registerEditor: (fileId: string, editor: editor.IStandaloneCodeEditor) => void;
  unregisterEditor: (fileId: string) => void;
  getEditor: (fileId: string) => editor.IStandaloneCodeEditor | undefined;
  
  // Search
  searchInFiles: (query: string, options?: { caseSensitive?: boolean; regex?: boolean }) => Promise<void>;
  clearSearchResults: () => void;
  
  // Recent files
  addToRecentFiles: (path: string) => void;
  
  // Utility
  getFileById: (fileId: string) => OpenFile | undefined;
  getFileByPath: (path: string) => OpenFile | undefined;
  isDirty: () => boolean;
  getDirtyFiles: () => OpenFile[];
}

export const useEditorStore = create<EditorState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    currentProject: null,
    openFiles: [],
    activeFile: null,
    fileTree: [],
    searchResults: [],
    recentFiles: [],
    configuration: defaultEditorConfig,
    editors: new Map(),

    // File operations
    createFile: async (name: string, content = '', path?: string) => {
      const fileId = generateId();
      const fullPath = path ? `${path}/${name}` : name;
      const extension = getFileExtension(name);
      const language = getLanguageFromExtension(extension);

      const newFile: OpenFile = {
        id: fileId,
        path: fullPath,
        name,
        content,
        originalContent: content,
        isDirty: false,
        isLoading: false,
        isReadonly: false,
        language,
        cursorPosition: { line: 1, column: 1 },
      };

      set((state) => ({
        openFiles: [...state.openFiles, newFile],
        activeFile: fileId,
      }));

      // Update recent files
      get().addToRecentFiles(fullPath);
    },

    openFile: async (path: string) => {
      const existingFile = get().getFileByPath(path);
      if (existingFile) {
        get().setActiveFile(existingFile.id);
        return;
      }

      const fileId = generateId();
      const fileName = path.split('/').pop() || '';
      const extension = getFileExtension(fileName);
      const language = getLanguageFromExtension(extension);

      // Create a loading file
      const newFile: OpenFile = {
        id: fileId,
        path,
        name: fileName,
        content: '',
        originalContent: '',
        isDirty: false,
        isLoading: true,
        isReadonly: false,
        language,
        cursorPosition: { line: 1, column: 1 },
      };

      set((state) => ({
        openFiles: [...state.openFiles, newFile],
        activeFile: fileId,
      }));

      try {
        // Simulate file loading - in real app, this would load from filesystem
        const content = await loadFileContent(path);
        
        set((state) => ({
          openFiles: state.openFiles.map(file =>
            file.id === fileId
              ? { ...file, content, originalContent: content, isLoading: false }
              : file
          ),
        }));

        // Update recent files
        get().addToRecentFiles(path);
      } catch (error) {
        console.error('Failed to load file:', error);
        set((state) => ({
          openFiles: state.openFiles.filter(file => file.id !== fileId),
          activeFile: state.openFiles.length > 1 ? state.openFiles[0].id : null,
        }));
      }
    },

    closeFile: (fileId: string) => {
      const file = get().getFileById(fileId);
      if (file?.isDirty) {
        // In a real app, show confirmation dialog
        if (!confirm(`File "${file.name}" has unsaved changes. Close anyway?`)) {
          return;
        }
      }

      // Unregister editor
      get().unregisterEditor(fileId);

      set((state) => {
        const newOpenFiles = state.openFiles.filter(f => f.id !== fileId);
        const newActiveFile = state.activeFile === fileId
          ? (newOpenFiles.length > 0 ? newOpenFiles[0].id : null)
          : state.activeFile;

        return {
          openFiles: newOpenFiles,
          activeFile: newActiveFile,
        };
      });
    },

    saveFile: async (fileId: string) => {
      const file = get().getFileById(fileId);
      if (!file) return;

      try {
        // Simulate file saving - in real app, this would save to filesystem
        await saveFileContent(file.path, file.content);
        
        set((state) => ({
          openFiles: state.openFiles.map(f =>
            f.id === fileId
              ? { ...f, isDirty: false, originalContent: f.content }
              : f
          ),
        }));
      } catch (error) {
        console.error('Failed to save file:', error);
        // In a real app, show error notification
      }
    },

    saveAllFiles: async () => {
      const dirtyFiles = get().getDirtyFiles();
      await Promise.all(dirtyFiles.map(file => get().saveFile(file.id)));
    },

    updateFileContent: (fileId: string, content: string) => {
      set((state) => ({
        openFiles: state.openFiles.map(file =>
          file.id === fileId
            ? { ...file, content, isDirty: content !== file.originalContent }
            : file
        ),
      }));
    },

    // File system operations
    loadDirectory: async (path: string) => {
      try {
        const fileTree = await loadDirectoryTree(path);
        set({ currentProject: path, fileTree });
      } catch (error) {
        console.error('Failed to load directory:', error);
      }
    },

    refreshFileTree: async () => {
      const currentProject = get().currentProject;
      if (currentProject) {
        await get().loadDirectory(currentProject);
      }
    },

    createDirectory: async (path: string) => {
      try {
        await createDirectory(path);
        await get().refreshFileTree();
      } catch (error) {
        console.error('Failed to create directory:', error);
      }
    },

    deleteFile: async (path: string) => {
      try {
        await deleteFile(path);
        
        // Close file if it's open
        const openFile = get().getFileByPath(path);
        if (openFile) {
          get().closeFile(openFile.id);
        }
        
        await get().refreshFileTree();
      } catch (error) {
        console.error('Failed to delete file:', error);
      }
    },

    renameFile: async (oldPath: string, newPath: string) => {
      try {
        await renameFile(oldPath, newPath);
        
        // Update open file if it's open
        const openFile = get().getFileByPath(oldPath);
        if (openFile) {
          const newName = newPath.split('/').pop() || '';
          const extension = getFileExtension(newName);
          const language = getLanguageFromExtension(extension);
          
          set((state) => ({
            openFiles: state.openFiles.map(file =>
              file.id === openFile.id
                ? { ...file, path: newPath, name: newName, language }
                : file
            ),
          }));
        }
        
        await get().refreshFileTree();
      } catch (error) {
        console.error('Failed to rename file:', error);
      }
    },

    // Navigation
    setActiveFile: (fileId: string | null) => {
      set({ activeFile: fileId });
    },

    switchToFile: (fileId: string) => {
      const file = get().getFileById(fileId);
      if (file) {
        get().setActiveFile(fileId);
      }
    },

    // Configuration
    updateConfiguration: (config: Partial<EditorConfiguration>) => {
      set((state) => ({
        configuration: { ...state.configuration, ...config },
      }));
    },

    // Editor management
    registerEditor: (fileId: string, editorInstance: editor.IStandaloneCodeEditor) => {
      const editors = get().editors;
      editors.set(fileId, editorInstance);
      set({ editors: new Map(editors) });
    },

    unregisterEditor: (fileId: string) => {
      const editors = get().editors;
      const editorInstance = editors.get(fileId);
      if (editorInstance) {
        editorInstance.dispose();
        editors.delete(fileId);
        set({ editors: new Map(editors) });
      }
    },

    getEditor: (fileId: string) => {
      return get().editors.get(fileId);
    },

    // Search
    searchInFiles: async (query: string, options = {}) => {
      try {
        const results = await searchInFiles(query, get().currentProject, options);
        set({ searchResults: results });
      } catch (error) {
        console.error('Search failed:', error);
      }
    },

    clearSearchResults: () => {
      set({ searchResults: [] });
    },

    // Recent files
    addToRecentFiles: (path: string) => {
      set((state) => {
        const recentFiles = state.recentFiles.filter(f => f !== path);
        return {
          recentFiles: [path, ...recentFiles].slice(0, 10), // Keep only 10 recent files
        };
      });
    },

    // Utility
    getFileById: (fileId: string) => {
      return get().openFiles.find(file => file.id === fileId);
    },

    getFileByPath: (path: string) => {
      return get().openFiles.find(file => file.path === path);
    },

    isDirty: () => {
      return get().openFiles.some(file => file.isDirty);
    },

    getDirtyFiles: () => {
      return get().openFiles.filter(file => file.isDirty);
    },
  }))
);

// Mock file system operations - in a real app, these would interact with actual file system
async function loadFileContent(path: string): Promise<string> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // Mock file content based on extension
  const extension = getFileExtension(path);
  const mockContent = getMockFileContent(extension);
  
  return mockContent;
}

async function saveFileContent(path: string, content: string): Promise<void> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // In a real app, this would save to filesystem
  console.log(`Saving file: ${path}`);
}

async function loadDirectoryTree(path: string): Promise<FileNode[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  // Mock directory tree
  return [
    {
      name: 'src',
      path: 'src',
      type: 'directory',
      children: [
        {
          name: 'components',
          path: 'src/components',
          type: 'directory',
          children: [
            { name: 'Button.tsx', path: 'src/components/Button.tsx', type: 'file', size: 1024 },
            { name: 'Input.tsx', path: 'src/components/Input.tsx', type: 'file', size: 512 },
          ],
        },
        {
          name: 'pages',
          path: 'src/pages',
          type: 'directory',
          children: [
            { name: 'Home.tsx', path: 'src/pages/Home.tsx', type: 'file', size: 2048 },
            { name: 'About.tsx', path: 'src/pages/About.tsx', type: 'file', size: 1536 },
          ],
        },
        { name: 'App.tsx', path: 'src/App.tsx', type: 'file', size: 4096 },
        { name: 'index.ts', path: 'src/index.ts', type: 'file', size: 256 },
      ],
    },
    {
      name: 'public',
      path: 'public',
      type: 'directory',
      children: [
        { name: 'index.html', path: 'public/index.html', type: 'file', size: 1024 },
        { name: 'favicon.ico', path: 'public/favicon.ico', type: 'file', size: 16384 },
      ],
    },
    { name: 'package.json', path: 'package.json', type: 'file', size: 2048 },
    { name: 'tsconfig.json', path: 'tsconfig.json', type: 'file', size: 1024 },
    { name: 'README.md', path: 'README.md', type: 'file', size: 512 },
  ];
}

async function createDirectory(path: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 100));
  console.log(`Creating directory: ${path}`);
}

async function deleteFile(path: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 100));
  console.log(`Deleting file: ${path}`);
}

async function renameFile(oldPath: string, newPath: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 100));
  console.log(`Renaming file: ${oldPath} -> ${newPath}`);
}

async function searchInFiles(query: string, projectPath: string | null, options: any): Promise<any[]> {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Mock search results
  return [
    {
      file: 'src/App.tsx',
      line: 10,
      column: 5,
      text: `const App = () => {`,
      match: query,
    },
    {
      file: 'src/components/Button.tsx',
      line: 15,
      column: 12,
      text: `export const Button = ({ ${query} }) => {`,
      match: query,
    },
  ];
}

function getMockFileContent(extension: string): string {
  const mockContent: Record<string, string> = {
    tsx: `import React from 'react';

interface Props {
  title: string;
  onClick?: () => void;
}

export const Component: React.FC<Props> = ({ title, onClick }) => {
  return (
    <div>
      <h1>{title}</h1>
      <button onClick={onClick}>Click me</button>
    </div>
  );
};`,
    
    ts: `export interface User {
  id: string;
  name: string;
  email: string;
}

export class UserService {
  private users: User[] = [];

  async getUser(id: string): Promise<User | null> {
    return this.users.find(user => user.id === id) || null;
  }

  async createUser(user: Omit<User, 'id'>): Promise<User> {
    const newUser: User = {
      id: Math.random().toString(36),
      ...user,
    };
    this.users.push(newUser);
    return newUser;
  }
}`,
    
    js: `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

const memoizedFibonacci = (() => {
  const cache = {};
  return function(n) {
    if (n in cache) return cache[n];
    if (n <= 1) return n;
    cache[n] = memoizedFibonacci(n - 1) + memoizedFibonacci(n - 2);
    return cache[n];
  };
})();

console.log(memoizedFibonacci(10));`,
    
    py: `def fibonacci(n):
    """Calculate fibonacci number using dynamic programming."""
    if n <= 1:
        return n
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    
    return b

class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result

if __name__ == "__main__":
    calc = Calculator()
    print(calc.add(2, 3))
    print(calc.multiply(4, 5))`,
    
    json: `{
  "name": "my-project",
  "version": "1.0.0",
  "description": "A sample project",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.0",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "jest": "^29.0.0",
    "nodemon": "^2.0.20"
  }
}`,
    
    css: `/* Modern CSS with custom properties */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--dark-color);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.375rem;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}`,
    
    html: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample HTML Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .content {
            line-height: 1.6;
        }
        .highlight {
            background-color: yellow;
            padding: 2px 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Welcome to My Website</h1>
        <p>This is a sample HTML page with some basic styling.</p>
    </div>
    
    <div class="content">
        <h2>About</h2>
        <p>This is a <span class="highlight">sample paragraph</span> with some text content.</p>
        
        <h2>Features</h2>
        <ul>
            <li>Responsive design</li>
            <li>Clean HTML structure</li>
            <li>Basic CSS styling</li>
            <li>Semantic markup</li>
        </ul>
        
        <h2>Contact</h2>
        <form>
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" required>
            
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
            
            <label for="message">Message:</label>
            <textarea id="message" name="message" rows="4" required></textarea>
            
            <button type="submit">Send Message</button>
        </form>
    </div>
</body>
</html>`,
    
    md: `# Sample Markdown Document

This is a sample markdown document with various formatting examples.

## Headers

You can create headers using the \`#\` symbol:

### Level 3 Header
#### Level 4 Header
##### Level 5 Header

## Text Formatting

- **Bold text** using \`**text**\`
- *Italic text* using \`*text*\`
- \`Inline code\` using backticks
- ~~Strikethrough text~~ using \`~~text~~\`

## Lists

### Unordered List
- Item 1
- Item 2
  - Nested item 2.1
  - Nested item 2.2
- Item 3

### Ordered List
1. First item
2. Second item
3. Third item

## Code Blocks

\`\`\`javascript
function hello(name) {
  return \`Hello, \${name}!\`;
}

console.log(hello('World'));
\`\`\`

## Links and Images

- [Link to Google](https://www.google.com)
- ![Alt text](https://via.placeholder.com/150)

## Tables

| Name | Age | City |
|------|-----|------|
| John | 25  | NYC  |
| Jane | 30  | LA   |
| Bob  | 35  | SF   |

## Blockquotes

> This is a blockquote. It can be used to highlight important information or quotes from other sources.

## Horizontal Rule

---

That's all for now!`,
  };
  
  return mockContent[extension] || `// Sample ${extension} file\n\n// This is a placeholder file content\n// for files with .${extension} extension`;
}