import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { fabric } from 'fabric';
import { useTypography } from '../../hooks/use-typography';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select } from '../ui/select';
import { Slider } from '../ui/slider';
import { Switch } from '../ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';
import { 
  FontFamily, 
  FontFilter, 
  FontPreview, 
  TextEffect, 
  TextEffectPreset,
  TypographySettings,
  TextRange
} from '../../lib/canvas/typography-engine';

// Icons (assuming you have an icon library)
import { 
  Type, 
  Bold, 
  Italic, 
  Underline, 
  AlignLeft, 
  AlignCenter, 
  AlignRight, 
  AlignJustify,
  Palette,
  Sparkles,
  Search,
  Star,
  Download,
  Upload,
  Settings,
  RefreshCw,
  Trash2,
  Copy,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronUp,
  Filter,
  Grid,
  List,
  Heart,
  History,
  Zap,
  Image,
  Layers,
  MousePointer,
  Move,
  RotateCw,
  Square,
  Circle,
  Triangle,
  Minus,
  Plus,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react';

interface TypographyPanelProps {
  canvas: fabric.Canvas | null;
  className?: string;
  onTextObjectSelect?: (textId: string) => void;
  onFontChange?: (fontFamily: FontFamily) => void;
  onSettingsChange?: (settings: Partial<TypographySettings>) => void;
}

// Font preview component
const FontPreviewCard: React.FC<{
  font: FontFamily;
  preview: FontPreview | null;
  isSelected: boolean;
  isFavorite: boolean;
  onSelect: (font: FontFamily) => void;
  onToggleFavorite: (fontId: string) => void;
  onLoad: (fontId: string) => void;
}> = ({ font, preview, isSelected, isFavorite, onSelect, onToggleFavorite, onLoad }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(font.loadingStatus === 'loaded');

  const handleLoadFont = async () => {
    if (isLoaded) return;
    
    setIsLoading(true);
    try {
      await onLoad(font.id);
      setIsLoaded(true);
    } catch (error) {
      console.error('Failed to load font:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
        isSelected ? 'ring-2 ring-blue-500' : ''
      }`}
      onClick={() => onSelect(font)}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-sm">{font.displayName}</CardTitle>
            {font.customFont && <Badge variant="secondary" className="text-xs">Custom</Badge>}
            {font.trending && <Badge variant="outline" className="text-xs">Trending</Badge>}
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onToggleFavorite(font.id);
              }}
              className="p-1"
            >
              <Heart className={`h-4 w-4 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
            </Button>
            {!isLoaded && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleLoadFont();
                }}
                disabled={isLoading}
                className="p-1"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Download className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <Badge variant="outline" className="text-xs">
            {font.category}
          </Badge>
          <Badge variant="outline" className="text-xs">
            {font.source}
          </Badge>
          {font.variants.length > 1 && (
            <Badge variant="outline" className="text-xs">
              {font.variants.length} variants
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {preview ? (
          <div className="relative bg-gray-50 rounded p-2 min-h-[60px] flex items-center justify-center">
            <img 
              src={preview.dataUrl} 
              alt={`${font.name} preview`}
              className="max-w-full max-h-full object-contain"
              style={{ fontFamily: font.name }}
            />
          </div>
        ) : (
          <div className="bg-gray-50 rounded p-2 min-h-[60px] flex items-center justify-center">
            <div 
              className="text-sm"
              style={{ fontFamily: isLoaded ? font.name : 'sans-serif' }}
            >
              {isLoaded ? 'The quick brown fox jumps over the lazy dog' : 'Loading...'}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Font filter component
const FontFilterPanel: React.FC<{
  filter: FontFilter;
  onFilterChange: (filter: FontFilter) => void;
  categories: Array<{ category: string; count: number }>;
}> = ({ filter, onFilterChange, categories }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleFilterChange = (key: keyof FontFilter, value: any) => {
    onFilterChange({ ...filter, [key]: value });
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Filters</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent className="pt-0 space-y-4">
          {/* Search */}
          <div>
            <Label htmlFor="font-search" className="text-xs">Search</Label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                id="font-search"
                placeholder="Search fonts..."
                value={filter.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Categories */}
          <div>
            <Label className="text-xs">Categories</Label>
            <div className="flex flex-wrap gap-1 mt-1">
              {categories.map(({ category, count }) => (
                <Button
                  key={category}
                  variant={filter.category?.includes(category as any) ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    const current = filter.category || [];
                    const updated = current.includes(category as any)
                      ? current.filter(c => c !== category)
                      : [...current, category as any];
                    handleFilterChange('category', updated);
                  }}
                  className="text-xs"
                >
                  {category} ({count})
                </Button>
              ))}
            </div>
          </div>

          {/* Source */}
          <div>
            <Label className="text-xs">Source</Label>
            <div className="flex flex-wrap gap-1 mt-1">
              {['google', 'system', 'custom'].map(source => (
                <Button
                  key={source}
                  variant={filter.source?.includes(source as any) ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    const current = filter.source || [];
                    const updated = current.includes(source as any)
                      ? current.filter(s => s !== source)
                      : [...current, source as any];
                    handleFilterChange('source', updated);
                  }}
                  className="text-xs capitalize"
                >
                  {source}
                </Button>
              ))}
            </div>
          </div>

          {/* Options */}
          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="loaded-only"
                checked={filter.loadedOnly || false}
                onCheckedChange={(checked) => handleFilterChange('loadedOnly', checked)}
              />
              <Label htmlFor="loaded-only" className="text-xs">Loaded only</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="custom-only"
                checked={filter.customOnly || false}
                onCheckedChange={(checked) => handleFilterChange('customOnly', checked)}
              />
              <Label htmlFor="custom-only" className="text-xs">Custom only</Label>
            </div>
          </div>

          {/* Clear filters */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onFilterChange({})}
            className="w-full"
          >
            Clear Filters
          </Button>
        </CardContent>
      )}
    </Card>
  );
};

// Typography settings component
const TypographySettingsPanel: React.FC<{
  settings: TypographySettings;
  onSettingsChange: (settings: Partial<TypographySettings>) => void;
}> = ({ settings, onSettingsChange }) => {
  const handleChange = (key: keyof TypographySettings, value: any) => {
    onSettingsChange({ [key]: value });
  };

  return (
    <div className="space-y-4">
      {/* Font Properties */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Font Properties</Label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor="font-size" className="text-xs">Size</Label>
            <Input
              id="font-size"
              type="number"
              value={settings.fontSize}
              onChange={(e) => handleChange('fontSize', parseInt(e.target.value))}
              className="text-xs"
              min="6"
              max="200"
            />
          </div>
          <div>
            <Label htmlFor="font-weight" className="text-xs">Weight</Label>
            <Select
              value={settings.fontWeight.toString()}
              onValueChange={(value) => handleChange('fontWeight', 
                isNaN(parseInt(value)) ? value : parseInt(value)
              )}
            >
              <option value="100">Thin</option>
              <option value="200">Extra Light</option>
              <option value="300">Light</option>
              <option value="400">Regular</option>
              <option value="500">Medium</option>
              <option value="600">Semi Bold</option>
              <option value="700">Bold</option>
              <option value="800">Extra Bold</option>
              <option value="900">Black</option>
            </Select>
          </div>
        </div>
      </div>

      {/* Text Style */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Text Style</Label>
        <div className="flex items-center gap-2 mb-2">
          <Button
            variant={settings.fontStyle === 'italic' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleChange('fontStyle', 
              settings.fontStyle === 'italic' ? 'normal' : 'italic'
            )}
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant={settings.textDecoration === 'underline' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleChange('textDecoration', 
              settings.textDecoration === 'underline' ? 'none' : 'underline'
            )}
          >
            <Underline className="h-4 w-4" />
          </Button>
        </div>
        <div>
          <Label htmlFor="text-transform" className="text-xs">Transform</Label>
          <Select
            value={settings.textTransform}
            onValueChange={(value) => handleChange('textTransform', value)}
          >
            <option value="none">None</option>
            <option value="uppercase">Uppercase</option>
            <option value="lowercase">Lowercase</option>
            <option value="capitalize">Capitalize</option>
          </Select>
        </div>
      </div>

      {/* Alignment */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Alignment</Label>
        <div className="flex items-center gap-1">
          <Button
            variant={settings.textAlign === 'left' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleChange('textAlign', 'left')}
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            variant={settings.textAlign === 'center' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleChange('textAlign', 'center')}
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            variant={settings.textAlign === 'right' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleChange('textAlign', 'right')}
          >
            <AlignRight className="h-4 w-4" />
          </Button>
          <Button
            variant={settings.textAlign === 'justify' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleChange('textAlign', 'justify')}
          >
            <AlignJustify className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Spacing */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Spacing</Label>
        <div className="space-y-2">
          <div>
            <Label htmlFor="letter-spacing" className="text-xs">Letter Spacing</Label>
            <div className="flex items-center gap-2">
              <Slider
                id="letter-spacing"
                value={[settings.letterSpacing]}
                onValueChange={(value) => handleChange('letterSpacing', value[0])}
                min={-10}
                max={10}
                step={0.1}
                className="flex-1"
              />
              <span className="text-xs min-w-[40px]">{settings.letterSpacing}</span>
            </div>
          </div>
          <div>
            <Label htmlFor="line-height" className="text-xs">Line Height</Label>
            <div className="flex items-center gap-2">
              <Slider
                id="line-height"
                value={[settings.lineHeight]}
                onValueChange={(value) => handleChange('lineHeight', value[0])}
                min={0.5}
                max={3}
                step={0.1}
                className="flex-1"
              />
              <span className="text-xs min-w-[40px]">{settings.lineHeight}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Colors */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Colors</Label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor="text-color" className="text-xs">Fill</Label>
            <div className="flex items-center gap-2">
              <Input
                id="text-color"
                type="color"
                value={settings.fill}
                onChange={(e) => handleChange('fill', e.target.value)}
                className="w-12 h-8 p-0 border-0"
              />
              <Input
                value={settings.fill}
                onChange={(e) => handleChange('fill', e.target.value)}
                className="text-xs"
                placeholder="#000000"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="stroke-color" className="text-xs">Stroke</Label>
            <div className="flex items-center gap-2">
              <Input
                id="stroke-color"
                type="color"
                value={settings.stroke}
                onChange={(e) => handleChange('stroke', e.target.value)}
                className="w-12 h-8 p-0 border-0"
              />
              <Input
                value={settings.stroke}
                onChange={(e) => handleChange('stroke', e.target.value)}
                className="text-xs"
                placeholder="transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Options */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Advanced</Label>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="kerning" className="text-xs">Kerning</Label>
            <Switch
              id="kerning"
              checked={settings.kerning}
              onCheckedChange={(checked) => handleChange('kerning', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label htmlFor="ligatures" className="text-xs">Ligatures</Label>
            <Switch
              id="ligatures"
              checked={settings.ligatures}
              onCheckedChange={(checked) => handleChange('ligatures', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label htmlFor="hyphenation" className="text-xs">Hyphenation</Label>
            <Switch
              id="hyphenation"
              checked={settings.hyphenation}
              onCheckedChange={(checked) => handleChange('hyphenation', checked)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Text effects component
const TextEffectsPanel: React.FC<{
  effects: TextEffect[];
  presets: TextEffectPreset[];
  activeInstances: any[];
  onAddEffect: (effectId: string) => void;
  onUpdateEffect: (instanceId: string, updates: any) => void;
  onRemoveEffect: (instanceId: string) => void;
  onApplyPreset: (instanceId: string, presetId: string) => void;
}> = ({ effects, presets, activeInstances, onAddEffect, onUpdateEffect, onRemoveEffect, onApplyPreset }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = useMemo(() => {
    const cats = new Set(effects.map(effect => effect.category));
    return ['all', ...Array.from(cats)];
  }, [effects]);

  const filteredEffects = useMemo(() => {
    return selectedCategory === 'all' 
      ? effects 
      : effects.filter(effect => effect.category === selectedCategory);
  }, [effects, selectedCategory]);

  return (
    <div className="space-y-4">
      {/* Category filter */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Categories</Label>
        <div className="flex flex-wrap gap-1">
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="text-xs capitalize"
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Available effects */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Available Effects</Label>
        <div className="grid grid-cols-2 gap-2">
          {filteredEffects.map(effect => (
            <Button
              key={effect.id}
              variant="outline"
              size="sm"
              onClick={() => onAddEffect(effect.id)}
              className="text-xs justify-start"
            >
              <Sparkles className="h-4 w-4 mr-1" />
              {effect.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Active effects */}
      {activeInstances.length > 0 && (
        <div>
          <Label className="text-sm font-medium mb-2 block">Active Effects</Label>
          <div className="space-y-2">
            {activeInstances.map(instance => (
              <Card key={instance.id} className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">{instance.effectId}</span>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemoveEffect(instance.id)}
                      className="p-1 text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label className="text-xs">Opacity</Label>
                    <Slider
                      value={[instance.opacity]}
                      onValueChange={(value) => onUpdateEffect(instance.id, { opacity: value[0] })}
                      min={0}
                      max={1}
                      step={0.1}
                      className="flex-1"
                    />
                    <span className="text-xs min-w-[40px]">{Math.round(instance.opacity * 100)}%</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Label className="text-xs">Intensity</Label>
                    <Slider
                      value={[instance.intensity]}
                      onValueChange={(value) => onUpdateEffect(instance.id, { intensity: value[0] })}
                      min={0}
                      max={2}
                      step={0.1}
                      className="flex-1"
                    />
                    <span className="text-xs min-w-[40px]">{Math.round(instance.intensity * 100)}%</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Presets */}
      {presets.length > 0 && (
        <div>
          <Label className="text-sm font-medium mb-2 block">Presets</Label>
          <div className="grid grid-cols-2 gap-2">
            {presets.map(preset => (
              <Button
                key={preset.id}
                variant="outline"
                size="sm"
                onClick={() => {
                  // Apply to first active instance for now
                  if (activeInstances.length > 0) {
                    onApplyPreset(activeInstances[0].id, preset.id);
                  }
                }}
                className="text-xs justify-start"
                disabled={activeInstances.length === 0}
              >
                <Palette className="h-4 w-4 mr-1" />
                {preset.name}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Main Typography Panel Component
export const TypographyPanel: React.FC<TypographyPanelProps> = ({
  canvas,
  className,
  onTextObjectSelect,
  onFontChange,
  onSettingsChange
}) => {
  const typography = useTypography(canvas, {
    enablePerformanceMonitoring: true,
    enableRealTimePreview: true,
    enableAutoSave: true,
    enableKeyboardShortcuts: true,
    enableGoogleFonts: true,
    enableCustomFonts: true
  });

  const [activeTab, setActiveTab] = useState('fonts');
  const [fontFilter, setFontFilter] = useState<FontFilter>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedText, setSelectedText] = useState('The quick brown fox jumps over the lazy dog');
  const [previewSize, setPreviewSize] = useState(24);

  // Font previews
  const [fontPreviews, setFontPreviews] = useState<Record<string, FontPreview>>({});

  // Generate font previews
  useEffect(() => {
    const generatePreviews = async () => {
      const previews: Record<string, FontPreview> = {};
      
      for (const font of typography.fontLibrary.fonts.slice(0, 20)) { // Limit for performance
        const preview = typography.preview.generatePreview(font.id, selectedText, { 
          fontSize: previewSize 
        });
        if (preview) {
          previews[font.id] = preview;
        }
      }
      
      setFontPreviews(previews);
    };

    if (typography.state.isInitialized) {
      generatePreviews();
    }
  }, [typography.state.isInitialized, selectedText, previewSize, typography.fontLibrary.fonts]);

  const handleFontSelect = useCallback((font: FontFamily) => {
    typography.actions.updateTypographySettings({ fontFamily: font.name });
    onFontChange?.(font);
  }, [typography.actions, onFontChange]);

  const handleTextObjectSelect = useCallback((textId: string) => {
    typography.actions.selectTextObject(textId);
    onTextObjectSelect?.(textId);
  }, [typography.actions, onTextObjectSelect]);

  const handleSettingsChange = useCallback((settings: Partial<TypographySettings>) => {
    typography.actions.updateTypographySettings(settings);
    onSettingsChange?.(settings);
  }, [typography.actions, onSettingsChange]);

  const handleCreateText = useCallback(() => {
    const textId = typography.actions.createTextObject(
      'New Text',
      typography.state.typographySettings,
      { x: 100, y: 100 }
    );
    handleTextObjectSelect(textId);
  }, [typography.actions, typography.state.typographySettings, handleTextObjectSelect]);

  const handleCustomFontUpload = useCallback(async (file: File) => {
    try {
      const fontId = await typography.actions.uploadCustomFont(file);
      console.log('Custom font uploaded:', fontId);
    } catch (error) {
      console.error('Failed to upload custom font:', error);
    }
  }, [typography.actions]);

  if (!typography.state.isInitialized) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          <span>Loading typography system...</span>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={`h-full flex flex-col ${className}`}>
        {/* Header */}
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Type className="h-5 w-5" />
              <h2 className="text-lg font-semibold">Typography</h2>
            </div>
            <div className="flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm" onClick={handleCreateText}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add Text</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm" onClick={() => typography.actions.clearCaches()}>
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Clear Caches</TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Performance indicators */}
          <div className="flex items-center gap-4 text-xs text-gray-500">
            <span>Fonts: {typography.fontLibrary.stats.totalFonts}</span>
            <span>Loaded: {typography.fontLibrary.stats.loadedFonts}</span>
            <span>Objects: {typography.state.textObjects.length}</span>
            <span>Memory: {Math.round(typography.performance.memoryUsage / 1024)}KB</span>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="fonts">Fonts</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="effects">Effects</TabsTrigger>
              <TabsTrigger value="objects">Objects</TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-hidden">
              {/* Fonts Tab */}
              <TabsContent value="fonts" className="h-full m-0">
                <div className="h-full flex flex-col">
                  {/* Font controls */}
                  <div className="p-4 border-b space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Button
                          variant={viewMode === 'grid' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setViewMode('grid')}
                        >
                          <Grid className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={viewMode === 'list' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setViewMode('list')}
                        >
                          <List className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="flex items-center gap-2">
                        <Label htmlFor="font-upload" className="cursor-pointer">
                          <Button variant="outline" size="sm" asChild>
                            <span>
                              <Upload className="h-4 w-4 mr-1" />
                              Upload
                            </span>
                          </Button>
                        </Label>
                        <input
                          id="font-upload"
                          type="file"
                          accept=".ttf,.otf,.woff,.woff2"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handleCustomFontUpload(file);
                            }
                          }}
                          className="hidden"
                        />
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Label htmlFor="preview-text" className="text-xs">Preview Text:</Label>
                      <Input
                        id="preview-text"
                        value={selectedText}
                        onChange={(e) => setSelectedText(e.target.value)}
                        className="flex-1 text-xs"
                      />
                      <Input
                        type="number"
                        value={previewSize}
                        onChange={(e) => setPreviewSize(parseInt(e.target.value))}
                        className="w-16 text-xs"
                        min="12"
                        max="48"
                      />
                    </div>
                  </div>

                  {/* Font filters */}
                  <div className="p-4 border-b">
                    <FontFilterPanel
                      filter={fontFilter}
                      onFilterChange={setFontFilter}
                      categories={typography.fontLibrary.categories}
                    />
                  </div>

                  {/* Font grid */}
                  <ScrollArea className="flex-1">
                    <div className="p-4">
                      <div className={`grid gap-4 ${
                        viewMode === 'grid' ? 'grid-cols-1' : 'grid-cols-1'
                      }`}>
                        {typography.fontLibrary.fonts.map(font => (
                          <FontPreviewCard
                            key={font.id}
                            font={font}
                            preview={fontPreviews[font.id] || null}
                            isSelected={typography.state.typographySettings.fontFamily === font.name}
                            isFavorite={typography.fontLibrary.favorites.some(f => f.id === font.id)}
                            onSelect={handleFontSelect}
                            onToggleFavorite={typography.actions.addToFavorites}
                            onLoad={typography.actions.loadFont}
                          />
                        ))}
                      </div>
                    </div>
                  </ScrollArea>
                </div>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings" className="h-full m-0">
                <ScrollArea className="h-full">
                  <div className="p-4">
                    <TypographySettingsPanel
                      settings={typography.state.typographySettings}
                      onSettingsChange={handleSettingsChange}
                    />
                  </div>
                </ScrollArea>
              </TabsContent>

              {/* Effects Tab */}
              <TabsContent value="effects" className="h-full m-0">
                <ScrollArea className="h-full">
                  <div className="p-4">
                    <TextEffectsPanel
                      effects={typography.effects.availableEffects}
                      presets={typography.effects.presets}
                      activeInstances={typography.effects.activeInstances}
                      onAddEffect={(effectId) => {
                        if (typography.state.activeTextObject) {
                          typography.actions.addEffect(typography.state.activeTextObject, effectId);
                        }
                      }}
                      onUpdateEffect={typography.actions.updateEffect}
                      onRemoveEffect={typography.actions.removeEffect}
                      onApplyPreset={typography.actions.applyEffectPreset}
                    />
                  </div>
                </ScrollArea>
              </TabsContent>

              {/* Objects Tab */}
              <TabsContent value="objects" className="h-full m-0">
                <ScrollArea className="h-full">
                  <div className="p-4">
                    <div className="space-y-2">
                      {typography.state.textObjects.map(textObject => (
                        <Card 
                          key={textObject.id}
                          className={`p-3 cursor-pointer transition-all ${
                            typography.state.activeTextObject === textObject.id 
                              ? 'ring-2 ring-blue-500' 
                              : ''
                          }`}
                          onClick={() => handleTextObjectSelect(textObject.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-sm truncate">
                                {textObject.content}
                              </div>
                              <div className="text-xs text-gray-500">
                                {textObject.settings.fontFamily} • {textObject.settings.fontSize}px
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  typography.actions.duplicateTextObject(textObject.id);
                                }}
                                className="p-1"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  typography.actions.deleteTextObject(textObject.id);
                                }}
                                className="p-1 text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default TypographyPanel;