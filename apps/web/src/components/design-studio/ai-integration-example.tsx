'use client';

import React, { useRef, useEffect, useState } from 'react';
import { AIImageGenerator } from './ai-image-generator';
import { HybridCanvasEditor } from './hybrid-canvas-editor';
import { LayerManagementPanel } from './layer-management-panel';
import { EnhancedHybridCanvasManager } from '../../lib/canvas/enhanced-hybrid-canvas';
import { AdvancedLayerManager } from '../../lib/canvas/advanced-layer-manager';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';

/**
 * Example integration component showing how to use the AI Image Generator
 * with the Visual Design Studio's canvas and layer management systems.
 * 
 * This component demonstrates:
 * - Complete integration between AI generation and canvas
 * - Layer management for AI-generated content
 * - Professional workflow combining manual and AI design
 */
export const AIIntegrationExample: React.FC<{
  apiKey: string;
  className?: string;
}> = ({ apiKey, className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvasManager, setCanvasManager] = useState<EnhancedHybridCanvasManager | null>(null);
  const [layerManager, setLayerManager] = useState<AdvancedLayerManager | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize canvas and layer managers
  useEffect(() => {
    if (!canvasRef.current) return;

    const initializeManagers = async () => {
      try {
        // Create canvas manager
        const canvas = new EnhancedHybridCanvasManager(canvasRef.current!, {
          fabricConfig: {
            width: 800,
            height: 600,
            backgroundColor: '#ffffff',
            selection: true,
            preserveObjectStacking: true
          },
          hybridConfig: {
            enableAutoRouting: true,
            performanceThreshold: 60,
            useWebGLWhenAvailable: true,
            enablePerformanceMonitoring: true,
            dynamicOptimization: true
          }
        });

        // Wait for canvas to be ready
        await new Promise<void>((resolve) => {
          if (canvas.isReady) {
            resolve();
          } else {
            canvas.once('hybrid:initialized', () => resolve());
          }
        });

        // Create layer manager
        const layers = new AdvancedLayerManager(canvas.fabricCanvasManager.canvas!);

        setCanvasManager(canvas);
        setLayerManager(layers);
        setIsInitialized(true);

        console.log('AI Integration Example: Canvas and layer managers initialized');
      } catch (error) {
        console.error('Failed to initialize canvas managers:', error);
      }
    };

    initializeManagers();

    return () => {
      // Cleanup
      if (canvasManager) {
        canvasManager.dispose();
      }
      if (layerManager) {
        layerManager.dispose();
      }
    };
  }, []);

  if (!isInitialized) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span>Initializing Visual Design Studio...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-3 gap-6 ${className}`}>
      {/* Canvas Area */}
      <div className="lg:col-span-2 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Visual Design Canvas</CardTitle>
            <CardDescription>
              Professional hybrid canvas with AI generation integration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden bg-gray-50">
              <canvas
                ref={canvasRef}
                width="800"
                height="600"
                className="max-w-full h-auto"
                style={{ display: 'block' }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Canvas Controls */}
        {canvasManager && layerManager && (
          <HybridCanvasEditor
            canvasManager={canvasManager}
            layerManager={layerManager}
          />
        )}
      </div>

      {/* AI Generation and Layer Management Sidebar */}
      <div className="space-y-6">
        {/* AI Image Generator */}
        <Card>
          <CardHeader>
            <CardTitle>AI Image Generation</CardTitle>
            <CardDescription>
              Professional AI-powered image generation with Replicate integration
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {canvasManager && layerManager ? (
              <AIImageGenerator
                canvasManager={canvasManager}
                layerManager={layerManager}
                apiKey={apiKey}
                className="p-6"
              />
            ) : (
              <div className="p-6 text-center text-muted-foreground">
                Waiting for canvas initialization...
              </div>
            )}
          </CardContent>
        </Card>

        <Separator />

        {/* Layer Management */}
        <Card>
          <CardHeader>
            <CardTitle>Layer Management</CardTitle>
            <CardDescription>
              Advanced layer system for organizing AI and manual content
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {canvasManager && layerManager ? (
              <LayerManagementPanel
                fabricCanvas={canvasManager.fabricCanvasManager.canvas!}
                pixiApp={canvasManager.pixiCanvasManager?.app || undefined}
                className="p-6"
              />
            ) : (
              <div className="p-6 text-center text-muted-foreground">
                Waiting for layer manager initialization...
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AIIntegrationExample;