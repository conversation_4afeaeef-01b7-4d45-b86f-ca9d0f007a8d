'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { 
  useAIGeneration, 
  useTextToImageGeneration, 
  useImageEnhancement, 
  useInpainting,
  AIGenerationHookReturn 
} from '../../hooks/use-ai-generation';
import { EnhancedHybridCanvasManager } from '../../lib/canvas/enhanced-hybrid-canvas';
import { AdvancedLayerManager } from '../../lib/canvas/advanced-layer-manager';
import { ReplicateModelConfig, GenerationProgress } from '../../lib/ai/replicate-api-service';
import { AIImagePlacementOptions } from '../../lib/ai/ai-canvas-integration';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Switch } from '../ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/dropdown-menu';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';

// Icons (assuming they're available from a UI library)
const Icons = {
  Wand: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
    </svg>
  ),
  Image: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
  ),
  Sparkles: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3l1.5 1.5L9 3l-1.5 1.5L5 3zm4 8l1.5 1.5L13 11l-1.5 1.5L9 11zm6-4l1.5 1.5L19 7l-1.5 1.5L15 7z" />
    </svg>
  ),
  Settings: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  ),
  Loader: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  ),
  Play: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293L12 11l.707-.707A1 1 0 0113.414 10H15M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  X: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
  ),
  Trash: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
    </svg>
  ),
  DollarSign: ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  )
};

// Component props
export interface AIImageGeneratorProps {
  canvasManager: EnhancedHybridCanvasManager | null;
  layerManager: AdvancedLayerManager | null;
  apiKey: string;
  className?: string;
}

// Prompt templates for quick start
const PROMPT_TEMPLATES = [
  {
    name: "Photorealistic Portrait",
    prompt: "professional portrait photography, beautiful person, studio lighting, high detail, photorealistic",
    negativePrompt: "cartoon, anime, painting, sketch, low quality, blurry"
  },
  {
    name: "Digital Art",
    prompt: "digital art, fantasy illustration, vibrant colors, detailed, concept art style",
    negativePrompt: "photograph, realistic, low quality, blurry"
  },
  {
    name: "Logo Design",
    prompt: "minimalist logo design, clean, vector style, professional, simple geometric shapes",
    negativePrompt: "complex, cluttered, photographic, realistic"
  },
  {
    name: "Landscape",
    prompt: "beautiful landscape, natural scenery, high resolution, scenic view, dramatic lighting",
    negativePrompt: "people, buildings, urban, low quality"
  },
  {
    name: "Abstract Art",
    prompt: "abstract art, geometric patterns, vibrant colors, modern design, artistic composition",
    negativePrompt: "realistic, photographic, people, objects"
  }
];

// Progress indicator component
const ProgressIndicator: React.FC<{ progress?: GenerationProgress }> = ({ progress }) => {
  if (!progress) return null;

  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm text-muted-foreground">
        <span>{progress.message}</span>
        <span>{Math.round(progress.progress || 0)}%</span>
      </div>
      <div className="w-full bg-secondary rounded-full h-2">
        <div 
          className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progress.progress || 0}%` }}
        />
      </div>
    </div>
  );
};

// Model selection component
const ModelSelector: React.FC<{
  models: ReplicateModelConfig[];
  selectedModel?: ReplicateModelConfig;
  onModelSelect: (modelId: string) => void;
}> = ({ models, selectedModel, onModelSelect }) => {
  const modelsByCategory = useMemo(() => {
    const categories = new Map<string, ReplicateModelConfig[]>();
    models.forEach(model => {
      if (!categories.has(model.category)) {
        categories.set(model.category, []);
      }
      categories.get(model.category)!.push(model);
    });
    return categories;
  }, [models]);

  return (
    <div className="space-y-4">
      <Label>AI Model</Label>
      <Select value={selectedModel?.modelId} onValueChange={onModelSelect}>
        <SelectTrigger>
          <SelectValue placeholder="Select a model" />
        </SelectTrigger>
        <SelectContent>
          {Array.from(modelsByCategory.entries()).map(([category, categoryModels]) => (
            <div key={category}>
              <div className="px-2 py-1 text-sm font-medium text-muted-foreground capitalize">
                {category.replace('-', ' ')}
              </div>
              {categoryModels.map((model) => (
                <SelectItem key={model.modelId} value={model.modelId}>
                  <div className="flex items-center justify-between w-full">
                    <span className="truncate">{model.modelId.split('/').pop()}</span>
                    <div className="flex items-center space-x-1 ml-2">
                      <Badge variant="secondary" className="text-xs">
                        ${model.costPerGeneration.toFixed(2)}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {model.averageProcessingTime}s
                      </Badge>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </div>
          ))}
        </SelectContent>
      </Select>
      
      {selectedModel && (
        <div className="p-3 border rounded-lg bg-muted/50 space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-medium">{selectedModel.modelId}</span>
            <Badge>{selectedModel.qualityScore}/10</Badge>
          </div>
          <div className="text-sm text-muted-foreground">
            Specializations: {selectedModel.specializations.join(', ')}
          </div>
          <div className="flex items-center space-x-4 text-sm">
            <span>Cost: ${selectedModel.costPerGeneration}</span>
            <span>~{selectedModel.averageProcessingTime}s</span>
          </div>
        </div>
      )}
    </div>
  );
};

// Cost tracker component
const CostTracker: React.FC<{ aiGeneration: AIGenerationHookReturn }> = ({ aiGeneration }) => {
  const { costSummary } = aiGeneration.state;

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center">
          <Icons.DollarSign className="w-4 h-4 mr-2" />
          Cost Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="text-muted-foreground">Today</div>
            <div className="font-medium">${costSummary.daily.toFixed(2)}</div>
          </div>
          <div>
            <div className="text-muted-foreground">This Month</div>
            <div className="font-medium">${costSummary.monthly.toFixed(2)}</div>
          </div>
        </div>
        
        <Separator />
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Avg/Generation</span>
            <span>${costSummary.perGeneration.toFixed(3)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Budget Remaining</span>
            <span className="text-green-600">${costSummary.remainingBudget.daily.toFixed(2)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Generation history component
const GenerationHistory: React.FC<{ 
  aiGeneration: AIGenerationHookReturn;
  onRetry: (operationId: string) => void;
}> = ({ aiGeneration, onRetry }) => {
  const { operationHistory, activeOperations } = aiGeneration.state;

  return (
    <ScrollArea className="h-64">
      <div className="space-y-2">
        {activeOperations.map((operation) => (
          <div key={operation.id} className="p-3 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
            <div className="flex items-center justify-between mb-2">
              <Badge variant="outline" className="text-xs">
                {operation.type}
              </Badge>
              <div className="flex items-center space-x-2">
                <Icons.Loader className="w-3 h-3 animate-spin" />
                <span className="text-xs text-muted-foreground">
                  {operation.progress}%
                </span>
              </div>
            </div>
            <ProgressIndicator progress={aiGeneration.state.currentProgress} />
          </div>
        ))}
        
        {operationHistory.slice(0, 10).map((operation) => (
          <div key={operation.id} className="p-3 border rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <Badge 
                variant={operation.status === 'completed' ? 'default' : 'destructive'}
                className="text-xs"
              >
                {operation.type}
              </Badge>
              <div className="flex items-center space-x-2">
                {operation.status === 'failed' && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onRetry(operation.id)}
                    className="h-6 px-2"
                  >
                    Retry
                  </Button>
                )}
                <span className="text-xs text-muted-foreground">
                  {operation.endTime ? 
                    `${((operation.endTime - operation.startTime) / 1000).toFixed(1)}s` : 
                    'Processing...'
                  }
                </span>
              </div>
            </div>
            
            {operation.status === 'failed' && operation.error && (
              <div className="text-xs text-red-600 mt-1">
                {operation.error}
              </div>
            )}
            
            {operation.result && (
              <div className="text-xs text-muted-foreground mt-1">
                Cost: ${operation.result.cost?.toFixed(3) || '0.000'}
              </div>
            )}
          </div>
        ))}
      </div>
    </ScrollArea>
  );
};

// Main AI Image Generator component
export const AIImageGenerator: React.FC<AIImageGeneratorProps> = ({
  canvasManager,
  layerManager,
  apiKey,
  className
}) => {
  // AI Generation hook
  const aiGeneration = useAIGeneration(canvasManager, layerManager, {
    apiKey,
    autoSelectOptimalModel: true,
    enableCaching: true,
    enablePerformanceMonitoring: true,
    defaultQuality: 'balanced'
  });

  // Specialized hooks
  const textToImage = useTextToImageGeneration(aiGeneration);
  const imageEnhancement = useImageEnhancement(aiGeneration);
  const inpainting = useInpainting(aiGeneration);

  // Local state
  const [activeTab, setActiveTab] = useState('text-to-image');
  const [placementOptions, setPlacementOptions] = useState<Partial<AIImagePlacementOptions>>({
    x: 0,
    y: 0,
    scaleToFit: false,
    maintainAspectRatio: true,
    opacity: 1
  });
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Error handling
  const [error, setError] = useState<string | null>(null);

  // Clear error when starting new generation
  useEffect(() => {
    if (aiGeneration.state.isGenerating) {
      setError(null);
    }
  }, [aiGeneration.state.isGenerating]);

  // Handle errors from AI generation
  useEffect(() => {
    if (aiGeneration.state.lastError) {
      setError(aiGeneration.state.lastError);
    }
  }, [aiGeneration.state.lastError]);

  // Handle prompt template selection
  const handleTemplateSelect = useCallback((template: typeof PROMPT_TEMPLATES[0]) => {
    textToImage.setPrompt(template.prompt);
    textToImage.setNegativePrompt(template.negativePrompt);
  }, [textToImage]);

  // Handle generation retry
  const handleRetry = useCallback(async (operationId: string) => {
    try {
      setError(null);
      await aiGeneration.actions.retryOperation(operationId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to retry operation');
    }
  }, [aiGeneration]);

  // Handle budget updates
  const handleBudgetUpdate = useCallback((type: 'daily' | 'monthly', value: number) => {
    if (type === 'daily') {
      aiGeneration.actions.setDailyBudget(value);
    } else {
      aiGeneration.actions.setMonthlyBudget(value);
    }
  }, [aiGeneration]);

  if (!aiGeneration.isReady) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2">
            <Icons.Loader className="w-5 h-5 animate-spin" />
            <span>Initializing AI services...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Error display */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
          <CardContent className="flex items-center justify-between py-3">
            <span className="text-red-600 dark:text-red-400 text-sm">{error}</span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setError(null)}
            >
              <Icons.X className="w-4 h-4" />
            </Button>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 w-full">
          <TabsTrigger value="text-to-image" className="text-xs">
            <Icons.Wand className="w-4 h-4 mr-1" />
            Text to Image
          </TabsTrigger>
          <TabsTrigger value="image-to-image" className="text-xs">
            <Icons.Image className="w-4 h-4 mr-1" />
            Image to Image
          </TabsTrigger>
          <TabsTrigger value="enhancement" className="text-xs">
            <Icons.Sparkles className="w-4 h-4 mr-1" />
            Enhancement
          </TabsTrigger>
          <TabsTrigger value="inpainting" className="text-xs">
            <Icons.Settings className="w-4 h-4 mr-1" />
            Inpainting
          </TabsTrigger>
        </TabsList>

        {/* Text to Image Tab */}
        <TabsContent value="text-to-image" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Generate from Text</CardTitle>
              <CardDescription>
                Create images from text descriptions using AI
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Prompt Templates */}
              <div className="space-y-2">
                <Label>Quick Templates</Label>
                <div className="flex flex-wrap gap-2">
                  {PROMPT_TEMPLATES.map((template) => (
                    <Button
                      key={template.name}
                      size="sm"
                      variant="outline"
                      onClick={() => handleTemplateSelect(template)}
                      className="text-xs"
                    >
                      {template.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Main Prompt */}
              <div className="space-y-2">
                <Label htmlFor="prompt">Prompt</Label>
                <textarea
                  id="prompt"
                  className="w-full min-h-20 p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Describe what you want to generate..."
                  value={textToImage.prompt}
                  onChange={(e) => textToImage.setPrompt(e.target.value)}
                />
              </div>

              {/* Negative Prompt */}
              <div className="space-y-2">
                <Label htmlFor="negative-prompt">Negative Prompt (Optional)</Label>
                <Input
                  id="negative-prompt"
                  placeholder="What to avoid in the image..."
                  value={textToImage.negativePrompt}
                  onChange={(e) => textToImage.setNegativePrompt(e.target.value)}
                />
              </div>

              {/* Generation Settings */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Quality</Label>
                  <Select value={textToImage.quality} onValueChange={(value: any) => textToImage.setQuality(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fast">Fast (~8s)</SelectItem>
                      <SelectItem value="balanced">Balanced (~20s)</SelectItem>
                      <SelectItem value="quality">Quality (~30s)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Dimensions</Label>
                  <Select 
                    value={`${textToImage.dimensions.width}x${textToImage.dimensions.height}`} 
                    onValueChange={(value) => {
                      const [width, height] = value.split('x').map(Number);
                      textToImage.setDimensions({ width, height });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="512x512">Square (512x512)</SelectItem>
                      <SelectItem value="768x768">Square (768x768)</SelectItem>
                      <SelectItem value="1024x1024">Square (1024x1024)</SelectItem>
                      <SelectItem value="768x1024">Portrait (768x1024)</SelectItem>
                      <SelectItem value="1024x768">Landscape (1024x768)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Advanced Options */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showAdvanced}
                    onCheckedChange={setShowAdvanced}
                  />
                  <Label>Advanced Options</Label>
                </div>

                {showAdvanced && (
                  <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                    <div className="space-y-2">
                      <Label htmlFor="seed">Seed (for reproducible results)</Label>
                      <Input
                        id="seed"
                        type="number"
                        placeholder="Leave empty for random"
                        value={textToImage.seed || ''}
                        onChange={(e) => textToImage.setSeed(e.target.value ? parseInt(e.target.value) : undefined)}
                      />
                    </div>

                    <ModelSelector
                      models={aiGeneration.state.availableModels}
                      selectedModel={aiGeneration.state.selectedModel}
                      onModelSelect={aiGeneration.actions.selectModel}
                    />
                  </div>
                )}
              </div>

              {/* Generate Button */}
              <Button 
                onClick={textToImage.generate}
                disabled={!textToImage.prompt.trim() || aiGeneration.state.isGenerating}
                className="w-full"
                size="lg"
              >
                {aiGeneration.state.isGenerating ? (
                  <>
                    <Icons.Loader className="w-4 h-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Icons.Play className="w-4 h-4 mr-2" />
                    Generate Image
                  </>
                )}
              </Button>

              {/* Progress */}
              {aiGeneration.state.currentProgress && (
                <ProgressIndicator progress={aiGeneration.state.currentProgress} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Image Enhancement Tab */}
        <TabsContent value="enhancement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Image Enhancement</CardTitle>
              <CardDescription>
                Upscale, restore, or enhance existing images
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Enhancement Type</Label>
                <Select 
                  value={imageEnhancement.enhancementType} 
                  onValueChange={(value: any) => imageEnhancement.setEnhancementType(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="upscale">Upscale (2-4x larger)</SelectItem>
                    <SelectItem value="restore">Face Restoration</SelectItem>
                    <SelectItem value="denoise">Remove Noise</SelectItem>
                    <SelectItem value="enhance">General Enhancement</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Button
                  onClick={imageEnhancement.enhanceFromCanvas}
                  disabled={aiGeneration.state.isGenerating}
                  variant="outline"
                >
                  <Icons.Image className="w-4 h-4 mr-2" />
                  Enhance Canvas
                </Button>

                <Button
                  disabled
                  variant="outline"
                  className="opacity-50"
                >
                  <Icons.Image className="w-4 h-4 mr-2" />
                  Upload Image
                </Button>
              </div>

              {/* Progress */}
              {aiGeneration.state.currentProgress && (
                <ProgressIndicator progress={aiGeneration.state.currentProgress} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Inpainting Tab */}
        <TabsContent value="inpainting" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Inpainting</CardTitle>
              <CardDescription>
                Modify selected areas of your canvas with AI
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {inpainting.selectedArea ? (
                <>
                  <div className="p-3 border rounded-lg bg-green-50 dark:bg-green-950/20">
                    <div className="text-sm text-green-600 dark:text-green-400">
                      Area selected: {inpainting.selectedArea.width}×{inpainting.selectedArea.height}px
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="inpaint-prompt">What to paint in the selected area</Label>
                    <textarea
                      id="inpaint-prompt"
                      className="w-full min-h-16 p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Describe what should appear in the selected area..."
                      value={inpainting.prompt}
                      onChange={(e) => inpainting.setPrompt(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="inpaint-negative">Negative Prompt (Optional)</Label>
                    <Input
                      id="inpaint-negative"
                      placeholder="What to avoid..."
                      value={inpainting.negativePrompt}
                      onChange={(e) => inpainting.setNegativePrompt(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Strength: {inpainting.strength}</Label>
                    <input
                      type="range"
                      min="0.1"
                      max="1"
                      step="0.1"
                      value={inpainting.strength}
                      onChange={(e) => inpainting.setStrength(parseFloat(e.target.value))}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      Lower values preserve more of the original, higher values allow more changes
                    </div>
                  </div>

                  <Button 
                    onClick={inpainting.inpaintSelection}
                    disabled={!inpainting.prompt.trim() || aiGeneration.state.isGenerating}
                    className="w-full"
                  >
                    {aiGeneration.state.isGenerating ? (
                      <>
                        <Icons.Loader className="w-4 h-4 mr-2 animate-spin" />
                        Inpainting...
                      </>
                    ) : (
                      <>
                        <Icons.Sparkles className="w-4 h-4 mr-2" />
                        Inpaint Selection
                      </>
                    )}
                  </Button>
                </>
              ) : (
                <div className="p-6 border-2 border-dashed rounded-lg text-center">
                  <Icons.Image className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                  <div className="text-sm text-muted-foreground">
                    Select an area on the canvas to begin inpainting
                  </div>
                </div>
              )}

              {/* Progress */}
              {aiGeneration.state.currentProgress && (
                <ProgressIndicator progress={aiGeneration.state.currentProgress} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Image to Image Tab */}
        <TabsContent value="image-to-image" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Image to Image</CardTitle>
              <CardDescription>
                Transform existing images with AI guidance
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center py-8">
              <div className="text-muted-foreground">
                Image to Image functionality coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Sidebar with Cost Tracking and History */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <CostTracker aiGeneration={aiGeneration} />
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Generation History</CardTitle>
          </CardHeader>
          <CardContent>
            <GenerationHistory 
              aiGeneration={aiGeneration} 
              onRetry={handleRetry} 
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};