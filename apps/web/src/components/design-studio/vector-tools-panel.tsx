import React, { useState, useCallback, useMemo } from 'react';
import { 
  Box, 
  Flex, 
  VStack, 
  HStack, 
  Button, 
  ButtonGroup,
  IconButton, 
  Tooltip, 
  Divider,
  Text,
  Badge,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Switch,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Grid,
  GridItem,
  Card,
  CardBody,
  useColorModeValue,
  useToast
} from '@chakra-ui/react';
import {
  FiMousePointer,
  FiEdit3,
  FiSquare,
  FiCircle,
  FiMinus,
  FiTriangle,
  FiStar,
  FiHeart,
  FiMessageCircle,
  FiArrowRight,
  FiSearch,
  FiGrid,
  FiMove,
  FiRotateCw,
  FiCopy,
  FiTrash2,
  FiUndo,
  FiRedo,
  FiAlignLeft,
  FiAlignCenter,
  FiAlignRight,
  FiAlignJustify,
  FiLayers,
  FiSettings,
  FiDownload,
  FiUpload
} from 'react-icons/fi';

import { VectorTool, VectorShape } from '../../lib/canvas/vector-graphics-engine';
import { ShapeTemplate } from '../../lib/canvas/shape-library';
import { VectorToolConfig, ToolWorkflow } from '../../lib/canvas/vector-tools-manager';
import { 
  useVectorTools, 
  VectorToolsHookReturn, 
  useShapeLibraryManager,
  VectorToolsHookOptions 
} from '../../hooks/use-vector-tools';
import { fabric } from 'fabric';

// Component props interfaces
export interface VectorToolsPanelProps {
  canvas: fabric.Canvas | null;
  onToolChange?: (tool: VectorTool) => void;
  onShapeCreated?: (shape: VectorShape) => void;
  options?: VectorToolsHookOptions;
  className?: string;
}

interface ToolButtonProps {
  tool: VectorToolConfig;
  isActive: boolean;
  onClick: () => void;
  size?: 'sm' | 'md' | 'lg';
}

interface ShapeLibraryPanelProps {
  templates: ShapeTemplate[];
  onCreateShape: (templateId: string, x: number, y: number) => void;
  onSaveAsTemplate: (shape: VectorShape) => void;
  className?: string;
}

interface VectorPropertiesPanelProps {
  selectedShapes: VectorShape[];
  onUpdateShape: (shapeId: string, updates: Partial<VectorShape>) => void;
  className?: string;
}

// Tool icons mapping
const toolIcons: Record<VectorTool, React.ComponentType> = {
  select: FiMousePointer,
  pen: FiEdit3,
  bezier: FiEdit3,
  rectangle: FiSquare,
  ellipse: FiCircle,
  polygon: FiTriangle,
  line: FiMinus,
  pencil: FiEdit3,
  brush: FiEdit3
};

// Shape template icons mapping
const shapeIcons: Record<string, React.ComponentType> = {
  rectangle: FiSquare,
  ellipse: FiCircle,
  polygon: FiTriangle,
  star: FiStar,
  heart: FiHeart,
  'speech-bubble': FiMessageCircle,
  arrow: FiArrowRight,
  'double-arrow': FiArrowRight
};

// Tool Button Component
const ToolButton: React.FC<ToolButtonProps> = ({ tool, isActive, onClick, size = 'md' }) => {
  const IconComponent = toolIcons[tool.id as VectorTool] || FiMousePointer;
  
  return (
    <Tooltip label={`${tool.name} (${tool.shortcuts.join(', ')})`} placement="right">
      <IconButton
        aria-label={tool.name}
        icon={<IconComponent />}
        size={size}
        variant={isActive ? 'solid' : 'ghost'}
        colorScheme={isActive ? 'blue' : 'gray'}
        onClick={onClick}
        isDisabled={!tool.enabled}
      />
    </Tooltip>
  );
};

// Tool Options Panel Component
const ToolOptionsPanel: React.FC<{
  activeTool: VectorTool;
  toolConfig: VectorToolConfig | undefined;
  toolOptions: Record<string, any>;
  onOptionChange: (option: string, value: any) => void;
}> = ({ activeTool, toolConfig, toolOptions, onOptionChange }) => {
  if (!toolConfig || !toolConfig.options.length) {
    return (
      <Box p={4} textAlign="center">
        <Text color="gray.500" fontSize="sm">
          No options available for this tool
        </Text>
      </Box>
    );
  }

  const groupedOptions = useMemo(() => {
    const groups: Record<string, typeof toolConfig.options> = {};
    
    toolConfig.options.forEach(option => {
      const group = option.group || 'General';
      if (!groups[group]) groups[group] = [];
      groups[group].push(option);
    });
    
    return groups;
  }, [toolConfig]);

  return (
    <VStack spacing={4} align="stretch">
      {Object.entries(groupedOptions).map(([group, options]) => (
        <Box key={group}>
          <Text fontWeight="semibold" mb={2} fontSize="sm">
            {group}
          </Text>
          <VStack spacing={3} align="stretch">
            {options.map(option => {
              const currentValue = toolOptions[option.name] ?? option.defaultValue;
              
              return (
                <Box key={option.name}>
                  <Text fontSize="xs" mb={1} color="gray.600">
                    {option.description || option.name}
                  </Text>
                  
                  {option.type === 'range' && (
                    <Slider
                      value={currentValue}
                      min={option.min}
                      max={option.max}
                      step={option.step}
                      onChange={(value) => onOptionChange(option.name, value)}
                      size="sm"
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb boxSize={4} />
                    </Slider>
                  )}
                  
                  {option.type === 'boolean' && (
                    <Switch
                      isChecked={currentValue}
                      onChange={(e) => onOptionChange(option.name, e.target.checked)}
                      size="sm"
                    />
                  )}
                  
                  {option.type === 'color' && (
                    <Input
                      type="color"
                      value={currentValue}
                      onChange={(e) => onOptionChange(option.name, e.target.value)}
                      size="sm"
                      h={8}
                    />
                  )}
                  
                  {option.type === 'select' && option.options && (
                    <Select
                      value={currentValue}
                      onChange={(e) => onOptionChange(option.name, e.target.value)}
                      size="sm"
                    >
                      {option.options.map(opt => (
                        <option key={opt.value} value={opt.value}>
                          {opt.label}
                        </option>
                      ))}
                    </Select>
                  )}
                  
                  {option.type === 'number' && (
                    <Input
                      type="number"
                      value={currentValue}
                      onChange={(e) => onOptionChange(option.name, parseFloat(e.target.value))}
                      min={option.min}
                      max={option.max}
                      step={option.step}
                      size="sm"
                    />
                  )}
                </Box>
              );
            })}
          </VStack>
        </Box>
      ))}
    </VStack>
  );
};

// Shape Library Panel Component
const ShapeLibraryPanel: React.FC<ShapeLibraryPanelProps> = ({ 
  templates, 
  onCreateShape, 
  onSaveAsTemplate,
  className 
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = useMemo(() => {
    const cats = new Set(templates.map(t => t.category));
    return ['all', ...Array.from(cats)];
  }, [templates]);

  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
      const matchesSearch = !searchQuery || 
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      return matchesCategory && matchesSearch;
    });
  }, [templates, selectedCategory, searchQuery]);

  const handleShapeClick = useCallback((templateId: string) => {
    // Create shape at center of canvas
    onCreateShape(templateId, 200, 200);
  }, [onCreateShape]);

  return (
    <Box className={className}>
      <VStack spacing={4} align="stretch">
        {/* Search and Filter */}
        <VStack spacing={2} align="stretch">
          <InputGroup size="sm">
            <InputLeftElement pointerEvents="none">
              <FiSearch />
            </InputLeftElement>
            <Input
              placeholder="Search shapes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </InputGroup>
          
          <Select
            size="sm"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </Select>
        </VStack>

        {/* Shape Grid */}
        <Grid templateColumns="repeat(3, 1fr)" gap={2}>
          {filteredTemplates.map(template => {
            const IconComponent = shapeIcons[template.id] || FiSquare;
            
            return (
              <GridItem key={template.id}>
                <Tooltip label={template.description || template.name}>
                  <Card
                    size="sm"
                    variant="outline"
                    cursor="pointer"
                    _hover={{ bg: 'gray.50' }}
                    onClick={() => handleShapeClick(template.id)}
                  >
                    <CardBody textAlign="center" p={2}>
                      <IconComponent size={20} />
                      <Text fontSize="xs" mt={1} noOfLines={1}>
                        {template.name}
                      </Text>
                      {template.complexity !== 'simple' && (
                        <Badge size="xs" colorScheme="orange">
                          {template.complexity}
                        </Badge>
                      )}
                    </CardBody>
                  </Card>
                </Tooltip>
              </GridItem>
            );
          })}
        </Grid>

        {filteredTemplates.length === 0 && (
          <Box textAlign="center" py={4}>
            <Text color="gray.500" fontSize="sm">
              No shapes found
            </Text>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

// Vector Properties Panel Component
const VectorPropertiesPanel: React.FC<VectorPropertiesPanelProps> = ({ 
  selectedShapes, 
  onUpdateShape,
  className 
}) => {
  const hasSelection = selectedShapes.length > 0;
  const multiSelection = selectedShapes.length > 1;
  
  if (!hasSelection) {
    return (
      <Box className={className} p={4} textAlign="center">
        <Text color="gray.500" fontSize="sm">
          Select an object to view properties
        </Text>
      </Box>
    );
  }

  const firstShape = selectedShapes[0];
  
  return (
    <Box className={className}>
      <VStack spacing={4} align="stretch">
        <Box>
          <Text fontWeight="semibold" mb={2}>
            {multiSelection ? `${selectedShapes.length} objects selected` : firstShape.metadata.tags[0] || 'Shape'}
          </Text>
        </Box>

        <Accordion defaultIndex={[0]} allowMultiple>
          {/* Transform Properties */}
          <AccordionItem>
            <AccordionButton>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Transform</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel pb={4}>
              <VStack spacing={3} align="stretch">
                <HStack>
                  <Box flex="1">
                    <Text fontSize="xs" mb={1}>X</Text>
                    <Input
                      size="sm"
                      type="number"
                      value={firstShape.transform.x}
                      onChange={(e) => onUpdateShape(firstShape.id, {
                        transform: { ...firstShape.transform, x: parseFloat(e.target.value) }
                      })}
                    />
                  </Box>
                  <Box flex="1">
                    <Text fontSize="xs" mb={1}>Y</Text>
                    <Input
                      size="sm"
                      type="number"
                      value={firstShape.transform.y}
                      onChange={(e) => onUpdateShape(firstShape.id, {
                        transform: { ...firstShape.transform, y: parseFloat(e.target.value) }
                      })}
                    />
                  </Box>
                </HStack>
                
                <HStack>
                  <Box flex="1">
                    <Text fontSize="xs" mb={1}>Scale X</Text>
                    <Input
                      size="sm"
                      type="number"
                      step="0.1"
                      value={firstShape.transform.scaleX}
                      onChange={(e) => onUpdateShape(firstShape.id, {
                        transform: { ...firstShape.transform, scaleX: parseFloat(e.target.value) }
                      })}
                    />
                  </Box>
                  <Box flex="1">
                    <Text fontSize="xs" mb={1}>Scale Y</Text>
                    <Input
                      size="sm"
                      type="number"
                      step="0.1"
                      value={firstShape.transform.scaleY}
                      onChange={(e) => onUpdateShape(firstShape.id, {
                        transform: { ...firstShape.transform, scaleY: parseFloat(e.target.value) }
                      })}
                    />
                  </Box>
                </HStack>
                
                <Box>
                  <Text fontSize="xs" mb={1}>Rotation</Text>
                  <Slider
                    value={firstShape.transform.rotation}
                    min={-180}
                    max={180}
                    step={1}
                    onChange={(value) => onUpdateShape(firstShape.id, {
                      transform: { ...firstShape.transform, rotation: value }
                    })}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <SliderThumb />
                  </Slider>
                </Box>
              </VStack>
            </AccordionPanel>
          </AccordionItem>

          {/* Appearance Properties */}
          <AccordionItem>
            <AccordionButton>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Appearance</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel pb={4}>
              <VStack spacing={3} align="stretch">
                <Box>
                  <Text fontSize="xs" mb={1}>Opacity</Text>
                  <Slider
                    value={(firstShape.style.opacity || 1) * 100}
                    min={0}
                    max={100}
                    step={1}
                    onChange={(value) => onUpdateShape(firstShape.id, {
                      style: { ...firstShape.style, opacity: value / 100 }
                    })}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <SliderThumb />
                  </Slider>
                </Box>
                
                <HStack>
                  <Box flex="1">
                    <Text fontSize="xs" mb={1}>Fill</Text>
                    <Input
                      size="sm"
                      type="color"
                      value={firstShape.style.fill || '#ffffff'}
                      onChange={(e) => onUpdateShape(firstShape.id, {
                        style: { ...firstShape.style, fill: e.target.value }
                      })}
                    />
                  </Box>
                  <Box flex="1">
                    <Text fontSize="xs" mb={1}>Stroke</Text>
                    <Input
                      size="sm"
                      type="color"
                      value={firstShape.style.stroke || '#000000'}
                      onChange={(e) => onUpdateShape(firstShape.id, {
                        style: { ...firstShape.style, stroke: e.target.value }
                      })}
                    />
                  </Box>
                </HStack>
                
                <Box>
                  <Text fontSize="xs" mb={1}>Stroke Width</Text>
                  <Slider
                    value={firstShape.style.strokeWidth || 1}
                    min={0}
                    max={20}
                    step={0.5}
                    onChange={(value) => onUpdateShape(firstShape.id, {
                      style: { ...firstShape.style, strokeWidth: value }
                    })}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <SliderThumb />
                  </Slider>
                </Box>
              </VStack>
            </AccordionPanel>
          </AccordionItem>

          {/* Effects (if any) */}
          {firstShape.path && (
            <AccordionItem>
              <AccordionButton>
                <Box flex="1" textAlign="left">
                  <Text fontSize="sm" fontWeight="medium">Path Properties</Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel pb={4}>
                <VStack spacing={3} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="xs">Points:</Text>
                    <Badge>{firstShape.path.points.length}</Badge>
                  </HStack>
                  <HStack justify="space-between">
                    <Text fontSize="xs">Closed:</Text>
                    <Badge colorScheme={firstShape.path.closed ? 'green' : 'gray'}>
                      {firstShape.path.closed ? 'Yes' : 'No'}
                    </Badge>
                  </HStack>
                </VStack>
              </AccordionPanel>
            </AccordionItem>
          )}
        </Accordion>
      </VStack>
    </Box>
  );
};

// Main Vector Tools Panel Component
export const VectorToolsPanel: React.FC<VectorToolsPanelProps> = ({
  canvas,
  onToolChange,
  onShapeCreated,
  options,
  className
}) => {
  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Initialize vector tools
  const vectorTools = useVectorTools(canvas, options);
  const { state, actions, tools, workflows, shapeLibrary, history } = vectorTools;

  // Shape library manager
  const shapeManager = useShapeLibraryManager(null); // Would pass actual shape library instance

  // Handle tool selection
  const handleToolSelect = useCallback((tool: VectorTool) => {
    actions.setTool(tool);
    onToolChange?.(tool);
  }, [actions, onToolChange]);

  // Handle tool option changes
  const handleToolOptionChange = useCallback((option: string, value: any) => {
    actions.setToolOption(state.activeTool, option, value);
  }, [actions, state.activeTool]);

  // Handle shape creation
  const handleCreateShape = useCallback((templateId: string, x: number, y: number) => {
    actions.createShape(templateId, { x, y });
    
    toast({
      title: 'Shape created',
      description: `Created ${templateId} at (${x}, ${y})`,
      status: 'success',
      duration: 2000,
      isClosable: true
    });
  }, [actions, toast]);

  // Handle shape updates
  const handleUpdateShape = useCallback((shapeId: string, updates: Partial<VectorShape>) => {
    // Implementation would update the shape through vector tools
    console.log('Update shape:', shapeId, updates);
  }, []);

  // Get current tool config
  const currentToolConfig = useMemo(() => 
    tools.find(tool => tool.id === state.activeTool),
    [tools, state.activeTool]
  );

  // Group tools by category
  const toolsByCategory = useMemo(() => {
    const categories: Record<string, VectorToolConfig[]> = {};
    tools.forEach(tool => {
      if (!categories[tool.category]) categories[tool.category] = [];
      categories[tool.category].push(tool);
    });
    return categories;
  }, [tools]);

  if (!state.isInitialized) {
    return (
      <Box className={className} p={4} textAlign="center">
        <Text>Initializing vector tools...</Text>
      </Box>
    );
  }

  if (state.error) {
    return (
      <Box className={className} p={4} textAlign="center">
        <Text color="red.500">Error: {state.error}</Text>
      </Box>
    );
  }

  return (
    <Box 
      className={className}
      bg={bgColor}
      borderWidth="1px"
      borderColor={borderColor}
      borderRadius="md"
      w="320px"
      h="100%"
      display="flex"
      flexDirection="column"
    >
      <Tabs variant="enclosed" flex="1" display="flex" flexDirection="column">
        <TabList>
          <Tab fontSize="sm">Tools</Tab>
          <Tab fontSize="sm">Shapes</Tab>
          <Tab fontSize="sm">Properties</Tab>
        </TabList>

        <TabPanels flex="1" overflow="hidden">
          {/* Tools Panel */}
          <TabPanel p={0} h="100%" display="flex" flexDirection="column">
            <VStack spacing={4} align="stretch" flex="1">
              {/* Tool Categories */}
              <Box p={4}>
                {Object.entries(toolsByCategory).map(([category, categoryTools]) => (
                  <Box key={category} mb={4}>
                    <Text fontSize="xs" fontWeight="semibold" mb={2} color="gray.600" textTransform="uppercase">
                      {category}
                    </Text>
                    <ButtonGroup size="sm" variant="outline" spacing={1}>
                      {categoryTools.map(tool => (
                        <ToolButton
                          key={tool.id}
                          tool={tool}
                          isActive={state.activeTool === tool.id}
                          onClick={() => handleToolSelect(tool.id as VectorTool)}
                          size="sm"
                        />
                      ))}
                    </ButtonGroup>
                  </Box>
                ))}
              </Box>

              <Divider />

              {/* Tool Options */}
              <Box flex="1" overflow="auto" p={4}>
                <Text fontSize="sm" fontWeight="semibold" mb={3}>
                  Tool Options
                </Text>
                <ToolOptionsPanel
                  activeTool={state.activeTool}
                  toolConfig={currentToolConfig}
                  toolOptions={state.toolOptions[state.activeTool] || {}}
                  onOptionChange={handleToolOptionChange}
                />
              </Box>

              {/* Action Buttons */}
              <Box p={4} borderTopWidth="1px" borderColor={borderColor}>
                <VStack spacing={2}>
                  <ButtonGroup size="sm" variant="outline" width="100%">
                    <Button
                      leftIcon={<FiUndo />}
                      onClick={actions.undo}
                      isDisabled={!history.canUndo}
                      flex="1"
                    >
                      Undo
                    </Button>
                    <Button
                      leftIcon={<FiRedo />}
                      onClick={actions.redo}
                      isDisabled={!history.canRedo}
                      flex="1"
                    >
                      Redo
                    </Button>
                  </ButtonGroup>
                  
                  <ButtonGroup size="sm" variant="outline" width="100%">
                    <Button leftIcon={<FiCopy />} onClick={actions.copySelection} flex="1">
                      Copy
                    </Button>
                    <Button leftIcon={<FiTrash2 />} onClick={actions.deleteSelection} flex="1">
                      Delete
                    </Button>
                  </ButtonGroup>
                </VStack>
              </Box>
            </VStack>
          </TabPanel>

          {/* Shapes Panel */}
          <TabPanel p={4} h="100%" overflow="auto">
            <ShapeLibraryPanel
              templates={shapeLibrary.templates}
              onCreateShape={handleCreateShape}
              onSaveAsTemplate={shapeLibrary.createCustomShape}
            />
          </TabPanel>

          {/* Properties Panel */}
          <TabPanel p={0} h="100%" overflow="auto">
            <VectorPropertiesPanel
              selectedShapes={[]} // Would get from selection state
              onUpdateShape={handleUpdateShape}
            />
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Performance Monitor */}
      {options?.enablePerformanceMonitoring && (
        <Box p={2} borderTopWidth="1px" borderColor={borderColor} fontSize="xs">
          <HStack justify="space-between">
            <Text color="gray.500">Objects: {vectorTools.performance.objectCount}</Text>
            <Text color="gray.500">
              Render: {vectorTools.performance.renderTime.toFixed(1)}ms
            </Text>
            <Badge 
              colorScheme={vectorTools.performance.isOptimized ? 'green' : 'orange'}
              size="sm"
            >
              {vectorTools.performance.isOptimized ? 'Optimized' : 'Slow'}
            </Badge>
          </HStack>
        </Box>
      )}
    </Box>
  );
};

export default VectorToolsPanel;