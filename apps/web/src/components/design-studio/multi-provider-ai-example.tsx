'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Loader2, 
  Image as ImageIcon, 
  CheckCircle, 
  AlertTriangle,
  Palette,
  Zap,
  DollarSign,
  Clock
} from 'lucide-react';
import { 
  useMultiProviderAI, 
  ProviderManagementPanel 
} from './index';
import { 
  MultiProviderConfig, 
  SelectionPriority 
} from '../../lib/ai/multi-provider-service';

interface MultiProviderAIExampleProps {
  config: MultiProviderConfig;
  className?: string;
}

export const MultiProviderAIExample: React.FC<MultiProviderAIExampleProps> = ({
  config,
  className = ''
}) => {
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectionPriority, setSelectionPriority] = useState<SelectionPriority>('balanced');
  const [dimensions, setDimensions] = useState({ width: 1024, height: 1024 });
  const [quality, setQuality] = useState<'draft' | 'standard' | 'premium'>('standard');
  const [generatedImages, setGeneratedImages] = useState<Array<{
    id: string;
    url: string;
    provider: string;
    cost: number;
    time: number;
    metadata: any;
  }>>([]);

  // Initialize multi-provider AI service
  const multiProviderAI = useMultiProviderAI({
    config,
    enableAutoRetry: true,
    maxRetries: 3,
    enableProgressTracking: true,
    enableBudgetAlerts: true,
    onGenerationComplete: (response) => {
      if (response.status === 'completed' && response.images.length > 0) {
        const newImages = response.images.map((image, index) => ({
          id: `${response.id}_${index}`,
          url: image.url,
          provider: response.provider,
          cost: response.totalCost,
          time: response.totalTime,
          metadata: response.metadata
        }));
        setGeneratedImages(prev => [...newImages, ...prev]);
      }
    },
    onGenerationError: (error) => {
      console.error('Generation failed:', error);
    },
    onBudgetAlert: (alert) => {
      console.warn('Budget alert:', alert);
    }
  });

  const { state, actions, isReady } = multiProviderAI;

  // Handle text-to-image generation
  const handleGenerate = useCallback(async () => {
    if (!prompt.trim() || !isReady) return;

    try {
      await actions.generateTextToImage(prompt, {
        negativePrompt: negativePrompt || undefined,
        width: dimensions.width,
        height: dimensions.height,
        quality,
        providerPreference: selectedProvider || undefined,
        selectionCriteria: {
          priority: selectionPriority,
          quality: {
            minQualityScore: quality === 'premium' ? 9 : quality === 'standard' ? 7 : 5,
            outputResolution: quality === 'premium' ? 'high' : quality === 'standard' ? 'medium' : 'low'
          },
          speed: {
            urgency: 'medium'
          }
        }
      });
    } catch (error) {
      console.error('Generation error:', error);
    }
  }, [prompt, negativePrompt, dimensions, quality, selectedProvider, selectionPriority, actions, isReady]);

  // Handle provider recommendation
  const handleGetRecommendations = useCallback(async () => {
    try {
      const recommendations = await actions.getProviderRecommendations('text-to-image');
      console.log('Provider recommendations:', recommendations);
    } catch (error) {
      console.error('Failed to get recommendations:', error);
    }
  }, [actions]);

  // Render generation form
  const renderGenerationForm = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="w-5 h-5" />
          Multi-Provider Image Generation
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Generation Status */}
        {state.isGenerating && state.currentGeneration && (
          <Alert>
            <Loader2 className="w-4 h-4 animate-spin" />
            <AlertDescription>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>Generating with {state.currentGeneration.provider}...</span>
                  <Badge variant="secondary">{state.currentGeneration.status}</Badge>
                </div>
                {state.currentGeneration.progress > 0 && (
                  <Progress value={state.currentGeneration.progress} />
                )}
                {state.currentGeneration.estimatedTimeRemaining && (
                  <div className="text-sm text-muted-foreground">
                    ~{Math.round(state.currentGeneration.estimatedTimeRemaining)}s remaining
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Error Display */}
        {state.lastError && (
          <Alert variant="destructive">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>{state.lastError}</AlertDescription>
          </Alert>
        )}

        {/* Prompt Input */}
        <div className="space-y-2">
          <Label htmlFor="prompt">Prompt</Label>
          <Input
            id="prompt"
            placeholder="Describe the image you want to generate..."
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            disabled={state.isGenerating}
          />
        </div>

        {/* Negative Prompt Input */}
        <div className="space-y-2">
          <Label htmlFor="negative-prompt">Negative Prompt (Optional)</Label>
          <Input
            id="negative-prompt"
            placeholder="What to avoid in the image..."
            value={negativePrompt}
            onChange={(e) => setNegativePrompt(e.target.value)}
            disabled={state.isGenerating}
          />
        </div>

        {/* Generation Options */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="provider">Provider Preference</Label>
            <Select value={selectedProvider} onValueChange={setSelectedProvider}>
              <SelectTrigger>
                <SelectValue placeholder="Auto-select (recommended)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Auto-select (recommended)</SelectItem>
                {state.availableProviders.map(provider => (
                  <SelectItem key={provider} value={provider}>
                    {provider}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="priority">Selection Priority</Label>
            <Select value={selectionPriority} onValueChange={(value: SelectionPriority) => setSelectionPriority(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cost">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4" />
                    Cost - Cheapest
                  </div>
                </SelectItem>
                <SelectItem value="quality">
                  <div className="flex items-center gap-2">
                    <Palette className="w-4 h-4" />
                    Quality - Best Results
                  </div>
                </SelectItem>
                <SelectItem value="speed">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    Speed - Fastest
                  </div>
                </SelectItem>
                <SelectItem value="balanced">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Balanced - Optimal
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Quality and Dimensions */}
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="quality">Quality</Label>
            <Select value={quality} onValueChange={(value: 'draft' | 'standard' | 'premium') => setQuality(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="premium">Premium</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="width">Width</Label>
            <Select value={dimensions.width.toString()} onValueChange={(value) => setDimensions(prev => ({ ...prev, width: parseInt(value) }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="512">512px</SelectItem>
                <SelectItem value="1024">1024px</SelectItem>
                <SelectItem value="1536">1536px</SelectItem>
                <SelectItem value="2048">2048px</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="height">Height</Label>
            <Select value={dimensions.height.toString()} onValueChange={(value) => setDimensions(prev => ({ ...prev, height: parseInt(value) }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="512">512px</SelectItem>
                <SelectItem value="1024">1024px</SelectItem>
                <SelectItem value="1536">1536px</SelectItem>
                <SelectItem value="2048">2048px</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button 
            onClick={handleGenerate} 
            disabled={!prompt.trim() || state.isGenerating || !isReady}
            className="flex-1"
          >
            {state.isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <ImageIcon className="w-4 h-4 mr-2" />
                Generate Image
              </>
            )}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleGetRecommendations}
            disabled={!isReady}
          >
            Get Recommendations
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  // Render generation history
  const renderGenerationHistory = () => (
    <Card>
      <CardHeader>
        <CardTitle>Generation History</CardTitle>
      </CardHeader>
      <CardContent>
        {generatedImages.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No images generated yet. Try creating your first image!
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {generatedImages.map((image) => (
              <div key={image.id} className="space-y-2">
                <div className="aspect-square bg-muted rounded-lg overflow-hidden">
                  <img 
                    src={image.url} 
                    alt="Generated image"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      {image.provider}
                    </Badge>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {Math.round(image.time / 1000)}s
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-3 h-3" />
                        ${image.cost.toFixed(3)}
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground truncate">
                    {image.metadata.prompt}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  // Render metrics summary
  const renderMetricsSummary = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="text-center">
            <div className="text-2xl font-bold">{state.metrics.totalGenerations}</div>
            <div className="text-sm text-muted-foreground">Total Generations</div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="text-center">
            <div className="text-2xl font-bold">{(state.metrics.successRate * 100).toFixed(1)}%</div>
            <div className="text-sm text-muted-foreground">Success Rate</div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="text-center">
            <div className="text-2xl font-bold">${state.metrics.averageCost.toFixed(3)}</div>
            <div className="text-sm text-muted-foreground">Avg Cost</div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="text-center">
            <div className="text-2xl font-bold">{Math.round(state.metrics.averageTime / 1000)}s</div>
            <div className="text-sm text-muted-foreground">Avg Time</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  if (!isReady) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">Initializing AI providers...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Metrics Summary */}
      {renderMetricsSummary()}

      {/* Generation Form */}
      {renderGenerationForm()}

      {/* Provider Management Panel */}
      <ProviderManagementPanel multiProviderAI={multiProviderAI} />

      {/* Generation History */}
      {renderGenerationHistory()}
    </div>
  );
};