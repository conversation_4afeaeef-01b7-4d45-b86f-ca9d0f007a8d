import { fabric } from 'fabric';
import * as PIXI from 'pixi.js';
import { AdvancedLayerManager, LayerNode, CreateLayerOptions } from '../../../lib/canvas/advanced-layer-manager';
import { LayerHybridIntegration } from '../../../lib/canvas/layer-hybrid-integration';

// Mock PIXI Application for testing
class MockPIXIApp {
  stage = new PIXI.Container();
  renderer = {
    render: jest.fn(),
    extract: {
      canvas: jest.fn()
    }
  };
  
  destroy = jest.fn();
}

describe('Layer Management Integration', () => {
  let fabricCanvas: fabric.Canvas;
  let pixiApp: any;
  let layerManager: AdvancedLayerManager;
  let hybridIntegration: LayerHybridIntegration;

  beforeEach(() => {
    // Create mock canvas element
    const canvasElement = document.createElement('canvas');
    canvasElement.width = 800;
    canvasElement.height = 600;
    
    // Initialize Fabric.js canvas
    fabricCanvas = new fabric.Canvas(canvasElement);
    
    // Initialize mock PIXI app
    pixiApp = new MockPIXIApp();
    
    // Initialize layer manager
    layerManager = new AdvancedLayerManager(fabricCanvas);
    
    // Initialize hybrid integration
    hybridIntegration = new LayerHybridIntegration(
      fabricCanvas,
      pixiApp as any,
      layerManager
    );
  });

  afterEach(() => {
    // Cleanup
    layerManager.dispose();
    hybridIntegration.dispose();
    fabricCanvas.dispose();
  });

  describe('Basic Layer Operations', () => {
    it('should create layers successfully', () => {
      const options: CreateLayerOptions = {
        name: 'Test Layer',
        type: 'shape',
        visible: true,
        locked: false,
        opacity: 1.0
      };

      const layer = layerManager.createLayer(options);

      expect(layer).toBeDefined();
      expect(layer.name).toBe('Test Layer');
      expect(layer.type).toBe('shape');
      expect(layer.visible).toBe(true);
      expect(layer.locked).toBe(false);
      expect(layer.opacity).toBe(1.0);
    });

    it('should manage layer hierarchy', () => {
      // Create parent layer
      const parent = layerManager.createLayer({
        name: 'Parent',
        type: 'group'
      });

      // Create child layer
      const child = layerManager.createLayer({
        name: 'Child',
        type: 'shape',
        parent: parent.id
      });

      expect(parent.children).toContain(child.id);
      expect(child.parent).toBe(parent.id);
    });

    it('should handle layer property updates', () => {
      const layer = layerManager.createLayer({
        name: 'Test Layer',
        type: 'text'
      });

      const updated = layerManager.updateLayerProperty(layer.id, 'opacity', 0.5);
      expect(updated).toBe(true);

      const updatedLayer = layerManager.getLayer(layer.id);
      expect(updatedLayer?.opacity).toBe(0.5);
    });

    it('should support layer effects', () => {
      const layer = layerManager.createLayer({
        name: 'Effect Layer',
        type: 'shape'
      });

      const effect = {
        id: 'test-effect',
        type: 'drop-shadow' as const,
        enabled: true,
        opacity: 0.75,
        blendMode: 'normal' as const,
        parameters: {
          color: '#000000',
          blur: 4,
          offsetX: 4,
          offsetY: 4
        }
      };

      const added = layerManager.addLayerEffect(layer.id, effect);
      expect(added).toBe(true);

      const updatedLayer = layerManager.getLayer(layer.id);
      expect(updatedLayer?.effects).toHaveLength(1);
      expect(updatedLayer?.effects[0].type).toBe('drop-shadow');
    });

    it('should support layer grouping', () => {
      const layer1 = layerManager.createLayer({ name: 'Layer 1', type: 'shape' });
      const layer2 = layerManager.createLayer({ name: 'Layer 2', type: 'text' });

      const group = layerManager.createLayerGroup([layer1.id, layer2.id], 'Test Group');

      expect(group.type).toBe('group');
      expect(group.children).toContain(layer1.id);
      expect(group.children).toContain(layer2.id);

      const updatedLayer1 = layerManager.getLayer(layer1.id);
      const updatedLayer2 = layerManager.getLayer(layer2.id);
      
      expect(updatedLayer1?.parent).toBe(group.id);
      expect(updatedLayer2?.parent).toBe(group.id);
    });

    it('should support bulk operations', () => {
      const layer1 = layerManager.createLayer({ name: 'Layer 1', type: 'shape' });
      const layer2 = layerManager.createLayer({ name: 'Layer 2', type: 'text' });

      const updated = layerManager.bulkUpdateLayers([layer1.id, layer2.id], {
        opacity: 0.5,
        visible: false
      });

      expect(updated).toHaveLength(2);
      expect(updated).toContain(layer1.id);
      expect(updated).toContain(layer2.id);

      const updatedLayer1 = layerManager.getLayer(layer1.id);
      const updatedLayer2 = layerManager.getLayer(layer2.id);

      expect(updatedLayer1?.opacity).toBe(0.5);
      expect(updatedLayer1?.visible).toBe(false);
      expect(updatedLayer2?.opacity).toBe(0.5);
      expect(updatedLayer2?.visible).toBe(false);
    });
  });

  describe('Search and Filtering', () => {
    beforeEach(() => {
      // Create test layers
      layerManager.createLayer({
        name: 'Background',
        type: 'image',
        tags: ['background', 'photo']
      });

      layerManager.createLayer({
        name: 'Title Text',
        type: 'text',
        tags: ['text', 'title']
      });

      layerManager.createLayer({
        name: 'Button Shape',
        type: 'shape',
        tags: ['ui', 'button']
      });
    });

    it('should search layers by name', () => {
      const results = layerManager.searchLayers({
        query: 'Title',
        includeMetadata: false,
        caseSensitive: false,
        exactMatch: false,
        searchTags: false,
        searchEffects: false
      });

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('Title Text');
    });

    it('should search layers by tags', () => {
      const results = layerManager.searchLayers({
        query: 'text',
        includeMetadata: true,
        caseSensitive: false,
        exactMatch: false,
        searchTags: true,
        searchEffects: false
      });

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('Title Text');
    });

    it('should filter layers by type', () => {
      const results = layerManager.filterLayers({
        types: ['text', 'shape']
      });

      expect(results).toHaveLength(2);
      expect(results.some(l => l.name === 'Title Text')).toBe(true);
      expect(results.some(l => l.name === 'Button Shape')).toBe(true);
    });

    it('should filter layers by visibility', () => {
      // Hide one layer
      const layer = Array.from(layerManager.layers.values())[0];
      layerManager.updateLayerProperty(layer.id, 'visible', false);

      const visibleResults = layerManager.filterLayers({
        visible: true
      });

      const hiddenResults = layerManager.filterLayers({
        visible: false
      });

      expect(visibleResults).toHaveLength(2);
      expect(hiddenResults).toHaveLength(1);
    });
  });

  describe('Hybrid Rendering Integration', () => {
    it('should route layers to appropriate engines', () => {
      // Create a complex layer that should go to PIXI
      const complexLayer = layerManager.createLayer({
        name: 'Complex Layer',
        type: 'image'
      });

      // Add complex effects
      layerManager.addLayerEffect(complexLayer.id, {
        id: 'glow-effect',
        type: 'outer-glow',
        enabled: true,
        opacity: 0.8,
        blendMode: 'normal',
        parameters: { color: '#ffffff', size: 10 }
      });

      const engine = hybridIntegration.getLayerEngine(complexLayer.id);
      expect(engine).toBe('pixi');
    });

    it('should provide engine distribution stats', () => {
      // Create layers
      layerManager.createLayer({ name: 'Simple Text', type: 'text' });
      layerManager.createLayer({ name: 'Complex Image', type: 'image' });

      const distribution = hybridIntegration.getEngineDistribution();
      
      expect(distribution).toHaveProperty('fabric');
      expect(distribution).toHaveProperty('pixi');
      expect(typeof distribution.fabric).toBe('number');
      expect(typeof distribution.pixi).toBe('number');
    });

    it('should support performance optimization', () => {
      // Create multiple layers
      for (let i = 0; i < 10; i++) {
        layerManager.createLayer({
          name: `Layer ${i}`,
          type: i % 2 === 0 ? 'text' : 'image'
        });
      }

      // This should not throw
      expect(() => {
        hybridIntegration.optimizeForPerformance();
      }).not.toThrow();
    });
  });

  describe('Performance and Caching', () => {
    it('should provide cache statistics', () => {
      const stats = layerManager.getCacheStats();
      
      expect(stats).toHaveProperty('size');
      expect(stats).toHaveProperty('hitRate');
      expect(stats).toHaveProperty('memoryUsage');
      expect(typeof stats.size).toBe('number');
      expect(typeof stats.hitRate).toBe('number');
      expect(typeof stats.memoryUsage).toBe('number');
    });

    it('should handle large numbers of layers efficiently', () => {
      const startTime = performance.now();
      
      // Create 100 layers
      for (let i = 0; i < 100; i++) {
        layerManager.createLayer({
          name: `Performance Test Layer ${i}`,
          type: i % 3 === 0 ? 'text' : i % 3 === 1 ? 'shape' : 'image'
        });
      }
      
      const creationTime = performance.now() - startTime;
      
      // Should create 100 layers in reasonable time (< 1 second)
      expect(creationTime).toBeLessThan(1000);
      expect(layerManager.layerCount).toBe(100);
    });

    it('should maintain performance with complex operations', () => {
      // Create base layers
      const layers = [];
      for (let i = 0; i < 50; i++) {
        layers.push(layerManager.createLayer({
          name: `Layer ${i}`,
          type: 'shape'
        }));
      }

      const startTime = performance.now();
      
      // Perform bulk operations
      const layerIds = layers.map(l => l.id);
      layerManager.bulkUpdateLayers(layerIds, { opacity: 0.8 });
      
      const operationTime = performance.now() - startTime;
      
      // Should complete bulk operation quickly
      expect(operationTime).toBeLessThan(500);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid layer operations gracefully', () => {
      // Try to update non-existent layer
      const result = layerManager.updateLayerProperty('invalid-id', 'opacity', 0.5);
      expect(result).toBe(false);
    });

    it('should handle invalid effect operations gracefully', () => {
      const layer = layerManager.createLayer({ name: 'Test', type: 'shape' });
      
      // Try to remove non-existent effect
      const result = layerManager.removeLayerEffect(layer.id, 'invalid-effect');
      expect(result).toBe(false);
    });

    it('should handle invalid parent relationships gracefully', () => {
      const layer = layerManager.createLayer({ name: 'Test', type: 'shape' });
      
      // Try to move to non-existent parent
      const result = layerManager.moveLayer(layer.id, 'invalid-parent');
      expect(result).toBe(false);
    });
  });
});