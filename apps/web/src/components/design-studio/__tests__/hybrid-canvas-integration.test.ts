/**
 * @jest-environment jsdom
 */

import { WebGLCapabilityService, PerformanceMonitor, HybridCanvasManager } from '@/lib/canvas/pixi-performance-layer';

// Mock PIXI.js
jest.mock('pixi.js', () => ({
  Application: jest.fn().mockImplementation(() => ({
    stage: { addChild: jest.fn() },
    renderer: { render: jest.fn(), extract: { canvas: jest.fn() } },
    ticker: { add: jest.fn() },
    destroy: jest.fn()
  })),
  Container: jest.fn().mockImplementation(() => ({
    addChild: jest.fn(),
    removeChildren: jest.fn(),
    getChildByName: jest.fn()
  })),
  Sprite: jest.fn(),
  Graphics: jest.fn(),
  Text: jest.fn(),
  TextStyle: jest.fn(),
  ParticleContainer: jest.fn(),
  BlurFilter: jest.fn(),
  ColorMatrixFilter: jest.fn().mockImplementation(() => ({
    brightness: jest.fn(),
    contrast: jest.fn(),
    sepia: jest.fn()
  })),
  Filter: jest.fn(),
  Texture: {
    WHITE: {},
    from: jest.fn()
  },
  RenderTexture: {
    create: jest.fn()
  },
  settings: {
    SCALE_MODE: 0,
    ROUND_PIXELS: true,
    SPRITE_BATCH_SIZE: 4096
  },
  SCALE_MODES: {
    LINEAR: 0
  }
}));

// Mock fabric.js
jest.mock('fabric', () => ({
  fabric: {
    Canvas: jest.fn(),
    Object: { prototype: {} }
  }
}));

describe('WebGL Capability Detection', () => {
  beforeEach(() => {
    // Mock WebGL context
    const mockCanvas = document.createElement('canvas');
    const mockGL = {
      getParameter: jest.fn((param) => {
        switch (param) {
          case 0x0D33: // MAX_TEXTURE_SIZE
            return 4096;
          case 0x8869: // MAX_VERTEX_ATTRIBS
            return 16;
          default:
            return 0;
        }
      }),
      getSupportedExtensions: jest.fn(() => ['WEBGL_compressed_texture_s3tc']),
      MAX_TEXTURE_SIZE: 0x0D33,
      MAX_VERTEX_ATTRIBS: 0x8869,
      RENDERER: 0x1F01,
      VENDOR: 0x1F00
    };

    jest.spyOn(document, 'createElement').mockReturnValue(mockCanvas);
    jest.spyOn(mockCanvas, 'getContext').mockReturnValue(mockGL);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('should detect WebGL support correctly', () => {
    const capabilities = WebGLCapabilityService.detectWebGLSupport();
    
    expect(capabilities.supported).toBe(true);
    expect(capabilities.version).toBe('webgl1');
    expect(capabilities.features.maxTextureSize).toBe(4096);
    expect(capabilities.features.maxVertexAttribs).toBe(16);
  });

  test('should create optimal PIXI config based on capabilities', () => {
    const capabilities = WebGLCapabilityService.detectWebGLSupport();
    const config = WebGLCapabilityService.createOptimalPIXIConfig(capabilities, 800, 600);
    
    expect(config.width).toBe(800);
    expect(config.height).toBe(600);
    expect(config.powerPreference).toBe('high-performance');
    expect(config.antialias).toBe(true); // Should be true for texture size > 4096
  });

  test('should handle WebGL unavailable scenario', () => {
    // Mock no WebGL support
    jest.spyOn(document.createElement('canvas'), 'getContext').mockReturnValue(null);
    
    const capabilities = WebGLCapabilityService.detectWebGLSupport();
    expect(capabilities.supported).toBe(false);
    expect(capabilities.version).toBe('none');
    
    const config = WebGLCapabilityService.createOptimalPIXIConfig(capabilities);
    expect(config.powerPreference).toBe('default');
    expect(config.antialias).toBe(false);
  });
});

describe('Performance Monitoring', () => {
  let performanceMonitor: PerformanceMonitor;

  beforeEach(() => {
    performanceMonitor = new PerformanceMonitor();
    
    // Mock performance.now
    jest.spyOn(performance, 'now').mockReturnValue(1000);
    
    // Mock performance.memory
    Object.defineProperty(performance, 'memory', {
      value: {
        usedJSHeapSize: 100 * 1048576 // 100MB
      },
      configurable: true
    });
  });

  afterEach(() => {
    performanceMonitor.stopMonitoring();
    jest.restoreAllMocks();
  });

  test('should initialize performance monitor correctly', () => {
    expect(performanceMonitor).toBeDefined();
    expect(typeof performanceMonitor.startMonitoring).toBe('function');
    expect(typeof performanceMonitor.getPerformanceReport).toBe('function');
  });

  test('should generate performance report', () => {
    const report = performanceMonitor.getPerformanceReport();
    
    expect(report).toHaveProperty('frameRate');
    expect(report).toHaveProperty('memory');
    expect(report).toHaveProperty('renderTime');
    expect(report.frameRate).toHaveProperty('current');
    expect(report.frameRate).toHaveProperty('average');
    expect(report.memory).toHaveProperty('current');
    expect(report.renderTime).toHaveProperty('current');
  });

  test('should handle optimization callbacks', () => {
    const optimizationCallback = jest.fn();
    performanceMonitor.onOptimizationNeeded(optimizationCallback);
    
    // Trigger low FPS scenario (this would normally be done by the monitoring loop)
    performanceMonitor.emit('optimization:needed', {
      averageFPS: 20,
      memoryUsage: 100,
      renderTime: 25,
      reason: 'low_framerate',
      data: { fps: 20 }
    });
    
    expect(optimizationCallback).toHaveBeenCalled();
  });
});

describe('Hybrid Canvas Manager Integration', () => {
  let mockCanvasElement: HTMLCanvasElement;
  let hybridManager: HybridCanvasManager;

  beforeEach(() => {
    mockCanvasElement = document.createElement('canvas');
    mockCanvasElement.width = 800;
    mockCanvasElement.height = 600;
    
    // Mock device capabilities
    Object.defineProperty(navigator, 'deviceMemory', {
      value: 8,
      configurable: true
    });
    
    Object.defineProperty(navigator, 'hardwareConcurrency', {
      value: 8,
      configurable: true
    });

    const config = {
      width: 800,
      height: 600,
      antialias: true,
      resolution: 1,
      powerPreference: 'high-performance' as const,
      backgroundAlpha: 1,
      preserveDrawingBuffer: true,
      clearBeforeRender: true,
      sharedTicker: true,
      autoStart: false
    };

    hybridManager = new HybridCanvasManager(mockCanvasElement, config);
  });

  afterEach(() => {
    hybridManager.dispose();
  });

  test('should initialize hybrid canvas manager', () => {
    expect(hybridManager).toBeDefined();
    expect(hybridManager.isReady).toBe(true);
  });

  test('should detect device capabilities', () => {
    const capabilities = hybridManager.getDeviceCapabilities();
    
    expect(capabilities).toHaveProperty('hasWebGL');
    expect(capabilities).toHaveProperty('deviceMemory');
    expect(capabilities).toHaveProperty('hardwareConcurrency');
    expect(capabilities).toHaveProperty('performanceScore');
    expect(capabilities.deviceMemory).toBe(8);
    expect(capabilities.hardwareConcurrency).toBe(8);
  });

  test('should provide performance report', () => {
    const report = hybridManager.getPerformanceReport();
    
    expect(report).toHaveProperty('frameRate');
    expect(report).toHaveProperty('memory');
    expect(report).toHaveProperty('renderTime');
  });

  test('should handle object routing decisions', () => {
    const simpleObject = {
      id: 'test1',
      type: 'rectangle',
      x: 0,
      y: 0,
      width: 100,
      height: 100
    };

    const complexObject = {
      id: 'test2',
      type: 'polygon',
      points: new Array(150).fill({ x: 0, y: 0 }), // Complex polygon
      animated: true,
      animationFrameRate: 60
    };

    expect(hybridManager.routeObject(simpleObject)).toBe('pixi'); // Default to PIXI for performance
    expect(hybridManager.routeObject(complexObject)).toBe('pixi'); // Complex objects to PIXI
  });

  test('should optimize for device capabilities', () => {
    const initialStrategy = hybridManager.getRenderingStrategy();
    
    hybridManager.optimizeForDevice();
    
    const optimizedStrategy = hybridManager.getRenderingStrategy();
    expect(optimizedStrategy).toBeDefined();
    expect(optimizedStrategy.useWebGL).toBeDefined();
    expect(optimizedStrategy.batchSize).toBeDefined();
  });
});

describe('Performance Optimization', () => {
  test('should calculate performance score correctly', () => {
    // This tests the performance scoring algorithm used internally
    const highEndCapabilities = {
      hasWebGL: true,
      maxTextureSize: 8192,
      maxVertexAttribs: 32,
      deviceMemory: 16,
      hardwareConcurrency: 12,
      isLowEnd: false,
      isHighEnd: false,
      performanceScore: 0
    };

    // Score calculation logic (simplified version of what's in the actual code)
    let score = 0;
    if (highEndCapabilities.hasWebGL) score += 20;
    if (highEndCapabilities.maxTextureSize >= 8192) score += 20;
    if (highEndCapabilities.deviceMemory >= 8) score += 20;
    if (highEndCapabilities.hardwareConcurrency >= 8) score += 20;
    if (highEndCapabilities.maxVertexAttribs >= 32) score += 20;

    expect(score).toBe(100); // Perfect score for high-end device
  });

  test('should classify device capabilities correctly', () => {
    const lowEndScore = 25;
    const midRangeScore = 50;
    const highEndScore = 85;

    expect(lowEndScore < 30).toBe(true); // Should be classified as low-end
    expect(midRangeScore >= 30 && midRangeScore <= 70).toBe(true); // Mid-range
    expect(highEndScore > 70).toBe(true); // High-end
  });
});