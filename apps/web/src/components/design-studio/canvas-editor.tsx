'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  Square, 
  Circle, 
  Triangle, 
  Type, 
  Image as ImageIcon, 
  Undo2, 
  Redo2, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw,
  Save,
  Download,
  Upload,
  Layers,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Trash2,
  Copy,
  Move,
  MousePointer,
  Pen,
  Eraser,
  Paintbrush,
  Star,
  ArrowRight,
  Grid,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedCanvasManager, LayerManager, SnapManager, HistoryManager } from '@/lib/canvas/canvas-engine';
import { FabricObjectFactory, Shapes, Text, UI } from '@/lib/canvas/fabric-object-factory';
import { fabric } from 'fabric';

// Tool Types
type CanvasTool = 
  | 'select'
  | 'rectangle'
  | 'circle'
  | 'triangle'
  | 'star'
  | 'arrow'
  | 'line'
  | 'text'
  | 'textbox'
  | 'pen'
  | 'brush'
  | 'eraser'
  | 'image';

interface CanvasEditorProps {
  className?: string;
  initialWidth?: number;
  initialHeight?: number;
  onCanvasReady?: (canvas: EnhancedCanvasManager) => void;
}

interface ToolButtonProps {
  tool: CanvasTool;
  activeTool: CanvasTool;
  onToolSelect: (tool: CanvasTool) => void;
  icon: React.ReactNode;
  label: string;
  disabled?: boolean;
}

interface LayerInfo {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  objectCount: number;
}

interface SelectedObjectInfo {
  id: string;
  type: string;
  properties: {
    left: number;
    top: number;
    width: number;
    height: number;
    angle: number;
    opacity: number;
  };
}

const ToolButton: React.FC<ToolButtonProps> = ({ 
  tool, 
  activeTool, 
  onToolSelect, 
  icon, 
  label, 
  disabled = false 
}) => (
  <Button
    variant={activeTool === tool ? "default" : "outline"}
    size="sm"
    className={cn(
      "flex flex-col items-center gap-1 h-auto p-2",
      activeTool === tool && "bg-blue-600 text-white"
    )}
    onClick={() => onToolSelect(tool)}
    disabled={disabled}
    title={label}
  >
    {icon}
    <span className="text-xs">{label}</span>
  </Button>
);

export default function CanvasEditor({ 
  className,
  initialWidth = 800,
  initialHeight = 600,
  onCanvasReady
}: CanvasEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const canvasManagerRef = useRef<EnhancedCanvasManager | null>(null);
  
  // State management
  const [activeTool, setActiveTool] = useState<CanvasTool>('select');
  const [isCanvasReady, setIsCanvasReady] = useState(false);
  const [selectedObjects, setSelectedObjects] = useState<SelectedObjectInfo[]>([]);
  const [layers, setLayers] = useState<LayerInfo[]>([]);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [zoom, setZoom] = useState(100);
  const [canvasSize, setCanvasSize] = useState({ width: initialWidth, height: initialHeight });
  const [snapEnabled, setSnapEnabled] = useState(true);
  const [gridEnabled, setGridEnabled] = useState(false);

  // Initialize canvas
  useEffect(() => {
    if (!canvasRef.current) return;

    const canvasManager = new EnhancedCanvasManager(canvasRef.current, {
      width: initialWidth,
      height: initialHeight,
      backgroundColor: '#ffffff'
    });

    canvasManagerRef.current = canvasManager;

    // Set up event listeners
    canvasManager.on('canvas:initialized', () => {
      setIsCanvasReady(true);
      onCanvasReady?.(canvasManager);
    });

    canvasManager.on('selection:changed', ({ selected, count }) => {
      const objectInfo = selected.map(({ id, object }) => ({
        id,
        type: object.type || 'unknown',
        properties: {
          left: object.left || 0,
          top: object.top || 0,
          width: object.width || 0,
          height: object.height || 0,
          angle: object.angle || 0,
          opacity: object.opacity || 1
        }
      }));
      setSelectedObjects(objectInfo);
    });

    canvasManager.history.on('history:updated', ({ canUndo, canRedo }) => {
      setCanUndo(canUndo);
      setCanRedo(canRedo);
    });

    canvasManager.layers.on('layer:created', updateLayers);
    canvasManager.layers.on('layer:deleted', updateLayers);
    canvasManager.layers.on('layer:visibility:changed', updateLayers);
    canvasManager.layers.on('layer:lock:changed', updateLayers);

    // Initial layer update
    updateLayers();

    // Cleanup
    return () => {
      canvasManager.dispose();
    };
  }, [initialWidth, initialHeight, onCanvasReady]);

  const updateLayers = useCallback(() => {
    if (!canvasManagerRef.current) return;
    const layerData = canvasManagerRef.current.layers.getLayers();
    setLayers(layerData);
  }, []);

  // Tool handlers
  const handleToolSelect = useCallback((tool: CanvasTool) => {
    setActiveTool(tool);
    
    if (!canvasManagerRef.current) return;

    const canvas = canvasManagerRef.current.canvas;
    
    // Reset canvas state
    canvas.defaultCursor = 'default';
    canvas.hoverCursor = 'move';
    canvas.moveCursor = 'move';
    canvas.selection = true;
    canvas.isDrawingMode = false;

    switch (tool) {
      case 'select':
        canvas.defaultCursor = 'default';
        canvas.selection = true;
        break;
      
      case 'pen':
      case 'brush':
        canvas.isDrawingMode = true;
        canvas.freeDrawingBrush.width = 3;
        canvas.freeDrawingBrush.color = '#000000';
        canvas.selection = false;
        break;
      
      case 'eraser':
        canvas.isDrawingMode = true;
        canvas.freeDrawingBrush = new fabric.EraserBrush(canvas);
        canvas.freeDrawingBrush.width = 10;
        canvas.selection = false;
        break;
      
      default:
        canvas.defaultCursor = 'crosshair';
        canvas.selection = false;
        break;
    }
  }, []);

  // Canvas actions
  const createShape = useCallback((shapeType: CanvasTool) => {
    if (!canvasManagerRef.current) return;

    let shape: fabric.Object;
    const options = {
      left: 100,
      top: 100,
      metadata: { tool: shapeType }
    };

    switch (shapeType) {
      case 'rectangle':
        shape = Shapes.rect(options);
        break;
      case 'circle':
        shape = Shapes.circle(options);
        break;
      case 'triangle':
        shape = Shapes.triangle(options);
        break;
      case 'star':
        shape = Shapes.star({ ...options, points: 5, innerRadius: 20, outerRadius: 40 });
        break;
      case 'arrow':
        shape = Shapes.arrow(options);
        break;
      case 'line':
        shape = Shapes.line(100, 100, 200, 100, options);
        break;
      case 'text':
        shape = Text.text('Text', options);
        break;
      case 'textbox':
        shape = Text.textbox('Text Box', { ...options, width: 200 });
        break;
      default:
        return;
    }

    canvasManagerRef.current.addObject(shape);
    setActiveTool('select');
  }, []);

  // History actions
  const handleUndo = useCallback(() => {
    canvasManagerRef.current?.history.undo();
  }, []);

  const handleRedo = useCallback(() => {
    canvasManagerRef.current?.history.redo();
  }, []);

  // Zoom actions
  const handleZoomIn = useCallback(() => {
    if (!canvasManagerRef.current) return;
    const canvas = canvasManagerRef.current.canvas;
    const currentZoom = canvas.getZoom();
    const newZoom = Math.min(currentZoom * 1.2, 5);
    canvas.setZoom(newZoom);
    setZoom(Math.round(newZoom * 100));
  }, []);

  const handleZoomOut = useCallback(() => {
    if (!canvasManagerRef.current) return;
    const canvas = canvasManagerRef.current.canvas;
    const currentZoom = canvas.getZoom();
    const newZoom = Math.max(currentZoom / 1.2, 0.1);
    canvas.setZoom(newZoom);
    setZoom(Math.round(newZoom * 100));
  }, []);

  const handleZoomFit = useCallback(() => {
    canvasManagerRef.current?.zoomToFit();
    const canvas = canvasManagerRef.current?.canvas;
    if (canvas) {
      setZoom(Math.round(canvas.getZoom() * 100));
    }
  }, []);

  // Layer actions
  const handleLayerVisibilityToggle = useCallback((layerId: string) => {
    if (!canvasManagerRef.current) return;
    const layer = layers.find(l => l.id === layerId);
    if (layer) {
      canvasManagerRef.current.layers.setLayerVisibility(layerId, !layer.visible);
    }
  }, [layers]);

  const handleLayerLockToggle = useCallback((layerId: string) => {
    if (!canvasManagerRef.current) return;
    const layer = layers.find(l => l.id === layerId);
    if (layer) {
      canvasManagerRef.current.layers.setLayerLocked(layerId, !layer.locked);
    }
  }, [layers]);

  const handleDeleteSelected = useCallback(() => {
    if (!canvasManagerRef.current || selectedObjects.length === 0) return;
    
    selectedObjects.forEach(({ id }) => {
      canvasManagerRef.current!.removeObject(id);
    });
  }, [selectedObjects]);

  const handleDuplicateSelected = useCallback(() => {
    if (!canvasManagerRef.current || selectedObjects.length === 0) return;
    
    selectedObjects.forEach(({ id }) => {
      canvasManagerRef.current!.duplicateObject(id);
    });
  }, [selectedObjects]);

  // File actions
  const handleExportJSON = useCallback(() => {
    if (!canvasManagerRef.current) return;
    const json = canvasManagerRef.current.canvas.toJSON(['id', 'metadata']);
    const blob = new Blob([JSON.stringify(json, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'canvas-design.json';
    link.click();
    URL.revokeObjectURL(url);
  }, []);

  const handleExportImage = useCallback(() => {
    if (!canvasManagerRef.current) return;
    const dataURL = canvasManagerRef.current.canvas.toDataURL({
      format: 'png',
      quality: 1,
      multiplier: 2
    });
    const link = document.createElement('a');
    link.href = dataURL;
    link.download = 'canvas-design.png';
    link.click();
  }, []);

  return (
    <div className={cn("flex h-screen bg-gray-50", className)}>
      {/* Left Sidebar - Tools */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Design Tools</h2>
        </div>
        
        <ScrollArea className="flex-1">
          <div className="p-4 space-y-6">
            {/* Basic Tools */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Selection</h3>
              <div className="grid grid-cols-2 gap-2">
                <ToolButton
                  tool="select"
                  activeTool={activeTool}
                  onToolSelect={handleToolSelect}
                  icon={<MousePointer size={16} />}
                  label="Select"
                />
                <ToolButton
                  tool="pen"
                  activeTool={activeTool}
                  onToolSelect={handleToolSelect}
                  icon={<Pen size={16} />}
                  label="Pen"
                />
              </div>
            </div>

            {/* Shape Tools */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Shapes</h3>
              <div className="grid grid-cols-2 gap-2">
                <ToolButton
                  tool="rectangle"
                  activeTool={activeTool}
                  onToolSelect={() => createShape('rectangle')}
                  icon={<Square size={16} />}
                  label="Rectangle"
                />
                <ToolButton
                  tool="circle"
                  activeTool={activeTool}
                  onToolSelect={() => createShape('circle')}
                  icon={<Circle size={16} />}
                  label="Circle"
                />
                <ToolButton
                  tool="triangle"
                  activeTool={activeTool}
                  onToolSelect={() => createShape('triangle')}
                  icon={<Triangle size={16} />}
                  label="Triangle"
                />
                <ToolButton
                  tool="star"
                  activeTool={activeTool}
                  onToolSelect={() => createShape('star')}
                  icon={<Star size={16} />}
                  label="Star"
                />
                <ToolButton
                  tool="arrow"
                  activeTool={activeTool}
                  onToolSelect={() => createShape('arrow')}
                  icon={<ArrowRight size={16} />}
                  label="Arrow"
                />
              </div>
            </div>

            {/* Text Tools */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Text</h3>
              <div className="grid grid-cols-2 gap-2">
                <ToolButton
                  tool="text"
                  activeTool={activeTool}
                  onToolSelect={() => createShape('text')}
                  icon={<Type size={16} />}
                  label="Text"
                />
                <ToolButton
                  tool="textbox"
                  activeTool={activeTool}
                  onToolSelect={() => createShape('textbox')}
                  icon={<Type size={16} />}
                  label="Text Box"
                />
              </div>
            </div>

            {/* Drawing Tools */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Drawing</h3>
              <div className="grid grid-cols-2 gap-2">
                <ToolButton
                  tool="brush"
                  activeTool={activeTool}
                  onToolSelect={handleToolSelect}
                  icon={<Paintbrush size={16} />}
                  label="Brush"
                />
                <ToolButton
                  tool="eraser"
                  activeTool={activeTool}
                  onToolSelect={handleToolSelect}
                  icon={<Eraser size={16} />}
                  label="Eraser"
                />
              </div>
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Toolbar */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* History Controls */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleUndo}
                disabled={!canUndo}
                title="Undo"
              >
                <Undo2 size={16} />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRedo}
                disabled={!canRedo}
                title="Redo"
              >
                <Redo2 size={16} />
              </Button>

              <Separator orientation="vertical" className="h-6" />

              {/* Zoom Controls */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomOut}
                title="Zoom Out"
              >
                <ZoomOut size={16} />
              </Button>
              <div className="px-3 py-1 bg-gray-100 rounded text-sm font-medium min-w-[60px] text-center">
                {zoom}%
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomIn}
                title="Zoom In"
              >
                <ZoomIn size={16} />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomFit}
                title="Zoom to Fit"
              >
                Fit
              </Button>

              <Separator orientation="vertical" className="h-6" />

              {/* Object Actions */}
              {selectedObjects.length > 0 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDuplicateSelected}
                    title="Duplicate"
                  >
                    <Copy size={16} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeleteSelected}
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </Button>
                </>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* Canvas Options */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSnapEnabled(!snapEnabled)}
                title="Toggle Snap"
                className={snapEnabled ? "bg-blue-50 border-blue-200" : ""}
              >
                Snap
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setGridEnabled(!gridEnabled)}
                title="Toggle Grid"
                className={gridEnabled ? "bg-blue-50 border-blue-200" : ""}
              >
                <Grid size={16} />
              </Button>

              <Separator orientation="vertical" className="h-6" />

              {/* File Actions */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportJSON}
                title="Export JSON"
              >
                <Save size={16} />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportImage}
                title="Export Image"
              >
                <Download size={16} />
              </Button>
            </div>
          </div>
        </div>

        {/* Canvas Container */}
        <div className="flex-1 relative overflow-hidden bg-gray-100">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-white shadow-lg rounded-lg overflow-hidden">
              <canvas
                ref={canvasRef}
                className="block"
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%'
                }}
              />
            </div>
          </div>
        </div>

        {/* Status Bar */}
        <div className="bg-white border-t border-gray-200 px-4 py-2">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>Canvas: {canvasSize.width} × {canvasSize.height}</span>
              <span>Objects: {selectedObjects.length} selected</span>
              {isCanvasReady && <Badge variant="secondary">Ready</Badge>}
            </div>
            <div className="flex items-center gap-2">
              <span>Tool: {activeTool}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Sidebar - Properties & Layers */}
      <div className="w-80 bg-white border-l border-gray-200">
        <Tabs defaultValue="layers" className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-2 m-4 mb-0">
            <TabsTrigger value="layers">Layers</TabsTrigger>
            <TabsTrigger value="properties">Properties</TabsTrigger>
          </TabsList>

          <TabsContent value="layers" className="flex-1 m-4 mt-4">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Layers</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 p-4 pt-0">
                <ScrollArea className="h-64">
                  {layers.map((layer) => (
                    <div
                      key={layer.id}
                      className="flex items-center justify-between p-2 rounded hover:bg-gray-50"
                    >
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleLayerVisibilityToggle(layer.id)}
                          className="p-1 h-auto"
                        >
                          {layer.visible ? <Eye size={14} /> : <EyeOff size={14} />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleLayerLockToggle(layer.id)}
                          className="p-1 h-auto"
                        >
                          {layer.locked ? <Lock size={14} /> : <Unlock size={14} />}
                        </Button>
                        <span className="text-sm">{layer.name}</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {layer.objectCount}
                      </Badge>
                    </div>
                  ))}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="properties" className="flex-1 m-4 mt-4">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Properties</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-4 pt-0">
                {selectedObjects.length === 0 ? (
                  <p className="text-sm text-gray-500">No objects selected</p>
                ) : (
                  <ScrollArea className="h-64">
                    {selectedObjects.map((obj) => (
                      <div key={obj.id} className="space-y-3 border-b border-gray-100 pb-3 mb-3">
                        <div>
                          <div className="text-sm font-medium">{obj.type}</div>
                          <div className="text-xs text-gray-500">{obj.id}</div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <label className="text-gray-600">X</label>
                            <div className="font-mono">{Math.round(obj.properties.left)}</div>
                          </div>
                          <div>
                            <label className="text-gray-600">Y</label>
                            <div className="font-mono">{Math.round(obj.properties.top)}</div>
                          </div>
                          <div>
                            <label className="text-gray-600">W</label>
                            <div className="font-mono">{Math.round(obj.properties.width)}</div>
                          </div>
                          <div>
                            <label className="text-gray-600">H</label>
                            <div className="font-mono">{Math.round(obj.properties.height)}</div>
                          </div>
                        </div>

                        <div>
                          <label className="text-xs text-gray-600">Angle</label>
                          <div className="text-sm font-mono">{Math.round(obj.properties.angle)}°</div>
                        </div>

                        <div>
                          <label className="text-xs text-gray-600">Opacity</label>
                          <div className="text-sm font-mono">{Math.round(obj.properties.opacity * 100)}%</div>
                        </div>
                      </div>
                    ))}
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}