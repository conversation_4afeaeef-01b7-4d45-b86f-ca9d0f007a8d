// apps/web/src/components/design-studio/component-library-demo.tsx
// Component Library Demo - Comprehensive demonstration of the component library system

"use client";

import React, { useState, useEffect, useRef } from 'react';
import { fabric } from 'fabric';
import { 
  Play, 
  Square, 
  Circle, 
  Triangle, 
  Type, 
  Image, 
  Layers, 
  Package, 
  Palette, 
  Sparkles, 
  Wand2, 
  Download,
  Upload,
  RefreshCw,
  Settings,
  Info,
  CheckCircle,
  AlertCircle,
  Clock,
  TrendingUp,
  Users,
  Star,
  Eye,
  Heart,
  Share2,
  Trash2,
  Edit,
  Copy,
  MoreHorizontal,
  Search,
  Filter,
  Grid,
  List,
  Plus,
  Minus,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  RotateCw,
  Move,
  MousePointer,
  Hand,
  Crosshair
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import ComponentLibraryPanel from './component-library-panel';
import { 
  useComponentLibrary, 
  ComponentLibraryEngine, 
  AssetManager,
  DesignComponent,
  DesignAsset,
  AssetType,
  PropertyType,
  ComponentCategory,
  AssetCategory
} from './index';

interface ComponentLibraryDemoProps {
  className?: string;
}

const ComponentLibraryDemo: React.FC<ComponentLibraryDemoProps> = ({ className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [isLibraryOpen, setIsLibraryOpen] = useState(true);
  const [selectedTool, setSelectedTool] = useState<string>('select');
  const [demoProgress, setDemoProgress] = useState(0);
  const [isRunningDemo, setIsRunningDemo] = useState(false);
  const [demoStep, setDemoStep] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newComponentName, setNewComponentName] = useState('');
  const [newComponentDescription, setNewComponentDescription] = useState('');
  const [newComponentCategory, setNewComponentCategory] = useState('General');

  // Initialize component library
  const {
    state: libraryState,
    actions: libraryActions,
    libraryEngine,
    assetManager
  } = useComponentLibrary({
    canvas: canvas || undefined,
    enableAutoSave: true,
    enablePerformanceMonitoring: true,
    componentLibraryOptions: {
      maxComponents: 1000,
      enableAI: true,
      enableVersioning: true,
      enableSync: true
    },
    assetManagerOptions: {
      maxFileSize: 10 * 1024 * 1024, // 10MB
      enableOptimization: true,
      enableCDN: false,
      enableAI: true
    }
  });

  // Initialize canvas
  useEffect(() => {
    if (canvasRef.current && !canvas) {
      const fabricCanvas = new fabric.Canvas(canvasRef.current, {
        width: 800,
        height: 600,
        backgroundColor: '#f8f9fa'
      });

      // Add grid background
      const grid = 20;
      for (let i = 0; i < (800 / grid); i++) {
        fabricCanvas.add(new fabric.Line([i * grid, 0, i * grid, 600], {
          stroke: '#e0e0e0',
          strokeWidth: 1,
          selectable: false,
          evented: false
        }));
      }
      for (let i = 0; i < (600 / grid); i++) {
        fabricCanvas.add(new fabric.Line([0, i * grid, 800, i * grid], {
          stroke: '#e0e0e0',
          strokeWidth: 1,
          selectable: false,
          evented: false
        }));
      }

      setCanvas(fabricCanvas);
    }
  }, [canvas]);

  // Generate demo data
  useEffect(() => {
    if (libraryEngine && assetManager && libraryState.components.length === 0) {
      generateDemoData();
    }
  }, [libraryEngine, assetManager, libraryState.components.length]);

  // Generate demo components and assets
  const generateDemoData = async () => {
    if (!canvas || !libraryEngine || !assetManager) return;

    try {
      // Create demo components
      const demoComponents = await createDemoComponents();
      
      // Create demo assets
      const demoAssets = await createDemoAssets();
      
      console.log('Demo data generated:', {
        components: demoComponents.length,
        assets: demoAssets.length
      });
    } catch (error) {
      console.error('Failed to generate demo data:', error);
    }
  };

  // Create demo components
  const createDemoComponents = async (): Promise<DesignComponent[]> => {
    if (!canvas || !libraryEngine) return [];

    const components: DesignComponent[] = [];

    // Button component
    const buttonRect = new fabric.Rect({
      left: 100,
      top: 100,
      width: 120,
      height: 40,
      fill: '#3B82F6',
      rx: 8,
      ry: 8
    });

    const buttonText = new fabric.Text('Button', {
      left: 160,
      top: 120,
      fontSize: 16,
      fill: 'white',
      fontFamily: 'Arial',
      originX: 'center',
      originY: 'center'
    });

    const buttonGroup = new fabric.Group([buttonRect, buttonText], {
      left: 100,
      top: 100
    });

    canvas.add(buttonGroup);
    canvas.setActiveObject(buttonGroup);

    const buttonComponent = await libraryActions.createComponent(buttonGroup, {
      name: 'Primary Button',
      description: 'A primary action button component',
      category: 'Buttons',
      tags: ['button', 'primary', 'action']
    });

    components.push(buttonComponent);

    // Card component
    const cardRect = new fabric.Rect({
      left: 300,
      top: 100,
      width: 200,
      height: 150,
      fill: 'white',
      stroke: '#e0e0e0',
      strokeWidth: 1,
      rx: 12,
      ry: 12
    });

    const cardTitle = new fabric.Text('Card Title', {
      left: 320,
      top: 120,
      fontSize: 18,
      fill: '#1f2937',
      fontFamily: 'Arial',
      fontWeight: 'bold'
    });

    const cardContent = new fabric.Text('Card content goes here...', {
      left: 320,
      top: 150,
      fontSize: 14,
      fill: '#6b7280',
      fontFamily: 'Arial'
    });

    const cardGroup = new fabric.Group([cardRect, cardTitle, cardContent], {
      left: 300,
      top: 100
    });

    canvas.add(cardGroup);
    canvas.setActiveObject(cardGroup);

    const cardComponent = await libraryActions.createComponent(cardGroup, {
      name: 'Content Card',
      description: 'A card component for displaying content',
      category: 'Cards',
      tags: ['card', 'content', 'container']
    });

    components.push(cardComponent);

    // Icon component
    const iconCircle = new fabric.Circle({
      left: 550,
      top: 150,
      radius: 20,
      fill: '#EF4444',
      originX: 'center',
      originY: 'center'
    });

    const iconText = new fabric.Text('!', {
      left: 550,
      top: 150,
      fontSize: 24,
      fill: 'white',
      fontFamily: 'Arial',
      fontWeight: 'bold',
      originX: 'center',
      originY: 'center'
    });

    const iconGroup = new fabric.Group([iconCircle, iconText], {
      left: 550,
      top: 150
    });

    canvas.add(iconGroup);
    canvas.setActiveObject(iconGroup);

    const iconComponent = await libraryActions.createComponent(iconGroup, {
      name: 'Alert Icon',
      description: 'An alert icon component',
      category: 'Icons',
      tags: ['icon', 'alert', 'notification']
    });

    components.push(iconComponent);

    canvas.discardActiveObject();
    canvas.requestRenderAll();

    return components;
  };

  // Create demo assets
  const createDemoAssets = async (): Promise<DesignAsset[]> => {
    if (!assetManager) return [];

    const assets: DesignAsset[] = [];

    // Simulate demo assets (normally these would be actual files)
    const demoAssetData = [
      {
        name: 'Hero Image',
        type: AssetType.IMAGE,
        category: 'Images',
        size: 1024000,
        dimensions: { width: 1200, height: 800, aspectRatio: 1.5 },
        tags: ['hero', 'banner', 'marketing']
      },
      {
        name: 'Icon Set',
        type: AssetType.ICON,
        category: 'Icons',
        size: 52000,
        dimensions: { width: 24, height: 24, aspectRatio: 1 },
        tags: ['icons', 'ui', 'interface']
      },
      {
        name: 'Brand Logo',
        type: AssetType.VECTOR,
        category: 'Brand Assets',
        size: 8000,
        dimensions: { width: 300, height: 100, aspectRatio: 3 },
        tags: ['logo', 'brand', 'identity']
      },
      {
        name: 'Color Palette',
        type: AssetType.COLOR_PALETTE,
        category: 'Colors',
        size: 1000,
        tags: ['colors', 'palette', 'theme']
      },
      {
        name: 'Typography Guide',
        type: AssetType.DOCUMENT,
        category: 'Documents',
        size: 245000,
        tags: ['typography', 'fonts', 'guide']
      }
    ];

    // Create mock assets
    for (const assetData of demoAssetData) {
      const asset: DesignAsset = {
        id: `asset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: assetData.name,
        type: assetData.type,
        category: assetData.category,
        tags: assetData.tags,
        url: `https://example.com/assets/${assetData.name.toLowerCase().replace(/\s+/g, '-')}`,
        thumbnailUrl: `https://example.com/thumbnails/${assetData.name.toLowerCase().replace(/\s+/g, '-')}`,
        mimeType: assetData.type === AssetType.IMAGE ? 'image/jpeg' : 'application/octet-stream',
        size: assetData.size,
        dimensions: assetData.dimensions,
        metadata: {
          source: 'demo',
          format: assetData.type === AssetType.IMAGE ? 'jpeg' : 'svg',
          keywords: assetData.tags
        },
        versions: [],
        usage: {
          totalUsage: Math.floor(Math.random() * 100),
          projects: [],
          components: [],
          lastUsed: new Date(),
          downloads: Math.floor(Math.random() * 50),
          views: Math.floor(Math.random() * 200),
          popularity: Math.floor(Math.random() * 100)
        },
        created: new Date(),
        updated: new Date(),
        author: 'Demo User',
        status: 'active' as any
      };

      assets.push(asset);
    }

    return assets;
  };

  // Handle component creation
  const handleCreateComponent = async () => {
    if (!canvas || !libraryEngine) return;

    const activeObjects = canvas.getActiveObjects();
    if (activeObjects.length === 0) {
      alert('Please select objects to create a component');
      return;
    }

    try {
      const component = await libraryActions.createComponent(activeObjects, {
        name: newComponentName,
        description: newComponentDescription,
        category: newComponentCategory,
        tags: newComponentName.toLowerCase().split(' ')
      });

      console.log('Component created:', component);
      setShowCreateDialog(false);
      setNewComponentName('');
      setNewComponentDescription('');
      setNewComponentCategory('General');
    } catch (error) {
      console.error('Failed to create component:', error);
    }
  };

  // Handle component instantiation
  const handleComponentInstantiate = async (componentId: string, position: { x: number; y: number }) => {
    if (!canvas) return;

    try {
      const instance = await libraryActions.createComponentInstance(componentId, position);
      console.log('Component instance created:', instance);
    } catch (error) {
      console.error('Failed to create component instance:', error);
    }
  };

  // Handle asset upload
  const handleAssetUpload = async (files: FileList) => {
    if (!assetManager) return;

    try {
      const results = await libraryActions.uploadAssets(files, {
        autoOptimize: true,
        generateThumbnail: true,
        extractMetadata: true
      });

      console.log('Assets uploaded:', results);
    } catch (error) {
      console.error('Failed to upload assets:', error);
    }
  };

  // Add drawing tools
  const addRectangle = () => {
    if (!canvas) return;

    const rect = new fabric.Rect({
      left: 200,
      top: 200,
      width: 100,
      height: 100,
      fill: '#FF6B6B',
      stroke: '#FF5252',
      strokeWidth: 2
    });

    canvas.add(rect);
    canvas.setActiveObject(rect);
    canvas.requestRenderAll();
  };

  const addCircle = () => {
    if (!canvas) return;

    const circle = new fabric.Circle({
      left: 300,
      top: 200,
      radius: 50,
      fill: '#4ECDC4',
      stroke: '#26A69A',
      strokeWidth: 2
    });

    canvas.add(circle);
    canvas.setActiveObject(circle);
    canvas.requestRenderAll();
  };

  const addText = () => {
    if (!canvas) return;

    const text = new fabric.Text('Sample Text', {
      left: 400,
      top: 200,
      fontSize: 24,
      fill: '#2C3E50',
      fontFamily: 'Arial'
    });

    canvas.add(text);
    canvas.setActiveObject(text);
    canvas.requestRenderAll();
  };

  // Run demo workflow
  const runDemo = async () => {
    setIsRunningDemo(true);
    setDemoStep(0);
    setDemoProgress(0);

    const steps = [
      'Creating demo components...',
      'Adding assets to library...',
      'Demonstrating component instances...',
      'Showcasing asset management...',
      'Completing demo workflow...'
    ];

    for (let i = 0; i < steps.length; i++) {
      setDemoStep(i);
      setDemoProgress((i / steps.length) * 100);
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    setDemoProgress(100);
    setIsRunningDemo(false);
  };

  // Get demo statistics
  const getDemoStats = () => ({
    components: libraryState.components.length,
    assets: libraryState.assets.length,
    instances: libraryState.instances.length,
    categories: libraryState.categories.length,
    totalUsage: libraryState.components.reduce((sum, comp) => sum + comp.usage.totalInstances, 0),
    totalAssetSize: libraryState.assets.reduce((sum, asset) => sum + asset.size, 0)
  });

  const stats = getDemoStats();

  return (
    <TooltipProvider>
      <div className={`flex flex-col h-screen bg-gray-50 ${className}`}>
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold text-gray-900">Component Library Demo</h1>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                V4.1a Sprint 12
              </Badge>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                onClick={runDemo}
                disabled={isRunningDemo}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isRunningDemo ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Running Demo
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Run Demo
                  </>
                )}
              </Button>
              
              <Button
                onClick={() => setIsLibraryOpen(!isLibraryOpen)}
                variant="outline"
                className="border-gray-300"
              >
                <Layers className="w-4 h-4 mr-2" />
                {isLibraryOpen ? 'Hide' : 'Show'} Library
              </Button>
            </div>
          </div>

          {/* Demo Progress */}
          {isRunningDemo && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3 mb-2">
                <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
                <span className="text-sm text-blue-800">
                  Step {demoStep + 1}/5: Running component library demo...
                </span>
              </div>
              <Progress value={demoProgress} className="h-2" />
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Tools and Stats */}
          <div className="w-64 bg-white border-r border-gray-200 p-4 overflow-y-auto">
            <div className="space-y-6">
              {/* Drawing Tools */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Drawing Tools</h3>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    onClick={addRectangle}
                    variant="outline"
                    size="sm"
                    className="h-12 flex-col"
                  >
                    <Square className="w-4 h-4 mb-1" />
                    Rectangle
                  </Button>
                  <Button
                    onClick={addCircle}
                    variant="outline"
                    size="sm"
                    className="h-12 flex-col"
                  >
                    <Circle className="w-4 h-4 mb-1" />
                    Circle
                  </Button>
                  <Button
                    onClick={addText}
                    variant="outline"
                    size="sm"
                    className="h-12 flex-col"
                  >
                    <Type className="w-4 h-4 mb-1" />
                    Text
                  </Button>
                  <Button
                    onClick={() => setShowCreateDialog(true)}
                    variant="outline"
                    size="sm"
                    className="h-12 flex-col"
                  >
                    <Package className="w-4 h-4 mb-1" />
                    Component
                  </Button>
                </div>
              </div>

              <Separator />

              {/* Library Stats */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Library Statistics</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Package className="w-4 h-4 text-blue-600" />
                      <span className="text-sm text-gray-600">Components</span>
                    </div>
                    <Badge variant="secondary">{stats.components}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Image className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-gray-600">Assets</span>
                    </div>
                    <Badge variant="secondary">{stats.assets}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Layers className="w-4 h-4 text-purple-600" />
                      <span className="text-sm text-gray-600">Instances</span>
                    </div>
                    <Badge variant="secondary">{stats.instances}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-orange-600" />
                      <span className="text-sm text-gray-600">Total Usage</span>
                    </div>
                    <Badge variant="secondary">{stats.totalUsage}</Badge>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Performance Metrics */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Performance</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Memory Usage</span>
                    <span className="text-gray-900">
                      {Math.round(stats.totalAssetSize / 1024 / 1024 * 100) / 100} MB
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Load Time</span>
                    <span className="text-gray-900">< 2s</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Render FPS</span>
                    <span className="text-gray-900">60fps</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Feature Status */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Features</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600">Smart Components</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600">Asset Management</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600">Drag & Drop</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600">Version Control</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600">AI Integration</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Canvas Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 p-4 overflow-hidden">
              <div className="bg-white rounded-lg border border-gray-200 p-4 h-full">
                <canvas
                  ref={canvasRef}
                  className="border border-gray-300 rounded-lg"
                />
              </div>
            </div>

            {/* Canvas Tools */}
            <div className="bg-white border-t border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
                    <Button
                      variant={selectedTool === 'select' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setSelectedTool('select')}
                    >
                      <MousePointer className="w-4 h-4" />
                    </Button>
                    <Button
                      variant={selectedTool === 'move' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setSelectedTool('move')}
                    >
                      <Move className="w-4 h-4" />
                    </Button>
                    <Button
                      variant={selectedTool === 'zoom' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setSelectedTool('zoom')}
                    >
                      <ZoomIn className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  <Separator orientation="vertical" className="h-6" />
                  
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => canvas?.getActiveObjects().forEach(obj => obj.rotate(obj.angle - 90))}
                    >
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => canvas?.getActiveObjects().forEach(obj => obj.rotate(obj.angle + 90))}
                    >
                      <RotateCw className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">
                    {canvas?.getActiveObjects().length || 0} selected
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => canvas?.discardActiveObject()}
                  >
                    Clear Selection
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Component Library Panel */}
        <ComponentLibraryPanel
          isOpen={isLibraryOpen}
          onToggle={() => setIsLibraryOpen(!isLibraryOpen)}
          onComponentCreate={libraryActions.createComponent}
          onComponentInstantiate={handleComponentInstantiate}
          onAssetUpload={handleAssetUpload}
          components={libraryState.components}
          categories={libraryState.categories}
          assets={libraryState.assets}
          selectedComponent={libraryState.selectedComponent}
          selectedAsset={libraryState.selectedAsset}
          onComponentSelect={libraryActions.selectComponent}
          onAssetSelect={libraryActions.selectAsset}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          isLoading={libraryState.isLoading}
          error={libraryState.error}
        />

        {/* Create Component Dialog */}
        {showCreateDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-96">
              <h3 className="text-lg font-semibold mb-4">Create Component</h3>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Component Name</Label>
                  <Input
                    id="name"
                    value={newComponentName}
                    onChange={(e) => setNewComponentName(e.target.value)}
                    placeholder="Enter component name"
                  />
                </div>
                
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newComponentDescription}
                    onChange={(e) => setNewComponentDescription(e.target.value)}
                    placeholder="Enter component description"
                    rows={3}
                  />
                </div>
                
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={newComponentCategory} onValueChange={setNewComponentCategory}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="General">General</SelectItem>
                      <SelectItem value="Buttons">Buttons</SelectItem>
                      <SelectItem value="Cards">Cards</SelectItem>
                      <SelectItem value="Icons">Icons</SelectItem>
                      <SelectItem value="Forms">Forms</SelectItem>
                      <SelectItem value="Layout">Layout</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setShowCreateDialog(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateComponent}
                  disabled={!newComponentName.trim()}
                >
                  Create Component
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Status Messages */}
        {libraryState.error && (
          <div className="fixed bottom-4 right-4 z-50">
            <Alert className="bg-red-50 border-red-200">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {libraryState.error}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {libraryState.isUploading && (
          <div className="fixed bottom-4 left-4 z-50">
            <Alert className="bg-blue-50 border-blue-200">
              <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
              <AlertDescription className="text-blue-800">
                Uploading assets... {libraryState.uploadProgress}%
              </AlertDescription>
            </Alert>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
};

export default ComponentLibraryDemo;