// Layer Management System V1.3a Exports
export { LayerManagementPanel } from './layer-management-panel';
export { default as LayerManagementPanel } from './layer-management-panel';

// Canvas Editor Components
export { CanvasEditor } from './canvas-editor';
export { HybridCanvasEditor } from './hybrid-canvas-editor';

// AI Image Generation V2.1a Exports
export { AIImageGenerator } from './ai-image-generator';
export type { AIImageGeneratorProps } from './ai-image-generator';

// Re-export layer management types and utilities
export type {
  LayerNode,
  LayerType,
  BlendMode,
  LayerEffect,
  LayerTransform,
  LayerMetadata,
  CreateLayerOptions,
  LayerFilterCriteria,
  LayerSearchOptions
} from '../../lib/canvas/advanced-layer-manager';

export { AdvancedLayerManager } from '../../lib/canvas/advanced-layer-manager';
export { LayerHybridIntegration } from '../../lib/canvas/layer-hybrid-integration';

// Re-export hooks
export {
  useLayerManagement,
  useLayerSelection,
  useLayerSearch,
  useLayerEffects,
  useLayerPerformance
} from '../../hooks/use-layer-management';

export type {
  LayerManagerState,
  LayerManagerActions,
  LayerManagerPerformance,
  LayerManagerHookOptions,
  LayerManagerHookReturn
} from '../../hooks/use-layer-management';

// AI Generation hooks
export {
  useAIGeneration,
  useTextToImageGeneration,
  useImageEnhancement,
  useInpainting
} from '../../hooks/use-ai-generation';

export type {
  AIGenerationState,
  AIGenerationActions,
  AIGenerationHookOptions,
  AIGenerationHookReturn
} from '../../hooks/use-ai-generation';

// AI Services
export { EnhancedReplicateService } from '../../lib/ai/replicate-api-service';
export { AICanvasIntegration } from '../../lib/ai/ai-canvas-integration';

export type {
  ReplicateModelConfig,
  GenerationResult,
  AIGenerationRequest,
  WorkflowResult,
  GenerationOptions,
  GenerationProgress,
  ModelAnalytics
} from '../../lib/ai/replicate-api-service';

export type {
  AIImagePlacementOptions,
  CanvasSelectionArea,
  ImageToImageRequest,
  InpaintingRegion,
  StyleTransferRequest,
  AICanvasOperation,
  CanvasAIMetrics
} from '../../lib/ai/ai-canvas-integration';

// Vector Graphics Tools V3.1a Exports
export { VectorToolsPanel } from './vector-tools-panel';
export type { VectorToolsPanelProps } from './vector-tools-panel';

// Vector Graphics Engine
export { 
  VectorGraphicsEngine,
  PathEditor,
  PathOperations
} from '../../lib/canvas/vector-graphics-engine';

export type {
  VectorTool,
  VectorPoint,
  VectorPath,
  VectorShape,
  VectorSelection,
  VectorEditingOptions,
  BezierCurve,
  VectorOperationResult
} from '../../lib/canvas/vector-graphics-engine';

// Shape Library System
export { 
  ShapeLibrary,
  ShapeGenerators,
  SmartShapeCreator
} from '../../lib/canvas/shape-library';

export type {
  ShapeTemplate,
  ShapeCategory,
  ShapeParameter,
  ShapeConstraint,
  ShapeCreationOptions,
  SmartShapeResult,
  ShapeLibraryConfig
} from '../../lib/canvas/shape-library';

// Vector Tools Manager
export { 
  VectorToolsManager,
  VectorToolOperations,
  VectorKeyboardManager
} from '../../lib/canvas/vector-tools-manager';

export type {
  VectorToolConfig,
  ToolOption,
  ToolState,
  ToolWorkflow,
  ToolWorkflowStep
} from '../../lib/canvas/vector-tools-manager';

// Vector Tools React Hooks
export {
  useVectorTools,
  useVectorToolsPerformance,
  useShapeLibrary,
  useVectorHistory,
  useVectorAutoSave,
  usePathEditor,
  useShapeLibraryManager
} from '../../hooks/use-vector-tools';

export type {
  VectorToolsState,
  VectorToolsActions,
  VectorToolsPerformance,
  VectorToolsHookOptions,
  VectorToolsHookReturn
} from '../../hooks/use-vector-tools';

// Canvas State Management V1.4a Exports
export { StateManagementPanel } from './state-management-panel';
export { default as StateManagementPanel } from './state-management-panel';

// Canvas State Management Core
export { 
  CanvasStateManager,
  StateCompressor,
  StateDifferentialEncoder
} from '../../lib/canvas/canvas-state-manager';

export type {
  CanvasState,
  StateSnapshot,
  StateDelta,
  StateBranch,
  StateHistory,
  StateManagerConfig,
  StateOperation
} from '../../lib/canvas/canvas-state-manager';

// Auto-save System
export { 
  AutoSaveManager,
  LocalStorageManager,
  ChangeDetector,
  ProgressIndicator
} from '../../lib/canvas/auto-save-manager';

export type {
  AutoSaveConfig,
  SavedProject,
  RecoverySession,
  AutoSaveStatus,
  SaveProgress,
  ActivityLogEntry,
  CrashInfo
} from '../../lib/canvas/auto-save-manager';

// State Synchronization
export { 
  StateSyncManager,
  OperationalTransform,
  ConflictResolver,
  NetworkManager
} from '../../lib/canvas/state-sync-manager';

export type {
  SyncConfig,
  SyncOperation,
  SyncUser,
  SyncSession,
  ConflictInfo,
  MergeResult,
  NetworkStatus,
  UserPermissions,
  SessionSettings
} from '../../lib/canvas/state-sync-manager';

// State Management React Hooks
export {
  useCanvasState,
  useStateChangeTracking,
  useConflictResolution
} from '../../hooks/use-canvas-state';

export type {
  CanvasStateHookConfig,
  CanvasStateHookReturn,
  StateChangeEvent
} from '../../hooks/use-canvas-state';

// Multi-Provider AI System V2.2a Exports
export { ProviderManagementPanel } from './provider-management-panel';

// Multi-Provider Core Services
export { MultiProviderService } from '../../lib/ai/multi-provider-service';
export { ProviderSelectionEngine } from '../../lib/ai/provider-selection-engine';

export type {
  MultiProviderConfig,
  MultiProviderGenerationRequest,
  MultiProviderGenerationResponse,
  MultiProviderMetrics,
  ProviderBudget,
  BudgetAlert
} from '../../lib/ai/multi-provider-service';

export type {
  ProviderSelectionCriteria,
  SelectionResult,
  TaskType,
  SelectionPriority,
  BudgetConstraints,
  QualityRequirements,
  SpeedRequirements,
  FeatureRequirements
} from '../../lib/ai/provider-selection-engine';

// Provider Adapters
export { BaseProviderAdapter } from '../../lib/ai/providers/provider-base';
export { StabilityAIAdapter } from '../../lib/ai/providers/stability-ai-adapter';
export { DALLEAdapter } from '../../lib/ai/providers/dalle-adapter';
export { MidjourneyAdapter } from '../../lib/ai/providers/midjourney-adapter';

export type {
  AIImageProvider,
  AIImageModel,
  GenerationRequest,
  GenerationResponse,
  ProviderCapabilities,
  ModelCapabilities,
  ProviderUsageStats
} from '../../lib/ai/providers/provider-base';

// Multi-Provider React Hooks
export {
  useMultiProviderAI,
  useProviderSelection,
  useBudgetMonitoring
} from '../../hooks/use-multi-provider-ai';

export type {
  MultiProviderAIState,
  MultiProviderAIActions,
  MultiProviderAIHookOptions,
  MultiProviderAIHookReturn
} from '../../hooks/use-multi-provider-ai';

// Filter & Effects Pipeline V3.2a Exports
export { FilterEffectsPanel } from './filter-effects-panel';
export { default as FilterEffectsPanel } from './filter-effects-panel';

// Filter Pipeline Engine
export { 
  FilterPipelineEngine,
  WebGLResourceManager,
  ShaderManager
} from '../../lib/canvas/filter-pipeline-engine';

export type {
  WebGLFilterContext,
  FilterShader,
  FilterUniform,
  FilterAttribute,
  FilterCategory,
  FilterInstance,
  FilterBounds,
  FilterMask,
  FilterKeyframe,
  BlendMode as FilterBlendMode,
  FilterPipelineConfig,
  FilterPerformanceMetrics,
  FilterProcessingOptions
} from '../../lib/canvas/filter-pipeline-engine';

// Filter Library
export { FilterLibrary } from '../../lib/canvas/filter-library';

export type {
  FilterPreset,
  FilterGroup
} from '../../lib/canvas/filter-library';

// Effects Manager
export { 
  EffectsManager,
  EffectAnimationController
} from '../../lib/canvas/effects-manager';

export type {
  EffectLayer,
  EffectMask,
  MaskData,
  EffectAnimation,
  EffectKeyframe,
  EffectBounds,
  EffectGroup,
  EffectPreset,
  EffectStackState,
  EffectProcessingOptions,
  EffectPerformanceMetrics
} from '../../lib/canvas/effects-manager';

// Filter & Effects React Hooks
export {
  useFiltersEffects,
  useFilterAnimation,
  useFilterPresets
} from '../../hooks/use-filters-effects';

export type {
  FiltersEffectsHookConfig,
  FiltersEffectsHookReturn,
  FilterUpdateEvent
} from '../../hooks/use-filters-effects';

// Style Transfer System V2.3a Exports
export { StyleTransferPanel } from './style-transfer-panel';
export type { StyleTransferPanelProps } from './style-transfer-panel';

// Style Transfer Engine
export { AdvancedStyleTransferService } from '../../lib/ai/style-transfer-engine';

export type {
  StyleModel,
  StyleCapability,
  ModelPerformance,
  StyleReference,
  StyleMood,
  StyleParameters,
  BlendMode,
  StyleTransferRequest,
  TransferOptions,
  StyleTransferResult,
  ContentAnalysis,
  StyleAnalysis,
  QualityMetrics,
  StylePreviewResult,
  StyleFeatures,
  ColorFeatures,
  TextureFeatures,
  CompositionFeatures,
  SemanticFeatures,
  StyleSimilarityResult
} from '../../lib/ai/style-transfer-engine';

// Style Library Manager
export { StyleLibraryManager } from '../../lib/ai/style-library-manager';

export type {
  StyleLibraryEntry,
  StyleThumbnails,
  StyleRating,
  StyleSearchQuery,
  StyleSearchResult,
  StyleCollection,
  ExtractedStyle,
  StyleAnalytics
} from '../../lib/ai/style-library-manager';

// Style Analysis Service
export { StyleAnalysisService } from '../../lib/ai/style-analysis-service';

export type {
  ImageStyleAnalysis,
  StyleClassification,
  ArtisticStyle,
  StyleCompatibility,
  ImageQualityMetrics,
  StyleMatchingResult,
  StyleRecommendation,
  RecommendedAction,
  ColorHarmonyAnalysis,
  TextureAnalysisResult,
  CompositionAnalysisResult
} from '../../lib/ai/style-analysis-service';

// Style Transfer React Hooks
export {
  useStyleTransfer
} from '../../hooks/use-style-transfer';

export type {
  StyleTransferState,
  StyleTransferActions,
  StyleTransferHookOptions,
  StyleTransferHookReturn
} from '../../hooks/use-style-transfer';

// Typography System V3.3a Exports
export { TypographyPanel } from './typography-panel';
export { default as TypographyPanel } from './typography-panel';

// Typography Engine
export { TypographyEngine } from '../../lib/canvas/typography-engine';

export type {
  TypographySettings,
  TypographyObject,
  TypographyMeasurement,
  TypographyPerformanceMetrics,
  TypographyEngineConfig,
  TextRange,
  TextPath,
  TextBox,
  FontMetrics
} from '../../lib/canvas/typography-engine';

// Font Library Manager
export { FontLibraryManager } from '../../lib/canvas/font-library-manager';

export type {
  FontFamily,
  FontVariant,
  FontMetrics as FontMetricsType,
  FontLicense,
  FontClassification,
  FontCategory,
  FontLoadingStatus,
  FontSource,
  FontFilter,
  FontCollection,
  FontPreview,
  FontLoadingOptions,
  FontLibraryConfig,
  FontLibraryStats
} from '../../lib/canvas/font-library-manager';

// Text Effects Manager
export { TextEffectManager } from '../../lib/canvas/text-effects-manager';

export type {
  TextEffect,
  TextEffectType,
  TextEffectCategory,
  TextEffectParameters,
  TextEffectPreset,
  TextEffectInstance,
  TextEffectGroup,
  TextEffectAnimation,
  TextEffectRenderOptions,
  TextEffectPerformance,
  BlendMode as TextBlendMode,
  GradientStop,
  AnimationKeyframe
} from '../../lib/canvas/text-effects-manager';

// Typography React Hooks
export {
  useTypography,
  useTypographyPerformance,
  useFontLibrary,
  useTextEffects,
  useTypographyAutoSave,
  useTypographyKeyboard,
  useTypographyHistory,
  useTypographyCollaborative
} from '../../hooks/use-typography';

export type {
  TypographyState,
  TypographyActions,
  TypographyHookOptions,
  TypographyHookReturn
} from '../../hooks/use-typography';

// Export & Optimization System V3.4a Exports
export { ExportPanel } from './export-panel';
export type { ExportPanelProps } from './export-panel';

// Export Engine
export { ExportEngine } from '../../lib/export/export-engine';

export type {
  ExportFormat,
  ExportOptions,
  ExportQuality,
  ExportResult,
  ExportProgress,
  ExportHistoryEntry,
  ExportPerformanceMetrics,
  BatchExportConfig,
  ColorSpace
} from '../../lib/export/export-engine';

// Optimization Pipeline
export { OptimizationPipeline } from '../../lib/export/optimization-pipeline';

export type {
  OptimizationStrategy,
  OptimizationConfig,
  OptimizationResult,
  OptimizationMetrics,
  QualityAssessment,
  AdaptiveQualityConfig,
  ProgressiveLoadingConfig,
  CompressionAlgorithm
} from '../../lib/export/optimization-pipeline';

// Export & Optimization React Hooks
export {
  useExportOptimization,
  useExportTemplates,
  useExportQueue
} from '../../hooks/use-export-optimization';

export type {
  ExportOptimizationConfig,
  ExportOptimizationHookReturn,
  ExportState,
  PreviewData,
  BatchExportState,
  PerformanceMonitor,
  QualityMonitor
} from '../../hooks/use-export-optimization';

// Component Library System V4.1a Exports
export { default as ComponentLibraryPanel } from './component-library-panel';
export type { ComponentLibraryPanelProps } from './component-library-panel';

// Component Library Engine
export { ComponentLibraryEngine } from '../../lib/design-system/component-library-engine';
export { AssetManager } from '../../lib/design-system/asset-manager';

export type {
  DesignComponent,
  ComponentVariant,
  ComponentProperty,
  ComponentOverride,
  ComponentInstance,
  ComponentCategory,
  ComponentUsage,
  ComponentMetadata,
  ComponentCreationOptions,
  InstanceCreationOptions,
  ComponentSearchFilters,
  ComponentSearchResult,
  ComponentSuggestionContext,
  ComponentStyle,
  ComponentAnalytics,
  ComponentLibraryExport,
  ComponentLibraryImport,
  ImportOptions,
  ComponentLibraryOptions,
  PropertyType,
  OverrideType,
  Point,
  CanvasState
} from '../../lib/design-system/component-library-engine';

export type {
  DesignAsset,
  AssetType,
  AssetStatus,
  AssetDimensions,
  AssetMetadata,
  AssetOptimization,
  AssetFormat,
  ResponsiveAsset,
  AssetVersion,
  AssetUsage,
  AssetLicense,
  AssetCategory,
  AssetCollection,
  AssetSearchFilters,
  AssetSearchResult,
  AssetUploadOptions,
  AssetUploadResult,
  AssetBatchOperation,
  AssetBatchResult,
  AssetManagerOptions,
  AssetSyncOptions,
  LicenseType,
  CollectionVisibility,
  BatchOperationType,
  SyncType
} from '../../lib/design-system/asset-manager';

// Component Library React Hooks
export {
  useComponentLibrary,
  default as useComponentLibrary
} from '../../hooks/use-component-library';

export type {
  UseComponentLibraryOptions,
  ComponentLibraryState,
  ComponentLibraryActions,
  UseComponentLibraryReturn
} from '../../hooks/use-component-library';

// Image Enhancement System V2.4a Exports
export { default as ImageEnhancementPanel } from './image-enhancement-panel';
export type { ImageEnhancementPanelProps } from './image-enhancement-panel';

// Image Enhancement Engine
export { ImageEnhancementEngine, createImageEnhancementEngine } from '../../lib/ai/image-enhancement-engine';

export type {
  EnhancementModel,
  EnhancementRequest,
  EnhancementResult,
  EnhancementParameters,
  EnhancementOptions,
  QualityTarget,
  QualityImprovement,
  EnhancementMetadata,
  ProcessingStep,
  QualityAssessment,
  QualityIssue,
  EnhancementRecommendation,
  EnhancementCapability,
  ModelParameter,
  EnhancementPerformance,
  QualityMetrics,
  EnhancementType,
  EnhancementEngineConfig
} from '../../lib/ai/image-enhancement-engine';

// Enhancement Pipeline Manager
export { EnhancementPipelineManager } from '../../lib/ai/enhancement-pipeline-manager';

export type {
  PipelineStage,
  PipelineConfig,
  PipelineExecution,
  PipelineStageResult,
  PipelinePreset,
  PipelineMetrics,
  QualityGate
} from '../../lib/ai/enhancement-pipeline-manager';

// Image Enhancement React Hooks
export {
  useImageEnhancement,
  default as useImageEnhancement
} from '../../hooks/use-image-enhancement';

export type {
  UseImageEnhancementOptions,
  EnhancementHistory,
  EnhancementSession,
  PerformanceMetrics,
  UseImageEnhancementReturn
} from '../../hooks/use-image-enhancement';