import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { FilterShader, FilterCategory } from '../../lib/canvas/filter-library';
import { EffectLayer, EffectGroup } from '../../lib/canvas/effects-manager';
import { FilterInstance } from '../../lib/canvas/filter-pipeline-engine';
import { useFiltersEffects } from '../../hooks/use-filters-effects';
import { EnhancedHybridCanvasManager } from '../../lib/canvas/enhanced-hybrid-canvas';

// UI Components (you would import these from your UI library)
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '../ui/tabs';
import { <PERSON>lider } from '../ui/slider';
import { Switch } from '../ui/switch';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';

interface FilterEffectsPanelProps {
  canvasManager: EnhancedHybridCanvasManager | null;
  className?: string;
  onFilterApplied?: (layerId: string, filterId: string) => void;
  onLayerCreated?: (layer: EffectLayer) => void;
  onPresetSaved?: (presetId: string) => void;
}

interface FilterParameterControlProps {
  name: string;
  uniform: any;
  value: any;
  onChange: (value: any) => void;
  disabled?: boolean;
}

interface LayerItemProps {
  layer: EffectLayer;
  isSelected: boolean;
  onSelect: () => void;
  onToggleEnabled: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onRename: (name: string) => void;
}

interface FilterItemProps {
  filter: FilterInstance;
  shader: FilterShader;
  onUpdate: (parameters: Record<string, any>) => void;
  onRemove: () => void;
  onToggleEnabled: () => void;
  expanded: boolean;
  onToggleExpanded: () => void;
}

// Filter Parameter Control Component
const FilterParameterControl: React.FC<FilterParameterControlProps> = ({
  name,
  uniform,
  value,
  onChange,
  disabled = false
}) => {
  const handleChange = useCallback((newValue: any) => {
    if (!disabled) {
      onChange(newValue);
    }
  }, [onChange, disabled]);

  if (uniform.type === 'float') {
    return (
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label className="text-sm font-medium">{uniform.description || name}</Label>
          <span className="text-xs text-muted-foreground">
            {typeof value === 'number' ? value.toFixed(3) : value}
          </span>
        </div>
        <Slider
          value={[value]}
          onValueChange={([val]) => handleChange(val)}
          min={uniform.min || 0}
          max={uniform.max || 1}
          step={uniform.step || 0.01}
          disabled={disabled}
          className="w-full"
        />
      </div>
    );
  }

  if (uniform.type === 'vec2') {
    return (
      <div className="space-y-2">
        <Label className="text-sm font-medium">{uniform.description || name}</Label>
        <div className="grid grid-cols-2 gap-2">
          <Input
            type="number"
            value={value[0] || 0}
            onChange={(e) => handleChange([parseFloat(e.target.value), value[1] || 0])}
            disabled={disabled}
            className="text-xs"
            placeholder="X"
          />
          <Input
            type="number"
            value={value[1] || 0}
            onChange={(e) => handleChange([value[0] || 0, parseFloat(e.target.value)])}
            disabled={disabled}
            className="text-xs"
            placeholder="Y"
          />
        </div>
      </div>
    );
  }

  if (uniform.type === 'vec3') {
    return (
      <div className="space-y-2">
        <Label className="text-sm font-medium">{uniform.description || name}</Label>
        <div className="grid grid-cols-3 gap-1">
          <Input
            type="number"
            value={value[0] || 0}
            onChange={(e) => handleChange([parseFloat(e.target.value), value[1] || 0, value[2] || 0])}
            disabled={disabled}
            className="text-xs"
            placeholder="R"
          />
          <Input
            type="number"
            value={value[1] || 0}
            onChange={(e) => handleChange([value[0] || 0, parseFloat(e.target.value), value[2] || 0])}
            disabled={disabled}
            className="text-xs"
            placeholder="G"
          />
          <Input
            type="number"
            value={value[2] || 0}
            onChange={(e) => handleChange([value[0] || 0, value[1] || 0, parseFloat(e.target.value)])}
            disabled={disabled}
            className="text-xs"
            placeholder="B"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{uniform.description || name}</Label>
      <Input
        type="number"
        value={value}
        onChange={(e) => handleChange(parseFloat(e.target.value))}
        disabled={disabled}
        className="text-xs"
      />
    </div>
  );
};

// Filter Item Component
const FilterItem: React.FC<FilterItemProps> = ({
  filter,
  shader,
  onUpdate,
  onRemove,
  onToggleEnabled,
  expanded,
  onToggleExpanded
}) => {
  const handleParameterChange = useCallback((paramName: string, value: any) => {
    onUpdate({ [paramName]: value });
  }, [onUpdate]);

  return (
    <Card className="p-3 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Switch
            checked={filter.enabled}
            onCheckedChange={onToggleEnabled}
            size="sm"
          />
          <span className="font-medium text-sm">{filter.name}</span>
          <Badge variant="secondary" className="text-xs">
            {shader.category}
          </Badge>
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleExpanded}
            className="h-6 w-6 p-0"
          >
            {expanded ? '−' : '+'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
          >
            ×
          </Button>
        </div>
      </div>

      {expanded && (
        <div className="space-y-3 pt-2 border-t">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Opacity</Label>
            <Slider
              value={[filter.opacity]}
              onValueChange={([val]) => onUpdate({ opacity: val })}
              min={0}
              max={1}
              step={0.01}
              className="w-full"
            />
          </div>

          {Object.entries(shader.uniforms).map(([name, uniform]) => {
            if (name === 'u_opacity') return null; // Skip opacity as it's handled above
            
            const paramName = name.startsWith('u_') ? name.slice(2) : name;
            const value = filter.parameters[name] || uniform.value;

            return (
              <FilterParameterControl
                key={name}
                name={paramName}
                uniform={uniform}
                value={value}
                onChange={(newValue) => handleParameterChange(name, newValue)}
                disabled={!filter.enabled}
              />
            );
          })}
        </div>
      )}
    </Card>
  );
};

// Layer Item Component
const LayerItem: React.FC<LayerItemProps> = ({
  layer,
  isSelected,
  onSelect,
  onToggleEnabled,
  onDelete,
  onDuplicate,
  onRename
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(layer.name);

  const handleRename = useCallback(() => {
    if (editName.trim() !== layer.name) {
      onRename(editName.trim());
    }
    setIsEditing(false);
  }, [editName, layer.name, onRename]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setEditName(layer.name);
      setIsEditing(false);
    }
  }, [handleRename, layer.name]);

  return (
    <Card className={`p-3 cursor-pointer transition-colors ${
      isSelected ? 'ring-2 ring-primary' : ''
    }`}>
      <div className="flex items-center justify-between" onClick={onSelect}>
        <div className="flex items-center space-x-2 flex-1">
          <Switch
            checked={layer.enabled}
            onCheckedChange={onToggleEnabled}
            size="sm"
            onClick={(e) => e.stopPropagation()}
          />
          
          {isEditing ? (
            <Input
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleRename}
              onKeyDown={handleKeyDown}
              className="h-6 text-sm"
              autoFocus
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <span 
              className="font-medium text-sm flex-1"
              onDoubleClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}
            >
              {layer.name}
            </span>
          )}
          
          <Badge variant="outline" className="text-xs">
            {layer.filters.length} filters
          </Badge>
        </div>

        <div className="flex items-center space-x-1">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onDuplicate();
                }}
                className="h-6 w-6 p-0"
              >
                📋
              </Button>
            </TooltipTrigger>
            <TooltipContent>Duplicate Layer</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                className="h-6 w-6 p-0 text-destructive hover:text-destructive"
              >
                🗑
              </Button>
            </TooltipTrigger>
            <TooltipContent>Delete Layer</TooltipContent>
          </Tooltip>
        </div>
      </div>

      <div className="mt-2 flex items-center justify-between text-xs text-muted-foreground">
        <span>Opacity: {Math.round(layer.opacity * 100)}%</span>
        <span>{layer.blendMode}</span>
      </div>
    </Card>
  );
};

// Main Filter Effects Panel Component
export const FilterEffectsPanel: React.FC<FilterEffectsPanelProps> = ({
  canvasManager,
  className,
  onFilterApplied,
  onLayerCreated,
  onPresetSaved
}) => {
  const filtersEffects = useFiltersEffects(canvasManager);
  const [selectedLayerId, setSelectedLayerId] = useState<string | null>(null);
  const [expandedFilters, setExpandedFilters] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<FilterCategory | 'all'>('all');
  const [activeTab, setActiveTab] = useState<'layers' | 'filters' | 'presets'>('layers');

  const {
    availableFilters,
    filtersByCategory,
    activeLayers,
    effectGroups,
    createLayer,
    deleteLayer,
    updateLayer,
    duplicateLayer,
    addFilter,
    removeFilter,
    updateFilterParameters,
    performanceMetrics,
    isInitialized,
    error,
    filterLibrary
  } = filtersEffects;

  // Filtered available filters based on search and category
  const filteredFilters = useMemo(() => {
    let filters = selectedCategory === 'all' 
      ? availableFilters 
      : filtersByCategory[selectedCategory] || [];

    if (searchTerm) {
      filters = filters.filter(filter =>
        filter.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        filter.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filters;
  }, [availableFilters, filtersByCategory, selectedCategory, searchTerm]);

  // Get selected layer
  const selectedLayer = useMemo(() => {
    return selectedLayerId ? activeLayers.find(l => l.id === selectedLayerId) : null;
  }, [selectedLayerId, activeLayers]);

  // Event handlers
  const handleCreateLayer = useCallback(() => {
    const layer = createLayer(`Layer ${activeLayers.length + 1}`);
    setSelectedLayerId(layer.id);
    onLayerCreated?.(layer);
  }, [createLayer, activeLayers.length, onLayerCreated]);

  const handleSelectLayer = useCallback((layerId: string) => {
    setSelectedLayerId(layerId);
  }, []);

  const handleToggleLayerEnabled = useCallback((layerId: string) => {
    const layer = activeLayers.find(l => l.id === layerId);
    if (layer) {
      updateLayer(layerId, { enabled: !layer.enabled });
    }
  }, [activeLayers, updateLayer]);

  const handleDeleteLayer = useCallback((layerId: string) => {
    if (deleteLayer(layerId)) {
      if (selectedLayerId === layerId) {
        setSelectedLayerId(null);
      }
    }
  }, [deleteLayer, selectedLayerId]);

  const handleDuplicateLayer = useCallback((layerId: string) => {
    const newLayer = duplicateLayer(layerId);
    if (newLayer) {
      setSelectedLayerId(newLayer.id);
    }
  }, [duplicateLayer]);

  const handleRenameLayer = useCallback((layerId: string, name: string) => {
    updateLayer(layerId, { name });
  }, [updateLayer]);

  const handleAddFilter = useCallback((filterId: string) => {
    if (!selectedLayerId) {
      // Create a new layer if none selected
      const layer = createLayer('New Layer');
      setSelectedLayerId(layer.id);
      const filterInstance = addFilter(layer.id, filterId);
      if (filterInstance) {
        onFilterApplied?.(layer.id, filterId);
      }
    } else {
      const filterInstance = addFilter(selectedLayerId, filterId);
      if (filterInstance) {
        onFilterApplied?.(selectedLayerId, filterId);
      }
    }
  }, [selectedLayerId, createLayer, addFilter, onFilterApplied]);

  const handleRemoveFilter = useCallback((filterId: string) => {
    if (selectedLayerId) {
      removeFilter(selectedLayerId, filterId);
    }
  }, [selectedLayerId, removeFilter]);

  const handleUpdateFilter = useCallback((filterId: string, parameters: Record<string, any>) => {
    if (selectedLayerId) {
      updateFilterParameters(selectedLayerId, filterId, parameters);
    }
  }, [selectedLayerId, updateFilterParameters]);

  const handleToggleFilterExpanded = useCallback((filterId: string) => {
    setExpandedFilters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(filterId)) {
        newSet.delete(filterId);
      } else {
        newSet.add(filterId);
      }
      return newSet;
    });
  }, []);

  const handleToggleFilterEnabled = useCallback((filterId: string) => {
    if (selectedLayerId && selectedLayer) {
      const filter = selectedLayer.filters.find(f => f.id === filterId);
      if (filter) {
        handleUpdateFilter(filterId, { enabled: !filter.enabled });
      }
    }
  }, [selectedLayerId, selectedLayer, handleUpdateFilter]);

  // Loading and error states
  if (!isInitialized) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="text-center text-muted-foreground">
          Initializing filters and effects...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="text-center text-destructive">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold mb-2">Filters & Effects</h2>
        <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="layers">Layers</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
            <TabsTrigger value="presets">Presets</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} className="h-full">
          {/* Layers Tab */}
          <TabsContent value="layers" className="h-full p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">Effect Layers</h3>
              <Button onClick={handleCreateLayer} size="sm">
                + New Layer
              </Button>
            </div>

            <ScrollArea className="h-[300px]">
              <div className="space-y-2">
                {activeLayers.map(layer => (
                  <LayerItem
                    key={layer.id}
                    layer={layer}
                    isSelected={selectedLayerId === layer.id}
                    onSelect={() => handleSelectLayer(layer.id)}
                    onToggleEnabled={() => handleToggleLayerEnabled(layer.id)}
                    onDelete={() => handleDeleteLayer(layer.id)}
                    onDuplicate={() => handleDuplicateLayer(layer.id)}
                    onRename={(name) => handleRenameLayer(layer.id, name)}
                  />
                ))}
                {activeLayers.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    No layers yet. Create your first layer to get started.
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Selected Layer Filters */}
            {selectedLayer && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h4 className="font-medium">
                    {selectedLayer.name} - Filters ({selectedLayer.filters.length})
                  </h4>
                  
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-2">
                      {selectedLayer.filters.map(filter => {
                        const shader = filterLibrary?.getFilterById(filter.shaderId);
                        if (!shader) return null;

                        return (
                          <FilterItem
                            key={filter.id}
                            filter={filter}
                            shader={shader}
                            onUpdate={(params) => handleUpdateFilter(filter.id, params)}
                            onRemove={() => handleRemoveFilter(filter.id)}
                            onToggleEnabled={() => handleToggleFilterEnabled(filter.id)}
                            expanded={expandedFilters.has(filter.id)}
                            onToggleExpanded={() => handleToggleFilterExpanded(filter.id)}
                          />
                        );
                      })}
                      {selectedLayer.filters.length === 0 && (
                        <div className="text-center text-muted-foreground py-4">
                          No filters applied. Browse the Filters tab to add some.
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              </>
            )}
          </TabsContent>

          {/* Filters Tab */}
          <TabsContent value="filters" className="h-full p-4 space-y-4">
            {/* Search and Category Filter */}
            <div className="space-y-3">
              <Input
                placeholder="Search filters..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
              
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedCategory === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('all')}
                >
                  All
                </Button>
                {Object.keys(filtersByCategory).map(category => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category as FilterCategory)}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>

            {/* Available Filters */}
            <ScrollArea className="h-[400px]">
              <div className="grid grid-cols-1 gap-2">
                {filteredFilters.map(filter => (
                  <Card 
                    key={filter.id} 
                    className="p-3 cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleAddFilter(filter.id)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium text-sm">{filter.name}</h4>
                        <p className="text-xs text-muted-foreground mt-1">
                          Click to add to {selectedLayer ? selectedLayer.name : 'new layer'}
                        </p>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {filter.category}
                      </Badge>
                    </div>
                  </Card>
                ))}
                {filteredFilters.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    No filters found matching your criteria.
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Presets Tab */}
          <TabsContent value="presets" className="h-full p-4 space-y-4">
            <div className="text-center text-muted-foreground py-8">
              Presets functionality coming soon...
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Performance Footer */}
      <div className="border-t p-3 text-xs text-muted-foreground">
        <div className="flex justify-between">
          <span>FPS: {performanceMetrics.averageFPS.toFixed(1)}</span>
          <span>Memory: {(performanceMetrics.memoryUsage / 1024 / 1024).toFixed(1)}MB</span>
          <span>Draw Calls: {performanceMetrics.drawCalls}</span>
        </div>
      </div>
    </div>
  );
};

export default FilterEffectsPanel;