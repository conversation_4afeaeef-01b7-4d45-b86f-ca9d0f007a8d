import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Clock, 
  GitBranch, 
  Save, 
  Upload, 
  Download, 
  Trash2, 
  RotateCcw, 
  RotateCw,
  Activity,
  Users,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Settings,
  Eye,
  EyeOff,
  Zap,
  Database,
  Monitor
} from 'lucide-react';
import { useCanvasState, useConflictResolution } from '../../hooks/use-canvas-state';
import { EnhancedHybridCanvasManager } from '../../lib/canvas/enhanced-hybrid-canvas';
import { AdvancedLayerManager } from '../../lib/canvas/advanced-layer-manager';
import { StateSnapshot, StateBranch } from '../../lib/canvas/canvas-state-manager';
import { SavedProject } from '../../lib/canvas/auto-save-manager';
import { SyncUser, ConflictInfo } from '../../lib/canvas/state-sync-manager';

interface StateManagementPanelProps {
  canvasManager: EnhancedHybridCanvasManager | null;
  layerManager: AdvancedLayerManager | null;
  className?: string;
}

interface TabConfig {
  id: string;
  label: string;
  icon: React.ReactNode;
  component: React.ComponentType<any>;
}

// History Tab Component
const HistoryTab: React.FC<{
  currentSnapshot?: StateSnapshot;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  onCaptureState: () => void;
  memoryUsage: number;
}> = ({ currentSnapshot, canUndo, canRedo, onUndo, onRedo, onCaptureState, memoryUsage }) => {
  const formatTimestamp = useCallback((timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  }, []);

  const formatMemoryUsage = useCallback((bytes: number) => {
    return `${(bytes).toFixed(1)} MB`;
  }, []);

  return (
    <div className="space-y-4">
      {/* Quick Actions */}
      <div className="flex space-x-2">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onUndo}
          disabled={!canUndo}
          className={`flex-1 flex items-center justify-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
            canUndo 
              ? 'bg-blue-500 hover:bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          <RotateCcw size={16} />
          <span>Undo</span>
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onRedo}
          disabled={!canRedo}
          className={`flex-1 flex items-center justify-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
            canRedo 
              ? 'bg-blue-500 hover:bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          <RotateCw size={16} />
          <span>Redo</span>
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onCaptureState}
          className="flex items-center justify-center px-3 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm font-medium transition-colors"
        >
          <Save size={16} />
        </motion.button>
      </div>

      {/* Current State Info */}
      {currentSnapshot && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">Current State</h4>
            <span className="text-xs text-gray-500">
              {formatTimestamp(currentSnapshot.timestamp)}
            </span>
          </div>
          
          <div className="text-sm text-gray-600">
            <div>ID: {currentSnapshot.id.substring(0, 8)}...</div>
            <div>Size: {(currentSnapshot.size / 1024).toFixed(1)} KB</div>
            {currentSnapshot.compressed && (
              <div className="flex items-center space-x-1 text-blue-600">
                <Zap size={12} />
                <span>Compressed</span>
              </div>
            )}
          </div>

          {currentSnapshot.state.metadata.changeDescription && (
            <div className="text-sm italic text-gray-500">
              "{currentSnapshot.state.metadata.changeDescription}"
            </div>
          )}
        </div>
      )}

      {/* Memory Usage */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium text-gray-900 flex items-center space-x-2">
            <Database size={16} />
            <span>Memory Usage</span>
          </h4>
          <span className="text-sm text-gray-600">
            {formatMemoryUsage(memoryUsage)}
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              memoryUsage > 80 ? 'bg-red-500' : 
              memoryUsage > 60 ? 'bg-yellow-500' : 'bg-green-500'
            }`}
            style={{ width: `${Math.min(100, memoryUsage)}%` }}
          />
        </div>
        
        {memoryUsage > 80 && (
          <p className="text-xs text-red-600 mt-1">
            High memory usage. Consider saving your work.
          </p>
        )}
      </div>
    </div>
  );
};

// Branches Tab Component
const BranchesTab: React.FC<{
  branches: StateBranch[];
  currentBranch?: StateBranch;
  onCreateBranch: (name: string) => void;
  onSwitchBranch: (branchId: string) => void;
  onMergeBranch: (sourceBranchId: string, targetBranchId: string) => void;
}> = ({ branches, currentBranch, onCreateBranch, onSwitchBranch, onMergeBranch }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newBranchName, setNewBranchName] = useState('');
  const [selectedBranches, setSelectedBranches] = useState<string[]>([]);

  const handleCreateBranch = useCallback(() => {
    if (newBranchName.trim()) {
      onCreateBranch(newBranchName.trim());
      setNewBranchName('');
      setShowCreateForm(false);
    }
  }, [newBranchName, onCreateBranch]);

  const handleMergeBranches = useCallback(() => {
    if (selectedBranches.length === 2) {
      onMergeBranch(selectedBranches[0], selectedBranches[1]);
      setSelectedBranches([]);
    }
  }, [selectedBranches, onMergeBranch]);

  const formatTimestamp = useCallback((timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  }, []);

  return (
    <div className="space-y-4">
      {/* Create Branch */}
      <div>
        <AnimatePresence>
          {!showCreateForm ? (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowCreateForm(true)}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors"
            >
              <GitBranch size={16} />
              <span>Create Branch</span>
            </motion.button>
          ) : (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-2"
            >
              <input
                type="text"
                value={newBranchName}
                onChange={(e) => setNewBranchName(e.target.value)}
                placeholder="Branch name..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
                onKeyPress={(e) => e.key === 'Enter' && handleCreateBranch()}
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleCreateBranch}
                  disabled={!newBranchName.trim()}
                  className="flex-1 px-3 py-1 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded text-sm font-medium transition-colors"
                >
                  Create
                </button>
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    setNewBranchName('');
                  }}
                  className="flex-1 px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Merge Action */}
      {selectedBranches.length === 2 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-yellow-50 border border-yellow-200 rounded-lg p-3"
        >
          <div className="text-sm text-yellow-800 mb-2">
            Merge "{branches.find(b => b.id === selectedBranches[0])?.name}" into "{branches.find(b => b.id === selectedBranches[1])?.name}"?
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleMergeBranches}
              className="px-3 py-1 bg-yellow-500 hover:bg-yellow-600 text-white rounded text-sm font-medium transition-colors"
            >
              Merge
            </button>
            <button
              onClick={() => setSelectedBranches([])}
              className="px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm font-medium transition-colors"
            >
              Cancel
            </button>
          </div>
        </motion.div>
      )}

      {/* Branches List */}
      <div className="space-y-2">
        <h4 className="font-medium text-gray-900">Branches</h4>
        <div className="space-y-1">
          {branches.map((branch) => (
            <motion.div
              key={branch.id}
              whileHover={{ scale: 1.01 }}
              className={`p-3 rounded-lg border cursor-pointer transition-all ${
                branch.id === currentBranch?.id
                  ? 'bg-blue-50 border-blue-200'
                  : selectedBranches.includes(branch.id)
                  ? 'bg-yellow-50 border-yellow-200'
                  : 'bg-white border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => {
                if (selectedBranches.length < 2 && !selectedBranches.includes(branch.id)) {
                  setSelectedBranches(prev => [...prev, branch.id]);
                } else if (selectedBranches.includes(branch.id)) {
                  setSelectedBranches(prev => prev.filter(id => id !== branch.id));
                } else {
                  onSwitchBranch(branch.id);
                }
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <GitBranch size={14} />
                  <span className="font-medium text-sm">{branch.name}</span>
                  {branch.id === currentBranch?.id && (
                    <span className="px-2 py-1 bg-blue-500 text-white text-xs rounded">
                      Current
                    </span>
                  )}
                </div>
                <span className="text-xs text-gray-500">
                  {branch.snapshots.length} states
                </span>
              </div>
              
              <div className="mt-1 text-xs text-gray-600">
                Created: {formatTimestamp(branch.createdAt)}
              </div>
              
              {branch.mergedTo && (
                <div className="mt-1 text-xs text-green-600">
                  Merged to {branches.find(b => b.id === branch.mergedTo)?.name}
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Projects Tab Component
const ProjectsTab: React.FC<{
  onSaveProject: (name: string) => Promise<void>;
  onLoadProject: (projectId: string) => Promise<void>;
  onDeleteProject: (projectId: string) => Promise<void>;
  getAllProjects: () => Promise<SavedProject[]>;
  saveProgress: any;
}> = ({ onSaveProject, onLoadProject, onDeleteProject, getAllProjects, saveProgress }) => {
  const [projects, setProjects] = useState<SavedProject[]>([]);
  const [showSaveForm, setShowSaveForm] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [loading, setLoading] = useState(false);

  const loadProjects = useCallback(async () => {
    try {
      const projectList = await getAllProjects();
      setProjects(projectList);
    } catch (error) {
      console.error('Failed to load projects:', error);
    }
  }, [getAllProjects]);

  React.useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  const handleSaveProject = useCallback(async () => {
    if (!newProjectName.trim()) return;
    
    setLoading(true);
    try {
      await onSaveProject(newProjectName.trim());
      setNewProjectName('');
      setShowSaveForm(false);
      await loadProjects();
    } catch (error) {
      console.error('Failed to save project:', error);
    } finally {
      setLoading(false);
    }
  }, [newProjectName, onSaveProject, loadProjects]);

  const handleLoadProject = useCallback(async (projectId: string) => {
    setLoading(true);
    try {
      await onLoadProject(projectId);
      await loadProjects();
    } catch (error) {
      console.error('Failed to load project:', error);
    } finally {
      setLoading(false);
    }
  }, [onLoadProject, loadProjects]);

  const handleDeleteProject = useCallback(async (projectId: string) => {
    if (!confirm('Are you sure you want to delete this project?')) return;
    
    setLoading(true);
    try {
      await onDeleteProject(projectId);
      await loadProjects();
    } catch (error) {
      console.error('Failed to delete project:', error);
    } finally {
      setLoading(false);
    }
  }, [onDeleteProject, loadProjects]);

  const formatFileSize = useCallback((bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  }, []);

  const formatTimestamp = useCallback((timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  }, []);

  return (
    <div className="space-y-4">
      {/* Save Progress */}
      {saveProgress.stage !== 'complete' && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-900">
              {saveProgress.message}
            </span>
            <span className="text-sm text-blue-700">
              {saveProgress.progress}%
            </span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div 
              className="h-2 bg-blue-500 rounded-full transition-all duration-300"
              style={{ width: `${saveProgress.progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Save New Project */}
      <div>
        <AnimatePresence>
          {!showSaveForm ? (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowSaveForm(true)}
              disabled={loading}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded-lg text-sm font-medium transition-colors"
            >
              <Save size={16} />
              <span>Save Project</span>
            </motion.button>
          ) : (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-2"
            >
              <input
                type="text"
                value={newProjectName}
                onChange={(e) => setNewProjectName(e.target.value)}
                placeholder="Project name..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                autoFocus
                onKeyPress={(e) => e.key === 'Enter' && handleSaveProject()}
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleSaveProject}
                  disabled={!newProjectName.trim() || loading}
                  className="flex-1 px-3 py-1 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded text-sm font-medium transition-colors"
                >
                  Save
                </button>
                <button
                  onClick={() => {
                    setShowSaveForm(false);
                    setNewProjectName('');
                  }}
                  disabled={loading}
                  className="flex-1 px-3 py-1 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white rounded text-sm font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Projects List */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-gray-900">Saved Projects</h4>
          <button
            onClick={loadProjects}
            disabled={loading}
            className="text-sm text-blue-600 hover:text-blue-700 disabled:text-gray-400"
          >
            Refresh
          </button>
        </div>

        <div className="space-y-2 max-h-96 overflow-y-auto">
          {projects.map((project) => (
            <motion.div
              key={project.id}
              whileHover={{ scale: 1.01 }}
              className="bg-white border border-gray-200 hover:border-gray-300 rounded-lg p-3 transition-all"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h5 className="font-medium text-sm truncate">{project.name}</h5>
                    {project.isAutoSave && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">
                        Auto-save
                      </span>
                    )}
                  </div>
                  
                  <div className="mt-1 text-xs text-gray-600 space-y-1">
                    <div>Size: {formatFileSize(project.size)}</div>
                    <div>Saved: {formatTimestamp(project.timestamp)}</div>
                  </div>

                  {project.thumbnail && (
                    <div className="mt-2">
                      <img 
                        src={project.thumbnail} 
                        alt="Project thumbnail"
                        className="w-16 h-12 object-cover rounded border"
                      />
                    </div>
                  )}
                </div>

                <div className="flex flex-col space-y-1 ml-2">
                  <button
                    onClick={() => handleLoadProject(project.id)}
                    disabled={loading}
                    className="p-1 text-blue-600 hover:text-blue-700 disabled:text-gray-400 transition-colors"
                    title="Load project"
                  >
                    <Upload size={14} />
                  </button>
                  
                  <button
                    onClick={() => handleDeleteProject(project.id)}
                    disabled={loading}
                    className="p-1 text-red-600 hover:text-red-700 disabled:text-gray-400 transition-colors"
                    title="Delete project"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}

          {projects.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Save size={24} className="mx-auto mb-2 opacity-50" />
              <p className="text-sm">No saved projects yet</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Collaboration Tab Component
const CollaborationTab: React.FC<{
  isConnected: boolean;
  networkStatus: any;
  sessionUsers: SyncUser[];
  conflicts: ConflictInfo[];
  onCreateSession: (name: string) => Promise<void>;
  onJoinSession: (sessionId: string, userName?: string) => Promise<void>;
  onLeaveSession: () => void;
  onResolveConflict: (conflictId: string, resolution: string) => void;
}> = ({ 
  isConnected, 
  networkStatus, 
  sessionUsers, 
  conflicts,
  onCreateSession, 
  onJoinSession, 
  onLeaveSession,
  onResolveConflict 
}) => {
  const [showCreateSession, setShowCreateSession] = useState(false);
  const [showJoinSession, setShowJoinSession] = useState(false);
  const [sessionName, setSessionName] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [userName, setUserName] = useState('');

  const getQualityColor = useCallback((quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  }, []);

  const getQualityIcon = useCallback((quality: string) => {
    switch (quality) {
      case 'excellent':
      case 'good':
        return <CheckCircle size={16} />;
      case 'fair':
        return <AlertTriangle size={16} />;
      case 'poor':
        return <XCircle size={16} />;
      default:
        return <Activity size={16} />;
    }
  }, []);

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <div className={`p-3 rounded-lg border ${
        isConnected 
          ? 'bg-green-50 border-green-200' 
          : 'bg-red-50 border-red-200'
      }`}>
        <div className="flex items-center space-x-2">
          {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
          <span className="font-medium text-sm">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        
        {isConnected && (
          <div className="mt-2 space-y-1 text-xs">
            <div className={`flex items-center space-x-2 ${getQualityColor(networkStatus.quality)}`}>
              {getQualityIcon(networkStatus.quality)}
              <span>Quality: {networkStatus.quality}</span>
            </div>
            <div className="text-gray-600">
              Latency: {networkStatus.latency}ms
            </div>
          </div>
        )}
      </div>

      {/* Session Actions */}
      {!isConnected && (
        <div className="space-y-2">
          <button
            onClick={() => setShowCreateSession(true)}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors"
          >
            <Users size={16} />
            <span>Create Session</span>
          </button>

          <button
            onClick={() => setShowJoinSession(true)}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm font-medium transition-colors"
          >
            <Wifi size={16} />
            <span>Join Session</span>
          </button>
        </div>
      )}

      {isConnected && (
        <button
          onClick={onLeaveSession}
          className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg text-sm font-medium transition-colors"
        >
          <WifiOff size={16} />
          <span>Leave Session</span>
        </button>
      )}

      {/* Create Session Form */}
      <AnimatePresence>
        {showCreateSession && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-2 bg-gray-50 p-3 rounded-lg"
          >
            <input
              type="text"
              value={sessionName}
              onChange={(e) => setSessionName(e.target.value)}
              placeholder="Session name..."
              className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <div className="flex space-x-2">
              <button
                onClick={async () => {
                  if (sessionName.trim()) {
                    await onCreateSession(sessionName.trim());
                    setSessionName('');
                    setShowCreateSession(false);
                  }
                }}
                className="flex-1 px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium transition-colors"
              >
                Create
              </button>
              <button
                onClick={() => {
                  setShowCreateSession(false);
                  setSessionName('');
                }}
                className="flex-1 px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm font-medium transition-colors"
              >
                Cancel
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Join Session Form */}
      <AnimatePresence>
        {showJoinSession && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-2 bg-gray-50 p-3 rounded-lg"
          >
            <input
              type="text"
              value={sessionId}
              onChange={(e) => setSessionId(e.target.value)}
              placeholder="Session ID..."
              className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
            />
            <input
              type="text"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              placeholder="Your name..."
              className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
            />
            <div className="flex space-x-2">
              <button
                onClick={async () => {
                  if (sessionId.trim()) {
                    await onJoinSession(sessionId.trim(), userName.trim() || undefined);
                    setSessionId('');
                    setUserName('');
                    setShowJoinSession(false);
                  }
                }}
                className="flex-1 px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm font-medium transition-colors"
              >
                Join
              </button>
              <button
                onClick={() => {
                  setShowJoinSession(false);
                  setSessionId('');
                  setUserName('');
                }}
                className="flex-1 px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm font-medium transition-colors"
              >
                Cancel
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Session Users */}
      {sessionUsers.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900">Session Users</h4>
          <div className="space-y-1">
            {sessionUsers.map((user) => (
              <div
                key={user.id}
                className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg"
              >
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: user.color }}
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{user.name}</span>
                    {user.permissions.isOwner && (
                      <span className="px-1 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded">
                        Owner
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    Status: {user.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Conflicts */}
      {conflicts.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900 flex items-center space-x-2">
            <AlertTriangle size={16} className="text-yellow-600" />
            <span>Conflicts</span>
          </h4>
          <div className="space-y-2">
            {conflicts.map((conflict) => (
              <div
                key={conflict.id}
                className="bg-yellow-50 border border-yellow-200 rounded-lg p-3"
              >
                <div className="text-sm font-medium text-yellow-900 mb-1">
                  {conflict.type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </div>
                <div className="text-xs text-yellow-700 mb-2">
                  Users: {conflict.users.join(', ')}
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => onResolveConflict(conflict.id, 'accept-local')}
                    className="px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded transition-colors"
                  >
                    Keep Mine
                  </button>
                  <button
                    onClick={() => onResolveConflict(conflict.id, 'accept-remote')}
                    className="px-2 py-1 bg-green-500 hover:bg-green-600 text-white text-xs rounded transition-colors"
                  >
                    Keep Theirs
                  </button>
                  <button
                    onClick={() => onResolveConflict(conflict.id, 'merge')}
                    className="px-2 py-1 bg-purple-500 hover:bg-purple-600 text-white text-xs rounded transition-colors"
                  >
                    Merge
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Performance Tab Component
const PerformanceTab: React.FC<{
  performanceReport: any;
  memoryUsage: number;
}> = ({ performanceReport, memoryUsage }) => {
  const formatBytes = useCallback((bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  }, []);

  const getPerformanceColor = useCallback((value: number, thresholds: { good: number; fair: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.fair) return 'text-yellow-600';
    return 'text-red-600';
  }, []);

  return (
    <div className="space-y-4">
      {/* Memory Usage */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-900 flex items-center space-x-2">
            <Database size={16} />
            <span>Memory Usage</span>
          </h4>
          <span className={`text-sm font-medium ${getPerformanceColor(memoryUsage, { good: 50, fair: 80 })}`}>
            {formatBytes(memoryUsage * 1024 * 1024)}
          </span>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-600">
            <span>Used</span>
            <span>Limit: {formatBytes(100 * 1024 * 1024)}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                memoryUsage > 80 ? 'bg-red-500' : 
                memoryUsage > 50 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(100, memoryUsage)}%` }}
            />
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 flex items-center space-x-2 mb-3">
          <Monitor size={16} />
          <span>Performance Metrics</span>
        </h4>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-xs text-gray-600 mb-1">Snapshots</div>
            <div className="text-lg font-semibold text-gray-900">
              {performanceReport.snapshotCount || 0}
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-600 mb-1">Branches</div>
            <div className="text-lg font-semibold text-gray-900">
              {performanceReport.branchCount || 0}
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-600 mb-1">Compression</div>
            <div className="text-lg font-semibold text-gray-900">
              {performanceReport.compressionRatio ? 
                `${(performanceReport.compressionRatio * 100).toFixed(1)}%` : 
                'N/A'
              }
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-600 mb-1">Operations</div>
            <div className="text-lg font-semibold text-gray-900">
              {performanceReport.operationCount || 0}
            </div>
          </div>
        </div>
      </div>

      {/* System Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3">System Information</h4>
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Browser:</span>
            <span className="text-gray-900">{navigator.userAgent.split(' ')[0]}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-600">Platform:</span>
            <span className="text-gray-900">{navigator.platform}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-600">Language:</span>
            <span className="text-gray-900">{navigator.language}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-600">Online:</span>
            <span className={navigator.onLine ? 'text-green-600' : 'text-red-600'}>
              {navigator.onLine ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main State Management Panel Component
export const StateManagementPanel: React.FC<StateManagementPanelProps> = ({
  canvasManager,
  layerManager,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('history');
  const [isVisible, setIsVisible] = useState(true);

  // Initialize canvas state hook
  const canvasState = useCanvasState(canvasManager, layerManager, {
    enableAutoSave: true,
    enableSync: false, // Disabled by default for now
    autoSaveInterval: 30000,
    maxUndoSteps: 50,
    enableBranching: true,
    enableCompression: true,
    memoryLimit: 100
  });

  // Initialize conflict resolution
  const conflictResolution = useConflictResolution(canvasState.syncManager);

  // Error handling
  React.useEffect(() => {
    if (canvasState.error) {
      console.error('Canvas state error:', canvasState.error);
    }
  }, [canvasState.error]);

  // Tab configuration
  const tabs: TabConfig[] = useMemo(() => [
    {
      id: 'history',
      label: 'History',
      icon: <Clock size={16} />,
      component: HistoryTab
    },
    {
      id: 'branches',
      label: 'Branches',
      icon: <GitBranch size={16} />,
      component: BranchesTab
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: <Save size={16} />,
      component: ProjectsTab
    },
    {
      id: 'collaboration',
      label: 'Collaborate',
      icon: <Users size={16} />,
      component: CollaborationTab
    },
    {
      id: 'performance',
      label: 'Performance',
      icon: <Activity size={16} />,
      component: PerformanceTab
    }
  ], []);

  const activeTabConfig = tabs.find(tab => tab.id === activeTab);
  const ActiveTabComponent = activeTabConfig?.component;

  if (!canvasState.isInitialized) {
    return (
      <div className={`bg-white border border-gray-300 rounded-lg shadow-sm ${className}`}>
        <div className="p-4 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Initializing state management...</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white border border-gray-300 rounded-lg shadow-sm ${className}`}
    >
      {/* Panel Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">State Management</h3>
          <div className="flex items-center space-x-2">
            {canvasState.autoSaveStatus.enabled && (
              <div className="flex items-center space-x-1 text-green-600">
                <CheckCircle size={14} />
                <span className="text-xs">Auto-save</span>
              </div>
            )}
            {canvasState.isConnected && (
              <div className="flex items-center space-x-1 text-blue-600">
                <Wifi size={14} />
                <span className="text-xs">Connected</span>
              </div>
            )}
            <button
              onClick={() => setIsVisible(!isVisible)}
              className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
            >
              {isVisible ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
        </div>

        {/* Error Display */}
        {canvasState.error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
            {canvasState.error}
          </div>
        )}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="overflow-hidden"
          >
            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
              <div className="flex space-x-1 p-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    {tab.icon}
                    <span>{tab.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-4">
              {ActiveTabComponent && (
                <ActiveTabComponent
                  // History tab props
                  currentSnapshot={canvasState.currentSnapshot}
                  canUndo={canvasState.canUndo}
                  canRedo={canvasState.canRedo}
                  onUndo={canvasState.undo}
                  onRedo={canvasState.redo}
                  onCaptureState={() => canvasState.captureState('Manual capture')}
                  memoryUsage={canvasState.memoryUsage}
                  
                  // Branches tab props
                  branches={canvasState.branches}
                  currentBranch={canvasState.currentBranch}
                  onCreateBranch={canvasState.createBranch}
                  onSwitchBranch={canvasState.switchBranch}
                  onMergeBranch={canvasState.mergeBranch}
                  
                  // Projects tab props
                  onSaveProject={canvasState.saveProject}
                  onLoadProject={canvasState.loadProject}
                  onDeleteProject={canvasState.deleteProject}
                  getAllProjects={canvasState.getAllProjects}
                  saveProgress={canvasState.saveProgress}
                  
                  // Collaboration tab props
                  isConnected={canvasState.isConnected}
                  networkStatus={canvasState.networkStatus}
                  sessionUsers={canvasState.sessionUsers}
                  conflicts={conflictResolution.activeConflicts}
                  onCreateSession={canvasState.createSession}
                  onJoinSession={canvasState.joinSession}
                  onLeaveSession={canvasState.leaveSession}
                  onResolveConflict={conflictResolution.resolveConflict}
                  
                  // Performance tab props
                  performanceReport={canvasState.performanceReport}
                />
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default StateManagementPanel;