'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useFabricCanvas } from '../../hooks/use-fabric-canvas';
import { fabric } from 'fabric';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';

export interface FabricCanvasEditorProps {
  width?: number;
  height?: number;
  className?: string;
}

export function FabricCanvasEditor({ 
  width = 800, 
  height = 600, 
  className = '' 
}: FabricCanvasEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const {
    canvasManager,
    canvas,
    isInitialized,
    addObject,
    removeObject,
    clearCanvas,
    undo,
    redo,
    canUndo,
    canRedo
  } = useFabricCanvas(canvasRef, {
    width,
    height,
    backgroundColor: '#ffffff',
    selection: true,
    preserveObjectStacking: true
  });

  const [selectedObjects, setSelectedObjects] = useState<fabric.Object[]>([]);

  useEffect(() => {
    if (!canvas) return;

    const handleSelection = (e: any) => {
      setSelectedObjects(e.selected || []);
    };

    canvas.on('selection:created', handleSelection);
    canvas.on('selection:updated', handleSelection);
    canvas.on('selection:cleared', () => setSelectedObjects([]));

    return () => {
      canvas.off('selection:created', handleSelection);
      canvas.off('selection:updated', handleSelection);
      canvas.off('selection:cleared');
    };
  }, [canvas]);

  const addRectangle = () => {
    const rect = new fabric.Rect({
      left: 100,
      top: 100,
      width: 100,
      height: 100,
      fill: '#ff0000',
      stroke: '#000000',
      strokeWidth: 2
    });
    addObject(rect);
  };

  const addCircle = () => {
    const circle = new fabric.Circle({
      left: 200,
      top: 200,
      radius: 50,
      fill: '#00ff00',
      stroke: '#000000',
      strokeWidth: 2
    });
    addObject(circle);
  };

  const addText = () => {
    const text = new fabric.Text('Hello World', {
      left: 150,
      top: 150,
      fontSize: 24,
      fill: '#000000',
      fontFamily: 'Arial'
    });
    addObject(text);
  };

  const deleteSelected = () => {
    if (canvas && selectedObjects.length > 0) {
      selectedObjects.forEach(obj => {
        canvas.remove(obj);
      });
      canvas.renderAll();
    }
  };

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-lg">Initializing canvas...</div>
      </div>
    );
  }

  return (
    <div className={`fabric-canvas-editor ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle>Fabric.js Canvas Editor</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Toolbar */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Button onClick={addRectangle} variant="outline" size="sm">
              Add Rectangle
            </Button>
            <Button onClick={addCircle} variant="outline" size="sm">
              Add Circle
            </Button>
            <Button onClick={addText} variant="outline" size="sm">
              Add Text
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button 
              onClick={undo} 
              disabled={!canUndo}
              variant="outline" 
              size="sm"
            >
              Undo
            </Button>
            <Button 
              onClick={redo} 
              disabled={!canRedo}
              variant="outline" 
              size="sm"
            >
              Redo
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button 
              onClick={deleteSelected} 
              disabled={selectedObjects.length === 0}
              variant="destructive" 
              size="sm"
            >
              Delete Selected
            </Button>
            <Button onClick={clearCanvas} variant="outline" size="sm">
              Clear Canvas
            </Button>
          </div>

          {/* Canvas */}
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <canvas
              ref={canvasRef}
              width={width}
              height={height}
              className="block"
            />
          </div>

          {/* Status */}
          <div className="mt-4 text-sm text-gray-600">
            Selected objects: {selectedObjects.length}
            {selectedObjects.length > 0 && (
              <span className="ml-2">
                ({selectedObjects.map(obj => obj.type).join(', ')})
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}