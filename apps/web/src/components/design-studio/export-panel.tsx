"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  Settings, 
  Image, 
  FileText, 
  Layers, 
  Palette,
  Zap,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  History,
  Trash2,
  RefreshCw,
  Monitor,
  Smartphone,
  Printer,
  Globe,
  FileImage
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  ExportEngine, 
  ExportFormat, 
  ExportOptions, 
  ExportQuality, 
  ExportResult, 
  ExportProgress,
  ExportHistoryEntry,
  BatchExportConfig,
  ColorSpace
} from '@/lib/export/export-engine';
import { 
  OptimizationPipeline, 
  OptimizationConfig, 
  OptimizationResult,
  OptimizationStrategy,
  QualityAssessment,
  AdaptiveQualityConfig
} from '@/lib/export/optimization-pipeline';

interface ExportPanelProps {
  canvas: fabric.Canvas | null;
  className?: string;
}

interface PreviewState {
  originalSize: number;
  optimizedSize: number;
  qualityScore: number;
  compressionRatio: number;
  previewUrl: string | null;
  isGenerating: boolean;
}

interface BatchExportState {
  isActive: boolean;
  progress: number;
  currentOperation: string;
  results: ExportResult[];
  errors: string[];
}

const formatOptions: { value: ExportFormat; label: string; icon: React.ReactNode }[] = [
  { value: 'PNG', label: 'PNG', icon: <FileImage className="w-4 h-4" /> },
  { value: 'JPEG', label: 'JPEG', icon: <Image className="w-4 h-4" /> },
  { value: 'WebP', label: 'WebP', icon: <Globe className="w-4 h-4" /> },
  { value: 'SVG', label: 'SVG', icon: <FileText className="w-4 h-4" /> },
  { value: 'PDF', label: 'PDF', icon: <FileText className="w-4 h-4" /> },
  { value: 'PSD', label: 'PSD', icon: <Layers className="w-4 h-4" /> }
];

const colorSpaceOptions: { value: ColorSpace; label: string }[] = [
  { value: 'sRGB', label: 'sRGB' },
  { value: 'Adobe RGB', label: 'Adobe RGB' },
  { value: 'Display P3', label: 'Display P3' },
  { value: 'CMYK', label: 'CMYK' }
];

const optimizationStrategyOptions: { value: OptimizationStrategy; label: string; icon: React.ReactNode }[] = [
  { value: 'size', label: 'Size Optimized', icon: <Zap className="w-4 h-4" /> },
  { value: 'quality', label: 'Quality First', icon: <Monitor className="w-4 h-4" /> },
  { value: 'balanced', label: 'Balanced', icon: <BarChart3 className="w-4 h-4" /> },
  { value: 'fast', label: 'Fast Export', icon: <Clock className="w-4 h-4" /> },
  { value: 'custom', label: 'Custom', icon: <Settings className="w-4 h-4" /> }
];

const presetOptions = [
  { value: 'web-optimized', label: 'Web Optimized', icon: <Globe className="w-4 h-4" /> },
  { value: 'mobile-optimized', label: 'Mobile Optimized', icon: <Smartphone className="w-4 h-4" /> },
  { value: 'print-ready', label: 'Print Ready', icon: <Printer className="w-4 h-4" /> },
  { value: 'social-media', label: 'Social Media', icon: <Monitor className="w-4 h-4" /> },
  { value: 'high-quality', label: 'High Quality', icon: <Monitor className="w-4 h-4" /> }
];

const batchSizeOptions = [
  { width: 1920, height: 1080, name: 'Full HD' },
  { width: 1280, height: 720, name: 'HD' },
  { width: 800, height: 600, name: 'SVGA' },
  { width: 400, height: 300, name: 'Thumbnail' }
];

export function ExportPanel({ canvas, className }: ExportPanelProps) {
  // State management
  const [exportEngine, setExportEngine] = useState<ExportEngine | null>(null);
  const [optimizationPipeline, setOptimizationPipeline] = useState<OptimizationPipeline | null>(null);
  const [activeTab, setActiveTab] = useState<'export' | 'batch' | 'history' | 'settings'>('export');
  
  // Export configuration
  const [exportFormat, setExportFormat] = useState<ExportFormat>('PNG');
  const [quality, setQuality] = useState<number>(85);
  const [scale, setScale] = useState<number>(1);
  const [dpi, setDpi] = useState<number>(72);
  const [colorSpace, setColorSpace] = useState<ColorSpace>('sRGB');
  const [includeMetadata, setIncludeMetadata] = useState<boolean>(true);
  const [enableTransparency, setEnableTransparency] = useState<boolean>(true);
  const [enableProgressive, setEnableProgressive] = useState<boolean>(true);
  const [backgroundColor, setBackgroundColor] = useState<string>('');
  const [cropToContent, setCropToContent] = useState<boolean>(false);
  
  // Optimization configuration
  const [optimizationStrategy, setOptimizationStrategy] = useState<OptimizationStrategy>('balanced');
  const [targetSize, setTargetSize] = useState<number>(500);
  const [targetQuality, setTargetQuality] = useState<number>(85);
  const [enableOptimization, setEnableOptimization] = useState<boolean>(true);
  const [enableAdaptiveQuality, setEnableAdaptiveQuality] = useState<boolean>(true);
  
  // UI state
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [exportProgress, setExportProgress] = useState<ExportProgress | null>(null);
  const [previewState, setPreviewState] = useState<PreviewState>({
    originalSize: 0,
    optimizedSize: 0,
    qualityScore: 0,
    compressionRatio: 0,
    previewUrl: null,
    isGenerating: false
  });
  const [batchExportState, setBatchExportState] = useState<BatchExportState>({
    isActive: false,
    progress: 0,
    currentOperation: '',
    results: [],
    errors: []
  });
  
  // Export history
  const [exportHistory, setExportHistory] = useState<ExportHistoryEntry[]>([]);
  const [qualityAssessment, setQualityAssessment] = useState<QualityAssessment | null>(null);
  
  // Batch export configuration
  const [batchFormats, setBatchFormats] = useState<ExportFormat[]>(['PNG', 'JPEG']);
  const [batchSizes, setBatchSizes] = useState<typeof batchSizeOptions>([batchSizeOptions[0], batchSizeOptions[1]]);
  const [enableParallelProcessing, setEnableParallelProcessing] = useState<boolean>(true);
  const [maxConcurrentExports, setMaxConcurrentExports] = useState<number>(3);

  // Initialize engines
  useEffect(() => {
    if (canvas) {
      const engine = new ExportEngine(canvas);
      const pipeline = new OptimizationPipeline();
      
      setExportEngine(engine);
      setOptimizationPipeline(pipeline);
      
      // Listen to export events
      engine.on('export-progress', handleExportProgress);
      
      return () => {
        engine.dispose();
        pipeline.dispose();
      };
    }
  }, [canvas]);

  // Load export history
  useEffect(() => {
    if (exportEngine) {
      const history = exportEngine.getExportHistory();
      setExportHistory(history);
    }
  }, [exportEngine]);

  // Generate preview when settings change
  useEffect(() => {
    if (exportEngine && optimizationPipeline) {
      generatePreview();
    }
  }, [exportFormat, quality, scale, optimizationStrategy, targetSize, enableOptimization]);

  // Event handlers
  const handleExportProgress = useCallback((data: { exportId: string; progress: ExportProgress }) => {
    setExportProgress(data.progress);
  }, []);

  const generatePreview = useCallback(async () => {
    if (!exportEngine || !optimizationPipeline || !canvas) return;
    
    setPreviewState(prev => ({ ...prev, isGenerating: true }));
    
    try {
      const exportOptions = createExportOptions();
      const result = await exportEngine.exportCanvas(exportOptions);
      
      if (result.success && result.data) {
        let optimizedResult = result;
        
        if (enableOptimization) {
          const optimizationConfig = createOptimizationConfig();
          const optimization = await optimizationPipeline.optimize(result, optimizationConfig);
          optimizedResult = { ...result, data: result.data, metadata: { ...result.metadata, size: optimization.optimizedSize } };
        }
        
        const qualityAssessment = await optimizationPipeline.assessQuality(optimizedResult.data!, exportFormat);
        
        setPreviewState({
          originalSize: result.metadata.size,
          optimizedSize: optimizedResult.metadata.size,
          qualityScore: qualityAssessment.score,
          compressionRatio: optimizedResult.metadata.compressionRatio,
          previewUrl: optimizedResult.url || null,
          isGenerating: false
        });
        
        setQualityAssessment(qualityAssessment);
      }
    } catch (error) {
      console.error('Preview generation failed:', error);
      setPreviewState(prev => ({ ...prev, isGenerating: false }));
    }
  }, [exportEngine, optimizationPipeline, canvas, exportFormat, quality, scale, enableOptimization]);

  const createExportOptions = useCallback((): ExportOptions => {
    const qualityConfig: ExportQuality = {
      format: exportFormat,
      quality,
      compression: quality > 90 ? 'lossless' : 'lossy',
      dpi,
      colorSpace,
      enableProgressive,
      enableOptimization
    };

    return {
      format: exportFormat,
      quality: qualityConfig,
      scale,
      includeMetadata,
      enableProgressive,
      backgroundColor: backgroundColor || undefined,
      cropToContent,
      enableTransparency,
      enableColorManagement: true
    };
  }, [exportFormat, quality, scale, dpi, colorSpace, includeMetadata, enableProgressive, backgroundColor, cropToContent, enableTransparency]);

  const createOptimizationConfig = useCallback((): OptimizationConfig => {
    return {
      strategy: optimizationStrategy,
      targetSize: targetSize * 1024, // Convert KB to bytes
      targetQuality,
      enableProgressiveLoading: enableProgressive,
      enableAdaptiveQuality,
      compressionAlgorithm: 'webp',
      colorOptimization: {
        reduceColorDepth: optimizationStrategy === 'size',
        optimizePalette: true,
        enableDithering: false,
        quantizationLevels: 256
      },
      imageOptimization: {
        enableSharpening: optimizationStrategy === 'quality',
        enableNoiseReduction: true,
        enableContrastEnhancement: optimizationStrategy === 'quality',
        enableColorCorrection: optimizationStrategy === 'quality'
      }
    };
  }, [optimizationStrategy, targetSize, targetQuality, enableProgressive, enableAdaptiveQuality]);

  const handleExport = async () => {
    if (!exportEngine || !optimizationPipeline) return;
    
    setIsExporting(true);
    setExportProgress(null);
    
    try {
      const exportOptions = createExportOptions();
      const result = await exportEngine.exportCanvas(exportOptions);
      
      if (result.success && result.data) {
        let finalResult = result;
        
        if (enableOptimization) {
          const optimizationConfig = createOptimizationConfig();
          await optimizationPipeline.optimize(result, optimizationConfig);
          finalResult = result; // Result is modified in-place
        }
        
        await exportEngine.downloadExport(finalResult);
        
        // Update history
        const history = exportEngine.getExportHistory();
        setExportHistory(history);
      }
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
      setExportProgress(null);
    }
  };

  const handleBatchExport = async () => {
    if (!exportEngine) return;
    
    setBatchExportState({
      isActive: true,
      progress: 0,
      currentOperation: 'Preparing batch export...',
      results: [],
      errors: []
    });
    
    try {
      const batchConfig: BatchExportConfig = {
        formats: batchFormats,
        sizes: batchSizes,
        quality: {
          format: exportFormat,
          quality,
          compression: quality > 90 ? 'lossless' : 'lossy',
          dpi,
          colorSpace,
          enableProgressive,
          enableOptimization
        },
        enableParallelProcessing,
        maxConcurrentExports
      };
      
      const results = await exportEngine.batchExport(batchConfig);
      
      // Download all results
      for (const result of results) {
        if (result.success && result.data) {
          await exportEngine.downloadExport(result);
        }
      }
      
      setBatchExportState({
        isActive: false,
        progress: 100,
        currentOperation: 'Batch export completed!',
        results,
        errors: results.filter(r => !r.success).map(r => r.error || 'Unknown error')
      });
      
    } catch (error) {
      setBatchExportState(prev => ({
        ...prev,
        isActive: false,
        errors: [...prev.errors, error instanceof Error ? error.message : 'Unknown error']
      }));
    }
  };

  const handlePresetSelect = (preset: string) => {
    if (!exportEngine) return;
    
    const presets = exportEngine.getExportPresets();
    const presetConfig = presets[preset];
    
    if (presetConfig) {
      setExportFormat(presetConfig.format);
      setQuality(presetConfig.quality.quality);
      setScale(presetConfig.scale);
      setDpi(presetConfig.quality.dpi);
      setColorSpace(presetConfig.quality.colorSpace);
      setIncludeMetadata(presetConfig.includeMetadata);
      setEnableTransparency(presetConfig.enableTransparency);
      setEnableProgressive(presetConfig.enableProgressive);
      setCropToContent(presetConfig.cropToContent);
      
      if (presetConfig.width && presetConfig.height) {
        // Set custom dimensions if provided
      }
    }
  };

  const handleOptimizationPresetSelect = (preset: string) => {
    if (!optimizationPipeline) return;
    
    const presets = optimizationPipeline.getOptimizationPresets();
    const presetConfig = presets[preset];
    
    if (presetConfig) {
      setOptimizationStrategy(presetConfig.strategy);
      setTargetSize((presetConfig.targetSize || 500000) / 1024); // Convert bytes to KB
      setTargetQuality(presetConfig.targetQuality || 85);
      setEnableOptimization(true);
      setEnableAdaptiveQuality(presetConfig.enableAdaptiveQuality || false);
    }
  };

  const clearHistory = () => {
    if (exportEngine) {
      exportEngine.clearExportHistory();
      setExportHistory([]);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  return (
    <Card className={cn("w-full max-w-2xl", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="w-5 h-5" />
          Export & Optimization
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="export">Export</TabsTrigger>
            <TabsTrigger value="batch">Batch</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="space-y-6">
            {/* Format Selection */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Export Format</Label>
                <Select value={exportFormat} onValueChange={(value) => setExportFormat(value as ExportFormat)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {formatOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          {option.icon}
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Quality Settings */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Quality</Label>
                  <span className="text-sm text-muted-foreground">{quality}%</span>
                </div>
                <Slider
                  value={[quality]}
                  onValueChange={([value]) => setQuality(value)}
                  max={100}
                  min={1}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* Scale */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Scale</Label>
                  <span className="text-sm text-muted-foreground">{scale}x</span>
                </div>
                <Slider
                  value={[scale]}
                  onValueChange={([value]) => setScale(value)}
                  max={5}
                  min={0.1}
                  step={0.1}
                  className="w-full"
                />
              </div>

              {/* DPI */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">DPI</Label>
                  <Input
                    type="number"
                    value={dpi}
                    onChange={(e) => setDpi(Number(e.target.value))}
                    min={72}
                    max={300}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Color Space</Label>
                  <Select value={colorSpace} onValueChange={(value) => setColorSpace(value as ColorSpace)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorSpaceOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Options */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Include Metadata</Label>
                  <Switch checked={includeMetadata} onCheckedChange={setIncludeMetadata} />
                </div>
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Enable Transparency</Label>
                  <Switch 
                    checked={enableTransparency} 
                    onCheckedChange={setEnableTransparency}
                    disabled={exportFormat === 'JPEG'}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Progressive Loading</Label>
                  <Switch checked={enableProgressive} onCheckedChange={setEnableProgressive} />
                </div>
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Crop to Content</Label>
                  <Switch checked={cropToContent} onCheckedChange={setCropToContent} />
                </div>
              </div>

              {/* Background Color */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Background Color</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="color"
                    value={backgroundColor || '#ffffff'}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-12 h-8 p-0 border rounded"
                  />
                  <Input
                    type="text"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    placeholder="Transparent"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Optimization Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Enable Optimization</Label>
                <Switch checked={enableOptimization} onCheckedChange={setEnableOptimization} />
              </div>

              {enableOptimization && (
                <div className="space-y-4">
                  {/* Optimization Strategy */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Optimization Strategy</Label>
                    <Select value={optimizationStrategy} onValueChange={(value) => setOptimizationStrategy(value as OptimizationStrategy)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {optimizationStrategyOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              {option.icon}
                              {option.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Target Size */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">Target Size</Label>
                      <span className="text-sm text-muted-foreground">{targetSize} KB</span>
                    </div>
                    <Slider
                      value={[targetSize]}
                      onValueChange={([value]) => setTargetSize(value)}
                      max={5000}
                      min={50}
                      step={50}
                      className="w-full"
                    />
                  </div>

                  {/* Target Quality */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">Target Quality</Label>
                      <span className="text-sm text-muted-foreground">{targetQuality}%</span>
                    </div>
                    <Slider
                      value={[targetQuality]}
                      onValueChange={([value]) => setTargetQuality(value)}
                      max={100}
                      min={50}
                      step={5}
                      className="w-full"
                    />
                  </div>

                  {/* Adaptive Quality */}
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Adaptive Quality</Label>
                    <Switch checked={enableAdaptiveQuality} onCheckedChange={setEnableAdaptiveQuality} />
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Preview */}
            <div className="space-y-4">
              <Label className="text-sm font-medium">Preview & Analysis</Label>
              
              {previewState.isGenerating ? (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Generating preview...
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">File Size</div>
                    <div className="text-sm text-muted-foreground">
                      {formatFileSize(previewState.optimizedSize)}
                      {previewState.originalSize > 0 && (
                        <span className="ml-1 text-green-600">
                          ({formatPercentage(previewState.compressionRatio)} smaller)
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Quality Score</div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">{previewState.qualityScore}%</span>
                      <Progress value={previewState.qualityScore} className="flex-1" />
                    </div>
                  </div>
                </div>
              )}

              {qualityAssessment && (
                <div className="space-y-2">
                  {qualityAssessment.recommendations.length > 0 && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        {qualityAssessment.recommendations.join(', ')}
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  {qualityAssessment.warnings.length > 0 && (
                    <Alert variant="destructive">
                      <XCircle className="h-4 w-4" />
                      <AlertDescription>
                        {qualityAssessment.warnings.join(', ')}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </div>

            <Separator />

            {/* Presets */}
            <div className="space-y-4">
              <Label className="text-sm font-medium">Quick Presets</Label>
              <div className="grid grid-cols-2 gap-2">
                {presetOptions.map((preset) => (
                  <Button
                    key={preset.value}
                    variant="outline"
                    size="sm"
                    onClick={() => handlePresetSelect(preset.value)}
                    className="justify-start"
                  >
                    {preset.icon}
                    {preset.label}
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Export Progress */}
            {exportProgress && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Export Progress</Label>
                  <span className="text-sm text-muted-foreground">{exportProgress.progress}%</span>
                </div>
                <Progress value={exportProgress.progress} />
                <div className="text-sm text-muted-foreground">{exportProgress.message}</div>
              </div>
            )}

            {/* Export Button */}
            <Button
              onClick={handleExport}
              disabled={!canvas || isExporting}
              className="w-full"
              size="lg"
            >
              {isExporting ? (
                <RefreshCw className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              {isExporting ? 'Exporting...' : 'Export'}
            </Button>
          </TabsContent>

          <TabsContent value="batch" className="space-y-6">
            {/* Batch Export Configuration */}
            <div className="space-y-4">
              <Label className="text-sm font-medium">Batch Export Configuration</Label>
              
              {/* Formats */}
              <div className="space-y-2">
                <Label className="text-sm">Export Formats</Label>
                <div className="grid grid-cols-3 gap-2">
                  {formatOptions.map((format) => (
                    <Button
                      key={format.value}
                      variant={batchFormats.includes(format.value) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (batchFormats.includes(format.value)) {
                          setBatchFormats(batchFormats.filter(f => f !== format.value));
                        } else {
                          setBatchFormats([...batchFormats, format.value]);
                        }
                      }}
                      className="justify-start"
                    >
                      {format.icon}
                      {format.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Sizes */}
              <div className="space-y-2">
                <Label className="text-sm">Export Sizes</Label>
                <div className="grid grid-cols-2 gap-2">
                  {batchSizeOptions.map((size) => (
                    <Button
                      key={size.name}
                      variant={batchSizes.includes(size) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (batchSizes.includes(size)) {
                          setBatchSizes(batchSizes.filter(s => s !== size));
                        } else {
                          setBatchSizes([...batchSizes, size]);
                        }
                      }}
                      className="justify-start"
                    >
                      {size.name} ({size.width}x{size.height})
                    </Button>
                  ))}
                </div>
              </div>

              {/* Processing Options */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Parallel Processing</Label>
                  <Switch checked={enableParallelProcessing} onCheckedChange={setEnableParallelProcessing} />
                </div>
                
                {enableParallelProcessing && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm">Max Concurrent Exports</Label>
                      <span className="text-sm text-muted-foreground">{maxConcurrentExports}</span>
                    </div>
                    <Slider
                      value={[maxConcurrentExports]}
                      onValueChange={([value]) => setMaxConcurrentExports(value)}
                      max={10}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Batch Export Progress */}
            {batchExportState.isActive && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Batch Export Progress</Label>
                  <span className="text-sm text-muted-foreground">{batchExportState.progress}%</span>
                </div>
                <Progress value={batchExportState.progress} />
                <div className="text-sm text-muted-foreground">{batchExportState.currentOperation}</div>
              </div>
            )}

            {/* Batch Export Results */}
            {batchExportState.results.length > 0 && (
              <div className="space-y-3">
                <Label className="text-sm font-medium">Export Results</Label>
                <div className="space-y-2">
                  {batchExportState.results.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                      <div className="flex items-center gap-2">
                        {result.success ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-600" />
                        )}
                        <span className="text-sm">
                          {result.metadata.format} - {result.metadata.dimensions.width}x{result.metadata.dimensions.height}
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {formatFileSize(result.metadata.size)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Batch Export Errors */}
            {batchExportState.errors.length > 0 && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  {batchExportState.errors.length} exports failed: {batchExportState.errors.join(', ')}
                </AlertDescription>
              </Alert>
            )}

            {/* Batch Export Button */}
            <Button
              onClick={handleBatchExport}
              disabled={!canvas || batchExportState.isActive || batchFormats.length === 0 || batchSizes.length === 0}
              className="w-full"
              size="lg"
            >
              {batchExportState.isActive ? (
                <RefreshCw className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              {batchExportState.isActive ? 'Exporting...' : `Export ${batchFormats.length * batchSizes.length} Files`}
            </Button>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Export History</Label>
              <Button variant="outline" size="sm" onClick={clearHistory}>
                <Trash2 className="w-4 h-4 mr-2" />
                Clear History
              </Button>
            </div>

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {exportHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <History className="w-8 h-8 mx-auto mb-2" />
                  No export history yet
                </div>
              ) : (
                exportHistory.map((entry) => (
                  <div key={entry.id} className="flex items-center justify-between p-3 bg-muted rounded">
                    <div className="flex items-center gap-3">
                      {entry.success ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-600" />
                      )}
                      <div>
                        <div className="text-sm font-medium">
                          {entry.format} - {entry.dimensions.width}x{entry.dimensions.height}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(entry.timestamp).toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm">{formatFileSize(entry.size)}</div>
                      <div className="text-xs text-muted-foreground">
                        Quality: {entry.quality.quality}%
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <div className="space-y-4">
              <Label className="text-sm font-medium">Performance Settings</Label>
              
              {/* Cache Management */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Enable Export Cache</Label>
                  <Switch defaultChecked />
                </div>
                
                <div className="space-y-2">
                  <Label className="text-sm">Cache Size Limit</Label>
                  <div className="flex items-center gap-2">
                    <Slider
                      defaultValue={[50]}
                      max={200}
                      min={10}
                      step={10}
                      className="flex-1"
                    />
                    <span className="text-sm text-muted-foreground w-12">50 MB</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Quality Settings */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Default Quality Settings</Label>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Auto-optimize exports</Label>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Progressive loading by default</Label>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Include metadata by default</Label>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Advanced Settings */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Advanced Settings</Label>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Use WebWorkers for processing</Label>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Enable color management</Label>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Enable experimental formats</Label>
                    <Switch />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}