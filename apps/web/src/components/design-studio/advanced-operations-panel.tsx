import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { Switch } from '../ui/switch';
import { Slider } from '../ui/slider';
import { Label } from '../ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { 
  Play, 
  Pause, 
  Square, 
  Settings, 
  Zap, 
  Activity, 
  Layers, 
  Wand2, 
  Sparkles,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  BarChart3,
  Gauge,
  Cpu,
  HardDrive,
  Wifi,
  RefreshCw,
  Download,
  Upload,
  Filter,
  Search,
  Star,
  Bookmark,
  Share,
  Copy,
  Trash2,
  Edit,
  Eye,
  EyeOff,
  ChevronRight,
  ChevronDown,
  Info,
  Maximize,
  Minimize
} from 'lucide-react';
import { 
  AdvancedCanvasHookReturn, 
  useAdvancedCanvas, 
  useIntelligentGeneration, 
  useProfessionalWorkflows 
} from '../../hooks/use-advanced-canvas';
import { 
  AdvancedCanvasOperation, 
  AdvancedCanvasWorkflow, 
  AdvancedCanvasOperationResult 
} from '../../lib/canvas/advanced-canvas-operations';
import { 
  WorkflowTemplate, 
  AutomationRule 
} from '../../lib/canvas/workflow-automation';
import { 
  PerformanceProfile, 
  PerformanceAlert 
} from '../../lib/canvas/performance-optimizer';

interface AdvancedOperationsPanelProps {
  advancedCanvas: AdvancedCanvasHookReturn;
  className?: string;
}

interface OperationCardProps {
  operation: AdvancedCanvasOperation;
  onExecute: (operationId: string, parameters: Record<string, any>) => void;
  isExecuting?: boolean;
  disabled?: boolean;
}

interface WorkflowCardProps {
  workflow: AdvancedCanvasWorkflow;
  onExecute: (workflowId: string, parameters: Record<string, any>) => void;
  isExecuting?: boolean;
  disabled?: boolean;
}

interface PerformanceMetricsProps {
  metrics: any;
  profile: PerformanceProfile | null;
  alerts: PerformanceAlert[];
  onProfileChange: (profileId: string) => void;
  onClearAlerts: () => void;
}

interface AutomationRulesProps {
  rules: AutomationRule[];
  onToggleRule: (ruleId: string, enabled: boolean) => void;
  onDeleteRule: (ruleId: string) => void;
  onCreateRule: () => void;
}

// Operation Card Component
const OperationCard: React.FC<OperationCardProps> = ({ 
  operation, 
  onExecute, 
  isExecuting = false, 
  disabled = false 
}) => {
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [showParameters, setShowParameters] = useState(false);

  const handleExecute = useCallback(() => {
    onExecute(operation.id, parameters);
  }, [operation.id, parameters, onExecute]);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'ai': return 'bg-purple-100 text-purple-800';
      case 'design': return 'bg-blue-100 text-blue-800';
      case 'effect': return 'bg-green-100 text-green-800';
      case 'typography': return 'bg-yellow-100 text-yellow-800';
      case 'vector': return 'bg-indigo-100 text-indigo-800';
      case 'composite': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getOperationIcon = (type: string) => {
    switch (type) {
      case 'ai-composite-generation': return <Sparkles className="w-4 h-4" />;
      case 'multi-system-style-transfer': return <Wand2 className="w-4 h-4" />;
      case 'cross-layer-effect-synthesis': return <Layers className="w-4 h-4" />;
      case 'intelligent-text-image-fusion': return <Zap className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getOperationIcon(operation.type)}
            <CardTitle className="text-sm font-medium">{operation.name}</CardTitle>
          </div>
          <Badge className={`text-xs ${getCategoryColor(operation.category)}`}>
            {operation.category}
          </Badge>
        </div>
        <p className="text-xs text-gray-600 mt-1">{operation.description}</p>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Operation Details */}
          <div className="text-xs text-gray-500">
            <div className="flex items-center justify-between">
              <span>Steps: {operation.workflow.length}</span>
              <span>Output: {operation.outputFormat}</span>
            </div>
            {operation.dependencies.length > 0 && (
              <div className="mt-1">
                Dependencies: {operation.dependencies.join(', ')}
              </div>
            )}
          </div>

          {/* Parameters Toggle */}
          {Object.keys(operation.parameters).length > 0 && (
            <div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowParameters(!showParameters)}
                className="w-full justify-between p-2 h-8"
              >
                <span className="text-xs">Parameters</span>
                {showParameters ? (
                  <ChevronDown className="w-3 h-3" />
                ) : (
                  <ChevronRight className="w-3 h-3" />
                )}
              </Button>
              
              {showParameters && (
                <div className="mt-2 space-y-2 p-2 bg-gray-50 rounded text-xs">
                  {Object.entries(operation.parameters).map(([key, defaultValue]) => (
                    <div key={key} className="flex items-center justify-between">
                      <label className="text-xs font-medium">{key}:</label>
                      <Input
                        value={parameters[key] || defaultValue}
                        onChange={(e) => setParameters(prev => ({
                          ...prev,
                          [key]: e.target.value
                        }))}
                        className="w-20 h-6 text-xs"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Action Button */}
          <Button
            onClick={handleExecute}
            disabled={disabled || isExecuting}
            className="w-full h-8 text-xs"
            size="sm"
          >
            {isExecuting ? (
              <>
                <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                Executing...
              </>
            ) : (
              <>
                <Play className="w-3 h-3 mr-1" />
                Execute
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Workflow Card Component
const WorkflowCard: React.FC<WorkflowCardProps> = ({ 
  workflow, 
  onExecute, 
  isExecuting = false, 
  disabled = false 
}) => {
  const [parameters, setParameters] = useState<Record<string, any>>({});

  const handleExecute = useCallback(() => {
    onExecute(workflow.id, parameters);
  }, [workflow.id, parameters, onExecute]);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'professional': return 'bg-blue-100 text-blue-800';
      case 'creative': return 'bg-purple-100 text-purple-800';
      case 'production': return 'bg-green-100 text-green-800';
      case 'experimental': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Layers className="w-4 h-4" />
            <CardTitle className="text-sm font-medium">{workflow.name}</CardTitle>
          </div>
          <Badge className={`text-xs ${getCategoryColor(workflow.category)}`}>
            {workflow.category}
          </Badge>
        </div>
        <p className="text-xs text-gray-600 mt-1">{workflow.description}</p>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Workflow Details */}
          <div className="text-xs text-gray-500">
            <div className="flex items-center justify-between">
              <span>Operations: {workflow.operations.length}</span>
              <span>Parallel: {workflow.parallelizable ? 'Yes' : 'No'}</span>
            </div>
          </div>

          {/* Global Parameters */}
          {Object.keys(workflow.globalParameters).length > 0 && (
            <div className="space-y-2">
              <Label className="text-xs font-medium">Global Parameters</Label>
              <div className="space-y-1">
                {Object.entries(workflow.globalParameters).map(([key, defaultValue]) => (
                  <div key={key} className="flex items-center justify-between">
                    <span className="text-xs">{key}:</span>
                    <Input
                      value={parameters[key] || defaultValue}
                      onChange={(e) => setParameters(prev => ({
                        ...prev,
                        [key]: e.target.value
                      }))}
                      className="w-20 h-6 text-xs"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Button */}
          <Button
            onClick={handleExecute}
            disabled={disabled || isExecuting}
            className="w-full h-8 text-xs"
            size="sm"
          >
            {isExecuting ? (
              <>
                <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <Play className="w-3 h-3 mr-1" />
                Run Workflow
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Performance Metrics Component
const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
  metrics,
  profile,
  alerts,
  onProfileChange,
  onClearAlerts
}) => {
  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default: return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  const getMetricColor = (value: number, threshold: number) => {
    if (value > threshold * 0.8) return 'text-red-600';
    if (value > threshold * 0.6) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="space-y-4">
      {/* Performance Profile */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Performance Profile</Label>
        <Select value={profile?.id || ''} onValueChange={onProfileChange}>
          <SelectTrigger className="h-8 text-xs">
            <SelectValue placeholder="Select profile" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="high-quality">High Quality</SelectItem>
            <SelectItem value="balanced">Balanced</SelectItem>
            <SelectItem value="performance">Performance</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Operations</span>
            <span className="text-xs font-medium">{metrics.operationCount}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Avg Time</span>
            <span className="text-xs font-medium">
              {metrics.averageExecutionTime.toFixed(1)}ms
            </span>
          </div>
        </div>
        
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Memory</span>
            <span className={`text-xs font-medium ${getMetricColor(metrics.memoryEfficiency, 100)}`}>
              {metrics.memoryEfficiency.toFixed(1)}%
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Error Rate</span>
            <span className={`text-xs font-medium ${getMetricColor(metrics.errorRate, 10)}`}>
              {metrics.errorRate.toFixed(1)}%
            </span>
          </div>
        </div>
      </div>

      {/* System Utilization */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">System Utilization</Label>
        <div className="space-y-2">
          {Object.entries(metrics.systemUtilization).map(([system, utilization]) => (
            <div key={system} className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">{system}</span>
                <span className="text-xs font-medium">{utilization}%</span>
              </div>
              <Progress value={utilization as number} className="h-1" />
            </div>
          ))}
        </div>
      </div>

      {/* Performance Alerts */}
      {alerts.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Alerts</Label>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearAlerts}
              className="h-6 px-2 text-xs"
            >
              Clear All
            </Button>
          </div>
          <ScrollArea className="h-24">
            <div className="space-y-1">
              {alerts.slice(0, 5).map((alert) => (
                <div key={alert.id} className="flex items-start gap-2 p-2 bg-gray-50 rounded text-xs">
                  {getAlertIcon(alert.severity)}
                  <div className="flex-1">
                    <div className="font-medium">{alert.message}</div>
                    <div className="text-gray-500 text-xs">
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
};

// Automation Rules Component
const AutomationRules: React.FC<AutomationRulesProps> = ({
  rules,
  onToggleRule,
  onDeleteRule,
  onCreateRule
}) => {
  const [expandedRule, setExpandedRule] = useState<string | null>(null);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'productivity': return 'bg-blue-100 text-blue-800';
      case 'quality': return 'bg-green-100 text-green-800';
      case 'performance': return 'bg-yellow-100 text-yellow-800';
      case 'creative': return 'bg-purple-100 text-purple-800';
      case 'maintenance': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Automation Rules</Label>
        <Button
          variant="outline"
          size="sm"
          onClick={onCreateRule}
          className="h-7 px-2 text-xs"
        >
          <Plus className="w-3 h-3 mr-1" />
          Create Rule
        </Button>
      </div>

      {/* Rules List */}
      <ScrollArea className="h-48">
        <div className="space-y-2">
          {rules.map((rule) => (
            <Card key={rule.id} className="p-3">
              <div className="space-y-2">
                {/* Rule Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={rule.enabled}
                      onCheckedChange={(enabled) => onToggleRule(rule.id, enabled)}
                    />
                    <span className="text-xs font-medium">{rule.name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Badge className={`text-xs ${getCategoryColor(rule.category)}`}>
                      {rule.category}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setExpandedRule(
                        expandedRule === rule.id ? null : rule.id
                      )}
                      className="h-6 w-6 p-0"
                    >
                      {expandedRule === rule.id ? (
                        <ChevronDown className="w-3 h-3" />
                      ) : (
                        <ChevronRight className="w-3 h-3" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Rule Description */}
                <p className="text-xs text-gray-600">{rule.description}</p>

                {/* Rule Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Executed: {rule.executionCount}</span>
                  <span>Avg: {rule.averageExecutionTime.toFixed(1)}ms</span>
                </div>

                {/* Expanded Details */}
                {expandedRule === rule.id && (
                  <div className="mt-2 pt-2 border-t space-y-2">
                    <div className="text-xs">
                      <div className="font-medium">Triggers:</div>
                      <div className="text-gray-600">
                        {rule.triggers.map(t => t.name).join(', ')}
                      </div>
                    </div>
                    <div className="text-xs">
                      <div className="font-medium">Actions:</div>
                      <div className="text-gray-600">
                        {rule.actions.map(a => a.name).join(', ')}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 pt-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDeleteRule(rule.id)}
                        className="h-6 px-2 text-xs text-red-600"
                      >
                        <Trash2 className="w-3 h-3 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

// Main Advanced Operations Panel Component
export const AdvancedOperationsPanel: React.FC<AdvancedOperationsPanelProps> = ({
  advancedCanvas,
  className
}) => {
  const [activeTab, setActiveTab] = useState('operations');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [isMinimized, setIsMinimized] = useState(false);

  // Filter operations based on search and category
  const filteredOperations = useMemo(() => {
    return advancedCanvas.state.availableOperations.filter(op => {
      const matchesSearch = op.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           op.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = categoryFilter === 'all' || op.category === categoryFilter;
      return matchesSearch && matchesCategory;
    });
  }, [advancedCanvas.state.availableOperations, searchTerm, categoryFilter]);

  // Filter workflows based on search and category
  const filteredWorkflows = useMemo(() => {
    return advancedCanvas.state.availableWorkflows.filter(workflow => {
      const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           workflow.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = categoryFilter === 'all' || workflow.category === categoryFilter;
      return matchesSearch && matchesCategory;
    });
  }, [advancedCanvas.state.availableWorkflows, searchTerm, categoryFilter]);

  // Get unique categories
  const categories = useMemo(() => {
    const operationCategories = advancedCanvas.state.availableOperations.map(op => op.category);
    const workflowCategories = advancedCanvas.state.availableWorkflows.map(w => w.category);
    return Array.from(new Set([...operationCategories, ...workflowCategories]));
  }, [advancedCanvas.state.availableOperations, advancedCanvas.state.availableWorkflows]);

  // Event handlers
  const handleExecuteOperation = useCallback(async (operationId: string, parameters: Record<string, any>) => {
    try {
      await advancedCanvas.actions.executeOperation(operationId, parameters);
    } catch (error) {
      console.error('Failed to execute operation:', error);
    }
  }, [advancedCanvas.actions]);

  const handleExecuteWorkflow = useCallback(async (workflowId: string, parameters: Record<string, any>) => {
    try {
      await advancedCanvas.actions.executeWorkflow(workflowId, parameters);
    } catch (error) {
      console.error('Failed to execute workflow:', error);
    }
  }, [advancedCanvas.actions]);

  const handleProfileChange = useCallback(async (profileId: string) => {
    try {
      await advancedCanvas.actions.setPerformanceProfile(profileId);
    } catch (error) {
      console.error('Failed to change performance profile:', error);
    }
  }, [advancedCanvas.actions]);

  const handleClearAlerts = useCallback(() => {
    advancedCanvas.actions.clearPerformanceAlerts();
  }, [advancedCanvas.actions]);

  const handleToggleRule = useCallback((ruleId: string, enabled: boolean) => {
    advancedCanvas.actions.toggleAutomationRule(ruleId, enabled);
  }, [advancedCanvas.actions]);

  const handleDeleteRule = useCallback((ruleId: string) => {
    advancedCanvas.actions.deleteAutomationRule(ruleId);
  }, [advancedCanvas.actions]);

  const handleCreateRule = useCallback(() => {
    // Open rule creation dialog
    console.log('Create rule dialog not implemented');
  }, []);

  if (!advancedCanvas.isReady) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center justify-center">
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            <span className="text-sm">Initializing advanced operations...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              <CardTitle className="text-sm">Advanced Operations</CardTitle>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="h-6 w-6 p-0"
              >
                {isMinimized ? (
                  <Maximize className="w-3 h-3" />
                ) : (
                  <Minimize className="w-3 h-3" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
              >
                <Settings className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-4 pt-0">
            {/* Search and Filter */}
            <div className="space-y-3 mb-4">
              <div className="flex items-center gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
                  <Input
                    placeholder="Search operations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-7 h-7 text-xs"
                  />
                </div>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-24 h-7 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Status Bar */}
            {(advancedCanvas.state.isOperationInProgress || advancedCanvas.state.isWorkflowRunning) && (
              <div className="mb-4 p-3 bg-blue-50 rounded border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="w-3 h-3 animate-spin" />
                    <span className="text-xs font-medium">
                      {advancedCanvas.state.isWorkflowRunning ? 'Running Workflow' : 'Executing Operation'}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                  >
                    <Square className="w-3 h-3 mr-1" />
                    Cancel
                  </Button>
                </div>
                
                {advancedCanvas.state.currentOperation && (
                  <div className="mt-2 space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span>{advancedCanvas.state.currentOperation.name}</span>
                      <span>{advancedCanvas.state.currentOperation.stage}</span>
                    </div>
                    <Progress value={advancedCanvas.state.currentOperation.progress} className="h-1" />
                  </div>
                )}
              </div>
            )}

            {/* Error Display */}
            {advancedCanvas.state.error && (
              <Alert className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  {advancedCanvas.state.error}
                </AlertDescription>
              </Alert>
            )}

            {/* Main Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4 h-8">
                <TabsTrigger value="operations" className="text-xs">Operations</TabsTrigger>
                <TabsTrigger value="workflows" className="text-xs">Workflows</TabsTrigger>
                <TabsTrigger value="performance" className="text-xs">Performance</TabsTrigger>
                <TabsTrigger value="automation" className="text-xs">Automation</TabsTrigger>
              </TabsList>

              {/* Operations Tab */}
              <TabsContent value="operations" className="mt-4">
                <ScrollArea className="h-96">
                  <div className="grid gap-3">
                    {filteredOperations.map((operation) => (
                      <OperationCard
                        key={operation.id}
                        operation={operation}
                        onExecute={handleExecuteOperation}
                        isExecuting={advancedCanvas.state.isOperationInProgress}
                        disabled={!advancedCanvas.isReady}
                      />
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>

              {/* Workflows Tab */}
              <TabsContent value="workflows" className="mt-4">
                <ScrollArea className="h-96">
                  <div className="grid gap-3">
                    {filteredWorkflows.map((workflow) => (
                      <WorkflowCard
                        key={workflow.id}
                        workflow={workflow}
                        onExecute={handleExecuteWorkflow}
                        isExecuting={advancedCanvas.state.isWorkflowRunning}
                        disabled={!advancedCanvas.isReady}
                      />
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>

              {/* Performance Tab */}
              <TabsContent value="performance" className="mt-4">
                <ScrollArea className="h-96">
                  <PerformanceMetrics
                    metrics={advancedCanvas.state.performanceMetrics}
                    profile={advancedCanvas.state.performanceProfile}
                    alerts={advancedCanvas.state.performanceAlerts}
                    onProfileChange={handleProfileChange}
                    onClearAlerts={handleClearAlerts}
                  />
                </ScrollArea>
              </TabsContent>

              {/* Automation Tab */}
              <TabsContent value="automation" className="mt-4">
                <ScrollArea className="h-96">
                  <AutomationRules
                    rules={advancedCanvas.state.automationRules}
                    onToggleRule={handleToggleRule}
                    onDeleteRule={handleDeleteRule}
                    onCreateRule={handleCreateRule}
                  />
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </CardContent>
        )}
      </Card>
    </TooltipProvider>
  );
};

export default AdvancedOperationsPanel;