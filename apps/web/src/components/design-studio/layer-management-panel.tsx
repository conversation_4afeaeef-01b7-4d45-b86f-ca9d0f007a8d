'use client';

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { fabric } from 'fabric';
import * as PIXI from 'pixi.js';
import { 
  LayerNode, 
  LayerType, 
  BlendMode, 
  LayerEffect, 
  CreateLayerOptions,
  LayerFilterCriteria,
  LayerSearchOptions
} from '../../lib/canvas/advanced-layer-manager';
import { useLayerManagement, useLayerSearch, useLayerSelection } from '../../hooks/use-layer-management';
import { 
  Eye, 
  EyeOff, 
  Lock, 
  Unlock, 
  ChevronDown, 
  ChevronRight, 
  Search, 
  Plus, 
  Trash2, 
  Copy, 
  Move3D, 
  Layers,
  MoreHorizontal,
  Settings,
  Filter,
  Archive,
  FolderPlus,
  Ungroup,
  Group,
  ArrowUp,
  ArrowDown,
  Sliders,
  Paintbrush,
  Type,
  Image,
  Square,
  Circle,
  Star,
  X
} from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Switch } from '../ui/switch';
import { Separator } from '../ui/separator';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '../ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { ScrollArea } from '../ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';

// Layer management panel props
interface LayerManagementPanelProps {
  fabricCanvas: fabric.Canvas | null;
  pixiApp?: PIXI.Application | null;
  className?: string;
  onLayerSelect?: (layerId: string) => void;
  onLayerCreate?: (layer: LayerNode) => void;
  onLayerDelete?: (layerId: string) => void;
  enableHybridRendering?: boolean;
}

// Layer item component
interface LayerItemProps {
  layer: LayerNode;
  isSelected: boolean;
  isActive: boolean;
  depth: number;
  onSelect: (layerId: string, addToSelection?: boolean) => void;
  onToggleVisibility: (layerId: string) => void;
  onToggleLock: (layerId: string) => void;
  onRename: (layerId: string, newName: string) => void;
  onDelete: (layerId: string) => void;
  onDuplicate: (layerId: string) => void;
  onMove: (layerId: string, direction: 'up' | 'down') => void;
  children?: React.ReactNode;
}

// Layer type icons
const getLayerTypeIcon = (type: LayerType, size: number = 16) => {
  const iconProps = { size, className: "text-gray-500" };
  
  switch (type) {
    case 'text':
      return <Type {...iconProps} />;
    case 'image':
      return <Image {...iconProps} />;
    case 'shape':
      return <Square {...iconProps} />;
    case 'group':
      return <Layers {...iconProps} />;
    case 'mask':
      return <Circle {...iconProps} />;
    case 'effect':
      return <Paintbrush {...iconProps} />;
    default:
      return <Square {...iconProps} />;
  }
};

// Blend mode selector component
interface BlendModeSelectorProps {
  value: BlendMode;
  onChange: (value: BlendMode) => void;
}

const BlendModeSelector: React.FC<BlendModeSelectorProps> = ({ value, onChange }) => {
  const blendModes: BlendMode[] = [
    'normal', 'multiply', 'screen', 'overlay', 'soft-light', 'hard-light',
    'color-dodge', 'color-burn', 'darken', 'lighten', 'difference', 'exclusion',
    'hue', 'saturation', 'color', 'luminosity', 'add', 'subtract', 'pass-through'
  ];

  return (
    <select 
      value={value} 
      onChange={(e) => onChange(e.target.value as BlendMode)}
      className="w-full p-2 border border-gray-300 rounded-md text-sm"
    >
      {blendModes.map(mode => (
        <option key={mode} value={mode}>
          {mode.charAt(0).toUpperCase() + mode.slice(1).replace('-', ' ')}
        </option>
      ))}
    </select>
  );
};

// Layer effects panel
interface LayerEffectsPanelProps {
  layerId: string;
  effects: LayerEffect[];
  onAddEffect: (layerId: string, effect: LayerEffect) => void;
  onRemoveEffect: (layerId: string, effectId: string) => void;
  onUpdateEffect: (layerId: string, effectId: string, updates: Partial<LayerEffect>) => void;
}

const LayerEffectsPanel: React.FC<LayerEffectsPanelProps> = ({
  layerId,
  effects,
  onAddEffect,
  onRemoveEffect,
  onUpdateEffect
}) => {
  const [showEffectDialog, setShowEffectDialog] = useState(false);

  const addDropShadow = () => {
    const effect: LayerEffect = {
      id: `effect_${Date.now()}`,
      type: 'drop-shadow',
      enabled: true,
      opacity: 0.75,
      blendMode: 'normal',
      parameters: {
        color: '#000000',
        blur: 4,
        offsetX: 4,
        offsetY: 4,
        spread: 0
      }
    };
    onAddEffect(layerId, effect);
  };

  const addOuterGlow = () => {
    const effect: LayerEffect = {
      id: `effect_${Date.now()}`,
      type: 'outer-glow',
      enabled: true,
      opacity: 0.75,
      blendMode: 'normal',
      parameters: {
        color: '#ffffff',
        size: 8,
        spread: 0,
        noise: 0
      }
    };
    onAddEffect(layerId, effect);
  };

  const addStroke = () => {
    const effect: LayerEffect = {
      id: `effect_${Date.now()}`,
      type: 'stroke',
      enabled: true,
      opacity: 1.0,
      blendMode: 'normal',
      parameters: {
        color: '#000000',
        width: 2,
        position: 'outside'
      }
    };
    onAddEffect(layerId, effect);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Effects</Label>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Plus size={12} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={addDropShadow}>
              Drop Shadow
            </DropdownMenuItem>
            <DropdownMenuItem onClick={addOuterGlow}>
              Outer Glow
            </DropdownMenuItem>
            <DropdownMenuItem onClick={addStroke}>
              Stroke
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {effects.map((effect) => (
        <Card key={effect.id} className="p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <Switch
                checked={effect.enabled}
                onCheckedChange={(enabled) => 
                  onUpdateEffect(layerId, effect.id, { enabled })
                }
              />
              <span className="text-sm font-medium">
                {effect.type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onRemoveEffect(layerId, effect.id)}
            >
              <Trash2 size={12} />
            </Button>
          </div>

          <div className="space-y-2">
            <div>
              <Label className="text-xs">Opacity</Label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={effect.opacity}
                onChange={(e) => 
                  onUpdateEffect(layerId, effect.id, { opacity: parseFloat(e.target.value) })
                }
                className="w-full"
              />
              <span className="text-xs text-gray-500">{Math.round(effect.opacity * 100)}%</span>
            </div>

            {effect.type === 'drop-shadow' && (
              <>
                <div>
                  <Label className="text-xs">Color</Label>
                  <input
                    type="color"
                    value={effect.parameters.color}
                    onChange={(e) =>
                      onUpdateEffect(layerId, effect.id, {
                        parameters: { ...effect.parameters, color: e.target.value }
                      })
                    }
                    className="w-full h-8 rounded"
                  />
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">Blur</Label>
                    <Input
                      type="number"
                      value={effect.parameters.blur}
                      onChange={(e) =>
                        onUpdateEffect(layerId, effect.id, {
                          parameters: { ...effect.parameters, blur: parseInt(e.target.value) }
                        })
                      }
                      className="h-8"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Distance</Label>
                    <Input
                      type="number"
                      value={Math.sqrt(effect.parameters.offsetX ** 2 + effect.parameters.offsetY ** 2)}
                      onChange={(e) => {
                        const distance = parseInt(e.target.value);
                        const angle = Math.atan2(effect.parameters.offsetY, effect.parameters.offsetX);
                        onUpdateEffect(layerId, effect.id, {
                          parameters: {
                            ...effect.parameters,
                            offsetX: Math.cos(angle) * distance,
                            offsetY: Math.sin(angle) * distance
                          }
                        });
                      }}
                      className="h-8"
                    />
                  </div>
                </div>
              </>
            )}

            {effect.type === 'outer-glow' && (
              <>
                <div>
                  <Label className="text-xs">Color</Label>
                  <input
                    type="color"
                    value={effect.parameters.color}
                    onChange={(e) =>
                      onUpdateEffect(layerId, effect.id, {
                        parameters: { ...effect.parameters, color: e.target.value }
                      })
                    }
                    className="w-full h-8 rounded"
                  />
                </div>
                <div>
                  <Label className="text-xs">Size</Label>
                  <input
                    type="range"
                    min="0"
                    max="50"
                    value={effect.parameters.size}
                    onChange={(e) =>
                      onUpdateEffect(layerId, effect.id, {
                        parameters: { ...effect.parameters, size: parseInt(e.target.value) }
                      })
                    }
                    className="w-full"
                  />
                  <span className="text-xs text-gray-500">{effect.parameters.size}px</span>
                </div>
              </>
            )}

            {effect.type === 'stroke' && (
              <>
                <div>
                  <Label className="text-xs">Color</Label>
                  <input
                    type="color"
                    value={effect.parameters.color}
                    onChange={(e) =>
                      onUpdateEffect(layerId, effect.id, {
                        parameters: { ...effect.parameters, color: e.target.value }
                      })
                    }
                    className="w-full h-8 rounded"
                  />
                </div>
                <div>
                  <Label className="text-xs">Width</Label>
                  <input
                    type="range"
                    min="1"
                    max="20"
                    value={effect.parameters.width}
                    onChange={(e) =>
                      onUpdateEffect(layerId, effect.id, {
                        parameters: { ...effect.parameters, width: parseInt(e.target.value) }
                      })
                    }
                    className="w-full"
                  />
                  <span className="text-xs text-gray-500">{effect.parameters.width}px</span>
                </div>
              </>
            )}
          </div>
        </Card>
      ))}
    </div>
  );
};

// Layer item component
const LayerItem: React.FC<LayerItemProps> = ({
  layer,
  isSelected,
  isActive,
  depth,
  onSelect,
  onToggleVisibility,
  onToggleLock,
  onRename,
  onDelete,
  onDuplicate,
  onMove,
  children
}) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [editName, setEditName] = useState(layer.name);
  const [isExpanded, setIsExpanded] = useState(true);

  const handleRename = () => {
    if (editName.trim() && editName !== layer.name) {
      onRename(layer.id, editName.trim());
    }
    setIsRenaming(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setEditName(layer.name);
      setIsRenaming(false);
    }
  };

  return (
    <div className="select-none">
      <div
        className={`
          flex items-center py-1 px-2 rounded cursor-pointer transition-colors
          ${isSelected ? 'bg-blue-100 border border-blue-300' : 'hover:bg-gray-50'}
          ${isActive ? 'ring-2 ring-blue-500' : ''}
        `}
        style={{ paddingLeft: `${depth * 16 + 8}px` }}
        onClick={(e) => onSelect(layer.id, e.ctrlKey || e.metaKey)}
        onDoubleClick={() => setIsRenaming(true)}
      >
        {/* Expand/Collapse for groups */}
        {layer.type === 'group' && (
          <Button
            variant="ghost"
            size="sm"
            className="w-4 h-4 p-0 mr-1"
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
          >
            {isExpanded ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
          </Button>
        )}

        {/* Layer type icon */}
        <div className="mr-2">
          {getLayerTypeIcon(layer.type, 14)}
        </div>

        {/* Layer name */}
        <div className="flex-1 min-w-0">
          {isRenaming ? (
            <Input
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleRename}
              onKeyDown={handleKeyPress}
              className="h-6 text-sm"
              autoFocus
            />
          ) : (
            <span className="text-sm truncate">{layer.name}</span>
          )}
        </div>

        {/* Layer effects indicator */}
        {layer.effects.length > 0 && (
          <Badge variant="secondary" className="text-xs mr-1">
            {layer.effects.length} FX
          </Badge>
        )}

        {/* Blend mode indicator */}
        {layer.blendMode !== 'normal' && (
          <Badge variant="outline" className="text-xs mr-1">
            {layer.blendMode}
          </Badge>
        )}

        {/* Lock button */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="w-6 h-6 p-0 mr-1"
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleLock(layer.id);
                }}
              >
                {layer.locked ? <Lock size={12} /> : <Unlock size={12} />}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {layer.locked ? 'Unlock layer' : 'Lock layer'}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Visibility button */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="w-6 h-6 p-0 mr-1"
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleVisibility(layer.id);
                }}
              >
                {layer.visible ? <Eye size={12} /> : <EyeOff size={12} />}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {layer.visible ? 'Hide layer' : 'Show layer'}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* More options menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="w-6 h-6 p-0">
              <MoreHorizontal size={12} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onDuplicate(layer.id)}>
              <Copy size={14} className="mr-2" />
              Duplicate
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setIsRenaming(true)}>
              <Type size={14} className="mr-2" />
              Rename
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onMove(layer.id, 'up')}>
              <ArrowUp size={14} className="mr-2" />
              Move Up
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onMove(layer.id, 'down')}>
              <ArrowDown size={14} className="mr-2" />
              Move Down
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => onDelete(layer.id)}
              className="text-red-600"
            >
              <Trash2 size={14} className="mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Child layers for groups */}
      {layer.type === 'group' && isExpanded && children}
    </div>
  );
};

// Main layer management panel component
export const LayerManagementPanel: React.FC<LayerManagementPanelProps> = ({
  fabricCanvas,
  pixiApp,
  className = '',
  onLayerSelect,
  onLayerCreate,
  onLayerDelete,
  enableHybridRendering = true
}) => {
  const {
    state,
    actions,
    performance,
    layerManager,
    hybridIntegration,
    isReady
  } = useLayerManagement(fabricCanvas, pixiApp, { enableHybridRendering });

  const {
    searchQuery,
    setSearchQuery,
    searchResults,
    clearSearch
  } = useLayerSearch(layerManager);

  const [activeTab, setActiveTab] = useState('layers');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [filterCriteria, setFilterCriteria] = useState<LayerFilterCriteria>({});
  const [showFilters, setShowFilters] = useState(false);

  // Create new layer dialog state
  const [newLayerType, setNewLayerType] = useState<LayerType>('shape');
  const [newLayerName, setNewLayerName] = useState('');

  // Filtered and searched layers
  const displayLayers = useMemo(() => {
    if (searchQuery.trim()) {
      return searchResults;
    }
    
    if (Object.keys(filterCriteria).length > 0) {
      return actions.filterLayers(filterCriteria);
    }
    
    return Array.from(state.layers.values());
  }, [searchQuery, searchResults, filterCriteria, state.layers, actions]);

  // Handle layer creation
  const handleCreateLayer = useCallback(async () => {
    if (!newLayerName.trim()) return;

    try {
      const options: CreateLayerOptions = {
        type: newLayerType,
        name: newLayerName.trim(),
        visible: true,
        locked: false,
        opacity: 1.0
      };

      const layer = await actions.createLayer(options);
      
      setShowCreateDialog(false);
      setNewLayerName('');
      onLayerCreate?.(layer);
    } catch (error) {
      console.error('Failed to create layer:', error);
    }
  }, [newLayerType, newLayerName, actions, onLayerCreate]);

  // Handle layer property updates
  const handleLayerPropertyUpdate = useCallback(async (
    layerId: string,
    property: keyof LayerNode,
    value: any
  ) => {
    try {
      await actions.updateLayerProperty(layerId, property, value);
    } catch (error) {
      console.error('Failed to update layer property:', error);
    }
  }, [actions]);

  // Render layer tree recursively
  const renderLayerTree = useCallback((layerIds: string[], depth = 0): React.ReactNode => {
    return layerIds.map(layerId => {
      const layer = state.layers.get(layerId);
      if (!layer) return null;

      const isSelected = state.selectedLayers.has(layerId);
      const isActive = state.activeLayer === layerId;

      return (
        <LayerItem
          key={layerId}
          layer={layer}
          isSelected={isSelected}
          isActive={isActive}
          depth={depth}
          onSelect={(id, addToSelection) => {
            actions.selectLayer(id, addToSelection);
            onLayerSelect?.(id);
          }}
          onToggleVisibility={(id) => {
            const currentLayer = state.layers.get(id);
            if (currentLayer) {
              handleLayerPropertyUpdate(id, 'visible', !currentLayer.visible);
            }
          }}
          onToggleLock={(id) => {
            const currentLayer = state.layers.get(id);
            if (currentLayer) {
              handleLayerPropertyUpdate(id, 'locked', !currentLayer.locked);
            }
          }}
          onRename={(id, newName) => {
            handleLayerPropertyUpdate(id, 'name', newName);
          }}
          onDelete={(id) => {
            actions.deleteLayer(id);
            onLayerDelete?.(id);
          }}
          onDuplicate={(id) => {
            actions.duplicateLayer(id);
          }}
          onMove={(id, direction) => {
            // Implement layer reordering logic
            console.log('Move layer', id, direction);
          }}
        >
          {layer.children.length > 0 && renderLayerTree(layer.children, depth + 1)}
        </LayerItem>
      );
    });
  }, [state.layers, state.selectedLayers, state.activeLayer, actions, onLayerSelect, onLayerDelete, handleLayerPropertyUpdate]);

  if (!isReady) {
    return (
      <div className={`bg-white border-r border-gray-200 ${className}`}>
        <div className="p-4 text-center text-gray-500">
          Loading layer manager...
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={`bg-white border-r border-gray-200 flex flex-col h-full ${className}`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold flex items-center">
              <Layers className="mr-2" size={20} />
              Layers
            </h3>
            <div className="flex space-x-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    <Filter size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Toggle filters</TooltipContent>
              </Tooltip>
              
              <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                <DialogTrigger asChild>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Plus size={16} />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Create new layer</TooltipContent>
                  </Tooltip>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Layer</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label>Layer Type</Label>
                      <select
                        value={newLayerType}
                        onChange={(e) => setNewLayerType(e.target.value as LayerType)}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="shape">Shape</option>
                        <option value="text">Text</option>
                        <option value="image">Image</option>
                        <option value="group">Group</option>
                        <option value="mask">Mask</option>
                      </select>
                    </div>
                    <div>
                      <Label>Layer Name</Label>
                      <Input
                        value={newLayerName}
                        onChange={(e) => setNewLayerName(e.target.value)}
                        placeholder="Enter layer name"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleCreateLayer();
                        }}
                      />
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => setShowCreateDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handleCreateLayer} disabled={!newLayerName.trim()}>
                        Create
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <Input
              placeholder="Search layers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
                onClick={clearSearch}
              >
                <X size={14} />
              </Button>
            )}
          </div>

          {/* Filters */}
          {showFilters && (
            <Card className="mt-3">
              <CardContent className="p-3 space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">Visible</Label>
                    <Switch
                      checked={filterCriteria.visible ?? true}
                      onCheckedChange={(visible) => 
                        setFilterCriteria(prev => ({ ...prev, visible }))
                      }
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Locked</Label>
                    <Switch
                      checked={filterCriteria.locked ?? false}
                      onCheckedChange={(locked) => 
                        setFilterCriteria(prev => ({ ...prev, locked }))
                      }
                    />
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilterCriteria({})}
                  className="w-full"
                >
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="grid grid-cols-3 mx-4 mt-2">
            <TabsTrigger value="layers">Layers</TabsTrigger>
            <TabsTrigger value="properties">Properties</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          {/* Layer Tree */}
          <TabsContent value="layers" className="flex-1 flex flex-col mt-2">
            <ScrollArea className="flex-1">
              <div className="p-2">
                {state.layerCount === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    <Layers size={48} className="mx-auto mb-4 text-gray-300" />
                    <p>No layers yet</p>
                    <p className="text-sm">Create your first layer to get started</p>
                  </div>
                ) : (
                  renderLayerTree(searchQuery ? displayLayers.map(l => l.id) : state.rootLayers)
                )}
              </div>
            </ScrollArea>

            {/* Layer actions */}
            <div className="border-t border-gray-200 p-2">
              <div className="flex space-x-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const selectedIds = Array.from(state.selectedLayers);
                        if (selectedIds.length > 1) {
                          actions.createLayerGroup(selectedIds);
                        }
                      }}
                      disabled={state.selectedLayers.size < 2}
                    >
                      <Group size={14} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Group selected layers</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const activeLayer = state.activeLayer;
                        if (activeLayer) {
                          const layer = state.layers.get(activeLayer);
                          if (layer?.type === 'group') {
                            actions.ungroupLayers(activeLayer);
                          }
                        }
                      }}
                      disabled={!state.activeLayer || state.layers.get(state.activeLayer!)?.type !== 'group'}
                    >
                      <Ungroup size={14} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Ungroup layers</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const selectedIds = Array.from(state.selectedLayers);
                        actions.duplicateLayers(selectedIds);
                      }}
                      disabled={state.selectedLayers.size === 0}
                    >
                      <Copy size={14} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Duplicate selected layers</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const selectedIds = Array.from(state.selectedLayers);
                        actions.deleteLayers(selectedIds);
                      }}
                      disabled={state.selectedLayers.size === 0}
                    >
                      <Trash2 size={14} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Delete selected layers</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </TabsContent>

          {/* Properties Panel */}
          <TabsContent value="properties" className="flex-1 p-4">
            {state.activeLayer ? (
              <LayerPropertiesPanel
                layer={state.layers.get(state.activeLayer)!}
                onPropertyUpdate={handleLayerPropertyUpdate}
                onAddEffect={(effect) => actions.addLayerEffect(state.activeLayer!, effect)}
                onRemoveEffect={(effectId) => actions.removeLayerEffect(state.activeLayer!, effectId)}
                onUpdateEffect={(effectId, updates) => actions.updateLayerEffect(state.activeLayer!, effectId, updates)}
              />
            ) : (
              <div className="text-center text-gray-500 py-8">
                <Settings size={48} className="mx-auto mb-4 text-gray-300" />
                <p>No layer selected</p>
                <p className="text-sm">Select a layer to edit its properties</p>
              </div>
            )}
          </TabsContent>

          {/* Performance Panel */}
          <TabsContent value="performance" className="flex-1 p-4">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Rendering Performance</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Cache Hit Rate:</span>
                    <span>{Math.round(performance.cacheHitRate * 100)}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Memory Usage:</span>
                    <span>{Math.round(performance.memoryUsage / 1024 / 1024)}MB</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Render Time:</span>
                    <span>{performance.renderTime.toFixed(1)}ms</span>
                  </div>
                </CardContent>
              </Card>

              {enableHybridRendering && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Engine Distribution</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Fabric.js Layers:</span>
                      <span>{performance.engineDistribution.fabric}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>PIXI.js Layers:</span>
                      <span>{performance.engineDistribution.pixi}</span>
                    </div>
                    <div className="pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => hybridIntegration?.optimizeForPerformance()}
                        className="w-full"
                      >
                        Optimize Performance
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Layer Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Total Layers:</span>
                    <span>{state.layerCount}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Selected:</span>
                    <span>{state.selectedLayers.size}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Root Layers:</span>
                    <span>{state.rootLayers.length}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </TooltipProvider>
  );
};

// Layer properties panel component
interface LayerPropertiesPanelProps {
  layer: LayerNode;
  onPropertyUpdate: (layerId: string, property: keyof LayerNode, value: any) => void;
  onAddEffect: (effect: LayerEffect) => void;
  onRemoveEffect: (effectId: string) => void;
  onUpdateEffect: (effectId: string, updates: Partial<LayerEffect>) => void;
}

const LayerPropertiesPanel: React.FC<LayerPropertiesPanelProps> = ({
  layer,
  onPropertyUpdate,
  onAddEffect,
  onRemoveEffect,
  onUpdateEffect
}) => {
  return (
    <ScrollArea className="h-full">
      <div className="space-y-4">
        {/* Basic Properties */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Basic Properties</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm">Name</Label>
              <Input
                value={layer.name}
                onChange={(e) => onPropertyUpdate(layer.id, 'name', e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label className="text-sm">Opacity</Label>
              <div className="mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={layer.opacity}
                  onChange={(e) => onPropertyUpdate(layer.id, 'opacity', parseFloat(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0%</span>
                  <span>{Math.round(layer.opacity * 100)}%</span>
                  <span>100%</span>
                </div>
              </div>
            </div>

            <div>
              <Label className="text-sm">Blend Mode</Label>
              <div className="mt-1">
                <BlendModeSelector
                  value={layer.blendMode}
                  onChange={(value) => onPropertyUpdate(layer.id, 'blendMode', value)}
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={layer.visible}
                  onCheckedChange={(visible) => onPropertyUpdate(layer.id, 'visible', visible)}
                />
                <Label className="text-sm">Visible</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={layer.locked}
                  onCheckedChange={(locked) => onPropertyUpdate(layer.id, 'locked', locked)}
                />
                <Label className="text-sm">Locked</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Transform Properties */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Transform</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-sm">X Position</Label>
                <Input
                  type="number"
                  value={layer.transform.x}
                  onChange={(e) => onPropertyUpdate(layer.id, 'transform', {
                    ...layer.transform,
                    x: parseFloat(e.target.value) || 0
                  })}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-sm">Y Position</Label>
                <Input
                  type="number"
                  value={layer.transform.y}
                  onChange={(e) => onPropertyUpdate(layer.id, 'transform', {
                    ...layer.transform,
                    y: parseFloat(e.target.value) || 0
                  })}
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-sm">Scale X</Label>
                <Input
                  type="number"
                  step="0.1"
                  value={layer.transform.scaleX}
                  onChange={(e) => onPropertyUpdate(layer.id, 'transform', {
                    ...layer.transform,
                    scaleX: parseFloat(e.target.value) || 1
                  })}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-sm">Scale Y</Label>
                <Input
                  type="number"
                  step="0.1"
                  value={layer.transform.scaleY}
                  onChange={(e) => onPropertyUpdate(layer.id, 'transform', {
                    ...layer.transform,
                    scaleY: parseFloat(e.target.value) || 1
                  })}
                  className="mt-1"
                />
              </div>
            </div>

            <div>
              <Label className="text-sm">Rotation (degrees)</Label>
              <Input
                type="number"
                value={layer.transform.rotation}
                onChange={(e) => onPropertyUpdate(layer.id, 'transform', {
                  ...layer.transform,
                  rotation: parseFloat(e.target.value) || 0
                })}
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>

        {/* Layer Effects */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Layer Effects</CardTitle>
          </CardHeader>
          <CardContent>
            <LayerEffectsPanel
              layerId={layer.id}
              effects={layer.effects}
              onAddEffect={onAddEffect}
              onRemoveEffect={onRemoveEffect}
              onUpdateEffect={onUpdateEffect}
            />
          </CardContent>
        </Card>

        {/* Metadata */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Metadata</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-xs text-gray-500">
              <div>Created: {new Date(layer.metadata.createdAt).toLocaleDateString()}</div>
              <div>Updated: {new Date(layer.metadata.updatedAt).toLocaleDateString()}</div>
              <div>Z-Index: {layer.zIndex}</div>
              {layer.metadata.tags.length > 0 && (
                <div>Tags: {layer.metadata.tags.join(', ')}</div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
};

export default LayerManagementPanel;