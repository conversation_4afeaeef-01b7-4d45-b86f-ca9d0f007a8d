'use client';

import React, { useState, useEffect, useC<PERSON>back, useMemo } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Play,
  Pause,
  Square,
  RefreshCw,
  Settings,
  Image as ImageIcon,
  Zap,
  Clock,
  DollarSign,
  Star,
  ChevronRight,
  ChevronDown,
  Eye,
  Download,
  Upload,
  Trash2,
  Info,
  AlertTriangle,
  CheckCircle,
  X,
  Plus,
  Minus,
  RotateCcw,
  Maximize2,
  Minimize2,
  Copy,
  Save,
  Share2,
  Filter,
  Layers,
  Sliders,
  Sparkles,
  Target,
  Timer,
  TrendingUp,
  Wrench,
  Palette,
  Contrast,
  Brightness,
  Blur,
  Focus,
  Brush,
  Eraser,
  Crop,
  Move,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Grid,
  Maximize,
  MoreHorizontal,
  FileImage,
  Layers3,
  Wand2,
  Gauge,
  BarChart3,
  Calendar,
  Users,
  Heart,
  MessageCircle,
  Bookmark,
  Share,
  ArrowUpDown,
  ArrowLeftRight,
  ZoomIn,
  ZoomOut,
  RotateClockwise,
  RotateCounterClockwise,
  Expand,
  Shrink,
  PaintBucket,
  Droplets,
  Sun,
  Moon,
  Lightbulb,
  Scissors,
  Pipette,
  Paintbrush,
  Stamp,
  Flame,
  Snowflake,
  Waves,
  Mountain,
  Feather,
  Gem,
  Leaf,
  Flower,
  Bug,
  Zap as Lightning,
  Sparkles as Stars,
  Wand as Magic,
  Cpu,
  HardDrive,
  Wifi,
  Signal,
  Battery,
  Power,
  Database,
  Server,
  Cloud,
  Shield,
  Lock,
  Unlock,
  Key,
  Search,
  Filter as FilterIcon,
  SortAsc,
  SortDesc,
  List,
  Grid3x3,
  LayoutGrid,
  Columns,
  Rows,
  MoreVertical,
  Menu,
  Home,
  User,
  Settings as SettingsIcon,
  Help,
  LogOut,
  Bell,
  Mail,
  Phone,
  MapPin,
  Globe,
  Link,
  ExternalLink,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ChevronsUp,
  ChevronsDown,
  ChevronsLeft,
  ChevronsRight,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Camera,
  CameraOff,
  Monitor,
  Smartphone,
  Tablet,
  Laptop,
  Desktop,
  Headphones,
  Speaker,
  Gamepad2,
  Keyboard,
  Mouse,
  Printer,
  Scanner,
  Fax,
  Tv,
  Radio,
  Bluetooth,
  Rss,
  Wifi as WifiIcon,
  Activity,
  Airplay,
  Archive,
  ArchiveX,
  Briefcase,
  Building,
  Calculator,
  CalendarDays,
  CreditCard,
  Flag,
  Folder,
  FolderOpen,
  Hash,
  Inbox,
  Layers2,
  LifeBuoy,
  Loader,
  Loader2,
  Map,
  Paperclip,
  PenTool,
  Percent,
  PieChart,
  Presentation,
  Receipt,
  Repeat,
  Scan,
  ShoppingCart,
  Truck,
  Umbrella,
  User2,
  UserCheck,
  UserMinus,
  UserPlus,
  UserX,
  Users2,
  Wallet,
  Warehouse,
  Wrench as WrenchIcon,
  Zap as ZapIcon,
  Smile,
  Frown,
  Meh,
  Angry,
  Annoyed,
  Confused,
  Expressionless,
  Grimacing,
  Grin,
  Grinning,
  GrinningSquint,
  Kiss,
  Laugh,
  Neutral,
  Sad,
  Satisfied,
  Surprised,
  Thinking,
  ThumbsUp,
  ThumbsDown,
  Thumbs,
  Trophy,
  Award,
  Medal,
  Crown,
  Gem as GemIcon,
  Diamond,
  Star as StarIcon,
  Gift,
  PartyPopper,
  Cake,
  Candy,
  Coffee,
  Wine,
  Beer,
  Martini,
  Champagne,
  Milk,
  Egg,
  Meat,
  Bread,
  Cheese,
  Pizza,
  Burger,
  Fries,
  Donut,
  Cookie,
  Chocolate,
  Lollipop,
  Honey,
  Salt,
  Pepper,
  Spice,
  Herb,
  Leaf as LeafIcon,
  Tree,
  Evergreen,
  Deciduous,
  Palm,
  Cactus,
  Flower as FlowerIcon,
  Rose,
  Tulip,
  Daisy,
  Sunflower,
  Lily,
  Orchid,
  Hibiscus,
  Cherry,
  Grape,
  Apple,
  Orange,
  Banana,
  Pineapple,
  Strawberry,
  Watermelon,
  Melon,
  Kiwi,
  Peach,
  Pear,
  Plum,
  Avocado,
  Tomato,
  Eggplant,
  Corn,
  Carrot,
  Potato,
  Onion,
  Garlic,
  Ginger,
  Chili,
  Broccoli,
  Cabbage,
  Lettuce,
  Spinach,
  Mushroom,
  Pumpkin,
  Squash,
  Cucumber,
  Pickle,
  Olive,
  Coconut,
  Almond,
  Peanut,
  Walnut,
  Hazelnut,
  Pistachio,
  Cashew,
  Seed,
  Sprout,
  Grain,
  Rice,
  Wheat,
  Oats,
  Barley,
  Rye,
  Quinoa,
  Pasta,
  Noodles,
  Soup,
  Salad,
  Sandwich,
  Wrap,
  Taco,
  Burrito,
  Sushi,
  Ramen,
  Curry,
  Stew,
  Grill,
  Bbq,
  Roast,
  Bake,
  Fry,
  Steam,
  Boil,
  Microwave,
  Oven,
  Stove,
  Fridge,
  Freezer,
  Dishwasher,
  Washer,
  Dryer,
  Iron,
  Vacuum,
  Broom,
  Mop,
  Bucket,
  Soap,
  Sponge,
  Towel,
  Tissue,
  Toilet,
  Shower,
  Bath,
  Sink,
  Faucet,
  Drain,
  Pipe,
  Valve,
  Meter,
  Gauge as GaugeIcon,
  Thermometer,
  Barometer,
  Hygrometer,
  Anemometer,
  Compass,
  Gps,
  Satellite,
  Radar,
  Sonar,
  Telescope,
  Microscope,
  Magnifier,
  Binoculars,
  Glasses,
  Sunglasses,
  Contacts,
  Monocle,
  Eyepatch,
  Blindfold,
  Mask,
  Helmet,
  Hat,
  Cap,
  Beret,
  Beanie,
  Headband,
  Turban,
  Veil,
  Scarf,
  Tie,
  Bowtie,
  Collar,
  Necklace,
  Pendant,
  Earring,
  Ring,
  Bracelet,
  Watch,
  Clock,
  Stopwatch,
  Timer as TimerIcon,
  Hourglass,
  Sundial,
  Metronome,
  Pendulum,
  Spring,
  Gear,
  Cog,
  Screw,
  Bolt,
  Nut,
  Nail,
  Hammer,
  Screwdriver,
  Wrench as WrenchTool,
  Pliers,
  Saw,
  Drill,
  Chisel,
  File,
  Sandpaper,
  Plane,
  Level,
  Ruler,
  Tape,
  Caliper,
  Protractor,
  Square,
  Triangle,
  Circle,
  Oval,
  Rectangle,
  Rhombus,
  Pentagon,
  Hexagon,
  Octagon,
  Star as StarShape,
  Heart as HeartShape,
  Diamond as DiamondShape,
  Spade,
  Club,
  Cross,
  Checkmark,
  X as XMark,
  Plus as PlusSign,
  Minus as MinusSign,
  Multiply,
  Divide,
  Equal,
  NotEqual,
  LessThan,
  GreaterThan,
  LessEqual,
  GreaterEqual,
  Infinity,
  Pi,
  Sigma,
  Alpha,
  Beta,
  Gamma,
  Delta,
  Epsilon,
  Zeta,
  Eta,
  Theta,
  Iota,
  Kappa,
  Lambda,
  Mu,
  Nu,
  Xi,
  Omicron,
  Pi as PiSymbol,
  Rho,
  Tau,
  Upsilon,
  Phi,
  Chi,
  Psi,
  Omega,
  Alef,
  Bet,
  Gimel,
  Dalet,
  He,
  Vav,
  Zayin,
  Het,
  Tet,
  Yod,
  Kaf,
  Lamed,
  Mem,
  Nun,
  Samekh,
  Ayin,
  Pe,
  Tsadi,
  Qof,
  Resh,
  Shin,
  Tav
} from 'lucide-react';

import { 
  EnhancementType, 
  EnhancementParameters, 
  EnhancementResult,
  QualityAssessment,
  EnhancementRecommendation,
  EnhancementModel
} from '@/lib/ai/image-enhancement-engine';

import { 
  PipelinePreset, 
  PipelineConfig, 
  PipelineExecution, 
  PipelineStage,
  PipelineMetrics
} from '@/lib/ai/enhancement-pipeline-manager';

import { useImageEnhancement } from '@/hooks/use-image-enhancement';

interface ImageEnhancementPanelProps {
  initialImage?: ImageData | string;
  onImageChange?: (image: ImageData | string) => void;
  onEnhancementComplete?: (result: EnhancementResult) => void;
  className?: string;
  isActive?: boolean;
}

interface BeforeAfterViewProps {
  originalImage: ImageData | string;
  enhancedImage?: ImageData | string;
  isLoading?: boolean;
  progress?: number;
  onZoomChange?: (zoom: number) => void;
  onPanChange?: (pan: { x: number; y: number }) => void;
}

const BeforeAfterView: React.FC<BeforeAfterViewProps> = ({
  originalImage,
  enhancedImage,
  isLoading = false,
  progress = 0,
  onZoomChange,
  onPanChange
}) => {
  const [splitPosition, setSplitPosition] = useState(50);
  const [showComparison, setShowComparison] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    
    const newPan = {
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    };
    
    setPan(newPan);
    onPanChange?.(newPan);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleZoom = (delta: number) => {
    const newZoom = Math.max(0.1, Math.min(5, zoom + delta));
    setZoom(newZoom);
    onZoomChange?.(newZoom);
  };

  const resetView = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
    onZoomChange?.(1);
    onPanChange?.({ x: 0, y: 0 });
  };

  const getImageSrc = (image: ImageData | string) => {
    if (typeof image === 'string') {
      return image;
    }
    // Convert ImageData to data URL
    const canvas = document.createElement('canvas');
    canvas.width = image.width;
    canvas.height = image.height;
    const ctx = canvas.getContext('2d');
    ctx?.putImageData(image, 0, 0);
    return canvas.toDataURL();
  };

  return (
    <div className="relative w-full h-64 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="absolute top-2 left-2 z-10 flex items-center gap-2">
        <div className="flex items-center gap-1 bg-white dark:bg-gray-800 rounded-md p-1 shadow-sm">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowComparison(!showComparison)}
            className="h-8 w-8 p-0"
          >
            {showComparison ? <Layers /> : <Eye />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleZoom(-0.1)}
            className="h-8 w-8 p-0"
          >
            <ZoomOut />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleZoom(0.1)}
            className="h-8 w-8 p-0"
          >
            <ZoomIn />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={resetView}
            className="h-8 w-8 p-0"
          >
            <RotateCcw />
          </Button>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-md px-2 py-1 text-xs font-medium shadow-sm">
          {Math.round(zoom * 100)}%
        </div>
      </div>

      {/* Progress indicator */}
      {isLoading && (
        <div className="absolute top-2 right-2 z-10 bg-white dark:bg-gray-800 rounded-md p-2 shadow-sm">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-xs font-medium">{Math.round(progress)}%</span>
          </div>
        </div>
      )}

      {/* Image container */}
      <div
        className="relative w-full h-full cursor-move"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Original image */}
        <img
          src={getImageSrc(originalImage)}
          alt="Original"
          className="absolute inset-0 w-full h-full object-contain"
          style={{
            transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
            transformOrigin: 'center'
          }}
        />

        {/* Enhanced image with split view */}
        {enhancedImage && showComparison && (
          <div
            className="absolute inset-0 overflow-hidden"
            style={{
              clipPath: `polygon(${splitPosition}% 0%, 100% 0%, 100% 100%, ${splitPosition}% 100%)`
            }}
          >
            <img
              src={getImageSrc(enhancedImage)}
              alt="Enhanced"
              className="absolute inset-0 w-full h-full object-contain"
              style={{
                transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
                transformOrigin: 'center'
              }}
            />
          </div>
        )}

        {/* Split line */}
        {enhancedImage && showComparison && (
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-white shadow-lg cursor-ew-resize z-10"
            style={{ left: `${splitPosition}%` }}
            onMouseDown={(e) => {
              e.stopPropagation();
              const startX = e.clientX;
              const startSplit = splitPosition;
              
              const handleMouseMove = (e: MouseEvent) => {
                const rect = e.currentTarget?.parentElement?.getBoundingClientRect();
                if (rect) {
                  const newSplit = Math.max(0, Math.min(100, 
                    startSplit + ((e.clientX - startX) / rect.width) * 100
                  ));
                  setSplitPosition(newSplit);
                }
              };

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
              };

              document.addEventListener('mousemove', handleMouseMove);
              document.addEventListener('mouseup', handleMouseUp);
            }}
          />
        )}

        {/* Labels */}
        {enhancedImage && showComparison && (
          <>
            <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs font-medium">
              Original
            </div>
            <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs font-medium">
              Enhanced
            </div>
          </>
        )}

        {/* Loading overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-sm font-medium">Enhancing image...</p>
              <div className="mt-2 w-48">
                <Progress value={progress} className="h-2" />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

interface EnhancementControlsProps {
  enhancementType: EnhancementType;
  parameters: EnhancementParameters;
  onParametersChange: (parameters: EnhancementParameters) => void;
  availableModels: EnhancementModel[];
  selectedModel?: string;
  onModelChange: (model: string) => void;
  qualityAssessment?: QualityAssessment;
  recommendations?: EnhancementRecommendation[];
}

const EnhancementControls: React.FC<EnhancementControlsProps> = ({
  enhancementType,
  parameters,
  onParametersChange,
  availableModels,
  selectedModel,
  onModelChange,
  qualityAssessment,
  recommendations
}) => {
  const updateParameter = (key: keyof EnhancementParameters, value: any) => {
    onParametersChange({
      ...parameters,
      [key]: value
    });
  };

  const getControlsForType = () => {
    switch (enhancementType) {
      case EnhancementType.DENOISE:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Noise Reduction Level</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[parameters.denoiseLevel || 0.5]}
                  onValueChange={([value]) => updateParameter('denoiseLevel', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  className="flex-1"
                />
                <span className="text-sm font-medium w-12">
                  {Math.round((parameters.denoiseLevel || 0.5) * 100)}%
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <Label>Preserve Edges</Label>
              <Switch
                checked={parameters.preserveEdges || false}
                onCheckedChange={(checked) => updateParameter('preserveEdges', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label>Adaptive Enhancement</Label>
              <Switch
                checked={parameters.adaptiveEnhancement || false}
                onCheckedChange={(checked) => updateParameter('adaptiveEnhancement', checked)}
              />
            </div>
          </div>
        );

      case EnhancementType.SHARPEN:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Sharpening Level</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[parameters.sharpenLevel || 0.5]}
                  onValueChange={([value]) => updateParameter('sharpenLevel', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  className="flex-1"
                />
                <span className="text-sm font-medium w-12">
                  {Math.round((parameters.sharpenLevel || 0.5) * 100)}%
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <Label>Preserve Edges</Label>
              <Switch
                checked={parameters.preserveEdges || false}
                onCheckedChange={(checked) => updateParameter('preserveEdges', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label>Adaptive Enhancement</Label>
              <Switch
                checked={parameters.adaptiveEnhancement || false}
                onCheckedChange={(checked) => updateParameter('adaptiveEnhancement', checked)}
              />
            </div>
          </div>
        );

      case EnhancementType.UPSCALE:
      case EnhancementType.SUPER_RESOLUTION:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Upscaling Factor</Label>
              <Select
                value={parameters.scale?.toString() || '2'}
                onValueChange={(value) => updateParameter('scale', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select scale" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2">2x</SelectItem>
                  <SelectItem value="4">4x</SelectItem>
                  <SelectItem value="8">8x</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between">
              <Label>Face Enhancement</Label>
              <Switch
                checked={parameters.faceEnhance || false}
                onCheckedChange={(checked) => updateParameter('faceEnhance', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label>Preserve Edges</Label>
              <Switch
                checked={parameters.preserveEdges || false}
                onCheckedChange={(checked) => updateParameter('preserveEdges', checked)}
              />
            </div>
          </div>
        );

      case EnhancementType.COLOR_CORRECT:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Saturation Adjustment</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[parameters.saturationAdjustment || 0]}
                  onValueChange={([value]) => updateParameter('saturationAdjustment', value)}
                  min={-1}
                  max={1}
                  step={0.1}
                  className="flex-1"
                />
                <span className="text-sm font-medium w-12">
                  {Math.round((parameters.saturationAdjustment || 0) * 100)}%
                </span>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Contrast Boost</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[parameters.contrastBoost || 0]}
                  onValueChange={([value]) => updateParameter('contrastBoost', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  className="flex-1"
                />
                <span className="text-sm font-medium w-12">
                  {Math.round((parameters.contrastBoost || 0) * 100)}%
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <Label>Auto Color Correction</Label>
              <Switch
                checked={parameters.colorCorrection || false}
                onCheckedChange={(checked) => updateParameter('colorCorrection', checked)}
              />
            </div>
          </div>
        );

      case EnhancementType.EXPOSURE_FIX:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Exposure Adjustment</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[parameters.exposureAdjustment || 0]}
                  onValueChange={([value]) => updateParameter('exposureAdjustment', value)}
                  min={-2}
                  max={2}
                  step={0.1}
                  className="flex-1"
                />
                <span className="text-sm font-medium w-12">
                  {parameters.exposureAdjustment || 0}
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <Label>HDR Mode</Label>
              <Switch
                checked={parameters.hdrMode || false}
                onCheckedChange={(checked) => updateParameter('hdrMode', checked)}
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Enhancement Strength</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[parameters.strength || 0.5]}
                  onValueChange={([value]) => updateParameter('strength', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  className="flex-1"
                />
                <span className="text-sm font-medium w-12">
                  {Math.round((parameters.strength || 0.5) * 100)}%
                </span>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Preserve Original</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[parameters.preserveOriginal || 0.8]}
                  onValueChange={([value]) => updateParameter('preserveOriginal', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  className="flex-1"
                />
                <span className="text-sm font-medium w-12">
                  {Math.round((parameters.preserveOriginal || 0.8) * 100)}%
                </span>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Model Selection */}
      <div className="space-y-2">
        <Label>Enhancement Model</Label>
        <Select value={selectedModel} onValueChange={onModelChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select model" />
          </SelectTrigger>
          <SelectContent>
            {availableModels.map((model) => (
              <SelectItem key={model.id} value={model.id}>
                <div className="flex items-center justify-between w-full">
                  <span>{model.name}</span>
                  <div className="flex items-center gap-2 ml-2">
                    <Badge variant="outline" className="text-xs">
                      {model.provider}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      ${model.costPerOperation}
                    </span>
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Enhancement Controls */}
      <div className="space-y-4">
        <Label className="text-sm font-medium">Enhancement Parameters</Label>
        {getControlsForType()}
      </div>

      {/* Quality Assessment */}
      {qualityAssessment && (
        <div className="space-y-4">
          <Label className="text-sm font-medium">Quality Assessment</Label>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Sharpness</span>
                <span className="text-sm font-medium">
                  {Math.round(qualityAssessment.metrics.sharpness)}%
                </span>
              </div>
              <Progress value={qualityAssessment.metrics.sharpness} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Noise</span>
                <span className="text-sm font-medium">
                  {Math.round(qualityAssessment.metrics.noise)}%
                </span>
              </div>
              <Progress value={qualityAssessment.metrics.noise} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Contrast</span>
                <span className="text-sm font-medium">
                  {Math.round(qualityAssessment.metrics.contrast)}%
                </span>
              </div>
              <Progress value={qualityAssessment.metrics.contrast} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Color</span>
                <span className="text-sm font-medium">
                  {Math.round(qualityAssessment.metrics.color)}%
                </span>
              </div>
              <Progress value={qualityAssessment.metrics.color} className="h-2" />
            </div>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <span className="text-sm font-medium">Overall Quality</span>
            <div className="flex items-center gap-2">
              <Progress value={qualityAssessment.overallScore} className="h-2 w-16" />
              <span className="text-sm font-medium">
                {Math.round(qualityAssessment.overallScore)}%
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Recommendations */}
      {recommendations && recommendations.length > 0 && (
        <div className="space-y-4">
          <Label className="text-sm font-medium">Recommendations</Label>
          <div className="space-y-2">
            {recommendations.slice(0, 3).map((rec, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full ${
                    rec.priority === 'high' ? 'bg-red-500' :
                    rec.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  }`} />
                  <div>
                    <p className="text-sm font-medium">{rec.type}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">{rec.reason}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-500">
                    +{Math.round(rec.expectedImprovement * 100)}%
                  </div>
                  <div className="text-xs text-gray-500">
                    ${rec.estimatedCost}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface BatchProcessingProps {
  images: (ImageData | string)[];
  onImagesChange: (images: (ImageData | string)[]) => void;
  onBatchProcess: (config: PipelineConfig) => void;
  progress?: number;
  isProcessing?: boolean;
}

const BatchProcessing: React.FC<BatchProcessingProps> = ({
  images,
  onImagesChange,
  onBatchProcess,
  progress = 0,
  isProcessing = false
}) => {
  const [selectedPreset, setSelectedPreset] = useState<string>('');
  const [batchConfig, setBatchConfig] = useState<PipelineConfig | null>(null);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    Promise.all(files.map(file => {
      return new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(file);
      });
    })).then(newImages => {
      onImagesChange([...images, ...newImages]);
    });
  };

  const removeImage = (index: number) => {
    onImagesChange(images.filter((_, i) => i !== index));
  };

  const processBatch = () => {
    if (batchConfig) {
      onBatchProcess(batchConfig);
    }
  };

  return (
    <div className="space-y-6">
      {/* Image Upload */}
      <div className="space-y-4">
        <Label className="text-sm font-medium">Images to Process</Label>
        
        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
          <input
            type="file"
            multiple
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
            id="batch-upload"
          />
          <label htmlFor="batch-upload" className="cursor-pointer">
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Click to upload images or drag and drop
            </p>
            <p className="text-xs text-gray-500 mt-1">
              PNG, JPG, WebP up to 10MB each
            </p>
          </label>
        </div>

        {/* Image Grid */}
        {images.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image, index) => (
              <div key={index} className="relative group">
                <img
                  src={typeof image === 'string' ? image : ''}
                  alt={`Batch image ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => removeImage(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Batch Settings */}
      <div className="space-y-4">
        <Label className="text-sm font-medium">Batch Settings</Label>
        
        <div className="space-y-2">
          <Label>Processing Pipeline</Label>
          <Select value={selectedPreset} onValueChange={setSelectedPreset}>
            <SelectTrigger>
              <SelectValue placeholder="Select preset pipeline" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="photography-pro">Professional Photography</SelectItem>
              <SelectItem value="social-media-opt">Social Media Optimization</SelectItem>
              <SelectItem value="print-quality">Print Quality</SelectItem>
              <SelectItem value="digital-art-enhance">Digital Art Enhancement</SelectItem>
              <SelectItem value="photo-restoration">Photo Restoration</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Output Format</Label>
            <Select defaultValue="png">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="png">PNG</SelectItem>
                <SelectItem value="jpg">JPEG</SelectItem>
                <SelectItem value="webp">WebP</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label>Quality</Label>
            <Select defaultValue="high">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Process Button */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {images.length} images selected
        </div>
        <Button
          onClick={processBatch}
          disabled={images.length === 0 || !selectedPreset || isProcessing}
          className="flex items-center gap-2"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              Process Batch
            </>
          )}
        </Button>
      </div>

      {/* Progress */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Processing Progress</span>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {Math.round(progress)}%
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}
    </div>
  );
};

export const ImageEnhancementPanel: React.FC<ImageEnhancementPanelProps> = ({
  initialImage,
  onImageChange,
  onEnhancementComplete,
  className = '',
  isActive = true
}) => {
  const {
    // State
    currentImage,
    enhancedImage,
    isProcessing,
    progress,
    error,
    
    // Enhancement
    enhanceImage,
    enhancementType,
    setEnhancementType,
    parameters,
    setParameters,
    
    // Models
    availableModels,
    selectedModel,
    setSelectedModel,
    
    // Quality
    qualityAssessment,
    recommendations,
    
    // Pipeline
    presets,
    executePipeline,
    cancelExecution,
    
    // Metrics
    metrics,
    
    // Utilities
    resetEnhancement,
    clearHistory
  } = useImageEnhancement(initialImage);

  const [activeTab, setActiveTab] = useState('single');
  const [batchImages, setBatchImages] = useState<(ImageData | string)[]>([]);
  const [currentStage, setCurrentStage] = useState<string>('');

  // Handle image changes
  useEffect(() => {
    if (currentImage) {
      onImageChange?.(currentImage);
    }
  }, [currentImage, onImageChange]);

  // Handle enhancement completion
  useEffect(() => {
    if (enhancedImage && !isProcessing) {
      const result: EnhancementResult = {
        success: true,
        enhancedImage,
        originalImage: currentImage,
        processingTime: 0,
        cost: 0
      };
      onEnhancementComplete?.(result);
    }
  }, [enhancedImage, isProcessing, currentImage, onEnhancementComplete]);

  const handleSingleEnhancement = async () => {
    if (!currentImage) return;
    
    try {
      await enhanceImage(currentImage, {
        enhancementType,
        parameters,
        model: selectedModel,
        progressCallback: (progress, message) => {
          setCurrentStage(message || '');
        }
      });
    } catch (error) {
      console.error('Enhancement failed:', error);
    }
  };

  const handleBatchProcess = async (config: PipelineConfig) => {
    if (batchImages.length === 0) return;
    
    try {
      for (let i = 0; i < batchImages.length; i++) {
        const image = batchImages[i];
        await executePipeline(image, config, {
          progressCallback: (progress, stage) => {
            setCurrentStage(`Processing image ${i + 1}/${batchImages.length}: ${stage}`);
          }
        });
      }
    } catch (error) {
      console.error('Batch processing failed:', error);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const image = e.target?.result as string;
        onImageChange?.(image);
      };
      reader.readAsDataURL(file);
    }
  };

  if (!isActive) {
    return (
      <Card className={`w-full h-full ${className}`}>
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <ImageIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Image Enhancement tools are not active
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full h-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5" />
          Image Enhancement
        </CardTitle>
      </CardHeader>
      
      <CardContent className="h-full overflow-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="single">Single Image</TabsTrigger>
            <TabsTrigger value="batch">Batch Processing</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="single" className="space-y-6">
            {/* Image Upload */}
            {!currentImage && (
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer">
                  <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Click to upload an image or drag and drop
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    PNG, JPG, WebP up to 10MB
                  </p>
                </label>
              </div>
            )}

            {/* Before/After View */}
            {currentImage && (
              <BeforeAfterView
                originalImage={currentImage}
                enhancedImage={enhancedImage}
                isLoading={isProcessing}
                progress={progress}
              />
            )}

            {/* Enhancement Type Selection */}
            {currentImage && (
              <div className="space-y-4">
                <Label className="text-sm font-medium">Enhancement Type</Label>
                <Select
                  value={enhancementType}
                  onValueChange={(value) => setEnhancementType(value as EnhancementType)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select enhancement type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={EnhancementType.DENOISE}>Noise Reduction</SelectItem>
                    <SelectItem value={EnhancementType.SHARPEN}>Sharpening</SelectItem>
                    <SelectItem value={EnhancementType.UPSCALE}>Upscaling</SelectItem>
                    <SelectItem value={EnhancementType.SUPER_RESOLUTION}>Super Resolution</SelectItem>
                    <SelectItem value={EnhancementType.COLOR_CORRECT}>Color Correction</SelectItem>
                    <SelectItem value={EnhancementType.EXPOSURE_FIX}>Exposure Fix</SelectItem>
                    <SelectItem value={EnhancementType.FACE_RESTORATION}>Face Restoration</SelectItem>
                    <SelectItem value={EnhancementType.ARTIFACT_REMOVAL}>Artifact Removal</SelectItem>
                    <SelectItem value={EnhancementType.DETAIL_ENHANCEMENT}>Detail Enhancement</SelectItem>
                    <SelectItem value={EnhancementType.HDR_ENHANCEMENT}>HDR Enhancement</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Enhancement Controls */}
            {currentImage && (
              <EnhancementControls
                enhancementType={enhancementType}
                parameters={parameters}
                onParametersChange={setParameters}
                availableModels={availableModels}
                selectedModel={selectedModel}
                onModelChange={setSelectedModel}
                qualityAssessment={qualityAssessment}
                recommendations={recommendations}
              />
            )}

            {/* Action Buttons */}
            {currentImage && (
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleSingleEnhancement}
                  disabled={isProcessing}
                  className="flex items-center gap-2"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Enhancing...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      Enhance Image
                    </>
                  )}
                </Button>
                
                {isProcessing && (
                  <Button
                    variant="outline"
                    onClick={cancelExecution}
                    className="flex items-center gap-2"
                  >
                    <Square className="h-4 w-4" />
                    Cancel
                  </Button>
                )}
                
                <Button
                  variant="outline"
                  onClick={resetEnhancement}
                  disabled={isProcessing}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Reset
                </Button>
              </div>
            )}

            {/* Progress */}
            {isProcessing && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {currentStage || 'Processing...'}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {Math.round(progress)}%
                  </span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            )}

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </TabsContent>

          <TabsContent value="batch" className="space-y-6">
            <BatchProcessing
              images={batchImages}
              onImagesChange={setBatchImages}
              onBatchProcess={handleBatchProcess}
              progress={progress}
              isProcessing={isProcessing}
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* Metrics Dashboard */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Total Enhancements</p>
                      <p className="text-2xl font-bold">{metrics.totalExecutions}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Success Rate</p>
                      <p className="text-2xl font-bold">{Math.round(metrics.successRate * 100)}%</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Avg. Time</p>
                      <p className="text-2xl font-bold">{Math.round(metrics.averageTime)}s</p>
                    </div>
                    <Clock className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Avg. Cost</p>
                      <p className="text-2xl font-bold">${metrics.averageCost.toFixed(3)}</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Popular Enhancement Types */}
            <Card>
              <CardHeader>
                <CardTitle>Popular Enhancement Types</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {metrics.popularStages.slice(0, 5).map((stage, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <span className="text-sm font-medium">{index + 1}</span>
                        </div>
                        <span className="text-sm font-medium">{stage.stage}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                          <div
                            className="h-2 bg-blue-500 rounded-full"
                            style={{ 
                              width: `${(stage.count / Math.max(...metrics.popularStages.map(s => s.count))) * 100}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {stage.count}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ImageEnhancementPanel;