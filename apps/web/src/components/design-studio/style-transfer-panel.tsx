'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Slider } from '../ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { Switch } from '../ui/switch';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { 
  Palette, 
  Wand2, 
  Image, 
  Search, 
  Star, 
  Download, 
  Upload, 
  Play, 
  Pause, 
  RotateCcw, 
  Settings, 
  Eye, 
  Heart, 
  Zap,
  Clock,
  TrendingUp,
  Filter,
  Grid,
  List,
  Info,
  AlertCircle,
  CheckCircle,
  Loader2,
  X,
  Plus,
  Minus,
  RefreshCw,
  Sparkles,
  Layers,
  Paintbrush,
  Sliders,
  MonitorSpeaker,
  Camera,
  Palette as PaletteIcon,
  Brush,
  Aperture,
  Contrast,
  Sun,
  Moon,
  Cpu,
  BarChart3,
  Target,
  Gauge,
  Timer,
  Wrench,
  Save,
  Share,
  Copy,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  BookOpen,
  Maximize2,
  Minimize2,
  ZoomIn,
  ZoomOut,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Crop,
  PaintBucket,
  Eraser,
  Eyedropper,
  Move,
  MousePointer,
  Hand,
  Type,
  Circle,
  Square,
  Triangle,
  Star as StarIcon,
  Hexagon,
  Pentagon,
  Octagon,
  Diamond,
  Heart as HeartIcon,
  Cloud,
  Zap as ZapIcon,
  Flame,
  Snowflake,
  Droplets,
  Wind,
  Rainbow,
  Feather,
  Leaf,
  Flower,
  TreePine,
  Mountain,
  Waves,
  Sun as SunIcon,
  Moon as MoonIcon,
  Stars,
  Sparkles as SparklesIcon,
  Wand2 as WandIcon,
  Palette as PaletteIcon2,
  Brush as BrushIcon,
  Paintbrush as PaintbrushIcon,
  Pipette,
  Blend,
  Layers as LayersIcon,
  Grid as GridIcon,
  Hexagon as HexagonIcon,
  Pentagon as PentagonIcon,
  Octagon as OctagonIcon,
  Diamond as DiamondIcon,
  Heart as HeartIcon2,
  Cloud as CloudIcon,
  Zap as ZapIcon2,
  Flame as FlameIcon,
  Snowflake as SnowflakeIcon,
  Droplets as DropletsIcon,
  Wind as WindIcon,
  Rainbow as RainbowIcon,
  Feather as FeatherIcon,
  Leaf as LeafIcon,
  Flower as FlowerIcon,
  TreePine as TreePineIcon,
  Mountain as MountainIcon,
  Waves as WavesIcon
} from 'lucide-react';

import { useStyleTransfer } from '../../hooks/use-style-transfer';
import { useMultiProviderAI } from '../../hooks/use-multi-provider-ai';
import { useToast } from '../../hooks/use-toast';
import { 
  StyleReference, 
  StyleParameters, 
  StyleTransferResult, 
  StylePreviewResult, 
  StyleModel, 
  BlendMode,
  TransferOptions
} from '../../lib/ai/style-transfer-engine';
import { 
  StyleLibraryEntry, 
  StyleSearchQuery, 
  StyleSearchResult,
  StyleCollection,
  ExtractedStyle
} from '../../lib/ai/style-library-manager';
import { 
  ImageStyleAnalysis, 
  StyleMatchingResult,
  StyleRecommendation
} from '../../lib/ai/style-analysis-service';

interface StyleTransferPanelProps {
  onStyleTransfer?: (result: StyleTransferResult) => void;
  onPreviewGenerated?: (preview: StylePreviewResult) => void;
  onError?: (error: string) => void;
  sourceImage?: ImageData | HTMLImageElement | string;
  className?: string;
  disabled?: boolean;
}

interface PreviewState {
  image: ImageData | null;
  isGenerating: boolean;
  error?: string;
  processingTime?: number;
  estimatedFullTime?: number;
}

interface TransferState {
  isProcessing: boolean;
  progress: number;
  status: string;
  result?: StyleTransferResult;
  error?: string;
  processingTime?: number;
}

interface StyleSelectorState {
  selectedStyle: StyleLibraryEntry | null;
  searchQuery: string;
  searchResults: StyleSearchResult[];
  isSearching: boolean;
  activeCollection?: StyleCollection;
  viewMode: 'grid' | 'list';
  sortBy: 'relevance' | 'rating' | 'usage' | 'recent';
  filters: {
    categories: string[];
    tags: string[];
    complexity?: 'low' | 'medium' | 'high';
    rating?: number;
  };
}

interface ParameterPanelState {
  isAdvancedMode: boolean;
  presetName?: string;
  hasUnsavedChanges: boolean;
}

export function StyleTransferPanel({
  onStyleTransfer,
  onPreviewGenerated,
  onError,
  sourceImage,
  className,
  disabled = false
}: StyleTransferPanelProps) {
  const { toast } = useToast();
  const multiProviderAI = useMultiProviderAI();
  
  const { 
    state: transferState, 
    actions: transferActions, 
    services,
    isReady 
  } = useStyleTransfer({
    multiProviderAI,
    enablePreviewCache: true,
    enableAutoAnalysis: true,
    onTransferComplete: (result) => {
      setTransferState(prev => ({
        ...prev,
        isProcessing: false,
        result,
        error: undefined
      }));
      onStyleTransfer?.(result);
      toast({
        title: "Style Transfer Complete",
        description: "Your image has been stylized successfully!",
        duration: 3000
      });
    },
    onTransferError: (error) => {
      setTransferState(prev => ({
        ...prev,
        isProcessing: false,
        error,
        result: undefined
      }));
      onError?.(error);
      toast({
        title: "Style Transfer Failed",
        description: error,
        variant: "destructive",
        duration: 5000
      });
    },
    onPreviewGenerated: (preview) => {
      setPreviewState(prev => ({
        ...prev,
        image: preview.preview || null,
        isGenerating: false,
        error: preview.error,
        processingTime: preview.processingTime,
        estimatedFullTime: preview.estimatedFullTime
      }));
      onPreviewGenerated?.(preview);
    }
  });

  // Component states
  const [previewState, setPreviewState] = useState<PreviewState>({
    image: null,
    isGenerating: false
  });

  const [transferStateLocal, setTransferState] = useState<TransferState>({
    isProcessing: false,
    progress: 0,
    status: 'idle'
  });

  const [styleSelectorState, setStyleSelectorState] = useState<StyleSelectorState>({
    selectedStyle: null,
    searchQuery: '',
    searchResults: [],
    isSearching: false,
    viewMode: 'grid',
    sortBy: 'relevance',
    filters: {
      categories: [],
      tags: []
    }
  });

  const [parameterPanelState, setParameterPanelState] = useState<ParameterPanelState>({
    isAdvancedMode: false,
    hasUnsavedChanges: false
  });

  const [styleParameters, setStyleParameters] = useState<StyleParameters>({
    strength: 0.7,
    preserveContent: 0.8,
    colorTransfer: true,
    textureTransfer: true,
    edgePreservation: 0.7,
    detailLevel: 'medium',
    blendMode: 'normal'
  });

  const [transferOptions, setTransferOptions] = useState<TransferOptions>({
    priority: 'balanced',
    enablePreview: true,
    enableCache: true,
    outputFormat: 'png',
    outputQuality: 0.9,
    maxRetries: 3,
    timeoutMs: 120000
  });

  const [analysisResult, setAnalysisResult] = useState<ImageStyleAnalysis | null>(null);
  const [recommendations, setRecommendations] = useState<StyleRecommendation[]>([]);
  const [selectedModel, setSelectedModel] = useState<StyleModel | null>(null);

  // Refs
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();

  // Effects
  useEffect(() => {
    if (isReady && transferState.availableModels.length > 0) {
      setSelectedModel(transferState.availableModels[0]);
    }
  }, [isReady, transferState.availableModels]);

  useEffect(() => {
    if (sourceImage) {
      handleSourceImageAnalysis();
    }
  }, [sourceImage]);

  useEffect(() => {
    if (transferState.currentTransfer) {
      setTransferState(prev => ({
        ...prev,
        isProcessing: true,
        progress: transferState.currentTransfer?.progress || 0,
        status: transferState.currentTransfer?.status || 'processing'
      }));
    }
  }, [transferState.currentTransfer]);

  // Handlers
  const handleSourceImageAnalysis = useCallback(async () => {
    if (!sourceImage || !services.styleAnalysis) return;

    try {
      const imageData = await convertToImageData(sourceImage);
      const analysis = await services.styleAnalysis.analyzeImageStyle(imageData);
      setAnalysisResult(analysis);

      // Get style recommendations
      const matchingResult = await transferActions.getStyleRecommendations(imageData);
      setRecommendations(matchingResult.recommendations);

      // Auto-select best matching style if none selected
      if (!styleSelectorState.selectedStyle && matchingResult.matchedStyles.length > 0) {
        const bestMatch = matchingResult.matchedStyles[0];
        if (bestMatch.compatibility > 0.7) {
          handleStyleSelection(bestMatch.style as StyleLibraryEntry);
        }
      }
    } catch (error) {
      console.error('Analysis failed:', error);
      toast({
        title: "Analysis Failed",
        description: "Could not analyze source image",
        variant: "destructive",
        duration: 3000
      });
    }
  }, [sourceImage, services.styleAnalysis, transferActions, styleSelectorState.selectedStyle]);

  const handleStyleSelection = useCallback((style: StyleLibraryEntry) => {
    setStyleSelectorState(prev => ({
      ...prev,
      selectedStyle: style
    }));

    // Generate preview if source image is available
    if (sourceImage && transferOptions.enablePreview) {
      generatePreview(style);
    }
  }, [sourceImage, transferOptions.enablePreview]);

  const generatePreview = useCallback(async (style?: StyleLibraryEntry) => {
    if (!sourceImage || !isReady) return;

    const selectedStyle = style || styleSelectorState.selectedStyle;
    if (!selectedStyle) return;

    setPreviewState(prev => ({
      ...prev,
      isGenerating: true,
      error: undefined
    }));

    try {
      const styleReference: StyleReference = {
        type: selectedStyle.type,
        source: selectedStyle.source,
        name: selectedStyle.name,
        description: selectedStyle.description,
        tags: selectedStyle.tags,
        mood: selectedStyle.mood,
        artisticPeriod: selectedStyle.artisticPeriod,
        dominantColors: selectedStyle.dominantColors,
        complexity: selectedStyle.complexity
      };

      const preview = await transferActions.generatePreview(
        sourceImage,
        styleReference,
        styleParameters
      );

      if (preview.success && preview.preview) {
        drawPreviewToCanvas(preview.preview);
      }
    } catch (error) {
      console.error('Preview generation failed:', error);
      setPreviewState(prev => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Preview generation failed'
      }));
    }
  }, [sourceImage, isReady, styleSelectorState.selectedStyle, styleParameters, transferActions]);

  const drawPreviewToCanvas = useCallback((imageData: ImageData) => {
    if (!previewCanvasRef.current) return;

    const canvas = previewCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = imageData.width;
    canvas.height = imageData.height;
    ctx.putImageData(imageData, 0, 0);
  }, []);

  const handleStartTransfer = useCallback(async () => {
    if (!sourceImage || !styleSelectorState.selectedStyle || !isReady) return;

    setTransferState(prev => ({
      ...prev,
      isProcessing: true,
      progress: 0,
      status: 'starting',
      error: undefined
    }));

    try {
      const styleReference: StyleReference = {
        type: styleSelectorState.selectedStyle.type,
        source: styleSelectorState.selectedStyle.source,
        name: styleSelectorState.selectedStyle.name,
        description: styleSelectorState.selectedStyle.description,
        tags: styleSelectorState.selectedStyle.tags,
        mood: styleSelectorState.selectedStyle.mood,
        artisticPeriod: styleSelectorState.selectedStyle.artisticPeriod,
        dominantColors: styleSelectorState.selectedStyle.dominantColors,
        complexity: styleSelectorState.selectedStyle.complexity
      };

      const result = await transferActions.transferStyle({
        contentImage: sourceImage,
        styleReference,
        model: selectedModel?.id,
        parameters: styleParameters,
        options: transferOptions
      });

      if (result.success) {
        setTransferState(prev => ({
          ...prev,
          isProcessing: false,
          progress: 100,
          status: 'completed',
          result,
          processingTime: result.processingTime
        }));
      } else {
        throw new Error(result.error || 'Transfer failed');
      }
    } catch (error) {
      setTransferState(prev => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Transfer failed'
      }));
    }
  }, [sourceImage, styleSelectorState.selectedStyle, isReady, selectedModel, styleParameters, transferOptions, transferActions]);

  const handleParameterChange = useCallback((parameter: keyof StyleParameters, value: any) => {
    setStyleParameters(prev => ({
      ...prev,
      [parameter]: value
    }));

    setParameterPanelState(prev => ({
      ...prev,
      hasUnsavedChanges: true
    }));

    // Debounced preview regeneration
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      if (transferOptions.enablePreview) {
        generatePreview();
      }
    }, 500);
  }, [transferOptions.enablePreview, generatePreview]);

  const handleStyleSearch = useCallback(async (query: string) => {
    if (!services.styleLibrary) return;

    setStyleSelectorState(prev => ({
      ...prev,
      searchQuery: query,
      isSearching: true
    }));

    try {
      const searchQuery: StyleSearchQuery = {
        text: query,
        sortBy: styleSelectorState.sortBy,
        limit: 20
      };

      const results = await services.styleLibrary.searchStyles(searchQuery);
      
      setStyleSelectorState(prev => ({
        ...prev,
        searchResults: results,
        isSearching: false
      }));
    } catch (error) {
      console.error('Search failed:', error);
      setStyleSelectorState(prev => ({
        ...prev,
        isSearching: false
      }));
    }
  }, [services.styleLibrary, styleSelectorState.sortBy]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (typeof result === 'string') {
        // Handle custom style upload
        handleCustomStyleUpload(result, file.name);
      }
    };
    reader.readAsDataURL(file);
  }, []);

  const handleCustomStyleUpload = useCallback(async (imageData: string, filename: string) => {
    if (!services.styleLibrary) return;

    try {
      const styleId = await transferActions.addCustomStyle(imageData, {
        name: filename.replace(/\.[^/.]+$/, ''),
        description: 'Custom uploaded style',
        tags: ['custom', 'uploaded'],
        categories: ['custom']
      });

      const newStyle = services.styleLibrary.getStyleById(styleId);
      if (newStyle) {
        handleStyleSelection(newStyle);
        toast({
          title: "Custom Style Added",
          description: "Your custom style has been added to the library",
          duration: 3000
        });
      }
    } catch (error) {
      console.error('Custom style upload failed:', error);
      toast({
        title: "Upload Failed",
        description: "Could not add custom style to library",
        variant: "destructive",
        duration: 3000
      });
    }
  }, [services.styleLibrary, transferActions, handleStyleSelection]);

  // Utility functions
  const convertToImageData = async (image: ImageData | HTMLImageElement | string): Promise<ImageData> => {
    if (image instanceof ImageData) return image;
    
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      if (typeof image === 'string') {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          resolve(ctx.getImageData(0, 0, canvas.width, canvas.height));
        };
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = image;
      } else if (image instanceof HTMLImageElement) {
        canvas.width = image.width;
        canvas.height = image.height;
        ctx.drawImage(image, 0, 0);
        resolve(ctx.getImageData(0, 0, canvas.width, canvas.height));
      }
    });
  };

  const formatTime = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getProcessingStatusIcon = () => {
    if (transferStateLocal.isProcessing) {
      return <Loader2 className="w-4 h-4 animate-spin" />;
    }
    if (transferStateLocal.error) {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    if (transferStateLocal.result) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    return <Wand2 className="w-4 h-4" />;
  };

  // Render loading state
  if (!isReady) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Initializing style transfer system...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            Style Transfer
            {transferState.isProcessing && (
              <Badge variant="secondary" className="ml-2">
                <Loader2 className="w-3 h-3 animate-spin mr-1" />
                Processing
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="styles" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="styles">Styles</TabsTrigger>
              <TabsTrigger value="parameters">Parameters</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="analysis">Analysis</TabsTrigger>
            </TabsList>

            {/* Style Selection Tab */}
            <TabsContent value="styles" className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      placeholder="Search styles..."
                      value={styleSelectorState.searchQuery}
                      onChange={(e) => handleStyleSearch(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Select
                    value={styleSelectorState.sortBy}
                    onValueChange={(value) => setStyleSelectorState(prev => ({ ...prev, sortBy: value as any }))}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="relevance">Relevance</SelectItem>
                      <SelectItem value="rating">Rating</SelectItem>
                      <SelectItem value="usage">Usage</SelectItem>
                      <SelectItem value="recent">Recent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant={styleSelectorState.viewMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setStyleSelectorState(prev => ({ ...prev, viewMode: 'grid' }))}
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={styleSelectorState.viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setStyleSelectorState(prev => ({ ...prev, viewMode: 'list' }))}
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <ScrollArea className="h-64 w-full rounded-md border p-4">
                {styleSelectorState.isSearching ? (
                  <div className="flex items-center justify-center h-32">
                    <Loader2 className="w-6 h-6 animate-spin" />
                  </div>
                ) : (
                  <div className={
                    styleSelectorState.viewMode === 'grid' 
                      ? "grid grid-cols-3 gap-2" 
                      : "space-y-2"
                  }>
                    {(styleSelectorState.searchResults.length > 0 
                      ? styleSelectorState.searchResults.map(r => r.style)
                      : transferState.styleLibrary.recentStyles
                    ).map((style) => (
                      <div
                        key={style.id}
                        className={`cursor-pointer rounded-lg border p-2 transition-all hover:border-primary ${
                          styleSelectorState.selectedStyle?.id === style.id
                            ? 'border-primary bg-primary/10'
                            : ''
                        }`}
                        onClick={() => handleStyleSelection(style)}
                      >
                        {styleSelectorState.viewMode === 'grid' ? (
                          <div className="space-y-1">
                            <div className="aspect-square rounded bg-muted flex items-center justify-center">
                              <Palette className="w-6 h-6 text-muted-foreground" />
                            </div>
                            <p className="text-xs font-medium truncate">{style.name}</p>
                            <div className="flex items-center gap-1">
                              <Star className="w-3 h-3 text-yellow-500 fill-current" />
                              <span className="text-xs">{style.averageRating.toFixed(1)}</span>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 rounded bg-muted flex items-center justify-center">
                              <Palette className="w-6 h-6 text-muted-foreground" />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium">{style.name}</p>
                              <p className="text-sm text-muted-foreground truncate">{style.description}</p>
                              <div className="flex items-center gap-2 mt-1">
                                <div className="flex items-center gap-1">
                                  <Star className="w-3 h-3 text-yellow-500 fill-current" />
                                  <span className="text-xs">{style.averageRating.toFixed(1)}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Eye className="w-3 h-3 text-muted-foreground" />
                                  <span className="text-xs">{style.usageCount}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>

              {styleSelectorState.selectedStyle && (
                <div className="rounded-lg border p-4 bg-muted/50">
                  <div className="flex items-start gap-3">
                    <div className="w-16 h-16 rounded bg-background flex items-center justify-center">
                      <Palette className="w-8 h-8 text-muted-foreground" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium">{styleSelectorState.selectedStyle.name}</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {styleSelectorState.selectedStyle.description}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                          <span className="text-xs">{styleSelectorState.selectedStyle.averageRating.toFixed(1)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="w-3 h-3 text-muted-foreground" />
                          <span className="text-xs">{styleSelectorState.selectedStyle.usageCount}</span>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {styleSelectorState.selectedStyle.complexity}
                        </Badge>
                      </div>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {styleSelectorState.selectedStyle.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Parameters Tab */}
            <TabsContent value="parameters" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Style Parameters</h3>
                <div className="flex items-center gap-2">
                  <Label htmlFor="advanced-mode" className="text-sm">Advanced</Label>
                  <Switch
                    id="advanced-mode"
                    checked={parameterPanelState.isAdvancedMode}
                    onCheckedChange={(checked) => setParameterPanelState(prev => ({ ...prev, isAdvancedMode: checked }))}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="strength">Style Strength</Label>
                  <div className="flex items-center gap-3 mt-2">
                    <Slider
                      id="strength"
                      min={0}
                      max={1}
                      step={0.1}
                      value={[styleParameters.strength]}
                      onValueChange={([value]) => handleParameterChange('strength', value)}
                      className="flex-1"
                    />
                    <span className="text-sm font-mono w-10 text-center">
                      {styleParameters.strength.toFixed(1)}
                    </span>
                  </div>
                </div>

                <div>
                  <Label htmlFor="preserve-content">Preserve Content</Label>
                  <div className="flex items-center gap-3 mt-2">
                    <Slider
                      id="preserve-content"
                      min={0}
                      max={1}
                      step={0.1}
                      value={[styleParameters.preserveContent]}
                      onValueChange={([value]) => handleParameterChange('preserveContent', value)}
                      className="flex-1"
                    />
                    <span className="text-sm font-mono w-10 text-center">
                      {styleParameters.preserveContent.toFixed(1)}
                    </span>
                  </div>
                </div>

                <div>
                  <Label htmlFor="edge-preservation">Edge Preservation</Label>
                  <div className="flex items-center gap-3 mt-2">
                    <Slider
                      id="edge-preservation"
                      min={0}
                      max={1}
                      step={0.1}
                      value={[styleParameters.edgePreservation]}
                      onValueChange={([value]) => handleParameterChange('edgePreservation', value)}
                      className="flex-1"
                    />
                    <span className="text-sm font-mono w-10 text-center">
                      {styleParameters.edgePreservation.toFixed(1)}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="detail-level">Detail Level</Label>
                    <Select
                      value={styleParameters.detailLevel}
                      onValueChange={(value) => handleParameterChange('detailLevel', value)}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="blend-mode">Blend Mode</Label>
                    <Select
                      value={styleParameters.blendMode}
                      onValueChange={(value) => handleParameterChange('blendMode', value)}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="multiply">Multiply</SelectItem>
                        <SelectItem value="screen">Screen</SelectItem>
                        <SelectItem value="overlay">Overlay</SelectItem>
                        <SelectItem value="soft-light">Soft Light</SelectItem>
                        <SelectItem value="hard-light">Hard Light</SelectItem>
                        <SelectItem value="color-dodge">Color Dodge</SelectItem>
                        <SelectItem value="color-burn">Color Burn</SelectItem>
                        <SelectItem value="darken">Darken</SelectItem>
                        <SelectItem value="lighten">Lighten</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="color-transfer"
                      checked={styleParameters.colorTransfer}
                      onCheckedChange={(checked) => handleParameterChange('colorTransfer', checked)}
                    />
                    <Label htmlFor="color-transfer">Color Transfer</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="texture-transfer"
                      checked={styleParameters.textureTransfer}
                      onCheckedChange={(checked) => handleParameterChange('textureTransfer', checked)}
                    />
                    <Label htmlFor="texture-transfer">Texture Transfer</Label>
                  </div>
                </div>

                {parameterPanelState.isAdvancedMode && (
                  <div className="space-y-4 pt-4 border-t">
                    <h4 className="font-medium text-sm">Advanced Options</h4>
                    
                    <div>
                      <Label htmlFor="model-selection">Model</Label>
                      <Select
                        value={selectedModel?.id || ''}
                        onValueChange={(value) => {
                          const model = transferState.availableModels.find(m => m.id === value);
                          setSelectedModel(model || null);
                        }}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {transferState.availableModels.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div className="flex items-center gap-2">
                                <span>{model.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {model.performance.speed}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="priority">Priority</Label>
                      <Select
                        value={transferOptions.priority}
                        onValueChange={(value) => setTransferOptions(prev => ({ ...prev, priority: value as any }))}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="quality">Quality</SelectItem>
                          <SelectItem value="speed">Speed</SelectItem>
                          <SelectItem value="balanced">Balanced</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="output-format">Output Format</Label>
                      <Select
                        value={transferOptions.outputFormat}
                        onValueChange={(value) => setTransferOptions(prev => ({ ...prev, outputFormat: value as any }))}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="png">PNG</SelectItem>
                          <SelectItem value="jpeg">JPEG</SelectItem>
                          <SelectItem value="webp">WebP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="output-quality">Output Quality</Label>
                      <div className="flex items-center gap-3 mt-2">
                        <Slider
                          id="output-quality"
                          min={0.1}
                          max={1}
                          step={0.1}
                          value={[transferOptions.outputQuality]}
                          onValueChange={([value]) => setTransferOptions(prev => ({ ...prev, outputQuality: value }))}
                          className="flex-1"
                        />
                        <span className="text-sm font-mono w-10 text-center">
                          {transferOptions.outputQuality.toFixed(1)}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {parameterPanelState.hasUnsavedChanges && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Parameters changed. Preview will update automatically.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            {/* Preview Tab */}
            <TabsContent value="preview" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Real-time Preview</h3>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => generatePreview()}
                    disabled={!sourceImage || !styleSelectorState.selectedStyle}
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh
                  </Button>
                  <Switch
                    checked={transferOptions.enablePreview}
                    onCheckedChange={(checked) => setTransferOptions(prev => ({ ...prev, enablePreview: checked }))}
                  />
                </div>
              </div>

              <div className="relative rounded-lg border bg-muted/50 min-h-[200px] flex items-center justify-center">
                {previewState.isGenerating ? (
                  <div className="text-center">
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Generating preview...</p>
                  </div>
                ) : previewState.error ? (
                  <div className="text-center">
                    <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
                    <p className="text-sm text-red-500">{previewState.error}</p>
                  </div>
                ) : previewState.image ? (
                  <canvas
                    ref={previewCanvasRef}
                    className="max-w-full max-h-[300px] rounded"
                  />
                ) : (
                  <div className="text-center">
                    <Eye className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Select a style to see preview
                    </p>
                  </div>
                )}
              </div>

              {previewState.processingTime && (
                <div className="text-center text-sm text-muted-foreground">
                  Preview generated in {formatTime(previewState.processingTime)}
                  {previewState.estimatedFullTime && (
                    <span className="ml-2">
                      • Est. full transfer: {formatTime(previewState.estimatedFullTime)}
                    </span>
                  )}
                </div>
              )}
            </TabsContent>

            {/* Analysis Tab */}
            <TabsContent value="analysis" className="space-y-4">
              <h3 className="font-medium">Image Analysis</h3>
              
              {analysisResult ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Style Classification</Label>
                      <div className="mt-2 space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">{analysisResult.styleClassification.primaryStyle.name}</span>
                          <Badge variant="secondary">
                            {Math.round(analysisResult.styleClassification.confidence * 100)}%
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {analysisResult.styleClassification.primaryStyle.period}
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label>Quality Metrics</Label>
                      <div className="mt-2 space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Sharpness</span>
                          <span className="text-sm">{Math.round(analysisResult.qualityMetrics.sharpness * 100)}%</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Contrast</span>
                          <span className="text-sm">{Math.round(analysisResult.qualityMetrics.contrast * 100)}%</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Saturation</span>
                          <span className="text-sm">{Math.round(analysisResult.qualityMetrics.saturation * 100)}%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label>Color Analysis</Label>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {analysisResult.features.color.palette.slice(0, 5).map((color, index) => (
                        <div
                          key={index}
                          className="w-8 h-8 rounded-full border"
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                    <div className="mt-2 text-sm text-muted-foreground">
                      Harmony: {analysisResult.features.color.harmony}
                    </div>
                  </div>

                  {recommendations.length > 0 && (
                    <div>
                      <Label>Recommendations</Label>
                      <div className="mt-2 space-y-2">
                        {recommendations.map((rec, index) => (
                          <Alert key={index}>
                            <Info className="h-4 w-4" />
                            <AlertDescription>
                              <strong>{rec.title}</strong>: {rec.description}
                            </AlertDescription>
                          </Alert>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BarChart3 className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Upload an image to see analysis
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <Separator className="my-4" />

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getProcessingStatusIcon()}
              <span className="text-sm text-muted-foreground">
                {transferStateLocal.isProcessing 
                  ? `${transferStateLocal.status} (${transferStateLocal.progress}%)`
                  : transferStateLocal.error 
                  ? 'Transfer failed'
                  : transferStateLocal.result 
                  ? 'Transfer complete'
                  : 'Ready to transfer'
                }
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setPreviewState({ image: null, isGenerating: false });
                  setTransferState({ isProcessing: false, progress: 0, status: 'idle' });
                }}
                disabled={transferStateLocal.isProcessing}
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset
              </Button>
              
              <Button
                onClick={handleStartTransfer}
                disabled={!sourceImage || !styleSelectorState.selectedStyle || transferStateLocal.isProcessing || disabled}
              >
                {transferStateLocal.isProcessing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-4 h-4 mr-2" />
                    Transfer Style
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Processing Progress */}
          {transferStateLocal.isProcessing && (
            <div className="mt-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Processing</span>
                <span className="text-sm text-muted-foreground">{transferStateLocal.progress}%</span>
              </div>
              <Progress value={transferStateLocal.progress} className="w-full" />
            </div>
          )}

          {/* Error Display */}
          {transferStateLocal.error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{transferStateLocal.error}</AlertDescription>
            </Alert>
          )}

          {/* Success Display */}
          {transferStateLocal.result && (
            <Alert className="mt-4">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Style transfer completed successfully! 
                {transferStateLocal.processingTime && (
                  <span className="ml-2">
                    Processed in {formatTime(transferStateLocal.processingTime)}
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}