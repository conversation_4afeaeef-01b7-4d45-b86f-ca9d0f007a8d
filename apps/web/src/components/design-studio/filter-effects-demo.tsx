import React, { useState, useEffect, useRef } from 'react';
import { EnhancedHybridCanvasManager } from '../../lib/canvas/enhanced-hybrid-canvas';
import { FilterEffectsPanel } from './filter-effects-panel';
import { useFiltersEffects } from '../../hooks/use-filters-effects';

interface FilterEffectsDemoProps {
  className?: string;
}

/**
 * Demo component showcasing the V3.2a Filter & Effects Pipeline
 * This component demonstrates:
 * - WebGL-accelerated filter processing
 * - Layer-based effects system
 * - Real-time parameter controls
 * - Professional filter library (20+ filters)
 * - Performance monitoring
 */
export const FilterEffectsDemo: React.FC<FilterEffectsDemoProps> = ({ className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvasManager, setCanvasManager] = useState<EnhancedHybridCanvasManager | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize canvas manager
  useEffect(() => {
    const initializeCanvas = async () => {
      if (!canvasRef.current) return;

      try {
        setIsLoading(true);
        
        // Create enhanced hybrid canvas manager
        const manager = new EnhancedHybridCanvasManager(canvasRef.current, {
          fabricConfig: {
            width: 800,
            height: 600,
            backgroundColor: '#ffffff',
            selection: true,
            preserveObjectStacking: true
          },
          hybridConfig: {
            enableAutoRouting: true,
            performanceThreshold: 60,
            useWebGLWhenAvailable: true,
            enablePerformanceMonitoring: true,
            dynamicOptimization: true
          }
        });

        // Wait for initialization
        await new Promise<void>((resolve) => {
          manager.on('hybrid:initialized', () => {
            resolve();
          });
        });

        setCanvasManager(manager);
        setError(null);

        // Add some sample content for demonstration
        addSampleContent(manager);

      } catch (err) {
        console.error('Failed to initialize canvas:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    initializeCanvas();

    return () => {
      if (canvasManager) {
        canvasManager.dispose();
      }
    };
  }, []);

  // Add sample content to demonstrate filters
  const addSampleContent = (manager: EnhancedHybridCanvasManager) => {
    const { fabric } = window as any;
    
    // Add a colorful rectangle
    const rect = new fabric.Rect({
      left: 100,
      top: 100,
      width: 200,
      height: 150,
      fill: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
      stroke: '#333',
      strokeWidth: 2
    });

    // Add a circle
    const circle = new fabric.Circle({
      left: 400,
      top: 200,
      radius: 80,
      fill: 'radial-gradient(circle, #ffeaa7, #fab1a0)',
      stroke: '#2d3436',
      strokeWidth: 3
    });

    // Add some text
    const text = new fabric.Text('Filter Effects Demo', {
      left: 200,
      top: 350,
      fontSize: 32,
      fontFamily: 'Arial',
      fill: '#2d3436',
      fontWeight: 'bold'
    });

    // Add objects to canvas
    manager.addObject(rect);
    manager.addObject(circle);
    manager.addObject(text);
  };

  // Use the filters effects hook
  const filtersEffects = useFiltersEffects(canvasManager, {
    enableWebGL2: true,
    enableFloatTextures: true,
    enableGPUAcceleration: true,
    maxFilterInstances: 32,
    enableRealTimePreview: true,
    enableAnimations: true,
    enablePresetSystem: true,
    cacheSize: 50
  });

  const handleFilterApplied = (layerId: string, filterId: string) => {
    console.log(`Filter ${filterId} applied to layer ${layerId}`);
  };

  const handleLayerCreated = (layer: any) => {
    console.log(`Layer created: ${layer.name} (${layer.id})`);
  };

  const handlePresetSaved = (presetId: string) => {
    console.log(`Preset saved: ${presetId}`);
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing WebGL Filter Pipeline...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="text-center text-red-600">
          <h3 className="text-lg font-semibold mb-2">Initialization Error</h3>
          <p>{error}</p>
          <p className="text-sm mt-2 text-gray-500">
            Make sure your browser supports WebGL
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex h-screen ${className}`}>
      {/* Canvas Area */}
      <div className="flex-1 p-4">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold">V3.2a Filter & Effects Pipeline Demo</h2>
            <p className="text-sm text-gray-600 mt-1">
              Professional-grade WebGL filters with real-time preview
            </p>
          </div>
          
          <div className="p-4">
            <canvas
              ref={canvasRef}
              width={800}
              height={600}
              className="border border-gray-300 rounded-lg shadow-inner"
              style={{ maxWidth: '100%', height: 'auto' }}
            />
          </div>

          {/* Performance Stats */}
          {filtersEffects.isInitialized && (
            <div className="px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">FPS:</span>{' '}
                  <span className="text-green-600">
                    {filtersEffects.performanceMetrics.averageFPS.toFixed(1)}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Memory:</span>{' '}
                  <span className="text-blue-600">
                    {(filtersEffects.memoryUsage / 1024 / 1024).toFixed(1)}MB
                  </span>
                </div>
                <div>
                  <span className="font-medium">Layers:</span>{' '}
                  <span className="text-purple-600">
                    {filtersEffects.activeLayers.length}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Filters:</span>{' '}
                  <span className="text-orange-600">
                    {filtersEffects.availableFilters.length}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Filter Effects Panel */}
      <div className="w-96 border-l border-gray-200 bg-white">
        <FilterEffectsPanel
          canvasManager={canvasManager}
          onFilterApplied={handleFilterApplied}
          onLayerCreated={handleLayerCreated}
          onPresetSaved={handlePresetSaved}
          className="h-full"
        />
      </div>
    </div>
  );
};

export default FilterEffectsDemo;