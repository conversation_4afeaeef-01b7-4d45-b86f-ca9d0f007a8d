'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  DollarSign, 
  Settings, 
  TrendingUp, 
  Zap,
  Clock,
  Star,
  MoreHorizontal,
  RefreshCw,
  TestTube,
  Shield,
  BarChart3
} from 'lucide-react';
import { 
  useMultiProviderAI, 
  useProviderSelection, 
  useBudgetMonitoring 
} from '../../hooks/use-multi-provider-ai';
import { SelectionPriority, TaskType } from '../../lib/ai/provider-selection-engine';

interface ProviderManagementPanelProps {
  multiProviderAI: ReturnType<typeof useMultiProviderAI>;
  className?: string;
}

interface ProviderTestDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onTest: (provider: string) => Promise<{ success: boolean; error?: string; responseTime?: number }>;
  providers: string[];
}

interface BudgetConfigDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (budgets: Record<string, { daily: number; monthly: number }>) => void;
  currentBudgets: Record<string, { daily: { limit: number }; monthly: { limit: number } }>;
}

const ProviderTestDialog: React.FC<ProviderTestDialogProps> = ({
  isOpen,
  onClose,
  onTest,
  providers
}) => {
  const [selectedProvider, setSelectedProvider] = useState('');
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [testing, setTesting] = useState(false);

  const handleTest = async (provider: string) => {
    setTesting(true);
    try {
      const result = await onTest(provider);
      setTestResults(prev => ({ ...prev, [provider]: result }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        [provider]: { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        } 
      }));
    }
    setTesting(false);
  };

  const handleTestAll = async () => {
    setTesting(true);
    setTestResults({});
    
    for (const provider of providers) {
      await handleTest(provider);
    }
    
    setTesting(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TestTube className="w-5 h-5" />
            Provider Testing
          </DialogTitle>
          <DialogDescription>
            Test individual providers or run a comprehensive test across all providers
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="flex gap-2">
            <Select value={selectedProvider} onValueChange={setSelectedProvider}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select provider to test" />
              </SelectTrigger>
              <SelectContent>
                {providers.map(provider => (
                  <SelectItem key={provider} value={provider}>
                    {provider}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              onClick={() => selectedProvider && handleTest(selectedProvider)}
              disabled={!selectedProvider || testing}
            >
              <TestTube className="w-4 h-4 mr-2" />
              Test
            </Button>
            <Button 
              onClick={handleTestAll}
              disabled={testing}
              variant="outline"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Test All
            </Button>
          </div>
          
          {Object.keys(testResults).length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Test Results</h4>
              {providers.map(provider => {
                const result = testResults[provider];
                if (!result) return null;
                
                return (
                  <div key={provider} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-2">
                      {result.success ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <AlertTriangle className="w-4 h-4 text-red-500" />
                      )}
                      <span className="font-medium">{provider}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {result.success ? (
                        <span>✓ {result.responseTime}ms</span>
                      ) : (
                        <span>✗ {result.error}</span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const BudgetConfigDialog: React.FC<BudgetConfigDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  currentBudgets
}) => {
  const [budgets, setBudgets] = useState(() => {
    const initialBudgets: Record<string, { daily: number; monthly: number }> = {};
    Object.entries(currentBudgets).forEach(([provider, budget]) => {
      initialBudgets[provider] = {
        daily: budget.daily.limit,
        monthly: budget.monthly.limit
      };
    });
    return initialBudgets;
  });

  const handleSave = () => {
    onSave(budgets);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Budget Configuration
          </DialogTitle>
          <DialogDescription>
            Set daily and monthly spending limits for each provider
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {Object.entries(budgets).map(([provider, budget]) => (
            <Card key={provider}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium capitalize">{provider}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor={`${provider}-daily`} className="text-xs">Daily Limit ($)</Label>
                    <Input
                      id={`${provider}-daily`}
                      type="number"
                      min="0"
                      step="0.01"
                      value={budget.daily}
                      onChange={(e) => setBudgets(prev => ({
                        ...prev,
                        [provider]: { ...prev[provider], daily: parseFloat(e.target.value) || 0 }
                      }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`${provider}-monthly`} className="text-xs">Monthly Limit ($)</Label>
                    <Input
                      id={`${provider}-monthly`}
                      type="number"
                      min="0"
                      step="0.01"
                      value={budget.monthly}
                      onChange={(e) => setBudgets(prev => ({
                        ...prev,
                        [provider]: { ...prev[provider], monthly: parseFloat(e.target.value) || 0 }
                      }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const ProviderManagementPanel: React.FC<ProviderManagementPanelProps> = ({
  multiProviderAI,
  className = ''
}) => {
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [budgetDialogOpen, setBudgetDialogOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState('overview');

  const { state, actions, isReady } = multiProviderAI;
  
  const providerSelection = useProviderSelection(multiProviderAI);
  const budgetMonitoring = useBudgetMonitoring(multiProviderAI);

  // Handle provider preference changes
  const handleProviderPreferenceChange = useCallback((provider: string, preferred: boolean) => {
    actions.setProviderPreference(provider, preferred);
  }, [actions]);

  const handleProviderExclusionChange = useCallback((provider: string, excluded: boolean) => {
    actions.excludeProvider(provider, excluded);
  }, [actions]);

  const handlePriorityChange = useCallback((priority: SelectionPriority) => {
    actions.setDefaultPriority(priority);
  }, [actions]);

  const handleBudgetSave = useCallback((budgets: Record<string, { daily: number; monthly: number }>) => {
    Object.entries(budgets).forEach(([provider, budget]) => {
      if (provider === 'total') {
        actions.setBudgetLimit('daily', budget.daily);
        actions.setBudgetLimit('monthly', budget.monthly);
      } else {
        actions.setProviderBudgetLimit(provider, 'daily', budget.daily);
        actions.setProviderBudgetLimit(provider, 'monthly', budget.monthly);
      }
    });
  }, [actions]);

  // Compute provider health status
  const getProviderHealthStatus = (status: any) => {
    if (!status) return { icon: AlertTriangle, color: 'text-gray-400', label: 'Unknown' };
    
    switch (status.health?.status) {
      case 'healthy': return { icon: CheckCircle, color: 'text-green-500', label: 'Healthy' };
      case 'degraded': return { icon: AlertTriangle, color: 'text-yellow-500', label: 'Degraded' };
      case 'unhealthy': return { icon: AlertTriangle, color: 'text-red-500', label: 'Unhealthy' };
      default: return { icon: Activity, color: 'text-blue-500', label: 'Checking' };
    }
  };

  // Render provider status overview
  const renderProviderOverview = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Provider Status</h3>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setTestDialogOpen(true)}
          >
            <TestTube className="w-4 h-4 mr-2" />
            Test Providers
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setBudgetDialogOpen(true)}
          >
            <Settings className="w-4 h-4 mr-2" />
            Configure Budgets
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {state.availableProviders.map(provider => {
          const status = state.providerStatuses[provider];
          const budget = state.budgetSummary[provider];
          const health = getProviderHealthStatus(status);
          const HealthIcon = health.icon;

          return (
            <Card key={provider}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium capitalize">{provider}</CardTitle>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => actions.testProvider(provider)}>
                        <TestTube className="w-4 h-4 mr-2" />
                        Test Provider
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleProviderPreferenceChange(
                          provider, 
                          !state.providerPreferences.preferredProviders.includes(provider)
                        )}
                      >
                        <Star className="w-4 h-4 mr-2" />
                        {state.providerPreferences.preferredProviders.includes(provider) ? 'Remove Preference' : 'Set Preferred'}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleProviderExclusionChange(
                          provider, 
                          !state.providerPreferences.excludedProviders.includes(provider)
                        )}
                      >
                        <Shield className="w-4 h-4 mr-2" />
                        {state.providerPreferences.excludedProviders.includes(provider) ? 'Remove Exclusion' : 'Exclude Provider'}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <HealthIcon className={`w-4 h-4 ${health.color}`} />
                  <span className="text-sm">{health.label}</span>
                  {status?.models && (
                    <Badge variant="secondary" className="text-xs">
                      {status.models} models
                    </Badge>
                  )}
                </div>

                {budget && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Daily Budget</span>
                      <span>${budget.daily.spent.toFixed(2)} / ${budget.daily.limit.toFixed(2)}</span>
                    </div>
                    <Progress 
                      value={(budget.daily.spent / budget.daily.limit) * 100} 
                      className="h-1"
                    />
                    
                    <div className="flex justify-between text-xs">
                      <span>Monthly Budget</span>
                      <span>${budget.monthly.spent.toFixed(2)} / ${budget.monthly.limit.toFixed(2)}</span>
                    </div>
                    <Progress 
                      value={(budget.monthly.spent / budget.monthly.limit) * 100} 
                      className="h-1"
                    />
                  </div>
                )}

                <div className="flex gap-1">
                  {state.providerPreferences.preferredProviders.includes(provider) && (
                    <Badge variant="default" className="text-xs">
                      <Star className="w-3 h-3 mr-1" />
                      Preferred
                    </Badge>
                  )}
                  {state.providerPreferences.excludedProviders.includes(provider) && (
                    <Badge variant="destructive" className="text-xs">
                      <Shield className="w-3 h-3 mr-1" />
                      Excluded
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );

  // Render budget monitoring
  const renderBudgetMonitoring = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Budget Monitoring</h3>
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setBudgetDialogOpen(true)}
        >
          <Settings className="w-4 h-4 mr-2" />
          Configure
        </Button>
      </div>

      {budgetMonitoring.budgetWarnings.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-orange-600">Budget Alerts</h4>
          {budgetMonitoring.budgetWarnings.map((warning, index) => (
            <Alert key={index} className="border-orange-200">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription>
                <strong>{warning.provider}</strong> has exceeded {Math.round(warning.percentage * 100)}% 
                of {warning.period} budget ({warning.severity} threshold)
              </AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(state.budgetSummary).map(([provider, budget]) => (
          <Card key={provider}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium capitalize">{provider} Budget</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Daily</span>
                  <span className="text-sm font-medium">
                    ${budget.daily.spent.toFixed(2)} / ${budget.daily.limit.toFixed(2)}
                  </span>
                </div>
                <Progress 
                  value={(budget.daily.spent / budget.daily.limit) * 100} 
                  className="h-2"
                />
                <div className="text-xs text-muted-foreground">
                  ${budget.daily.remaining.toFixed(2)} remaining
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Monthly</span>
                  <span className="text-sm font-medium">
                    ${budget.monthly.spent.toFixed(2)} / ${budget.monthly.limit.toFixed(2)}
                  </span>
                </div>
                <Progress 
                  value={(budget.monthly.spent / budget.monthly.limit) * 100} 
                  className="h-2"
                />
                <div className="text-xs text-muted-foreground">
                  ${budget.monthly.remaining.toFixed(2)} remaining
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  // Render analytics
  const renderAnalytics = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Analytics & Insights</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">Total Generations</span>
            </div>
            <div className="text-2xl font-bold mt-1">{state.metrics.totalGenerations}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium">Success Rate</span>
            </div>
            <div className="text-2xl font-bold mt-1">{(state.metrics.successRate * 100).toFixed(1)}%</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium">Avg Cost</span>
            </div>
            <div className="text-2xl font-bold mt-1">${state.metrics.averageCost.toFixed(3)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-purple-500" />
              <span className="text-sm font-medium">Avg Time</span>
            </div>
            <div className="text-2xl font-bold mt-1">{Math.round(state.metrics.averageTime)}s</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Provider Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(state.metrics.providerDistribution).map(([provider, count]) => (
                <div key={provider} className="flex justify-between items-center">
                  <span className="text-sm capitalize">{provider}</span>
                  <span className="text-sm font-medium">{count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {state.generationHistory.slice(0, 5).map((generation, index) => (
                <div key={generation.id} className="flex justify-between items-center text-sm">
                  <div className="flex items-center gap-2">
                    {generation.status === 'completed' ? (
                      <CheckCircle className="w-3 h-3 text-green-500" />
                    ) : (
                      <AlertTriangle className="w-3 h-3 text-red-500" />
                    )}
                    <span className="truncate max-w-32">
                      {generation.metadata.prompt.slice(0, 30)}...
                    </span>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {generation.provider}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // Render selection preferences
  const renderSelectionPreferences = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Selection Preferences</h3>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Default Priority</CardTitle>
        </CardHeader>
        <CardContent>
          <Select 
            value={state.providerPreferences.defaultPriority} 
            onValueChange={handlePriorityChange}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cost">Cost - Prioritize cheapest options</SelectItem>
              <SelectItem value="quality">Quality - Prioritize best results</SelectItem>
              <SelectItem value="speed">Speed - Prioritize fastest generation</SelectItem>
              <SelectItem value="balanced">Balanced - Optimize across all factors</SelectItem>
              <SelectItem value="features">Features - Prioritize capabilities</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Provider Preferences</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {state.availableProviders.map(provider => (
              <div key={provider} className="flex items-center justify-between">
                <span className="text-sm capitalize">{provider}</span>
                <div className="flex gap-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor={`prefer-${provider}`} className="text-xs">Preferred</Label>
                    <Switch
                      id={`prefer-${provider}`}
                      checked={state.providerPreferences.preferredProviders.includes(provider)}
                      onCheckedChange={(checked) => handleProviderPreferenceChange(provider, checked)}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Label htmlFor={`exclude-${provider}`} className="text-xs">Excluded</Label>
                    <Switch
                      id={`exclude-${provider}`}
                      checked={state.providerPreferences.excludedProviders.includes(provider)}
                      onCheckedChange={(checked) => handleProviderExclusionChange(provider, checked)}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  if (!isReady) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">Loading providers...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Multi-Provider Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="budget">Budget</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="mt-4">
            {renderProviderOverview()}
          </TabsContent>
          
          <TabsContent value="budget" className="mt-4">
            {renderBudgetMonitoring()}
          </TabsContent>
          
          <TabsContent value="analytics" className="mt-4">
            {renderAnalytics()}
          </TabsContent>
          
          <TabsContent value="preferences" className="mt-4">
            {renderSelectionPreferences()}
          </TabsContent>
        </Tabs>

        {/* Dialogs */}
        <ProviderTestDialog
          isOpen={testDialogOpen}
          onClose={() => setTestDialogOpen(false)}
          onTest={actions.testProvider}
          providers={state.availableProviders}
        />

        <BudgetConfigDialog
          isOpen={budgetDialogOpen}
          onClose={() => setBudgetDialogOpen(false)}
          onSave={handleBudgetSave}
          currentBudgets={state.budgetSummary}
        />
      </CardContent>
    </Card>
  );
};