"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  Image, 
  Zap, 
  BarChart3, 
  CheckCircle, 
  AlertTriangle,
  FileImage,
  Layers,
  Globe,
  Smartphone,
  Printer,
  RefreshCw
} from 'lucide-react';
import { ExportPanel } from './export-panel';
import { useExportOptimization } from '../../hooks/use-export-optimization';
import { ExportFormat, ExportOptions } from '../../lib/export/export-engine';
import { OptimizationStrategy } from '../../lib/export/optimization-pipeline';
import { fabric } from 'fabric';
import { cn } from '@/lib/utils';

interface ExportOptimizationDemoProps {
  className?: string;
}

const demoCanvasOptions = {
  width: 800,
  height: 600,
  backgroundColor: '#f8f9fa'
};

export function ExportOptimizationDemo({ className }: ExportOptimizationDemoProps) {
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [activeDemo, setActiveDemo] = useState<'basic' | 'advanced' | 'batch'>('basic');
  
  // Initialize export system
  const exportSystem = useExportOptimization(canvas, {
    enableAutoOptimization: true,
    enablePreviewGeneration: true,
    enablePerformanceMonitoring: true,
    enableAdaptiveQuality: true,
    defaultStrategy: 'balanced'
  });

  // Initialize demo canvas
  useEffect(() => {
    const canvasElement = document.getElementById('export-demo-canvas') as HTMLCanvasElement;
    if (canvasElement) {
      const fabricCanvas = new fabric.Canvas(canvasElement, demoCanvasOptions);
      
      // Add some demo objects
      addDemoObjects(fabricCanvas);
      
      setCanvas(fabricCanvas);
      
      return () => {
        fabricCanvas.dispose();
      };
    }
  }, []);

  const addDemoObjects = (canvas: fabric.Canvas) => {
    // Add a rectangle
    const rect = new fabric.Rect({
      left: 100,
      top: 100,
      width: 200,
      height: 150,
      fill: '#e74c3c',
      stroke: '#c0392b',
      strokeWidth: 2,
      rx: 10,
      ry: 10
    });

    // Add a circle
    const circle = new fabric.Circle({
      left: 400,
      top: 150,
      radius: 80,
      fill: '#3498db',
      stroke: '#2980b9',
      strokeWidth: 3
    });

    // Add text
    const text = new fabric.Text('Export Demo', {
      left: 200,
      top: 350,
      fontSize: 36,
      fontFamily: 'Arial',
      fill: '#2c3e50',
      fontWeight: 'bold'
    });

    // Add a triangle
    const triangle = new fabric.Triangle({
      left: 550,
      top: 300,
      width: 100,
      height: 120,
      fill: '#f39c12',
      stroke: '#e67e22',
      strokeWidth: 2
    });

    canvas.add(rect, circle, text, triangle);
    canvas.renderAll();
  };

  const handleQuickExport = async (format: ExportFormat, preset?: string) => {
    if (!canvas) return;

    try {
      const options: ExportOptions = {
        format,
        quality: {
          format,
          quality: 85,
          compression: 'lossy',
          dpi: 72,
          colorSpace: 'sRGB',
          enableProgressive: true,
          enableOptimization: true
        },
        scale: 1,
        includeMetadata: true,
        enableProgressive: true,
        cropToContent: false,
        enableTransparency: format !== 'JPEG',
        enableColorManagement: true
      };

      const result = await exportSystem.exportCanvas(options);
      
      if (result.success) {
        await exportSystem.downloadExport(result, `demo-${format.toLowerCase()}-${Date.now()}`);
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleBatchExport = async () => {
    if (!canvas) return;

    try {
      const batchConfig = {
        formats: ['PNG', 'JPEG', 'WebP'] as ExportFormat[],
        sizes: [
          { width: 800, height: 600, name: 'Original' },
          { width: 400, height: 300, name: 'Half' },
          { width: 200, height: 150, name: 'Thumbnail' }
        ],
        quality: {
          format: 'PNG' as ExportFormat,
          quality: 85,
          compression: 'lossy' as const,
          dpi: 72,
          colorSpace: 'sRGB' as const,
          enableProgressive: true,
          enableOptimization: true
        },
        enableParallelProcessing: true,
        maxConcurrentExports: 3
      };

      const results = await exportSystem.batchExport(batchConfig);
      
      // Download all successful exports
      for (const result of results) {
        if (result.success) {
          await exportSystem.downloadExport(result);
        }
      }
    } catch (error) {
      console.error('Batch export failed:', error);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn("w-full max-w-7xl mx-auto space-y-6", className)}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">Export & Optimization Demo</h2>
        <p className="text-muted-foreground">
          Comprehensive export system with professional optimization and multi-format support
        </p>
      </div>

      {/* Canvas Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Image className="w-5 h-5" />
            Demo Canvas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center">
            <canvas
              id="export-demo-canvas"
              className="border border-gray-300 rounded-lg shadow-sm"
            />
          </div>
        </CardContent>
      </Card>

      {/* Export Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Export Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-5 h-5" />
              Quick Export
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => handleQuickExport('PNG')}
                variant="outline"
                className="justify-start"
                disabled={exportSystem.exportState.isExporting}
              >
                <FileImage className="w-4 h-4 mr-2" />
                PNG
              </Button>
              <Button
                onClick={() => handleQuickExport('JPEG')}
                variant="outline"
                className="justify-start"
                disabled={exportSystem.exportState.isExporting}
              >
                <Image className="w-4 h-4 mr-2" />
                JPEG
              </Button>
              <Button
                onClick={() => handleQuickExport('WebP')}
                variant="outline"
                className="justify-start"
                disabled={exportSystem.exportState.isExporting}
              >
                <Globe className="w-4 h-4 mr-2" />
                WebP
              </Button>
              <Button
                onClick={() => handleQuickExport('SVG')}
                variant="outline"
                className="justify-start"
                disabled={exportSystem.exportState.isExporting}
              >
                <Layers className="w-4 h-4 mr-2" />
                SVG
              </Button>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Preset Exports</div>
              <div className="grid grid-cols-1 gap-1">
                <Button
                  onClick={() => handleQuickExport('PNG', 'web-optimized')}
                  variant="outline"
                  size="sm"
                  className="justify-start"
                  disabled={exportSystem.exportState.isExporting}
                >
                  <Globe className="w-4 h-4 mr-2" />
                  Web Optimized
                </Button>
                <Button
                  onClick={() => handleQuickExport('PNG', 'mobile-optimized')}
                  variant="outline"
                  size="sm"
                  className="justify-start"
                  disabled={exportSystem.exportState.isExporting}
                >
                  <Smartphone className="w-4 h-4 mr-2" />
                  Mobile Optimized
                </Button>
                <Button
                  onClick={() => handleQuickExport('PDF', 'print-ready')}
                  variant="outline"
                  size="sm"
                  className="justify-start"
                  disabled={exportSystem.exportState.isExporting}
                >
                  <Printer className="w-4 h-4 mr-2" />
                  Print Ready
                </Button>
              </div>
            </div>

            <Button
              onClick={handleBatchExport}
              className="w-full"
              disabled={exportSystem.exportState.isBatchExporting}
            >
              {exportSystem.exportState.isBatchExporting ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              Batch Export (9 Files)
            </Button>
          </CardContent>
        </Card>

        {/* Performance Monitor */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Performance Monitor
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className="text-sm font-medium">Total Exports</div>
                <div className="text-2xl font-bold">
                  {exportSystem.performanceMonitor.exportMetrics.totalExports}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium">Success Rate</div>
                <div className="text-2xl font-bold text-green-600">
                  {Math.round(exportSystem.performanceMonitor.exportMetrics.successRate)}%
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium">Avg Export Time</div>
                <div className="text-2xl font-bold">
                  {Math.round(exportSystem.performanceMonitor.exportMetrics.averageExportTime)}ms
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium">Data Exported</div>
                <div className="text-2xl font-bold">
                  {formatFileSize(exportSystem.performanceMonitor.exportMetrics.totalDataExported)}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Cache Hit Rate</div>
              <div className="flex items-center gap-2">
                <Progress value={exportSystem.performanceMonitor.cacheHitRate} className="flex-1" />
                <span className="text-sm text-muted-foreground">
                  {Math.round(exportSystem.performanceMonitor.cacheHitRate)}%
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Compression Efficiency</div>
              <div className="flex items-center gap-2">
                <Progress value={exportSystem.performanceMonitor.exportMetrics.compressionEfficiency} className="flex-1" />
                <span className="text-sm text-muted-foreground">
                  {Math.round(exportSystem.performanceMonitor.exportMetrics.compressionEfficiency)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Export Status */}
      {exportSystem.exportState.progress && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">Export Progress</div>
                <span className="text-sm text-muted-foreground">
                  {exportSystem.exportState.progress.progress}%
                </span>
              </div>
              <Progress value={exportSystem.exportState.progress.progress} />
              <div className="text-sm text-muted-foreground">
                {exportSystem.exportState.progress.message}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Batch Export Status */}
      {exportSystem.batchExportState.isActive && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">Batch Export Progress</div>
                <span className="text-sm text-muted-foreground">
                  {exportSystem.batchExportState.currentFile}/{exportSystem.batchExportState.totalFiles}
                </span>
              </div>
              <Progress value={exportSystem.batchExportState.progress} />
              <div className="text-sm text-muted-foreground">
                Processing file {exportSystem.batchExportState.currentFile} of {exportSystem.batchExportState.totalFiles}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quality Monitor */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Quality Monitor
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium">Average Quality</div>
              <div className="text-2xl font-bold">
                {Math.round(exportSystem.qualityMonitor.averageQuality)}%
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium">Current Quality</div>
              <div className="text-2xl font-bold">
                {exportSystem.qualityMonitor.currentAssessment?.score || 0}%
              </div>
            </div>
          </div>

          {exportSystem.qualityMonitor.recommendations.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {exportSystem.qualityMonitor.recommendations.join(', ')}
              </AlertDescription>
            </Alert>
          )}

          {exportSystem.qualityMonitor.warnings.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {exportSystem.qualityMonitor.warnings.join(', ')}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Preview Data */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Image className="w-5 h-5" />
            Preview & Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium">Original Size</div>
              <div className="text-lg font-bold">
                {formatFileSize(exportSystem.previewData.originalSize)}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium">Optimized Size</div>
              <div className="text-lg font-bold text-green-600">
                {formatFileSize(exportSystem.previewData.optimizedSize)}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium">Compression</div>
              <div className="text-lg font-bold">
                {Math.round(exportSystem.previewData.compressionRatio * 100)}%
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm font-medium">Quality Score</div>
            <div className="flex items-center gap-2">
              <Progress value={exportSystem.previewData.qualityScore} className="flex-1" />
              <span className="text-sm text-muted-foreground">
                {Math.round(exportSystem.previewData.qualityScore)}%
              </span>
            </div>
          </div>

          {exportSystem.previewData.previewUrl && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Preview</div>
              <div className="relative w-full h-40 bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={exportSystem.previewData.previewUrl}
                  alt="Export preview"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Export History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Export History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {exportSystem.exportHistory.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                No exports yet
              </div>
            ) : (
              exportSystem.exportHistory.slice(0, 5).map((entry) => (
                <div key={entry.id} className="flex items-center justify-between p-2 bg-muted rounded">
                  <div className="flex items-center gap-2">
                    {entry.success ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-red-600" />
                    )}
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {entry.format} - {entry.dimensions.width}x{entry.dimensions.height}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(entry.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm">{formatFileSize(entry.size)}</div>
                    <Badge variant="secondary" className="text-xs">
                      Q: {entry.quality.quality}%
                    </Badge>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Export Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Advanced Export Controls
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ExportPanel canvas={canvas} />
        </CardContent>
      </Card>
    </div>
  );
}