// apps/web/src/components/design-studio/component-library-panel.tsx
// Component Library Panel - Interactive component browser and management interface

"use client";

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { 
  Search, 
  Plus, 
  Grid, 
  List, 
  Filter, 
  Download, 
  Upload, 
  Star, 
  Tag, 
  Folder, 
  FolderOpen, 
  ChevronRight, 
  ChevronDown,
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  Eye,
  Heart,
  Share2,
  Settings,
  Zap,
  Package,
  Palette,
  Type,
  Image,
  Video,
  FileText,
  Music,
  Code,
  Layers,
  MousePointer,
  Users,
  Clock,
  TrendingUp,
  BookOpen,
  ShoppingCart,
  Award,
  Target,
  GitBranch,
  Sparkles,
  Wand2,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';

import { 
  DesignComponent, 
  ComponentCategory, 
  ComponentSearchFilters, 
  ComponentSearchResult,
  AssetType,
  AssetSearchFilters,
  DesignAsset,
  AssetSearchResult,
  ComponentInstance,
  PropertyType,
  ComponentProperty,
  ComponentVariant,
  OverrideType
} from '@/lib/design-system/component-library-engine';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ComponentLibraryPanelProps {
  isOpen: boolean;
  onToggle: () => void;
  onComponentCreate: (component: DesignComponent) => void;
  onComponentInstantiate: (componentId: string, position: { x: number; y: number }) => void;
  onAssetUpload: (files: FileList) => void;
  components: DesignComponent[];
  categories: ComponentCategory[];
  assets: DesignAsset[];
  selectedComponent?: DesignComponent;
  selectedAsset?: DesignAsset;
  onComponentSelect: (component: DesignComponent) => void;
  onAssetSelect: (asset: DesignAsset) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  isLoading?: boolean;
  error?: string;
}

interface ComponentListItemProps {
  component: DesignComponent;
  isSelected: boolean;
  onSelect: (component: DesignComponent) => void;
  onInstantiate: (componentId: string) => void;
  onEdit: (component: DesignComponent) => void;
  onDelete: (componentId: string) => void;
  onDuplicate: (component: DesignComponent) => void;
  viewMode: 'grid' | 'list';
}

interface AssetListItemProps {
  asset: DesignAsset;
  isSelected: boolean;
  onSelect: (asset: DesignAsset) => void;
  onUse: (assetId: string) => void;
  onEdit: (asset: DesignAsset) => void;
  onDelete: (assetId: string) => void;
  viewMode: 'grid' | 'list';
}

interface ComponentPropertiesProps {
  component: DesignComponent;
  selectedVariant: string;
  onVariantChange: (variantId: string) => void;
  onPropertyChange: (propertyId: string, value: any) => void;
  overrides: { [key: string]: any };
}

interface AssetPreviewProps {
  asset: DesignAsset;
  onClose: () => void;
  onUse: (assetId: string) => void;
  onDownload: (assetId: string) => void;
  onAddToCollection: (assetId: string) => void;
}

const ComponentLibraryPanel: React.FC<ComponentLibraryPanelProps> = ({
  isOpen,
  onToggle,
  onComponentCreate,
  onComponentInstantiate,
  onAssetUpload,
  components,
  categories,
  assets,
  selectedComponent,
  selectedAsset,
  onComponentSelect,
  onAssetSelect,
  searchQuery,
  onSearchChange,
  viewMode,
  onViewModeChange,
  isLoading,
  error
}) => {
  const [activeTab, setActiveTab] = useState<'components' | 'assets' | 'collections'>('components');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedAssetType, setSelectedAssetType] = useState<AssetType | 'all'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'popularity'>('name');
  const [filterTags, setFilterTags] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewAsset, setPreviewAsset] = useState<DesignAsset | null>(null);
  const [searchResults, setSearchResults] = useState<(ComponentSearchResult | AssetSearchResult)[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dragOverRef = useRef<HTMLDivElement>(null);

  // Filter and sort components
  const filteredComponents = useMemo(() => {
    let filtered = components;

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(comp => comp.category === selectedCategory);
    }

    // Apply tag filter
    if (filterTags.length > 0) {
      filtered = filtered.filter(comp => 
        filterTags.some(tag => comp.tags.includes(tag))
      );
    }

    // Apply search query
    if (searchQuery) {
      filtered = filtered.filter(comp => 
        comp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        comp.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        comp.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Sort components
    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'date':
        filtered.sort((a, b) => b.updated.getTime() - a.updated.getTime());
        break;
      case 'popularity':
        filtered.sort((a, b) => b.usage.popularity - a.usage.popularity);
        break;
    }

    return filtered;
  }, [components, selectedCategory, filterTags, searchQuery, sortBy]);

  // Filter and sort assets
  const filteredAssets = useMemo(() => {
    let filtered = assets;

    // Apply type filter
    if (selectedAssetType !== 'all') {
      filtered = filtered.filter(asset => asset.type === selectedAssetType);
    }

    // Apply search query
    if (searchQuery) {
      filtered = filtered.filter(asset => 
        asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Sort assets
    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'date':
        filtered.sort((a, b) => b.updated.getTime() - a.updated.getTime());
        break;
      case 'popularity':
        filtered.sort((a, b) => b.usage.popularity - a.usage.popularity);
        break;
    }

    return filtered;
  }, [assets, selectedAssetType, searchQuery, sortBy]);

  // Handle category toggle
  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // Handle file upload
  const handleFileUpload = async (files: FileList) => {
    if (files.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      await onAssetUpload(files);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 1000);
    } catch (error) {
      console.error('Upload failed:', error);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };

  // Handle component instantiation
  const handleComponentInstantiate = (componentId: string) => {
    // Default position - center of canvas
    const position = { x: 400, y: 300 };
    onComponentInstantiate(componentId, position);
  };

  // Handle asset usage
  const handleAssetUse = (assetId: string) => {
    // Implementation depends on how assets are used in the design
    console.log('Use asset:', assetId);
  };

  // Get asset type icon
  const getAssetTypeIcon = (type: AssetType) => {
    switch (type) {
      case AssetType.IMAGE:
        return <Image className="w-4 h-4" />;
      case AssetType.VECTOR:
        return <Zap className="w-4 h-4" />;
      case AssetType.ICON:
        return <Sparkles className="w-4 h-4" />;
      case AssetType.FONT:
        return <Type className="w-4 h-4" />;
      case AssetType.COLOR_PALETTE:
        return <Palette className="w-4 h-4" />;
      case AssetType.VIDEO:
        return <Video className="w-4 h-4" />;
      case AssetType.AUDIO:
        return <Music className="w-4 h-4" />;
      case AssetType.DOCUMENT:
        return <FileText className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  // Get file size display
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) {
    return (
      <div className="fixed right-4 top-20 z-50">
        <Button
          onClick={onToggle}
          variant="outline"
          size="sm"
          className="bg-white/90 backdrop-blur-sm border-gray-200 hover:bg-white/95"
        >
          <Layers className="w-4 h-4 mr-2" />
          Library
        </Button>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="fixed right-4 top-4 bottom-4 w-80 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg z-50 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-gray-900">Component Library</h2>
            <Button
              onClick={onToggle}
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
            >
              <XCircle className="w-4 h-4" />
            </Button>
          </div>

          {/* Search */}
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search components and assets..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 pr-4 py-2 text-sm"
            />
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="components" className="text-xs">
                <Package className="w-3 h-3 mr-1" />
                Components
              </TabsTrigger>
              <TabsTrigger value="assets" className="text-xs">
                <Image className="w-3 h-3 mr-1" />
                Assets
              </TabsTrigger>
              <TabsTrigger value="collections" className="text-xs">
                <Folder className="w-3 h-3 mr-1" />
                Collections
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Toolbar */}
        <div className="p-3 border-b border-gray-200 bg-gray-50/50">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-1">
              <Button
                onClick={() => onViewModeChange(viewMode === 'grid' ? 'list' : 'grid')}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
              </Button>
              
              <Button
                onClick={() => setShowFilters(!showFilters)}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <Filter className="w-4 h-4" />
              </Button>
            </div>

            <div className="flex items-center gap-1">
              {activeTab === 'assets' && (
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  disabled={isUploading}
                >
                  <Upload className="w-4 h-4" />
                </Button>
              )}
              
              <Button
                onClick={() => {}}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="space-y-2 p-2 bg-white rounded border">
              <div className="flex items-center gap-2">
                <Label className="text-xs font-medium">Sort:</Label>
                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger className="h-7 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="popularity">Popularity</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {activeTab === 'components' && (
                <div className="flex items-center gap-2">
                  <Label className="text-xs font-medium">Category:</Label>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="h-7 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.name}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {activeTab === 'assets' && (
                <div className="flex items-center gap-2">
                  <Label className="text-xs font-medium">Type:</Label>
                  <Select value={selectedAssetType} onValueChange={(value: any) => setSelectedAssetType(value)}>
                    <SelectTrigger className="h-7 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {Object.values(AssetType).map(type => (
                        <SelectItem key={type} value={type}>
                          <div className="flex items-center gap-2">
                            {getAssetTypeIcon(type)}
                            {type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} className="h-full">
            <TabsContent value="components" className="h-full m-0">
              <ScrollArea className="h-full">
                <div className="p-3">
                  {error && (
                    <Alert className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  {isLoading ? (
                    <div className="space-y-3">
                      {[...Array(6)].map((_, i) => (
                        <div key={i} className="animate-pulse">
                          <div className="h-16 bg-gray-200 rounded-lg"></div>
                        </div>
                      ))}
                    </div>
                  ) : filteredComponents.length === 0 ? (
                    <div className="text-center py-12">
                      <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 text-sm">No components found</p>
                      <p className="text-gray-400 text-xs">Try adjusting your filters or search query</p>
                    </div>
                  ) : (
                    <div className={viewMode === 'grid' ? 'grid grid-cols-2 gap-3' : 'space-y-2'}>
                      {filteredComponents.map(component => (
                        <ComponentListItem
                          key={component.id}
                          component={component}
                          isSelected={selectedComponent?.id === component.id}
                          onSelect={onComponentSelect}
                          onInstantiate={handleComponentInstantiate}
                          onEdit={() => {}}
                          onDelete={() => {}}
                          onDuplicate={() => {}}
                          viewMode={viewMode}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="assets" className="h-full m-0">
              <ScrollArea className="h-full">
                <div 
                  className="p-3"
                  ref={dragOverRef}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                >
                  {isUploading && (
                    <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
                        <span className="text-sm text-blue-800">Uploading assets...</span>
                      </div>
                      <Progress value={uploadProgress} className="h-2" />
                    </div>
                  )}

                  {filteredAssets.length === 0 ? (
                    <div className="text-center py-12">
                      <Image className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 text-sm">No assets found</p>
                      <p className="text-gray-400 text-xs mb-4">
                        Drag and drop files here or use the upload button
                      </p>
                      <Button
                        onClick={() => fileInputRef.current?.click()}
                        variant="outline"
                        size="sm"
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Assets
                      </Button>
                    </div>
                  ) : (
                    <div className={viewMode === 'grid' ? 'grid grid-cols-2 gap-3' : 'space-y-2'}>
                      {filteredAssets.map(asset => (
                        <AssetListItem
                          key={asset.id}
                          asset={asset}
                          isSelected={selectedAsset?.id === asset.id}
                          onSelect={onAssetSelect}
                          onUse={handleAssetUse}
                          onEdit={() => {}}
                          onDelete={() => {}}
                          viewMode={viewMode}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="collections" className="h-full m-0">
              <ScrollArea className="h-full">
                <div className="p-3">
                  <div className="text-center py-12">
                    <Folder className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 text-sm">Collections coming soon</p>
                    <p className="text-gray-400 text-xs">
                      Organize your components and assets into collections
                    </p>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        {/* Selected Component/Asset Details */}
        {selectedComponent && (
          <div className="border-t border-gray-200 p-3 bg-gray-50/50">
            <ComponentProperties
              component={selectedComponent}
              selectedVariant={selectedComponent.variants[0]?.id || ''}
              onVariantChange={() => {}}
              onPropertyChange={() => {}}
              overrides={{}}
            />
          </div>
        )}

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*,audio/*,.pdf,.svg,.eps,.ai,.psd"
          onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
          className="hidden"
        />

        {/* Asset Preview Modal */}
        {previewAsset && (
          <AssetPreview
            asset={previewAsset}
            onClose={() => setPreviewAsset(null)}
            onUse={handleAssetUse}
            onDownload={() => {}}
            onAddToCollection={() => {}}
          />
        )}
      </div>
    </TooltipProvider>
  );
};

// Component List Item
const ComponentListItem: React.FC<ComponentListItemProps> = ({
  component,
  isSelected,
  onSelect,
  onInstantiate,
  onEdit,
  onDelete,
  onDuplicate,
  viewMode
}) => {
  const [isHovered, setIsHovered] = useState(false);

  if (viewMode === 'grid') {
    return (
      <Card 
        className={`cursor-pointer transition-all duration-200 ${
          isSelected ? 'ring-2 ring-blue-500' : 'hover:ring-1 hover:ring-gray-300'
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => onSelect(component)}
      >
        <div className="p-3">
          {/* Thumbnail */}
          <div className="aspect-square bg-gray-100 rounded-lg mb-2 flex items-center justify-center overflow-hidden">
            {component.thumbnail ? (
              <img 
                src={component.thumbnail} 
                alt={component.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Package className="w-8 h-8 text-gray-400" />
            )}
          </div>

          {/* Info */}
          <div className="space-y-1">
            <h3 className="font-medium text-sm text-gray-900 truncate">
              {component.name}
            </h3>
            <p className="text-xs text-gray-500 truncate">
              {component.description || 'No description'}
            </p>
            <div className="flex items-center justify-between">
              <Badge variant="outline" className="text-xs">
                {component.category}
              </Badge>
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-400">
                  {component.usage.totalInstances}
                </span>
                <Users className="w-3 h-3 text-gray-400" />
              </div>
            </div>
          </div>

          {/* Actions */}
          {isHovered && (
            <div className="absolute top-2 right-2 flex gap-1">
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  onInstantiate(component.id);
                }}
                size="sm"
                variant="secondary"
                className="h-6 w-6 p-0"
              >
                <Plus className="w-3 h-3" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    onClick={(e) => e.stopPropagation()}
                    size="sm"
                    variant="secondary"
                    className="h-6 w-6 p-0"
                  >
                    <MoreHorizontal className="w-3 h-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => onEdit(component)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onDuplicate(component)}>
                    <Copy className="w-4 h-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onDelete(component.id)}>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </Card>
    );
  }

  return (
    <div 
      className={`flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 ${
        isSelected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
      }`}
      onClick={() => onSelect(component)}
    >
      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
        {component.thumbnail ? (
          <img 
            src={component.thumbnail} 
            alt={component.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <Package className="w-5 h-5 text-gray-400" />
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-sm text-gray-900 truncate">
          {component.name}
        </h3>
        <p className="text-xs text-gray-500 truncate">
          {component.description || 'No description'}
        </p>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-xs">
            {component.category}
          </Badge>
          <span className="text-xs text-gray-400">
            {component.usage.totalInstances} uses
          </span>
        </div>
      </div>

      <Button
        onClick={(e) => {
          e.stopPropagation();
          onInstantiate(component.id);
        }}
        size="sm"
        variant="ghost"
        className="h-8 w-8 p-0 flex-shrink-0"
      >
        <Plus className="w-4 h-4" />
      </Button>
    </div>
  );
};

// Asset List Item
const AssetListItem: React.FC<AssetListItemProps> = ({
  asset,
  isSelected,
  onSelect,
  onUse,
  onEdit,
  onDelete,
  viewMode
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getAssetTypeIcon = (type: AssetType) => {
    switch (type) {
      case AssetType.IMAGE:
        return <Image className="w-4 h-4" />;
      case AssetType.VECTOR:
        return <Zap className="w-4 h-4" />;
      case AssetType.ICON:
        return <Sparkles className="w-4 h-4" />;
      case AssetType.FONT:
        return <Type className="w-4 h-4" />;
      case AssetType.COLOR_PALETTE:
        return <Palette className="w-4 h-4" />;
      case AssetType.VIDEO:
        return <Video className="w-4 h-4" />;
      case AssetType.AUDIO:
        return <Music className="w-4 h-4" />;
      case AssetType.DOCUMENT:
        return <FileText className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  if (viewMode === 'grid') {
    return (
      <Card 
        className={`cursor-pointer transition-all duration-200 ${
          isSelected ? 'ring-2 ring-blue-500' : 'hover:ring-1 hover:ring-gray-300'
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => onSelect(asset)}
      >
        <div className="p-3">
          {/* Thumbnail */}
          <div className="aspect-square bg-gray-100 rounded-lg mb-2 flex items-center justify-center overflow-hidden">
            {asset.thumbnailUrl ? (
              <img 
                src={asset.thumbnailUrl} 
                alt={asset.name}
                className="w-full h-full object-cover"
              />
            ) : (
              getAssetTypeIcon(asset.type)
            )}
          </div>

          {/* Info */}
          <div className="space-y-1">
            <h3 className="font-medium text-sm text-gray-900 truncate">
              {asset.name}
            </h3>
            <p className="text-xs text-gray-500 truncate">
              {formatFileSize(asset.size)}
            </p>
            <div className="flex items-center justify-between">
              <Badge variant="outline" className="text-xs">
                {asset.type.replace('_', ' ')}
              </Badge>
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-400">
                  {asset.usage.downloads}
                </span>
                <Download className="w-3 h-3 text-gray-400" />
              </div>
            </div>
          </div>

          {/* Actions */}
          {isHovered && (
            <div className="absolute top-2 right-2 flex gap-1">
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  onUse(asset.id);
                }}
                size="sm"
                variant="secondary"
                className="h-6 w-6 p-0"
              >
                <Plus className="w-3 h-3" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    onClick={(e) => e.stopPropagation()}
                    size="sm"
                    variant="secondary"
                    className="h-6 w-6 p-0"
                  >
                    <MoreHorizontal className="w-3 h-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => onEdit(asset)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onDelete(asset.id)}>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </Card>
    );
  }

  return (
    <div 
      className={`flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 ${
        isSelected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
      }`}
      onClick={() => onSelect(asset)}
    >
      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
        {asset.thumbnailUrl ? (
          <img 
            src={asset.thumbnailUrl} 
            alt={asset.name}
            className="w-full h-full object-cover"
          />
        ) : (
          getAssetTypeIcon(asset.type)
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-sm text-gray-900 truncate">
          {asset.name}
        </h3>
        <p className="text-xs text-gray-500 truncate">
          {formatFileSize(asset.size)} • {asset.type.replace('_', ' ')}
        </p>
        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs text-gray-400">
            {asset.usage.downloads} downloads
          </span>
        </div>
      </div>

      <Button
        onClick={(e) => {
          e.stopPropagation();
          onUse(asset.id);
        }}
        size="sm"
        variant="ghost"
        className="h-8 w-8 p-0 flex-shrink-0"
      >
        <Plus className="w-4 h-4" />
      </Button>
    </div>
  );
};

// Component Properties Panel
const ComponentProperties: React.FC<ComponentPropertiesProps> = ({
  component,
  selectedVariant,
  onVariantChange,
  onPropertyChange,
  overrides
}) => {
  const variant = component.variants.find(v => v.id === selectedVariant) || component.variants[0];

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-sm">{component.name}</h3>
        <Badge variant="outline" className="text-xs">
          v{component.version}
        </Badge>
      </div>

      {component.description && (
        <p className="text-xs text-gray-500">{component.description}</p>
      )}

      {/* Variants */}
      {component.variants.length > 1 && (
        <div>
          <Label className="text-xs font-medium">Variant</Label>
          <Select value={selectedVariant} onValueChange={onVariantChange}>
            <SelectTrigger className="h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {component.variants.map(variant => (
                <SelectItem key={variant.id} value={variant.id}>
                  {variant.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Properties */}
      {component.properties.length > 0 && (
        <div className="space-y-2">
          <Label className="text-xs font-medium">Properties</Label>
          {component.properties.map(property => (
            <div key={property.id} className="space-y-1">
              <Label className="text-xs">{property.name}</Label>
              {property.type === PropertyType.TEXT && (
                <Input
                  value={overrides[property.id] || property.default}
                  onChange={(e) => onPropertyChange(property.id, e.target.value)}
                  className="h-8 text-xs"
                />
              )}
              {property.type === PropertyType.NUMBER && (
                <Input
                  type="number"
                  value={overrides[property.id] || property.default}
                  onChange={(e) => onPropertyChange(property.id, Number(e.target.value))}
                  min={property.constraints?.min}
                  max={property.constraints?.max}
                  step={property.constraints?.step}
                  className="h-8 text-xs"
                />
              )}
              {property.type === PropertyType.COLOR && (
                <Input
                  type="color"
                  value={overrides[property.id] || property.default}
                  onChange={(e) => onPropertyChange(property.id, e.target.value)}
                  className="h-8 w-full"
                />
              )}
              {property.type === PropertyType.BOOLEAN && (
                <Switch
                  checked={overrides[property.id] || property.default}
                  onCheckedChange={(checked) => onPropertyChange(property.id, checked)}
                />
              )}
              {property.type === PropertyType.SELECT && (
                <Select
                  value={overrides[property.id] || property.default}
                  onValueChange={(value) => onPropertyChange(property.id, value)}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {property.options?.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Tags */}
      {component.tags.length > 0 && (
        <div>
          <Label className="text-xs font-medium">Tags</Label>
          <div className="flex flex-wrap gap-1 mt-1">
            {component.tags.map(tag => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Usage Stats */}
      <div className="pt-2 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Usage: {component.usage.totalInstances}</span>
          <span>Updated: {component.updated.toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  );
};

// Asset Preview Modal
const AssetPreview: React.FC<AssetPreviewProps> = ({
  asset,
  onClose,
  onUse,
  onDownload,
  onAddToCollection
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {asset.name}
            <Badge variant="outline">{asset.type.replace('_', ' ')}</Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Preview */}
          <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
            {asset.thumbnailUrl ? (
              <img 
                src={asset.thumbnailUrl} 
                alt={asset.name}
                className="max-w-full max-h-full object-contain"
              />
            ) : (
              <Package className="w-16 h-16 text-gray-400" />
            )}
          </div>

          {/* Details */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Label className="font-medium">Size</Label>
              <p className="text-gray-600">{formatFileSize(asset.size)}</p>
            </div>
            {asset.dimensions && (
              <div>
                <Label className="font-medium">Dimensions</Label>
                <p className="text-gray-600">
                  {asset.dimensions.width} × {asset.dimensions.height}
                </p>
              </div>
            )}
            <div>
              <Label className="font-medium">Format</Label>
              <p className="text-gray-600">{asset.mimeType}</p>
            </div>
            <div>
              <Label className="font-medium">Created</Label>
              <p className="text-gray-600">{asset.created.toLocaleDateString()}</p>
            </div>
          </div>

          {/* Description */}
          {asset.description && (
            <div>
              <Label className="font-medium">Description</Label>
              <p className="text-gray-600 text-sm">{asset.description}</p>
            </div>
          )}

          {/* Tags */}
          {asset.tags.length > 0 && (
            <div>
              <Label className="font-medium">Tags</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {asset.tags.map(tag => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={() => onUse(asset.id)} className="flex-1">
              <MousePointer className="w-4 h-4 mr-2" />
              Use Asset
            </Button>
            <Button onClick={() => onDownload(asset.id)} variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button onClick={() => onAddToCollection(asset.id)} variant="outline">
              <Heart className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ComponentLibraryPanel;