'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useHybridCanvas } from '../../hooks/use-hybrid-canvas';
import { fabric } from 'fabric';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Progress } from '../ui/progress';

export interface HybridCanvasEditorProps {
  width?: number;
  height?: number;
  className?: string;
  enableWebGL?: boolean;
}

export function HybridCanvasEditor({ 
  width = 800, 
  height = 600, 
  className = '',
  enableWebGL = true
}: HybridCanvasEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const {
    canvasManager,
    canvas,
    isInitialized,
    isWebGLEnabled,
    performanceMetrics,
    addObject,
    removeObject,
    clearCanvas,
    undo,
    redo,
    canUndo,
    canRedo,
    enableHybridRendering,
    updateRenderingStrategy
  } = useHybridCanvas(canvasRef, {
    width,
    height,
    backgroundColor: '#ffffff',
    enableWebGL
  });

  const [selectedObjects, setSelectedObjects] = useState<fabric.Object[]>([]);
  const [hybridRenderingEnabled, setHybridRenderingEnabled] = useState(false);
  const [renderingSettings, setRenderingSettings] = useState({
    batchRendering: true,
    culling: true,
    textureOptimization: true
  });

  useEffect(() => {
    if (!canvas) return;

    const handleSelection = (e: any) => {
      setSelectedObjects(e.selected || []);
    };

    canvas.on('selection:created', handleSelection);
    canvas.on('selection:updated', handleSelection);
    canvas.on('selection:cleared', () => setSelectedObjects([]));

    return () => {
      canvas.off('selection:created', handleSelection);
      canvas.off('selection:updated', handleSelection);
      canvas.off('selection:cleared');
    };
  }, [canvas]);

  const addRectangle = () => {
    const rect = new fabric.Rect({
      left: Math.random() * 200 + 100,
      top: Math.random() * 200 + 100,
      width: 100,
      height: 100,
      fill: `hsl(${Math.random() * 360}, 70%, 50%)`,
      stroke: '#000000',
      strokeWidth: 2
    });
    addObject(rect);
  };

  const addCircle = () => {
    const circle = new fabric.Circle({
      left: Math.random() * 200 + 200,
      top: Math.random() * 200 + 200,
      radius: 50,
      fill: `hsl(${Math.random() * 360}, 70%, 50%)`,
      stroke: '#000000',
      strokeWidth: 2
    });
    addObject(circle);
  };

  const addLargeImage = () => {
    // Create a large colored rectangle to simulate a large image
    const largeRect = new fabric.Rect({
      left: Math.random() * 100 + 50,
      top: Math.random() * 100 + 50,
      width: 400,
      height: 300,
      fill: `linear-gradient(45deg, hsl(${Math.random() * 360}, 70%, 50%), hsl(${Math.random() * 360}, 70%, 30%))`,
      stroke: '#000000',
      strokeWidth: 1
    });
    addObject(largeRect);
  };

  const addComplexShape = () => {
    const group = new fabric.Group([
      new fabric.Rect({ width: 80, height: 80, fill: '#ff0000' }),
      new fabric.Circle({ radius: 40, left: 40, top: 40, fill: '#00ff00' }),
      new fabric.Text('Complex', { left: 10, top: 90, fontSize: 12 })
    ], {
      left: Math.random() * 200 + 150,
      top: Math.random() * 200 + 150
    });
    addObject(group);
  };

  const deleteSelected = () => {
    if (canvas && selectedObjects.length > 0) {
      selectedObjects.forEach(obj => {
        canvas.remove(obj);
      });
      canvas.renderAll();
    }
  };

  const toggleHybridRendering = () => {
    if (!hybridRenderingEnabled) {
      enableHybridRendering();
      setHybridRenderingEnabled(true);
    }
  };

  const updateRenderingOptions = (key: string, value: boolean) => {
    const newSettings = { ...renderingSettings, [key]: value };
    setRenderingSettings(newSettings);
    updateRenderingStrategy({
      useWebGL: isWebGLEnabled,
      ...newSettings
    });
  };

  const getPerformanceColor = (fps: number) => {
    if (fps >= 55) return 'text-green-600';
    if (fps >= 30) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-lg">Initializing hybrid canvas...</div>
      </div>
    );
  }

  return (
    <div className={`hybrid-canvas-editor ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Hybrid Canvas Editor</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant={isWebGLEnabled ? "default" : "secondary"}>
                {isWebGLEnabled ? "WebGL" : "Canvas 2D"}
              </Badge>
              <Badge variant="outline" className={getPerformanceColor(performanceMetrics.fps)}>
                {performanceMetrics.fps} FPS
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Performance Metrics */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <Label className="text-xs text-gray-600">Frame Rate</Label>
                <div className={`font-mono ${getPerformanceColor(performanceMetrics.fps)}`}>
                  {performanceMetrics.fps} FPS
                </div>
              </div>
              <div>
                <Label className="text-xs text-gray-600">Frame Time</Label>
                <div className="font-mono">{performanceMetrics.frameTime}ms</div>
              </div>
              <div>
                <Label className="text-xs text-gray-600">Memory</Label>
                <div className="font-mono">{performanceMetrics.memoryUsage}MB</div>
              </div>
              <div>
                <Label className="text-xs text-gray-600">Objects</Label>
                <div className="font-mono">{performanceMetrics.objectCount}</div>
              </div>
            </div>
            <div className="mt-2">
              <Label className="text-xs text-gray-600">Performance</Label>
              <Progress 
                value={Math.min(performanceMetrics.fps / 60 * 100, 100)} 
                className="h-2"
              />
            </div>
          </div>

          {/* Rendering Controls */}
          {isWebGLEnabled && (
            <div className="mb-4 p-3 border rounded-lg">
              <Label className="text-sm font-medium mb-2 block">Rendering Options</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="hybrid-rendering"
                    checked={hybridRenderingEnabled}
                    onCheckedChange={toggleHybridRendering}
                  />
                  <Label htmlFor="hybrid-rendering" className="text-sm">
                    Hybrid Rendering
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="batch-rendering"
                    checked={renderingSettings.batchRendering}
                    onCheckedChange={(checked) => updateRenderingOptions('batchRendering', checked)}
                  />
                  <Label htmlFor="batch-rendering" className="text-sm">
                    Batch Rendering
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="culling"
                    checked={renderingSettings.culling}
                    onCheckedChange={(checked) => updateRenderingOptions('culling', checked)}
                  />
                  <Label htmlFor="culling" className="text-sm">
                    Frustum Culling
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="texture-optimization"
                    checked={renderingSettings.textureOptimization}
                    onCheckedChange={(checked) => updateRenderingOptions('textureOptimization', checked)}
                  />
                  <Label htmlFor="texture-optimization" className="text-sm">
                    Texture Optimization
                  </Label>
                </div>
              </div>
            </div>
          )}

          {/* Toolbar */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Button onClick={addRectangle} variant="outline" size="sm">
              Add Rectangle
            </Button>
            <Button onClick={addCircle} variant="outline" size="sm">
              Add Circle
            </Button>
            <Button onClick={addLargeImage} variant="outline" size="sm">
              Add Large Image
            </Button>
            <Button onClick={addComplexShape} variant="outline" size="sm">
              Add Complex Shape
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button 
              onClick={undo} 
              disabled={!canUndo}
              variant="outline" 
              size="sm"
            >
              Undo
            </Button>
            <Button 
              onClick={redo} 
              disabled={!canRedo}
              variant="outline" 
              size="sm"
            >
              Redo
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button 
              onClick={deleteSelected} 
              disabled={selectedObjects.length === 0}
              variant="destructive" 
              size="sm"
            >
              Delete Selected
            </Button>
            <Button onClick={clearCanvas} variant="outline" size="sm">
              Clear Canvas
            </Button>
          </div>

          {/* Canvas Container */}
          <div className="relative border border-gray-300 rounded-lg overflow-hidden">
            <canvas
              ref={canvasRef}
              width={width}
              height={height}
              className="block"
            />
          </div>

          {/* Status */}
          <div className="mt-4 text-sm text-gray-600">
            Selected objects: {selectedObjects.length}
            {selectedObjects.length > 0 && (
              <span className="ml-2">
                ({selectedObjects.map(obj => obj.type).join(', ')})
              </span>
            )}
            {isWebGLEnabled && hybridRenderingEnabled && (
              <Badge variant="outline" className="ml-2">
                Hybrid Rendering Active
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
