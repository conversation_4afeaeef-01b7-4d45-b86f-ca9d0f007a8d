'use client';

import React, { useState, useCallback } from 'react';
import { useEditorStore } from '@/store/editor-store';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { X, FileText, Circle, MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface FileTabsProps {
  className?: string;
}

interface FileTabProps {
  fileId: string;
  isActive: boolean;
  onClick: () => void;
  onClose: () => void;
  isDirty: boolean;
  name: string;
}

const FileTab: React.FC<FileTabProps> = ({
  fileId,
  isActive,
  onClick,
  onClose,
  isDirty,
  name,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClose = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onClose();
  }, [onClose]);

  return (
    <div
      className={cn(
        'group flex items-center gap-2 px-3 py-2 border-r border-border bg-background hover:bg-accent cursor-pointer transition-colors min-w-0 max-w-[200px]',
        isActive && 'bg-accent text-accent-foreground border-b-2 border-b-primary'
      )}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={name}
    >
      <FileText className="h-3 w-3 flex-shrink-0" />
      <span className="text-sm truncate flex-1">{name}</span>
      
      {/* Dirty indicator or close button */}
      <div className="flex-shrink-0 flex items-center">
        {isDirty && !isHovered ? (
          <Circle className="h-2 w-2 fill-current" />
        ) : (
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              'h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground rounded-sm',
              !isHovered && 'opacity-0 group-hover:opacity-100'
            )}
            onClick={handleClose}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
};

export function FileTabs({ className = '' }: FileTabsProps) {
  const {
    openFiles,
    activeFile,
    setActiveFile,
    closeFile,
    saveFile,
    saveAllFiles,
    getDirtyFiles,
  } = useEditorStore();

  const handleTabClick = useCallback((fileId: string) => {
    setActiveFile(fileId);
  }, [setActiveFile]);

  const handleTabClose = useCallback((fileId: string) => {
    closeFile(fileId);
  }, [closeFile]);

  const handleCloseAll = useCallback(() => {
    openFiles.forEach(file => {
      closeFile(file.id);
    });
  }, [openFiles, closeFile]);

  const handleCloseOthers = useCallback(() => {
    if (activeFile) {
      openFiles.forEach(file => {
        if (file.id !== activeFile) {
          closeFile(file.id);
        }
      });
    }
  }, [openFiles, activeFile, closeFile]);

  const handleCloseSaved = useCallback(() => {
    openFiles.forEach(file => {
      if (!file.isDirty) {
        closeFile(file.id);
      }
    });
  }, [openFiles, closeFile]);

  const handleSaveAll = useCallback(() => {
    saveAllFiles();
  }, [saveAllFiles]);

  const dirtyFiles = getDirtyFiles();

  if (openFiles.length === 0) {
    return null;
  }

  return (
    <div className={cn('border-b bg-muted/30', className)}>
      <div className="flex items-center min-h-[40px]">
        <ScrollArea className="flex-1">
          <div className="flex">
            {openFiles.map((file) => (
              <FileTab
                key={file.id}
                fileId={file.id}
                isActive={file.id === activeFile}
                onClick={() => handleTabClick(file.id)}
                onClose={() => handleTabClose(file.id)}
                isDirty={file.isDirty}
                name={file.name}
              />
            ))}
          </div>
        </ScrollArea>

        {/* Tab actions */}
        <div className="flex-shrink-0 px-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-accent"
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={handleSaveAll} disabled={dirtyFiles.length === 0}>
                Save All
                {dirtyFiles.length > 0 && (
                  <span className="ml-auto text-xs text-muted-foreground">
                    ({dirtyFiles.length})
                  </span>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleCloseSaved}>
                Close Saved
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCloseOthers} disabled={!activeFile}>
                Close Others
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCloseAll}>
                Close All
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}