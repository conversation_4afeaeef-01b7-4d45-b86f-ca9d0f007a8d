'use client';

import React, { useState, useMemo } from 'react';
import { TerminalSession } from '@/lib/terminal/terminal-service';
import { SessionGroup, SessionSnapshot, CollaborativeSession } from '@/lib/terminal/advanced-session-manager';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Plus, 
  Search, 
  Filter, 
  Grid, 
  List, 
  TreePine, 
  MoreVertical,
  FolderIcon,
  Terminal,
  Play,
  Pause,
  Share,
  Download,
  Upload,
  Copy,
  Trash2,
  Clock,
  Users,
  Activity,
  Workflow,
  Camera,
  PlayCircle,
  StopCircle
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface SessionManagerProps {
  sessions: TerminalSession[];
  groups: SessionGroup[];
  snapshots: SessionSnapshot[];
  collaborativeSessions: CollaborativeSession[];
  activeSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onSessionCreate: (config: any) => Promise<void>;
  onSessionDelete: (sessionId: string) => Promise<void>;
  onSessionShare: (sessionId: string, permissions: any) => Promise<void>;
  onSnapshotCreate: (sessionId: string, name?: string) => Promise<void>;
  onSnapshotRestore: (snapshotId: string) => Promise<void>;
  onGroupCreate: (name: string, sessionIds: string[]) => Promise<void>;
}

export const SessionManager: React.FC<SessionManagerProps> = ({
  sessions,
  groups,
  snapshots,
  collaborativeSessions,
  activeSessionId,
  onSessionSelect,
  onSessionCreate,
  onSessionDelete,
  onSessionShare,
  onSnapshotCreate,
  onSnapshotRestore,
  onGroupCreate
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'tree'>('tree');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedTab, setSelectedTab] = useState('sessions');

  const filteredSessions = useMemo(() => {
    let filtered = sessions;

    if (searchQuery) {
      filtered = filtered.filter(session =>
        session.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        session.cwd.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedGroup) {
      const group = groups.find(g => g.id === selectedGroup);
      if (group) {
        filtered = filtered.filter(session => group.sessionIds.includes(session.id));
      }
    }

    return filtered;
  }, [sessions, searchQuery, selectedGroup, groups]);

  const handleCreateSession = async () => {
    try {
      await onSessionCreate({
        name: `Terminal ${sessions.length + 1}`,
        shell: 'bash'
      });
      setShowCreateDialog(false);
    } catch (error) {
      console.error('Failed to create session:', error);
    }
  };

  const handleSessionAction = async (sessionId: string, action: string) => {
    try {
      switch (action) {
        case 'duplicate':
          await onSessionCreate({ name: `Copy of ${sessions.find(s => s.id === sessionId)?.name}` });
          break;
        case 'snapshot':
          await onSnapshotCreate(sessionId);
          break;
        case 'share':
          await onSessionShare(sessionId, { canExecute: true, canView: true });
          break;
        case 'delete':
          await onSessionDelete(sessionId);
          break;
        default:
          console.warn(`Unknown action: ${action}`);
      }
    } catch (error) {
      console.error(`Failed to ${action} session:`, error);
    }
  };

  const handleGroupAction = async (groupId: string, action: string) => {
    // Implementation for group actions
    console.log(`Group action: ${action} on ${groupId}`);
  };

  return (
    <div className="session-manager h-full flex flex-col">
      <SessionManagerHeader
        onCreateSession={() => setShowCreateDialog(true)}
        onImportSessions={() => {}}
        onCreateGroup={() => {}}
      />

      <SessionFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        selectedGroup={selectedGroup}
        onGroupChange={setSelectedGroup}
        groups={groups}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
      />

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="groups">Groups</TabsTrigger>
          <TabsTrigger value="snapshots">Snapshots</TabsTrigger>
          <TabsTrigger value="collaborative">Shared</TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="flex-1">
          <SessionList
            sessions={filteredSessions}
            groups={groups}
            activeSessionId={activeSessionId}
            viewMode={viewMode}
            onSessionSelect={onSessionSelect}
            onSessionAction={handleSessionAction}
          />
        </TabsContent>

        <TabsContent value="groups" className="flex-1">
          <GroupList
            groups={groups}
            sessions={sessions}
            onGroupAction={handleGroupAction}
          />
        </TabsContent>

        <TabsContent value="snapshots" className="flex-1">
          <SnapshotList
            snapshots={snapshots}
            sessions={sessions}
            onSnapshotRestore={onSnapshotRestore}
          />
        </TabsContent>

        <TabsContent value="collaborative" className="flex-1">
          <CollaborativeSessionList
            collaborativeSessions={collaborativeSessions}
            sessions={sessions}
          />
        </TabsContent>
      </Tabs>

      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Terminal Session</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Session Name</label>
              <Input placeholder="Terminal Session" />
            </div>
            <div>
              <label className="text-sm font-medium">Shell</label>
              <select className="w-full p-2 border rounded">
                <option value="bash">Bash</option>
                <option value="zsh">Zsh</option>
                <option value="fish">Fish</option>
                <option value="sh">Shell</option>
              </select>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateSession}>
                Create Session
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const SessionManagerHeader: React.FC<{
  onCreateSession: () => void;
  onImportSessions: () => void;
  onCreateGroup: () => void;
}> = ({ onCreateSession, onImportSessions, onCreateGroup }) => {
  return (
    <div className="flex items-center justify-between p-4 border-b">
      <h2 className="text-lg font-semibold">Terminal Sessions</h2>
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" onClick={onCreateGroup}>
          <FolderIcon size={16} className="mr-2" />
          New Group
        </Button>
        <Button variant="outline" size="sm" onClick={onImportSessions}>
          <Upload size={16} className="mr-2" />
          Import
        </Button>
        <Button size="sm" onClick={onCreateSession}>
          <Plus size={16} className="mr-2" />
          New Session
        </Button>
      </div>
    </div>
  );
};

const SessionFilters: React.FC<{
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedGroup: string | null;
  onGroupChange: (groupId: string | null) => void;
  groups: SessionGroup[];
  viewMode: 'list' | 'grid' | 'tree';
  onViewModeChange: (mode: 'list' | 'grid' | 'tree') => void;
}> = ({
  searchQuery,
  onSearchChange,
  selectedGroup,
  onGroupChange,
  groups,
  viewMode,
  onViewModeChange
}) => {
  return (
    <div className="flex items-center space-x-4 p-4 border-b">
      <div className="flex-1">
        <div className="relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search sessions..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
      
      <select
        value={selectedGroup || ''}
        onChange={(e) => onGroupChange(e.target.value || null)}
        className="px-3 py-2 border rounded"
      >
        <option value="">All Groups</option>
        {groups.map(group => (
          <option key={group.id} value={group.id}>
            {group.name}
          </option>
        ))}
      </select>

      <div className="flex items-center space-x-1">
        <Button
          variant={viewMode === 'list' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('list')}
        >
          <List size={16} />
        </Button>
        <Button
          variant={viewMode === 'grid' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('grid')}
        >
          <Grid size={16} />
        </Button>
        <Button
          variant={viewMode === 'tree' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('tree')}
        >
          <TreePine size={16} />
        </Button>
      </div>
    </div>
  );
};

const SessionList: React.FC<{
  sessions: TerminalSession[];
  groups: SessionGroup[];
  activeSessionId?: string;
  viewMode: 'list' | 'grid' | 'tree';
  onSessionSelect: (sessionId: string) => void;
  onSessionAction: (sessionId: string, action: string) => void;
}> = ({ sessions, groups, activeSessionId, viewMode, onSessionSelect, onSessionAction }) => {
  if (viewMode === 'grid') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        {sessions.map(session => (
          <SessionCard
            key={session.id}
            session={session}
            isActive={session.id === activeSessionId}
            onSelect={() => onSessionSelect(session.id)}
            onAction={(action) => onSessionAction(session.id, action)}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-2 p-4">
      {sessions.map(session => (
        <SessionListItem
          key={session.id}
          session={session}
          isActive={session.id === activeSessionId}
          onSelect={() => onSessionSelect(session.id)}
          onAction={(action) => onSessionAction(session.id, action)}
        />
      ))}
    </div>
  );
};

const SessionCard: React.FC<{
  session: TerminalSession;
  isActive: boolean;
  onSelect: () => void;
  onAction: (action: string) => void;
}> = ({ session, isActive, onSelect, onAction }) => {
  const getStatusColor = () => {
    if (!session.state.isActive) return 'text-gray-500';
    return 'text-green-500';
  };

  const getStatusIcon = () => {
    if (!session.state.isActive) return <StopCircle size={16} />;
    return <PlayCircle size={16} />;
  };

  return (
    <Card className={`cursor-pointer transition-colors ${isActive ? 'ring-2 ring-blue-500' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <div className={`${getStatusColor()}`}>
              {getStatusIcon()}
            </div>
            <h4 className="font-medium" onClick={onSelect}>
              {session.name}
            </h4>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onAction('duplicate')}>
                <Copy size={16} className="mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction('snapshot')}>
                <Camera size={16} className="mr-2" />
                Snapshot
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction('share')}>
                <Share size={16} className="mr-2" />
                Share
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onAction('delete')} className="text-red-600">
                <Trash2 size={16} className="mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center text-sm text-gray-600">
            <FolderIcon size={14} className="mr-2" />
            <span className="truncate">{session.cwd}</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <Badge variant="outline">{session.shell}</Badge>
            <span className="text-gray-500">
              {formatDistanceToNow(session.lastActivity)} ago
            </span>
          </div>
          
          <div className="text-sm text-gray-500">
            {session.history.length} commands
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const SessionListItem: React.FC<{
  session: TerminalSession;
  isActive: boolean;
  onSelect: () => void;
  onAction: (action: string) => void;
}> = ({ session, isActive, onSelect, onAction }) => {
  return (
    <div
      className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${
        isActive ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
      }`}
      onClick={onSelect}
    >
      <div className="flex-1">
        <div className="flex items-center space-x-3">
          <Terminal size={16} />
          <div>
            <h4 className="font-medium">{session.name}</h4>
            <p className="text-sm text-gray-600">{session.cwd}</p>
          </div>
        </div>
      </div>
      
      <div className="flex items-center space-x-3">
        <Badge variant="outline">{session.shell}</Badge>
        <span className="text-sm text-gray-500">
          {formatDistanceToNow(session.lastActivity)} ago
        </span>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreVertical size={16} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => onAction('duplicate')}>
              Duplicate
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onAction('snapshot')}>
              Snapshot
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onAction('share')}>
              Share
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onAction('delete')} className="text-red-600">
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

const GroupList: React.FC<{
  groups: SessionGroup[];
  sessions: TerminalSession[];
  onGroupAction: (groupId: string, action: string) => void;
}> = ({ groups, sessions, onGroupAction }) => {
  return (
    <div className="space-y-2 p-4">
      {groups.map(group => (
        <div key={group.id} className="border rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FolderIcon size={16} />
              <h4 className="font-medium">{group.name}</h4>
              <Badge variant="secondary">{group.sessionIds.length} sessions</Badge>
            </div>
            <Button variant="ghost" size="sm">
              <MoreVertical size={16} />
            </Button>
          </div>
          {group.description && (
            <p className="text-sm text-gray-600 mt-1">{group.description}</p>
          )}
        </div>
      ))}
    </div>
  );
};

const SnapshotList: React.FC<{
  snapshots: SessionSnapshot[];
  sessions: TerminalSession[];
  onSnapshotRestore: (snapshotId: string) => void;
}> = ({ snapshots, sessions, onSnapshotRestore }) => {
  return (
    <div className="space-y-2 p-4">
      {snapshots.map(snapshot => (
        <div key={snapshot.id} className="border rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">{snapshot.name}</h4>
              <p className="text-sm text-gray-600">
                {formatDistanceToNow(snapshot.createdAt)} ago
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onSnapshotRestore(snapshot.id)}
              >
                <PlayCircle size={16} className="mr-2" />
                Restore
              </Button>
              <Button variant="ghost" size="sm">
                <MoreVertical size={16} />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

const CollaborativeSessionList: React.FC<{
  collaborativeSessions: CollaborativeSession[];
  sessions: TerminalSession[];
}> = ({ collaborativeSessions, sessions }) => {
  return (
    <div className="space-y-2 p-4">
      {collaborativeSessions.map(session => (
        <div key={session.id} className="border rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Shared Session</h4>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Users size={14} />
                <span>{session.participants.length} participants</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={session.isActive ? 'default' : 'secondary'}>
                {session.isActive ? 'Active' : 'Inactive'}
              </Badge>
              <Button variant="ghost" size="sm">
                <MoreVertical size={16} />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SessionManager;