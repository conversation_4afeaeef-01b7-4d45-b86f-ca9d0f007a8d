'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { SearchAddon } from 'xterm-addon-search';
import { TerminalSession, TerminalConfiguration, TerminalTheme } from '@/lib/terminal/terminal-service';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Square, 
  MoreVertical, 
  Copy, 
  Search, 
  Settings,
  X,
  Maximize2,
  Minimize2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TerminalProps {
  session?: TerminalSession;
  configuration: TerminalConfiguration;
  onCommand?: (command: string) => void;
  onResize?: (cols: number, rows: number) => void;
  onSessionChange?: (session: TerminalSession) => void;
  onClose?: () => void;
  className?: string;
}

export const WebTerminal: React.FC<TerminalProps> = ({
  session,
  configuration,
  onCommand,
  onResize,
  onSessionChange,
  onClose,
  className = ''
}) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<Terminal>();
  const fitAddonRef = useRef<FitAddon>();
  const webLinksAddonRef = useRef<WebLinksAddon>();
  const searchAddonRef = useRef<SearchAddon>();
  const wsRef = useRef<WebSocket>();
  
  const [isConnected, setIsConnected] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [showSearch, setShowSearch] = useState(false);

  useEffect(() => {
    if (!terminalRef.current) return;

    // Initialize XTerm.js terminal
    const terminal = new Terminal({
      theme: convertTheme(configuration.theme),
      fontSize: configuration.fontSize,
      fontFamily: configuration.fontFamily,
      scrollback: configuration.scrollback,
      bellStyle: configuration.bellStyle,
      cursorBlink: configuration.cursorBlink,
      cursorStyle: configuration.cursorStyle,
      allowTransparency: configuration.allowTransparency,
      convertEol: true,
      disableStdin: false,
      cols: 80,
      rows: 24
    });

    // Add addons
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();
    const searchAddon = new SearchAddon();

    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);
    terminal.loadAddon(searchAddon);

    // Open terminal in container
    terminal.open(terminalRef.current);

    // Store references
    xtermRef.current = terminal;
    fitAddonRef.current = fitAddon;
    webLinksAddonRef.current = webLinksAddon;
    searchAddonRef.current = searchAddon;

    // Setup event handlers
    setupTerminalEvents(terminal);

    // Fit terminal to container
    fitAddon.fit();

    // Setup WebSocket connection
    setupWebSocketConnection();

    // Setup resize observer
    const resizeObserver = new ResizeObserver(() => {
      fitAddon.fit();
    });
    resizeObserver.observe(terminalRef.current);

    return () => {
      terminal.dispose();
      resizeObserver.disconnect();
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  useEffect(() => {
    if (xtermRef.current && session) {
      restoreSession(xtermRef.current, session);
    }
  }, [session]);

  const setupTerminalEvents = (terminal: Terminal) => {
    terminal.onData((data) => {
      // Send input to WebSocket
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          type: 'send_input',
          sessionId: session?.id,
          data
        }));
      }
    });

    terminal.onResize(({ cols, rows }) => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          type: 'resize_terminal',
          sessionId: session?.id,
          cols,
          rows
        }));
      }
      onResize?.(cols, rows);
    });

    terminal.onSelectionChange(() => {
      const selection = terminal.getSelection();
      if (selection && session) {
        // Update session selection state
        // This would be implemented with proper session management
      }
    });
  };

  const setupWebSocketConnection = () => {
    // In a real implementation, this would connect to your WebSocket server
    // For now, we'll create a mock connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/api/terminal/ws`;
    
    try {
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        setIsConnected(true);
        // Create a new session if none exists
        if (!session) {
          ws.send(JSON.stringify({
            type: 'create_session',
            config: configuration
          }));
        }
      };

      ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      };

      ws.onclose = () => {
        setIsConnected(false);
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnected(false);
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      // Fallback to mock implementation
      setupMockTerminal();
    }
  };

  const setupMockTerminal = () => {
    // Mock terminal for demonstration
    const terminal = xtermRef.current;
    if (!terminal) return;

    terminal.writeln('Welcome to Web Terminal (Mock Mode)');
    terminal.writeln('Type commands below:');
    terminal.write('$ ');

    let currentCommand = '';
    terminal.onData((data) => {
      if (data === '\r') {
        terminal.writeln('');
        if (currentCommand.trim()) {
          handleMockCommand(currentCommand.trim());
          onCommand?.(currentCommand.trim());
        }
        currentCommand = '';
        terminal.write('$ ');
      } else if (data === '\u007f') { // Backspace
        if (currentCommand.length > 0) {
          currentCommand = currentCommand.slice(0, -1);
          terminal.write('\b \b');
        }
      } else {
        currentCommand += data;
        terminal.write(data);
      }
    });

    setIsConnected(true);
  };

  const handleMockCommand = (command: string) => {
    const terminal = xtermRef.current;
    if (!terminal) return;

    switch (command) {
      case 'help':
        terminal.writeln('Available commands:');
        terminal.writeln('  help    - Show this help message');
        terminal.writeln('  clear   - Clear the terminal');
        terminal.writeln('  date    - Show current date');
        terminal.writeln('  echo    - Echo text back');
        break;
      case 'clear':
        terminal.clear();
        break;
      case 'date':
        terminal.writeln(new Date().toString());
        break;
      default:
        if (command.startsWith('echo ')) {
          terminal.writeln(command.substring(5));
        } else {
          terminal.writeln(`Command not found: ${command}`);
        }
    }
  };

  const handleWebSocketMessage = (message: any) => {
    const terminal = xtermRef.current;
    if (!terminal) return;

    switch (message.type) {
      case 'session_created':
        onSessionChange?.(message.session);
        break;
      case 'output':
        terminal.write(message.data);
        break;
      case 'error':
        terminal.writeln(`\x1b[31mError: ${message.message}\x1b[0m`);
        break;
      case 'session_terminated':
        terminal.writeln('\x1b[33mSession terminated\x1b[0m');
        setIsConnected(false);
        break;
    }
  };

  const restoreSession = (terminal: Terminal, session: TerminalSession) => {
    if (session.state.buffer) {
      terminal.write(session.state.buffer);
    }
    if (session.state.scrollPosition) {
      terminal.scrollToTop();
      // Scroll to position would be implemented here
    }
  };

  const convertTheme = (theme: TerminalTheme) => {
    return {
      background: theme.background,
      foreground: theme.foreground,
      cursor: theme.cursor,
      selection: theme.selection,
      black: theme.black,
      red: theme.red,
      green: theme.green,
      yellow: theme.yellow,
      blue: theme.blue,
      magenta: theme.magenta,
      cyan: theme.cyan,
      white: theme.white,
      brightBlack: theme.brightBlack,
      brightRed: theme.brightRed,
      brightGreen: theme.brightGreen,
      brightYellow: theme.brightYellow,
      brightBlue: theme.brightBlue,
      brightMagenta: theme.brightMagenta,
      brightCyan: theme.brightCyan,
      brightWhite: theme.brightWhite,
    };
  };

  const handleCopyToClipboard = () => {
    const terminal = xtermRef.current;
    if (!terminal) return;

    const selection = terminal.getSelection();
    if (selection) {
      navigator.clipboard.writeText(selection);
    }
  };

  const handleSearch = () => {
    setShowSearch(!showSearch);
    // Search functionality would be implemented here
  };

  const handleClearTerminal = () => {
    const terminal = xtermRef.current;
    if (terminal) {
      terminal.clear();
    }
  };

  const handleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  return (
    <div className={`web-terminal ${className} ${isMaximized ? 'maximized' : ''}`}>
      <div className="terminal-header flex items-center justify-between bg-gray-800 text-white px-4 py-2">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm font-medium">
            {session?.name || 'New Terminal'}
          </span>
          {session && (
            <Badge variant="secondary" className="text-xs">
              {session.shell}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopyToClipboard}
            className="text-white hover:bg-gray-700"
          >
            <Copy size={16} />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSearch}
            className="text-white hover:bg-gray-700"
          >
            <Search size={16} />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleMaximize}
            className="text-white hover:bg-gray-700"
          >
            {isMaximized ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="text-white hover:bg-gray-700">
                <MoreVertical size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleClearTerminal}>
                Clear Terminal
              </DropdownMenuItem>
              <DropdownMenuItem>
                Split Terminal
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-gray-700"
            >
              <X size={16} />
            </Button>
          )}
        </div>
      </div>
      
      <div 
        ref={terminalRef} 
        className="terminal-container flex-1 p-2"
        style={{ 
          backgroundColor: configuration.theme.background,
          height: isMaximized ? '100vh' : '400px'
        }}
      />
      
      {session && (
        <div className="terminal-status-bar bg-gray-700 text-white px-4 py-1 text-xs flex justify-between">
          <span>
            {session.cwd}
          </span>
          <span>
            {session.shell} | {session.history.length} commands
          </span>
        </div>
      )}
    </div>
  );
};

export default WebTerminal;