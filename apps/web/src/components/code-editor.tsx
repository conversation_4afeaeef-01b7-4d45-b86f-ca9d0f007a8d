'use client';

import React, { useEffect, useState } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { useEditorStore } from '@/store/editor-store';
import { FileExplorer } from './file-explorer';
import { FileTabs } from './file-tabs';
import { MonacoEditor } from './monaco-editor';
import { StatusBar } from './status-bar';
import { Button } from './ui/button';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Save, 
  Search, 
  Settings, 
  Terminal,
  Palette,
  Zap,
  FileText,
  FolderOpen,
  GitBranch,
  Bug,
  Lightbulb
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface CodeEditorProps {
  className?: string;
}

export function CodeEditor({ className = '' }: CodeEditorProps) {
  const [isExplorerVisible, setIsExplorerVisible] = useState(true);
  const [isTerminalVisible, setIsTerminalVisible] = useState(false);
  const [leftPanelSize, setLeftPanelSize] = useState(25);
  const [bottomPanelSize, setBottomPanelSize] = useState(30);

  const {
    activeFile,
    getFileById,
    saveFile,
    saveAllFiles,
    loadDirectory,
    isDirty,
    getDirtyFiles,
    openFiles,
  } = useEditorStore();

  const activeFileData = activeFile ? getFileById(activeFile) : null;
  const dirtyFiles = getDirtyFiles();

  useEffect(() => {
    // Load sample project on mount
    loadDirectory('/sample-project');
  }, [loadDirectory]);

  const handleSave = () => {
    if (activeFile) {
      saveFile(activeFile);
    }
  };

  const handleSaveAll = () => {
    saveAllFiles();
  };

  const handleRun = () => {
    // Mock run functionality
    console.log('Running file:', activeFileData?.path);
  };

  const handleToggleExplorer = () => {
    setIsExplorerVisible(!isExplorerVisible);
  };

  const handleToggleTerminal = () => {
    setIsTerminalVisible(!isTerminalVisible);
  };

  const getWelcomeScreen = () => (
    <div className="h-full flex flex-col items-center justify-center bg-background text-center">
      <div className="max-w-md space-y-6">
        <div className="space-y-2">
          <FileText className="h-16 w-16 mx-auto text-muted-foreground" />
          <h1 className="text-2xl font-bold">Welcome to Code Editor</h1>
          <p className="text-muted-foreground">
            Open a file from the explorer or create a new one to get started
          </p>
        </div>
        
        <div className="space-y-3">
          <Button
            onClick={() => loadDirectory('/sample-project')}
            className="w-full"
            variant="outline"
          >
            <FolderOpen className="h-4 w-4 mr-2" />
            Open Sample Project
          </Button>
          
          <div className="text-sm text-muted-foreground space-y-1">
            <p>Keyboard shortcuts:</p>
            <ul className="text-xs space-y-1">
              <li><kbd className="px-1 py-0.5 bg-muted rounded">Ctrl+S</kbd> - Save file</li>
              <li><kbd className="px-1 py-0.5 bg-muted rounded">Ctrl+Shift+S</kbd> - Save all</li>
              <li><kbd className="px-1 py-0.5 bg-muted rounded">Ctrl+P</kbd> - Quick open</li>
              <li><kbd className="px-1 py-0.5 bg-muted rounded">Ctrl+`</kbd> - Toggle terminal</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const getTerminalPanel = () => (
    <div className="h-full bg-black text-green-400 font-mono text-sm p-4">
      <div className="mb-2">
        <span className="text-blue-400">$</span> npm run dev
      </div>
      <div className="text-gray-300">
        <div>Local: http://localhost:3000</div>
        <div>✓ Ready in 2.3s</div>
      </div>
      <div className="mt-2">
        <span className="text-blue-400">$</span> <span className="animate-pulse">_</span>
      </div>
    </div>
  );

  return (
    <TooltipProvider>
      <div className={cn('h-screen w-full flex flex-col bg-background', className)}>
        {/* Top toolbar */}
        <div className="h-12 border-b bg-card flex items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleExplorer}
              className={cn('h-8 w-8 p-0', isExplorerVisible && 'bg-accent')}
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <FolderOpen className="h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Toggle Explorer</p>
                </TooltipContent>
              </Tooltip>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {}}
              className="h-8 w-8 p-0"
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <Search className="h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Search</p>
                </TooltipContent>
              </Tooltip>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {}}
              className="h-8 w-8 p-0"
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <GitBranch className="h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Source Control</p>
                </TooltipContent>
              </Tooltip>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {}}
              className="h-8 w-8 p-0"
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <Bug className="h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Debug</p>
                </TooltipContent>
              </Tooltip>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {}}
              className="h-8 w-8 p-0"
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <Lightbulb className="h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Extensions</p>
                </TooltipContent>
              </Tooltip>
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSave}
              disabled={!activeFile || !activeFileData?.isDirty}
              className="h-8 px-3"
            >
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleSaveAll}
              disabled={dirtyFiles.length === 0}
              className="h-8 px-3"
            >
              <Save className="h-4 w-4 mr-2" />
              Save All
              {dirtyFiles.length > 0 && (
                <span className="ml-1 text-xs bg-accent px-1 rounded">
                  {dirtyFiles.length}
                </span>
              )}
            </Button>

            <div className="h-4 w-px bg-border mx-2" />

            <Button
              variant="ghost"
              size="sm"
              onClick={handleRun}
              disabled={!activeFile}
              className="h-8 px-3"
            >
              <Play className="h-4 w-4 mr-2" />
              Run
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleTerminal}
              className={cn('h-8 w-8 p-0', isTerminalVisible && 'bg-accent')}
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <Terminal className="h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Toggle Terminal</p>
                </TooltipContent>
              </Tooltip>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {}}
              className="h-8 w-8 p-0"
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <Settings className="h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Settings</p>
                </TooltipContent>
              </Tooltip>
            </Button>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 overflow-hidden">
          <PanelGroup direction="horizontal">
            {/* Left panel - File explorer */}
            {isExplorerVisible && (
              <>
                <Panel
                  defaultSize={leftPanelSize}
                  minSize={15}
                  maxSize={50}
                  onResize={(size) => setLeftPanelSize(size)}
                >
                  <FileExplorer />
                </Panel>
                <PanelResizeHandle className="w-1 bg-border hover:bg-accent transition-colors" />
              </>
            )}

            {/* Main editor panel */}
            <Panel minSize={30}>
              <PanelGroup direction="vertical">
                {/* Editor area */}
                <Panel minSize={40}>
                  <div className="h-full flex flex-col">
                    {/* File tabs */}
                    <FileTabs />

                    {/* Editor content */}
                    <div className="flex-1 min-h-0">
                      {activeFile ? (
                        <MonacoEditor
                          fileId={activeFile}
                          className="h-full"
                        />
                      ) : (
                        getWelcomeScreen()
                      )}
                    </div>
                  </div>
                </Panel>

                {/* Terminal panel */}
                {isTerminalVisible && (
                  <>
                    <PanelResizeHandle className="h-1 bg-border hover:bg-accent transition-colors" />
                    <Panel
                      defaultSize={bottomPanelSize}
                      minSize={20}
                      maxSize={60}
                      onResize={(size) => setBottomPanelSize(size)}
                    >
                      <div className="h-full border-t">
                        <div className="h-8 border-b bg-card flex items-center px-3">
                          <span className="text-sm font-medium">Terminal</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleToggleTerminal}
                            className="ml-auto h-6 w-6 p-0"
                          >
                            <Square className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="h-[calc(100%-2rem)]">
                          {getTerminalPanel()}
                        </div>
                      </div>
                    </Panel>
                  </>
                )}
              </PanelGroup>
            </Panel>
          </PanelGroup>
        </div>

        {/* Status bar */}
        <StatusBar />
      </div>
    </TooltipProvider>
  );
}