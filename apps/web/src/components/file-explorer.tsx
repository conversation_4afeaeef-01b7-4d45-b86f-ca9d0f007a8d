'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { useEditorStore } from '@/store/editor-store';
import { FileNode } from '@/types/editor';
import { ScrollArea } from '@/components/ui/scroll-area';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Search, 
  FolderPlus, 
  FileText, 
  Folder, 
  FolderOpen, 
  ChevronRight, 
  ChevronDown,
  MoreHorizontal,
  Trash2,
  Edit2,
  Copy,
  Download,
  Upload,
  RefreshCw,
  Filter,
  Settings,
  Eye,
  FileType,
  Clock,
  HardDrive,
  Star,
  AlertCircle,
  CheckCircle,
  X,
  Grid,
  List,
  Tree,
  SortAsc,
  SortDesc,
  Move
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { getFileExtension } from '@/lib/utils';
import { FileSystemEntry, SearchQuery, SearchResult, FileOperation, OperationResult } from '@/lib/filesystem/filesystem-service';

interface FileExplorerProps {
  className?: string;
}

interface FileExplorerState {
  viewMode: 'tree' | 'list' | 'grid';
  showHidden: boolean;
  sortBy: 'name' | 'size' | 'modified' | 'type';
  sortOrder: 'asc' | 'desc';
  selectedFiles: Set<string>;
  isSearchMode: boolean;
  searchResults: SearchResult[];
  operationProgress: OperationProgress | null;
  watchedFiles: Map<string, string>; // path -> watcherId
  bookmarks: string[];
}

interface OperationProgress {
  type: string;
  current: number;
  total: number;
  status: 'running' | 'completed' | 'error';
  error?: string;
}

interface AdvancedSearchOptions {
  includeContent: boolean;
  fileTypes: string[];
  excludePatterns: string[];
  maxResults: number;
  caseSensitive: boolean;
  useRegex: boolean;
}

interface FileTreeNodeProps {
  node: FileNode;
  level: number;
  isExpanded: boolean;
  onToggle: (path: string) => void;
  onFileClick: (path: string) => void;
  onContextMenu: (node: FileNode, event: React.MouseEvent) => void;
  searchQuery: string;
  expandedFolders: Set<string>;
}

const FileTreeNode: React.FC<FileTreeNodeProps> = ({
  node,
  level,
  isExpanded,
  onToggle,
  onFileClick,
  onContextMenu,
  searchQuery,
  expandedFolders,
}) => {
  const paddingLeft = level * 16;
  const isSearchMatch = searchQuery && node.name.toLowerCase().includes(searchQuery.toLowerCase());

  const handleClick = () => {
    if (node.type === 'directory') {
      onToggle(node.path);
    } else {
      onFileClick(node.path);
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = getFileExtension(fileName);
    const iconMap: Record<string, React.ReactNode> = {
      tsx: <FileText className="h-4 w-4 text-blue-500" />,
      ts: <FileText className="h-4 w-4 text-blue-600" />,
      jsx: <FileText className="h-4 w-4 text-yellow-500" />,
      js: <FileText className="h-4 w-4 text-yellow-600" />,
      py: <FileText className="h-4 w-4 text-green-500" />,
      html: <FileText className="h-4 w-4 text-orange-500" />,
      css: <FileText className="h-4 w-4 text-blue-400" />,
      json: <FileText className="h-4 w-4 text-orange-400" />,
      md: <FileText className="h-4 w-4 text-gray-500" />,
    };
    return iconMap[extension] || <FileText className="h-4 w-4 text-gray-400" />;
  };

  return (
    <div>
      <ContextMenu>
        <ContextMenuTrigger asChild>
          <div
            className={cn(
              'flex items-center gap-1 py-1 px-2 text-sm cursor-pointer hover:bg-accent rounded-sm group',
              isSearchMatch && 'bg-accent/50'
            )}
            style={{ paddingLeft }}
            onClick={handleClick}
            onContextMenu={(e) => onContextMenu(node, e)}
          >
            {node.type === 'directory' ? (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={(e) => {
                    e.stopPropagation();
                    onToggle(node.path);
                  }}
                >
                  {isExpanded ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronRight className="h-3 w-3" />
                  )}
                </Button>
                {isExpanded ? (
                  <FolderOpen className="h-4 w-4 text-blue-500" />
                ) : (
                  <Folder className="h-4 w-4 text-blue-600" />
                )}
              </>
            ) : (
              <>
                <div className="w-4" />
                {getFileIcon(node.name)}
              </>
            )}
            <span className={cn(
              'flex-1 truncate',
              node.type === 'directory' && 'font-medium'
            )}>
              {node.name}
            </span>
            {node.size && (
              <span className="text-xs text-muted-foreground opacity-0 group-hover:opacity-100">
                {formatFileSize(node.size)}
              </span>
            )}
          </div>
        </ContextMenuTrigger>
        <ContextMenuContent>
          {node.type === 'directory' ? (
            <>
              <ContextMenuItem>
                <FileText className="h-4 w-4 mr-2" />
                New File
              </ContextMenuItem>
              <ContextMenuItem>
                <FolderPlus className="h-4 w-4 mr-2" />
                New Folder
              </ContextMenuItem>
              <ContextMenuSeparator />
              <ContextMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                Upload Files
              </ContextMenuItem>
              <ContextMenuSeparator />
              <ContextMenuItem>
                <Copy className="h-4 w-4 mr-2" />
                Copy Path
              </ContextMenuItem>
              <ContextMenuItem>
                <Edit2 className="h-4 w-4 mr-2" />
                Rename
              </ContextMenuItem>
              <ContextMenuItem className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </ContextMenuItem>
            </>
          ) : (
            <>
              <ContextMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download
              </ContextMenuItem>
              <ContextMenuItem>
                <Copy className="h-4 w-4 mr-2" />
                Copy Path
              </ContextMenuItem>
              <ContextMenuSeparator />
              <ContextMenuItem>
                <Edit2 className="h-4 w-4 mr-2" />
                Rename
              </ContextMenuItem>
              <ContextMenuItem className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </ContextMenuItem>
            </>
          )}
        </ContextMenuContent>
      </ContextMenu>

      {node.type === 'directory' && isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <FileTreeNode
              key={child.path}
              node={child}
              level={level + 1}
              isExpanded={expandedFolders.has(child.path)}
              onToggle={onToggle}
              onFileClick={onFileClick}
              onContextMenu={onContextMenu}
              searchQuery={searchQuery}
              expandedFolders={expandedFolders}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export function FileExplorer({ className = '' }: FileExplorerProps) {
  // Basic state
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [createType, setCreateType] = useState<'file' | 'folder'>('file');
  const [createName, setCreateName] = useState('');
  const [createPath, setCreatePath] = useState('');

  // Enhanced state
  const [explorerState, setExplorerState] = useState<FileExplorerState>({
    viewMode: 'tree',
    showHidden: false,
    sortBy: 'name',
    sortOrder: 'asc',
    selectedFiles: new Set(),
    isSearchMode: false,
    searchResults: [],
    operationProgress: null,
    watchedFiles: new Map(),
    bookmarks: []
  });

  // Advanced search state
  const [advancedSearchOpen, setAdvancedSearchOpen] = useState(false);
  const [searchOptions, setSearchOptions] = useState<AdvancedSearchOptions>({
    includeContent: false,
    fileTypes: [],
    excludePatterns: ['.git', 'node_modules', '.next'],
    maxResults: 100,
    caseSensitive: false,
    useRegex: false
  });

  // Bulk operations state
  const [bulkOperationsOpen, setBulkOperationsOpen] = useState(false);
  const [pendingOperations, setPendingOperations] = useState<FileOperation[]>([]);

  const {
    fileTree,
    currentProject,
    openFile,
    createFile,
    createDirectory,
    deleteFile,
    renameFile,
    loadDirectory,
    refreshFileTree,
  } = useEditorStore();

  useEffect(() => {
    // Load a default project on mount
    if (!currentProject) {
      loadDirectory('/sample-project');
    }
  }, [currentProject, loadDirectory]);

  // File system API integration
  const callFileSystemAPI = async (action: string, data: any = {}) => {
    try {
      const method = action === 'list' || action === 'info' || action === 'read' || action === 'exists' ? 'GET' : 'POST';
      const url = method === 'GET' 
        ? `/api/filesystem?action=${action}&${new URLSearchParams(data).toString()}`
        : '/api/filesystem';

      const response = await fetch(url, {
        method,
        headers: method === 'POST' ? { 'Content-Type': 'application/json' } : {},
        body: method === 'POST' ? JSON.stringify({ action, ...data }) : undefined
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('File system API error:', error);
      throw error;
    }
  };

  // Advanced search functionality
  const performAdvancedSearch = useCallback(async () => {
    if (!searchQuery.trim()) return;

    setExplorerState(prev => ({ ...prev, isSearchMode: true, searchResults: [] }));

    try {
      const query: SearchQuery = {
        path: currentProject || '/',
        pattern: searchQuery,
        includeContent: searchOptions.includeContent,
        fileTypes: searchOptions.fileTypes,
        excludePatterns: searchOptions.excludePatterns,
        maxResults: searchOptions.maxResults,
        caseSensitive: searchOptions.caseSensitive,
        useRegex: searchOptions.useRegex
      };

      const result = await callFileSystemAPI('search', { query });
      
      if (result.success) {
        setExplorerState(prev => ({ 
          ...prev, 
          searchResults: result.results,
          isSearchMode: true 
        }));
      }
    } catch (error) {
      console.error('Search failed:', error);
    }
  }, [searchQuery, searchOptions, currentProject]);

  // Bulk operations
  const executeBulkOperations = useCallback(async () => {
    if (pendingOperations.length === 0) return;

    setExplorerState(prev => ({
      ...prev,
      operationProgress: {
        type: 'bulk_operations',
        current: 0,
        total: pendingOperations.length,
        status: 'running'
      }
    }));

    try {
      const result = await callFileSystemAPI('bulk_operation', { operations: pendingOperations });
      
      if (result.success) {
        setExplorerState(prev => ({
          ...prev,
          operationProgress: {
            type: 'bulk_operations',
            current: result.results.length,
            total: pendingOperations.length,
            status: 'completed'
          }
        }));
        
        // Clear operations after completion
        setPendingOperations([]);
        setBulkOperationsOpen(false);
        
        // Refresh file tree
        refreshFileTree();
      }
    } catch (error) {
      setExplorerState(prev => ({
        ...prev,
        operationProgress: {
          type: 'bulk_operations',
          current: 0,
          total: pendingOperations.length,
          status: 'error',
          error: error.message
        }
      }));
    }
  }, [pendingOperations, refreshFileTree]);

  // File watching
  const toggleFileWatch = useCallback(async (filePath: string) => {
    const watcherId = explorerState.watchedFiles.get(filePath);
    
    if (watcherId) {
      // Unwatch file
      try {
        await callFileSystemAPI('watch', { path: filePath, action: 'unwatch', watcherId });
        setExplorerState(prev => {
          const newWatchedFiles = new Map(prev.watchedFiles);
          newWatchedFiles.delete(filePath);
          return { ...prev, watchedFiles: newWatchedFiles };
        });
      } catch (error) {
        console.error('Failed to unwatch file:', error);
      }
    } else {
      // Watch file
      try {
        const result = await callFileSystemAPI('watch', { path: filePath, action: 'watch' });
        if (result.success) {
          setExplorerState(prev => {
            const newWatchedFiles = new Map(prev.watchedFiles);
            newWatchedFiles.set(filePath, result.watcherId);
            return { ...prev, watchedFiles: newWatchedFiles };
          });
        }
      } catch (error) {
        console.error('Failed to watch file:', error);
      }
    }
  }, [explorerState.watchedFiles]);

  // Selection management
  const toggleFileSelection = useCallback((filePath: string) => {
    setExplorerState(prev => {
      const newSelected = new Set(prev.selectedFiles);
      if (newSelected.has(filePath)) {
        newSelected.delete(filePath);
      } else {
        newSelected.add(filePath);
      }
      return { ...prev, selectedFiles: newSelected };
    });
  }, []);

  const selectAllVisible = useCallback(() => {
    const visibleFiles = explorerState.isSearchMode 
      ? explorerState.searchResults.map(r => r.entry.path)
      : getAllFilePaths(displayTree);
    
    setExplorerState(prev => ({ 
      ...prev, 
      selectedFiles: new Set(visibleFiles)
    }));
  }, [explorerState.isSearchMode, explorerState.searchResults]);

  const clearSelection = useCallback(() => {
    setExplorerState(prev => ({ ...prev, selectedFiles: new Set() }));
  }, []);

  // Utility functions
  const getAllFilePaths = (nodes: FileNode[]): string[] => {
    const paths: string[] = [];
    const traverse = (nodeList: FileNode[]) => {
      nodeList.forEach(node => {
        paths.push(node.path);
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(nodes);
    return paths;
  };

  const handleToggleFolder = useCallback((path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  }, []);

  const handleFileClick = useCallback((path: string) => {
    openFile(path);
  }, [openFile]);

  const handleContextMenu = useCallback((node: FileNode, event: React.MouseEvent) => {
    event.preventDefault();
    // Context menu is handled by the ContextMenu component
  }, []);

  const handleCreateItem = useCallback(async () => {
    if (!createName.trim()) return;

    try {
      const fullPath = createPath ? `${createPath}/${createName}` : createName;
      
      if (createType === 'file') {
        await createFile(createName, '', createPath);
      } else {
        await createDirectory(fullPath);
      }
      
      setIsCreateDialogOpen(false);
      setCreateName('');
      setCreatePath('');
      
      // Expand parent folder if creating inside a folder
      if (createPath) {
        setExpandedFolders(prev => new Set([...prev, createPath]));
      }
    } catch (error) {
      console.error('Failed to create item:', error);
    }
  }, [createName, createPath, createType, createFile, createDirectory]);

  const handleRefresh = useCallback(() => {
    refreshFileTree();
  }, [refreshFileTree]);

  const filteredTree = useCallback((nodes: FileNode[]): FileNode[] => {
    if (!searchQuery.trim()) {
      return nodes;
    }

    const query = searchQuery.toLowerCase();
    
    const filterNode = (node: FileNode): FileNode | null => {
      const matchesSearch = node.name.toLowerCase().includes(query);
      
      if (node.type === 'file') {
        return matchesSearch ? node : null;
      }
      
      // For directories, check if any children match
      const filteredChildren = node.children?.map(filterNode).filter(Boolean) || [];
      
      if (matchesSearch || filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren as FileNode[],
        };
      }
      
      return null;
    };

    return nodes.map(filterNode).filter(Boolean) as FileNode[];
  }, [searchQuery]);

  const displayTree = filteredTree(fileTree);

  // Auto-expand folders when searching
  useEffect(() => {
    if (searchQuery.trim()) {
      const expandAllFolders = (nodes: FileNode[]) => {
        nodes.forEach(node => {
          if (node.type === 'directory') {
            setExpandedFolders(prev => new Set([...prev, node.path]));
            if (node.children) {
              expandAllFolders(node.children);
            }
          }
        });
      };
      expandAllFolders(displayTree);
    }
  }, [searchQuery, displayTree]);

  return (
    <div className={cn('h-full flex flex-col border-r bg-card', className)}>
      {/* Enhanced Header */}
      <div className="p-3 border-b">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium flex items-center gap-2">
            Explorer
            {explorerState.selectedFiles.size > 0 && (
              <Badge variant="secondary" className="text-xs">
                {explorerState.selectedFiles.size} selected
              </Badge>
            )}
          </h3>
          <div className="flex items-center gap-1">
            {/* View Mode Toggle */}
            <div className="flex items-center border rounded p-0.5">
              <Button
                variant={explorerState.viewMode === 'tree' ? 'default' : 'ghost'}
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => setExplorerState(prev => ({ ...prev, viewMode: 'tree' }))}
                title="Tree View"
              >
                <Tree className="h-3 w-3" />
              </Button>
              <Button
                variant={explorerState.viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => setExplorerState(prev => ({ ...prev, viewMode: 'list' }))}
                title="List View"
              >
                <List className="h-3 w-3" />
              </Button>
              <Button
                variant={explorerState.viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => setExplorerState(prev => ({ ...prev, viewMode: 'grid' }))}
                title="Grid View"
              >
                <Grid className="h-3 w-3" />
              </Button>
            </div>
            
            <Separator orientation="vertical" className="h-4" />
            
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => {
                setCreateType('file');
                setIsCreateDialogOpen(true);
              }}
              title="New File"
            >
              <FileText className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => {
                setCreateType('folder');
                setIsCreateDialogOpen(true);
              }}
              title="New Folder"
            >
              <FolderPlus className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={handleRefresh}
              title="Refresh"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => setAdvancedSearchOpen(true)}
              title="Advanced Search"
            >
              <Filter className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Enhanced Search */}
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
            <Input
              placeholder="Search files..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  performAdvancedSearch();
                }
              }}
              className="pl-7 h-7 text-xs"
            />
            {explorerState.isSearchMode && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0"
                onClick={() => {
                  setSearchQuery('');
                  setExplorerState(prev => ({ ...prev, isSearchMode: false, searchResults: [] }));
                }}
                title="Clear Search"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* Selection Controls */}
          {explorerState.selectedFiles.size > 0 && (
            <div className="flex items-center gap-1 text-xs">
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs"
                onClick={selectAllVisible}
              >
                Select All
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs"
                onClick={clearSelection}
              >
                Clear
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs"
                onClick={() => setBulkOperationsOpen(true)}
              >
                Bulk Ops
              </Button>
            </div>
          )}

          {/* Operation Progress */}
          {explorerState.operationProgress && (
            <div className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span>{explorerState.operationProgress.type}</span>
                <span>
                  {explorerState.operationProgress.current}/{explorerState.operationProgress.total}
                </span>
              </div>
              <Progress 
                value={(explorerState.operationProgress.current / explorerState.operationProgress.total) * 100} 
                className="h-1"
              />
              {explorerState.operationProgress.status === 'error' && (
                <div className="text-xs text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {explorerState.operationProgress.error}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* File Tree / Search Results */}
      <ScrollArea className="flex-1">
        <div className="p-1">
          {explorerState.isSearchMode ? (
            /* Search Results View */
            explorerState.searchResults.length > 0 ? (
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground p-2 border-b">
                  {explorerState.searchResults.length} results for "{searchQuery}"
                </div>
                {explorerState.searchResults.map((result, index) => (
                  <SearchResultItem
                    key={`${result.entry.path}-${index}`}
                    result={result}
                    isSelected={explorerState.selectedFiles.has(result.entry.path)}
                    onSelect={() => toggleFileSelection(result.entry.path)}
                    onOpen={() => handleFileClick(result.entry.path)}
                    onWatch={() => toggleFileWatch(result.entry.path)}
                    isWatched={explorerState.watchedFiles.has(result.entry.path)}
                  />
                ))}
              </div>
            ) : searchQuery ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground text-sm">No results found for "{searchQuery}"</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => setAdvancedSearchOpen(true)}
                >
                  Advanced Search
                </Button>
              </div>
            ) : null
          ) : (
            /* Regular File Tree View */
            displayTree.length > 0 ? (
              <div className="space-y-0.5">
                {displayTree.map((node) => (
                  <EnhancedFileTreeNode
                    key={node.path}
                    node={node}
                    level={0}
                    viewMode={explorerState.viewMode}
                    isExpanded={expandedFolders.has(node.path)}
                    isSelected={explorerState.selectedFiles.has(node.path)}
                    isWatched={explorerState.watchedFiles.has(node.path)}
                    onToggle={handleToggleFolder}
                    onFileClick={handleFileClick}
                    onContextMenu={handleContextMenu}
                    onSelect={() => toggleFileSelection(node.path)}
                    onWatch={() => toggleFileWatch(node.path)}
                    searchQuery={searchQuery}
                    expandedFolders={expandedFolders}
                    showHidden={explorerState.showHidden}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground text-sm">
                  {searchQuery ? 'No files found' : 'No project loaded'}
                </p>
                {!currentProject && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => loadDirectory('/sample-project')}
                  >
                    Load Sample Project
                  </Button>
                )}
              </div>
            )
          )}
        </div>
      </ScrollArea>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Create New {createType === 'file' ? 'File' : 'Folder'}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Name</label>
              <Input
                value={createName}
                onChange={(e) => setCreateName(e.target.value)}
                placeholder={createType === 'file' ? 'file.txt' : 'folder-name'}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleCreateItem();
                  }
                }}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Path</label>
              <Input
                value={createPath}
                onChange={(e) => setCreatePath(e.target.value)}
                placeholder="Optional: parent/folder/path"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateItem} disabled={!createName.trim()}>
                Create
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Advanced Search Dialog */}
      <Dialog open={advancedSearchOpen} onOpenChange={setAdvancedSearchOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Advanced Search</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Search Pattern</label>
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Enter search pattern..."
                className="mt-1"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeContent"
                    checked={searchOptions.includeContent}
                    onCheckedChange={(checked) => 
                      setSearchOptions(prev => ({ ...prev, includeContent: !!checked }))
                    }
                  />
                  <label htmlFor="includeContent" className="text-sm">Search file contents</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="caseSensitive"
                    checked={searchOptions.caseSensitive}
                    onCheckedChange={(checked) => 
                      setSearchOptions(prev => ({ ...prev, caseSensitive: !!checked }))
                    }
                  />
                  <label htmlFor="caseSensitive" className="text-sm">Case sensitive</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="useRegex"
                    checked={searchOptions.useRegex}
                    onCheckedChange={(checked) => 
                      setSearchOptions(prev => ({ ...prev, useRegex: !!checked }))
                    }
                  />
                  <label htmlFor="useRegex" className="text-sm">Use regex</label>
                </div>
              </div>
              
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium">File Types</label>
                  <Input
                    placeholder="js,ts,tsx,py (optional)"
                    value={searchOptions.fileTypes.join(',')}
                    onChange={(e) => 
                      setSearchOptions(prev => ({ 
                        ...prev, 
                        fileTypes: e.target.value.split(',').filter(t => t.trim()) 
                      }))
                    }
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Max Results</label>
                  <Input
                    type="number"
                    value={searchOptions.maxResults}
                    onChange={(e) => 
                      setSearchOptions(prev => ({ ...prev, maxResults: parseInt(e.target.value) || 100 }))
                    }
                    className="mt-1"
                  />
                </div>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium">Exclude Patterns</label>
              <Input
                placeholder="node_modules,.git,.next"
                value={searchOptions.excludePatterns.join(',')}
                onChange={(e) => 
                  setSearchOptions(prev => ({ 
                    ...prev, 
                    excludePatterns: e.target.value.split(',').filter(p => p.trim()) 
                  }))
                }
                className="mt-1"
              />
            </div>
            
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setAdvancedSearchOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                performAdvancedSearch();
                setAdvancedSearchOpen(false);
              }}>
                Search
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Bulk Operations Dialog */}
      <Dialog open={bulkOperationsOpen} onOpenChange={setBulkOperationsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bulk Operations</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              {explorerState.selectedFiles.size} files selected
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  const operations: FileOperation[] = Array.from(explorerState.selectedFiles).map(path => ({
                    type: 'delete',
                    sourcePath: path
                  }));
                  setPendingOperations(operations);
                }}
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Delete All
              </Button>
              
              <Button
                variant="outline"
                onClick={() => {
                  // TODO: Implement move to folder dialog
                  console.log('Move files:', explorerState.selectedFiles);
                }}
                className="flex items-center gap-2"
              >
                <Move className="h-4 w-4" />
                Move To...
              </Button>
            </div>
            
            {pendingOperations.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm font-medium">Pending Operations:</div>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {pendingOperations.map((op, index) => (
                    <div key={index} className="text-xs p-2 bg-muted rounded flex items-center justify-between">
                      <span>{op.type}: {op.sourcePath}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setPendingOperations(prev => prev.filter((_, i) => i !== index));
                        }}
                        className="h-4 w-4 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setPendingOperations([])}>
                    Clear
                  </Button>
                  <Button onClick={executeBulkOperations}>
                    Execute ({pendingOperations.length})
                  </Button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

// Enhanced File Tree Node Component
interface EnhancedFileTreeNodeProps {
  node: FileNode;
  level: number;
  viewMode: 'tree' | 'list' | 'grid';
  isExpanded: boolean;
  isSelected: boolean;
  isWatched: boolean;
  onToggle: (path: string) => void;
  onFileClick: (path: string) => void;
  onContextMenu: (node: FileNode, event: React.MouseEvent) => void;
  onSelect: () => void;
  onWatch: () => void;
  searchQuery: string;
  expandedFolders: Set<string>;
  showHidden: boolean;
}

const EnhancedFileTreeNode: React.FC<EnhancedFileTreeNodeProps> = ({
  node,
  level,
  viewMode,
  isExpanded,
  isSelected,
  isWatched,
  onToggle,
  onFileClick,
  onContextMenu,
  onSelect,
  onWatch,
  searchQuery,
  expandedFolders,
  showHidden,
}) => {
  const paddingLeft = viewMode === 'tree' ? level * 16 : 8;
  const isSearchMatch = searchQuery && node.name.toLowerCase().includes(searchQuery.toLowerCase());
  const isHidden = node.name.startsWith('.');

  // Don't render hidden files unless showHidden is true
  if (isHidden && !showHidden && !searchQuery) {
    return null;
  }

  const handleClick = () => {
    if (node.type === 'directory') {
      onToggle(node.path);
    } else {
      onFileClick(node.path);
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = getFileExtension(fileName);
    const iconMap: Record<string, React.ReactNode> = {
      tsx: <FileText className="h-4 w-4 text-blue-500" />,
      ts: <FileText className="h-4 w-4 text-blue-600" />,
      jsx: <FileText className="h-4 w-4 text-yellow-500" />,
      js: <FileText className="h-4 w-4 text-yellow-600" />,
      py: <FileText className="h-4 w-4 text-green-500" />,
      html: <FileText className="h-4 w-4 text-orange-500" />,
      css: <FileText className="h-4 w-4 text-blue-400" />,
      json: <FileText className="h-4 w-4 text-orange-400" />,
      md: <FileText className="h-4 w-4 text-gray-500" />,
    };
    return iconMap[extension] || <FileText className="h-4 w-4 text-gray-400" />;
  };

  if (viewMode === 'grid') {
    return (
      <div className="w-full">
        <ContextMenu>
          <ContextMenuTrigger asChild>
            <div
              className={cn(
                'p-3 border rounded-lg cursor-pointer hover:bg-accent transition-colors',
                isSelected && 'bg-blue-50 border-blue-200',
                isSearchMatch && 'bg-accent/50',
                isHidden && 'opacity-60'
              )}
              onClick={handleClick}
              onContextMenu={(e) => onContextMenu(node, e)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={onSelect}
                    onClick={(e) => e.stopPropagation()}
                  />
                  {isWatched && <Eye className="h-3 w-3 text-blue-500" />}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-5 w-5 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    onWatch();
                  }}
                  title={isWatched ? "Stop watching" : "Watch file"}
                >
                  <Eye className={cn("h-3 w-3", isWatched ? "text-blue-500" : "text-gray-400")} />
                </Button>
              </div>
              
              <div className="flex flex-col items-center text-center space-y-2">
                {node.type === 'directory' ? (
                  isExpanded ? (
                    <FolderOpen className="h-8 w-8 text-blue-500" />
                  ) : (
                    <Folder className="h-8 w-8 text-blue-600" />
                  )
                ) : (
                  getFileIcon(node.name)
                )}
                
                <div className="space-y-1">
                  <div className={cn(
                    'text-sm font-medium truncate max-w-full',
                    node.type === 'directory' && 'font-semibold'
                  )}>
                    {node.name}
                  </div>
                  
                  {node.size && (
                    <div className="text-xs text-muted-foreground">
                      {formatFileSize(node.size)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </ContextMenuTrigger>
          <FileContextMenu node={node} onWatch={onWatch} isWatched={isWatched} />
        </ContextMenu>

        {node.type === 'directory' && isExpanded && node.children && (
          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {node.children.map((child) => (
              <EnhancedFileTreeNode
                key={child.path}
                node={child}
                level={level + 1}
                viewMode={viewMode}
                isExpanded={expandedFolders.has(child.path)}
                isSelected={isSelected}
                isWatched={isWatched}
                onToggle={onToggle}
                onFileClick={onFileClick}
                onContextMenu={onContextMenu}
                onSelect={onSelect}
                onWatch={onWatch}
                searchQuery={searchQuery}
                expandedFolders={expandedFolders}
                showHidden={showHidden}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <div>
      <ContextMenu>
        <ContextMenuTrigger asChild>
          <div
            className={cn(
              'flex items-center gap-2 py-1 px-2 text-sm cursor-pointer hover:bg-accent rounded-sm group',
              isSelected && 'bg-blue-50 border-l-2 border-l-blue-500',
              isSearchMatch && 'bg-accent/50',
              isHidden && 'opacity-60'
            )}
            style={{ paddingLeft }}
            onClick={handleClick}
            onContextMenu={(e) => onContextMenu(node, e)}
          >
            {/* Selection Checkbox */}
            <Checkbox
              checked={isSelected}
              onCheckedChange={onSelect}
              onClick={(e) => e.stopPropagation()}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            />

            {/* Directory Toggle */}
            {node.type === 'directory' ? (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={(e) => {
                    e.stopPropagation();
                    onToggle(node.path);
                  }}
                >
                  {isExpanded ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronRight className="h-3 w-3" />
                  )}
                </Button>
                {isExpanded ? (
                  <FolderOpen className="h-4 w-4 text-blue-500" />
                ) : (
                  <Folder className="h-4 w-4 text-blue-600" />
                )}
              </>
            ) : (
              <>
                <div className="w-4" />
                {getFileIcon(node.name)}
              </>
            )}

            {/* File Name */}
            <span className={cn(
              'flex-1 truncate',
              node.type === 'directory' && 'font-medium'
            )}>
              {node.name}
            </span>

            {/* Indicators */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {isWatched && <Eye className="h-3 w-3 text-blue-500" />}
              
              {node.size && (
                <span className="text-xs text-muted-foreground">
                  {formatFileSize(node.size)}
                </span>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  onWatch();
                }}
                title={isWatched ? "Stop watching" : "Watch file"}
              >
                <Eye className={cn("h-3 w-3", isWatched ? "text-blue-500" : "text-gray-400")} />
              </Button>
            </div>
          </div>
        </ContextMenuTrigger>
        <FileContextMenu node={node} onWatch={onWatch} isWatched={isWatched} />
      </ContextMenu>

      {node.type === 'directory' && isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <EnhancedFileTreeNode
              key={child.path}
              node={child}
              level={level + 1}
              viewMode={viewMode}
              isExpanded={expandedFolders.has(child.path)}
              isSelected={isSelected}
              isWatched={isWatched}
              onToggle={onToggle}
              onFileClick={onFileClick}
              onContextMenu={onContextMenu}
              onSelect={onSelect}
              onWatch={onWatch}
              searchQuery={searchQuery}
              expandedFolders={expandedFolders}
              showHidden={showHidden}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Search Result Item Component
interface SearchResultItemProps {
  result: SearchResult;
  isSelected: boolean;
  onSelect: () => void;
  onOpen: () => void;
  onWatch: () => void;
  isWatched: boolean;
}

const SearchResultItem: React.FC<SearchResultItemProps> = ({
  result,
  isSelected,
  onSelect,
  onOpen,
  onWatch,
  isWatched,
}) => {
  const getFileIcon = (fileName: string) => {
    const extension = getFileExtension(fileName);
    const iconMap: Record<string, React.ReactNode> = {
      tsx: <FileText className="h-4 w-4 text-blue-500" />,
      ts: <FileText className="h-4 w-4 text-blue-600" />,
      jsx: <FileText className="h-4 w-4 text-yellow-500" />,
      js: <FileText className="h-4 w-4 text-yellow-600" />,
      py: <FileText className="h-4 w-4 text-green-500" />,
      html: <FileText className="h-4 w-4 text-orange-500" />,
      css: <FileText className="h-4 w-4 text-blue-400" />,
      json: <FileText className="h-4 w-4 text-orange-400" />,
      md: <FileText className="h-4 w-4 text-gray-500" />,
    };
    return iconMap[extension] || <FileText className="h-4 w-4 text-gray-400" />;
  };

  return (
    <div
      className={cn(
        'p-3 border rounded-lg cursor-pointer hover:bg-accent transition-colors',
        isSelected && 'bg-blue-50 border-blue-200'
      )}
      onClick={onOpen}
    >
      <div className="flex items-start gap-3">
        <Checkbox
          checked={isSelected}
          onCheckedChange={onSelect}
          onClick={(e) => e.stopPropagation()}
        />
        
        {result.entry.type === 'directory' ? (
          <Folder className="h-4 w-4 text-blue-600 mt-0.5" />
        ) : (
          getFileIcon(result.entry.name)
        )}
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium truncate">{result.entry.name}</span>
            <Badge variant="outline" className="text-xs">
              {Math.round(result.relevance)}% match
            </Badge>
            {isWatched && <Eye className="h-3 w-3 text-blue-500" />}
          </div>
          
          <div className="text-xs text-muted-foreground truncate mb-2">
            {result.entry.path}
          </div>
          
          {result.matches && result.matches.length > 0 && (
            <div className="space-y-1">
              {result.matches.slice(0, 3).map((match, index) => (
                <div key={index} className="text-xs bg-muted p-1 rounded font-mono">
                  {match}
                </div>
              ))}
              {result.matches.length > 3 && (
                <div className="text-xs text-muted-foreground">
                  +{result.matches.length - 3} more matches
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {result.entry.size && (
            <span className="text-xs text-muted-foreground">
              {formatFileSize(result.entry.size)}
            </span>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onWatch();
            }}
            title={isWatched ? "Stop watching" : "Watch file"}
          >
            <Eye className={cn("h-3 w-3", isWatched ? "text-blue-500" : "text-gray-400")} />
          </Button>
        </div>
      </div>
    </div>
  );
};

// Enhanced Context Menu Component
interface FileContextMenuProps {
  node: FileNode;
  onWatch: () => void;
  isWatched: boolean;
}

const FileContextMenu: React.FC<FileContextMenuProps> = ({ node, onWatch, isWatched }) => {
  return (
    <ContextMenuContent>
      {node.type === 'directory' ? (
        <>
          <ContextMenuItem>
            <FileText className="h-4 w-4 mr-2" />
            New File
          </ContextMenuItem>
          <ContextMenuItem>
            <FolderPlus className="h-4 w-4 mr-2" />
            New Folder
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem>
            <Upload className="h-4 w-4 mr-2" />
            Upload Files
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem onClick={onWatch}>
            <Eye className="h-4 w-4 mr-2" />
            {isWatched ? 'Stop Watching' : 'Watch Directory'}
          </ContextMenuItem>
          <ContextMenuItem>
            <Copy className="h-4 w-4 mr-2" />
            Copy Path
          </ContextMenuItem>
          <ContextMenuItem>
            <Edit2 className="h-4 w-4 mr-2" />
            Rename
          </ContextMenuItem>
          <ContextMenuItem className="text-destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </ContextMenuItem>
        </>
      ) : (
        <>
          <ContextMenuItem>
            <Download className="h-4 w-4 mr-2" />
            Download
          </ContextMenuItem>
          <ContextMenuItem onClick={onWatch}>
            <Eye className="h-4 w-4 mr-2" />
            {isWatched ? 'Stop Watching' : 'Watch File'}
          </ContextMenuItem>
          <ContextMenuItem>
            <Copy className="h-4 w-4 mr-2" />
            Copy Path
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem>
            <Edit2 className="h-4 w-4 mr-2" />
            Rename
          </ContextMenuItem>
          <ContextMenuItem className="text-destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </ContextMenuItem>
        </>
      )}
    </ContextMenuContent>
  );
};