'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Editor, OnMount, OnChange } from '@monaco-editor/react';
import { editor } from 'monaco-editor';
import { useEditorStore } from '@/store/editor-store';
import { getMonacoEditorOptions, setupMonacoEnvironment, registerCustomTheme } from '@/lib/monaco-config';
import { languageManager } from '@/lib/language-manager';
import { languageServiceManager } from '@/lib/language-providers';
import { FormattingService } from '@/lib/formatting-service';
import { aiLanguageServiceManager } from '@/lib/ai/ai-language-provider';
import { aiServiceManager } from '@/lib/ai/ai-service-manager';
import { useTheme } from 'next-themes';
import { Loader2 } from 'lucide-react';

interface MonacoEditorProps {
  fileId: string;
  className?: string;
}

export function MonacoEditor({ fileId, className = '' }: MonacoEditorProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const { theme } = useTheme();
  
  const {
    getFileById,
    updateFileContent,
    configuration,
    registerEditor,
    unregisterEditor,
    saveFile,
  } = useEditorStore();

  const file = getFileById(fileId);

  useEffect(() => {
    // Setup Monaco environment on first load
    setupMonacoEnvironment();
    registerCustomTheme();
    
    // Initialize language service providers
    languageServiceManager.initializeAllLanguages();
    
    // Initialize AI-enhanced language providers
    aiLanguageServiceManager.initializeAllAILanguages();
    
    // Initialize AI services
    aiServiceManager.initializeServices().catch(console.error);
  }, []);

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (editorRef.current) {
        unregisterEditor(fileId);
      }
    };
  }, [fileId, unregisterEditor]);

  const handleEditorDidMount: OnMount = (editorInstance, monacoInstance) => {
    editorRef.current = editorInstance;
    setIsLoading(false);
    setError(null);

    // Register the editor instance
    registerEditor(fileId, editorInstance);

    // Focus the editor
    editorInstance.focus();

    // Add keyboard shortcuts
    editorInstance.addCommand(
      monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyCode.KeyS,
      () => {
        saveFile(fileId);
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.Shift | monacoInstance.KeyMod.Alt | monacoInstance.KeyCode.KeyF,
      async () => {
        const model = editorInstance.getModel();
        if (model) {
          // Use custom formatting service
          const edits = await FormattingService.formatDocument(model, {
            tabSize: configuration.tabSize,
            insertSpaces: configuration.insertSpaces,
          }, configuration);
          
          if (edits.length > 0) {
            editorInstance.executeEdits('format-document', edits);
          }
        }
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyCode.KeyD,
      () => {
        editorInstance.getAction('editor.action.addSelectionToNextFindMatch')?.run();
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyCode.Slash,
      () => {
        editorInstance.getAction('editor.action.commentLine')?.run();
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.Alt | monacoInstance.KeyCode.UpArrow,
      () => {
        editorInstance.getAction('editor.action.moveLinesUpAction')?.run();
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.Alt | monacoInstance.KeyCode.DownArrow,
      () => {
        editorInstance.getAction('editor.action.moveLinesDownAction')?.run();
      }
    );

    // Enhanced features
    editorInstance.addCommand(
      monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyCode.KeyK,
      () => {
        editorInstance.getAction('editor.action.showDefinitionPreviewHover')?.run();
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.F12,
      () => {
        editorInstance.getAction('editor.action.revealDefinition')?.run();
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.Shift | monacoInstance.KeyCode.F12,
      () => {
        editorInstance.getAction('editor.action.goToReferences')?.run();
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.F2,
      () => {
        editorInstance.getAction('editor.action.rename')?.run();
      }
    );

    // AI-powered commands
    editorInstance.addCommand(
      monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyMod.Shift | monacoInstance.KeyCode.KeyI,
      async () => {
        // AI code suggestions
        const model = editorInstance.getModel();
        const selection = editorInstance.getSelection();
        if (model && selection) {
          try {
            const selectedText = model.getValueInRange(selection);
            const fullCode = model.getValue();
            const suggestions = await aiServiceManager.getIntelligentSuggestions(
              fullCode,
              selectedText,
              file.language,
              file.name
            );
            
            if (suggestions.length > 0) {
              // Show suggestions in a quick pick or notification
              console.log('AI Suggestions:', suggestions);
              // TODO: Implement suggestions UI
            }
          } catch (error) {
            console.error('AI suggestions failed:', error);
          }
        }
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyMod.Shift | monacoInstance.KeyCode.KeyA,
      async () => {
        // AI code analysis
        const model = editorInstance.getModel();
        if (model) {
          try {
            const code = model.getValue();
            const analysis = await aiServiceManager.performCodeAnalysis(
              code,
              file.language,
              file.name
            );
            
            if (analysis) {
              console.log('AI Analysis:', analysis);
              // TODO: Implement analysis results UI
            }
          } catch (error) {
            console.error('AI analysis failed:', error);
          }
        }
      }
    );

    editorInstance.addCommand(
      monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyMod.Shift | monacoInstance.KeyCode.KeyT,
      async () => {
        // AI test generation
        const model = editorInstance.getModel();
        if (model) {
          try {
            const code = model.getValue();
            const tests = await aiServiceManager.createTests(
              code,
              file.language,
              file.name
            );
            
            if (tests) {
              console.log('AI Generated Tests:', tests);
              // TODO: Implement test generation UI
            }
          } catch (error) {
            console.error('AI test generation failed:', error);
          }
        }
      }
    );

    // Auto-save configuration
    if (configuration.autoSave) {
      const disposable = editorInstance.onDidChangeModelContent(() => {
        // Debounced auto-save
        setTimeout(async () => {
          if (file?.isDirty) {
            // Format on save if enabled
            if (configuration.formatOnSave) {
              const model = editorInstance.getModel();
              if (model) {
                const edits = await FormattingService.formatDocument(model, {
                  tabSize: configuration.tabSize,
                  insertSpaces: configuration.insertSpaces,
                }, configuration);
                
                if (edits.length > 0) {
                  editorInstance.executeEdits('format-on-save', edits);
                }
              }
            }
            
            saveFile(fileId);
          }
        }, configuration.autoSaveDelay);
      });

      // Store disposable for cleanup
      editorInstance.onDidDispose(() => {
        disposable.dispose();
      });
    }

    // Accessibility enhancements
    editorInstance.updateOptions({
      accessibilitySupport: 'on',
      ariaLabel: `Code editor for ${file?.name || 'file'}`,
    });

    // Performance optimizations
    editorInstance.updateOptions({
      smoothScrolling: true,
      cursorSmoothCaretAnimation: 'on',
      suggest: {
        ...editorInstance.getOptions().suggest,
        filterGraceful: true,
        localityBonus: true,
      },
    });
  };

  const handleEditorChange: OnChange = (value) => {
    if (value !== undefined) {
      updateFileContent(fileId, value);
    }
  };

  const handleEditorValidation = (markers: editor.IMarkerData[]) => {
    // Handle validation markers (errors, warnings, etc.)
    if (markers.length > 0) {
      console.log('Validation markers:', markers);
    }
  };

  // Enhanced validation with language service providers and linting
  useEffect(() => {
    if (editorRef.current && file) {
      const model = editorRef.current.getModel();
      if (model) {
        const validateModel = async () => {
          try {
            // Get diagnostics from language service providers
            const diagnostics = await languageServiceManager.provideDiagnostics(model);
            
            // Get linting results from formatting service
            const lintResults = await FormattingService.lintDocument(model);
            
            // Combine diagnostics and linting results
            const allMarkers = [...diagnostics, ...lintResults];
            
            // Set markers on the model
            editor.setModelMarkers(model, 'validation', allMarkers);
          } catch (error) {
            console.error('Error validating model:', error);
          }
        };

        // Validate on content change
        const disposable = model.onDidChangeContent(() => {
          // Debounce validation
          clearTimeout((window as any).validationTimeout);
          (window as any).validationTimeout = setTimeout(validateModel, 500);
        });

        // Initial validation
        validateModel();

        return () => {
          disposable.dispose();
          clearTimeout((window as any).validationTimeout);
        };
      }
    }
  }, [file, editorRef.current]);

  if (!file) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <p className="text-muted-foreground">File not found</p>
        </div>
      </div>
    );
  }

  if (file.isLoading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">Loading file...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <p className="text-destructive mb-2">Error loading editor</p>
          <p className="text-xs text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  const editorOptions = getMonacoEditorOptions({
    ...configuration,
    theme: theme === 'dark' ? 'unified-dark' : 'vs',
    readOnly: file.isReadonly,
    minimap: {
      enabled: file.content.length > 5000 ? configuration.minimap.enabled : false,
      side: configuration.minimap.side,
    },
  });

  return (
    <div className={`relative h-full ${className}`}>
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
          <div className="text-center">
            <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
            <p className="text-muted-foreground text-sm">Initializing editor...</p>
          </div>
        </div>
      )}

      {/* Readonly indicator */}
      {file.isReadonly && (
        <div className="absolute top-2 right-2 z-20">
          <span className="bg-muted text-muted-foreground px-2 py-1 rounded text-xs">
            Read-only
          </span>
        </div>
      )}

      {/* Dirty indicator */}
      {file.isDirty && (
        <div className="absolute top-2 left-2 z-20">
          <span className="bg-warning text-warning-foreground px-2 py-1 rounded text-xs">
            Unsaved changes
          </span>
        </div>
      )}

      <Editor
        height="100%"
        language={file.language}
        value={file.content}
        options={editorOptions}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        onValidate={handleEditorValidation}
        theme={theme === 'dark' ? 'unified-dark' : 'vs'}
        loading={
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
              <p className="text-muted-foreground text-sm">Loading Monaco Editor...</p>
            </div>
          </div>
        }
        beforeMount={(monacoInstance) => {
          // Additional Monaco setup before mounting
          try {
            // Register custom theme
            registerCustomTheme();
            
            // Initialize language manager
            languageManager.getSupportedLanguages().forEach(lang => {
              if (!monacoInstance.languages.getLanguages().find(l => l.id === lang.id)) {
                monacoInstance.languages.register({ id: lang.id });
              }
            });
          } catch (error) {
            console.error('Error setting up Monaco:', error);
            setError('Failed to initialize editor features');
          }
        }}
      />
    </div>
  );
}