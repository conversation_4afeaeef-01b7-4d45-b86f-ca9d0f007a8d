'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FileSystemEntry, SearchQuery, SearchResult, WebFileSystemService } from '@/lib/filesystem/filesystem-service';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  FolderIcon, 
  FileIcon, 
  Search, 
  Plus, 
  Upload, 
  Download, 
  MoreVertical,
  Grid,
  List,
  TreePine,
  Copy,
  Trash2,
  <PERSON>,
  Eye,
  Folder,
  FolderOpen,
  ChevronRight,
  ChevronDown,
  Home,
  RefreshCw,
  Filter,
  SortAsc,
  SortDesc,
  Settings,
  Share,
  Star,
  Clock,
  Size,
  Code,
  Image,
  FileText,
  Music,
  Video,
  Archive,
  X
} from 'lucide-react';
import { formatBytes, formatDistanceToNow } from 'date-fns';

interface FileExplorerProps {
  rootPath: string;
  selectedFiles: string[];
  onFileSelect: (files: string[]) => void;
  onFileOpen: (path: string) => void;
  onFileOperation?: (operation: any) => Promise<void>;
  viewMode?: 'tree' | 'list' | 'grid';
  showHidden?: boolean;
  className?: string;
}

export const FileExplorer: React.FC<FileExplorerProps> = ({
  rootPath,
  selectedFiles,
  onFileSelect,
  onFileOpen,
  onFileOperation,
  viewMode = 'tree',
  showHidden = false,
  className = ''
}) => {
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set([rootPath]));
  const [loading, setLoading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [currentPath, setCurrentPath] = useState(rootPath);
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'modified' | 'type'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [fileSystemService] = useState(() => new WebFileSystemService());

  useEffect(() => {
    initializeFileSystem();
  }, []);

  useEffect(() => {
    loadFileTree();
  }, [rootPath, showHidden, currentPath]);

  const initializeFileSystem = async () => {
    try {
      await fileSystemService.initialize();
    } catch (error) {
      console.error('Failed to initialize file system:', error);
    }
  };

  const loadFileTree = async () => {
    setLoading(true);
    try {
      const entries = await fileSystemService.listDirectory(currentPath);
      const tree = await buildFileTree(entries, showHidden);
      setFileTree(tree);
    } catch (error) {
      console.error('Failed to load file tree:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildFileTree = async (entries: FileSystemEntry[], includeHidden: boolean): Promise<FileNode[]> => {
    const filteredEntries = entries.filter(entry => includeHidden || !entry.isHidden);
    
    return filteredEntries.map(entry => ({
      id: entry.path,
      name: entry.name,
      path: entry.path,
      type: entry.type,
      size: entry.size,
      modified: entry.modified,
      created: entry.created,
      permissions: entry.permissions,
      isHidden: entry.isHidden,
      mimeType: entry.mimeType,
      children: entry.type === 'directory' ? [] : undefined,
      isExpanded: expandedPaths.has(entry.path),
      isSelected: selectedFiles.includes(entry.path)
    }));
  };

  const handlePathExpand = async (path: string) => {
    const newExpanded = new Set(expandedPaths);
    
    if (expandedPaths.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
      
      // Load children if not already loaded
      try {
        const entries = await fileSystemService.listDirectory(path);
        const children = await buildFileTree(entries, showHidden);
        
        setFileTree(prevTree => {
          const updateNode = (nodes: FileNode[]): FileNode[] => {
            return nodes.map(node => {
              if (node.path === path) {
                return { ...node, children, isExpanded: true };
              }
              if (node.children) {
                return { ...node, children: updateNode(node.children) };
              }
              return node;
            });
          };
          return updateNode(prevTree);
        });
      } catch (error) {
        console.error('Failed to load directory:', error);
      }
    }
    
    setExpandedPaths(newExpanded);
  };

  const handleFileSelection = (path: string, multiSelect: boolean = false) => {
    if (multiSelect) {
      const newSelection = selectedFiles.includes(path)
        ? selectedFiles.filter(p => p !== path)
        : [...selectedFiles, path];
      onFileSelect(newSelection);
    } else {
      onFileSelect([path]);
    }
  };

  const handleFileDoubleClick = (path: string, type: 'file' | 'directory') => {
    if (type === 'directory') {
      setCurrentPath(path);
      handlePathExpand(path);
    } else {
      onFileOpen(path);
    }
  };

  const handleDrop = async (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    
    const files = Array.from(event.dataTransfer.files);
    if (files.length === 0) return;

    try {
      await uploadFiles(files, currentPath);
      await loadFileTree();
    } catch (error) {
      console.error('Failed to upload files:', error);
    }
  };

  const uploadFiles = async (files: File[], targetPath: string) => {
    for (const file of files) {
      const content = new Uint8Array(await file.arrayBuffer());
      const filePath = `${targetPath}/${file.name}`;
      await fileSystemService.writeFile(filePath, content);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const query: SearchQuery = {
        path: currentPath,
        pattern: searchQuery,
        includeContent: true,
        fileTypes: [],
        excludePatterns: ['.git', 'node_modules', '.vscode'],
        maxResults: 100,
        caseSensitive: false,
        useRegex: false
      };

      const results = await fileSystemService.searchFiles(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleFileAction = async (path: string, action: string) => {
    try {
      switch (action) {
        case 'copy':
          await navigator.clipboard.writeText(path);
          break;
        case 'delete':
          await fileSystemService.deleteFile(path);
          await loadFileTree();
          break;
        case 'rename':
          // Implementation for rename dialog
          break;
        case 'duplicate':
          const newPath = `${path}.copy`;
          await fileSystemService.copyFile(path, newPath);
          await loadFileTree();
          break;
        case 'properties':
          // Implementation for properties dialog
          break;
        default:
          console.warn(`Unknown action: ${action}`);
      }
    } catch (error) {
      console.error(`Failed to ${action} file:`, error);
    }
  };

  const handleNewFile = async () => {
    const fileName = prompt('Enter file name:');
    if (fileName) {
      const filePath = `${currentPath}/${fileName}`;
      await fileSystemService.writeFile(filePath, new Uint8Array());
      await loadFileTree();
    }
  };

  const handleNewFolder = async () => {
    const folderName = prompt('Enter folder name:');
    if (folderName) {
      const folderPath = `${currentPath}/${folderName}`;
      await fileSystemService.createDirectory(folderPath);
      await loadFileTree();
    }
  };

  const sortedTree = useMemo(() => {
    const sortNodes = (nodes: FileNode[]): FileNode[] => {
      const sorted = [...nodes].sort((a, b) => {
        // Always put directories first
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }

        let comparison = 0;
        switch (sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name);
            break;
          case 'size':
            comparison = a.size - b.size;
            break;
          case 'modified':
            comparison = a.modified.getTime() - b.modified.getTime();
            break;
          case 'type':
            comparison = (a.mimeType || '').localeCompare(b.mimeType || '');
            break;
        }

        return sortOrder === 'asc' ? comparison : -comparison;
      });

      return sorted.map(node => ({
        ...node,
        children: node.children ? sortNodes(node.children) : undefined
      }));
    };

    return sortNodes(fileTree);
  }, [fileTree, sortBy, sortOrder]);

  const breadcrumbs = useMemo(() => {
    const parts = currentPath.split('/').filter(p => p);
    return parts.map((part, index) => ({
      name: part,
      path: '/' + parts.slice(0, index + 1).join('/')
    }));
  }, [currentPath]);

  return (
    <div
      className={`file-explorer ${className} h-full flex flex-col`}
      onDrop={handleDrop}
      onDragOver={(e) => { e.preventDefault(); setDragOver(true); }}
      onDragLeave={() => setDragOver(false)}
    >
      <FileExplorerHeader
        currentPath={currentPath}
        breadcrumbs={breadcrumbs}
        onPathChange={setCurrentPath}
        onRefresh={loadFileTree}
        onNewFile={handleNewFile}
        onNewFolder={handleNewFolder}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSortBy}
        onSortOrderChange={setSortOrder}
      />

      <FileExplorerSearch
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onSearch={handleSearch}
        isSearching={isSearching}
        results={searchResults}
        onResultClick={(path) => onFileOpen(path)}
      />

      <div className="flex-1 overflow-hidden">
        {loading ? (
          <FileExplorerSkeleton />
        ) : searchResults.length > 0 ? (
          <SearchResultsList
            results={searchResults}
            onResultClick={onFileOpen}
            onResultSelect={handleFileSelection}
          />
        ) : (
          <FileExplorerContent
            fileTree={sortedTree}
            viewMode={viewMode}
            selectedFiles={selectedFiles}
            expandedPaths={expandedPaths}
            onFileSelect={handleFileSelection}
            onFileDoubleClick={handleFileDoubleClick}
            onFileAction={handleFileAction}
            onPathExpand={handlePathExpand}
          />
        )}
      </div>

      {dragOver && (
        <div className="absolute inset-0 bg-blue-50 bg-opacity-75 flex items-center justify-center border-2 border-blue-300 border-dashed">
          <div className="text-center">
            <Upload size={48} className="mx-auto mb-4 text-blue-500" />
            <p className="text-lg font-medium text-blue-700">Drop files here to upload</p>
          </div>
        </div>
      )}
    </div>
  );
};

const FileExplorerHeader: React.FC<{
  currentPath: string;
  breadcrumbs: { name: string; path: string }[];
  onPathChange: (path: string) => void;
  onRefresh: () => void;
  onNewFile: () => void;
  onNewFolder: () => void;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (sort: 'name' | 'size' | 'modified' | 'type') => void;
  onSortOrderChange: (order: 'asc' | 'desc') => void;
}> = ({
  currentPath,
  breadcrumbs,
  onPathChange,
  onRefresh,
  onNewFile,
  onNewFolder,
  sortBy,
  sortOrder,
  onSortChange,
  onSortOrderChange
}) => {
  return (
    <div className="border-b bg-gray-50 p-3">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={() => onPathChange('/')}>
            <Home size={16} />
          </Button>
          <div className="flex items-center space-x-1">
            {breadcrumbs.map((crumb, index) => (
              <React.Fragment key={crumb.path}>
                <ChevronRight size={12} className="text-gray-400" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onPathChange(crumb.path)}
                  className="text-sm"
                >
                  {crumb.name}
                </Button>
              </React.Fragment>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter size={16} className="mr-2" />
                Sort: {sortBy}
                {sortOrder === 'asc' ? <SortAsc size={16} className="ml-2" /> : <SortDesc size={16} className="ml-2" />}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onSortChange('name')}>Name</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onSortChange('size')}>Size</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onSortChange('modified')}>Modified</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onSortChange('type')}>Type</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onSortOrderChange('asc')}>Ascending</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onSortOrderChange('desc')}>Descending</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw size={16} />
          </Button>

          <Button variant="outline" size="sm" onClick={onNewFile}>
            <Plus size={16} className="mr-2" />
            File
          </Button>

          <Button variant="outline" size="sm" onClick={onNewFolder}>
            <Folder size={16} className="mr-2" />
            Folder
          </Button>
        </div>
      </div>
    </div>
  );
};

const FileExplorerSearch: React.FC<{
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSearch: () => void;
  isSearching: boolean;
  results: SearchResult[];
  onResultClick: (path: string) => void;
}> = ({
  searchQuery,
  onSearchChange,
  onSearch,
  isSearching,
  results,
  onResultClick
}) => {
  return (
    <div className="border-b p-3">
      <div className="flex items-center space-x-2">
        <div className="flex-1 relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search files and folders..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && onSearch()}
            className="pl-10"
          />
        </div>
        <Button onClick={onSearch} disabled={isSearching}>
          {isSearching ? 'Searching...' : 'Search'}
        </Button>
      </div>
      
      {results.length > 0 && (
        <div className="mt-2">
          <Badge variant="secondary">{results.length} results found</Badge>
        </div>
      )}
    </div>
  );
};

const FileExplorerContent: React.FC<{
  fileTree: FileNode[];
  viewMode: 'tree' | 'list' | 'grid';
  selectedFiles: string[];
  expandedPaths: Set<string>;
  onFileSelect: (path: string, multiSelect?: boolean) => void;
  onFileDoubleClick: (path: string, type: 'file' | 'directory') => void;
  onFileAction: (path: string, action: string) => void;
  onPathExpand: (path: string) => void;
}> = ({
  fileTree,
  viewMode,
  selectedFiles,
  expandedPaths,
  onFileSelect,
  onFileDoubleClick,
  onFileAction,
  onPathExpand
}) => {
  return (
    <ScrollArea className="h-full">
      {viewMode === 'tree' ? (
        <FileTreeView
          nodes={fileTree}
          selectedFiles={selectedFiles}
          expandedPaths={expandedPaths}
          onSelect={onFileSelect}
          onDoubleClick={onFileDoubleClick}
          onAction={onFileAction}
          onExpand={onPathExpand}
        />
      ) : viewMode === 'grid' ? (
        <FileGridView
          nodes={fileTree}
          selectedFiles={selectedFiles}
          onSelect={onFileSelect}
          onDoubleClick={onFileDoubleClick}
          onAction={onFileAction}
        />
      ) : (
        <FileListView
          nodes={fileTree}
          selectedFiles={selectedFiles}
          onSelect={onFileSelect}
          onDoubleClick={onFileDoubleClick}
          onAction={onFileAction}
        />
      )}
    </ScrollArea>
  );
};

const FileTreeView: React.FC<{
  nodes: FileNode[];
  selectedFiles: string[];
  expandedPaths: Set<string>;
  onSelect: (path: string, multiSelect?: boolean) => void;
  onDoubleClick: (path: string, type: 'file' | 'directory') => void;
  onAction: (path: string, action: string) => void;
  onExpand: (path: string) => void;
  depth?: number;
}> = ({
  nodes,
  selectedFiles,
  expandedPaths,
  onSelect,
  onDoubleClick,
  onAction,
  onExpand,
  depth = 0
}) => {
  return (
    <div className="space-y-1">
      {nodes.map(node => (
        <FileTreeNode
          key={node.id}
          node={node}
          depth={depth}
          isSelected={selectedFiles.includes(node.path)}
          isExpanded={expandedPaths.has(node.path)}
          onSelect={(multiSelect) => onSelect(node.path, multiSelect)}
          onDoubleClick={() => onDoubleClick(node.path, node.type)}
          onAction={(action) => onAction(node.path, action)}
          onExpand={() => onExpand(node.path)}
        >
          {node.children && expandedPaths.has(node.path) && (
            <FileTreeView
              nodes={node.children}
              selectedFiles={selectedFiles}
              expandedPaths={expandedPaths}
              onSelect={onSelect}
              onDoubleClick={onDoubleClick}
              onAction={onAction}
              onExpand={onExpand}
              depth={depth + 1}
            />
          )}
        </FileTreeNode>
      ))}
    </div>
  );
};

const FileTreeNode: React.FC<{
  node: FileNode;
  depth: number;
  isSelected: boolean;
  isExpanded: boolean;
  onSelect: (multiSelect: boolean) => void;
  onDoubleClick: () => void;
  onAction: (action: string) => void;
  onExpand: () => void;
  children?: React.ReactNode;
}> = ({
  node,
  depth,
  isSelected,
  isExpanded,
  onSelect,
  onDoubleClick,
  onAction,
  onExpand,
  children
}) => {
  const getFileIcon = (type: 'file' | 'directory', mimeType?: string) => {
    if (type === 'directory') {
      return isExpanded ? <FolderOpen size={16} /> : <Folder size={16} />;
    }

    if (mimeType) {
      if (mimeType.startsWith('image/')) return <Image size={16} />;
      if (mimeType.startsWith('video/')) return <Video size={16} />;
      if (mimeType.startsWith('audio/')) return <Music size={16} />;
      if (mimeType.includes('text/') || mimeType.includes('application/json')) return <FileText size={16} />;
      if (mimeType.includes('javascript') || mimeType.includes('typescript')) return <Code size={16} />;
      if (mimeType.includes('zip') || mimeType.includes('tar')) return <Archive size={16} />;
    }

    return <FileIcon size={16} />;
  };

  return (
    <div>
      <div
        className={`flex items-center py-1 px-2 rounded hover:bg-gray-100 cursor-pointer ${
          isSelected ? 'bg-blue-100' : ''
        }`}
        style={{ paddingLeft: `${depth * 20 + 8}px` }}
        onClick={(e) => onSelect(e.ctrlKey || e.metaKey)}
        onDoubleClick={onDoubleClick}
        onContextMenu={(e) => e.preventDefault()}
      >
        {node.type === 'directory' && (
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto mr-1"
            onClick={(e) => {
              e.stopPropagation();
              onExpand();
            }}
          >
            {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </Button>
        )}
        
        <div className="flex items-center space-x-2 flex-1">
          {getFileIcon(node.type, node.mimeType)}
          <span className={`text-sm ${node.isHidden ? 'text-gray-500' : ''}`}>
            {node.name}
          </span>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="p-1 h-auto">
              <MoreVertical size={12} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => onAction('copy')}>
              <Copy size={16} className="mr-2" />
              Copy Path
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onAction('rename')}>
              <Edit size={16} className="mr-2" />
              Rename
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onAction('duplicate')}>
              <Copy size={16} className="mr-2" />
              Duplicate
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onAction('delete')} className="text-red-600">
              <Trash2 size={16} className="mr-2" />
              Delete
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onAction('properties')}>
              <Settings size={16} className="mr-2" />
              Properties
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {children}
    </div>
  );
};

const FileGridView: React.FC<{
  nodes: FileNode[];
  selectedFiles: string[];
  onSelect: (path: string, multiSelect?: boolean) => void;
  onDoubleClick: (path: string, type: 'file' | 'directory') => void;
  onAction: (path: string, action: string) => void;
}> = ({ nodes, selectedFiles, onSelect, onDoubleClick, onAction }) => {
  return (
    <div className="grid grid-cols-4 gap-4 p-4">
      {nodes.map(node => (
        <FileGridItem
          key={node.id}
          node={node}
          isSelected={selectedFiles.includes(node.path)}
          onSelect={(multiSelect) => onSelect(node.path, multiSelect)}
          onDoubleClick={() => onDoubleClick(node.path, node.type)}
          onAction={(action) => onAction(node.path, action)}
        />
      ))}
    </div>
  );
};

const FileGridItem: React.FC<{
  node: FileNode;
  isSelected: boolean;
  onSelect: (multiSelect: boolean) => void;
  onDoubleClick: () => void;
  onAction: (action: string) => void;
}> = ({ node, isSelected, onSelect, onDoubleClick, onAction }) => {
  return (
    <Card
      className={`cursor-pointer transition-colors ${isSelected ? 'ring-2 ring-blue-500' : ''}`}
      onClick={(e) => onSelect(e.ctrlKey || e.metaKey)}
      onDoubleClick={onDoubleClick}
    >
      <CardHeader className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {node.type === 'directory' ? <Folder size={20} /> : <FileIcon size={20} />}
            <span className="text-sm font-medium truncate">{node.name}</span>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onAction('copy')}>Copy</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction('rename')}>Rename</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction('delete')}>Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="p-3 pt-0">
        <div className="text-xs text-gray-500">
          {node.type === 'file' && `${formatBytes(node.size)} • `}
          {formatDistanceToNow(node.modified)} ago
        </div>
      </CardContent>
    </Card>
  );
};

const FileListView: React.FC<{
  nodes: FileNode[];
  selectedFiles: string[];
  onSelect: (path: string, multiSelect?: boolean) => void;
  onDoubleClick: (path: string, type: 'file' | 'directory') => void;
  onAction: (path: string, action: string) => void;
}> = ({ nodes, selectedFiles, onSelect, onDoubleClick, onAction }) => {
  return (
    <div className="space-y-1 p-2">
      {nodes.map(node => (
        <div
          key={node.id}
          className={`flex items-center p-2 rounded hover:bg-gray-100 cursor-pointer ${
            selectedFiles.includes(node.path) ? 'bg-blue-100' : ''
          }`}
          onClick={(e) => onSelect(node.path, e.ctrlKey || e.metaKey)}
          onDoubleClick={() => onDoubleClick(node.path, node.type)}
        >
          <div className="flex items-center space-x-3 flex-1">
            {node.type === 'directory' ? <Folder size={16} /> : <FileIcon size={16} />}
            <span className="text-sm">{node.name}</span>
          </div>
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            {node.type === 'file' && <span>{formatBytes(node.size)}</span>}
            <span>{formatDistanceToNow(node.modified)} ago</span>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onAction('copy')}>Copy</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction('rename')}>Rename</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction('delete')}>Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ))}
    </div>
  );
};

const SearchResultsList: React.FC<{
  results: SearchResult[];
  onResultClick: (path: string) => void;
  onResultSelect: (path: string, multiSelect?: boolean) => void;
}> = ({ results, onResultClick, onResultSelect }) => {
  return (
    <ScrollArea className="h-full">
      <div className="space-y-2 p-4">
        {results.map((result, index) => (
          <div
            key={index}
            className="border rounded p-3 hover:bg-gray-50 cursor-pointer"
            onClick={() => onResultClick(result.entry.path)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {result.entry.type === 'directory' ? <Folder size={16} /> : <FileIcon size={16} />}
                <span className="font-medium">{result.entry.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {Math.round(result.relevance * 100)}%
                </Badge>
              </div>
              <span className="text-xs text-gray-500">{result.entry.path}</span>
            </div>
            {result.matches.length > 0 && (
              <div className="mt-2 space-y-1">
                {result.matches.slice(0, 3).map((match, idx) => (
                  <div key={idx} className="text-xs text-gray-600 bg-gray-100 p-1 rounded">
                    Line {match.line}: {match.context}
                  </div>
                ))}
                {result.matches.length > 3 && (
                  <div className="text-xs text-gray-500">
                    +{result.matches.length - 3} more matches
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </ScrollArea>
  );
};

const FileExplorerSkeleton: React.FC = () => {
  return (
    <div className="space-y-2 p-4">
      {[...Array(10)].map((_, i) => (
        <div key={i} className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse flex-1" />
        </div>
      ))}
    </div>
  );
};

interface FileNode {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  modified: Date;
  created: Date;
  permissions: any;
  isHidden: boolean;
  mimeType?: string;
  children?: FileNode[];
  isExpanded?: boolean;
  isSelected?: boolean;
}

export default FileExplorer;