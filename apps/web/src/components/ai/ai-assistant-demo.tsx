'use client';

import React from 'react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from 'react-resizable-panels';
import { MonacoEditor } from '../monaco-editor';
import { AIAssistantPanel } from './ai-assistant-panel';
import { useEditorStore } from '@/store/editor-store';

export function AIAssistantDemo() {
  const { activeFile, createFile } = useEditorStore();

  React.useEffect(() => {
    // Create a demo file if none exists
    if (!activeFile) {
      createFile(
        'demo.tsx',
        `import React, { useState } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
}

export function UserProfile({ user }: { user: User }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(user.name);

  const handleSave = () => {
    // TODO: Implement save logic
    console.log('Saving user:', { ...user, name: editedName });
    setIsEditing(false);
  };

  return (
    <div className="user-profile">
      <h2>User Profile</h2>
      {isEditing ? (
        <div>
          <input
            value={editedName}
            onChange={(e) => setEditedName(e.target.value)}
            placeholder="Enter name"
          />
          <button onClick={handleSave}>Save</button>
          <button onClick={() => setIsEditing(false)}>Cancel</button>
        </div>
      ) : (
        <div>
          <p>Name: {user.name}</p>
          <p>Email: {user.email}</p>
          <button onClick={() => setIsEditing(true)}>Edit</button>
        </div>
      )}
    </div>
  );
}`
      );
    }
  }, [activeFile, createFile]);

  if (!activeFile) {
    return <div>Loading demo...</div>;
  }

  return (
    <div className="h-screen bg-background">
      <ResizablePanelGroup direction="horizontal" className="h-full">
        {/* Main Editor Panel */}
        <ResizablePanel defaultSize={70} minSize={30}>
          <div className="h-full border-r">
            <div className="h-8 border-b bg-muted/30 flex items-center px-3">
              <span className="text-sm font-medium">Code Editor</span>
            </div>
            <div className="h-[calc(100%-2rem)]">
              <MonacoEditor fileId={activeFile} />
            </div>
          </div>
        </ResizablePanel>

        <ResizableHandle withHandle />

        {/* AI Assistant Panel */}
        <ResizablePanel defaultSize={30} minSize={20} maxSize={50}>
          <div className="h-full">
            <div className="h-8 border-b bg-muted/30 flex items-center px-3">
              <span className="text-sm font-medium">AI Assistant</span>
            </div>
            <div className="h-[calc(100%-2rem)]">
              <AIAssistantPanel />
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}

// Instructions for using the AI Assistant:
/*
To use the AI Development Assistant:

1. **Code Completion**: 
   - Start typing in the editor
   - AI-powered suggestions will appear automatically
   - Use Ctrl+Space to trigger manual completion

2. **AI Suggestions**:
   - Select code in the editor
   - Press Ctrl+Shift+I to get AI improvement suggestions
   - Click "Apply Suggestion" to implement changes

3. **Code Analysis**:
   - Click "Analyze" in the Analysis tab
   - View quality metrics, issues, and recommendations
   - Fix issues based on AI recommendations

4. **Test Generation**:
   - Click "Generate Tests" in the Tests tab
   - Review and customize generated tests
   - Copy test code to create test files

5. **Debug Assistant**:
   - When encountering errors, use the Debug tab
   - Paste error messages for AI analysis
   - Follow suggested solutions and documentation links

6. **Configuration**:
   - Use the Settings tab to configure AI providers
   - Add API keys for OpenAI, Anthropic, etc.
   - Toggle features on/off as needed
   - Monitor service health and performance

Keyboard Shortcuts:
- Ctrl+Shift+I: AI code suggestions
- Ctrl+Shift+A: AI code analysis  
- Ctrl+Shift+T: AI test generation
- Ctrl+S: Save file
- Ctrl+D: Duplicate selection
- Ctrl+/: Toggle comment
- F12: Go to definition
- Shift+F12: Find references
- F2: Rename symbol

Note: To use AI features, you need to:
1. Add API keys in the Settings tab
2. Ensure features are enabled
3. Have an active internet connection
*/