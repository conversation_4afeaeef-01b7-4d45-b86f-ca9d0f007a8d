'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings, 
  Brain, 
  Key, 
  Zap, 
  Shield, 
  Activity,
  AlertCircle,
  CheckCircle,
  Trash2,
  Plus,
} from 'lucide-react';
import { 
  AIConfiguration, 
  AIConfigurationManager, 
  AIProvider,
  defaultAIConfiguration 
} from '@/lib/ai/ai-service-base';
import { aiServiceManager } from '@/lib/ai/ai-service-manager';

interface AIConfigurationProps {
  className?: string;
}

export function AIConfiguration({ className = '' }: AIConfigurationProps) {
  const [config, setConfig] = useState<AIConfiguration>(defaultAIConfiguration);
  const [serviceHealth, setServiceHealth] = useState<any>(null);
  const [newProvider, setNewProvider] = useState<Partial<AIProvider>>({});
  const [isAddingProvider, setIsAddingProvider] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const configManager = AIConfigurationManager.getInstance();

  useEffect(() => {
    loadConfiguration();
    checkServiceHealth();
  }, []);

  const loadConfiguration = () => {
    const currentConfig = configManager.getConfiguration();
    setConfig(currentConfig);
  };

  const checkServiceHealth = async () => {
    try {
      const health = await aiServiceManager.getServiceHealth();
      setServiceHealth(health);
    } catch (error) {
      console.error('Failed to check service health:', error);
    }
  };

  const saveConfiguration = async () => {
    setIsSaving(true);
    try {
      configManager.updateConfiguration(config);
      
      // Reinitialize services with new configuration
      await aiServiceManager.initializeServices();
      
      console.log('Configuration saved successfully');
    } catch (error) {
      console.error('Failed to save configuration:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const updateProvider = (index: number, updates: Partial<AIProvider>) => {
    const newProviders = [...config.providers];
    newProviders[index] = { ...newProviders[index], ...updates };
    setConfig({ ...config, providers: newProviders });
  };

  const removeProvider = (index: number) => {
    const newProviders = config.providers.filter((_, i) => i !== index);
    setConfig({ ...config, providers: newProviders });
  };

  const addProvider = () => {
    if (!newProvider.name) return;
    
    const provider: AIProvider = {
      name: newProvider.name,
      apiKey: newProvider.apiKey || '',
      baseUrl: newProvider.baseUrl,
      model: newProvider.model || 'default',
      maxTokens: newProvider.maxTokens || 4000,
      temperature: newProvider.temperature || 0.1,
    };

    setConfig({
      ...config,
      providers: [...config.providers, provider],
    });

    setNewProvider({});
    setIsAddingProvider(false);
  };

  const toggleFeature = (feature: keyof AIConfiguration['features']) => {
    setConfig({
      ...config,
      features: {
        ...config.features,
        [feature]: !config.features[feature],
      },
    });
  };

  const updateCacheConfig = (field: keyof AIConfiguration['cacheConfig'], value: number) => {
    setConfig({
      ...config,
      cacheConfig: {
        ...config.cacheConfig,
        [field]: value,
      },
    });
  };

  const updateRequestLimits = (field: keyof AIConfiguration['requestLimits'], value: number) => {
    setConfig({
      ...config,
      requestLimits: {
        ...config.requestLimits,
        [field]: value,
      },
    });
  };

  const clearCache = () => {
    aiServiceManager.clearCache();
    checkServiceHealth();
  };

  const getProviderStatusBadge = (providerName: string) => {
    if (!serviceHealth || !serviceHealth[providerName]) {
      return <Badge variant="secondary">Unknown</Badge>;
    }

    const health = serviceHealth[providerName];
    if (health.available) {
      return (
        <div className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3 text-green-500" />
          <Badge variant="default">Active</Badge>
          {health.responseTime && (
            <span className="text-xs text-muted-foreground">{health.responseTime}ms</span>
          )}
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3 text-red-500" />
          <Badge variant="destructive">Error</Badge>
        </div>
      );
    }
  };

  const renderProvidersTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">AI Providers</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsAddingProvider(true)}
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Provider
        </Button>
      </div>

      {config.providers.map((provider, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="text-sm capitalize">{provider.name}</CardTitle>
                {getProviderStatusBadge(provider.name)}
              </div>
              <div className="flex items-center gap-2">
                {config.defaultProvider === provider.name && (
                  <Badge variant="outline">Default</Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeProvider(index)}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor={`model-${index}`} className="text-xs">Model</Label>
              <Input
                id={`model-${index}`}
                value={provider.model || ''}
                onChange={(e) => updateProvider(index, { model: e.target.value })}
                placeholder="Model name"
                className="h-8"
              />
            </div>
            <div>
              <Label htmlFor={`maxTokens-${index}`} className="text-xs">Max Tokens</Label>
              <Input
                id={`maxTokens-${index}`}
                type="number"
                value={provider.maxTokens || 4000}
                onChange={(e) => updateProvider(index, { maxTokens: Number(e.target.value) })}
                className="h-8"
              />
            </div>
            <div>
              <Label htmlFor={`temperature-${index}`} className="text-xs">Temperature</Label>
              <Input
                id={`temperature-${index}`}
                type="number"
                step="0.1"
                min="0"
                max="2"
                value={provider.temperature || 0.1}
                onChange={(e) => updateProvider(index, { temperature: Number(e.target.value) })}
                className="h-8"
              />
            </div>
            <div>
              <Label htmlFor={`baseUrl-${index}`} className="text-xs">Base URL (Optional)</Label>
              <Input
                id={`baseUrl-${index}`}
                value={provider.baseUrl || ''}
                onChange={(e) => updateProvider(index, { baseUrl: e.target.value })}
                placeholder="Custom API URL"
                className="h-8"
              />
            </div>
            <div className="col-span-2">
              <Label htmlFor={`apiKey-${index}`} className="text-xs">API Key</Label>
              <Input
                id={`apiKey-${index}`}
                type="password"
                value={provider.apiKey || ''}
                onChange={(e) => updateProvider(index, { apiKey: e.target.value })}
                placeholder="Enter API key"
                className="h-8"
              />
            </div>
            <div className="col-span-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfig({ ...config, defaultProvider: provider.name })}
                disabled={config.defaultProvider === provider.name}
                className="w-full"
              >
                {config.defaultProvider === provider.name ? 'Default Provider' : 'Set as Default'}
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}

      {isAddingProvider && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Add New Provider</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor="new-name" className="text-xs">Provider Name</Label>
              <Input
                id="new-name"
                value={newProvider.name || ''}
                onChange={(e) => setNewProvider({ ...newProvider, name: e.target.value })}
                placeholder="openai, anthropic, etc."
                className="h-8"
              />
            </div>
            <div>
              <Label htmlFor="new-model" className="text-xs">Model</Label>
              <Input
                id="new-model"
                value={newProvider.model || ''}
                onChange={(e) => setNewProvider({ ...newProvider, model: e.target.value })}
                placeholder="Model name"
                className="h-8"
              />
            </div>
            <div className="col-span-2">
              <Label htmlFor="new-apiKey" className="text-xs">API Key</Label>
              <Input
                id="new-apiKey"
                type="password"
                value={newProvider.apiKey || ''}
                onChange={(e) => setNewProvider({ ...newProvider, apiKey: e.target.value })}
                placeholder="Enter API key"
                className="h-8"
              />
            </div>
            <div className="col-span-2 flex gap-2">
              <Button
                size="sm"
                onClick={addProvider}
                disabled={!newProvider.name}
                className="flex-1"
              >
                Add Provider
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsAddingProvider(false);
                  setNewProvider({});
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderFeaturesTab = () => (
    <div className="space-y-4">
      <h3 className="font-semibold">AI Features</h3>
      
      <div className="space-y-3">
        {Object.entries(config.features).map(([feature, enabled]) => (
          <Card key={feature}>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium capitalize">
                    {feature.replace(/([A-Z])/g, ' $1').trim()}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {getFeatureDescription(feature)}
                  </div>
                </div>
                <Switch
                  checked={enabled}
                  onCheckedChange={() => toggleFeature(feature as keyof AIConfiguration['features'])}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderPerformanceTab = () => (
    <div className="space-y-4">
      <h3 className="font-semibold">Performance Settings</h3>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Cache Configuration</CardTitle>
          <CardDescription>Configure caching to improve response times</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <Label>Enable Caching</Label>
            <Switch
              checked={config.enableCaching}
              onCheckedChange={(checked) => setConfig({ ...config, enableCaching: checked })}
            />
          </div>
          
          <div>
            <Label htmlFor="cache-size" className="text-xs">Max Cache Size</Label>
            <Input
              id="cache-size"
              type="number"
              value={config.cacheConfig.maxSize}
              onChange={(e) => updateCacheConfig('maxSize', Number(e.target.value))}
              className="h-8"
            />
          </div>
          
          <div>
            <Label htmlFor="cache-ttl" className="text-xs">Cache TTL (minutes)</Label>
            <Input
              id="cache-ttl"
              type="number"
              value={Math.round(config.cacheConfig.ttl / 60000)}
              onChange={(e) => updateCacheConfig('ttl', Number(e.target.value) * 60000)}
              className="h-8"
            />
          </div>

          <Button variant="outline" onClick={clearCache} className="w-full">
            <Trash2 className="h-4 w-4 mr-1" />
            Clear Cache
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Request Limits</CardTitle>
          <CardDescription>Configure rate limiting and token limits</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <Label htmlFor="rate-limit" className="text-xs">Max Requests per Minute</Label>
            <Input
              id="rate-limit"
              type="number"
              value={config.requestLimits.maxRequestsPerMinute}
              onChange={(e) => updateRequestLimits('maxRequestsPerMinute', Number(e.target.value))}
              className="h-8"
            />
          </div>
          
          <div>
            <Label htmlFor="token-limit" className="text-xs">Max Tokens per Request</Label>
            <Input
              id="token-limit"
              type="number"
              value={config.requestLimits.maxTokensPerRequest}
              onChange={(e) => updateRequestLimits('maxTokensPerRequest', Number(e.target.value))}
              className="h-8"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderHealthTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">Service Health</h3>
        <Button variant="outline" size="sm" onClick={checkServiceHealth}>
          <Activity className="h-4 w-4 mr-1" />
          Refresh
        </Button>
      </div>

      {serviceHealth ? (
        <div className="space-y-3">
          {Object.entries(serviceHealth).map(([service, health]: [string, any]) => (
            <Card key={service}>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium capitalize">{service}</div>
                    <div className="text-sm text-muted-foreground">
                      {health.available ? 'Service is operational' : 'Service unavailable'}
                      {health.responseTime && ` • ${health.responseTime}ms response time`}
                    </div>
                    {health.lastError && (
                      <div className="text-xs text-destructive mt-1">
                        Error: {health.lastError}
                      </div>
                    )}
                    {health.cacheStats && (
                      <div className="text-xs text-muted-foreground mt-1">
                        Cache: {health.cacheStats.size} items
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {health.available ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="pt-4">
            <div className="text-center text-muted-foreground">
              <Activity className="h-8 w-8 mx-auto mb-2" />
              <p>No health data available</p>
              <p className="text-sm">Click refresh to check service status</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const getFeatureDescription = (feature: string): string => {
    const descriptions: Record<string, string> = {
      codeCompletion: 'AI-powered code completion and suggestions',
      codeSuggestions: 'Advanced code improvement recommendations', 
      codeAnalysis: 'Automated code analysis and review',
      debugging: 'AI-powered debugging assistance',
      testGeneration: 'Automated test generation',
    };
    return descriptions[feature] || 'AI feature';
  };

  return (
    <Card className={`h-full ${className}`}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          <CardTitle>AI Configuration</CardTitle>
        </div>
        <CardDescription>
          Configure AI providers and features
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="providers" className="h-full">
          <TabsList className="grid w-full grid-cols-4 px-3">
            <TabsTrigger value="providers" className="text-xs">
              <Key className="h-3 w-3 mr-1" />
              Providers
            </TabsTrigger>
            <TabsTrigger value="features" className="text-xs">
              <Zap className="h-3 w-3 mr-1" />
              Features
            </TabsTrigger>
            <TabsTrigger value="performance" className="text-xs">
              <Settings className="h-3 w-3 mr-1" />
              Performance
            </TabsTrigger>
            <TabsTrigger value="health" className="text-xs">
              <Activity className="h-3 w-3 mr-1" />
              Health
            </TabsTrigger>
          </TabsList>
          
          <div className="h-[calc(100%-3rem)] overflow-auto">
            <div className="p-3">
              <TabsContent value="providers" className="mt-0">
                {renderProvidersTab()}
              </TabsContent>
              <TabsContent value="features" className="mt-0">
                {renderFeaturesTab()}
              </TabsContent>
              <TabsContent value="performance" className="mt-0">
                {renderPerformanceTab()}
              </TabsContent>
              <TabsContent value="health" className="mt-0">
                {renderHealthTab()}
              </TabsContent>
            </div>
          </div>
        </Tabs>
        
        <div className="p-3 border-t">
          <Button 
            onClick={saveConfiguration} 
            disabled={isSaving}
            className="w-full"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Configuration'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}