'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, 
  Code, 
  Bug, 
  TestTube, 
  Lightbulb, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Info,
  Loader2,
  Sparkles,
  Settings,
} from 'lucide-react';
import { aiServiceManager } from '@/lib/ai/ai-service-manager';
import { 
  CompletionItem, 
  CodeSuggestion, 
  AnalysisResult, 
  DebugAnalysis, 
  TestGeneration 
} from '@/lib/ai/ai-service-base';
import { useEditorStore } from '@/store/editor-store';

interface AIAssistantPanelProps {
  className?: string;
}

export function AIAssistantPanel({ className = '' }: AIAssistantPanelProps) {
  const [activeTab, setActiveTab] = useState('suggestions');
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<CodeSuggestion[]>([]);
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);
  const [debugInfo, setDebugInfo] = useState<DebugAnalysis | null>(null);
  const [testGeneration, setTestGeneration] = useState<TestGeneration | null>(null);
  const [serviceHealth, setServiceHealth] = useState<any>(null);

  const { activeFile, getFileById, getEditor } = useEditorStore();
  const currentFile = activeFile ? getFileById(activeFile) : null;

  // Auto-refresh AI insights when file changes
  useEffect(() => {
    if (currentFile && currentFile.content) {
      refreshAIInsights();
    }
  }, [currentFile?.content, currentFile?.language]);

  const refreshAIInsights = useCallback(async () => {
    if (!currentFile) return;

    setIsLoading(true);
    try {
      // Get code suggestions
      const suggestionsResult = await aiServiceManager.getIntelligentSuggestions(
        currentFile.content,
        '',
        currentFile.language,
        currentFile.name
      );
      setSuggestions(suggestionsResult);

      // Perform code analysis
      const analysisResult = await aiServiceManager.performCodeAnalysis(
        currentFile.content,
        currentFile.language,
        currentFile.name
      );
      setAnalysis(analysisResult);
    } catch (error) {
      console.error('Failed to refresh AI insights:', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentFile]);

  const applySuggestion = async (suggestion: CodeSuggestion) => {
    if (!currentFile || !activeFile) return;

    const editor = getEditor(activeFile);
    if (!editor) return;

    // Apply code changes
    const edits = suggestion.codeChanges.map(change => ({
      range: {
        startLineNumber: change.startLine,
        startColumn: 1,
        endLineNumber: change.endLine,
        endColumn: Number.MAX_SAFE_INTEGER,
      },
      text: change.suggestedCode,
    }));

    editor.executeEdits('ai-suggestion', edits);
  };

  const generateTests = async () => {
    if (!currentFile) return;

    setIsLoading(true);
    try {
      const testResult = await aiServiceManager.createTests(
        currentFile.content,
        currentFile.language,
        currentFile.name
      );
      setTestGeneration(testResult);
      setActiveTab('tests');
    } catch (error) {
      console.error('Failed to generate tests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const analyzeError = async (errorMessage: string) => {
    if (!currentFile) return;

    setIsLoading(true);
    try {
      const debugResult = await aiServiceManager.getDebugHelp(
        errorMessage,
        currentFile.content,
        currentFile.language,
        currentFile.name
      );
      setDebugInfo(debugResult);
      setActiveTab('debug');
    } catch (error) {
      console.error('Failed to analyze error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkServiceHealth = async () => {
    try {
      const health = await aiServiceManager.getServiceHealth();
      setServiceHealth(health);
    } catch (error) {
      console.error('Failed to check service health:', error);
    }
  };

  useEffect(() => {
    checkServiceHealth();
  }, []);

  const renderSuggestions = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">Code Suggestions</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={refreshAIInsights}
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Sparkles className="h-4 w-4" />}
          Refresh
        </Button>
      </div>

      {suggestions.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Lightbulb className="h-8 w-8 mx-auto mb-2" />
          <p>No suggestions available</p>
          <p className="text-sm">Open a file to get AI-powered suggestions</p>
        </div>
      ) : (
        <div className="space-y-3">
          {suggestions.map((suggestion, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">{suggestion.title}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant={getBadgeVariant(suggestion.type)}>
                      {suggestion.type}
                    </Badge>
                    <Badge variant="outline">
                      {Math.round(suggestion.confidence * 100)}%
                    </Badge>
                  </div>
                </div>
                <CardDescription className="text-xs">
                  {suggestion.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  {suggestion.codeChanges.map((change, changeIndex) => (
                    <div key={changeIndex} className="text-xs bg-muted p-2 rounded">
                      <div className="font-mono text-destructive line-through mb-1">
                        {change.originalCode}
                      </div>
                      <div className="font-mono text-green-600">
                        {change.suggestedCode}
                      </div>
                      <div className="text-muted-foreground mt-1">
                        {change.explanation}
                      </div>
                    </div>
                  ))}
                </div>
                <Button
                  size="sm"
                  className="mt-2"
                  onClick={() => applySuggestion(suggestion)}
                >
                  Apply Suggestion
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );

  const renderAnalysis = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">Code Analysis</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={refreshAIInsights}
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Code className="h-4 w-4" />}
          Analyze
        </Button>
      </div>

      {!analysis ? (
        <div className="text-center py-8 text-muted-foreground">
          <Code className="h-8 w-8 mx-auto mb-2" />
          <p>No analysis available</p>
          <p className="text-sm">Run analysis to get insights</p>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Metrics */}
          {analysis.metrics && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Quality Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  {Object.entries(analysis.metrics).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="capitalize">{key}:</span>
                      <Badge variant={getMetricBadgeVariant(value as number)}>
                        {typeof value === 'number' ? `${Math.round(value)}%` : value}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Issues */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Issues Found</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {analysis.issues.length === 0 ? (
                  <div className="text-center py-4 text-muted-foreground">
                    <CheckCircle className="h-6 w-6 mx-auto mb-1 text-green-500" />
                    <p className="text-sm">No issues found</p>
                  </div>
                ) : (
                  analysis.issues.map((issue, index) => (
                    <div key={index} className="flex items-start gap-2 text-sm p-2 rounded border">
                      {getIssueIcon(issue.severity)}
                      <div className="flex-1">
                        <div className="font-medium">{issue.message}</div>
                        <div className="text-muted-foreground">
                          Line {issue.line}:{issue.column}
                          {issue.rule && <span className="ml-2">({issue.rule})</span>}
                        </div>
                        {issue.suggestions && issue.suggestions.length > 0 && (
                          <div className="mt-1">
                            <div className="text-xs text-muted-foreground">Suggestions:</div>
                            <ul className="text-xs list-disc list-inside">
                              {issue.suggestions.map((suggestion, i) => (
                                <li key={i}>{suggestion}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                      <Badge variant={getBadgeVariant(issue.severity)}>
                        {issue.severity}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          {analysis.recommendations && analysis.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm">
                  {analysis.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Lightbulb className="h-4 w-4 mt-0.5 text-yellow-500 flex-shrink-0" />
                      {rec}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );

  const renderTests = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">Test Generation</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={generateTests}
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <TestTube className="h-4 w-4" />}
          Generate Tests
        </Button>
      </div>

      {!testGeneration ? (
        <div className="text-center py-8 text-muted-foreground">
          <TestTube className="h-8 w-8 mx-auto mb-2" />
          <p>No tests generated</p>
          <p className="text-sm">Click "Generate Tests" to create tests</p>
        </div>
      ) : (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Generated Tests</CardTitle>
              <CardDescription>
                Framework: {testGeneration.testFramework}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-sm">{testGeneration.description}</div>
                
                <div className="bg-muted p-3 rounded font-mono text-xs overflow-auto max-h-60">
                  <pre>{testGeneration.testCode}</pre>
                </div>

                {testGeneration.coverage && (
                  <div className="grid grid-cols-1 gap-2 text-xs">
                    <div>
                      <span className="font-medium">Functions:</span> {testGeneration.coverage.functions.join(', ')}
                    </div>
                    <div>
                      <span className="font-medium">Edge Cases:</span> {testGeneration.coverage.edgeCases.join(', ')}
                    </div>
                  </div>
                )}

                {testGeneration.dependencies && testGeneration.dependencies.length > 0 && (
                  <div className="text-xs">
                    <span className="font-medium">Dependencies:</span> {testGeneration.dependencies.join(', ')}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );

  const renderDebug = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">Debug Assistant</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => analyzeError('Sample error for testing')}
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Bug className="h-4 w-4" />}
          Analyze Error
        </Button>
      </div>

      {!debugInfo ? (
        <div className="text-center py-8 text-muted-foreground">
          <Bug className="h-8 w-8 mx-auto mb-2" />
          <p>No debug analysis</p>
          <p className="text-sm">Paste an error to get help</p>
        </div>
      ) : (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">{debugInfo.errorType}</CardTitle>
              <CardDescription>{debugInfo.errorMessage}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-sm mb-2">Possible Causes:</h4>
                  <ul className="text-sm space-y-1">
                    {debugInfo.possibleCauses.map((cause, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0" />
                        {cause}
                      </li>
                    ))}
                  </ul>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium text-sm mb-2">Solutions:</h4>
                  <div className="space-y-2">
                    {debugInfo.solutions.map((solution, index) => (
                      <Card key={index} className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{solution.title}</span>
                          <Badge variant={solution.priority === 'high' ? 'destructive' : 'secondary'}>
                            {solution.priority}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{solution.description}</p>
                        {solution.codeExample && (
                          <div className="bg-muted p-2 rounded font-mono text-xs">
                            <pre>{solution.codeExample}</pre>
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>

                {debugInfo.relatedDocs && debugInfo.relatedDocs.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium text-sm mb-2">Related Documentation:</h4>
                      <div className="space-y-1">
                        {debugInfo.relatedDocs.map((doc, index) => (
                          <a
                            key={index}
                            href={doc.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block text-sm text-blue-600 hover:underline"
                          >
                            {doc.title}
                          </a>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-4">
      <h3 className="font-semibold">AI Assistant Settings</h3>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Service Health</CardTitle>
        </CardHeader>
        <CardContent>
          {serviceHealth ? (
            <div className="space-y-2">
              {Object.entries(serviceHealth).map(([service, health]: [string, any]) => (
                <div key={service} className="flex items-center justify-between text-sm">
                  <span className="capitalize">{service}</span>
                  <div className="flex items-center gap-2">
                    {health.available ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    {health.responseTime && (
                      <span className="text-muted-foreground">{health.responseTime}ms</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <Button variant="outline" onClick={checkServiceHealth}>
                Check Health
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Button
        variant="outline"
        onClick={() => aiServiceManager.clearCache()}
        className="w-full"
      >
        Clear Cache
      </Button>
    </div>
  );

  const getBadgeVariant = (type: string) => {
    switch (type) {
      case 'critical':
      case 'error':
      case 'fix':
        return 'destructive';
      case 'warning':
      case 'security':
        return 'secondary';
      case 'info':
      case 'improve':
        return 'outline';
      default:
        return 'default';
    }
  };

  const getMetricBadgeVariant = (value: number) => {
    if (value >= 80) return 'default';
    if (value >= 60) return 'secondary';
    return 'destructive';
  };

  const getIssueIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'high':
        return <XCircle className="h-4 w-4 text-red-500 flex-shrink-0" />;
      case 'medium':
        return <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0" />;
      case 'low':
      default:
        return <Info className="h-4 w-4 text-blue-500 flex-shrink-0" />;
    }
  };

  return (
    <Card className={`h-full ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          <CardTitle className="text-lg">AI Assistant</CardTitle>
        </div>
        <CardDescription>
          AI-powered development assistance
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-5 px-3">
            <TabsTrigger value="suggestions" className="text-xs">
              <Lightbulb className="h-3 w-3 mr-1" />
              Tips
            </TabsTrigger>
            <TabsTrigger value="analysis" className="text-xs">
              <Code className="h-3 w-3 mr-1" />
              Analysis
            </TabsTrigger>
            <TabsTrigger value="tests" className="text-xs">
              <TestTube className="h-3 w-3 mr-1" />
              Tests
            </TabsTrigger>
            <TabsTrigger value="debug" className="text-xs">
              <Bug className="h-3 w-3 mr-1" />
              Debug
            </TabsTrigger>
            <TabsTrigger value="settings" className="text-xs">
              <Settings className="h-3 w-3 mr-1" />
              Settings
            </TabsTrigger>
          </TabsList>
          
          <div className="h-[calc(100%-3rem)]">
            <ScrollArea className="h-full">
              <div className="p-3">
                <TabsContent value="suggestions" className="mt-0">
                  {renderSuggestions()}
                </TabsContent>
                <TabsContent value="analysis" className="mt-0">
                  {renderAnalysis()}
                </TabsContent>
                <TabsContent value="tests" className="mt-0">
                  {renderTests()}
                </TabsContent>
                <TabsContent value="debug" className="mt-0">
                  {renderDebug()}
                </TabsContent>
                <TabsContent value="settings" className="mt-0">
                  {renderSettings()}
                </TabsContent>
              </div>
            </ScrollArea>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}