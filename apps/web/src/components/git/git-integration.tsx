'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { WebGitService, GitStatus } from '@/lib/git/git-service';
import { GitPanel } from './git-panel';
import { GitDiffViewer } from './git-diff-viewer';
import { GitBranchManager } from './git-branch-manager';
import { GitRepositoryManager } from './git-repository-manager';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  GitBranch,
  GitCommit,
  GitPullRequest,
  FolderGit2,
  <PERSON><PERSON><PERSON>,
  Diff,
  Refresh<PERSON>w,
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  Info
} from 'lucide-react';

interface GitIntegrationProps {
  workspacePath: string;
  onFileOpen?: (path: string) => void;
  onStatusChange?: (status: GitStatus | null) => void;
  className?: string;
}

export const GitIntegration: React.FC<GitIntegrationProps> = ({
  workspacePath,
  onFileOpen,
  onStatusChange,
  className = ''
}) => {
  const [gitService] = useState(() => new WebGitService());
  const [gitStatus, setGitStatus] = useState<GitStatus | null>(null);
  const [hasRepository, setHasRepository] = useState(false);
  const [activeTab, setActiveTab] = useState('changes');
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [showDiffViewer, setShowDiffViewer] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeGit();
  }, [workspacePath]);

  const initializeGit = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      gitService.setWorkdir(workspacePath);
      await checkRepositoryStatus();
    } catch (error: any) {
      setError(error.message);
      setHasRepository(false);
      setGitStatus(null);
    } finally {
      setIsLoading(false);
    }
  };

  const checkRepositoryStatus = async () => {
    try {
      const status = await gitService.getStatus();
      setGitStatus(status);
      setHasRepository(true);
      
      if (onStatusChange) {
        onStatusChange(status);
      }
    } catch (error) {
      setGitStatus(null);
      setHasRepository(false);
      
      if (onStatusChange) {
        onStatusChange(null);
      }
    }
  };

  const handleStatusChange = (status: GitStatus) => {
    setGitStatus(status);
    if (onStatusChange) {
      onStatusChange(status);
    }
  };

  const handleRepositoryChange = (hasRepo: boolean) => {
    setHasRepository(hasRepo);
    if (hasRepo) {
      checkRepositoryStatus();
    } else {
      setGitStatus(null);
      if (onStatusChange) {
        onStatusChange(null);
      }
    }
  };

  const handleViewDiff = (file: string) => {
    setSelectedFile(file);
    setShowDiffViewer(true);
  };

  const handleFileOpen = (path: string) => {
    if (onFileOpen) {
      onFileOpen(path);
    }
  };

  const getTabBadge = (tab: string) => {
    if (!gitStatus) return null;

    switch (tab) {
      case 'changes':
        const totalChanges = gitStatus.staged.length + gitStatus.unstaged.length + gitStatus.untracked.length;
        return totalChanges > 0 ? totalChanges : null;
      case 'branches':
        return null; // Could show number of branches
      case 'history':
        return null; // Could show number of commits
      default:
        return null;
    }
  };

  const renderTabWithBadge = (value: string, label: string, icon: React.ReactNode) => {
    const badge = getTabBadge(value);
    return (
      <TabsTrigger value={value} className="flex items-center space-x-2">
        {icon}
        <span>{label}</span>
        {badge && <Badge variant="secondary" className="ml-1 text-xs">{badge}</Badge>}
      </TabsTrigger>
    );
  };

  if (isLoading) {
    return (
      <div className={`git-integration h-full flex items-center justify-center ${className}`}>
        <div className="text-center">
          <RefreshCw size={48} className="mx-auto mb-4 animate-spin opacity-50" />
          <p>Initializing Git integration...</p>
        </div>
      </div>
    );
  }

  if (!hasRepository) {
    return (
      <div className={`git-integration h-full flex flex-col ${className}`}>
        <GitRepositoryManager
          gitService={gitService}
          workspacePath={workspacePath}
          onRepositoryChange={handleRepositoryChange}
        />
      </div>
    );
  }

  return (
    <div className={`git-integration h-full flex flex-col ${className}`}>
      <GitIntegrationHeader
        status={gitStatus}
        onRefresh={checkRepositoryStatus}
        onManageRepository={() => setActiveTab('repository')}
        error={error}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4">
          {renderTabWithBadge('changes', 'Changes', <GitCommit size={16} />)}
          {renderTabWithBadge('branches', 'Branches', <GitBranch size={16} />)}
          {renderTabWithBadge('history', 'History', <GitPullRequest size={16} />)}
          {renderTabWithBadge('repository', 'Repository', <FolderGit2 size={16} />)}
        </TabsList>

        <TabsContent value="changes" className="flex-1">
          <GitPanel
            gitService={gitService}
            workspacePath={workspacePath}
            onStatusChange={handleStatusChange}
          />
        </TabsContent>

        <TabsContent value="branches" className="flex-1">
          <GitBranchManager
            gitService={gitService}
            currentBranch={gitStatus?.branch}
            onBranchChange={() => checkRepositoryStatus()}
          />
        </TabsContent>

        <TabsContent value="history" className="flex-1">
          <GitPanel
            gitService={gitService}
            workspacePath={workspacePath}
            onStatusChange={handleStatusChange}
          />
        </TabsContent>

        <TabsContent value="repository" className="flex-1">
          <GitRepositoryManager
            gitService={gitService}
            workspacePath={workspacePath}
            onRepositoryChange={handleRepositoryChange}
          />
        </TabsContent>
      </Tabs>

      {/* Diff Viewer Dialog */}
      <Dialog open={showDiffViewer} onOpenChange={setShowDiffViewer}>
        <DialogContent className="max-w-6xl h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Diff size={20} />
              <span>File Diff</span>
            </DialogTitle>
          </DialogHeader>
          {selectedFile && (
            <GitDiffViewer
              file={selectedFile}
              gitService={gitService}
              onClose={() => setShowDiffViewer(false)}
              className="flex-1"
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

const GitIntegrationHeader: React.FC<{
  status: GitStatus | null;
  onRefresh: () => void;
  onManageRepository: () => void;
  error: string | null;
}> = ({ status, onRefresh, onManageRepository, error }) => {
  const getStatusSummary = () => {
    if (!status) return 'No repository';
    
    const changes = status.staged.length + status.unstaged.length + status.untracked.length;
    if (changes === 0) return 'Working tree clean';
    
    return `${changes} change${changes > 1 ? 's' : ''}`;
  };

  const getStatusIcon = () => {
    if (!status) return <AlertTriangle size={16} className="text-orange-500" />;
    
    const hasChanges = status.staged.length + status.unstaged.length + status.untracked.length > 0;
    const hasConflicts = status.conflicted.length > 0;
    
    if (hasConflicts) return <AlertTriangle size={16} className="text-red-500" />;
    if (hasChanges) return <Info size={16} className="text-blue-500" />;
    return <CheckCircle size={16} className="text-green-500" />;
  };

  return (
    <div className="border-b p-3">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <FolderGit2 size={20} />
          <span className="font-semibold">Git</span>
          {status && (
            <Badge variant="outline" className="text-xs">
              {status.branch}
            </Badge>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw size={16} />
          </Button>
          <Button variant="outline" size="sm" onClick={onManageRepository}>
            <Settings size={16} />
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2 text-sm">
        {getStatusIcon()}
        <span>{getStatusSummary()}</span>
        
        {status && (status.ahead > 0 || status.behind > 0) && (
          <div className="flex items-center space-x-2 ml-auto">
            {status.ahead > 0 && (
              <Badge variant="outline" className="text-green-600 border-green-200">
                ↑{status.ahead}
              </Badge>
            )}
            {status.behind > 0 && (
              <Badge variant="outline" className="text-orange-600 border-orange-200">
                ↓{status.behind}
              </Badge>
            )}
          </div>
        )}
      </div>

      {error && (
        <div className="flex items-center space-x-2 text-red-600 text-sm mt-2">
          <AlertTriangle size={14} />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
};

export default GitIntegration;