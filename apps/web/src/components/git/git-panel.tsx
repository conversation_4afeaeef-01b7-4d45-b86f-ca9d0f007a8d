'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { GitStatus, WebGitService, GitCommit, GitBranch } from '@/lib/git/git-service';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  GitBranch as GitBranchIcon,
  GitCommit as GitCommitIcon,
  GitPullRequest,
  Plus,
  Download,
  Upload,
  RefreshCw,
  MoreVertical,
  Check,
  X,
  FileText,
  FolderOpen,
  Clock,
  User,
  Mail,
  Hash,
  ArrowUp,
  ArrowDown,
  GitMerge,
  Settings,
  AlertCircle,
  CheckCircle,
  Minus,
  Copy
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface GitPanelProps {
  gitService: WebGitService;
  workspacePath: string;
  onStatusChange?: (status: GitStatus) => void;
  className?: string;
}

export const GitPanel: React.FC<GitPanelProps> = ({
  gitService,
  workspacePath,
  onStatusChange,
  className = ''
}) => {
  const [gitStatus, setGitStatus] = useState<GitStatus | null>(null);
  const [branches, setBranches] = useState<GitBranch[]>([]);
  const [commits, setCommits] = useState<GitCommit[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [commitMessage, setCommitMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('changes');

  useEffect(() => {
    gitService.setWorkdir(workspacePath);
    loadGitData();

    // Set up event listeners
    const handleGitEvent = () => loadGitData();
    gitService.on('filesStaged', handleGitEvent);
    gitService.on('filesUnstaged', handleGitEvent);
    gitService.on('committed', handleGitEvent);
    gitService.on('branchSwitched', handleGitEvent);

    return () => {
      gitService.off('filesStaged', handleGitEvent);
      gitService.off('filesUnstaged', handleGitEvent);
      gitService.off('committed', handleGitEvent);
      gitService.off('branchSwitched', handleGitEvent);
    };
  }, [workspacePath, gitService]);

  const loadGitData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const [status, branchList, commitHistory] = await Promise.all([
        gitService.getStatus(),
        gitService.getBranches(),
        gitService.getCommitHistory({ maxCount: 50 })
      ]);
      
      setGitStatus(status);
      setBranches(branchList);
      setCommits(commitHistory);
      
      if (onStatusChange) {
        onStatusChange(status);
      }
    } catch (error: any) {
      setError(error.message);
      console.error('Failed to load Git data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [gitService, onStatusChange]);

  const handleStageFiles = async (files: string[]) => {
    setIsLoading(true);
    try {
      await gitService.stageFiles(files);
      await loadGitData();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnstageFiles = async (files: string[]) => {
    setIsLoading(true);
    try {
      await gitService.unstageFiles(files);
      await loadGitData();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCommit = async () => {
    if (!commitMessage.trim()) return;
    
    setIsLoading(true);
    try {
      await gitService.commit(commitMessage);
      setCommitMessage('');
      await loadGitData();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePush = async () => {
    if (!gitStatus?.remoteTrackingBranch) return;
    
    setIsLoading(true);
    try {
      const [remote] = gitStatus.remoteTrackingBranch.split('/');
      await gitService.push(remote, gitStatus.branch);
      await loadGitData();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePull = async () => {
    if (!gitStatus?.remoteTrackingBranch) return;
    
    setIsLoading(true);
    try {
      const [remote, branch] = gitStatus.remoteTrackingBranch.split('/');
      await gitService.pull(remote, branch);
      await loadGitData();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBranchSwitch = async (branchName: string) => {
    setIsLoading(true);
    try {
      await gitService.switchBranch(branchName);
      await loadGitData();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBranchCreate = async (branchName: string) => {
    setIsLoading(true);
    try {
      await gitService.createBranch(branchName);
      await loadGitData();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`git-panel h-full flex flex-col ${className}`}>
      <GitPanelHeader
        status={gitStatus}
        onRefresh={loadGitData}
        onPush={handlePush}
        onPull={handlePull}
        isLoading={isLoading}
        error={error}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="changes">Changes</TabsTrigger>
          <TabsTrigger value="branches">Branches</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="changes" className="flex-1 flex flex-col">
          <GitChanges
            status={gitStatus}
            selectedFiles={selectedFiles}
            onFileSelect={setSelectedFiles}
            onStage={handleStageFiles}
            onUnstage={handleUnstageFiles}
            isLoading={isLoading}
          />
          
          <GitCommitSection
            stagedFiles={gitStatus?.staged || []}
            commitMessage={commitMessage}
            onCommitMessageChange={setCommitMessage}
            onCommit={handleCommit}
            isLoading={isLoading}
            disabled={!gitStatus?.staged.length}
          />
        </TabsContent>

        <TabsContent value="branches" className="flex-1">
          <GitBranches
            branches={branches}
            currentBranch={gitStatus?.branch}
            onBranchSwitch={handleBranchSwitch}
            onBranchCreate={handleBranchCreate}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="history" className="flex-1">
          <GitHistory
            commits={commits}
            currentBranch={gitStatus?.branch}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

const GitPanelHeader: React.FC<{
  status: GitStatus | null;
  onRefresh: () => void;
  onPush: () => void;
  onPull: () => void;
  isLoading: boolean;
  error: string | null;
}> = ({ status, onRefresh, onPush, onPull, isLoading, error }) => {
  return (
    <div className="border-b p-3">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <GitBranchIcon size={16} />
          <span className="font-medium">{status?.branch || 'No Repository'}</span>
          {status && (
            <div className="flex items-center space-x-1">
              {status.ahead > 0 && (
                <Badge variant="outline" className="text-xs">
                  <ArrowUp size={12} className="mr-1" />
                  {status.ahead}
                </Badge>
              )}
              {status.behind > 0 && (
                <Badge variant="outline" className="text-xs">
                  <ArrowDown size={12} className="mr-1" />
                  {status.behind}
                </Badge>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-1">
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
          </Button>
          
          {status?.remoteTrackingBranch && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={onPull}
                disabled={isLoading || status.behind === 0}
              >
                <Download size={16} className="mr-1" />
                Pull
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={onPush}
                disabled={isLoading || status.ahead === 0}
              >
                <Upload size={16} className="mr-1" />
                Push
              </Button>
            </>
          )}
        </div>
      </div>

      {error && (
        <div className="flex items-center space-x-2 text-red-600 text-sm">
          <AlertCircle size={14} />
          <span>{error}</span>
        </div>
      )}

      {status && (
        <div className="text-sm text-gray-600">
          {status.staged.length} staged, {status.unstaged.length} unstaged, {status.untracked.length} untracked
        </div>
      )}
    </div>
  );
};

const GitChanges: React.FC<{
  status: GitStatus | null;
  selectedFiles: string[];
  onFileSelect: (files: string[]) => void;
  onStage: (files: string[]) => void;
  onUnstage: (files: string[]) => void;
  isLoading: boolean;
}> = ({ status, selectedFiles, onFileSelect, onStage, onUnstage, isLoading }) => {
  if (!status) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <FolderOpen size={48} className="mx-auto mb-4 opacity-50" />
          <p>No Git repository found</p>
        </div>
      </div>
    );
  }

  const hasChanges = status.staged.length > 0 || status.unstaged.length > 0 || status.untracked.length > 0;

  if (!hasChanges) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <CheckCircle size={48} className="mx-auto mb-4 text-green-500" />
          <p>No changes</p>
          <p className="text-sm">Working tree clean</p>
        </div>
      </div>
    );
  }

  return (
    <ScrollArea className="flex-1 p-4">
      <div className="space-y-4">
        {status.staged.length > 0 && (
          <FileSection
            title="Staged Changes"
            files={status.staged}
            selectedFiles={selectedFiles}
            onFileSelect={onFileSelect}
            onAction={(files) => onUnstage(files)}
            actionLabel="Unstage"
            actionIcon={<Minus size={16} />}
            isLoading={isLoading}
            variant="staged"
          />
        )}

        {status.unstaged.length > 0 && (
          <FileSection
            title="Changes"
            files={status.unstaged}
            selectedFiles={selectedFiles}
            onFileSelect={onFileSelect}
            onAction={(files) => onStage(files)}
            actionLabel="Stage"
            actionIcon={<Plus size={16} />}
            isLoading={isLoading}
            variant="unstaged"
          />
        )}

        {status.untracked.length > 0 && (
          <FileSection
            title="Untracked Files"
            files={status.untracked}
            selectedFiles={selectedFiles}
            onFileSelect={onFileSelect}
            onAction={(files) => onStage(files)}
            actionLabel="Stage"
            actionIcon={<Plus size={16} />}
            isLoading={isLoading}
            variant="untracked"
          />
        )}

        {status.conflicted.length > 0 && (
          <FileSection
            title="Conflicted Files"
            files={status.conflicted}
            selectedFiles={selectedFiles}
            onFileSelect={onFileSelect}
            onAction={() => {}}
            actionLabel="Resolve"
            actionIcon={<AlertCircle size={16} />}
            isLoading={isLoading}
            variant="conflicted"
          />
        )}
      </div>
    </ScrollArea>
  );
};

const FileSection: React.FC<{
  title: string;
  files: string[];
  selectedFiles: string[];
  onFileSelect: (files: string[]) => void;
  onAction: (files: string[]) => void;
  actionLabel: string;
  actionIcon: React.ReactNode;
  isLoading: boolean;
  variant: 'staged' | 'unstaged' | 'untracked' | 'conflicted';
}> = ({ title, files, selectedFiles, onFileSelect, onAction, actionLabel, actionIcon, isLoading, variant }) => {
  const getVariantColor = () => {
    switch (variant) {
      case 'staged': return 'text-green-600';
      case 'unstaged': return 'text-yellow-600';
      case 'untracked': return 'text-blue-600';
      case 'conflicted': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const toggleFileSelection = (file: string, multiSelect: boolean = false) => {
    if (multiSelect) {
      const newSelection = selectedFiles.includes(file)
        ? selectedFiles.filter(f => f !== file)
        : [...selectedFiles, file];
      onFileSelect(newSelection);
    } else {
      onFileSelect(selectedFiles.includes(file) ? [] : [file]);
    }
  };

  const handleActionClick = () => {
    const filesToAction = selectedFiles.length > 0 
      ? selectedFiles.filter(f => files.includes(f))
      : files;
    
    if (filesToAction.length > 0) {
      onAction(filesToAction);
      onFileSelect([]);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h4 className={`font-medium ${getVariantColor()}`}>{title}</h4>
          <Badge variant="secondary" className="text-xs">
            {files.length}
          </Badge>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleActionClick}
          disabled={isLoading || files.length === 0}
        >
          {actionIcon}
          <span className="ml-1">{actionLabel}</span>
        </Button>
      </div>

      <div className="space-y-1">
        {files.map(file => (
          <div
            key={file}
            className={`flex items-center p-2 rounded cursor-pointer transition-colors ${
              selectedFiles.includes(file) 
                ? 'bg-blue-100 border border-blue-200' 
                : 'hover:bg-gray-50'
            }`}
            onClick={(e) => toggleFileSelection(file, e.ctrlKey || e.metaKey)}
          >
            <FileText size={16} className="mr-2 text-gray-500" />
            <span className="flex-1 text-sm font-mono">{file}</span>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="p-1">
                  <MoreVertical size={14} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(file)}>
                  <Copy size={16} className="mr-2" />
                  Copy Path
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileText size={16} className="mr-2" />
                  View Diff
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onAction([file])}>
                  {actionIcon}
                  <span className="ml-2">{actionLabel}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ))}
      </div>
    </div>
  );
};

const GitCommitSection: React.FC<{
  stagedFiles: string[];
  commitMessage: string;
  onCommitMessageChange: (message: string) => void;
  onCommit: () => void;
  isLoading: boolean;
  disabled: boolean;
}> = ({ stagedFiles, commitMessage, onCommitMessageChange, onCommit, isLoading, disabled }) => {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey) && !disabled) {
      onCommit();
    }
  };

  return (
    <div className="border-t p-4 space-y-3">
      <div className="flex items-center space-x-2">
        <GitCommitIcon size={16} />
        <span className="font-medium">Commit</span>
        {stagedFiles.length > 0 && (
          <Badge variant="secondary" className="text-xs">
            {stagedFiles.length} files
          </Badge>
        )}
      </div>

      <div className="space-y-2">
        <Input
          placeholder="Commit message (Ctrl+Enter to commit)"
          value={commitMessage}
          onChange={(e) => onCommitMessageChange(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={isLoading || disabled}
        />
        
        <Button
          onClick={onCommit}
          disabled={isLoading || disabled || !commitMessage.trim()}
          className="w-full"
        >
          {isLoading ? (
            <RefreshCw size={16} className="mr-2 animate-spin" />
          ) : (
            <GitCommitIcon size={16} className="mr-2" />
          )}
          Commit
        </Button>
      </div>

      {disabled && (
        <p className="text-sm text-gray-500">
          Stage files to enable commit
        </p>
      )}
    </div>
  );
};

const GitBranches: React.FC<{
  branches: GitBranch[];
  currentBranch?: string;
  onBranchSwitch: (branch: string) => void;
  onBranchCreate: (branch: string) => void;
  isLoading: boolean;
}> = ({ branches, currentBranch, onBranchSwitch, onBranchCreate, isLoading }) => {
  const [newBranchName, setNewBranchName] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const handleCreateBranch = () => {
    if (newBranchName.trim()) {
      onBranchCreate(newBranchName.trim());
      setNewBranchName('');
      setShowCreateDialog(false);
    }
  };

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Branches</h3>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Plus size={16} className="mr-1" />
              New Branch
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Branch</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Input
                placeholder="Branch name"
                value={newBranchName}
                onChange={(e) => setNewBranchName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleCreateBranch()}
              />
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateBranch} disabled={!newBranchName.trim()}>
                  Create Branch
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <ScrollArea className="h-64">
        <div className="space-y-2">
          {branches.map(branch => (
            <div
              key={branch.name}
              className={`flex items-center p-2 rounded cursor-pointer transition-colors ${
                branch.isCurrent ? 'bg-blue-100 border border-blue-200' : 'hover:bg-gray-50'
              }`}
              onClick={() => !branch.isCurrent && onBranchSwitch(branch.name)}
            >
              <GitBranchIcon size={16} className="mr-2" />
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium">{branch.name}</span>
                  {branch.isCurrent && (
                    <Badge variant="default" className="text-xs">Current</Badge>
                  )}
                </div>
                <div className="text-xs text-gray-500">
                  {formatDistanceToNow(branch.lastCommit.date)} ago
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                {branch.ahead > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <ArrowUp size={10} className="mr-1" />
                    {branch.ahead}
                  </Badge>
                )}
                {branch.behind > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <ArrowDown size={10} className="mr-1" />
                    {branch.behind}
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

const GitHistory: React.FC<{
  commits: GitCommit[];
  currentBranch?: string;
  isLoading: boolean;
}> = ({ commits, currentBranch, isLoading }) => {
  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Commit History</h3>
        <Badge variant="outline">{commits.length} commits</Badge>
      </div>

      <ScrollArea className="h-96">
        <div className="space-y-3">
          {commits.map(commit => (
            <Card key={commit.hash} className="p-3">
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="font-medium text-sm">{commit.message}</p>
                    <div className="flex items-center space-x-3 text-xs text-gray-500 mt-1">
                      <div className="flex items-center space-x-1">
                        <User size={12} />
                        <span>{commit.author.name}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock size={12} />
                        <span>{formatDistanceToNow(commit.date)} ago</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigator.clipboard.writeText(commit.hash)}
                    >
                      <Hash size={12} className="mr-1" />
                      {commit.hash.substring(0, 7)}
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default GitPanel;