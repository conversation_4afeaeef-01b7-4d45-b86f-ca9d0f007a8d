'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { GitDiff, GitDiffHunk, GitDiffLine, WebGitService } from '@/lib/git/git-service';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  X,
  FileText,
  Plus,
  Minus,
  MoreVertical,
  Split,
  Maximize2,
  Copy,
  Download,
  ChevronDown,
  ChevronRight,
  Eye,
  EyeOff,
  RotateCcw,
  Check,
  AlertTriangle,
  Diff
} from 'lucide-react';

interface GitDiffViewerProps {
  file: string;
  gitService: WebGitService;
  staged?: boolean;
  onClose: () => void;
  onStageLines?: (file: string, lines: number[]) => void;
  onUnstageLines?: (file: string, lines: number[]) => void;
  className?: string;
}

export const GitDiffViewer: React.FC<GitDiffViewerProps> = ({
  file,
  gitService,
  staged = false,
  onClose,
  onStageLines,
  onUnstageLines,
  className = ''
}) => {
  const [diffs, setDiffs] = useState<GitDiff[]>([]);
  const [selectedLines, setSelectedLines] = useState<Set<number>>(new Set());
  const [viewMode, setViewMode] = useState<'unified' | 'split'>('unified');
  const [showWhitespace, setShowWhitespace] = useState(false);
  const [expandedHunks, setExpandedHunks] = useState<Set<number>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDiff();
  }, [file, staged, gitService]);

  const loadDiff = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const diffData = await gitService.getDiff(file, staged);
      setDiffs(diffData);
      
      // Expand all hunks by default
      const hunkIndices = new Set<number>();
      diffData.forEach((diff, diffIndex) => {
        diff.hunks.forEach((_, hunkIndex) => {
          hunkIndices.add(diffIndex * 1000 + hunkIndex);
        });
      });
      setExpandedHunks(hunkIndices);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const currentDiff = diffs.find(d => d.filename === file);

  const toggleLineSelection = (lineNumber: number) => {
    const newSelection = new Set(selectedLines);
    if (newSelection.has(lineNumber)) {
      newSelection.delete(lineNumber);
    } else {
      newSelection.add(lineNumber);
    }
    setSelectedLines(newSelection);
  };

  const selectAllChangedLines = () => {
    const changedLines = new Set<number>();
    currentDiff?.hunks.forEach(hunk => {
      hunk.lines.forEach(line => {
        if (line.type !== 'context' && (line.newLineNumber || line.oldLineNumber)) {
          const lineNum = line.newLineNumber || line.oldLineNumber || 0;
          changedLines.add(lineNum);
        }
      });
    });
    setSelectedLines(changedLines);
  };

  const clearSelection = () => {
    setSelectedLines(new Set());
  };

  const handleStageSelectedLines = () => {
    if (onStageLines && selectedLines.size > 0) {
      onStageLines(file, Array.from(selectedLines));
      clearSelection();
    }
  };

  const handleUnstageSelectedLines = () => {
    if (onUnstageLines && selectedLines.size > 0) {
      onUnstageLines(file, Array.from(selectedLines));
      clearSelection();
    }
  };

  const toggleHunkExpansion = (diffIndex: number, hunkIndex: number) => {
    const key = diffIndex * 1000 + hunkIndex;
    const newExpanded = new Set(expandedHunks);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedHunks(newExpanded);
  };

  const getFileStatusBadge = (status: string) => {
    switch (status) {
      case 'added':
        return <Badge variant="default" className="bg-green-100 text-green-800">Added</Badge>;
      case 'modified':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Modified</Badge>;
      case 'deleted':
        return <Badge variant="default" className="bg-red-100 text-red-800">Deleted</Badge>;
      case 'renamed':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Renamed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className={`git-diff-viewer ${className} h-full flex flex-col`}>
        <DiffViewerHeader
          file={file}
          onClose={onClose}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          showWhitespace={showWhitespace}
          onShowWhitespaceChange={setShowWhitespace}
        />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Diff size={48} className="mx-auto mb-4 opacity-50 animate-pulse" />
            <p>Loading diff...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`git-diff-viewer ${className} h-full flex flex-col`}>
        <DiffViewerHeader
          file={file}
          onClose={onClose}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          showWhitespace={showWhitespace}
          onShowWhitespaceChange={setShowWhitespace}
        />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-red-600">
            <AlertTriangle size={48} className="mx-auto mb-4" />
            <p>Failed to load diff</p>
            <p className="text-sm">{error}</p>
            <Button onClick={loadDiff} className="mt-4">
              <RotateCcw size={16} className="mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!currentDiff) {
    return (
      <div className={`git-diff-viewer ${className} h-full flex flex-col`}>
        <DiffViewerHeader
          file={file}
          onClose={onClose}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          showWhitespace={showWhitespace}
          onShowWhitespaceChange={setShowWhitespace}
        />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p>No changes to display</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`git-diff-viewer ${className} h-full flex flex-col border rounded-lg`}>
      <DiffViewerHeader
        file={file}
        onClose={onClose}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        showWhitespace={showWhitespace}
        onShowWhitespaceChange={setShowWhitespace}
      />

      <div className="border-b p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FileText size={16} />
            <span className="font-mono text-sm">{currentDiff.filename}</span>
            {getFileStatusBadge(currentDiff.status)}
            {currentDiff.oldFilename && currentDiff.oldFilename !== currentDiff.filename && (
              <span className="text-sm text-gray-500">
                from {currentDiff.oldFilename}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {currentDiff.hunks.reduce((acc, hunk) => 
                acc + hunk.lines.filter(l => l.type !== 'context').length, 0
              )} changes
            </span>
          </div>
        </div>
      </div>

      {selectedLines.size > 0 && (
        <div className="border-b p-3 bg-blue-50">
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {selectedLines.size} line{selectedLines.size > 1 ? 's' : ''} selected
            </span>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={clearSelection}>
                Clear Selection
              </Button>
              {!staged && onStageLines && (
                <Button size="sm" onClick={handleStageSelectedLines}>
                  <Plus size={16} className="mr-1" />
                  Stage Selected
                </Button>
              )}
              {staged && onUnstageLines && (
                <Button size="sm" onClick={handleUnstageSelectedLines}>
                  <Minus size={16} className="mr-1" />
                  Unstage Selected
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      <ScrollArea className="flex-1">
        <div className="p-4">
          {currentDiff.hunks.map((hunk, hunkIndex) => {
            const hunkKey = 0 * 1000 + hunkIndex;
            const isExpanded = expandedHunks.has(hunkKey);

            return (
              <DiffHunk
                key={hunkIndex}
                hunk={hunk}
                hunkIndex={hunkIndex}
                isExpanded={isExpanded}
                onToggleExpansion={() => toggleHunkExpansion(0, hunkIndex)}
                selectedLines={selectedLines}
                onLineSelect={toggleLineSelection}
                viewMode={viewMode}
                showWhitespace={showWhitespace}
              />
            );
          })}
        </div>
      </ScrollArea>

      <div className="border-t p-3 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={selectAllChangedLines}>
              Select All Changes
            </Button>
            <Button variant="outline" size="sm" onClick={clearSelection}>
              Clear Selection
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => navigator.clipboard.writeText(file)}>
              <Copy size={16} className="mr-1" />
              Copy Path
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

const DiffViewerHeader: React.FC<{
  file: string;
  onClose: () => void;
  viewMode: 'unified' | 'split';
  onViewModeChange: (mode: 'unified' | 'split') => void;
  showWhitespace: boolean;
  onShowWhitespaceChange: (show: boolean) => void;
}> = ({ file, onClose, viewMode, onViewModeChange, showWhitespace, onShowWhitespaceChange }) => {
  return (
    <div className="flex items-center justify-between p-3 border-b bg-gray-50">
      <div className="flex items-center space-x-2">
        <Diff size={16} />
        <span className="font-medium">Diff Viewer</span>
      </div>

      <div className="flex items-center space-x-2">
        <Tabs value={viewMode} onValueChange={(v) => onViewModeChange(v as 'unified' | 'split')}>
          <TabsList className="grid grid-cols-2 h-8">
            <TabsTrigger value="unified" className="text-xs">Unified</TabsTrigger>
            <TabsTrigger value="split" className="text-xs">Split</TabsTrigger>
          </TabsList>
        </Tabs>

        <Button
          variant={showWhitespace ? 'default' : 'outline'}
          size="sm"
          onClick={() => onShowWhitespaceChange(!showWhitespace)}
        >
          {showWhitespace ? <EyeOff size={16} /> : <Eye size={16} />}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <MoreVertical size={16} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => navigator.clipboard.writeText(file)}>
              <Copy size={16} className="mr-2" />
              Copy Path
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Download size={16} className="mr-2" />
              Download File
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Maximize2 size={16} className="mr-2" />
              Open in New Window
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button variant="ghost" size="sm" onClick={onClose}>
          <X size={16} />
        </Button>
      </div>
    </div>
  );
};

const DiffHunk: React.FC<{
  hunk: GitDiffHunk;
  hunkIndex: number;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  selectedLines: Set<number>;
  onLineSelect: (lineNumber: number) => void;
  viewMode: 'unified' | 'split';
  showWhitespace: boolean;
}> = ({ 
  hunk, 
  hunkIndex, 
  isExpanded, 
  onToggleExpansion, 
  selectedLines, 
  onLineSelect, 
  viewMode, 
  showWhitespace 
}) => {
  const addedLines = hunk.lines.filter(l => l.type === 'added').length;
  const deletedLines = hunk.lines.filter(l => l.type === 'deleted').length;
  const contextLines = hunk.lines.filter(l => l.type === 'context').length;

  return (
    <div className="border rounded-lg mb-4">
      <div 
        className="flex items-center justify-between p-3 bg-gray-50 border-b cursor-pointer hover:bg-gray-100"
        onClick={onToggleExpansion}
      >
        <div className="flex items-center space-x-2">
          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          <code className="text-sm font-mono text-gray-600">{hunk.header}</code>
        </div>
        
        <div className="flex items-center space-x-2">
          {addedLines > 0 && (
            <Badge variant="outline" className="text-green-600 border-green-200">
              +{addedLines}
            </Badge>
          )}
          {deletedLines > 0 && (
            <Badge variant="outline" className="text-red-600 border-red-200">
              -{deletedLines}
            </Badge>
          )}
          {contextLines > 0 && (
            <Badge variant="outline" className="text-gray-600">
              {contextLines} context
            </Badge>
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="font-mono text-sm">
          {viewMode === 'unified' ? (
            <UnifiedDiffView
              lines={hunk.lines}
              selectedLines={selectedLines}
              onLineSelect={onLineSelect}
              showWhitespace={showWhitespace}
            />
          ) : (
            <SplitDiffView
              lines={hunk.lines}
              selectedLines={selectedLines}
              onLineSelect={onLineSelect}
              showWhitespace={showWhitespace}
            />
          )}
        </div>
      )}
    </div>
  );
};

const UnifiedDiffView: React.FC<{
  lines: GitDiffLine[];
  selectedLines: Set<number>;
  onLineSelect: (lineNumber: number) => void;
  showWhitespace: boolean;
}> = ({ lines, selectedLines, onLineSelect, showWhitespace }) => {
  const formatContent = (content: string) => {
    if (!showWhitespace) return content;
    return content.replace(/ /g, '·').replace(/\t/g, '→');
  };

  return (
    <div>
      {lines.map((line, index) => {
        const lineNumber = line.newLineNumber || line.oldLineNumber || 0;
        const isSelected = selectedLines.has(lineNumber);
        
        let bgColor = '';
        let borderColor = '';
        let textColor = '';
        
        switch (line.type) {
          case 'added':
            bgColor = isSelected ? 'bg-green-200' : 'bg-green-50';
            borderColor = 'border-l-green-500';
            textColor = 'text-green-800';
            break;
          case 'deleted':
            bgColor = isSelected ? 'bg-red-200' : 'bg-red-50';
            borderColor = 'border-l-red-500';
            textColor = 'text-red-800';
            break;
          case 'context':
            bgColor = isSelected ? 'bg-gray-200' : '';
            borderColor = 'border-l-gray-300';
            textColor = 'text-gray-700';
            break;
        }

        return (
          <div
            key={index}
            className={`flex items-center border-l-2 ${bgColor} ${borderColor} ${textColor} hover:bg-opacity-75 cursor-pointer`}
            onClick={() => lineNumber > 0 && onLineSelect(lineNumber)}
          >
            <div className="w-16 px-2 py-1 text-gray-500 text-xs text-right border-r bg-gray-50">
              {line.oldLineNumber || ''}
            </div>
            <div className="w-16 px-2 py-1 text-gray-500 text-xs text-right border-r bg-gray-50">
              {line.newLineNumber || ''}
            </div>
            <div className="flex-1 px-3 py-1 whitespace-pre-wrap">
              {formatContent(line.content)}
            </div>
            {isSelected && (
              <div className="px-2">
                <Check size={16} className="text-blue-600" />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

const SplitDiffView: React.FC<{
  lines: GitDiffLine[];
  selectedLines: Set<number>;
  onLineSelect: (lineNumber: number) => void;
  showWhitespace: boolean;
}> = ({ lines, selectedLines, onLineSelect, showWhitespace }) => {
  const formatContent = (content: string) => {
    if (!showWhitespace) return content;
    return content.replace(/ /g, '·').replace(/\t/g, '→');
  };

  // Group lines for split view
  const leftLines: (GitDiffLine | null)[] = [];
  const rightLines: (GitDiffLine | null)[] = [];

  let leftIndex = 0;
  let rightIndex = 0;

  for (const line of lines) {
    if (line.type === 'deleted') {
      leftLines.push(line);
      rightLines.push(null);
    } else if (line.type === 'added') {
      leftLines.push(null);
      rightLines.push(line);
    } else {
      leftLines.push(line);
      rightLines.push(line);
    }
  }

  const maxLines = Math.max(leftLines.length, rightLines.length);

  return (
    <div className="grid grid-cols-2 divide-x">
      {/* Left side (deletions and context) */}
      <div>
        {Array.from({ length: maxLines }, (_, index) => {
          const line = leftLines[index];
          if (!line) {
            return <div key={`left-${index}`} className="h-6"></div>;
          }

          const lineNumber = line.oldLineNumber || 0;
          const isSelected = selectedLines.has(lineNumber);
          
          let bgColor = '';
          let textColor = '';
          
          switch (line.type) {
            case 'deleted':
              bgColor = isSelected ? 'bg-red-200' : 'bg-red-50';
              textColor = 'text-red-800';
              break;
            case 'context':
              bgColor = isSelected ? 'bg-gray-200' : '';
              textColor = 'text-gray-700';
              break;
          }

          return (
            <div
              key={`left-${index}`}
              className={`flex items-center ${bgColor} ${textColor} hover:bg-opacity-75 cursor-pointer`}
              onClick={() => lineNumber > 0 && onLineSelect(lineNumber)}
            >
              <div className="w-12 px-2 py-1 text-gray-500 text-xs text-right border-r bg-gray-50">
                {line.oldLineNumber || ''}
              </div>
              <div className="flex-1 px-3 py-1 whitespace-pre-wrap">
                {formatContent(line.content)}
              </div>
              {isSelected && (
                <div className="px-2">
                  <Check size={16} className="text-blue-600" />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Right side (additions and context) */}
      <div>
        {Array.from({ length: maxLines }, (_, index) => {
          const line = rightLines[index];
          if (!line) {
            return <div key={`right-${index}`} className="h-6"></div>;
          }

          const lineNumber = line.newLineNumber || 0;
          const isSelected = selectedLines.has(lineNumber);
          
          let bgColor = '';
          let textColor = '';
          
          switch (line.type) {
            case 'added':
              bgColor = isSelected ? 'bg-green-200' : 'bg-green-50';
              textColor = 'text-green-800';
              break;
            case 'context':
              bgColor = isSelected ? 'bg-gray-200' : '';
              textColor = 'text-gray-700';
              break;
          }

          return (
            <div
              key={`right-${index}`}
              className={`flex items-center ${bgColor} ${textColor} hover:bg-opacity-75 cursor-pointer`}
              onClick={() => lineNumber > 0 && onLineSelect(lineNumber)}
            >
              <div className="w-12 px-2 py-1 text-gray-500 text-xs text-right border-r bg-gray-50">
                {line.newLineNumber || ''}
              </div>
              <div className="flex-1 px-3 py-1 whitespace-pre-wrap">
                {formatContent(line.content)}
              </div>
              {isSelected && (
                <div className="px-2">
                  <Check size={16} className="text-blue-600" />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default GitDiffViewer;