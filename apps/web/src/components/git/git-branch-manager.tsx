'use client';

import React, { useState, useEffect } from 'react';
import { GitBranch, WebGitService } from '@/lib/git/git-service';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  GitBranch as GitBranchIcon,
  Plus,
  MoreVertical,
  Trash2,
  GitMerge,
  GitPullRequest,
  Clock,
  User,
  ArrowUp,
  ArrowDown,
  Check,
  RefreshCw,
  Copy,
  Settings,
  AlertTriangle,
  CheckCircle,
  GitCommit
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface GitBranchManagerProps {
  gitService: WebGitService;
  currentBranch?: string;
  onBranchChange?: (branch: string) => void;
  className?: string;
}

export const GitBranchManager: React.FC<GitBranchManagerProps> = ({
  gitService,
  currentBranch,
  onBranchChange,
  className = ''
}) => {
  const [branches, setBranches] = useState<GitBranch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState<string | null>(null);
  const [newBranchName, setNewBranchName] = useState('');
  const [baseBranch, setBaseBranch] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadBranches();

    // Set up event listeners
    const handleBranchEvent = () => loadBranches();
    gitService.on('branchCreated', handleBranchEvent);
    gitService.on('branchSwitched', handleBranchEvent);
    gitService.on('branchDeleted', handleBranchEvent);

    return () => {
      gitService.off('branchCreated', handleBranchEvent);
      gitService.off('branchSwitched', handleBranchEvent);
      gitService.off('branchDeleted', handleBranchEvent);
    };
  }, [gitService]);

  const loadBranches = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const branchList = await gitService.getBranches();
      setBranches(branchList);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateBranch = async () => {
    if (!newBranchName.trim()) return;
    
    setIsLoading(true);
    try {
      await gitService.createBranch(newBranchName.trim(), baseBranch || undefined);
      setNewBranchName('');
      setBaseBranch('');
      setShowCreateDialog(false);
      await loadBranches();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSwitchBranch = async (branchName: string) => {
    if (branchName === currentBranch) return;
    
    setIsLoading(true);
    try {
      await gitService.switchBranch(branchName);
      await loadBranches();
      if (onBranchChange) {
        onBranchChange(branchName);
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteBranch = async (branchName: string, force: boolean = false) => {
    setIsLoading(true);
    try {
      await gitService.deleteBranch(branchName, force);
      setShowDeleteDialog(null);
      await loadBranches();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMergeBranch = async (branchName: string) => {
    setIsLoading(true);
    try {
      const result = await gitService.merge(branchName);
      if (result.success) {
        await loadBranches();
      } else {
        setError(`Merge conflicts detected in: ${result.conflicts.join(', ')}`);
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredBranches = branches.filter(branch =>
    branch.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const localBranches = filteredBranches.filter(b => b.isLocal);
  const remoteBranches = filteredBranches.filter(b => b.isRemote && !b.isLocal);

  return (
    <div className={`git-branch-manager h-full flex flex-col ${className}`}>
      <BranchManagerHeader
        onCreateBranch={() => setShowCreateDialog(true)}
        onRefresh={loadBranches}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        isLoading={isLoading}
        error={error}
      />

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Local Branches */}
          <BranchSection
            title="Local Branches"
            branches={localBranches}
            currentBranch={currentBranch}
            onBranchSwitch={handleSwitchBranch}
            onBranchDelete={(branch) => setShowDeleteDialog(branch)}
            onBranchMerge={handleMergeBranch}
            isLoading={isLoading}
          />

          {/* Remote Branches */}
          {remoteBranches.length > 0 && (
            <BranchSection
              title="Remote Branches"
              branches={remoteBranches}
              currentBranch={currentBranch}
              onBranchSwitch={handleSwitchBranch}
              onBranchDelete={(branch) => setShowDeleteDialog(branch)}
              onBranchMerge={handleMergeBranch}
              isLoading={isLoading}
              isRemote
            />
          )}
        </div>
      </ScrollArea>

      {/* Create Branch Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Branch</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Branch Name</label>
              <Input
                placeholder="feature/new-feature"
                value={newBranchName}
                onChange={(e) => setNewBranchName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleCreateBranch()}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Base Branch (optional)</label>
              <select
                value={baseBranch}
                onChange={(e) => setBaseBranch(e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="">Current branch</option>
                {branches.filter(b => b.isLocal).map(branch => (
                  <option key={branch.name} value={branch.name}>
                    {branch.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateBranch} disabled={!newBranchName.trim() || isLoading}>
                Create Branch
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Branch Dialog */}
      <AlertDialog open={!!showDeleteDialog} onOpenChange={() => setShowDeleteDialog(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Branch</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the branch "{showDeleteDialog}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => showDeleteDialog && handleDeleteBranch(showDeleteDialog)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Branch
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

const BranchManagerHeader: React.FC<{
  onCreateBranch: () => void;
  onRefresh: () => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  isLoading: boolean;
  error: string | null;
}> = ({ onCreateBranch, onRefresh, searchQuery, onSearchChange, isLoading, error }) => {
  return (
    <div className="border-b p-4 space-y-3">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Branch Manager</h2>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
          </Button>
          <Button size="sm" onClick={onCreateBranch}>
            <Plus size={16} className="mr-1" />
            New Branch
          </Button>
        </div>
      </div>

      <Input
        placeholder="Search branches..."
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
      />

      {error && (
        <div className="flex items-center space-x-2 text-red-600 text-sm">
          <AlertTriangle size={14} />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
};

const BranchSection: React.FC<{
  title: string;
  branches: GitBranch[];
  currentBranch?: string;
  onBranchSwitch: (branch: string) => void;
  onBranchDelete: (branch: string) => void;
  onBranchMerge: (branch: string) => void;
  isLoading: boolean;
  isRemote?: boolean;
}> = ({ 
  title, 
  branches, 
  currentBranch, 
  onBranchSwitch, 
  onBranchDelete, 
  onBranchMerge, 
  isLoading,
  isRemote = false
}) => {
  if (branches.length === 0) return null;

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-gray-900">{title}</h3>
        <Badge variant="secondary">{branches.length}</Badge>
      </div>

      <div className="space-y-2">
        {branches.map(branch => (
          <BranchItem
            key={branch.name}
            branch={branch}
            isCurrent={branch.name === currentBranch}
            onSwitch={() => onBranchSwitch(branch.name)}
            onDelete={() => onBranchDelete(branch.name)}
            onMerge={() => onBranchMerge(branch.name)}
            isLoading={isLoading}
            isRemote={isRemote}
          />
        ))}
      </div>
    </div>
  );
};

const BranchItem: React.FC<{
  branch: GitBranch;
  isCurrent: boolean;
  onSwitch: () => void;
  onDelete: () => void;
  onMerge: () => void;
  isLoading: boolean;
  isRemote: boolean;
}> = ({ branch, isCurrent, onSwitch, onDelete, onMerge, isLoading, isRemote }) => {
  const getBranchStatus = () => {
    if (isCurrent) return 'current';
    if (branch.ahead > 0 && branch.behind > 0) return 'diverged';
    if (branch.ahead > 0) return 'ahead';
    if (branch.behind > 0) return 'behind';
    return 'synced';
  };

  const getStatusBadge = () => {
    const status = getBranchStatus();
    switch (status) {
      case 'current':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Current</Badge>;
      case 'ahead':
        return <Badge variant="outline" className="text-green-600 border-green-200">Ahead</Badge>;
      case 'behind':
        return <Badge variant="outline" className="text-orange-600 border-orange-200">Behind</Badge>;
      case 'diverged':
        return <Badge variant="outline" className="text-red-600 border-red-200">Diverged</Badge>;
      case 'synced':
        return <Badge variant="outline" className="text-gray-600">Synced</Badge>;
      default:
        return null;
    }
  };

  return (
    <Card className={`transition-colors ${isCurrent ? 'ring-2 ring-blue-500 bg-blue-50' : ''}`}>
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <GitBranchIcon size={16} className={isCurrent ? 'text-blue-600' : 'text-gray-500'} />
              <span 
                className={`font-medium cursor-pointer truncate ${
                  isCurrent ? 'text-blue-900' : 'text-gray-900'
                }`}
                onClick={!isCurrent ? onSwitch : undefined}
              >
                {branch.name}
              </span>
              {getStatusBadge()}
              {isRemote && (
                <Badge variant="outline" className="text-xs">Remote</Badge>
              )}
            </div>

            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <GitCommit size={12} />
                <span>{branch.lastCommit.hash.substring(0, 7)}</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <User size={12} />
                <span className="truncate">{branch.lastCommit.author.name}</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Clock size={12} />
                <span>{formatDistanceToNow(branch.lastCommit.date)} ago</span>
              </div>
            </div>

            {(branch.ahead > 0 || branch.behind > 0) && (
              <div className="flex items-center space-x-2 mt-1">
                {branch.ahead > 0 && (
                  <div className="flex items-center space-x-1 text-green-600">
                    <ArrowUp size={12} />
                    <span className="text-xs">{branch.ahead} ahead</span>
                  </div>
                )}
                {branch.behind > 0 && (
                  <div className="flex items-center space-x-1 text-orange-600">
                    <ArrowDown size={12} />
                    <span className="text-xs">{branch.behind} behind</span>
                  </div>
                )}
              </div>
            )}

            <div className="text-xs text-gray-500 mt-1 truncate">
              {branch.lastCommit.message}
            </div>
          </div>

          <div className="flex items-center space-x-1 ml-3">
            {!isCurrent && !isRemote && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onSwitch}
                disabled={isLoading}
                title="Switch to this branch"
              >
                <Check size={16} />
              </Button>
            )}

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical size={16} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {!isCurrent && (
                  <>
                    <DropdownMenuItem onClick={onSwitch}>
                      <Check size={16} className="mr-2" />
                      Switch to Branch
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                
                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(branch.name)}>
                  <Copy size={16} className="mr-2" />
                  Copy Branch Name
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(branch.lastCommit.hash)}>
                  <Copy size={16} className="mr-2" />
                  Copy Commit Hash
                </DropdownMenuItem>

                {!isCurrent && !isRemote && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={onMerge}>
                      <GitMerge size={16} className="mr-2" />
                      Merge into Current
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem onClick={onDelete} className="text-red-600">
                      <Trash2 size={16} className="mr-2" />
                      Delete Branch
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GitBranchManager;