'use client';

import React, { useState, useEffect } from 'react';
import { WebGitService, GitStatus } from '@/lib/git/git-service';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  GitBranch,
  Plus,
  Download,
  FolderGit2,
  <PERSON><PERSON>s,
  Trash2,
  <PERSON>fresh<PERSON>w,
  AlertTriangle,
  CheckCircle,
  Copy,
  ExternalLink,
  Key,
  Globe,
  Lock,
  Clock,
  User,
  GitCommit
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface GitRepositoryManagerProps {
  gitService: WebGitService;
  workspacePath: string;
  onRepositoryChange?: (hasRepository: boolean) => void;
  className?: string;
}

interface GitRepository {
  path: string;
  name: string;
  remoteUrl?: string;
  currentBranch?: string;
  lastActivity?: Date;
  isActive: boolean;
}

interface GitCredential {
  hostname: string;
  username?: string;
  hasPassword: boolean;
  hasToken: boolean;
  hasSSHKey: boolean;
  lastUsed?: Date;
}

export const GitRepositoryManager: React.FC<GitRepositoryManagerProps> = ({
  gitService,
  workspacePath,
  onRepositoryChange,
  className = ''
}) => {
  const [repositories, setRepositories] = useState<GitRepository[]>([]);
  const [credentials, setCredentials] = useState<GitCredential[]>([]);
  const [currentStatus, setCurrentStatus] = useState<GitStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showInitDialog, setShowInitDialog] = useState(false);
  const [showCloneDialog, setShowCloneDialog] = useState(false);
  const [showCredentialsDialog, setShowCredentialsDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Form states
  const [cloneUrl, setCloneUrl] = useState('');
  const [cloneBranch, setCloneBranch] = useState('');
  const [initPath, setInitPath] = useState(workspacePath);
  
  // Credentials form states
  const [credentialHostname, setCredentialHostname] = useState('');
  const [credentialUsername, setCredentialUsername] = useState('');
  const [credentialPassword, setCredentialPassword] = useState('');
  const [credentialToken, setCredentialToken] = useState('');

  useEffect(() => {
    checkRepositoryStatus();
    loadRepositories();
    loadCredentials();
  }, [workspacePath, gitService]);

  const checkRepositoryStatus = async () => {
    try {
      gitService.setWorkdir(workspacePath);
      const status = await gitService.getStatus();
      setCurrentStatus(status);
      if (onRepositoryChange) {
        onRepositoryChange(true);
      }
    } catch (error) {
      setCurrentStatus(null);
      if (onRepositoryChange) {
        onRepositoryChange(false);
      }
    }
  };

  const loadRepositories = async () => {
    // Mock implementation - in real app, this would load from storage/API
    const mockRepos: GitRepository[] = [
      {
        path: workspacePath,
        name: 'Current Project',
        remoteUrl: 'https://github.com/user/project.git',
        currentBranch: currentStatus?.branch,
        lastActivity: new Date(),
        isActive: !!currentStatus
      }
    ];
    setRepositories(mockRepos);
  };

  const loadCredentials = async () => {
    // Mock implementation - in real app, this would load from secure storage
    const mockCredentials: GitCredential[] = [
      {
        hostname: 'github.com',
        username: 'user',
        hasPassword: false,
        hasToken: true,
        hasSSHKey: true,
        lastUsed: new Date()
      }
    ];
    setCredentials(mockCredentials);
  };

  const handleInitRepository = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await gitService.initRepository(initPath);
      setShowInitDialog(false);
      await checkRepositoryStatus();
      await loadRepositories();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloneRepository = async () => {
    if (!cloneUrl.trim()) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await gitService.cloneRepository(cloneUrl.trim(), workspacePath, {
        branch: cloneBranch || undefined
      });
      setCloneUrl('');
      setCloneBranch('');
      setShowCloneDialog(false);
      await checkRepositoryStatus();
      await loadRepositories();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCredential = async () => {
    if (!credentialHostname.trim()) return;
    
    try {
      // In real implementation, this would securely store credentials
      const auth: any = {};
      
      if (credentialUsername) auth.username = credentialUsername;
      if (credentialPassword) auth.password = credentialPassword;
      if (credentialToken) auth.token = credentialToken;
      
      // gitService.credentials.setCredentials(credentialHostname, auth);
      
      setCredentialHostname('');
      setCredentialUsername('');
      setCredentialPassword('');
      setCredentialToken('');
      setShowCredentialsDialog(false);
      await loadCredentials();
    } catch (error: any) {
      setError(error.message);
    }
  };

  return (
    <div className={`git-repository-manager h-full flex flex-col ${className}`}>
      <RepositoryManagerHeader
        hasRepository={!!currentStatus}
        onInitRepository={() => setShowInitDialog(true)}
        onCloneRepository={() => setShowCloneDialog(true)}
        onManageCredentials={() => setShowCredentialsDialog(true)}
        onRefresh={checkRepositoryStatus}
        isLoading={isLoading}
        error={error}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="repositories">Repositories</TabsTrigger>
          <TabsTrigger value="credentials">Credentials</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="flex-1">
          <RepositoryOverview
            status={currentStatus}
            workspacePath={workspacePath}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="repositories" className="flex-1">
          <RepositoryList
            repositories={repositories}
            currentPath={workspacePath}
            onRepositorySelect={(path) => gitService.setWorkdir(path)}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="credentials" className="flex-1">
          <CredentialsList
            credentials={credentials}
            onCredentialDelete={(hostname) => console.log('Delete credential:', hostname)}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>

      {/* Initialize Repository Dialog */}
      <Dialog open={showInitDialog} onOpenChange={setShowInitDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Initialize Git Repository</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Repository Path</label>
              <Input
                value={initPath}
                onChange={(e) => setInitPath(e.target.value)}
                placeholder="/path/to/repository"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowInitDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleInitRepository} disabled={isLoading}>
                Initialize Repository
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Clone Repository Dialog */}
      <Dialog open={showCloneDialog} onOpenChange={setShowCloneDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Clone Repository</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Repository URL</label>
              <Input
                value={cloneUrl}
                onChange={(e) => setCloneUrl(e.target.value)}
                placeholder="https://github.com/user/repository.git"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Branch (optional)</label>
              <Input
                value={cloneBranch}
                onChange={(e) => setCloneBranch(e.target.value)}
                placeholder="main"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCloneDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleCloneRepository} disabled={!cloneUrl.trim() || isLoading}>
                Clone Repository
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Manage Credentials Dialog */}
      <Dialog open={showCredentialsDialog} onOpenChange={setShowCredentialsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Git Credentials</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Hostname</label>
              <Input
                value={credentialHostname}
                onChange={(e) => setCredentialHostname(e.target.value)}
                placeholder="github.com"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Username</label>
              <Input
                value={credentialUsername}
                onChange={(e) => setCredentialUsername(e.target.value)}
                placeholder="your-username"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Personal Access Token</label>
              <Input
                type="password"
                value={credentialToken}
                onChange={(e) => setCredentialToken(e.target.value)}
                placeholder="ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Password (if not using token)</label>
              <Input
                type="password"
                value={credentialPassword}
                onChange={(e) => setCredentialPassword(e.target.value)}
                placeholder="your-password"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCredentialsDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddCredential} disabled={!credentialHostname.trim()}>
                Add Credentials
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const RepositoryManagerHeader: React.FC<{
  hasRepository: boolean;
  onInitRepository: () => void;
  onCloneRepository: () => void;
  onManageCredentials: () => void;
  onRefresh: () => void;
  isLoading: boolean;
  error: string | null;
}> = ({ 
  hasRepository, 
  onInitRepository, 
  onCloneRepository, 
  onManageCredentials, 
  onRefresh, 
  isLoading, 
  error 
}) => {
  return (
    <div className="border-b p-4 space-y-3">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Git Repository</h2>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
          </Button>
          <Button variant="outline" size="sm" onClick={onManageCredentials}>
            <Key size={16} className="mr-1" />
            Credentials
          </Button>
          {!hasRepository && (
            <>
              <Button variant="outline" size="sm" onClick={onInitRepository}>
                <Plus size={16} className="mr-1" />
                Initialize
              </Button>
              <Button size="sm" onClick={onCloneRepository}>
                <Download size={16} className="mr-1" />
                Clone
              </Button>
            </>
          )}
        </div>
      </div>

      {error && (
        <div className="flex items-center space-x-2 text-red-600 text-sm">
          <AlertTriangle size={14} />
          <span>{error}</span>
        </div>
      )}

      <div className="flex items-center space-x-2">
        {hasRepository ? (
          <>
            <CheckCircle size={16} className="text-green-600" />
            <span className="text-sm text-green-600">Git repository detected</span>
          </>
        ) : (
          <>
            <AlertTriangle size={16} className="text-orange-600" />
            <span className="text-sm text-orange-600">No Git repository found</span>
          </>
        )}
      </div>
    </div>
  );
};

const RepositoryOverview: React.FC<{
  status: GitStatus | null;
  workspacePath: string;
  isLoading: boolean;
}> = ({ status, workspacePath, isLoading }) => {
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <RefreshCw size={48} className="animate-spin opacity-50" />
      </div>
    );
  }

  if (!status) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FolderGit2 size={20} />
              <span>No Git Repository</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-600">
                This directory is not a Git repository. You can initialize a new repository or clone an existing one.
              </p>
              <div className="text-sm text-gray-500">
                <strong>Current path:</strong> {workspacePath}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <GitBranch size={20} />
            <span>Repository Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Current Branch</label>
              <div className="text-lg font-mono">{status.branch}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Remote Tracking</label>
              <div className="text-lg">{status.remoteTrackingBranch || 'None'}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Staged Files</label>
              <div className="text-lg text-green-600">{status.staged.length}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Unstaged Files</label>
              <div className="text-lg text-orange-600">{status.unstaged.length}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Untracked Files</label>
              <div className="text-lg text-blue-600">{status.untracked.length}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Conflicts</label>
              <div className="text-lg text-red-600">{status.conflicted.length}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {(status.ahead > 0 || status.behind > 0) && (
        <Card>
          <CardHeader>
            <CardTitle>Sync Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-6">
              {status.ahead > 0 && (
                <div className="flex items-center space-x-2 text-green-600">
                  <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                  <span>{status.ahead} commits ahead</span>
                </div>
              )}
              {status.behind > 0 && (
                <div className="flex items-center space-x-2 text-orange-600">
                  <div className="w-3 h-3 bg-orange-600 rounded-full"></div>
                  <span>{status.behind} commits behind</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

const RepositoryList: React.FC<{
  repositories: GitRepository[];
  currentPath: string;
  onRepositorySelect: (path: string) => void;
  isLoading: boolean;
}> = ({ repositories, currentPath, onRepositorySelect, isLoading }) => {
  return (
    <div className="p-6 space-y-4">
      {repositories.map(repo => (
        <Card 
          key={repo.path} 
          className={`cursor-pointer transition-colors ${
            repo.path === currentPath ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
          }`}
          onClick={() => onRepositorySelect(repo.path)}
        >
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <FolderGit2 size={16} />
                  <span className="font-medium">{repo.name}</span>
                  {repo.isActive && (
                    <CheckCircle size={14} className="text-green-600" />
                  )}
                </div>
                
                <div className="text-sm text-gray-600 space-y-1">
                  <div>Path: {repo.path}</div>
                  {repo.remoteUrl && (
                    <div className="flex items-center space-x-1">
                      <Globe size={12} />
                      <span>Remote: {repo.remoteUrl}</span>
                    </div>
                  )}
                  {repo.currentBranch && (
                    <div className="flex items-center space-x-1">
                      <GitBranch size={12} />
                      <span>Branch: {repo.currentBranch}</span>
                    </div>
                  )}
                  {repo.lastActivity && (
                    <div className="flex items-center space-x-1">
                      <Clock size={12} />
                      <span>Last activity: {formatDistanceToNow(repo.lastActivity)} ago</span>
                    </div>
                  )}
                </div>
              </div>
              
              <Button variant="ghost" size="sm">
                <ExternalLink size={16} />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

const CredentialsList: React.FC<{
  credentials: GitCredential[];
  onCredentialDelete: (hostname: string) => void;
  isLoading: boolean;
}> = ({ credentials, onCredentialDelete, isLoading }) => {
  return (
    <div className="p-6 space-y-4">
      {credentials.map(credential => (
        <Card key={credential.hostname}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <Globe size={16} />
                  <span className="font-medium">{credential.hostname}</span>
                  {credential.username && (
                    <span className="text-sm text-gray-600">({credential.username})</span>
                  )}
                </div>
                
                <div className="flex items-center space-x-4 text-sm">
                  {credential.hasPassword && (
                    <div className="flex items-center space-x-1 text-green-600">
                      <Lock size={12} />
                      <span>Password</span>
                    </div>
                  )}
                  {credential.hasToken && (
                    <div className="flex items-center space-x-1 text-blue-600">
                      <Key size={12} />
                      <span>Token</span>
                    </div>
                  )}
                  {credential.hasSSHKey && (
                    <div className="flex items-center space-x-1 text-purple-600">
                      <Key size={12} />
                      <span>SSH Key</span>
                    </div>
                  )}
                </div>
                
                {credential.lastUsed && (
                  <div className="text-xs text-gray-500 mt-1">
                    Last used: {formatDistanceToNow(credential.lastUsed)} ago
                  </div>
                )}
              </div>
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => onCredentialDelete(credential.hostname)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 size={16} />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {credentials.length === 0 && (
        <div className="text-center text-gray-500 py-8">
          <Key size={48} className="mx-auto mb-4 opacity-50" />
          <p>No credentials configured</p>
          <p className="text-sm">Add credentials to access private repositories</p>
        </div>
      )}
    </div>
  );
};

export default GitRepositoryManager;