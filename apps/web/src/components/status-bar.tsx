'use client';

import React, { useEffect, useState } from 'react';
import { useEditorStore } from '@/store/editor-store';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { 
  GitBranch, 
  AlertCircle, 
  CheckCircle, 
  Settings, 
  Zap,
  FileText,
  Clock,
  Sun,
  Moon,
  Monitor
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface StatusBarProps {
  className?: string;
}

export function StatusBar({ className = '' }: StatusBarProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [cursorPosition, setCursorPosition] = useState({ line: 1, column: 1 });
  const [selectionCount, setSelectionCount] = useState(0);
  const { theme, setTheme } = useTheme();

  const {
    activeFile,
    getFileById,
    getEditor,
    getDirtyFiles,
    openFiles,
    configuration,
    updateConfiguration,
  } = useEditorStore();

  const activeFileData = activeFile ? getFileById(activeFile) : null;
  const activeEditor = activeFile ? getEditor(activeFile) : null;
  const dirtyFiles = getDirtyFiles();

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Monitor cursor position and selection
  useEffect(() => {
    if (activeEditor) {
      const disposable = activeEditor.onDidChangeCursorPosition((e) => {
        setCursorPosition({
          line: e.position.lineNumber,
          column: e.position.column,
        });
      });

      const selectionDisposable = activeEditor.onDidChangeCursorSelection((e) => {
        const selection = e.selection;
        if (selection.isEmpty()) {
          setSelectionCount(0);
        } else {
          const model = activeEditor.getModel();
          if (model) {
            const selectedText = model.getValueInRange(selection);
            setSelectionCount(selectedText.length);
          }
        }
      });

      return () => {
        disposable.dispose();
        selectionDisposable.dispose();
      };
    }
  }, [activeEditor]);

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
  };

  const handleConfigurationChange = (key: string, value: any) => {
    updateConfiguration({ [key]: value });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getLanguageDisplayName = (languageId: string) => {
    const languageMap: Record<string, string> = {
      javascript: 'JavaScript',
      typescript: 'TypeScript',
      python: 'Python',
      html: 'HTML',
      css: 'CSS',
      json: 'JSON',
      markdown: 'Markdown',
      plaintext: 'Plain Text',
    };
    return languageMap[languageId] || languageId.toUpperCase();
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light': return <Sun className="h-3 w-3" />;
      case 'dark': return <Moon className="h-3 w-3" />;
      default: return <Monitor className="h-3 w-3" />;
    }
  };

  return (
    <div className={cn('h-6 bg-primary text-primary-foreground px-2 flex items-center justify-between text-xs font-mono', className)}>
      {/* Left side - File info and stats */}
      <div className="flex items-center gap-4">
        {/* File count */}
        <div className="flex items-center gap-1">
          <FileText className="h-3 w-3" />
          <span>{openFiles.length}</span>
          {dirtyFiles.length > 0 && (
            <span className="text-yellow-300">
              ({dirtyFiles.length} unsaved)
            </span>
          )}
        </div>

        {/* Git status (mock) */}
        <div className="flex items-center gap-1">
          <GitBranch className="h-3 w-3" />
          <span>main</span>
        </div>

        {/* Cursor position */}
        {activeFileData && (
          <div className="flex items-center gap-2">
            <span>
              Ln {cursorPosition.line}, Col {cursorPosition.column}
            </span>
            {selectionCount > 0 && (
              <span className="text-yellow-300">
                ({selectionCount} selected)
              </span>
            )}
          </div>
        )}

        {/* Language */}
        {activeFileData && (
          <div className="flex items-center gap-1">
            <span>{getLanguageDisplayName(activeFileData.language)}</span>
          </div>
        )}
      </div>

      {/* Right side - Settings and status */}
      <div className="flex items-center gap-4">
        {/* Encoding */}
        <span>UTF-8</span>

        {/* Line ending */}
        <span>LF</span>

        {/* Auto-save indicator */}
        {configuration.autoSave && (
          <div className="flex items-center gap-1 text-green-300">
            <CheckCircle className="h-3 w-3" />
            <span>Auto-save</span>
          </div>
        )}

        {/* Theme switcher */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-4 px-1 text-primary-foreground hover:bg-primary-foreground/10"
            >
              {getThemeIcon()}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleThemeChange('light')}>
              <Sun className="h-4 w-4 mr-2" />
              Light
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleThemeChange('dark')}>
              <Moon className="h-4 w-4 mr-2" />
              Dark
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleThemeChange('system')}>
              <Monitor className="h-4 w-4 mr-2" />
              System
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Settings */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-4 px-1 text-primary-foreground hover:bg-primary-foreground/10"
            >
              <Settings className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              onClick={() => handleConfigurationChange('autoSave', !configuration.autoSave)}
            >
              <CheckCircle className={cn('h-4 w-4 mr-2', configuration.autoSave ? 'text-green-500' : 'text-gray-400')} />
              Auto Save
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleConfigurationChange('formatOnSave', !configuration.formatOnSave)}
            >
              <Zap className={cn('h-4 w-4 mr-2', configuration.formatOnSave ? 'text-green-500' : 'text-gray-400')} />
              Format on Save
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleConfigurationChange('minimap.enabled', !configuration.minimap.enabled)}
            >
              <CheckCircle className={cn('h-4 w-4 mr-2', configuration.minimap.enabled ? 'text-green-500' : 'text-gray-400')} />
              Minimap
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <span className="text-xs text-muted-foreground">Font Size: {configuration.fontSize}px</span>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <span className="text-xs text-muted-foreground">Tab Size: {configuration.tabSize}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Current time */}
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>{formatTime(currentTime)}</span>
        </div>
      </div>
    </div>
  );
}