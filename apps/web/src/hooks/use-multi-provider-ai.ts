import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { 
  MultiProviderService, 
  MultiProviderConfig, 
  MultiProviderGenerationRequest, 
  MultiProviderGenerationResponse,
  MultiProviderMetrics,
  ProviderBudget,
  BudgetAlert
} from '../lib/ai/multi-provider-service';
import { 
  ProviderSelectionCriteria, 
  SelectionPriority, 
  TaskType 
} from '../lib/ai/provider-selection-engine';
import { EnhancedHybridCanvasManager } from '../lib/canvas/enhanced-hybrid-canvas';
import { AdvancedLayerManager } from '../lib/canvas/advanced-layer-manager';

// Hook state types
export interface MultiProviderAIState {
  isInitialized: boolean;
  isGenerating: boolean;
  availableProviders: string[];
  providerStatuses: Record<string, any>;
  generationHistory: MultiProviderGenerationResponse[];
  currentGeneration?: {
    id: string;
    progress: number;
    status: string;
    provider: string;
    estimatedTimeRemaining?: number;
  };
  lastError?: string;
  metrics: MultiProviderMetrics;
  budgetSummary: Record<string, ProviderBudget>;
  budgetAlerts: BudgetAlert[];
  providerPreferences: {
    defaultPriority: SelectionPriority;
    preferredProviders: string[];
    excludedProviders: string[];
    budgetLimits: {
      daily: number;
      monthly: number;
    };
  };
}

export interface MultiProviderAIActions {
  // Core generation methods
  generateImage: (request: Partial<MultiProviderGenerationRequest>) => Promise<MultiProviderGenerationResponse>;
  
  generateTextToImage: (
    prompt: string,
    options?: {
      negativePrompt?: string;
      width?: number;
      height?: number;
      seed?: number;
      quality?: 'draft' | 'standard' | 'premium';
      style?: string;
      providerPreference?: string;
      selectionCriteria?: Partial<ProviderSelectionCriteria>;
    }
  ) => Promise<MultiProviderGenerationResponse>;
  
  generateImageToImage: (
    prompt: string,
    sourceImage: string,
    options?: {
      strength?: number;
      negativePrompt?: string;
      providerPreference?: string;
    }
  ) => Promise<MultiProviderGenerationResponse>;
  
  generateInpainting: (
    prompt: string,
    sourceImage: string,
    maskImage: string,
    options?: {
      strength?: number;
      negativePrompt?: string;
      providerPreference?: string;
    }
  ) => Promise<MultiProviderGenerationResponse>;
  
  // Provider management
  setProviderPreference: (provider: string, preferred: boolean) => void;
  excludeProvider: (provider: string, excluded: boolean) => void;
  setDefaultPriority: (priority: SelectionPriority) => void;
  
  // Budget management
  setBudgetLimit: (period: 'daily' | 'monthly', limit: number) => void;
  setProviderBudgetLimit: (provider: string, period: 'daily' | 'monthly', limit: number) => void;
  resetBudget: (provider?: string) => void;
  dismissBudgetAlert: (alertIndex: number) => void;
  
  // Generation management
  cancelGeneration: (generationId: string) => Promise<boolean>;
  retryGeneration: (generationId: string) => Promise<MultiProviderGenerationResponse>;
  
  // Analytics and insights
  getProviderAnalytics: (timeRange?: { start: Date; end: Date }) => MultiProviderMetrics;
  getProviderRecommendations: (task: TaskType) => Promise<string[]>;
  
  // Cache management
  clearCache: () => void;
  getCacheStats: () => any;
  
  // Provider testing
  testProvider: (providerName: string) => Promise<{ success: boolean; error?: string; responseTime?: number }>;
  testAllProviders: () => Promise<Record<string, { success: boolean; error?: string; responseTime?: number }>>;
}

export interface MultiProviderAIHookOptions {
  config: MultiProviderConfig;
  canvasManager?: EnhancedHybridCanvasManager | null;
  layerManager?: AdvancedLayerManager | null;
  enableAutoRetry?: boolean;
  maxRetries?: number;
  enableProgressTracking?: boolean;
  enableBudgetAlerts?: boolean;
  onGenerationComplete?: (response: MultiProviderGenerationResponse) => void;
  onGenerationError?: (error: string) => void;
  onBudgetAlert?: (alert: BudgetAlert) => void;
}

export interface MultiProviderAIHookReturn {
  state: MultiProviderAIState;
  actions: MultiProviderAIActions;
  service: MultiProviderService | null;
  isReady: boolean;
}

// Custom hook for multi-provider AI generation
export function useMultiProviderAI(
  options: MultiProviderAIHookOptions
): MultiProviderAIHookReturn {
  const {
    config,
    canvasManager,
    layerManager,
    enableAutoRetry = true,
    maxRetries = 3,
    enableProgressTracking = true,
    enableBudgetAlerts = true,
    onGenerationComplete,
    onGenerationError,
    onBudgetAlert
  } = options;

  // State management
  const [state, setState] = useState<MultiProviderAIState>({
    isInitialized: false,
    isGenerating: false,
    availableProviders: [],
    providerStatuses: {},
    generationHistory: [],
    metrics: {
      totalGenerations: 0,
      successRate: 0,
      averageCost: 0,
      averageTime: 0,
      providerDistribution: {},
      taskDistribution: {},
      costSavings: 0,
      qualityScore: 0,
      uptime: 100
    },
    budgetSummary: {},
    budgetAlerts: [],
    providerPreferences: {
      defaultPriority: config.defaultStrategy?.priority || 'balanced',
      preferredProviders: [],
      excludedProviders: [],
      budgetLimits: {
        daily: config.defaultStrategy?.budgetLimits?.dailyLimit || 50,
        monthly: config.defaultStrategy?.budgetLimits?.monthlyLimit || 500
      }
    }
  });

  // Service ref
  const serviceRef = useRef<MultiProviderService | null>(null);
  const metricsIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const activeGenerationsRef = useRef<Map<string, Promise<any>>>(new Map());

  // Initialize service
  useEffect(() => {
    const initializeService = async () => {
      try {
        setState(prev => ({ ...prev, lastError: undefined }));

        const service = new MultiProviderService(config);
        serviceRef.current = service;

        // Set up event listeners
        setupServiceEvents(service);

        // Initialize the service
        await service.initialize();

        // Load initial state
        const providers = service.getAvailableProviders();
        const statuses = service.getAllProviderStatuses();
        const budget = service.getBudgetSummary();
        const metrics = service.getMetrics();

        setState(prev => ({
          ...prev,
          isInitialized: true,
          availableProviders: providers,
          providerStatuses: statuses.reduce((acc, status) => {
            if (status) acc[status.name] = status;
            return acc;
          }, {} as Record<string, any>),
          budgetSummary: budget,
          metrics
        }));

        // Start metrics monitoring
        if (enableProgressTracking) {
          startMetricsMonitoring();
        }

      } catch (error) {
        console.error('Failed to initialize MultiProviderService:', error);
        setState(prev => ({
          ...prev,
          lastError: error instanceof Error ? error.message : 'Unknown error',
          isInitialized: false
        }));
      }
    };

    initializeService();

    return () => {
      // Cleanup
      if (serviceRef.current) {
        serviceRef.current.dispose();
        serviceRef.current = null;
      }
      if (metricsIntervalRef.current) {
        clearInterval(metricsIntervalRef.current);
        metricsIntervalRef.current = null;
      }
      activeGenerationsRef.current.clear();
    };
  }, [config, enableProgressTracking]);

  // Set up service event listeners
  const setupServiceEvents = useCallback((service: MultiProviderService) => {
    service.on('provider:selected', (data) => {
      setState(prev => ({
        ...prev,
        currentGeneration: prev.currentGeneration ? {
          ...prev.currentGeneration,
          provider: data.result.primary.provider
        } : undefined
      }));
    });

    service.on('generation:completed', (response) => {
      setState(prev => ({
        ...prev,
        generationHistory: [response, ...prev.generationHistory.slice(0, 99)],
        isGenerating: false,
        currentGeneration: undefined
      }));
      
      if (onGenerationComplete) {
        onGenerationComplete(response);
      }
    });

    service.on('generation:failed', (data) => {
      setState(prev => ({
        ...prev,
        isGenerating: false,
        currentGeneration: undefined,
        lastError: data.error
      }));
      
      if (onGenerationError) {
        onGenerationError(data.error);
      }
    });

    service.on('budget:alert', (alert: BudgetAlert) => {
      if (enableBudgetAlerts) {
        setState(prev => ({
          ...prev,
          budgetAlerts: [alert, ...prev.budgetAlerts.slice(0, 9)] // Keep last 10 alerts
        }));
        
        if (onBudgetAlert) {
          onBudgetAlert(alert);
        }
      }
    });

    service.on('budget:updated', (data) => {
      updateBudgetSummary();
    });

    service.on('fallback:success', (data) => {
      console.log(`Fallback successful: ${data.original} -> ${data.fallback}`);
    });

  }, [enableBudgetAlerts, onGenerationComplete, onGenerationError, onBudgetAlert]);

  // Start metrics monitoring
  const startMetricsMonitoring = useCallback(() => {
    if (metricsIntervalRef.current) {
      clearInterval(metricsIntervalRef.current);
    }

    metricsIntervalRef.current = setInterval(() => {
      updateMetrics();
      updateBudgetSummary();
      updateProviderStatuses();
    }, 10000); // Update every 10 seconds
  }, []);

  // Update functions
  const updateMetrics = useCallback(() => {
    if (!serviceRef.current) return;
    
    const metrics = serviceRef.current.getMetrics();
    setState(prev => ({ ...prev, metrics }));
  }, []);

  const updateBudgetSummary = useCallback(() => {
    if (!serviceRef.current) return;
    
    const budgetSummary = serviceRef.current.getBudgetSummary();
    setState(prev => ({ ...prev, budgetSummary }));
  }, []);

  const updateProviderStatuses = useCallback(() => {
    if (!serviceRef.current) return;
    
    const statuses = serviceRef.current.getAllProviderStatuses();
    const statusMap = statuses.reduce((acc, status) => {
      if (status) acc[status.name] = status;
      return acc;
    }, {} as Record<string, any>);
    
    setState(prev => ({ ...prev, providerStatuses: statusMap }));
  }, []);

  // Action creators
  const actions = useMemo<MultiProviderAIActions>(() => ({
    // Core generation methods
    generateImage: async (request: Partial<MultiProviderGenerationRequest>): Promise<MultiProviderGenerationResponse> => {
      if (!serviceRef.current) throw new Error('Service not initialized');

      const fullRequest: MultiProviderGenerationRequest = {
        prompt: '',
        model: '', // Will be selected automatically
        enableFallback: enableAutoRetry,
        maxRetries,
        ...request
      };

      const generationId = `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      setState(prev => ({
        ...prev,
        isGenerating: true,
        currentGeneration: {
          id: generationId,
          progress: 0,
          status: 'starting',
          provider: 'selecting...'
        }
      }));

      try {
        const promise = serviceRef.current.generateImage(fullRequest);
        activeGenerationsRef.current.set(generationId, promise);
        
        const response = await promise;
        activeGenerationsRef.current.delete(generationId);
        
        return response;
      } catch (error) {
        activeGenerationsRef.current.delete(generationId);
        setState(prev => ({
          ...prev,
          isGenerating: false,
          currentGeneration: undefined,
          lastError: error instanceof Error ? error.message : 'Unknown error'
        }));
        throw error;
      }
    },

    generateTextToImage: async (prompt: string, options = {}): Promise<MultiProviderGenerationResponse> => {
      return actions.generateImage({
        prompt,
        negativePrompt: options.negativePrompt,
        width: options.width,
        height: options.height,
        seed: options.seed,
        quality: options.quality,
        style: options.style,
        providerPreference: options.providerPreference,
        selectionCriteria: options.selectionCriteria
      });
    },

    generateImageToImage: async (prompt: string, sourceImage: string, options = {}): Promise<MultiProviderGenerationResponse> => {
      return actions.generateImage({
        prompt,
        imageInput: sourceImage,
        strength: options.strength,
        negativePrompt: options.negativePrompt,
        providerPreference: options.providerPreference
      });
    },

    generateInpainting: async (prompt: string, sourceImage: string, maskImage: string, options = {}): Promise<MultiProviderGenerationResponse> => {
      return actions.generateImage({
        prompt,
        imageInput: sourceImage,
        maskInput: maskImage,
        strength: options.strength,
        negativePrompt: options.negativePrompt,
        providerPreference: options.providerPreference
      });
    },

    // Provider management
    setProviderPreference: (provider: string, preferred: boolean) => {
      setState(prev => ({
        ...prev,
        providerPreferences: {
          ...prev.providerPreferences,
          preferredProviders: preferred
            ? [...prev.providerPreferences.preferredProviders.filter(p => p !== provider), provider]
            : prev.providerPreferences.preferredProviders.filter(p => p !== provider)
        }
      }));
    },

    excludeProvider: (provider: string, excluded: boolean) => {
      setState(prev => ({
        ...prev,
        providerPreferences: {
          ...prev.providerPreferences,
          excludedProviders: excluded
            ? [...prev.providerPreferences.excludedProviders.filter(p => p !== provider), provider]
            : prev.providerPreferences.excludedProviders.filter(p => p !== provider)
        }
      }));
    },

    setDefaultPriority: (priority: SelectionPriority) => {
      setState(prev => ({
        ...prev,
        providerPreferences: {
          ...prev.providerPreferences,
          defaultPriority: priority
        }
      }));
    },

    // Budget management
    setBudgetLimit: (period: 'daily' | 'monthly', limit: number) => {
      if (!serviceRef.current) return;
      
      serviceRef.current.setBudgetLimit('total', period, limit);
      setState(prev => ({
        ...prev,
        providerPreferences: {
          ...prev.providerPreferences,
          budgetLimits: {
            ...prev.providerPreferences.budgetLimits,
            [period]: limit
          }
        }
      }));
    },

    setProviderBudgetLimit: (provider: string, period: 'daily' | 'monthly', limit: number) => {
      if (!serviceRef.current) return;
      serviceRef.current.setBudgetLimit(provider, period, limit);
    },

    resetBudget: (provider?: string) => {
      // This would need to be implemented in the service
      console.log('Reset budget for:', provider || 'all providers');
    },

    dismissBudgetAlert: (alertIndex: number) => {
      setState(prev => ({
        ...prev,
        budgetAlerts: prev.budgetAlerts.filter((_, index) => index !== alertIndex)
      }));
    },

    // Generation management
    cancelGeneration: async (generationId: string): Promise<boolean> => {
      const activeGeneration = activeGenerationsRef.current.get(generationId);
      if (activeGeneration) {
        try {
          // This would need cancellation support in the service
          activeGenerationsRef.current.delete(generationId);
          setState(prev => ({
            ...prev,
            isGenerating: false,
            currentGeneration: undefined
          }));
          return true;
        } catch {
          return false;
        }
      }
      return false;
    },

    retryGeneration: async (generationId: string): Promise<MultiProviderGenerationResponse> => {
      const generation = state.generationHistory.find(g => g.id === generationId);
      if (!generation) {
        throw new Error('Generation not found');
      }

      // Extract original request parameters and retry
      return actions.generateImage({
        prompt: generation.metadata.prompt,
        // Add other parameters from the original request
      });
    },

    // Analytics and insights
    getProviderAnalytics: (timeRange?: { start: Date; end: Date }): MultiProviderMetrics => {
      if (!serviceRef.current) {
        return state.metrics;
      }
      return serviceRef.current.getMetrics(timeRange);
    },

    getProviderRecommendations: async (task: TaskType): Promise<string[]> => {
      // This would use the selection engine to recommend providers for a task
      return state.availableProviders.slice(0, 3); // Simplified
    },

    // Cache management
    clearCache: () => {
      if (!serviceRef.current) return;
      serviceRef.current.clearCache();
    },

    getCacheStats: () => {
      // This would need to be implemented in the service
      return {};
    },

    // Provider testing
    testProvider: async (providerName: string): Promise<{ success: boolean; error?: string; responseTime?: number }> => {
      try {
        const startTime = Date.now();
        await actions.generateTextToImage('test image', { 
          providerPreference: providerName,
          width: 256, 
          height: 256 
        });
        const responseTime = Date.now() - startTime;
        return { success: true, responseTime };
      } catch (error) {
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    },

    testAllProviders: async (): Promise<Record<string, { success: boolean; error?: string; responseTime?: number }>> => {
      const results: Record<string, { success: boolean; error?: string; responseTime?: number }> = {};
      
      for (const provider of state.availableProviders) {
        results[provider] = await actions.testProvider(provider);
      }
      
      return results;
    }
  }), [
    enableAutoRetry,
    maxRetries,
    state.availableProviders,
    state.generationHistory,
    state.metrics
  ]);

  // Computed values
  const isReady = useMemo(() => {
    return state.isInitialized && !!serviceRef.current && state.availableProviders.length > 0;
  }, [state.isInitialized, state.availableProviders.length]);

  return {
    state,
    actions,
    service: serviceRef.current,
    isReady
  };
}

// Helper hooks for specific use cases

export function useProviderSelection(
  multiProviderAI: MultiProviderAIHookReturn,
  defaultCriteria: Partial<ProviderSelectionCriteria> = {}
) {
  const [criteria, setCriteria] = useState<Partial<ProviderSelectionCriteria>>(defaultCriteria);
  const [recommendations, setRecommendations] = useState<string[]>([]);

  const updateCriteria = useCallback((updates: Partial<ProviderSelectionCriteria>) => {
    setCriteria(prev => ({ ...prev, ...updates }));
  }, []);

  const refreshRecommendations = useCallback(async (task: TaskType) => {
    if (multiProviderAI.isReady) {
      const recs = await multiProviderAI.actions.getProviderRecommendations(task);
      setRecommendations(recs);
    }
  }, [multiProviderAI]);

  return {
    criteria,
    setCriteria,
    updateCriteria,
    recommendations,
    refreshRecommendations
  };
}

export function useBudgetMonitoring(
  multiProviderAI: MultiProviderAIHookReturn,
  alertThresholds: { warning: number; critical: number } = { warning: 0.75, critical: 0.9 }
) {
  const [budgetWarnings, setBudgetWarnings] = useState<Array<{
    provider: string;
    period: 'daily' | 'monthly';
    percentage: number;
    severity: 'warning' | 'critical';
  }>>([]);

  useEffect(() => {
    const warnings: typeof budgetWarnings = [];
    
    Object.entries(multiProviderAI.state.budgetSummary).forEach(([provider, budget]) => {
      const dailyPercent = budget.daily.spent / budget.daily.limit;
      const monthlyPercent = budget.monthly.spent / budget.monthly.limit;
      
      if (dailyPercent >= alertThresholds.critical) {
        warnings.push({ provider, period: 'daily', percentage: dailyPercent, severity: 'critical' });
      } else if (dailyPercent >= alertThresholds.warning) {
        warnings.push({ provider, period: 'daily', percentage: dailyPercent, severity: 'warning' });
      }
      
      if (monthlyPercent >= alertThresholds.critical) {
        warnings.push({ provider, period: 'monthly', percentage: monthlyPercent, severity: 'critical' });
      } else if (monthlyPercent >= alertThresholds.warning) {
        warnings.push({ provider, period: 'monthly', percentage: monthlyPercent, severity: 'warning' });
      }
    });
    
    setBudgetWarnings(warnings);
  }, [multiProviderAI.state.budgetSummary, alertThresholds]);

  return {
    budgetWarnings,
    budgetSummary: multiProviderAI.state.budgetSummary,
    setBudgetLimit: multiProviderAI.actions.setBudgetLimit,
    setProviderBudgetLimit: multiProviderAI.actions.setProviderBudgetLimit
  };
}