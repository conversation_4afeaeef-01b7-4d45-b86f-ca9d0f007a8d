'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { 
  ImageEnhancementEngine, 
  EnhancementRequest, 
  EnhancementResult, 
  EnhancementType, 
  EnhancementParameters,
  EnhancementModel,
  QualityAssessment,
  EnhancementRecommendation,
  createImageEnhancementEngine,
  EnhancementEngineConfig
} from '@/lib/ai/image-enhancement-engine';

import { 
  EnhancementPipelineManager,
  PipelineConfig,
  PipelineExecution,
  PipelinePreset,
  PipelineMetrics
} from '@/lib/ai/enhancement-pipeline-manager';

import { MultiProviderService } from '@/lib/ai/multi-provider-service';
import { useToast } from '@/hooks/use-toast';

export interface UseImageEnhancementOptions {
  enableCaching?: boolean;
  enableMetrics?: boolean;
  autoOptimize?: boolean;
  maxConcurrentEnhancements?: number;
  qualityThreshold?: number;
  budgetLimits?: {
    daily: number;
    monthly: number;
  };
}

export interface EnhancementHistory {
  id: string;
  timestamp: Date;
  originalImage: ImageData | string;
  enhancedImage: ImageData | string;
  enhancementType: EnhancementType;
  parameters: EnhancementParameters;
  model: string;
  result: EnhancementResult;
  processingTime: number;
  cost: number;
  qualityImprovement: number;
}

export interface EnhancementSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  totalEnhancements: number;
  totalCost: number;
  totalTime: number;
  averageQualityImprovement: number;
  history: EnhancementHistory[];
}

export interface PerformanceMetrics {
  processingSpeed: number; // images per minute
  averageQualityImprovement: number;
  costEfficiency: number; // improvement per dollar
  errorRate: number;
  cacheHitRate: number;
  providerReliability: Record<string, number>;
  modelPerformance: Record<string, {
    averageTime: number;
    averageCost: number;
    averageQuality: number;
    successRate: number;
  }>;
}

export interface UseImageEnhancementReturn {
  // Core state
  currentImage: ImageData | string | null;
  enhancedImage: ImageData | string | null;
  isProcessing: boolean;
  progress: number;
  error: string | null;
  
  // Enhancement configuration
  enhancementType: EnhancementType;
  setEnhancementType: (type: EnhancementType) => void;
  parameters: EnhancementParameters;
  setParameters: (params: EnhancementParameters) => void;
  
  // Models and providers
  availableModels: EnhancementModel[];
  selectedModel: string | null;
  setSelectedModel: (model: string | null) => void;
  
  // Quality assessment
  qualityAssessment: QualityAssessment | null;
  recommendations: EnhancementRecommendation[];
  
  // Pipeline management
  presets: PipelinePreset[];
  selectedPreset: PipelinePreset | null;
  setSelectedPreset: (preset: PipelinePreset | null) => void;
  customConfig: PipelineConfig | null;
  setCustomConfig: (config: PipelineConfig | null) => void;
  
  // Execution management
  activeExecution: PipelineExecution | null;
  executionQueue: PipelineExecution[];
  
  // History and sessions
  history: EnhancementHistory[];
  currentSession: EnhancementSession;
  
  // Performance metrics
  metrics: PipelineMetrics;
  performanceMetrics: PerformanceMetrics;
  
  // Actions
  enhanceImage: (
    image: ImageData | string,
    options?: {
      enhancementType?: EnhancementType;
      parameters?: EnhancementParameters;
      model?: string;
      progressCallback?: (progress: number, message?: string) => void;
    }
  ) => Promise<EnhancementResult>;
  
  executePipeline: (
    image: ImageData | string,
    config: PipelineConfig,
    options?: {
      progressCallback?: (progress: number, stage?: string, message?: string) => void;
      priority?: 'high' | 'normal' | 'low';
    }
  ) => Promise<PipelineExecution>;
  
  cancelExecution: (executionId?: string) => boolean;
  
  // Utility functions
  assessImageQuality: (image: ImageData | string) => Promise<QualityAssessment>;
  getRecommendations: (image: ImageData | string) => Promise<EnhancementRecommendation[]>;
  optimizeParameters: (
    image: ImageData | string,
    type: EnhancementType
  ) => Promise<EnhancementParameters>;
  
  compareResults: (
    original: ImageData | string,
    enhanced: ImageData | string
  ) => Promise<{
    qualityImprovement: number;
    differences: Array<{
      metric: string;
      improvement: number;
      description: string;
    }>;
  }>;
  
  exportResult: (
    format: 'png' | 'jpg' | 'webp',
    quality?: number
  ) => Promise<string>;
  
  // State management
  resetEnhancement: () => void;
  clearHistory: () => void;
  saveSession: () => void;
  loadSession: (sessionId: string) => Promise<void>;
  
  // Caching
  clearCache: () => void;
  getCacheStats: () => {
    size: number;
    hits: number;
    misses: number;
    hitRate: number;
  };
  
  // Configuration
  updateConfig: (config: Partial<UseImageEnhancementOptions>) => void;
  
  // Status
  isInitialized: boolean;
  initializationError: string | null;
}

export const useImageEnhancement = (
  initialImage?: ImageData | string,
  options: UseImageEnhancementOptions = {}
): UseImageEnhancementReturn => {
  const { toast } = useToast();
  
  // Core state
  const [currentImage, setCurrentImage] = useState<ImageData | string | null>(initialImage || null);
  const [enhancedImage, setEnhancedImage] = useState<ImageData | string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  
  // Enhancement configuration
  const [enhancementType, setEnhancementType] = useState<EnhancementType>(EnhancementType.DETAIL_ENHANCEMENT);
  const [parameters, setParameters] = useState<EnhancementParameters>({
    strength: 0.7,
    preserveOriginal: 0.8
  });
  
  // Models and providers
  const [availableModels, setAvailableModels] = useState<EnhancementModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  
  // Quality assessment
  const [qualityAssessment, setQualityAssessment] = useState<QualityAssessment | null>(null);
  const [recommendations, setRecommendations] = useState<EnhancementRecommendation[]>([]);
  
  // Pipeline management
  const [presets, setPresets] = useState<PipelinePreset[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<PipelinePreset | null>(null);
  const [customConfig, setCustomConfig] = useState<PipelineConfig | null>(null);
  
  // Execution management
  const [activeExecution, setActiveExecution] = useState<PipelineExecution | null>(null);
  const [executionQueue, setExecutionQueue] = useState<PipelineExecution[]>([]);
  
  // History and sessions
  const [history, setHistory] = useState<EnhancementHistory[]>([]);
  const [currentSession, setCurrentSession] = useState<EnhancementSession>({
    id: `session_${Date.now()}`,
    startTime: new Date(),
    totalEnhancements: 0,
    totalCost: 0,
    totalTime: 0,
    averageQualityImprovement: 0,
    history: []
  });
  
  // Performance metrics
  const [metrics, setMetrics] = useState<PipelineMetrics>({
    totalExecutions: 0,
    successRate: 0,
    averageTime: 0,
    averageCost: 0,
    popularStages: [],
    qualityImprovements: { avg: 0, min: 0, max: 0 },
    costDistribution: { low: 0, medium: 0, high: 0 },
    timeDistribution: { fast: 0, medium: 0, slow: 0 }
  });
  
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    processingSpeed: 0,
    averageQualityImprovement: 0,
    costEfficiency: 0,
    errorRate: 0,
    cacheHitRate: 0,
    providerReliability: {},
    modelPerformance: {}
  });
  
  // Internal state
  const [isInitialized, setIsInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [config, setConfig] = useState<UseImageEnhancementOptions>({
    enableCaching: true,
    enableMetrics: true,
    autoOptimize: true,
    maxConcurrentEnhancements: 3,
    qualityThreshold: 0.8,
    budgetLimits: {
      daily: 10,
      monthly: 100
    },
    ...options
  });
  
  // Services
  const enhancementEngineRef = useRef<ImageEnhancementEngine | null>(null);
  const pipelineManagerRef = useRef<EnhancementPipelineManager | null>(null);
  const multiProviderServiceRef = useRef<MultiProviderService | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Initialize services
  useEffect(() => {
    const initializeServices = async () => {
      try {
        setIsInitialized(false);
        setInitializationError(null);
        
        // Initialize multi-provider service
        const multiProviderService = new MultiProviderService({
          providers: {
            stabilityAI: {
              apiKey: process.env.NEXT_PUBLIC_STABILITY_AI_API_KEY || ''
            },
            dalle: {
              apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || ''
            },
            replicate: {
              apiKey: process.env.NEXT_PUBLIC_REPLICATE_API_KEY || ''
            }
          },
          defaultStrategy: {
            priority: 'balanced',
            fallbackEnabled: true,
            budgetLimits: config.budgetLimits
          },
          enableCaching: config.enableCaching,
          enableLearning: true
        });
        
        await multiProviderService.initialize();
        multiProviderServiceRef.current = multiProviderService;
        
        // Initialize enhancement engine
        const enhancementEngineConfig: EnhancementEngineConfig = {
          providers: multiProviderService,
          enableCaching: config.enableCaching,
          maxConcurrentEnhancements: config.maxConcurrentEnhancements,
          defaultQuality: 'balanced',
          performanceMonitoring: config.enableMetrics
        };
        
        const enhancementEngine = createImageEnhancementEngine(enhancementEngineConfig);
        await enhancementEngine.initialize();
        enhancementEngineRef.current = enhancementEngine;
        
        // Initialize pipeline manager
        const pipelineManager = new EnhancementPipelineManager(
          enhancementEngine,
          multiProviderService
        );
        await pipelineManager.initialize();
        pipelineManagerRef.current = pipelineManager;
        
        // Load available models
        const models = enhancementEngine.getAvailableModels();
        setAvailableModels(models);
        
        // Select default model
        if (models.length > 0 && !selectedModel) {
          setSelectedModel(models[0].id);
        }
        
        // Load presets
        const loadedPresets = pipelineManager.getPresets();
        setPresets(loadedPresets);
        
        // Setup event listeners
        setupEventListeners(enhancementEngine, pipelineManager);
        
        setIsInitialized(true);
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize enhancement services';
        setInitializationError(errorMessage);
        setError(errorMessage);
        
        toast({
          title: 'Initialization Error',
          description: errorMessage,
          variant: 'destructive'
        });
      }
    };
    
    initializeServices();
    
    return () => {
      // Cleanup
      enhancementEngineRef.current?.dispose();
      pipelineManagerRef.current?.dispose();
      multiProviderServiceRef.current?.dispose();
    };
  }, []);
  
  // Setup event listeners
  const setupEventListeners = useCallback((
    enhancementEngine: ImageEnhancementEngine,
    pipelineManager: EnhancementPipelineManager
  ) => {
    // Enhancement engine events
    enhancementEngine.on('enhancement:started', (data) => {
      setIsProcessing(true);
      setProgress(0);
      setError(null);
    });
    
    enhancementEngine.on('enhancement:progress', (data) => {
      setProgress(data.progress || 0);
    });
    
    enhancementEngine.on('enhancement:completed', (data) => {
      setIsProcessing(false);
      setProgress(100);
      
      if (data.result.success) {
        setEnhancedImage(data.result.enhancedImage!);
        
        // Update history
        const historyEntry: EnhancementHistory = {
          id: `history_${Date.now()}`,
          timestamp: new Date(),
          originalImage: data.result.originalImage!,
          enhancedImage: data.result.enhancedImage!,
          enhancementType,
          parameters,
          model: selectedModel || 'unknown',
          result: data.result,
          processingTime: data.result.processingTime || 0,
          cost: data.result.cost || 0,
          qualityImprovement: data.result.qualityImprovement?.overallScore || 0
        };
        
        setHistory(prev => [historyEntry, ...prev.slice(0, 99)]);
        
        // Update session
        setCurrentSession(prev => ({
          ...prev,
          totalEnhancements: prev.totalEnhancements + 1,
          totalCost: prev.totalCost + historyEntry.cost,
          totalTime: prev.totalTime + historyEntry.processingTime,
          averageQualityImprovement: (prev.averageQualityImprovement * prev.totalEnhancements + historyEntry.qualityImprovement) / (prev.totalEnhancements + 1),
          history: [historyEntry, ...prev.history]
        }));
        
        toast({
          title: 'Enhancement Complete',
          description: `Image enhanced successfully with ${Math.round(historyEntry.qualityImprovement * 100)}% quality improvement`
        });
      } else {
        setError(data.result.error || 'Enhancement failed');
        toast({
          title: 'Enhancement Failed',
          description: data.result.error || 'Unknown error occurred',
          variant: 'destructive'
        });
      }
    });
    
    enhancementEngine.on('enhancement:failed', (data) => {
      setIsProcessing(false);
      setProgress(0);
      setError(data.error);
      
      toast({
        title: 'Enhancement Failed',
        description: data.error,
        variant: 'destructive'
      });
    });
    
    // Pipeline manager events
    pipelineManager.on('execution:started', (data) => {
      setIsProcessing(true);
      setProgress(0);
      setError(null);
    });
    
    pipelineManager.on('execution:progress', (data) => {
      setProgress(data.progress || 0);
    });
    
    pipelineManager.on('execution:completed', (data) => {
      setIsProcessing(false);
      setProgress(100);
      
      if (data.result?.success) {
        setEnhancedImage(data.result.enhancedImage!);
        
        toast({
          title: 'Pipeline Complete',
          description: 'Image enhancement pipeline completed successfully'
        });
      }
      
      setActiveExecution(null);
    });
    
    pipelineManager.on('execution:failed', (data) => {
      setIsProcessing(false);
      setProgress(0);
      setError(data.error);
      setActiveExecution(null);
      
      toast({
        title: 'Pipeline Failed',
        description: data.error,
        variant: 'destructive'
      });
    });
    
    pipelineManager.on('metrics:updated', (newMetrics) => {
      setMetrics(newMetrics);
    });
    
    // Update performance metrics periodically
    const updatePerformanceMetrics = () => {
      const engineMetrics = enhancementEngine.getPerformanceMetrics();
      const pipelineMetrics = pipelineManager.getMetrics();
      
      setPerformanceMetrics({
        processingSpeed: pipelineMetrics.totalExecutions / (Date.now() - currentSession.startTime.getTime()) * 60000,
        averageQualityImprovement: pipelineMetrics.qualityImprovements.avg,
        costEfficiency: pipelineMetrics.qualityImprovements.avg / (pipelineMetrics.averageCost || 1),
        errorRate: 1 - pipelineMetrics.successRate,
        cacheHitRate: 0.75, // TODO: Get from cache stats
        providerReliability: {},
        modelPerformance: {}
      });
    };
    
    const performanceInterval = setInterval(updatePerformanceMetrics, 30000);
    
    return () => {
      clearInterval(performanceInterval);
    };
  }, [enhancementType, parameters, selectedModel, currentSession.startTime, toast]);
  
  // Assess image quality when current image changes
  useEffect(() => {
    if (currentImage && enhancementEngineRef.current) {
      assessImageQuality(currentImage).then(assessment => {
        setQualityAssessment(assessment);
        
        // Get recommendations
        getRecommendations(currentImage).then(setRecommendations);
      }).catch(error => {
        console.error('Quality assessment failed:', error);
      });
    }
  }, [currentImage]);
  
  // Auto-optimize parameters based on quality assessment
  useEffect(() => {
    if (config.autoOptimize && qualityAssessment && currentImage) {
      optimizeParameters(currentImage, enhancementType).then(optimized => {
        setParameters(prev => ({ ...prev, ...optimized }));
      }).catch(error => {
        console.error('Parameter optimization failed:', error);
      });
    }
  }, [qualityAssessment, enhancementType, config.autoOptimize, currentImage]);
  
  // Core enhancement function
  const enhanceImage = useCallback(async (
    image: ImageData | string,
    options: {
      enhancementType?: EnhancementType;
      parameters?: EnhancementParameters;
      model?: string;
      progressCallback?: (progress: number, message?: string) => void;
    } = {}
  ): Promise<EnhancementResult> => {
    if (!enhancementEngineRef.current) {
      throw new Error('Enhancement engine not initialized');
    }
    
    // Cancel any previous enhancement
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    
    const enhancementRequest: EnhancementRequest = {
      image,
      enhancementType: options.enhancementType || enhancementType,
      parameters: options.parameters || parameters,
      model: options.model || selectedModel || undefined,
      options: {
        enableCaching: config.enableCaching,
        priorityMode: 'balanced',
        maxRetries: 3,
        timeout: 120000
      },
      progressCallback: options.progressCallback || ((progress, message) => {
        setProgress(progress);
      })
    };
    
    try {
      const result = await enhancementEngineRef.current.enhanceImage(enhancementRequest);
      return result;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Enhancement cancelled');
      }
      throw error;
    }
  }, [enhancementType, parameters, selectedModel, config.enableCaching]);
  
  // Pipeline execution function
  const executePipeline = useCallback(async (
    image: ImageData | string,
    config: PipelineConfig,
    options: {
      progressCallback?: (progress: number, stage?: string, message?: string) => void;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ): Promise<PipelineExecution> => {
    if (!pipelineManagerRef.current) {
      throw new Error('Pipeline manager not initialized');
    }
    
    const execution = await pipelineManagerRef.current.executePipeline(
      image,
      config,
      options
    );
    
    setActiveExecution(execution);
    setExecutionQueue(prev => [...prev, execution]);
    
    return execution;
  }, []);
  
  // Cancel execution
  const cancelExecution = useCallback((executionId?: string): boolean => {
    if (executionId) {
      return pipelineManagerRef.current?.cancelExecution(executionId) || false;
    }
    
    // Cancel current enhancement
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsProcessing(false);
      setProgress(0);
      return true;
    }
    
    // Cancel active pipeline execution
    if (activeExecution) {
      return pipelineManagerRef.current?.cancelExecution(activeExecution.id) || false;
    }
    
    return false;
  }, [activeExecution]);
  
  // Utility functions
  const assessImageQuality = useCallback(async (image: ImageData | string): Promise<QualityAssessment> => {
    if (!enhancementEngineRef.current) {
      throw new Error('Enhancement engine not initialized');
    }
    
    return enhancementEngineRef.current['qualityAssessor'].assessImageQuality(image);
  }, []);
  
  const getRecommendations = useCallback(async (image: ImageData | string): Promise<EnhancementRecommendation[]> => {
    if (!enhancementEngineRef.current) {
      return [];
    }
    
    try {
      const recommendations = await enhancementEngineRef.current.getModelRecommendations(image, enhancementType);
      return recommendations;
    } catch (error) {
      console.error('Failed to get recommendations:', error);
      return [];
    }
  }, [enhancementType]);
  
  const optimizeParameters = useCallback(async (
    image: ImageData | string,
    type: EnhancementType
  ): Promise<EnhancementParameters> => {
    if (!enhancementEngineRef.current) {
      return parameters;
    }
    
    try {
      const quality = await assessImageQuality(image);
      const model = availableModels.find(m => m.id === selectedModel);
      
      if (!model) {
        return parameters;
      }
      
      // This would use the private method, but we'll simulate optimization
      const optimized = { ...parameters };
      
      // Basic optimization based on quality metrics
      switch (type) {
        case EnhancementType.DENOISE:
          optimized.denoiseLevel = Math.min(quality.metrics.noise / 100 * 1.2, 0.8);
          break;
        case EnhancementType.SHARPEN:
          optimized.sharpenLevel = Math.min((100 - quality.metrics.sharpness) / 100 * 0.8, 0.7);
          break;
        case EnhancementType.COLOR_CORRECT:
          optimized.saturationAdjustment = (100 - quality.metrics.color) / 100 * 0.3;
          break;
        case EnhancementType.EXPOSURE_FIX:
          optimized.exposureAdjustment = (50 - quality.metrics.exposure) / 25;
          break;
      }
      
      return optimized;
    } catch (error) {
      console.error('Parameter optimization failed:', error);
      return parameters;
    }
  }, [parameters, selectedModel, availableModels]);
  
  const compareResults = useCallback(async (
    original: ImageData | string,
    enhanced: ImageData | string
  ): Promise<{
    qualityImprovement: number;
    differences: Array<{
      metric: string;
      improvement: number;
      description: string;
    }>;
  }> => {
    if (!enhancementEngineRef.current) {
      return { qualityImprovement: 0, differences: [] };
    }
    
    try {
      const originalQuality = await assessImageQuality(original);
      const enhancedQuality = await assessImageQuality(enhanced);
      
      const differences = [
        {
          metric: 'Sharpness',
          improvement: enhancedQuality.metrics.sharpness - originalQuality.metrics.sharpness,
          description: 'Overall image sharpness and detail clarity'
        },
        {
          metric: 'Noise',
          improvement: originalQuality.metrics.noise - enhancedQuality.metrics.noise,
          description: 'Image noise reduction (lower is better)'
        },
        {
          metric: 'Contrast',
          improvement: enhancedQuality.metrics.contrast - originalQuality.metrics.contrast,
          description: 'Image contrast and dynamic range'
        },
        {
          metric: 'Color',
          improvement: enhancedQuality.metrics.color - originalQuality.metrics.color,
          description: 'Color accuracy and vibrancy'
        }
      ];
      
      const overallImprovement = (enhancedQuality.overallScore - originalQuality.overallScore) / 100;
      
      return {
        qualityImprovement: overallImprovement,
        differences
      };
    } catch (error) {
      console.error('Result comparison failed:', error);
      return { qualityImprovement: 0, differences: [] };
    }
  }, []);
  
  const exportResult = useCallback(async (
    format: 'png' | 'jpg' | 'webp',
    quality: number = 90
  ): Promise<string> => {
    if (!enhancedImage) {
      throw new Error('No enhanced image to export');
    }
    
    // Convert to desired format
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (typeof enhancedImage === 'string') {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx?.drawImage(img, 0, 0);
          
          const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`;
          const dataUrl = canvas.toDataURL(mimeType, quality / 100);
          resolve(dataUrl);
        };
        img.onerror = reject;
        img.src = enhancedImage;
      });
    } else {
      // ImageData
      canvas.width = enhancedImage.width;
      canvas.height = enhancedImage.height;
      ctx?.putImageData(enhancedImage, 0, 0);
      
      const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`;
      return canvas.toDataURL(mimeType, quality / 100);
    }
  }, [enhancedImage]);
  
  // State management functions
  const resetEnhancement = useCallback(() => {
    setEnhancedImage(null);
    setError(null);
    setProgress(0);
    setIsProcessing(false);
    setQualityAssessment(null);
    setRecommendations([]);
    
    // Cancel any active processing
    cancelExecution();
  }, [cancelExecution]);
  
  const clearHistory = useCallback(() => {
    setHistory([]);
    setCurrentSession(prev => ({
      ...prev,
      totalEnhancements: 0,
      totalCost: 0,
      totalTime: 0,
      averageQualityImprovement: 0,
      history: []
    }));
  }, []);
  
  const saveSession = useCallback(() => {
    const sessionData = {
      ...currentSession,
      endTime: new Date()
    };
    
    localStorage.setItem(`enhancement_session_${sessionData.id}`, JSON.stringify(sessionData));
    
    toast({
      title: 'Session Saved',
      description: `Session saved with ${sessionData.totalEnhancements} enhancements`
    });
  }, [currentSession, toast]);
  
  const loadSession = useCallback(async (sessionId: string): Promise<void> => {
    const sessionData = localStorage.getItem(`enhancement_session_${sessionId}`);
    if (sessionData) {
      const session = JSON.parse(sessionData);
      setCurrentSession(session);
      setHistory(session.history || []);
      
      toast({
        title: 'Session Loaded',
        description: `Loaded session with ${session.totalEnhancements} enhancements`
      });
    }
  }, [toast]);
  
  const clearCache = useCallback(() => {
    enhancementEngineRef.current?.clearCache();
    pipelineManagerRef.current?.clearCache?.();
    
    toast({
      title: 'Cache Cleared',
      description: 'Enhancement cache has been cleared'
    });
  }, [toast]);
  
  const getCacheStats = useCallback(() => {
    // This would require access to cache internals
    return {
      size: 0,
      hits: 0,
      misses: 0,
      hitRate: 0
    };
  }, []);
  
  const updateConfig = useCallback((newConfig: Partial<UseImageEnhancementOptions>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  }, []);
  
  // Update current image when initialImage changes
  useEffect(() => {
    if (initialImage && initialImage !== currentImage) {
      setCurrentImage(initialImage);
      resetEnhancement();
    }
  }, [initialImage, currentImage, resetEnhancement]);
  
  return {
    // Core state
    currentImage,
    enhancedImage,
    isProcessing,
    progress,
    error,
    
    // Enhancement configuration
    enhancementType,
    setEnhancementType,
    parameters,
    setParameters,
    
    // Models and providers
    availableModels,
    selectedModel,
    setSelectedModel,
    
    // Quality assessment
    qualityAssessment,
    recommendations,
    
    // Pipeline management
    presets,
    selectedPreset,
    setSelectedPreset,
    customConfig,
    setCustomConfig,
    
    // Execution management
    activeExecution,
    executionQueue,
    
    // History and sessions
    history,
    currentSession,
    
    // Performance metrics
    metrics,
    performanceMetrics,
    
    // Actions
    enhanceImage,
    executePipeline,
    cancelExecution,
    
    // Utility functions
    assessImageQuality,
    getRecommendations,
    optimizeParameters,
    compareResults,
    exportResult,
    
    // State management
    resetEnhancement,
    clearHistory,
    saveSession,
    loadSession,
    
    // Caching
    clearCache,
    getCacheStats,
    
    // Configuration
    updateConfig,
    
    // Status
    isInitialized,
    initializationError
  };
};

export default useImageEnhancement;