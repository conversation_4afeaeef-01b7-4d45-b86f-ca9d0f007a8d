import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { 
  ExportEngine, 
  ExportFormat, 
  ExportOptions, 
  ExportResult, 
  ExportProgress,
  ExportHistoryEntry,
  ExportPerformanceMetrics,
  BatchExportConfig
} from '../lib/export/export-engine';
import { 
  OptimizationPipeline, 
  OptimizationConfig, 
  OptimizationResult,
  OptimizationMetrics,
  OptimizationStrategy,
  QualityAssessment,
  AdaptiveQualityConfig
} from '../lib/export/optimization-pipeline';
import { fabric } from 'fabric';

// Hook configuration
export interface ExportOptimizationConfig {
  enableAutoOptimization?: boolean;
  enablePreviewGeneration?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableAdaptiveQuality?: boolean;
  cacheSize?: number;
  maxConcurrentExports?: number;
  defaultStrategy?: OptimizationStrategy;
  enableErrorRecovery?: boolean;
}

// Export state
export interface ExportState {
  isExporting: boolean;
  isOptimizing: boolean;
  isBatchExporting: boolean;
  isGeneratingPreview: boolean;
  progress: ExportProgress | null;
  currentOperation: string;
  error: string | null;
}

// Preview data
export interface PreviewData {
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  qualityScore: number;
  previewUrl: string | null;
  isStale: boolean;
  lastGenerated: number;
}

// Batch export state
export interface BatchExportState {
  isActive: boolean;
  progress: number;
  currentFile: number;
  totalFiles: number;
  results: ExportResult[];
  errors: string[];
  estimatedTimeRemaining: number;
}

// Performance monitoring
export interface PerformanceMonitor {
  exportMetrics: ExportPerformanceMetrics;
  optimizationMetrics: OptimizationMetrics;
  memoryUsage: number;
  processingTime: number;
  cacheHitRate: number;
  errorRate: number;
}

// Quality monitoring
export interface QualityMonitor {
  currentAssessment: QualityAssessment | null;
  averageQuality: number;
  qualityTrend: number[];
  recommendations: string[];
  warnings: string[];
}

// Hook return type
export interface ExportOptimizationHookReturn {
  // Core functionality
  exportEngine: ExportEngine | null;
  optimizationPipeline: OptimizationPipeline | null;
  
  // Export operations
  exportCanvas: (options: ExportOptions) => Promise<ExportResult>;
  batchExport: (config: BatchExportConfig) => Promise<ExportResult[]>;
  downloadExport: (result: ExportResult, filename?: string) => Promise<void>;
  
  // Optimization operations
  optimizeExport: (result: ExportResult, config: OptimizationConfig) => Promise<OptimizationResult>;
  assessQuality: (data: Blob, format: ExportFormat) => Promise<QualityAssessment>;
  getAdaptiveSettings: (config: AdaptiveQualityConfig, format: ExportFormat) => OptimizationConfig;
  
  // Preview operations
  generatePreview: (options: ExportOptions) => Promise<void>;
  clearPreview: () => void;
  
  // State management
  exportState: ExportState;
  previewData: PreviewData;
  batchExportState: BatchExportState;
  
  // History and monitoring
  exportHistory: ExportHistoryEntry[];
  performanceMonitor: PerformanceMonitor;
  qualityMonitor: QualityMonitor;
  
  // Presets and configuration
  getExportPresets: () => Record<string, ExportOptions>;
  getOptimizationPresets: () => Record<string, OptimizationConfig>;
  applyPreset: (presetName: string) => void;
  
  // Utilities
  formatFileSize: (bytes: number) => string;
  formatProgress: (progress: number) => string;
  clearHistory: () => void;
  clearCache: () => void;
  
  // Settings
  updateConfig: (config: Partial<ExportOptimizationConfig>) => void;
  
  // Cleanup
  dispose: () => void;
}

// Performance optimization hook
function usePerformanceMonitoring(
  exportEngine: ExportEngine | null,
  optimizationPipeline: OptimizationPipeline | null
) {
  const [performanceMonitor, setPerformanceMonitor] = useState<PerformanceMonitor>({
    exportMetrics: {
      totalExports: 0,
      successRate: 0,
      averageExportTime: 0,
      totalDataExported: 0,
      formatUsageStats: {},
      compressionEfficiency: 0,
      cacheHitRate: 0
    },
    optimizationMetrics: {
      totalOptimizations: 0,
      averageCompressionRatio: 0,
      averageProcessingTime: 0,
      averageQualityScore: 0,
      strategyUsage: {},
      algorithmEfficiency: {},
      cacheHitRate: 0,
      errorRate: 0
    },
    memoryUsage: 0,
    processingTime: 0,
    cacheHitRate: 0,
    errorRate: 0
  });

  const updateIntervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!exportEngine || !optimizationPipeline) return;

    const updateMetrics = () => {
      const exportMetrics = exportEngine.getPerformanceMetrics();
      const optimizationMetrics = optimizationPipeline.getMetrics();
      
      setPerformanceMonitor(prev => ({
        ...prev,
        exportMetrics,
        optimizationMetrics,
        memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
        processingTime: Date.now() - (prev.processingTime || Date.now()),
        cacheHitRate: (exportMetrics.cacheHitRate + optimizationMetrics.cacheHitRate) / 2,
        errorRate: optimizationMetrics.errorRate
      }));
    };

    // Update metrics every 5 seconds
    updateIntervalRef.current = setInterval(updateMetrics, 5000);
    
    // Initial update
    updateMetrics();

    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [exportEngine, optimizationPipeline]);

  return performanceMonitor;
}

// Quality monitoring hook
function useQualityMonitoring(optimizationPipeline: OptimizationPipeline | null) {
  const [qualityMonitor, setQualityMonitor] = useState<QualityMonitor>({
    currentAssessment: null,
    averageQuality: 0,
    qualityTrend: [],
    recommendations: [],
    warnings: []
  });

  const updateQuality = useCallback(async (data: Blob, format: ExportFormat) => {
    if (!optimizationPipeline) return;

    try {
      const assessment = await optimizationPipeline.assessQuality(data, format);
      
      setQualityMonitor(prev => {
        const newTrend = [...prev.qualityTrend, assessment.score].slice(-10);
        const averageQuality = newTrend.reduce((sum, score) => sum + score, 0) / newTrend.length;
        
        return {
          currentAssessment: assessment,
          averageQuality,
          qualityTrend: newTrend,
          recommendations: assessment.recommendations,
          warnings: assessment.warnings
        };
      });
    } catch (error) {
      console.error('Quality assessment failed:', error);
    }
  }, [optimizationPipeline]);

  return { qualityMonitor, updateQuality };
}

// Preview generation hook
function usePreviewGeneration(
  exportEngine: ExportEngine | null,
  optimizationPipeline: OptimizationPipeline | null,
  config: ExportOptimizationConfig
) {
  const [previewData, setPreviewData] = useState<PreviewData>({
    originalSize: 0,
    optimizedSize: 0,
    compressionRatio: 0,
    qualityScore: 0,
    previewUrl: null,
    isStale: false,
    lastGenerated: 0
  });

  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const previewGenerationRef = useRef<NodeJS.Timeout>();

  const generatePreview = useCallback(async (options: ExportOptions) => {
    if (!exportEngine || !optimizationPipeline || !config.enablePreviewGeneration) return;

    setIsGeneratingPreview(true);
    setPreviewData(prev => ({ ...prev, isStale: true }));

    try {
      // Generate export
      const result = await exportEngine.exportCanvas(options);
      
      if (result.success && result.data) {
        let finalResult = result;
        
        // Apply optimization if enabled
        if (config.enableAutoOptimization) {
          const optimizationConfig: OptimizationConfig = {
            strategy: config.defaultStrategy || 'balanced',
            enableAdaptiveQuality: config.enableAdaptiveQuality,
            enableProgressiveLoading: true
          };
          
          const optimization = await optimizationPipeline.optimize(result, optimizationConfig);
          finalResult = { ...result, metadata: { ...result.metadata, size: optimization.optimizedSize } };
        }
        
        // Assess quality
        const qualityAssessment = await optimizationPipeline.assessQuality(finalResult.data!, options.format);
        
        setPreviewData({
          originalSize: result.metadata.size,
          optimizedSize: finalResult.metadata.size,
          compressionRatio: finalResult.metadata.compressionRatio,
          qualityScore: qualityAssessment.score,
          previewUrl: finalResult.url || null,
          isStale: false,
          lastGenerated: Date.now()
        });
      }
    } catch (error) {
      console.error('Preview generation failed:', error);
      setPreviewData(prev => ({ ...prev, isStale: false }));
    } finally {
      setIsGeneratingPreview(false);
    }
  }, [exportEngine, optimizationPipeline, config]);

  const clearPreview = useCallback(() => {
    setPreviewData({
      originalSize: 0,
      optimizedSize: 0,
      compressionRatio: 0,
      qualityScore: 0,
      previewUrl: null,
      isStale: false,
      lastGenerated: 0
    });
  }, []);

  // Debounced preview generation
  const debouncedGeneratePreview = useCallback((options: ExportOptions) => {
    if (previewGenerationRef.current) {
      clearTimeout(previewGenerationRef.current);
    }
    
    previewGenerationRef.current = setTimeout(() => {
      generatePreview(options);
    }, 1000);
  }, [generatePreview]);

  return {
    previewData,
    isGeneratingPreview,
    generatePreview: debouncedGeneratePreview,
    clearPreview
  };
}

// Main export optimization hook
export function useExportOptimization(
  canvas: fabric.Canvas | null,
  config: ExportOptimizationConfig = {}
): ExportOptimizationHookReturn {
  // Default configuration
  const defaultConfig: ExportOptimizationConfig = {
    enableAutoOptimization: true,
    enablePreviewGeneration: true,
    enablePerformanceMonitoring: true,
    enableAdaptiveQuality: true,
    cacheSize: 50 * 1024 * 1024, // 50MB
    maxConcurrentExports: 3,
    defaultStrategy: 'balanced',
    enableErrorRecovery: true,
    ...config
  };

  // Core engines
  const [exportEngine, setExportEngine] = useState<ExportEngine | null>(null);
  const [optimizationPipeline, setOptimizationPipeline] = useState<OptimizationPipeline | null>(null);
  
  // State management
  const [exportState, setExportState] = useState<ExportState>({
    isExporting: false,
    isOptimizing: false,
    isBatchExporting: false,
    isGeneratingPreview: false,
    progress: null,
    currentOperation: '',
    error: null
  });

  const [batchExportState, setBatchExportState] = useState<BatchExportState>({
    isActive: false,
    progress: 0,
    currentFile: 0,
    totalFiles: 0,
    results: [],
    errors: [],
    estimatedTimeRemaining: 0
  });

  const [exportHistory, setExportHistory] = useState<ExportHistoryEntry[]>([]);
  const [currentConfig, setCurrentConfig] = useState<ExportOptimizationConfig>(defaultConfig);

  // Custom hooks
  const performanceMonitor = usePerformanceMonitoring(exportEngine, optimizationPipeline);
  const { qualityMonitor, updateQuality } = useQualityMonitoring(optimizationPipeline);
  const { previewData, isGeneratingPreview, generatePreview, clearPreview } = usePreviewGeneration(
    exportEngine, 
    optimizationPipeline, 
    currentConfig
  );

  // Initialize engines
  useEffect(() => {
    if (canvas) {
      const engine = new ExportEngine(canvas);
      const pipeline = new OptimizationPipeline();
      
      setExportEngine(engine);
      setOptimizationPipeline(pipeline);
      
      // Set up event listeners
      engine.on('export-progress', (data: { exportId: string; progress: ExportProgress }) => {
        setExportState(prev => ({
          ...prev,
          progress: data.progress,
          currentOperation: data.progress.message
        }));
      });

      // Load initial history
      const history = engine.getExportHistory();
      setExportHistory(history);

      return () => {
        engine.dispose();
        pipeline.dispose();
      };
    }
  }, [canvas]);

  // Update export state based on internal state
  useEffect(() => {
    setExportState(prev => ({
      ...prev,
      isGeneratingPreview
    }));
  }, [isGeneratingPreview]);

  // Export operations
  const exportCanvas = useCallback(async (options: ExportOptions): Promise<ExportResult> => {
    if (!exportEngine) {
      throw new Error('Export engine not initialized');
    }

    setExportState(prev => ({ ...prev, isExporting: true, error: null }));

    try {
      const result = await exportEngine.exportCanvas(options);
      
      // Apply optimization if enabled
      if (currentConfig.enableAutoOptimization && optimizationPipeline) {
        setExportState(prev => ({ ...prev, isOptimizing: true }));
        
        const optimizationConfig: OptimizationConfig = {
          strategy: currentConfig.defaultStrategy || 'balanced',
          enableAdaptiveQuality: currentConfig.enableAdaptiveQuality,
          enableProgressiveLoading: true
        };
        
        await optimizationPipeline.optimize(result, optimizationConfig);
        
        setExportState(prev => ({ ...prev, isOptimizing: false }));
      }

      // Update quality monitoring
      if (result.success && result.data) {
        await updateQuality(result.data, options.format);
      }

      // Update history
      const history = exportEngine.getExportHistory();
      setExportHistory(history);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Export failed';
      setExportState(prev => ({ ...prev, error: errorMessage }));
      throw error;
    } finally {
      setExportState(prev => ({ 
        ...prev, 
        isExporting: false, 
        isOptimizing: false, 
        progress: null,
        currentOperation: ''
      }));
    }
  }, [exportEngine, optimizationPipeline, currentConfig, updateQuality]);

  const batchExport = useCallback(async (config: BatchExportConfig): Promise<ExportResult[]> => {
    if (!exportEngine) {
      throw new Error('Export engine not initialized');
    }

    const totalFiles = config.formats.length * config.sizes.length;
    
    setBatchExportState({
      isActive: true,
      progress: 0,
      currentFile: 0,
      totalFiles,
      results: [],
      errors: [],
      estimatedTimeRemaining: 0
    });

    setExportState(prev => ({ ...prev, isBatchExporting: true, error: null }));

    try {
      const startTime = Date.now();
      const results = await exportEngine.batchExport(config);
      
      // Update batch state with results
      setBatchExportState(prev => ({
        ...prev,
        isActive: false,
        progress: 100,
        currentFile: totalFiles,
        results,
        errors: results.filter(r => !r.success).map(r => r.error || 'Unknown error'),
        estimatedTimeRemaining: 0
      }));

      // Update history
      const history = exportEngine.getExportHistory();
      setExportHistory(history);

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Batch export failed';
      setExportState(prev => ({ ...prev, error: errorMessage }));
      setBatchExportState(prev => ({
        ...prev,
        isActive: false,
        errors: [...prev.errors, errorMessage]
      }));
      throw error;
    } finally {
      setExportState(prev => ({ 
        ...prev, 
        isBatchExporting: false,
        progress: null,
        currentOperation: ''
      }));
    }
  }, [exportEngine]);

  const downloadExport = useCallback(async (result: ExportResult, filename?: string): Promise<void> => {
    if (!exportEngine) {
      throw new Error('Export engine not initialized');
    }

    await exportEngine.downloadExport(result, filename);
  }, [exportEngine]);

  const optimizeExport = useCallback(async (
    result: ExportResult, 
    config: OptimizationConfig
  ): Promise<OptimizationResult> => {
    if (!optimizationPipeline) {
      throw new Error('Optimization pipeline not initialized');
    }

    setExportState(prev => ({ ...prev, isOptimizing: true, error: null }));

    try {
      const optimizationResult = await optimizationPipeline.optimize(result, config);
      
      // Update quality monitoring
      if (result.data) {
        await updateQuality(result.data, result.metadata.format);
      }

      return optimizationResult;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Optimization failed';
      setExportState(prev => ({ ...prev, error: errorMessage }));
      throw error;
    } finally {
      setExportState(prev => ({ ...prev, isOptimizing: false }));
    }
  }, [optimizationPipeline, updateQuality]);

  const assessQuality = useCallback(async (
    data: Blob, 
    format: ExportFormat
  ): Promise<QualityAssessment> => {
    if (!optimizationPipeline) {
      throw new Error('Optimization pipeline not initialized');
    }

    const assessment = await optimizationPipeline.assessQuality(data, format);
    await updateQuality(data, format);
    return assessment;
  }, [optimizationPipeline, updateQuality]);

  const getAdaptiveSettings = useCallback((
    adaptiveConfig: AdaptiveQualityConfig, 
    format: ExportFormat
  ): OptimizationConfig => {
    if (!optimizationPipeline) {
      throw new Error('Optimization pipeline not initialized');
    }

    return optimizationPipeline.getAdaptiveQualitySettings(adaptiveConfig, format);
  }, [optimizationPipeline]);

  // Preset management
  const getExportPresets = useCallback(() => {
    if (!exportEngine) return {};
    return exportEngine.getExportPresets();
  }, [exportEngine]);

  const getOptimizationPresets = useCallback(() => {
    if (!optimizationPipeline) return {};
    return optimizationPipeline.getOptimizationPresets();
  }, [optimizationPipeline]);

  const applyPreset = useCallback((presetName: string) => {
    const exportPresets = getExportPresets();
    const optimizationPresets = getOptimizationPresets();
    
    if (exportPresets[presetName]) {
      // Apply export preset
      // This would typically be handled by the UI component
    }
    
    if (optimizationPresets[presetName]) {
      // Apply optimization preset
      // This would typically be handled by the UI component
    }
  }, [getExportPresets, getOptimizationPresets]);

  // Utility functions
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  const formatProgress = useCallback((progress: number): string => {
    return `${Math.round(progress)}%`;
  }, []);

  const clearHistory = useCallback(() => {
    if (exportEngine) {
      exportEngine.clearExportHistory();
      setExportHistory([]);
    }
  }, [exportEngine]);

  const clearCache = useCallback(() => {
    if (optimizationPipeline) {
      optimizationPipeline.clearCache();
    }
  }, [optimizationPipeline]);

  // Configuration management
  const updateConfig = useCallback((newConfig: Partial<ExportOptimizationConfig>) => {
    setCurrentConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  // Cleanup
  const dispose = useCallback(() => {
    if (exportEngine) {
      exportEngine.dispose();
    }
    if (optimizationPipeline) {
      optimizationPipeline.dispose();
    }
  }, [exportEngine, optimizationPipeline]);

  // Memoized return value
  return useMemo(() => ({
    // Core functionality
    exportEngine,
    optimizationPipeline,
    
    // Export operations
    exportCanvas,
    batchExport,
    downloadExport,
    
    // Optimization operations
    optimizeExport,
    assessQuality,
    getAdaptiveSettings,
    
    // Preview operations
    generatePreview,
    clearPreview,
    
    // State management
    exportState,
    previewData,
    batchExportState,
    
    // History and monitoring
    exportHistory,
    performanceMonitor,
    qualityMonitor,
    
    // Presets and configuration
    getExportPresets,
    getOptimizationPresets,
    applyPreset,
    
    // Utilities
    formatFileSize,
    formatProgress,
    clearHistory,
    clearCache,
    
    // Settings
    updateConfig,
    
    // Cleanup
    dispose
  }), [
    exportEngine,
    optimizationPipeline,
    exportCanvas,
    batchExport,
    downloadExport,
    optimizeExport,
    assessQuality,
    getAdaptiveSettings,
    generatePreview,
    clearPreview,
    exportState,
    previewData,
    batchExportState,
    exportHistory,
    performanceMonitor,
    qualityMonitor,
    getExportPresets,
    getOptimizationPresets,
    applyPreset,
    formatFileSize,
    formatProgress,
    clearHistory,
    clearCache,
    updateConfig,
    dispose
  ]);
}

// Additional utility hooks

// Hook for managing export templates
export function useExportTemplates() {
  const [templates, setTemplates] = useState<Record<string, ExportOptions>>({});

  const saveTemplate = useCallback((name: string, options: ExportOptions) => {
    setTemplates(prev => ({ ...prev, [name]: options }));
  }, []);

  const loadTemplate = useCallback((name: string): ExportOptions | null => {
    return templates[name] || null;
  }, [templates]);

  const deleteTemplate = useCallback((name: string) => {
    setTemplates(prev => {
      const newTemplates = { ...prev };
      delete newTemplates[name];
      return newTemplates;
    });
  }, []);

  return {
    templates,
    saveTemplate,
    loadTemplate,
    deleteTemplate
  };
}

// Hook for managing export queues
export function useExportQueue() {
  const [queue, setQueue] = useState<ExportOptions[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const addToQueue = useCallback((options: ExportOptions) => {
    setQueue(prev => [...prev, options]);
  }, []);

  const removeFromQueue = useCallback((index: number) => {
    setQueue(prev => prev.filter((_, i) => i !== index));
  }, []);

  const clearQueue = useCallback(() => {
    setQueue([]);
  }, []);

  const processQueue = useCallback(async (
    exportFunction: (options: ExportOptions) => Promise<ExportResult>
  ) => {
    if (isProcessing || queue.length === 0) return;

    setIsProcessing(true);
    
    try {
      for (const options of queue) {
        await exportFunction(options);
      }
      setQueue([]);
    } catch (error) {
      console.error('Queue processing failed:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [queue, isProcessing]);

  return {
    queue,
    isProcessing,
    addToQueue,
    removeFromQueue,
    clearQueue,
    processQueue
  };
}