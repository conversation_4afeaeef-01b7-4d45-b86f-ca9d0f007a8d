import { useEffect, useRef, useState, useCallback } from 'react';
import { EnhancedHybridCanvasManager, FabricCanvasConfig } from '../lib/canvas/enhanced-hybrid-canvas';
import { fabric } from 'fabric';

export interface UseFabricCanvasReturn {
  canvasManager: EnhancedHybridCanvasManager | null;
  canvas: fabric.Canvas | null;
  isInitialized: boolean;
  addObject: (object: fabric.Object) => void;
  removeObject: (objectId: string) => void;
  clearCanvas: () => void;
  undo: () => boolean;
  redo: () => boolean;
  canUndo: boolean;
  canRedo: boolean;
}

export function useFabricCanvas(
  canvasRef: React.RefObject<HTMLCanvasElement>,
  config: FabricCanvasConfig
): UseFabricCanvasReturn {
  const [canvasManager, setCanvasManager] = useState<EnhancedHybridCanvasManager | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  useEffect(() => {
    if (!canvasRef.current) return;

    const manager = new EnhancedHybridCanvasManager(canvasRef.current, config);
    setCanvasManager(manager);
    setIsInitialized(true);

    // Update undo/redo state
    const updateHistoryState = () => {
      const historyManager = manager.getCanvas().historyManager;
      if (historyManager) {
        setCanUndo(historyManager.canUndo());
        setCanRedo(historyManager.canRedo());
      }
    };

    manager.on('object:added', updateHistoryState);
    manager.on('object:removed', updateHistoryState);
    manager.on('object:modified', updateHistoryState);

    return () => {
      manager.dispose();
    };
  }, [canvasRef, config]);

  const addObject = useCallback((object: fabric.Object) => {
    if (canvasManager) {
      canvasManager.addObject(object, { renderImmediately: true });
    }
  }, [canvasManager]);

  const removeObject = useCallback((objectId: string) => {
    if (canvasManager) {
      canvasManager.removeObject(objectId);
    }
  }, [canvasManager]);

  const clearCanvas = useCallback(() => {
    if (canvasManager) {
      const canvas = canvasManager.getCanvas();
      canvas.clear();
      canvas.renderAll();
    }
  }, [canvasManager]);

  const undo = useCallback(() => {
    if (canvasManager) {
      const historyManager = canvasManager.getCanvas().historyManager;
      return historyManager ? historyManager.undo() : false;
    }
    return false;
  }, [canvasManager]);

  const redo = useCallback(() => {
    if (canvasManager) {
      const historyManager = canvasManager.getCanvas().historyManager;
      return historyManager ? historyManager.redo() : false;
    }
    return false;
  }, [canvasManager]);

  return {
    canvasManager,
    canvas: canvasManager?.getCanvas() || null,
    isInitialized,
    addObject,
    removeObject,
    clearCanvas,
    undo,
    redo,
    canUndo,
    canRedo
  };
}