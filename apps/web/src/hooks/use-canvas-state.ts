import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { CanvasStateManager, StateSnapshot, StateBranch } from '../lib/canvas/canvas-state-manager';
import { AutoSaveManager, AutoSaveStatus, SaveProgress, SavedProject } from '../lib/canvas/auto-save-manager';
import { StateSyncManager, SyncUser, NetworkStatus, ConflictInfo } from '../lib/canvas/state-sync-manager';
import { EnhancedHybridCanvasManager } from '../lib/canvas/enhanced-hybrid-canvas';
import { AdvancedLayerManager } from '../lib/canvas/advanced-layer-manager';

export interface CanvasStateHookConfig {
  enableAutoSave?: boolean;
  enableSync?: boolean;
  autoSaveInterval?: number;
  maxUndoSteps?: number;
  enableBranching?: boolean;
  enableCompression?: boolean;
  memoryLimit?: number;
}

export interface CanvasStateHookReturn {
  // State management
  stateManager: CanvasStateManager;
  currentSnapshot: StateSnapshot | undefined;
  canUndo: boolean;
  canRedo: boolean;
  memoryUsage: number;
  
  // State operations
  captureState: (description?: string) => StateSnapshot;
  undo: () => boolean;
  redo: () => boolean;
  
  // Branching
  branches: StateBranch[];
  currentBranch: StateBranch | undefined;
  createBranch: (name: string, fromSnapshotId?: string) => StateBranch;
  switchBranch: (branchId: string) => boolean;
  mergeBranch: (sourceBranchId: string, targetBranchId: string) => boolean;
  
  // Auto-save
  autoSaveManager: AutoSaveManager;
  autoSaveStatus: AutoSaveStatus;
  saveProgress: SaveProgress;
  saveProject: (name: string) => Promise<SavedProject | null>;
  loadProject: (projectId: string) => Promise<boolean>;
  getAllProjects: () => Promise<SavedProject[]>;
  deleteProject: (projectId: string) => Promise<boolean>;
  
  // Synchronization
  syncManager: StateSyncManager;
  isConnected: boolean;
  networkStatus: NetworkStatus;
  sessionUsers: SyncUser[];
  conflicts: ConflictInfo[];
  createSession: (name: string) => Promise<void>;
  joinSession: (sessionId: string, userName?: string) => Promise<boolean>;
  leaveSession: () => void;
  
  // Performance
  performanceReport: any;
  
  // Utilities
  isInitialized: boolean;
  error: string | null;
}

export interface StateChangeEvent {
  type: 'captured' | 'undo' | 'redo' | 'branch_created' | 'branch_switched' | 'saved' | 'loaded';
  snapshot?: StateSnapshot;
  branch?: StateBranch;
  project?: SavedProject;
  timestamp: number;
}

// Performance optimization hook for large state operations
function usePerformanceOptimization(stateManager: CanvasStateManager | null) {
  const [performanceReport, setPerformanceReport] = useState<any>({});
  const performanceMonitorRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!stateManager) return;

    // Monitor performance metrics
    performanceMonitorRef.current = setInterval(() => {
      const report = stateManager.getPerformanceReport();
      setPerformanceReport(report);
    }, 5000);

    return () => {
      if (performanceMonitorRef.current) {
        clearInterval(performanceMonitorRef.current);
      }
    };
  }, [stateManager]);

  return performanceReport;
}

// Memory management hook
function useMemoryManagement(stateManager: CanvasStateManager | null) {
  const [memoryUsage, setMemoryUsage] = useState<number>(0);

  useEffect(() => {
    if (!stateManager) return;

    const updateMemoryUsage = () => {
      setMemoryUsage(stateManager.getMemoryUsage());
    };

    // Listen to memory-related events
    stateManager.on('state:captured', updateMemoryUsage);
    stateManager.on('state:cleanup', updateMemoryUsage);
    stateManager.on('memory:optimized', updateMemoryUsage);

    // Initial update
    updateMemoryUsage();

    return () => {
      stateManager.off('state:captured', updateMemoryUsage);
      stateManager.off('state:cleanup', updateMemoryUsage);
      stateManager.off('memory:optimized', updateMemoryUsage);
    };
  }, [stateManager]);

  return memoryUsage;
}

// Auto-save status hook
function useAutoSaveStatus(autoSaveManager: AutoSaveManager | null) {
  const [autoSaveStatus, setAutoSaveStatus] = useState<AutoSaveStatus>({
    enabled: false,
    lastSaveTime: 0,
    nextSaveTime: 0,
    saveInProgress: false,
    changesSinceLastSave: 0,
    storageUsed: 0,
    storageQuota: 0
  });

  const [saveProgress, setSaveProgress] = useState<SaveProgress>({
    stage: 'complete',
    progress: 100,
    message: 'Ready'
  });

  useEffect(() => {
    if (!autoSaveManager) return;

    const updateStatus = () => {
      setAutoSaveStatus(autoSaveManager.getStatus());
    };

    const updateProgress = (progress: SaveProgress) => {
      setSaveProgress(progress);
    };

    // Listen to auto-save events
    autoSaveManager.on('auto_save:completed', updateStatus);
    autoSaveManager.on('auto_save:failed', updateStatus);
    autoSaveManager.on('save:progress', updateProgress);
    autoSaveManager.on('project:saved', updateStatus);

    // Update status every second
    const statusInterval = setInterval(updateStatus, 1000);

    // Initial update
    updateStatus();

    return () => {
      clearInterval(statusInterval);
      autoSaveManager.off('auto_save:completed', updateStatus);
      autoSaveManager.off('auto_save:failed', updateStatus);
      autoSaveManager.off('save:progress', updateProgress);
      autoSaveManager.off('project:saved', updateStatus);
    };
  }, [autoSaveManager]);

  return { autoSaveStatus, saveProgress };
}

// Synchronization status hook
function useSyncStatus(syncManager: StateSyncManager | null) {
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    connected: false,
    latency: 0,
    quality: 'poor',
    lastHeartbeat: 0,
    reconnectAttempts: 0
  });
  const [sessionUsers, setSessionUsers] = useState<SyncUser[]>([]);
  const [conflicts, setConflicts] = useState<ConflictInfo[]>([]);

  useEffect(() => {
    if (!syncManager) return;

    const updateConnectionStatus = () => {
      setIsConnected(syncManager.getNetworkStatus().connected);
      setNetworkStatus(syncManager.getNetworkStatus());
    };

    const updateSessionUsers = () => {
      setSessionUsers(syncManager.getSessionUsers());
    };

    const handleConflict = (data: { conflicts: ConflictInfo[] }) => {
      setConflicts(prev => [...prev, ...data.conflicts]);
    };

    const handleConflictResolved = (data: { conflicts: ConflictInfo[] }) => {
      setConflicts(prev => prev.filter(c => !data.conflicts.some(rc => rc.id === c.id)));
    };

    // Listen to sync events
    syncManager.on('sync:connected', updateConnectionStatus);
    syncManager.on('sync:disconnected', updateConnectionStatus);
    syncManager.on('user:joined', updateSessionUsers);
    syncManager.on('user:left', updateSessionUsers);
    syncManager.on('conflicts:manual_resolution_required', handleConflict);
    syncManager.on('conflicts:resolved', handleConflictResolved);

    // Update status every few seconds
    const statusInterval = setInterval(updateConnectionStatus, 3000);

    // Initial update
    updateConnectionStatus();
    updateSessionUsers();

    return () => {
      clearInterval(statusInterval);
      syncManager.off('sync:connected', updateConnectionStatus);
      syncManager.off('sync:disconnected', updateConnectionStatus);
      syncManager.off('user:joined', updateSessionUsers);
      syncManager.off('user:left', updateSessionUsers);
      syncManager.off('conflicts:manual_resolution_required', handleConflict);
      syncManager.off('conflicts:resolved', handleConflictResolved);
    };
  }, [syncManager]);

  return {
    isConnected,
    networkStatus,
    sessionUsers,
    conflicts
  };
}

// State history hook
function useStateHistory(stateManager: CanvasStateManager | null) {
  const [currentSnapshot, setCurrentSnapshot] = useState<StateSnapshot | undefined>();
  const [branches, setBranches] = useState<StateBranch[]>([]);
  const [currentBranch, setCurrentBranch] = useState<StateBranch | undefined>();
  const [canUndo, setCanUndo] = useState<boolean>(false);
  const [canRedo, setCanRedo] = useState<boolean>(false);

  useEffect(() => {
    if (!stateManager) return;

    const updateHistory = () => {
      setCurrentSnapshot(stateManager.getCurrentSnapshot());
      setBranches(stateManager.getAllBranches());
      setCurrentBranch(stateManager.getCurrentBranch());
      setCanUndo(stateManager.canUndo());
      setCanRedo(stateManager.canRedo());
    };

    // Listen to state events
    stateManager.on('state:captured', updateHistory);
    stateManager.on('state:undo', updateHistory);
    stateManager.on('state:redo', updateHistory);
    stateManager.on('branch:created', updateHistory);
    stateManager.on('branch:switched', updateHistory);
    stateManager.on('branch:merged', updateHistory);

    // Initial update
    updateHistory();

    return () => {
      stateManager.off('state:captured', updateHistory);
      stateManager.off('state:undo', updateHistory);
      stateManager.off('state:redo', updateHistory);
      stateManager.off('branch:created', updateHistory);
      stateManager.off('branch:switched', updateHistory);
      stateManager.off('branch:merged', updateHistory);
    };
  }, [stateManager]);

  return {
    currentSnapshot,
    branches,
    currentBranch,
    canUndo,
    canRedo
  };
}

// Main canvas state hook
export function useCanvasState(
  canvasManager: EnhancedHybridCanvasManager | null,
  layerManager: AdvancedLayerManager | null,
  config: CanvasStateHookConfig = {}
): CanvasStateHookReturn {
  // State managers
  const [stateManager, setStateManager] = useState<CanvasStateManager | null>(null);
  const [autoSaveManager, setAutoSaveManager] = useState<AutoSaveManager | null>(null);
  const [syncManager, setSyncManager] = useState<StateSyncManager | null>(null);
  
  // Initialization state
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Event tracking
  const eventListenersRef = useRef<Array<() => void>>([]);

  // Initialize managers
  useEffect(() => {
    if (!canvasManager || !layerManager) {
      setIsInitialized(false);
      return;
    }

    try {
      // Initialize state manager
      const stateManagerInstance = new CanvasStateManager(canvasManager, layerManager, {
        maxUndoSteps: config.maxUndoSteps || 50,
        enableBranching: config.enableBranching ?? true,
        enableCompression: config.enableCompression ?? true,
        memoryLimit: config.memoryLimit || 100,
        enablePerformanceOptimization: true
      });

      // Initialize auto-save manager
      const autoSaveManagerInstance = new AutoSaveManager(stateManagerInstance, {
        enabled: config.enableAutoSave ?? true,
        interval: config.autoSaveInterval || 30000,
        enableCrashRecovery: true,
        enableProgressIndication: true
      });

      // Initialize sync manager
      const syncManagerInstance = new StateSyncManager(stateManagerInstance, {
        enabled: config.enableSync ?? false,
        conflictResolutionStrategy: 'operational-transform',
        enableConflictDetection: true
      });

      setStateManager(stateManagerInstance);
      setAutoSaveManager(autoSaveManagerInstance);
      setSyncManager(syncManagerInstance);
      setIsInitialized(true);
      setError(null);

    } catch (err) {
      console.error('Failed to initialize canvas state management:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setIsInitialized(false);
    }
  }, [canvasManager, layerManager, config]);

  // Custom hooks for different aspects
  const memoryUsage = useMemoryManagement(stateManager);
  const performanceReport = usePerformanceOptimization(stateManager);
  const { autoSaveStatus, saveProgress } = useAutoSaveStatus(autoSaveManager);
  const { isConnected, networkStatus, sessionUsers, conflicts } = useSyncStatus(syncManager);
  const { currentSnapshot, branches, currentBranch, canUndo, canRedo } = useStateHistory(stateManager);

  // State operations
  const captureState = useCallback((description?: string): StateSnapshot => {
    if (!stateManager) {
      throw new Error('State manager not initialized');
    }
    return stateManager.captureCurrentState(description);
  }, [stateManager]);

  const undo = useCallback((): boolean => {
    if (!stateManager) return false;
    return stateManager.undo();
  }, [stateManager]);

  const redo = useCallback((): boolean => {
    if (!stateManager) return false;
    return stateManager.redo();
  }, [stateManager]);

  // Branching operations
  const createBranch = useCallback((name: string, fromSnapshotId?: string): StateBranch => {
    if (!stateManager) {
      throw new Error('State manager not initialized');
    }
    return stateManager.createBranch(name, fromSnapshotId);
  }, [stateManager]);

  const switchBranch = useCallback((branchId: string): boolean => {
    if (!stateManager) return false;
    return stateManager.switchToBranch(branchId);
  }, [stateManager]);

  const mergeBranch = useCallback((sourceBranchId: string, targetBranchId: string): boolean => {
    if (!stateManager) return false;
    return stateManager.mergeBranch(sourceBranchId, targetBranchId);
  }, [stateManager]);

  // Project operations
  const saveProject = useCallback(async (name: string): Promise<SavedProject | null> => {
    if (!autoSaveManager) {
      throw new Error('Auto-save manager not initialized');
    }
    return autoSaveManager.saveProject(name, true);
  }, [autoSaveManager]);

  const loadProject = useCallback(async (projectId: string): Promise<boolean> => {
    if (!autoSaveManager) return false;
    return autoSaveManager.loadProject(projectId);
  }, [autoSaveManager]);

  const getAllProjects = useCallback(async (): Promise<SavedProject[]> => {
    if (!autoSaveManager) return [];
    return autoSaveManager.getAllProjects();
  }, [autoSaveManager]);

  const deleteProject = useCallback(async (projectId: string): Promise<boolean> => {
    if (!autoSaveManager) return false;
    return autoSaveManager.deleteProject(projectId);
  }, [autoSaveManager]);

  // Synchronization operations
  const createSession = useCallback(async (name: string): Promise<void> => {
    if (!syncManager) {
      throw new Error('Sync manager not initialized');
    }
    await syncManager.createSession(name);
  }, [syncManager]);

  const joinSession = useCallback(async (sessionId: string, userName?: string): Promise<boolean> => {
    if (!syncManager) return false;
    return syncManager.joinSession(sessionId, userName);
  }, [syncManager]);

  const leaveSession = useCallback((): void => {
    if (!syncManager) return;
    syncManager.leaveSession();
  }, [syncManager]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clean up event listeners
      eventListenersRef.current.forEach(cleanup => cleanup());
      
      // Dispose managers
      if (stateManager) {
        stateManager.dispose();
      }
      if (autoSaveManager) {
        autoSaveManager.dispose();
      }
      if (syncManager) {
        syncManager.dispose();
      }
    };
  }, [stateManager, autoSaveManager, syncManager]);

  // Error handling
  useEffect(() => {
    if (!stateManager || !autoSaveManager || !syncManager) return;

    const handleStateError = (data: { error: Error }) => {
      console.error('State management error:', data.error);
      setError(data.error.message);
    };

    const handleAutoSaveError = (data: { error: Error }) => {
      console.error('Auto-save error:', data.error);
      setError(data.error.message);
    };

    const handleSyncError = (data: { error: Error }) => {
      console.error('Sync error:', data.error);
      setError(data.error.message);
    };

    const clearError = () => {
      setError(null);
    };

    // Listen to error events
    stateManager.on('state:restore:error', handleStateError);
    autoSaveManager.on('save:error', handleAutoSaveError);
    autoSaveManager.on('load:error', handleAutoSaveError);
    syncManager.on('session:join:failed', handleSyncError);

    // Listen to success events to clear errors
    stateManager.on('state:captured', clearError);
    autoSaveManager.on('project:saved', clearError);
    syncManager.on('sync:connected', clearError);

    const cleanup = () => {
      stateManager.off('state:restore:error', handleStateError);
      autoSaveManager.off('save:error', handleAutoSaveError);
      autoSaveManager.off('load:error', handleAutoSaveError);
      syncManager.off('session:join:failed', handleSyncError);
      stateManager.off('state:captured', clearError);
      autoSaveManager.off('project:saved', clearError);
      syncManager.off('sync:connected', clearError);
    };

    eventListenersRef.current.push(cleanup);

    return cleanup;
  }, [stateManager, autoSaveManager, syncManager]);

  // Memoized return value for performance
  return useMemo(() => ({
    // State management
    stateManager: stateManager!,
    currentSnapshot,
    canUndo,
    canRedo,
    memoryUsage,
    
    // State operations
    captureState,
    undo,
    redo,
    
    // Branching
    branches,
    currentBranch,
    createBranch,
    switchBranch,
    mergeBranch,
    
    // Auto-save
    autoSaveManager: autoSaveManager!,
    autoSaveStatus,
    saveProgress,
    saveProject,
    loadProject,
    getAllProjects,
    deleteProject,
    
    // Synchronization
    syncManager: syncManager!,
    isConnected,
    networkStatus,
    sessionUsers,
    conflicts,
    createSession,
    joinSession,
    leaveSession,
    
    // Performance
    performanceReport,
    
    // Utilities
    isInitialized,
    error
  }), [
    stateManager,
    currentSnapshot,
    canUndo,
    canRedo,
    memoryUsage,
    captureState,
    undo,
    redo,
    branches,
    currentBranch,
    createBranch,
    switchBranch,
    mergeBranch,
    autoSaveManager,
    autoSaveStatus,
    saveProgress,
    saveProject,
    loadProject,
    getAllProjects,
    deleteProject,
    syncManager,
    isConnected,
    networkStatus,
    sessionUsers,
    conflicts,
    createSession,
    joinSession,
    leaveSession,
    performanceReport,
    isInitialized,
    error
  ]);
}

// Additional utility hooks

// Hook for tracking state changes with debouncing
export function useStateChangeTracking(
  stateManager: CanvasStateManager | null,
  debounceMs: number = 1000
) {
  const [recentChanges, setRecentChanges] = useState<StateChangeEvent[]>([]);
  const debounceRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!stateManager) return;

    const addChange = (type: StateChangeEvent['type'], data: any = {}) => {
      const change: StateChangeEvent = {
        type,
        timestamp: Date.now(),
        ...data
      };

      clearTimeout(debounceRef.current);
      debounceRef.current = setTimeout(() => {
        setRecentChanges(prev => [...prev.slice(-9), change]);
      }, debounceMs);
    };

    stateManager.on('state:captured', (data) => addChange('captured', { snapshot: data.snapshot }));
    stateManager.on('state:undo', (data) => addChange('undo', { snapshot: data.currentSnapshot }));
    stateManager.on('state:redo', (data) => addChange('redo', { snapshot: data.currentSnapshot }));
    stateManager.on('branch:created', (data) => addChange('branch_created', { branch: data.branch }));
    stateManager.on('branch:switched', (data) => addChange('branch_switched', { branch: data.branch }));

    return () => {
      clearTimeout(debounceRef.current);
      stateManager.removeAllListeners();
    };
  }, [stateManager, debounceMs]);

  return recentChanges;
}

// Hook for managing conflict resolution UI
export function useConflictResolution(syncManager: StateSyncManager | null) {
  const [activeConflicts, setActiveConflicts] = useState<ConflictInfo[]>([]);
  const [resolutionHistory, setResolutionHistory] = useState<Array<{
    conflictId: string;
    resolution: string;
    timestamp: number;
  }>>([]);

  const resolveConflict = useCallback((conflictId: string, resolution: 'accept-local' | 'accept-remote' | 'merge') => {
    if (!syncManager) return;
    
    syncManager.resolveConflictManually(conflictId, resolution);
    
    setResolutionHistory(prev => [...prev, {
      conflictId,
      resolution,
      timestamp: Date.now()
    }]);

    setActiveConflicts(prev => prev.filter(c => c.id !== conflictId));
  }, [syncManager]);

  useEffect(() => {
    if (!syncManager) return;

    const handleNewConflicts = (data: { conflicts: ConflictInfo[] }) => {
      setActiveConflicts(prev => [...prev, ...data.conflicts]);
    };

    const handleConflictResolved = (data: { conflicts: ConflictInfo[] }) => {
      setActiveConflicts(prev => prev.filter(c => !data.conflicts.some(rc => rc.id === c.id)));
    };

    syncManager.on('conflicts:manual_resolution_required', handleNewConflicts);
    syncManager.on('conflicts:resolved', handleConflictResolved);

    return () => {
      syncManager.off('conflicts:manual_resolution_required', handleNewConflicts);
      syncManager.off('conflicts:resolved', handleConflictResolved);
    };
  }, [syncManager]);

  return {
    activeConflicts,
    resolutionHistory,
    resolveConflict
  };
}