import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { FilterPipelineEngine, FilterInstance, FilterPerformanceMetrics } from '../lib/canvas/filter-pipeline-engine';
import { FilterLibrary, FilterShader, FilterPreset } from '../lib/canvas/filter-library';
import { EffectsManager, EffectLayer, EffectGroup, EffectPreset, EffectStackState } from '../lib/canvas/effects-manager';
import { EnhancedHybridCanvasManager } from '../lib/canvas/enhanced-hybrid-canvas';

export interface FiltersEffectsHookConfig {
  enableWebGL2: boolean;
  enableFloatTextures: boolean;
  enableGPUAcceleration: boolean;
  maxFilterInstances: number;
  enableRealTimePreview: boolean;
  enableAnimations: boolean;
  enablePresetSystem: boolean;
  cacheSize: number;
}

export interface FiltersEffectsHookReturn {
  // Core managers
  pipelineEngine: FilterPipelineEngine | null;
  effectsManager: EffectsManager | null;
  filterLibrary: FilterLibrary | null;
  
  // Filter management
  availableFilters: FilterShader[];
  filtersByCategory: Record<string, FilterShader[]>;
  addFilter: (layerId: string, filterId: string, parameters?: Record<string, any>) => FilterInstance | null;
  removeFilter: (layerId: string, filterId: string) => boolean;
  updateFilterParameters: (layerId: string, filterId: string, parameters: Record<string, any>) => boolean;
  duplicateFilter: (layerId: string, filterId: string) => FilterInstance | null;
  
  // Effect layer management
  effectStack: EffectStackState;
  activeLayers: EffectLayer[];
  createLayer: (name: string, options?: Partial<EffectLayer>) => EffectLayer;
  deleteLayer: (layerId: string) => boolean;
  updateLayer: (layerId: string, updates: Partial<EffectLayer>) => boolean;
  duplicateLayer: (layerId: string) => EffectLayer | null;
  reorderLayers: (layerIds: string[]) => void;
  
  // Group management
  effectGroups: EffectGroup[];
  createGroup: (name: string, layerIds?: string[]) => EffectGroup;
  deleteGroup: (groupId: string) => boolean;
  addLayerToGroup: (groupId: string, layerId: string) => boolean;
  removeLayerFromGroup: (groupId: string, layerId: string) => boolean;
  
  // Preset management
  filterPresets: FilterPreset[];
  effectPresets: EffectPreset[];
  savePreset: (name: string, description: string, tags?: string[]) => EffectPreset;
  loadPreset: (presetId: string) => boolean;
  deletePreset: (presetId: string) => boolean;
  
  // Animation controls
  startAnimation: (layerId: string) => boolean;
  stopAnimation: (layerId: string) => boolean;
  pauseAnimation: (layerId: string) => boolean;
  resumeAnimation: (layerId: string) => boolean;
  
  // Processing and preview
  processEffects: (sourceTexture: WebGLTexture) => WebGLTexture | null;
  previewLayer: (layerId: string, sourceTexture: WebGLTexture) => WebGLTexture | null;
  previewFilter: (filterId: string, parameters: Record<string, any>, sourceTexture: WebGLTexture) => WebGLTexture | null;
  
  // Real-time controls
  isRealTimeEnabled: boolean;
  toggleRealTime: () => void;
  previewEnabled: boolean;
  setPreviewEnabled: (enabled: boolean) => void;
  
  // Performance monitoring
  performanceMetrics: FilterPerformanceMetrics;
  memoryUsage: number;
  frameRate: number;
  resetMetrics: () => void;
  
  // State and status
  isInitialized: boolean;
  isProcessing: boolean;
  error: string | null;
  canvasReady: boolean;
}

export interface FilterUpdateEvent {
  type: 'filter:added' | 'filter:removed' | 'filter:updated' | 'layer:created' | 'layer:updated' | 'layer:deleted';
  layerId?: string;
  filterId?: string;
  data?: any;
  timestamp: number;
}

// Performance monitoring hook
function usePerformanceMonitoring(pipelineEngine: FilterPipelineEngine | null) {
  const [performanceMetrics, setPerformanceMetrics] = useState<FilterPerformanceMetrics>({
    frameTime: 0,
    filterTime: 0,
    textureMemory: 0,
    shaderCompilations: 0,
    drawCalls: 0,
    primitiveCount: 0,
    averageFPS: 60,
    memoryUsage: 0
  });
  
  const [memoryUsage, setMemoryUsage] = useState<number>(0);
  const [frameRate, setFrameRate] = useState<number>(60);
  
  useEffect(() => {
    if (!pipelineEngine) return;

    const updateMetrics = () => {
      const metrics = pipelineEngine.getPerformanceMetrics();
      setPerformanceMetrics(metrics);
      setMemoryUsage(metrics.memoryUsage);
      setFrameRate(metrics.averageFPS);
    };

    // Update metrics every second
    const metricsInterval = setInterval(updateMetrics, 1000);

    // Listen to pipeline events for real-time updates
    pipelineEngine.on('pipeline:processed', updateMetrics);

    return () => {
      clearInterval(metricsInterval);
      pipelineEngine.off('pipeline:processed', updateMetrics);
    };
  }, [pipelineEngine]);

  const resetMetrics = useCallback(() => {
    if (pipelineEngine) {
      pipelineEngine.resetPerformanceMetrics();
      setPerformanceMetrics({
        frameTime: 0,
        filterTime: 0,
        textureMemory: 0,
        shaderCompilations: 0,
        drawCalls: 0,
        primitiveCount: 0,
        averageFPS: 60,
        memoryUsage: 0
      });
    }
  }, [pipelineEngine]);

  return { performanceMetrics, memoryUsage, frameRate, resetMetrics };
}

// Effect stack state management hook
function useEffectStackState(effectsManager: EffectsManager | null) {
  const [effectStack, setEffectStack] = useState<EffectStackState>({
    layers: new Map(),
    groups: new Map(),
    renderOrder: [],
    version: 1,
    globalOpacity: 1.0,
    globalBlendMode: 'normal'
  });
  
  const [activeLayers, setActiveLayers] = useState<EffectLayer[]>([]);
  const [effectGroups, setEffectGroups] = useState<EffectGroup[]>([]);

  useEffect(() => {
    if (!effectsManager) return;

    const updateStack = () => {
      const stack = effectsManager.getEffectStack();
      setEffectStack(stack);
      setActiveLayers(effectsManager.getAllLayers());
      setEffectGroups(effectsManager.getAllGroups());
    };

    // Listen to effects manager events
    effectsManager.on('layer:created', updateStack);
    effectsManager.on('layer:updated', updateStack);
    effectsManager.on('layer:deleted', updateStack);
    effectsManager.on('group:created', updateStack);
    effectsManager.on('filter:added', updateStack);
    effectsManager.on('filter:removed', updateStack);
    effectsManager.on('filter:updated', updateStack);

    // Initial update
    updateStack();

    return () => {
      effectsManager.off('layer:created', updateStack);
      effectsManager.off('layer:updated', updateStack);
      effectsManager.off('layer:deleted', updateStack);
      effectsManager.off('group:created', updateStack);
      effectsManager.off('filter:added', updateStack);
      effectsManager.off('filter:removed', updateStack);
      effectsManager.off('filter:updated', updateStack);
    };
  }, [effectsManager]);

  return { effectStack, activeLayers, effectGroups };
}

// Real-time preview management hook
function useRealTimePreview(
  effectsManager: EffectsManager | null,
  pipelineEngine: FilterPipelineEngine | null,
  enabled: boolean
) {
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const processingRef = useRef<boolean>(false);
  const requestRef = useRef<number>();

  const processFrame = useCallback(() => {
    if (!enabled || !effectsManager || !pipelineEngine || processingRef.current) {
      return;
    }

    processingRef.current = true;
    setIsProcessing(true);

    // This would integrate with the canvas to get current frame
    // and process it through the effects pipeline
    
    setTimeout(() => {
      processingRef.current = false;
      setIsProcessing(false);
    }, 16); // ~60fps
  }, [enabled, effectsManager, pipelineEngine]);

  useEffect(() => {
    if (!enabled) return;

    const animate = () => {
      processFrame();
      requestRef.current = requestAnimationFrame(animate);
    };

    requestRef.current = requestAnimationFrame(animate);

    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [enabled, processFrame]);

  return { isProcessing };
}

// Filter library management hook
function useFilterLibrary() {
  const [filterLibrary] = useState<FilterLibrary>(() => FilterLibrary.getInstance());
  const [availableFilters, setAvailableFilters] = useState<FilterShader[]>([]);
  const [filtersByCategory, setFiltersByCategory] = useState<Record<string, FilterShader[]>>({});
  const [filterPresets, setFilterPresets] = useState<FilterPreset[]>([]);

  useEffect(() => {
    const filters = filterLibrary.getAllFilters();
    setAvailableFilters(filters);

    // Organize by category
    const byCategory: Record<string, FilterShader[]> = {};
    filterLibrary.getAllCategories().forEach(category => {
      byCategory[category] = filterLibrary.getFiltersByCategory(category);
    });
    setFiltersByCategory(byCategory);

    // Load presets
    setFilterPresets(filterLibrary.getFilterPresets());
  }, [filterLibrary]);

  return { filterLibrary, availableFilters, filtersByCategory, filterPresets };
}

// Main hook
export function useFiltersEffects(
  canvasManager: EnhancedHybridCanvasManager | null,
  config: Partial<FiltersEffectsHookConfig> = {}
): FiltersEffectsHookReturn {
  const fullConfig: FiltersEffectsHookConfig = {
    enableWebGL2: true,
    enableFloatTextures: true,
    enableGPUAcceleration: true,
    maxFilterInstances: 32,
    enableRealTimePreview: true,
    enableAnimations: true,
    enablePresetSystem: true,
    cacheSize: 50,
    ...config
  };

  // Core managers
  const [pipelineEngine, setPipelineEngine] = useState<FilterPipelineEngine | null>(null);
  const [effectsManager, setEffectsManager] = useState<EffectsManager | null>(null);
  
  // State
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [canvasReady, setCanvasReady] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [effectPresets, setEffectPresets] = useState<EffectPreset[]>([]);
  
  // Real-time controls
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState<boolean>(fullConfig.enableRealTimePreview);
  const [previewEnabled, setPreviewEnabled] = useState<boolean>(true);

  // Initialize managers
  useEffect(() => {
    if (!canvasManager || !canvasManager.isReady) {
      setCanvasReady(false);
      return;
    }

    setCanvasReady(true);

    try {
      // Get canvas element from the canvas manager
      const canvas = canvasManager.fabricCanvasManager.canvas.getElement() as HTMLCanvasElement;
      
      // Initialize pipeline engine
      const pipeline = new FilterPipelineEngine(canvas, {
        enableWebGL2: fullConfig.enableWebGL2,
        enableFloatTextures: fullConfig.enableFloatTextures,
        maxFilterInstances: fullConfig.maxFilterInstances,
        texturePoolSize: fullConfig.cacheSize,
        enablePipelineOptimization: true
      });

      // Initialize effects manager
      const effects = new EffectsManager(pipeline, {
        enableGPUAcceleration: fullConfig.enableGPUAcceleration,
        useFloatPrecision: fullConfig.enableFloatTextures,
        preserveAlpha: true
      });

      setPipelineEngine(pipeline);
      setEffectsManager(effects);
      setIsInitialized(true);
      setError(null);

    } catch (err) {
      console.error('Failed to initialize filters and effects:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setIsInitialized(false);
    }
  }, [canvasManager, fullConfig]);

  // Custom hooks
  const { filterLibrary, availableFilters, filtersByCategory, filterPresets } = useFilterLibrary();
  const { performanceMetrics, memoryUsage, frameRate, resetMetrics } = usePerformanceMonitoring(pipelineEngine);
  const { effectStack, activeLayers, effectGroups } = useEffectStackState(effectsManager);
  const { isProcessing } = useRealTimePreview(effectsManager, pipelineEngine, isRealTimeEnabled && previewEnabled);

  // Filter management functions
  const addFilter = useCallback((
    layerId: string, 
    filterId: string, 
    parameters: Record<string, any> = {}
  ): FilterInstance | null => {
    if (!effectsManager) return null;
    return effectsManager.addFilterToLayer(layerId, filterId, parameters);
  }, [effectsManager]);

  const removeFilter = useCallback((layerId: string, filterId: string): boolean => {
    if (!effectsManager) return false;
    return effectsManager.removeFilterFromLayer(layerId, filterId);
  }, [effectsManager]);

  const updateFilterParameters = useCallback((
    layerId: string, 
    filterId: string, 
    parameters: Record<string, any>
  ): boolean => {
    if (!effectsManager) return false;
    return effectsManager.updateFilterParameters(layerId, filterId, parameters);
  }, [effectsManager]);

  const duplicateFilter = useCallback((layerId: string, filterId: string): FilterInstance | null => {
    // Get the current filter
    const layer = effectsManager?.getLayer(layerId);
    const filter = layer?.filters.find(f => f.id === filterId);
    
    if (!effectsManager || !filter) return null;

    // Create duplicate with new ID
    return effectsManager.addFilterToLayer(layerId, filter.shaderId, filter.parameters);
  }, [effectsManager]);

  // Layer management functions
  const createLayer = useCallback((name: string, options: Partial<EffectLayer> = {}): EffectLayer => {
    if (!effectsManager) {
      throw new Error('Effects manager not initialized');
    }
    return effectsManager.createEffectLayer(name, options);
  }, [effectsManager]);

  const deleteLayer = useCallback((layerId: string): boolean => {
    if (!effectsManager) return false;
    return effectsManager.deleteEffectLayer(layerId);
  }, [effectsManager]);

  const updateLayer = useCallback((layerId: string, updates: Partial<EffectLayer>): boolean => {
    if (!effectsManager) return false;
    return effectsManager.updateEffectLayer(layerId, updates);
  }, [effectsManager]);

  const duplicateLayer = useCallback((layerId: string): EffectLayer | null => {
    if (!effectsManager) return null;
    return effectsManager.duplicateEffectLayer(layerId);
  }, [effectsManager]);

  const reorderLayers = useCallback((layerIds: string[]): void => {
    if (!effectsManager) return;
    
    layerIds.forEach((layerId, index) => {
      effectsManager.updateEffectLayer(layerId, { order: index });
    });
  }, [effectsManager]);

  // Group management functions
  const createGroup = useCallback((name: string, layerIds: string[] = []): EffectGroup => {
    if (!effectsManager) {
      throw new Error('Effects manager not initialized');
    }
    return effectsManager.createEffectGroup(name, layerIds);
  }, [effectsManager]);

  const deleteGroup = useCallback((groupId: string): boolean => {
    // Implementation would be added to effects manager
    console.warn('Delete group not yet implemented');
    return false;
  }, []);

  const addLayerToGroup = useCallback((groupId: string, layerId: string): boolean => {
    if (!effectsManager) return false;
    return effectsManager.addLayerToGroup(groupId, layerId);
  }, [effectsManager]);

  const removeLayerFromGroup = useCallback((groupId: string, layerId: string): boolean => {
    if (!effectsManager) return false;
    return effectsManager.removeLayerFromGroup(groupId, layerId);
  }, [effectsManager]);

  // Preset management functions
  const savePreset = useCallback((name: string, description: string, tags: string[] = []): EffectPreset => {
    if (!effectsManager) {
      throw new Error('Effects manager not initialized');
    }
    const preset = effectsManager.saveEffectPreset(name, description, tags);
    setEffectPresets(prev => [...prev, preset]);
    return preset;
  }, [effectsManager]);

  const loadPreset = useCallback((presetId: string): boolean => {
    if (!effectsManager) return false;
    return effectsManager.loadEffectPreset(presetId);
  }, [effectsManager]);

  const deletePreset = useCallback((presetId: string): boolean => {
    setEffectPresets(prev => prev.filter(p => p.id !== presetId));
    return true;
  }, []);

  // Animation controls
  const startAnimation = useCallback((layerId: string): boolean => {
    if (!effectsManager) return false;
    return effectsManager.startLayerAnimation(layerId);
  }, [effectsManager]);

  const stopAnimation = useCallback((layerId: string): boolean => {
    if (!effectsManager) return false;
    return effectsManager.stopLayerAnimation(layerId);
  }, [effectsManager]);

  const pauseAnimation = useCallback((layerId: string): boolean => {
    // Implementation would be added to effects manager
    console.warn('Pause animation not yet implemented');
    return false;
  }, []);

  const resumeAnimation = useCallback((layerId: string): boolean => {
    // Implementation would be added to effects manager
    console.warn('Resume animation not yet implemented');
    return false;
  }, []);

  // Processing functions
  const processEffects = useCallback((sourceTexture: WebGLTexture): WebGLTexture | null => {
    if (!effectsManager) return null;
    try {
      return effectsManager.processEffects(sourceTexture);
    } catch (err) {
      console.error('Error processing effects:', err);
      setError(err instanceof Error ? err.message : 'Processing error');
      return null;
    }
  }, [effectsManager]);

  const previewLayer = useCallback((layerId: string, sourceTexture: WebGLTexture): WebGLTexture | null => {
    if (!effectsManager || !pipelineEngine) return null;
    
    const layer = effectsManager.getLayer(layerId);
    if (!layer) return null;

    try {
      // Temporarily process only this layer
      layer.filters.forEach(filter => {
        if (filter.enabled) {
          pipelineEngine.addFilter(filter);
        }
      });

      const result = pipelineEngine.process(sourceTexture);
      pipelineEngine.clearFilters();
      
      return result;
    } catch (err) {
      console.error('Error previewing layer:', err);
      return null;
    }
  }, [effectsManager, pipelineEngine]);

  const previewFilter = useCallback((
    filterId: string, 
    parameters: Record<string, any>, 
    sourceTexture: WebGLTexture
  ): WebGLTexture | null => {
    if (!pipelineEngine || !filterLibrary) return null;

    const filterShader = filterLibrary.getFilterById(filterId);
    if (!filterShader) return null;

    try {
      const filterInstance: FilterInstance = {
        id: 'preview',
        shaderId: filterId,
        name: filterShader.name,
        enabled: true,
        opacity: 1.0,
        blendMode: 'normal',
        parameters
      };

      pipelineEngine.addFilter(filterInstance);
      const result = pipelineEngine.process(sourceTexture);
      pipelineEngine.clearFilters();

      return result;
    } catch (err) {
      console.error('Error previewing filter:', err);
      return null;
    }
  }, [pipelineEngine, filterLibrary]);

  // Real-time controls
  const toggleRealTime = useCallback(() => {
    setIsRealTimeEnabled(prev => !prev);
  }, []);

  // Cleanup
  useEffect(() => {
    return () => {
      if (pipelineEngine) {
        pipelineEngine.dispose();
      }
      if (effectsManager) {
        effectsManager.dispose();
      }
    };
  }, [pipelineEngine, effectsManager]);

  // Memoized return value
  return useMemo(() => ({
    // Core managers
    pipelineEngine,
    effectsManager,
    filterLibrary,
    
    // Filter management
    availableFilters,
    filtersByCategory,
    addFilter,
    removeFilter,
    updateFilterParameters,
    duplicateFilter,
    
    // Effect layer management
    effectStack,
    activeLayers,
    createLayer,
    deleteLayer,
    updateLayer,
    duplicateLayer,
    reorderLayers,
    
    // Group management
    effectGroups,
    createGroup,
    deleteGroup,
    addLayerToGroup,
    removeLayerFromGroup,
    
    // Preset management
    filterPresets,
    effectPresets,
    savePreset,
    loadPreset,
    deletePreset,
    
    // Animation controls
    startAnimation,
    stopAnimation,
    pauseAnimation,
    resumeAnimation,
    
    // Processing and preview
    processEffects,
    previewLayer,
    previewFilter,
    
    // Real-time controls
    isRealTimeEnabled,
    toggleRealTime,
    previewEnabled,
    setPreviewEnabled,
    
    // Performance monitoring
    performanceMetrics,
    memoryUsage,
    frameRate,
    resetMetrics,
    
    // State and status
    isInitialized,
    isProcessing,
    error,
    canvasReady
  }), [
    pipelineEngine,
    effectsManager,
    filterLibrary,
    availableFilters,
    filtersByCategory,
    addFilter,
    removeFilter,
    updateFilterParameters,
    duplicateFilter,
    effectStack,
    activeLayers,
    createLayer,
    deleteLayer,
    updateLayer,
    duplicateLayer,
    reorderLayers,
    effectGroups,
    createGroup,
    deleteGroup,
    addLayerToGroup,
    removeLayerFromGroup,
    filterPresets,
    effectPresets,
    savePreset,
    loadPreset,
    deletePreset,
    startAnimation,
    stopAnimation,
    pauseAnimation,
    resumeAnimation,
    processEffects,
    previewLayer,
    previewFilter,
    isRealTimeEnabled,
    toggleRealTime,
    previewEnabled,
    performanceMetrics,
    memoryUsage,
    frameRate,
    resetMetrics,
    isInitialized,
    isProcessing,
    error,
    canvasReady
  ]);
}

// Utility hooks for specific use cases

// Hook for filter parameter animation
export function useFilterAnimation(
  updateFilterParameters: (layerId: string, filterId: string, parameters: Record<string, any>) => boolean,
  layerId: string,
  filterId: string
) {
  const animationRef = useRef<number>();
  const [isAnimating, setIsAnimating] = useState<boolean>(false);

  const animateParameter = useCallback((
    parameterName: string,
    fromValue: number,
    toValue: number,
    duration: number = 1000,
    easing: string = 'ease-in-out'
  ) => {
    if (isAnimating) return;

    setIsAnimating(true);
    const startTime = performance.now();

    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Apply easing
      let easedProgress = progress;
      switch (easing) {
        case 'ease-in':
          easedProgress = progress * progress;
          break;
        case 'ease-out':
          easedProgress = 1 - (1 - progress) * (1 - progress);
          break;
        case 'ease-in-out':
          easedProgress = progress < 0.5 
            ? 2 * progress * progress 
            : 1 - 2 * (1 - progress) * (1 - progress);
          break;
      }

      const currentValue = fromValue + (toValue - fromValue) * easedProgress;
      updateFilterParameters(layerId, filterId, { [parameterName]: currentValue });

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        setIsAnimating(false);
      }
    };

    animationRef.current = requestAnimationFrame(animate);
  }, [updateFilterParameters, layerId, filterId, isAnimating]);

  const stopAnimation = useCallback(() => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      setIsAnimating(false);
    }
  }, []);

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return { animateParameter, stopAnimation, isAnimating };
}

// Hook for filter presets with undo/redo
export function useFilterPresets(
  addFilter: (layerId: string, filterId: string, parameters?: Record<string, any>) => FilterInstance | null,
  removeFilter: (layerId: string, filterId: string) => boolean
) {
  const [history, setHistory] = useState<Array<{action: string, data: any}>>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);

  const applyPreset = useCallback((layerId: string, preset: FilterPreset) => {
    // Store current state for undo
    const action = { action: 'apply_preset', data: { layerId, preset } };
    
    // Apply preset filters
    Object.entries(preset.parameters).forEach(([filterId, parameters]) => {
      addFilter(layerId, filterId, parameters);
    });

    // Update history
    setHistory(prev => [...prev.slice(0, historyIndex + 1), action]);
    setHistoryIndex(prev => prev + 1);
  }, [addFilter, historyIndex]);

  const undo = useCallback(() => {
    if (historyIndex >= 0) {
      // Implement undo logic
      setHistoryIndex(prev => prev - 1);
    }
  }, [historyIndex]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      // Implement redo logic
      setHistoryIndex(prev => prev + 1);
    }
  }, [historyIndex, history.length]);

  return {
    applyPreset,
    undo,
    redo,
    canUndo: historyIndex >= 0,
    canRedo: historyIndex < history.length - 1
  };
}