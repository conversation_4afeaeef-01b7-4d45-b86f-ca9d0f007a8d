import { useState, useCallback, useEffect, useRef } from 'react';
import { ReplicateService, GenerationRequest, GenerationResult, ReplicateModel } from '../lib/ai/replicate-service';

export interface UseReplicateGenerationReturn {
  generateImage: (request: Omit<GenerationRequest, 'model'> & { model?: string }) => Promise<GenerationResult>;
  cancelGeneration: (id: string) => Promise<void>;
  getGeneration: (id: string) => Promise<GenerationResult>;
  availableModels: ReplicateModel[];
  recommendModel: (prompt: string, requirements?: any) => ReplicateModel | null;
  estimateCost: (input: any, model?: string) => number;
  activeGenerations: GenerationResult[];
  isGenerating: boolean;
  error: string | null;
}

export function useReplicateGeneration(apiToken: string): UseReplicateGenerationReturn {
  const [availableModels, setAvailableModels] = useState<ReplicateModel[]>([]);
  const [activeGenerations, setActiveGenerations] = useState<GenerationResult[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const serviceRef = useRef<ReplicateService | null>(null);

  useEffect(() => {
    if (!apiToken) return;

    const service = new ReplicateService({ apiToken });
    serviceRef.current = service;

    // Set initial