import { useEffect, useRef, useState, useCallback } from 'react';
import { HybridCanvasManager, HybridCanvasConfig } from '../lib/canvas/hybrid-canvas-manager';
import { fabric } from 'fabric';

export interface UseHybridCanvasReturn {
  canvasManager: HybridCanvasManager | null;
  canvas: fabric.Canvas | null;
  isInitialized: boolean;
  isWebGLEnabled: boolean;
  performanceMetrics: PerformanceMetrics;
  addObject: (object: fabric.Object) => void;
  removeObject: (objectId: string) => void;
  clearCanvas: () => void;
  undo: () => boolean;
  redo: () => boolean;
  canUndo: boolean;
  canRedo: boolean;
  enableHybridRendering: () => void;
  updateRenderingStrategy: (strategy: any) => void;
}

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memoryUsage: number;
  renderCalls: number;
  objectCount: number;
}

export function useHybridCanvas(
  canvasRef: React.RefObject<HTMLCanvasElement>,
  config: HybridCanvasConfig
): UseHybridCanvasReturn {
  const [canvasManager, setCanvasManager] = useState<HybridCanvasManager | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isWebGLEnabled, setIsWebGLEnabled] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    frameTime: 16.67,
    memoryUsage: 0,
    renderCalls: 0,
    objectCount: 0
  });
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  const metricsIntervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!canvasRef.current) return;

    const manager = new HybridCanvasManager(canvasRef.current, config);
    setCanvasManager(manager);
    setIsInitialized(true);

    // Handle WebGL events
    manager.on('webgl:enabled', () => {
      setIsWebGLEnabled(true);
    });

    manager.on('webgl:fallback', (error) => {
      setIsWebGLEnabled(false);
      console.warn('WebGL fallback:', error);
    });

    // Update undo/redo state
    const updateHistoryState = () => {
      const canvas = manager.getCanvas();
      const historyManager = (canvas as any).historyManager;
      if (historyManager) {
        setCanUndo(historyManager.canUndo());
        setCanRedo(historyManager.canRedo());
      }
    };

    manager.on('object:added', updateHistoryState);
    manager.on('object:removed', updateHistoryState);
    manager.on('object:modified', updateHistoryState);

    // Start performance monitoring
    metricsIntervalRef.current = setInterval(() => {
      const metrics = manager.getPerformanceMetrics();
      setPerformanceMetrics(metrics);
    }, 1000);

    return () => {
      if (metricsIntervalRef.current) {
        clearInterval(metricsIntervalRef.current);
      }
      manager.dispose();
    };
  }, [canvasRef, config]);

  const addObject = useCallback((object: fabric.Object) => {
    if (canvasManager) {
      canvasManager.addObject(object, { renderImmediately: true });
    }
  }, [canvasManager]);

  const removeObject = useCallback((objectId: string) => {
    if (canvasManager) {
      canvasManager.removeObject(objectId);
    }
  }, [canvasManager]);

  const clearCanvas = useCallback(() => {
    if (canvasManager) {
      const canvas = canvasManager.getCanvas();
      canvas.clear();
      canvas.renderAll();
    }
  }, [canvasManager]);

  const undo = useCallback(() => {
    if (canvasManager) {
      const canvas = canvasManager.getCanvas();
      const historyManager = (canvas as any).historyManager;
      return historyManager ? historyManager.undo() : false;
    }
    return false;
  }, [canvasManager]);

  const redo = useCallback(() => {
    if (canvasManager) {
      const canvas = canvasManager.getCanvas();
      const historyManager = (canvas as any).historyManager;
      return historyManager ? historyManager.redo() : false;
    }
    return false;
  }, [canvasManager]);

  const enableHybridRendering = useCallback(() => {
    if (canvasManager) {
      canvasManager.enableHybridRendering();
    }
  }, [canvasManager]);

  const updateRenderingStrategy = useCallback((strategy: any) => {
    if (canvasManager) {
      canvasManager.updateRenderingStrategy(strategy);
    }
  }, [canvasManager]);

  return {
    canvasManager,
    canvas: canvasManager?.getCanvas() || null,
    isInitialized,
    isWebGLEnabled,
    performanceMetrics,
    addObject,
    removeObject,
    clearCanvas,
    undo,
    redo,
    canUndo,
    canRedo,
    enableHybridRendering,
    updateRenderingStrategy
  };
}