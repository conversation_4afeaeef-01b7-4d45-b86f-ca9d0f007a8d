import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { fabric } from 'fabric';
import * as PIXI from 'pixi.js';
import { 
  AdvancedLayerManager, 
  LayerNode, 
  LayerType, 
  BlendMode, 
  LayerEffect,
  CreateLayerOptions,
  LayerFilterCriteria,
  LayerSearchOptions
} from '../lib/canvas/advanced-layer-manager';
import { LayerHybridIntegration } from '../lib/canvas/layer-hybrid-integration';

// Hook state types
export interface LayerManagerState {
  layers: Map<string, LayerNode>;
  rootLayers: string[];
  selectedLayers: Set<string>;
  activeLayer?: string;
  layerCount: number;
  isLoading: boolean;
  error?: string;
}

export interface LayerManagerActions {
  createLayer: (options: CreateLayerOptions) => Promise<LayerNode>;
  deleteLayer: (layerId: string) => Promise<boolean>;
  duplicateLayer: (layerId: string) => Promise<LayerNode | null>;
  moveLayer: (layerId: string, targetParentId?: string, index?: number) => Promise<boolean>;
  updateLayerProperty: <T extends keyof LayerNode>(layerId: string, property: T, value: LayerNode[T]) => Promise<boolean>;
  addLayerEffect: (layerId: string, effect: LayerEffect) => Promise<boolean>;
  removeLayerEffect: (layerId: string, effectId: string) => Promise<boolean>;
  updateLayerEffect: (layerId: string, effectId: string, updates: Partial<LayerEffect>) => Promise<boolean>;
  selectLayer: (layerId: string, addToSelection?: boolean) => void;
  deselectLayer: (layerId: string) => void;
  selectAllLayers: () => void;
  clearSelection: () => void;
  searchLayers: (options: LayerSearchOptions) => LayerNode[];
  filterLayers: (criteria: LayerFilterCriteria) => LayerNode[];
  createLayerGroup: (layerIds: string[], groupName?: string) => Promise<LayerNode>;
  ungroupLayers: (groupId: string) => Promise<string[]>;
  bulkUpdateLayers: (layerIds: string[], updates: Partial<LayerNode>) => Promise<string[]>;
  duplicateLayers: (layerIds: string[]) => Promise<LayerNode[]>;
  deleteLayers: (layerIds: string[]) => Promise<string[]>;
}

export interface LayerManagerPerformance {
  renderTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  engineDistribution: { fabric: number; pixi: number };
}

export interface LayerManagerHookOptions {
  autoOptimize?: boolean;
  enableHybridRendering?: boolean;
  maxUndoHistory?: number;
  enableRealTimeSync?: boolean;
  performanceMonitoring?: boolean;
}

export interface LayerManagerHookReturn {
  state: LayerManagerState;
  actions: LayerManagerActions;
  performance: LayerManagerPerformance;
  layerManager: AdvancedLayerManager | null;
  hybridIntegration: LayerHybridIntegration | null;
  isReady: boolean;
}

// Custom hook for layer management
export function useLayerManagement(
  fabricCanvas: fabric.Canvas | null,
  pixiApp?: PIXI.Application | null,
  options: LayerManagerHookOptions = {}
): LayerManagerHookReturn {
  const {
    autoOptimize = true,
    enableHybridRendering = true,
    maxUndoHistory = 50,
    enableRealTimeSync = true,
    performanceMonitoring = true
  } = options;

  // State management
  const [state, setState] = useState<LayerManagerState>({
    layers: new Map(),
    rootLayers: [],
    selectedLayers: new Set(),
    layerCount: 0,
    isLoading: true,
    error: undefined
  });

  const [performance, setPerformance] = useState<LayerManagerPerformance>({
    renderTime: 0,
    cacheHitRate: 0,
    memoryUsage: 0,
    engineDistribution: { fabric: 0, pixi: 0 }
  });

  // Refs for managers
  const layerManagerRef = useRef<AdvancedLayerManager | null>(null);
  const hybridIntegrationRef = useRef<LayerHybridIntegration | null>(null);
  const performanceIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize layer manager
  useEffect(() => {
    if (!fabricCanvas) return;

    const initializeLayerManager = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: undefined }));

        // Create layer manager
        const layerManager = new AdvancedLayerManager(fabricCanvas);
        layerManagerRef.current = layerManager;

        // Initialize hybrid integration if PIXI is available
        if (enableHybridRendering && pixiApp) {
          const hybridIntegration = new LayerHybridIntegration(
            fabricCanvas,
            pixiApp,
            layerManager
          );
          hybridIntegrationRef.current = hybridIntegration;
        }

        // Set up event listeners
        setupEventListeners(layerManager);

        // Start performance monitoring
        if (performanceMonitoring) {
          startPerformanceMonitoring();
        }

        // Initial state update
        updateState(layerManager);

        setState(prev => ({ ...prev, isLoading: false }));
      } catch (error) {
        console.error('Failed to initialize layer manager:', error);
        setState(prev => ({ 
          ...prev, 
          isLoading: false, 
          error: error instanceof Error ? error.message : 'Unknown error'
        }));
      }
    };

    initializeLayerManager();

    return () => {
      // Cleanup
      if (layerManagerRef.current) {
        layerManagerRef.current.dispose();
        layerManagerRef.current = null;
      }
      if (hybridIntegrationRef.current) {
        hybridIntegrationRef.current.dispose();
        hybridIntegrationRef.current = null;
      }
      if (performanceIntervalRef.current) {
        clearInterval(performanceIntervalRef.current);
        performanceIntervalRef.current = null;
      }
    };
  }, [fabricCanvas, pixiApp, enableHybridRendering, performanceMonitoring]);

  // Set up event listeners
  const setupEventListeners = useCallback((layerManager: AdvancedLayerManager) => {
    const handleLayerChange = () => {
      updateState(layerManager);
    };

    const handlePerformanceChange = () => {
      updatePerformanceMetrics();
    };

    // Layer events
    layerManager.on('layer:created', handleLayerChange);
    layerManager.on('layer:deleted', handleLayerChange);
    layerManager.on('layer:moved', handleLayerChange);
    layerManager.on('layer:property:changed', handleLayerChange);
    layerManager.on('layer:selected', handleLayerChange);
    layerManager.on('layer:deselected', handleLayerChange);
    layerManager.on('layers:selection:cleared', handleLayerChange);
    layerManager.on('group:created', handleLayerChange);
    layerManager.on('group:ungrouped', handleLayerChange);

    // Hybrid integration events
    if (hybridIntegrationRef.current) {
      hybridIntegrationRef.current.on('performance:optimized', handlePerformanceChange);
      hybridIntegrationRef.current.on('layer:routed', handlePerformanceChange);
    }

    return () => {
      layerManager.removeAllListeners();
      if (hybridIntegrationRef.current) {
        hybridIntegrationRef.current.removeAllListeners();
      }
    };
  }, []);

  // Update state from layer manager
  const updateState = useCallback((layerManager: AdvancedLayerManager) => {
    setState(prev => ({
      ...prev,
      layers: layerManager.layers,
      rootLayers: layerManager.rootLayers,
      selectedLayers: layerManager.selectedLayers,
      activeLayer: layerManager.activeLayer,
      layerCount: layerManager.layerCount
    }));
  }, []);

  // Update performance metrics
  const updatePerformanceMetrics = useCallback(() => {
    if (!layerManagerRef.current) return;

    const cacheStats = layerManagerRef.current.getCacheStats();
    const engineDistribution = hybridIntegrationRef.current?.getEngineDistribution() || { fabric: 0, pixi: 0 };

    setPerformance({
      renderTime: 0, // Would be updated from actual measurements
      cacheHitRate: cacheStats.hitRate,
      memoryUsage: cacheStats.memoryUsage,
      engineDistribution
    });
  }, []);

  // Start performance monitoring
  const startPerformanceMonitoring = useCallback(() => {
    if (performanceIntervalRef.current) {
      clearInterval(performanceIntervalRef.current);
    }

    performanceIntervalRef.current = setInterval(() => {
      updatePerformanceMetrics();

      // Auto-optimize if performance is poor
      if (autoOptimize && hybridIntegrationRef.current) {
        const currentPerf = performance;
        if (currentPerf.renderTime > 16 || currentPerf.memoryUsage > 100 * 1024 * 1024) {
          hybridIntegrationRef.current.optimizeForPerformance();
        }
      }
    }, 1000);
  }, [autoOptimize, performance, updatePerformanceMetrics]);

  // Action creators
  const actions = useMemo<LayerManagerActions>(() => ({
    createLayer: async (options: CreateLayerOptions): Promise<LayerNode> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      
      const layer = layerManagerRef.current.createLayer(options);
      
      // Auto-route to optimal engine if hybrid rendering is enabled
      if (hybridIntegrationRef.current) {
        hybridIntegrationRef.current.routeLayer(layer.id);
      }
      
      return layer;
    },

    deleteLayer: async (layerId: string): Promise<boolean> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.deleteLayer(layerId);
    },

    duplicateLayer: async (layerId: string): Promise<LayerNode | null> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      
      const layers = layerManagerRef.current.duplicateLayers([layerId]);
      return layers.length > 0 ? layers[0] : null;
    },

    moveLayer: async (layerId: string, targetParentId?: string, index?: number): Promise<boolean> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.moveLayer(layerId, targetParentId, index);
    },

    updateLayerProperty: async <T extends keyof LayerNode>(
      layerId: string, 
      property: T, 
      value: LayerNode[T]
    ): Promise<boolean> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.updateLayerProperty(layerId, property, value);
    },

    addLayerEffect: async (layerId: string, effect: LayerEffect): Promise<boolean> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      const result = layerManagerRef.current.addLayerEffect(layerId, effect);
      
      // Reassess routing after adding effects
      if (hybridIntegrationRef.current) {
        hybridIntegrationRef.current.routeLayer(layerId);
      }
      
      return result;
    },

    removeLayerEffect: async (layerId: string, effectId: string): Promise<boolean> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      const result = layerManagerRef.current.removeLayerEffect(layerId, effectId);
      
      // Reassess routing after removing effects
      if (hybridIntegrationRef.current) {
        hybridIntegrationRef.current.routeLayer(layerId);
      }
      
      return result;
    },

    updateLayerEffect: async (layerId: string, effectId: string, updates: Partial<LayerEffect>): Promise<boolean> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.updateLayerEffect(layerId, effectId, updates);
    },

    selectLayer: (layerId: string, addToSelection: boolean = false): void => {
      if (!layerManagerRef.current) return;
      layerManagerRef.current.selectLayer(layerId, addToSelection);
    },

    deselectLayer: (layerId: string): void => {
      if (!layerManagerRef.current) return;
      layerManagerRef.current.deselectLayer(layerId);
    },

    selectAllLayers: (): void => {
      if (!layerManagerRef.current) return;
      layerManagerRef.current.selectAllLayers();
    },

    clearSelection: (): void => {
      if (!layerManagerRef.current) return;
      layerManagerRef.current.clearSelection();
    },

    searchLayers: (options: LayerSearchOptions): LayerNode[] => {
      if (!layerManagerRef.current) return [];
      return layerManagerRef.current.searchLayers(options);
    },

    filterLayers: (criteria: LayerFilterCriteria): LayerNode[] => {
      if (!layerManagerRef.current) return [];
      return layerManagerRef.current.filterLayers(criteria);
    },

    createLayerGroup: async (layerIds: string[], groupName?: string): Promise<LayerNode> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.createLayerGroup(layerIds, groupName);
    },

    ungroupLayers: async (groupId: string): Promise<string[]> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.ungroupLayers(groupId);
    },

    bulkUpdateLayers: async (layerIds: string[], updates: Partial<LayerNode>): Promise<string[]> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.bulkUpdateLayers(layerIds, updates);
    },

    duplicateLayers: async (layerIds: string[]): Promise<LayerNode[]> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.duplicateLayers(layerIds);
    },

    deleteLayers: async (layerIds: string[]): Promise<string[]> => {
      if (!layerManagerRef.current) throw new Error('Layer manager not initialized');
      return layerManagerRef.current.deleteLayers(layerIds);
    }
  }), []);

  // Computed values
  const isReady = useMemo(() => {
    return !state.isLoading && !!layerManagerRef.current;
  }, [state.isLoading]);

  return {
    state,
    actions,
    performance,
    layerManager: layerManagerRef.current,
    hybridIntegration: hybridIntegrationRef.current,
    isReady
  };
}

// Helper hooks for specific layer operations
export function useLayerSelection(layerManager: AdvancedLayerManager | null) {
  const [selectedLayers, setSelectedLayers] = useState<Set<string>>(new Set());
  const [activeLayer, setActiveLayer] = useState<string | undefined>();

  useEffect(() => {
    if (!layerManager) return;

    const handleSelectionChange = () => {
      setSelectedLayers(new Set(layerManager.selectedLayers));
      setActiveLayer(layerManager.activeLayer);
    };

    layerManager.on('layer:selected', handleSelectionChange);
    layerManager.on('layer:deselected', handleSelectionChange);
    layerManager.on('layers:selection:cleared', handleSelectionChange);

    // Initial state
    handleSelectionChange();

    return () => {
      layerManager.removeAllListeners();
    };
  }, [layerManager]);

  return { selectedLayers, activeLayer };
}

export function useLayerSearch(layerManager: AdvancedLayerManager | null) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<LayerNode[]>([]);
  const [searchOptions, setSearchOptions] = useState<LayerSearchOptions>({
    query: '',
    includeMetadata: true,
    caseSensitive: false,
    exactMatch: false,
    searchTags: true,
    searchEffects: false
  });

  const performSearch = useCallback((query: string, options?: Partial<LayerSearchOptions>) => {
    if (!layerManager || !query.trim()) {
      setSearchResults([]);
      return;
    }

    const searchOpts = { ...searchOptions, query, ...options };
    const results = layerManager.searchLayers(searchOpts);
    setSearchResults(results);
  }, [layerManager, searchOptions]);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchResults([]);
  }, []);

  useEffect(() => {
    if (searchQuery) {
      performSearch(searchQuery);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, performSearch]);

  return {
    searchQuery,
    setSearchQuery,
    searchResults,
    searchOptions,
    setSearchOptions,
    performSearch,
    clearSearch
  };
}

export function useLayerEffects(layerManager: AdvancedLayerManager | null, layerId: string | null) {
  const [effects, setEffects] = useState<LayerEffect[]>([]);

  useEffect(() => {
    if (!layerManager || !layerId) {
      setEffects([]);
      return;
    }

    const updateEffects = () => {
      const layer = layerManager.getLayer(layerId);
      if (layer) {
        setEffects([...layer.effects]);
      }
    };

    layerManager.on('layer:effect:added', ({ layerId: affectedLayerId }: any) => {
      if (affectedLayerId === layerId) updateEffects();
    });

    layerManager.on('layer:effect:removed', ({ layerId: affectedLayerId }: any) => {
      if (affectedLayerId === layerId) updateEffects();
    });

    layerManager.on('layer:effect:updated', ({ layerId: affectedLayerId }: any) => {
      if (affectedLayerId === layerId) updateEffects();
    });

    // Initial state
    updateEffects();

    return () => {
      layerManager.removeAllListeners();
    };
  }, [layerManager, layerId]);

  return effects;
}

export function useLayerPerformance(hybridIntegration: LayerHybridIntegration | null) {
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);

  useEffect(() => {
    if (!hybridIntegration) return;

    const updateMetrics = () => {
      const metrics = hybridIntegration.getPerformanceMetrics();
      setPerformanceMetrics(metrics);
    };

    hybridIntegration.on('performance:optimized', updateMetrics);
    hybridIntegration.on('layer:routed', updateMetrics);

    // Initial state
    updateMetrics();

    return () => {
      hybridIntegration.removeAllListeners();
    };
  }, [hybridIntegration]);

  return performanceMetrics;
}