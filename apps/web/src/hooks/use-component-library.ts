// apps/web/src/hooks/use-component-library.ts
// Component Library React Hook - State management and operations

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { 
  ComponentLibraryEngine, 
  AssetManager,
  DesignComponent, 
  ComponentCategory, 
  ComponentSearchFilters, 
  ComponentSearchResult,
  ComponentInstance,
  ComponentCreationOptions,
  InstanceCreationOptions,
  ComponentOverride,
  DesignAsset,
  AssetType,
  AssetSearchFilters,
  AssetSearchResult,
  AssetUploadOptions,
  AssetUploadResult,
  AssetCategory,
  AssetCollection,
  Point,
  PropertyType,
  ComponentLibraryOptions,
  AssetManagerOptions
} from '@/lib/design-system/component-library-engine';

export interface UseComponentLibraryOptions {
  canvas?: fabric.Canvas;
  enableAutoSave?: boolean;
  saveInterval?: number;
  maxUndoHistory?: number;
  enablePerformanceMonitoring?: boolean;
  componentLibraryOptions?: ComponentLibraryOptions;
  assetManagerOptions?: AssetManagerOptions;
}

export interface ComponentLibraryState {
  components: DesignComponent[];
  categories: ComponentCategory[];
  instances: ComponentInstance[];
  assets: DesignAsset[];
  assetCategories: AssetCategory[];
  assetCollections: AssetCollection[];
  selectedComponent?: DesignComponent;
  selectedAsset?: DesignAsset;
  selectedInstance?: ComponentInstance;
  searchQuery: string;
  searchResults: (ComponentSearchResult | AssetSearchResult)[];
  filters: ComponentSearchFilters & AssetSearchFilters;
  viewMode: 'grid' | 'list';
  isLoading: boolean;
  error?: string;
  uploadProgress: number;
  isUploading: boolean;
}

export interface ComponentLibraryActions {
  // Component management
  createComponent: (selection: fabric.Object | fabric.Object[], options: ComponentCreationOptions) => Promise<DesignComponent>;
  updateComponent: (id: string, updates: Partial<DesignComponent>) => Promise<DesignComponent>;
  deleteComponent: (id: string) => Promise<boolean>;
  duplicateComponent: (id: string) => Promise<DesignComponent>;
  
  // Component instances
  createComponentInstance: (componentId: string, position: Point, options?: InstanceCreationOptions) => Promise<ComponentInstance>;
  updateComponentInstance: (instanceId: string, updates: Partial<ComponentInstance>) => Promise<boolean>;
  deleteComponentInstance: (instanceId: string) => Promise<boolean>;
  setInstanceOverride: (instanceId: string, propertyId: string, value: any) => void;
  
  // Asset management
  uploadAsset: (file: File, options?: AssetUploadOptions) => Promise<AssetUploadResult>;
  uploadAssets: (files: FileList, options?: AssetUploadOptions) => Promise<AssetUploadResult[]>;
  deleteAsset: (id: string) => Promise<boolean>;
  updateAsset: (id: string, updates: Partial<DesignAsset>) => Promise<DesignAsset>;
  
  // Search and filtering
  searchComponents: (query: string, filters?: ComponentSearchFilters) => Promise<ComponentSearchResult[]>;
  searchAssets: (query: string, filters?: AssetSearchFilters) => Promise<AssetSearchResult[]>;
  setSearchQuery: (query: string) => void;
  setFilters: (filters: ComponentSearchFilters & AssetSearchFilters) => void;
  clearFilters: () => void;
  
  // Categories and collections
  createCategory: (name: string, parent?: string, options?: any) => Promise<ComponentCategory>;
  createAssetCategory: (name: string, parent?: string, options?: any) => Promise<AssetCategory>;
  createAssetCollection: (name: string, options?: any) => Promise<AssetCollection>;
  
  // Selection management
  selectComponent: (component: DesignComponent) => void;
  selectAsset: (asset: DesignAsset) => void;
  selectInstance: (instance: ComponentInstance) => void;
  clearSelection: () => void;
  
  // View management
  setViewMode: (mode: 'grid' | 'list') => void;
  
  // Import/export
  exportComponents: (componentIds?: string[]) => Promise<any>;
  importComponents: (data: any) => Promise<DesignComponent[]>;
  
  // Performance and monitoring
  getPerformanceMetrics: () => any;
  getAnalytics: () => any;
  
  // Error handling
  clearError: () => void;
  
  // Cleanup
  dispose: () => void;
}

export interface UseComponentLibraryReturn {
  state: ComponentLibraryState;
  actions: ComponentLibraryActions;
  libraryEngine: ComponentLibraryEngine | null;
  assetManager: AssetManager | null;
}

export const useComponentLibrary = (options: UseComponentLibraryOptions = {}): UseComponentLibraryReturn => {
  const {
    canvas,
    enableAutoSave = true,
    saveInterval = 30000,
    maxUndoHistory = 50,
    enablePerformanceMonitoring = true,
    componentLibraryOptions = {},
    assetManagerOptions = {}
  } = options;

  // Core engines
  const libraryEngineRef = useRef<ComponentLibraryEngine | null>(null);
  const assetManagerRef = useRef<AssetManager | null>(null);
  const saveIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const performanceMonitorRef = useRef<NodeJS.Timeout | null>(null);

  // State management
  const [state, setState] = useState<ComponentLibraryState>({
    components: [],
    categories: [],
    instances: [],
    assets: [],
    assetCategories: [],
    assetCollections: [],
    searchQuery: '',
    searchResults: [],
    filters: {},
    viewMode: 'grid',
    isLoading: false,
    uploadProgress: 0,
    isUploading: false
  });

  // Undo/redo history
  const [history, setHistory] = useState<any[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Initialize engines
  useEffect(() => {
    if (canvas && !libraryEngineRef.current) {
      try {
        libraryEngineRef.current = new ComponentLibraryEngine(canvas, componentLibraryOptions);
        assetManagerRef.current = new AssetManager(assetManagerOptions);
        
        // Load initial data
        loadInitialData();
        
        // Setup event listeners
        setupEventListeners();
        
        // Setup auto-save if enabled
        if (enableAutoSave) {
          setupAutoSave();
        }
        
        // Setup performance monitoring if enabled
        if (enablePerformanceMonitoring) {
          setupPerformanceMonitoring();
        }
        
      } catch (error) {
        console.error('Failed to initialize component library:', error);
        setState(prev => ({ 
          ...prev, 
          error: error instanceof Error ? error.message : 'Initialization failed',
          isLoading: false 
        }));
      }
    }
  }, [canvas, componentLibraryOptions, assetManagerOptions, enableAutoSave, enablePerformanceMonitoring]);

  // Load initial data
  const loadInitialData = useCallback(async () => {
    if (!libraryEngineRef.current || !assetManagerRef.current) return;

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // Load components and categories
      const components = libraryEngineRef.current.getComponents();
      const categories = libraryEngineRef.current.getCategories();
      
      // Load assets and asset categories
      const assets = assetManagerRef.current.getAssets();
      const assetCategories = assetManagerRef.current.getAssetCategories();
      const assetCollections = assetManagerRef.current.getAssetCollections();

      setState(prev => ({
        ...prev,
        components,
        categories,
        assets,
        assetCategories,
        assetCollections,
        isLoading: false
      }));
    } catch (error) {
      console.error('Failed to load initial data:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to load data',
        isLoading: false 
      }));
    }
  }, []);

  // Setup event listeners
  const setupEventListeners = useCallback(() => {
    if (!libraryEngineRef.current || !assetManagerRef.current) return;

    // Component events
    window.addEventListener('component:created', handleComponentCreated);
    window.addEventListener('component:updated', handleComponentUpdated);
    window.addEventListener('component:deleted', handleComponentDeleted);
    window.addEventListener('component:instance:created', handleInstanceCreated);
    window.addEventListener('component:instance:updated', handleInstanceUpdated);
    window.addEventListener('component:instance:deleted', handleInstanceDeleted);

    // Asset events
    window.addEventListener('asset:uploaded', handleAssetUploaded);
    window.addEventListener('asset:updated', handleAssetUpdated);
    window.addEventListener('asset:deleted', handleAssetDeleted);

    return () => {
      window.removeEventListener('component:created', handleComponentCreated);
      window.removeEventListener('component:updated', handleComponentUpdated);
      window.removeEventListener('component:deleted', handleComponentDeleted);
      window.removeEventListener('component:instance:created', handleInstanceCreated);
      window.removeEventListener('component:instance:updated', handleInstanceUpdated);
      window.removeEventListener('component:instance:deleted', handleInstanceDeleted);
      window.removeEventListener('asset:uploaded', handleAssetUploaded);
      window.removeEventListener('asset:updated', handleAssetUpdated);
      window.removeEventListener('asset:deleted', handleAssetDeleted);
    };
  }, []);

  // Setup auto-save
  const setupAutoSave = useCallback(() => {
    if (saveIntervalRef.current) {
      clearInterval(saveIntervalRef.current);
    }

    saveIntervalRef.current = setInterval(() => {
      saveState();
    }, saveInterval);
  }, [saveInterval]);

  // Setup performance monitoring
  const setupPerformanceMonitoring = useCallback(() => {
    if (performanceMonitorRef.current) {
      clearInterval(performanceMonitorRef.current);
    }

    performanceMonitorRef.current = setInterval(() => {
      monitorPerformance();
    }, 5000); // Check every 5 seconds
  }, []);

  // Event handlers
  const handleComponentCreated = useCallback((e: any) => {
    const component = e.detail;
    setState(prev => ({
      ...prev,
      components: [...prev.components, component]
    }));
    addToHistory('component:created', component);
  }, []);

  const handleComponentUpdated = useCallback((e: any) => {
    const component = e.detail;
    setState(prev => ({
      ...prev,
      components: prev.components.map(c => c.id === component.id ? component : c)
    }));
    addToHistory('component:updated', component);
  }, []);

  const handleComponentDeleted = useCallback((e: any) => {
    const { id } = e.detail;
    setState(prev => ({
      ...prev,
      components: prev.components.filter(c => c.id !== id),
      selectedComponent: prev.selectedComponent?.id === id ? undefined : prev.selectedComponent
    }));
    addToHistory('component:deleted', { id });
  }, []);

  const handleInstanceCreated = useCallback((e: any) => {
    const instance = e.detail;
    setState(prev => ({
      ...prev,
      instances: [...prev.instances, instance]
    }));
    addToHistory('instance:created', instance);
  }, []);

  const handleInstanceUpdated = useCallback((e: any) => {
    const instance = e.detail;
    setState(prev => ({
      ...prev,
      instances: prev.instances.map(i => i.id === instance.id ? instance : i)
    }));
    addToHistory('instance:updated', instance);
  }, []);

  const handleInstanceDeleted = useCallback((e: any) => {
    const { instanceId } = e.detail;
    setState(prev => ({
      ...prev,
      instances: prev.instances.filter(i => i.id !== instanceId),
      selectedInstance: prev.selectedInstance?.id === instanceId ? undefined : prev.selectedInstance
    }));
    addToHistory('instance:deleted', { instanceId });
  }, []);

  const handleAssetUploaded = useCallback((e: any) => {
    const asset = e.detail;
    setState(prev => ({
      ...prev,
      assets: [...prev.assets, asset]
    }));
    addToHistory('asset:uploaded', asset);
  }, []);

  const handleAssetUpdated = useCallback((e: any) => {
    const asset = e.detail;
    setState(prev => ({
      ...prev,
      assets: prev.assets.map(a => a.id === asset.id ? asset : a)
    }));
    addToHistory('asset:updated', asset);
  }, []);

  const handleAssetDeleted = useCallback((e: any) => {
    const { assetId } = e.detail;
    setState(prev => ({
      ...prev,
      assets: prev.assets.filter(a => a.id !== assetId),
      selectedAsset: prev.selectedAsset?.id === assetId ? undefined : prev.selectedAsset
    }));
    addToHistory('asset:deleted', { assetId });
  }, []);

  // History management
  const addToHistory = useCallback((action: string, data: any) => {
    const entry = {
      action,
      data,
      timestamp: Date.now()
    };

    setHistory(prev => {
      const newHistory = [...prev.slice(0, historyIndex + 1), entry];
      return newHistory.slice(-maxUndoHistory);
    });
    setHistoryIndex(prev => Math.min(prev + 1, maxUndoHistory - 1));
  }, [historyIndex, maxUndoHistory]);

  // Save state
  const saveState = useCallback(() => {
    try {
      const stateToSave = {
        components: state.components,
        categories: state.categories,
        instances: state.instances,
        assets: state.assets.map(asset => ({
          ...asset,
          // Don't save actual file data, just references
          url: asset.url,
          thumbnailUrl: asset.thumbnailUrl
        })),
        assetCategories: state.assetCategories,
        assetCollections: state.assetCollections
      };

      localStorage.setItem('componentLibraryState', JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Failed to save state:', error);
    }
  }, [state]);

  // Load state
  const loadState = useCallback(() => {
    try {
      const savedState = localStorage.getItem('componentLibraryState');
      if (savedState) {
        const parsed = JSON.parse(savedState);
        setState(prev => ({
          ...prev,
          ...parsed
        }));
      }
    } catch (error) {
      console.error('Failed to load state:', error);
    }
  }, []);

  // Performance monitoring
  const monitorPerformance = useCallback(() => {
    if (!libraryEngineRef.current || !assetManagerRef.current) return;

    const performance = {
      componentCount: state.components.length,
      instanceCount: state.instances.length,
      assetCount: state.assets.length,
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
      renderTime: 0 // Would be calculated from actual render times
    };

    // Log performance warnings
    if (performance.componentCount > 1000) {
      console.warn('High component count detected:', performance.componentCount);
    }
    if (performance.instanceCount > 5000) {
      console.warn('High instance count detected:', performance.instanceCount);
    }
  }, [state]);

  // Component management actions
  const createComponent = useCallback(async (
    selection: fabric.Object | fabric.Object[], 
    options: ComponentCreationOptions
  ): Promise<DesignComponent> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const component = await libraryEngineRef.current.createComponent(selection, options);
      setState(prev => ({ ...prev, isLoading: false }));
      return component;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to create component',
        isLoading: false 
      }));
      throw error;
    }
  }, []);

  const updateComponent = useCallback(async (
    id: string, 
    updates: Partial<DesignComponent>
  ): Promise<DesignComponent> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      const component = await libraryEngineRef.current.updateComponent(id, updates);
      return component;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to update component'
      }));
      throw error;
    }
  }, []);

  const deleteComponent = useCallback(async (id: string): Promise<boolean> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      const success = libraryEngineRef.current.deleteComponent(id);
      return success;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to delete component'
      }));
      throw error;
    }
  }, []);

  const duplicateComponent = useCallback(async (id: string): Promise<DesignComponent> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      const component = libraryEngineRef.current.getComponent(id);
      if (!component) {
        throw new Error('Component not found');
      }

      // Create a copy with new ID
      const duplicatedComponent = {
        ...component,
        id: `${component.id}_copy_${Date.now()}`,
        name: `${component.name} Copy`,
        created: new Date(),
        updated: new Date(),
        usage: {
          totalInstances: 0,
          projects: [],
          lastUsed: new Date(),
          popularity: 0,
          feedback: []
        }
      };

      // Store the duplicated component
      libraryEngineRef.current.components.set(duplicatedComponent.id, duplicatedComponent);
      
      setState(prev => ({
        ...prev,
        components: [...prev.components, duplicatedComponent]
      }));

      return duplicatedComponent;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to duplicate component'
      }));
      throw error;
    }
  }, []);

  // Instance management actions
  const createComponentInstance = useCallback(async (
    componentId: string, 
    position: Point, 
    options?: InstanceCreationOptions
  ): Promise<ComponentInstance> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      const instance = await libraryEngineRef.current.createComponentInstance(componentId, position, options);
      return instance;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to create instance'
      }));
      throw error;
    }
  }, []);

  const updateComponentInstance = useCallback(async (
    instanceId: string, 
    updates: Partial<ComponentInstance>
  ): Promise<boolean> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      const instance = libraryEngineRef.current.instances.get(instanceId);
      if (!instance) {
        throw new Error('Instance not found');
      }

      Object.assign(instance, updates);
      
      setState(prev => ({
        ...prev,
        instances: prev.instances.map(i => i.id === instanceId ? instance : i)
      }));

      return true;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to update instance'
      }));
      throw error;
    }
  }, []);

  const deleteComponentInstance = useCallback(async (instanceId: string): Promise<boolean> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      const success = libraryEngineRef.current.deleteComponentInstance(instanceId);
      return success;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to delete instance'
      }));
      throw error;
    }
  }, []);

  const setInstanceOverride = useCallback((instanceId: string, propertyId: string, value: any) => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      libraryEngineRef.current.setInstanceOverride(instanceId, propertyId, value);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to set override'
      }));
      throw error;
    }
  }, []);

  // Asset management actions
  const uploadAsset = useCallback(async (
    file: File, 
    options?: AssetUploadOptions
  ): Promise<AssetUploadResult> => {
    if (!assetManagerRef.current) {
      throw new Error('Asset manager not initialized');
    }

    setState(prev => ({ ...prev, isUploading: true, uploadProgress: 0 }));

    try {
      const result = await assetManagerRef.current.uploadAsset(file, options);
      setState(prev => ({ ...prev, isUploading: false, uploadProgress: 0 }));
      return result;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to upload asset',
        isUploading: false,
        uploadProgress: 0
      }));
      throw error;
    }
  }, []);

  const uploadAssets = useCallback(async (
    files: FileList, 
    options?: AssetUploadOptions
  ): Promise<AssetUploadResult[]> => {
    if (!assetManagerRef.current) {
      throw new Error('Asset manager not initialized');
    }

    setState(prev => ({ ...prev, isUploading: true, uploadProgress: 0 }));

    try {
      const results: AssetUploadResult[] = [];
      const totalFiles = files.length;

      for (let i = 0; i < totalFiles; i++) {
        const file = files[i];
        const result = await assetManagerRef.current.uploadAsset(file, options);
        results.push(result);
        
        // Update progress
        const progress = ((i + 1) / totalFiles) * 100;
        setState(prev => ({ ...prev, uploadProgress: progress }));
      }

      setState(prev => ({ ...prev, isUploading: false, uploadProgress: 0 }));
      return results;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to upload assets',
        isUploading: false,
        uploadProgress: 0
      }));
      throw error;
    }
  }, []);

  const deleteAsset = useCallback(async (id: string): Promise<boolean> => {
    if (!assetManagerRef.current) {
      throw new Error('Asset manager not initialized');
    }

    try {
      const success = await assetManagerRef.current.deleteAsset(id);
      return success;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to delete asset'
      }));
      throw error;
    }
  }, []);

  const updateAsset = useCallback(async (
    id: string, 
    updates: Partial<DesignAsset>
  ): Promise<DesignAsset> => {
    if (!assetManagerRef.current) {
      throw new Error('Asset manager not initialized');
    }

    try {
      const asset = assetManagerRef.current.getAsset(id);
      if (!asset) {
        throw new Error('Asset not found');
      }

      Object.assign(asset, updates);
      asset.updated = new Date();

      setState(prev => ({
        ...prev,
        assets: prev.assets.map(a => a.id === id ? asset : a)
      }));

      return asset;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to update asset'
      }));
      throw error;
    }
  }, []);

  // Search and filtering actions
  const searchComponents = useCallback(async (
    query: string, 
    filters?: ComponentSearchFilters
  ): Promise<ComponentSearchResult[]> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      const results = await libraryEngineRef.current.searchComponents(query, filters);
      setState(prev => ({ ...prev, searchResults: results }));
      return results;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to search components'
      }));
      throw error;
    }
  }, []);

  const searchAssets = useCallback(async (
    query: string, 
    filters?: AssetSearchFilters
  ): Promise<AssetSearchResult[]> => {
    if (!assetManagerRef.current) {
      throw new Error('Asset manager not initialized');
    }

    try {
      const results = await assetManagerRef.current.searchAssets(query, filters);
      setState(prev => ({ ...prev, searchResults: results }));
      return results;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to search assets'
      }));
      throw error;
    }
  }, []);

  const setSearchQuery = useCallback((query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }));
  }, []);

  const setFilters = useCallback((filters: ComponentSearchFilters & AssetSearchFilters) => {
    setState(prev => ({ ...prev, filters }));
  }, []);

  const clearFilters = useCallback(() => {
    setState(prev => ({ ...prev, filters: {} }));
  }, []);

  // Categories and collections
  const createCategory = useCallback(async (
    name: string, 
    parent?: string, 
    options?: any
  ): Promise<ComponentCategory> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      const category = await libraryEngineRef.current.createComponentCategory(name, parent, options);
      setState(prev => ({ ...prev, categories: [...prev.categories, category] }));
      return category;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to create category'
      }));
      throw error;
    }
  }, []);

  const createAssetCategory = useCallback(async (
    name: string, 
    parent?: string, 
    options?: any
  ): Promise<AssetCategory> => {
    if (!assetManagerRef.current) {
      throw new Error('Asset manager not initialized');
    }

    try {
      const category = await assetManagerRef.current.createAssetCategory(name, parent, options);
      setState(prev => ({ ...prev, assetCategories: [...prev.assetCategories, category] }));
      return category;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to create asset category'
      }));
      throw error;
    }
  }, []);

  const createAssetCollection = useCallback(async (
    name: string, 
    options?: any
  ): Promise<AssetCollection> => {
    if (!assetManagerRef.current) {
      throw new Error('Asset manager not initialized');
    }

    try {
      const collection = await assetManagerRef.current.createAssetCollection(name, options);
      setState(prev => ({ ...prev, assetCollections: [...prev.assetCollections, collection] }));
      return collection;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to create collection'
      }));
      throw error;
    }
  }, []);

  // Selection management
  const selectComponent = useCallback((component: DesignComponent) => {
    setState(prev => ({ ...prev, selectedComponent: component }));
  }, []);

  const selectAsset = useCallback((asset: DesignAsset) => {
    setState(prev => ({ ...prev, selectedAsset: asset }));
  }, []);

  const selectInstance = useCallback((instance: ComponentInstance) => {
    setState(prev => ({ ...prev, selectedInstance: instance }));
  }, []);

  const clearSelection = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      selectedComponent: undefined,
      selectedAsset: undefined,
      selectedInstance: undefined
    }));
  }, []);

  // View management
  const setViewMode = useCallback((mode: 'grid' | 'list') => {
    setState(prev => ({ ...prev, viewMode: mode }));
  }, []);

  // Import/export
  const exportComponents = useCallback(async (componentIds?: string[]): Promise<any> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      return await libraryEngineRef.current.exportComponentLibrary(componentIds);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to export components'
      }));
      throw error;
    }
  }, []);

  const importComponents = useCallback(async (data: any): Promise<DesignComponent[]> => {
    if (!libraryEngineRef.current) {
      throw new Error('Component library not initialized');
    }

    try {
      return await libraryEngineRef.current.importComponentLibrary(data);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to import components'
      }));
      throw error;
    }
  }, []);

  // Performance and analytics
  const getPerformanceMetrics = useCallback(() => {
    if (!libraryEngineRef.current || !assetManagerRef.current) {
      return null;
    }

    return {
      componentCount: state.components.length,
      instanceCount: state.instances.length,
      assetCount: state.assets.length,
      totalAssetSize: state.assets.reduce((sum, asset) => sum + asset.size, 0),
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0
    };
  }, [state]);

  const getAnalytics = useCallback(() => {
    if (!libraryEngineRef.current || !assetManagerRef.current) {
      return null;
    }

    return {
      library: assetManagerRef.current.getLibraryAnalytics(),
      components: state.components.map(c => ({
        id: c.id,
        name: c.name,
        usage: c.usage,
        created: c.created,
        updated: c.updated
      }))
    };
  }, [state]);

  // Error handling
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: undefined }));
  }, []);

  // Cleanup
  const dispose = useCallback(() => {
    if (saveIntervalRef.current) {
      clearInterval(saveIntervalRef.current);
    }
    if (performanceMonitorRef.current) {
      clearInterval(performanceMonitorRef.current);
    }
    if (libraryEngineRef.current) {
      libraryEngineRef.current.dispose();
    }
    if (assetManagerRef.current) {
      assetManagerRef.current.dispose();
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      dispose();
    };
  }, [dispose]);

  // Memoized actions
  const actions = useMemo(() => ({
    createComponent,
    updateComponent,
    deleteComponent,
    duplicateComponent,
    createComponentInstance,
    updateComponentInstance,
    deleteComponentInstance,
    setInstanceOverride,
    uploadAsset,
    uploadAssets,
    deleteAsset,
    updateAsset,
    searchComponents,
    searchAssets,
    setSearchQuery,
    setFilters,
    clearFilters,
    createCategory,
    createAssetCategory,
    createAssetCollection,
    selectComponent,
    selectAsset,
    selectInstance,
    clearSelection,
    setViewMode,
    exportComponents,
    importComponents,
    getPerformanceMetrics,
    getAnalytics,
    clearError,
    dispose
  }), [
    createComponent,
    updateComponent,
    deleteComponent,
    duplicateComponent,
    createComponentInstance,
    updateComponentInstance,
    deleteComponentInstance,
    setInstanceOverride,
    uploadAsset,
    uploadAssets,
    deleteAsset,
    updateAsset,
    searchComponents,
    searchAssets,
    setSearchQuery,
    setFilters,
    clearFilters,
    createCategory,
    createAssetCategory,
    createAssetCollection,
    selectComponent,
    selectAsset,
    selectInstance,
    clearSelection,
    setViewMode,
    exportComponents,
    importComponents,
    getPerformanceMetrics,
    getAnalytics,
    clearError,
    dispose
  ]);

  return {
    state,
    actions,
    libraryEngine: libraryEngineRef.current,
    assetManager: assetManagerRef.current
  };
};

export default useComponentLibrary;