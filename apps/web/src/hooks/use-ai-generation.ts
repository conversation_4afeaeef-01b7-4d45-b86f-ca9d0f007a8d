import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { fabric } from 'fabric';
import { 
  EnhancedReplicateService, 
  ReplicateModelConfig, 
  ModelRegistry,
  CostTracker,
  GenerationResult,
  AIGenerationRequest,
  WorkflowResult,
  GenerationOptions,
  GenerationProgress,
  ModelAnalytics,
  TimeRange
} from '../lib/ai/replicate-api-service';
import { 
  AICanvasIntegration, 
  AICanvasOperation,
  CanvasAIMetrics,
  AIImagePlacementOptions,
  ImageToImageRequest,
  InpaintingRegion,
  StyleTransferRequest,
  CanvasSelectionArea
} from '../lib/ai/ai-canvas-integration';
import { EnhancedHybridCanvasManager } from '../lib/canvas/enhanced-hybrid-canvas';
import { AdvancedLayerManager } from '../lib/canvas/advanced-layer-manager';
import { AIProvider } from '../lib/ai/ai-service-base';

// Hook state types
export interface AIGenerationState {
  isInitialized: boolean;
  isGenerating: boolean;
  availableModels: ReplicateModelConfig[];
  selectedModel?: ReplicateModelConfig;
  generationHistory: GenerationResult[];
  activeOperations: AICanvasOperation[];
  operationHistory: AICanvasOperation[];
  currentProgress?: GenerationProgress;
  lastError?: string;
  metrics: CanvasAIMetrics;
  costSummary: {
    daily: number;
    monthly: number;
    perGeneration: number;
    remainingBudget: { daily: number; monthly: number };
  };
}

export interface AIGenerationActions {
  // Model management
  selectModel: (modelId: string) => void;
  getOptimalModel: (
    task: string,
    inputType?: string,
    quality?: 'fast' | 'balanced' | 'quality',
    budget?: number
  ) => Promise<ReplicateModelConfig>;
  
  // Core generation methods
  generateTextToImage: (
    prompt: string,
    options?: Partial<AIImagePlacementOptions>,
    generationOptions?: {
      model?: string;
      quality?: 'fast' | 'balanced' | 'quality';
      width?: number;
      height?: number;
      negativePrompt?: string;
      seed?: number;
    }
  ) => Promise<string>;
  
  generateImageToImage: (
    request: ImageToImageRequest,
    options?: Partial<AIImagePlacementOptions>
  ) => Promise<string>;
  
  performInpainting: (
    region: InpaintingRegion,
    sourceImage: string,
    options?: Partial<AIImagePlacementOptions>
  ) => Promise<string>;
  
  performStyleTransfer: (
    request: StyleTransferRequest,
    options?: Partial<AIImagePlacementOptions>
  ) => Promise<string>;
  
  enhanceImage: (
    imageData: string,
    enhancementType: 'upscale' | 'restore' | 'denoise' | 'enhance',
    options?: Partial<AIImagePlacementOptions>
  ) => Promise<string>;
  
  executeWorkflow: (request: AIGenerationRequest) => Promise<string[]>;
  
  // Canvas interaction
  getSelectedArea: () => CanvasSelectionArea | null;
  createSelectionMask: (area: CanvasSelectionArea) => string;
  exportCanvasAsImage: () => string;
  
  // Operation management
  cancelOperation: (operationId: string) => boolean;
  retryOperation: (operationId: string) => Promise<void>;
  
  // Batch operations
  generateBatch: (requests: Array<{
    type: 'text-to-image' | 'image-to-image' | 'enhancement';
    prompt?: string;
    request?: any;
    options?: Partial<AIImagePlacementOptions>;
  }>) => Promise<string[]>;
  
  // Cost and budget management
  setDailyBudget: (budget: number) => void;
  setMonthlyBudget: (budget: number) => void;
  
  // Cache management
  clearCache: () => void;
  getCacheStats: () => any;
  
  // Analytics
  getModelAnalytics: (timeRange?: TimeRange) => ModelAnalytics;
}

export interface AIGenerationHookOptions {
  apiKey: string;
  autoSelectOptimalModel?: boolean;
  enableCaching?: boolean;
  enablePerformanceMonitoring?: boolean;
  defaultQuality?: 'fast' | 'balanced' | 'quality';
  maxRetries?: number;
  progressCallback?: (progress: GenerationProgress) => void;
}

export interface AIGenerationHookReturn {
  state: AIGenerationState;
  actions: AIGenerationActions;
  service: EnhancedReplicateService | null;
  canvasIntegration: AICanvasIntegration | null;
  isReady: boolean;
}

// Custom hook for AI generation with canvas integration
export function useAIGeneration(
  canvasManager: EnhancedHybridCanvasManager | null,
  layerManager: AdvancedLayerManager | null,
  options: AIGenerationHookOptions
): AIGenerationHookReturn {
  const {
    apiKey,
    autoSelectOptimalModel = true,
    enableCaching = true,
    enablePerformanceMonitoring = true,
    defaultQuality = 'balanced',
    maxRetries = 3,
    progressCallback
  } = options;

  // State management
  const [state, setState] = useState<AIGenerationState>({
    isInitialized: false,
    isGenerating: false,
    availableModels: [],
    generationHistory: [],
    activeOperations: [],
    operationHistory: [],
    metrics: {
      totalGenerations: 0,
      successfulGenerations: 0,
      averageProcessingTime: 0,
      totalCost: 0,
      cacheHitRate: 0,
      popularWorkflows: [],
      layerDistribution: { ai: 0, manual: 0 }
    },
    costSummary: {
      daily: 0,
      monthly: 0,
      perGeneration: 0,
      remainingBudget: { daily: 0, monthly: 0 }
    }
  });

  // Service refs
  const serviceRef = useRef<EnhancedReplicateService | null>(null);
  const canvasIntegrationRef = useRef<AICanvasIntegration | null>(null);
  const metricsIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize services
  useEffect(() => {
    if (!apiKey) {
      console.error('API key is required for AI generation');
      return;
    }

    const initializeServices = async () => {
      try {
        setState(prev => ({ ...prev, lastError: undefined }));

        // Create Replicate service
        const provider: AIProvider & { apiKey: string } = {
          name: 'replicate',
          apiKey,
          model: 'stability-ai/sdxl',
          maxTokens: 4000,
          temperature: 0.1
        };

        const replicateService = new EnhancedReplicateService(provider);
        serviceRef.current = replicateService;

        // Create canvas integration if canvas managers are available
        if (canvasManager && layerManager) {
          const canvasIntegration = new AICanvasIntegration(
            canvasManager,
            layerManager,
            replicateService
          );
          canvasIntegrationRef.current = canvasIntegration;
          
          // Set up canvas integration event listeners
          setupCanvasIntegrationEvents(canvasIntegration);
        }

        // Set up service event listeners
        setupServiceEvents(replicateService);

        // Load available models
        const models = replicateService.getAvailableModels();
        
        // Start metrics monitoring
        if (enablePerformanceMonitoring) {
          startMetricsMonitoring();
        }

        setState(prev => ({
          ...prev,
          isInitialized: true,
          availableModels: models,
          selectedModel: autoSelectOptimalModel ? models[0] : undefined
        }));

      } catch (error) {
        console.error('Failed to initialize AI generation services:', error);
        setState(prev => ({
          ...prev,
          lastError: error instanceof Error ? error.message : 'Unknown error',
          isInitialized: false
        }));
      }
    };

    initializeServices();

    return () => {
      // Cleanup
      if (serviceRef.current) {
        serviceRef.current.dispose();
        serviceRef.current = null;
      }
      if (canvasIntegrationRef.current) {
        canvasIntegrationRef.current.dispose();
        canvasIntegrationRef.current = null;
      }
      if (metricsIntervalRef.current) {
        clearInterval(metricsIntervalRef.current);
        metricsIntervalRef.current = null;
      }
    };
  }, [apiKey, canvasManager, layerManager, autoSelectOptimalModel, enablePerformanceMonitoring]);

  // Set up service event listeners
  const setupServiceEvents = useCallback((service: EnhancedReplicateService) => {
    service.on('budget:exceeded', (data) => {
      setState(prev => ({
        ...prev,
        lastError: `Budget exceeded: ${data.type} budget of $${data.budget} exceeded (spent: $${data.cost})`
      }));
    });

    service.on('generation:completed', (result) => {
      setState(prev => ({
        ...prev,
        generationHistory: [result, ...prev.generationHistory.slice(0, 99)] // Keep last 100
      }));
    });
  }, []);

  // Set up canvas integration event listeners
  const setupCanvasIntegrationEvents = useCallback((integration: AICanvasIntegration) => {
    integration.on('ai-canvas:operation:created', (operation) => {
      setState(prev => ({
        ...prev,
        activeOperations: [...prev.activeOperations, operation],
        isGenerating: true
      }));
    });

    integration.on('ai-canvas:operation:progress', ({ operation, progress, message }) => {
      setState(prev => ({
        ...prev,
        currentProgress: {
          step: 1,
          totalSteps: 1,
          status: 'processing',
          message: message || 'Processing...',
          progress
        }
      }));
      
      if (progressCallback) {
        progressCallback({
          step: 1,
          totalSteps: 1,
          status: 'processing',
          message: message || 'Processing...',
          progress
        });
      }
    });

    integration.on('ai-canvas:operation:completed', (operation) => {
      setState(prev => ({
        ...prev,
        activeOperations: prev.activeOperations.filter(op => op.id !== operation.id),
        operationHistory: [operation, ...prev.operationHistory.slice(0, 99)],
        isGenerating: prev.activeOperations.length <= 1,
        currentProgress: undefined
      }));
    });

    integration.on('ai-canvas:operation:failed', (operation) => {
      setState(prev => ({
        ...prev,
        activeOperations: prev.activeOperations.filter(op => op.id !== operation.id),
        operationHistory: [operation, ...prev.operationHistory.slice(0, 99)],
        isGenerating: prev.activeOperations.length <= 1,
        lastError: operation.error,
        currentProgress: undefined
      }));
    });

    integration.on('ai-canvas:metrics:updated', (metrics) => {
      setState(prev => ({ ...prev, metrics }));
    });
  }, [progressCallback]);

  // Start metrics monitoring
  const startMetricsMonitoring = useCallback(() => {
    if (metricsIntervalRef.current) {
      clearInterval(metricsIntervalRef.current);
    }

    metricsIntervalRef.current = setInterval(() => {
      updateMetrics();
    }, 5000); // Update every 5 seconds
  }, []);

  // Update metrics
  const updateMetrics = useCallback(() => {
    if (!canvasIntegrationRef.current) return;

    const metrics = canvasIntegrationRef.current.getMetrics();
    const costSummary = canvasIntegrationRef.current.getCostSummary();
    
    setState(prev => ({
      ...prev,
      metrics,
      costSummary
    }));
  }, []);

  // Action creators
  const actions = useMemo<AIGenerationActions>(() => ({
    // Model management
    selectModel: (modelId: string) => {
      if (!serviceRef.current) return;
      
      const model = serviceRef.current.getAvailableModels().find(m => m.modelId === modelId);
      if (model) {
        setState(prev => ({ ...prev, selectedModel: model }));
      }
    },

    getOptimalModel: async (
      task: string,
      inputType: string = 'text',
      quality: 'fast' | 'balanced' | 'quality' = defaultQuality,
      budget?: number
    ): Promise<ReplicateModelConfig> => {
      if (!serviceRef.current) throw new Error('Service not initialized');
      
      const model = await serviceRef.current.selectOptimalModel(task, inputType, quality, budget);
      
      if (autoSelectOptimalModel) {
        setState(prev => ({ ...prev, selectedModel: model }));
      }
      
      return model;
    },

    // Core generation methods
    generateTextToImage: async (
      prompt: string,
      options: Partial<AIImagePlacementOptions> = {},
      generationOptions: {
        model?: string;
        quality?: 'fast' | 'balanced' | 'quality';
        width?: number;
        height?: number;
        negativePrompt?: string;
        seed?: number;
      } = {}
    ): Promise<string> => {
      if (!canvasIntegrationRef.current) throw new Error('Canvas integration not initialized');
      
      const quality = generationOptions.quality || defaultQuality;
      const genOptions = {
        enableCaching,
        quality,
        progressCallback: progressCallback,
        ...generationOptions
      };
      
      return canvasIntegrationRef.current.generateTextToImage(prompt, options, genOptions);
    },

    generateImageToImage: async (
      request: ImageToImageRequest,
      options: Partial<AIImagePlacementOptions> = {}
    ): Promise<string> => {
      if (!canvasIntegrationRef.current) throw new Error('Canvas integration not initialized');
      return canvasIntegrationRef.current.generateImageToImage(request, options);
    },

    performInpainting: async (
      region: InpaintingRegion,
      sourceImage: string,
      options: Partial<AIImagePlacementOptions> = {}
    ): Promise<string> => {
      if (!canvasIntegrationRef.current) throw new Error('Canvas integration not initialized');
      return canvasIntegrationRef.current.performInpainting(region, sourceImage, options);
    },

    performStyleTransfer: async (
      request: StyleTransferRequest,
      options: Partial<AIImagePlacementOptions> = {}
    ): Promise<string> => {
      if (!canvasIntegrationRef.current) throw new Error('Canvas integration not initialized');
      return canvasIntegrationRef.current.performStyleTransfer(request, options);
    },

    enhanceImage: async (
      imageData: string,
      enhancementType: 'upscale' | 'restore' | 'denoise' | 'enhance',
      options: Partial<AIImagePlacementOptions> = {}
    ): Promise<string> => {
      if (!canvasIntegrationRef.current) throw new Error('Canvas integration not initialized');
      return canvasIntegrationRef.current.enhanceImage(imageData, enhancementType, options);
    },

    executeWorkflow: async (request: AIGenerationRequest): Promise<string[]> => {
      if (!canvasIntegrationRef.current) throw new Error('Canvas integration not initialized');
      return canvasIntegrationRef.current.executeAIWorkflow(request);
    },

    // Canvas interaction
    getSelectedArea: (): CanvasSelectionArea | null => {
      if (!canvasIntegrationRef.current) return null;
      return canvasIntegrationRef.current.getSelectedArea();
    },

    createSelectionMask: (area: CanvasSelectionArea): string => {
      if (!canvasIntegrationRef.current) throw new Error('Canvas integration not initialized');
      return canvasIntegrationRef.current.createSelectionMask(area);
    },

    exportCanvasAsImage: (): string => {
      if (!canvasIntegrationRef.current) throw new Error('Canvas integration not initialized');
      return canvasIntegrationRef.current.exportCanvasAsImage();
    },

    // Operation management
    cancelOperation: (operationId: string): boolean => {
      if (!canvasIntegrationRef.current) return false;
      return canvasIntegrationRef.current.cancelOperation(operationId);
    },

    retryOperation: async (operationId: string): Promise<void> => {
      const operation = canvasIntegrationRef.current?.getOperation(operationId);
      if (!operation || !canvasIntegrationRef.current) {
        throw new Error('Operation not found or canvas integration not initialized');
      }

      // Retry the operation based on its type
      try {
        switch (operation.type) {
          case 'text-to-image':
            await actions.generateTextToImage(
              operation.request.prompt,
              operation.request.options,
              operation.request.generationOptions
            );
            break;
          case 'image-to-image':
            await actions.generateImageToImage(operation.request, operation.request.options);
            break;
          case 'inpainting':
            await actions.performInpainting(
              operation.request.region,
              operation.request.sourceImage,
              operation.request.options
            );
            break;
          case 'style-transfer':
            await actions.performStyleTransfer(operation.request, operation.request.options);
            break;
          case 'enhancement':
            await actions.enhanceImage(
              operation.request.imageData,
              operation.request.enhancementType,
              operation.request.options
            );
            break;
          default:
            throw new Error(`Cannot retry operation of type: ${operation.type}`);
        }
      } catch (error) {
        console.error('Retry operation failed:', error);
        throw error;
      }
    },

    // Batch operations
    generateBatch: async (requests: Array<{
      type: 'text-to-image' | 'image-to-image' | 'enhancement';
      prompt?: string;
      request?: any;
      options?: Partial<AIImagePlacementOptions>;
    }>): Promise<string[]> => {
      const results: string[] = [];
      
      // Process requests sequentially to avoid overwhelming the API
      for (const req of requests) {
        try {
          let layerId: string;
          
          switch (req.type) {
            case 'text-to-image':
              layerId = await actions.generateTextToImage(req.prompt!, req.options);
              break;
            case 'image-to-image':
              layerId = await actions.generateImageToImage(req.request, req.options);
              break;
            case 'enhancement':
              layerId = await actions.enhanceImage(
                req.request.imageData,
                req.request.enhancementType,
                req.options
              );
              break;
            default:
              throw new Error(`Unsupported batch operation type: ${req.type}`);
          }
          
          results.push(layerId);
          
          // Add small delay between requests
          await new Promise(resolve => setTimeout(resolve, 1000));
          
        } catch (error) {
          console.error(`Batch request failed:`, error);
          // Continue with other requests even if one fails
        }
      }
      
      return results;
    },

    // Cost and budget management
    setDailyBudget: (budget: number) => {
      if (!serviceRef.current) return;
      serviceRef.current.setDailyBudget(budget);
    },

    setMonthlyBudget: (budget: number) => {
      if (!serviceRef.current) return;
      serviceRef.current.setMonthlyBudget(budget);
    },

    // Cache management
    clearCache: () => {
      if (!serviceRef.current) return;
      serviceRef.current.clearCache();
    },

    getCacheStats: () => {
      if (!serviceRef.current) return {};
      return serviceRef.current.getCacheStats();
    },

    // Analytics
    getModelAnalytics: (timeRange?: TimeRange): ModelAnalytics => {
      if (!serviceRef.current) {
        return {
          popularModels: [],
          performanceMetrics: [],
          costAnalysis: { totalSpent: 0, avgCostPerGeneration: 0, topCostlyModels: [] },
          qualityTrends: [],
          recommendations: []
        };
      }
      
      const range = timeRange || {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        end: new Date()
      };
      
      return serviceRef.current.getModelAnalytics(range);
    }
  }), [
    autoSelectOptimalModel,
    defaultQuality,
    enableCaching,
    progressCallback,
    maxRetries
  ]);

  // Computed values
  const isReady = useMemo(() => {
    return state.isInitialized && 
           !!serviceRef.current && 
           (!canvasManager || !!canvasIntegrationRef.current);
  }, [state.isInitialized, canvasManager]);

  return {
    state,
    actions,
    service: serviceRef.current,
    canvasIntegration: canvasIntegrationRef.current,
    isReady
  };
}

// Helper hooks for specific AI generation workflows

export function useTextToImageGeneration(
  aiGeneration: AIGenerationHookReturn,
  defaultOptions: Partial<AIImagePlacementOptions> = {}
) {
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [quality, setQuality] = useState<'fast' | 'balanced' | 'quality'>('balanced');
  const [dimensions, setDimensions] = useState({ width: 1024, height: 1024 });
  const [seed, setSeed] = useState<number | undefined>();

  const generate = useCallback(async () => {
    if (!prompt.trim() || !aiGeneration.isReady) return null;

    return aiGeneration.actions.generateTextToImage(
      prompt,
      defaultOptions,
      {
        quality,
        width: dimensions.width,
        height: dimensions.height,
        negativePrompt: negativePrompt || undefined,
        seed
      }
    );
  }, [prompt, negativePrompt, quality, dimensions, seed, defaultOptions, aiGeneration]);

  return {
    prompt,
    setPrompt,
    negativePrompt,
    setNegativePrompt,
    quality,
    setQuality,
    dimensions,
    setDimensions,
    seed,
    setSeed,
    generate,
    isGenerating: aiGeneration.state.isGenerating,
    progress: aiGeneration.state.currentProgress
  };
}

export function useImageEnhancement(
  aiGeneration: AIGenerationHookReturn,
  defaultOptions: Partial<AIImagePlacementOptions> = {}
) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [enhancementType, setEnhancementType] = useState<'upscale' | 'restore' | 'denoise' | 'enhance'>('upscale');

  const enhance = useCallback(async () => {
    if (!selectedImage || !aiGeneration.isReady) return null;

    return aiGeneration.actions.enhanceImage(selectedImage, enhancementType, defaultOptions);
  }, [selectedImage, enhancementType, defaultOptions, aiGeneration]);

  const enhanceFromCanvas = useCallback(async () => {
    if (!aiGeneration.isReady) return null;

    const canvasImage = aiGeneration.actions.exportCanvasAsImage();
    return aiGeneration.actions.enhanceImage(canvasImage, enhancementType, defaultOptions);
  }, [enhancementType, defaultOptions, aiGeneration]);

  return {
    selectedImage,
    setSelectedImage,
    enhancementType,
    setEnhancementType,
    enhance,
    enhanceFromCanvas,
    isGenerating: aiGeneration.state.isGenerating,
    progress: aiGeneration.state.currentProgress
  };
}

export function useInpainting(
  aiGeneration: AIGenerationHookReturn,
  defaultOptions: Partial<AIImagePlacementOptions> = {}
) {
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [strength, setStrength] = useState(0.9);

  const inpaintSelection = useCallback(async () => {
    if (!prompt.trim() || !aiGeneration.isReady) return null;

    const selectedArea = aiGeneration.actions.getSelectedArea();
    if (!selectedArea) {
      throw new Error('No area selected for inpainting');
    }

    const sourceImage = aiGeneration.actions.exportCanvasAsImage();
    const mask = aiGeneration.actions.createSelectionMask(selectedArea);

    const region: InpaintingRegion = {
      mask,
      prompt,
      negativePrompt: negativePrompt || undefined,
      strength
    };

    return aiGeneration.actions.performInpainting(region, sourceImage, defaultOptions);
  }, [prompt, negativePrompt, strength, defaultOptions, aiGeneration]);

  return {
    prompt,
    setPrompt,
    negativePrompt,
    setNegativePrompt,
    strength,
    setStrength,
    inpaintSelection,
    selectedArea: aiGeneration.actions.getSelectedArea(),
    isGenerating: aiGeneration.state.isGenerating,
    progress: aiGeneration.state.currentProgress
  };
}