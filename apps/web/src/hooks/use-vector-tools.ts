import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { fabric } from 'fabric';
import { 
  VectorGraphicsEngine, 
  VectorTool, 
  VectorSelection, 
  VectorShape, 
  VectorEditingOptions,
  VectorPoint
} from '../lib/canvas/vector-graphics-engine';
import { ShapeLibrary, ShapeCreationOptions, ShapeTemplate } from '../lib/canvas/shape-library';
import { 
  VectorToolsManager, 
  VectorToolConfig, 
  ToolState, 
  ToolWorkflow 
} from '../lib/canvas/vector-tools-manager';

// Hook types and interfaces
export interface VectorToolsState {
  activeTool: VectorTool;
  toolOptions: Record<string, any>;
  isDrawing: boolean;
  selection: VectorSelection;
  editingOptions: VectorEditingOptions;
  isInitialized: boolean;
  error: string | null;
}

export interface VectorToolsActions {
  setTool: (tool: VectorTool) => void;
  setToolOption: (tool: VectorTool, option: string, value: any) => void;
  createShape: (templateId: string, options: ShapeCreationOptions) => void;
  selectAll: () => void;
  selectNone: () => void;
  selectInvert: () => void;
  deleteSelection: () => void;
  copySelection: () => void;
  pasteSelection: () => void;
  undo: () => void;
  redo: () => void;
  alignObjects: (alignment: string) => void;
  distributeObjects: (distribution: 'horizontal' | 'vertical') => void;
  setEditingOptions: (options: Partial<VectorEditingOptions>) => void;
  startWorkflow: (workflowId: string) => void;
}

export interface VectorToolsPerformance {
  renderTime: number;
  objectCount: number;
  memoryUsage: number;
  isOptimized: boolean;
}

export interface VectorToolsHookOptions {
  enablePerformanceMonitoring?: boolean;
  enableAutoSave?: boolean;
  autoSaveInterval?: number;
  maxHistorySize?: number;
  enableKeyboardShortcuts?: boolean;
}

export interface VectorToolsHookReturn {
  state: VectorToolsState;
  actions: VectorToolsActions;
  performance: VectorToolsPerformance;
  tools: VectorToolConfig[];
  workflows: ToolWorkflow[];
  shapeLibrary: {
    templates: ShapeTemplate[];
    categories: Array<{ category: string; count: number }>;
    searchTemplates: (query: string) => ShapeTemplate[];
    createCustomShape: (shape: VectorShape) => string;
  };
  history: {
    canUndo: boolean;
    canRedo: boolean;
    undoStack: number;
    redoStack: number;
  };
}

// Performance monitoring hook
export function useVectorToolsPerformance(
  vectorEngine?: VectorGraphicsEngine,
  enabled: boolean = false
): VectorToolsPerformance {
  const [performance, setPerformance] = useState<VectorToolsPerformance>({
    renderTime: 0,
    objectCount: 0,
    memoryUsage: 0,
    isOptimized: true
  });

  const measurePerformance = useCallback(() => {
    if (!vectorEngine || !enabled) return;

    const startTime = performance.now();
    
    // Measure object count
    const shapes = vectorEngine.getAllVectorShapes();
    const objectCount = shapes.length;
    
    // Estimate memory usage (simplified)
    const memoryUsage = objectCount * 1024; // Rough estimate
    
    const renderTime = performance.now() - startTime;
    
    setPerformance({
      renderTime,
      objectCount,
      memoryUsage,
      isOptimized: renderTime < 16 && objectCount < 1000 // 60fps threshold
    });
  }, [vectorEngine, enabled]);

  useEffect(() => {
    if (!enabled || !vectorEngine) return;

    const interval = setInterval(measurePerformance, 1000);
    return () => clearInterval(interval);
  }, [measurePerformance, enabled, vectorEngine]);

  return performance;
}

// Shape library integration hook
export function useShapeLibrary(shapeLibrary?: ShapeLibrary) {
  const [templates, setTemplates] = useState<ShapeTemplate[]>([]);
  const [categories, setCategories] = useState<Array<{ category: string; count: number }>>([]);

  useEffect(() => {
    if (!shapeLibrary) return;

    setTemplates(shapeLibrary.getAllTemplates());
    setCategories(shapeLibrary.getAllCategories());
  }, [shapeLibrary]);

  const searchTemplates = useCallback((query: string) => {
    if (!shapeLibrary) return [];
    return shapeLibrary.searchTemplates(query);
  }, [shapeLibrary]);

  const createCustomShape = useCallback((shape: VectorShape) => {
    if (!shapeLibrary) return '';
    return shapeLibrary.saveCustomShape(shape);
  }, [shapeLibrary]);

  return {
    templates,
    categories,
    searchTemplates,
    createCustomShape
  };
}

// History management hook
export function useVectorHistory(toolsManager?: VectorToolsManager) {
  const [historyState, setHistoryState] = useState({
    canUndo: false,
    canRedo: false,
    undoStack: 0,
    redoStack: 0
  });

  useEffect(() => {
    if (!toolsManager) return;

    const handleHistoryUpdate = (data: any) => {
      setHistoryState({
        canUndo: data.canUndo,
        canRedo: data.canRedo,
        undoStack: data.undoStack || 0,
        redoStack: data.redoStack || 0
      });
    };

    toolsManager.on('history:updated', handleHistoryUpdate);
    
    return () => {
      toolsManager.off('history:updated', handleHistoryUpdate);
    };
  }, [toolsManager]);

  return historyState;
}

// Auto-save functionality hook
export function useVectorAutoSave(
  toolsManager?: VectorToolsManager,
  enabled: boolean = false,
  interval: number = 30000
) {
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  const saveProject = useCallback(async () => {
    if (!toolsManager) return;

    try {
      // Export current state
      const settings = toolsManager.exportToolSettings();
      
      // Save to localStorage or send to server
      localStorage.setItem('vector-tools-autosave', settings);
      localStorage.setItem('vector-tools-autosave-timestamp', Date.now().toString());
      
      console.log('Auto-saved vector tools state');
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [toolsManager]);

  const scheduleAutoSave = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
    
    if (enabled) {
      autoSaveTimeoutRef.current = setTimeout(saveProject, interval);
    }
  }, [enabled, interval, saveProject]);

  useEffect(() => {
    if (!toolsManager || !enabled) return;

    // Schedule auto-save on any significant change
    const handleChange = () => scheduleAutoSave();
    
    toolsManager.on('shape:created', handleChange);
    toolsManager.on('shape:updated', handleChange);
    toolsManager.on('shape:deleted', handleChange);
    toolsManager.on('tool:option:changed', handleChange);

    return () => {
      toolsManager.off('shape:created', handleChange);
      toolsManager.off('shape:updated', handleChange);
      toolsManager.off('shape:deleted', handleChange);
      toolsManager.off('tool:option:changed', handleChange);
      
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [toolsManager, enabled, scheduleAutoSave]);

  // Load auto-saved state on mount
  useEffect(() => {
    if (!toolsManager || !enabled) return;

    try {
      const savedSettings = localStorage.getItem('vector-tools-autosave');
      const timestamp = localStorage.getItem('vector-tools-autosave-timestamp');
      
      if (savedSettings && timestamp) {
        const saveTime = parseInt(timestamp);
        const timeDiff = Date.now() - saveTime;
        
        // Only restore if saved within last hour
        if (timeDiff < 60 * 60 * 1000) {
          toolsManager.importToolSettings(savedSettings);
          console.log('Restored auto-saved vector tools state');
        }
      }
    } catch (error) {
      console.error('Failed to restore auto-saved state:', error);
    }
  }, [toolsManager, enabled]);
}

// Main vector tools hook
export function useVectorTools(
  canvas: fabric.Canvas | null,
  options: VectorToolsHookOptions = {}
): VectorToolsHookReturn {
  const {
    enablePerformanceMonitoring = false,
    enableAutoSave = false,
    autoSaveInterval = 30000,
    maxHistorySize = 50,
    enableKeyboardShortcuts = true
  } = options;

  // Core instances
  const vectorEngineRef = useRef<VectorGraphicsEngine | null>(null);
  const shapeLibraryRef = useRef<ShapeLibrary | null>(null);
  const toolsManagerRef = useRef<VectorToolsManager | null>(null);

  // State management
  const [state, setState] = useState<VectorToolsState>({
    activeTool: 'select',
    toolOptions: {},
    isDrawing: false,
    selection: {
      selectedObjects: [],
      selectedNodes: [],
      manipulationMode: 'object'
    },
    editingOptions: {
      snapToGrid: true,
      snapToGuides: true,
      snapToObjects: true,
      snapTolerance: 5,
      showGrid: true,
      gridSize: 20,
      showGuides: true,
      showNodes: true,
      showHandles: true,
      nodeSize: 6,
      handleSize: 4
    },
    isInitialized: false,
    error: null
  });

  const [tools, setTools] = useState<VectorToolConfig[]>([]);
  const [workflows, setWorkflows] = useState<ToolWorkflow[]>([]);

  // Initialize vector tools system
  useEffect(() => {
    if (!canvas) return;

    try {
      // Initialize core components
      const vectorEngine = new VectorGraphicsEngine(canvas, state.editingOptions);
      const shapeLibrary = new ShapeLibrary();
      const toolsManager = new VectorToolsManager(vectorEngine, shapeLibrary, canvas);

      vectorEngineRef.current = vectorEngine;
      shapeLibraryRef.current = shapeLibrary;
      toolsManagerRef.current = toolsManager;

      // Set up event handlers
      const handleToolChanged = (tool: VectorTool) => {
        setState(prev => ({ ...prev, activeTool: tool }));
      };

      const handleSelectionChanged = (selection: VectorSelection) => {
        setState(prev => ({ ...prev, selection }));
      };

      const handleEditingOptionsChanged = (options: VectorEditingOptions) => {
        setState(prev => ({ ...prev, editingOptions: options }));
      };

      const handleError = (error: any) => {
        setState(prev => ({ ...prev, error: error.message || 'Unknown error' }));
      };

      // Register event listeners
      toolsManager.on('tool:changed', handleToolChanged);
      toolsManager.on('selection:changed', handleSelectionChanged);
      toolsManager.on('editing:options:changed', handleEditingOptionsChanged);
      toolsManager.on('error', handleError);

      // Load available tools and workflows
      setTools(toolsManager.getAllTools());
      setWorkflows(toolsManager.getWorkflows());

      setState(prev => ({ 
        ...prev, 
        isInitialized: true,
        toolOptions: toolsManager.getToolState().toolOptions
      }));

      // Cleanup function
      return () => {
        toolsManager.off('tool:changed', handleToolChanged);
        toolsManager.off('selection:changed', handleSelectionChanged);
        toolsManager.off('editing:options:changed', handleEditingOptionsChanged);
        toolsManager.off('error', handleError);
        
        vectorEngine.dispose();
        shapeLibrary.dispose();
        toolsManager.dispose();
        
        vectorEngineRef.current = null;
        shapeLibraryRef.current = null;
        toolsManagerRef.current = null;
      };
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to initialize vector tools'
      }));
    }
  }, [canvas]);

  // Hook integrations
  const performance = useVectorToolsPerformance(
    vectorEngineRef.current || undefined, 
    enablePerformanceMonitoring
  );

  const shapeLibrary = useShapeLibrary(shapeLibraryRef.current || undefined);
  const history = useVectorHistory(toolsManagerRef.current || undefined);

  useVectorAutoSave(
    toolsManagerRef.current || undefined,
    enableAutoSave,
    autoSaveInterval
  );

  // Actions
  const actions = useMemo<VectorToolsActions>(() => ({
    setTool: (tool: VectorTool) => {
      toolsManagerRef.current?.setTool(tool);
    },

    setToolOption: (tool: VectorTool, option: string, value: any) => {
      toolsManagerRef.current?.setToolOption(tool, option, value);
    },

    createShape: (templateId: string, options: ShapeCreationOptions) => {
      toolsManagerRef.current?.createShape(templateId, options);
    },

    selectAll: () => {
      toolsManagerRef.current?.selectAll();
    },

    selectNone: () => {
      toolsManagerRef.current?.selectNone();
    },

    selectInvert: () => {
      toolsManagerRef.current?.selectInvert();
    },

    deleteSelection: () => {
      toolsManagerRef.current?.deleteSelection();
    },

    copySelection: () => {
      toolsManagerRef.current?.copySelection();
    },

    pasteSelection: () => {
      toolsManagerRef.current?.pasteSelection();
    },

    undo: () => {
      toolsManagerRef.current?.undo();
    },

    redo: () => {
      toolsManagerRef.current?.redo();
    },

    alignObjects: (alignment: string) => {
      const validAlignments = ['left', 'center', 'right', 'top', 'middle', 'bottom'];
      if (validAlignments.includes(alignment)) {
        toolsManagerRef.current?.alignObjects(alignment as any);
      }
    },

    distributeObjects: (distribution: 'horizontal' | 'vertical') => {
      toolsManagerRef.current?.distributeObjects(distribution);
    },

    setEditingOptions: (options: Partial<VectorEditingOptions>) => {
      toolsManagerRef.current?.setEditingOptions(options);
    },

    startWorkflow: (workflowId: string) => {
      toolsManagerRef.current?.startWorkflow(workflowId);
    }
  }), []);

  return {
    state,
    actions,
    performance,
    tools,
    workflows,
    shapeLibrary,
    history
  };
}

// Specialized hook for path editing
export function usePathEditor(vectorEngine: VectorGraphicsEngine | null, pathId: string | null) {
  const [pathData, setPathData] = useState<VectorPoint[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedNodes, setSelectedNodes] = useState<number[]>([]);

  useEffect(() => {
    if (!vectorEngine || !pathId) return;

    const shape = vectorEngine.getVectorShape(pathId);
    if (shape?.path) {
      setPathData(shape.path.points);
    }
  }, [vectorEngine, pathId]);

  const startEditing = useCallback(() => {
    if (!vectorEngine || !pathId) return;
    
    const pathEditor = vectorEngine.editPath(pathId);
    if (pathEditor) {
      setIsEditing(true);
    }
  }, [vectorEngine, pathId]);

  const stopEditing = useCallback(() => {
    setIsEditing(false);
    setSelectedNodes([]);
  }, []);

  const addNode = useCallback((index: number, point: VectorPoint) => {
    // Implementation would call vector engine path editor
    setPathData(prev => {
      const newPath = [...prev];
      newPath.splice(index, 0, point);
      return newPath;
    });
  }, []);

  const removeNode = useCallback((index: number) => {
    setPathData(prev => {
      const newPath = [...prev];
      newPath.splice(index, 1);
      return newPath;
    });
  }, []);

  const updateNode = useCallback((index: number, updates: Partial<VectorPoint>) => {
    setPathData(prev => {
      const newPath = [...prev];
      if (newPath[index]) {
        Object.assign(newPath[index], updates);
      }
      return newPath;
    });
  }, []);

  const selectNode = useCallback((index: number, addToSelection: boolean = false) => {
    setSelectedNodes(prev => {
      if (addToSelection) {
        return prev.includes(index) ? prev : [...prev, index];
      } else {
        return [index];
      }
    });
  }, []);

  return {
    pathData,
    isEditing,
    selectedNodes,
    startEditing,
    stopEditing,
    addNode,
    removeNode,
    updateNode,
    selectNode
  };
}

// Specialized hook for shape library management
export function useShapeLibraryManager(shapeLibrary: ShapeLibrary | null) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [filteredTemplates, setFilteredTemplates] = useState<ShapeTemplate[]>([]);

  useEffect(() => {
    if (!shapeLibrary) return;

    let templates = shapeLibrary.getAllTemplates();

    // Apply category filter
    if (selectedCategory !== 'all') {
      templates = templates.filter(t => t.category === selectedCategory);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      templates = shapeLibrary.searchTemplates(searchQuery);
    }

    setFilteredTemplates(templates);
  }, [shapeLibrary, searchQuery, selectedCategory]);

  const createShapeFromTemplate = useCallback((templateId: string, options: ShapeCreationOptions) => {
    if (!shapeLibrary) return null;
    return shapeLibrary.createShape(templateId, options);
  }, [shapeLibrary]);

  const saveAsTemplate = useCallback((shape: VectorShape, metadata: any = {}) => {
    if (!shapeLibrary) return '';
    return shapeLibrary.saveCustomShape(shape, metadata);
  }, [shapeLibrary]);

  return {
    searchQuery,
    setSearchQuery,
    selectedCategory,
    setSelectedCategory,
    filteredTemplates,
    createShapeFromTemplate,
    saveAsTemplate
  };
}

// Export all hooks
export {
  useVectorToolsPerformance,
  useShapeLibrary,
  useVectorHistory,
  useVectorAutoSave,
  usePathEditor,
  useShapeLibraryManager
};