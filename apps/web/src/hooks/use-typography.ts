import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { fabric } from 'fabric';
import { 
  TypographyEngine, 
  TypographySettings, 
  TypographyObject, 
  TypographyMeasurement,
  TypographyPerformanceMetrics,
  TypographyEngineConfig,
  TextRange,
  TextPath,
  TextBox
} from '../lib/canvas/typography-engine';
import { 
  FontLibraryManager, 
  FontFamily, 
  FontFilter, 
  FontCollection, 
  FontPreview,
  FontLibraryStats,
  FontLibraryConfig
} from '../lib/canvas/font-library-manager';
import { 
  TextEffectManager, 
  TextEffectInstance, 
  TextEffectLibrary, 
  TextEffect,
  TextEffectPreset,
  TextEffectRenderOptions,
  TextEffectPerformance
} from '../lib/canvas/text-effects-manager';

// Hook types and interfaces
export interface TypographyState {
  isInitialized: boolean;
  activeTextObject: string | null;
  selectedFont: FontFamily | null;
  typographySettings: TypographySettings;
  textObjects: TypographyObject[];
  fontLibraryStats: FontLibraryStats;
  performanceMetrics: TypographyPerformanceMetrics;
  effectsPerformance: TextEffectPerformance;
  error: string | null;
  isLoading: boolean;
}

export interface TypographyActions {
  // Text object management
  createTextObject: (content: string, settings?: Partial<TypographySettings>, options?: any) => string;
  updateTextObject: (id: string, updates: Partial<TypographyObject>) => boolean;
  deleteTextObject: (id: string) => boolean;
  selectTextObject: (id: string) => void;
  duplicateTextObject: (id: string) => string | null;
  
  // Typography settings
  updateTypographySettings: (settings: Partial<TypographySettings>) => void;
  applySettingsToText: (textId: string, settings: Partial<TypographySettings>) => boolean;
  applyStyleToRange: (textId: string, range: TextRange) => boolean;
  
  // Font management
  loadFont: (fontId: string) => Promise<FontFace>;
  searchFonts: (query: string) => FontFamily[];
  filterFonts: (filter: FontFilter) => FontFamily[];
  addToFavorites: (fontId: string) => void;
  removeFromFavorites: (fontId: string) => void;
  uploadCustomFont: (file: File, metadata?: any) => Promise<string>;
  
  // Text measurement and layout
  measureText: (textId: string) => TypographyMeasurement | null;
  wrapText: (textId: string, maxWidth: number) => string[];
  createTextPath: (textId: string, pathData: string) => boolean;
  createTextBox: (textId: string, box: TextBox) => boolean;
  
  // Effects management
  addEffect: (textId: string, effectId: string, parameters?: any) => string;
  updateEffect: (instanceId: string, updates: any) => boolean;
  removeEffect: (instanceId: string) => boolean;
  applyEffectPreset: (instanceId: string, presetId: string) => boolean;
  
  // Performance and utilities
  optimizePerformance: () => void;
  clearCaches: () => void;
  resetPerformanceMetrics: () => void;
  exportTypographyData: () => any;
  importTypographyData: (data: any) => void;
}

export interface TypographyHookOptions {
  enablePerformanceMonitoring?: boolean;
  enableRealTimePreview?: boolean;
  enableAutoSave?: boolean;
  autoSaveInterval?: number;
  fontLoadingStrategy?: 'eager' | 'lazy' | 'preload';
  enableGPUAcceleration?: boolean;
  enableFontSubsetting?: boolean;
  maxTextObjects?: number;
  cacheSize?: number;
  enableKeyboardShortcuts?: boolean;
  enableGoogleFonts?: boolean;
  enableAdobeFonts?: boolean;
  enableCustomFonts?: boolean;
}

export interface TypographyHookReturn {
  state: TypographyState;
  actions: TypographyActions;
  fontLibrary: {
    fonts: FontFamily[];
    collections: FontCollection[];
    favorites: FontFamily[];
    recentlyUsed: FontFamily[];
    searchResults: FontFamily[];
    categories: Array<{ category: string; count: number }>;
    stats: FontLibraryStats;
  };
  effects: {
    library: TextEffectLibrary;
    availableEffects: TextEffect[];
    presets: TextEffectPreset[];
    activeInstances: TextEffectInstance[];
    performance: TextEffectPerformance;
  };
  preview: {
    generatePreview: (fontId: string, text?: string, options?: any) => FontPreview | null;
    generateVariantPreviews: (fontId: string, text?: string) => FontPreview[];
  };
  performance: {
    typography: TypographyPerformanceMetrics;
    fontLoading: { loaded: number; errors: number; averageTime: number };
    effects: TextEffectPerformance;
    memoryUsage: number;
    cacheHitRate: number;
  };
  keyboard: {
    shortcuts: Record<string, () => void>;
    enableShortcuts: (enabled: boolean) => void;
  };
}

// Performance monitoring hook
export function useTypographyPerformance(
  engine?: TypographyEngine,
  enabled: boolean = false
): TypographyPerformanceMetrics {
  const [metrics, setMetrics] = useState<TypographyPerformanceMetrics>({
    textRenderTime: 0,
    measurementTime: 0,
    fontLoadTime: 0,
    objectCount: 0,
    averageComplexity: 0,
    memoryUsage: 0,
    cacheHitRate: 0
  });

  const updateMetrics = useCallback(() => {
    if (!engine || !enabled) return;
    
    const newMetrics = engine.getPerformanceMetrics();
    setMetrics(newMetrics);
  }, [engine, enabled]);

  useEffect(() => {
    if (!enabled || !engine) return;

    const interval = setInterval(updateMetrics, 2000);
    return () => clearInterval(interval);
  }, [updateMetrics, enabled, engine]);

  return metrics;
}

// Font library integration hook
export function useFontLibrary(
  fontLibraryManager?: FontLibraryManager,
  searchQuery: string = '',
  filter: FontFilter = {}
) {
  const [fonts, setFonts] = useState<FontFamily[]>([]);
  const [collections, setCollections] = useState<FontCollection[]>([]);
  const [favorites, setFavorites] = useState<FontFamily[]>([]);
  const [recentlyUsed, setRecentlyUsed] = useState<FontFamily[]>([]);
  const [searchResults, setSearchResults] = useState<FontFamily[]>([]);
  const [stats, setStats] = useState<FontLibraryStats>({
    totalFonts: 0,
    loadedFonts: 0,
    categoryCounts: {} as any,
    sourceCounts: {} as any,
    popularFonts: [],
    recentlyUsed: [],
    cacheSize: 0,
    memoryUsage: 0,
    loadingErrors: 0,
    averageLoadTime: 0
  });

  useEffect(() => {
    if (!fontLibraryManager) return;

    const updateFontLibrary = () => {
      setFonts(fontLibraryManager.getAllFonts());
      setCollections(fontLibraryManager.getAllCollections());
      setFavorites(fontLibraryManager.getFavorites());
      setRecentlyUsed(fontLibraryManager.getRecentlyUsed());
      setStats(fontLibraryManager.getStats());
    };

    // Initial load
    updateFontLibrary();

    // Listen for changes
    fontLibraryManager.on('library:initialized', updateFontLibrary);
    fontLibraryManager.on('font:loaded', updateFontLibrary);
    fontLibraryManager.on('favorites:updated', updateFontLibrary);
    fontLibraryManager.on('custom:font:added', updateFontLibrary);

    return () => {
      fontLibraryManager.off('library:initialized', updateFontLibrary);
      fontLibraryManager.off('font:loaded', updateFontLibrary);
      fontLibraryManager.off('favorites:updated', updateFontLibrary);
      fontLibraryManager.off('custom:font:added', updateFontLibrary);
    };
  }, [fontLibraryManager]);

  // Search functionality
  useEffect(() => {
    if (!fontLibraryManager) return;

    if (searchQuery.trim()) {
      const results = fontLibraryManager.searchFonts(searchQuery);
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  }, [fontLibraryManager, searchQuery]);

  // Filter functionality
  const filteredFonts = useMemo(() => {
    if (!fontLibraryManager) return [];

    if (Object.keys(filter).length > 0) {
      return fontLibraryManager.filterFonts(filter);
    }

    return fonts;
  }, [fontLibraryManager, fonts, filter]);

  // Category counts
  const categories = useMemo(() => {
    return Object.entries(stats.categoryCounts).map(([category, count]) => ({
      category,
      count
    }));
  }, [stats.categoryCounts]);

  return {
    fonts: filteredFonts,
    collections,
    favorites,
    recentlyUsed,
    searchResults,
    categories,
    stats
  };
}

// Text effects integration hook
export function useTextEffects(
  effectsManager?: TextEffectManager,
  textObjectId?: string
) {
  const [availableEffects, setAvailableEffects] = useState<TextEffect[]>([]);
  const [presets, setPresets] = useState<TextEffectPreset[]>([]);
  const [activeInstances, setActiveInstances] = useState<TextEffectInstance[]>([]);
  const [performance, setPerformance] = useState<TextEffectPerformance>({
    totalRenderTime: 0,
    averageRenderTime: 0,
    cacheHitRate: 0,
    gpuAccelerated: 0,
    memoryUsage: 0,
    textureMemory: 0,
    effectsCount: 0,
    animationsCount: 0
  });

  useEffect(() => {
    if (!effectsManager) return;

    const updateEffects = () => {
      const library = effectsManager.getEffectLibrary();
      setAvailableEffects(library.getAllEffects());
      setPresets(library.getAllPresets());
      setPerformance(effectsManager.getPerformanceMetrics());
      
      if (textObjectId) {
        setActiveInstances(effectsManager.getEffectInstancesForText(textObjectId));
      }
    };

    updateEffects();

    // Listen for changes
    effectsManager.on('effect:added', updateEffects);
    effectsManager.on('effect:updated', updateEffects);
    effectsManager.on('effect:removed', updateEffects);
    effectsManager.on('effects:rendered', updateEffects);

    return () => {
      effectsManager.off('effect:added', updateEffects);
      effectsManager.off('effect:updated', updateEffects);
      effectsManager.off('effect:removed', updateEffects);
      effectsManager.off('effects:rendered', updateEffects);
    };
  }, [effectsManager, textObjectId]);

  return {
    library: effectsManager?.getEffectLibrary(),
    availableEffects,
    presets,
    activeInstances,
    performance
  };
}

// Auto-save functionality hook
export function useTypographyAutoSave(
  engine?: TypographyEngine,
  enabled: boolean = false,
  interval: number = 30000
) {
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  const saveProject = useCallback(async () => {
    if (!engine) return;

    try {
      const data = engine.exportTypographyData();
      localStorage.setItem('typography-autosave', JSON.stringify(data));
      localStorage.setItem('typography-autosave-timestamp', Date.now().toString());
      
      console.log('Auto-saved typography state');
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [engine]);

  const scheduleAutoSave = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
    
    if (enabled) {
      autoSaveTimeoutRef.current = setTimeout(saveProject, interval);
    }
  }, [enabled, interval, saveProject]);

  useEffect(() => {
    if (!engine || !enabled) return;

    // Schedule auto-save on changes
    const handleChange = () => scheduleAutoSave();
    
    engine.on('typography:object:created', handleChange);
    engine.on('typography:object:updated', handleChange);
    engine.on('typography:object:deleted', handleChange);

    return () => {
      engine.off('typography:object:created', handleChange);
      engine.off('typography:object:updated', handleChange);
      engine.off('typography:object:deleted', handleChange);
      
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [engine, enabled, scheduleAutoSave]);

  // Load auto-saved state on mount
  useEffect(() => {
    if (!engine || !enabled) return;

    try {
      const savedData = localStorage.getItem('typography-autosave');
      const timestamp = localStorage.getItem('typography-autosave-timestamp');
      
      if (savedData && timestamp) {
        const saveTime = parseInt(timestamp);
        const timeDiff = Date.now() - saveTime;
        
        // Only restore if saved within last hour
        if (timeDiff < 60 * 60 * 1000) {
          const data = JSON.parse(savedData);
          engine.importTypographyData(data);
          console.log('Restored auto-saved typography state');
        }
      }
    } catch (error) {
      console.error('Failed to restore auto-saved state:', error);
    }
  }, [engine, enabled]);
}

// Keyboard shortcuts hook
export function useTypographyKeyboard(
  actions: TypographyActions,
  enabled: boolean = true
) {
  const shortcuts = useMemo(() => ({
    'ctrl+b': () => actions.updateTypographySettings({ fontWeight: 'bold' }),
    'ctrl+i': () => actions.updateTypographySettings({ fontStyle: 'italic' }),
    'ctrl+u': () => actions.updateTypographySettings({ textDecoration: 'underline' }),
    'ctrl+shift+>': () => actions.updateTypographySettings({ fontSize: (prev: number) => prev + 2 }),
    'ctrl+shift+<': () => actions.updateTypographySettings({ fontSize: (prev: number) => Math.max(8, prev - 2) }),
    'ctrl+shift+l': () => actions.updateTypographySettings({ textAlign: 'left' }),
    'ctrl+shift+e': () => actions.updateTypographySettings({ textAlign: 'center' }),
    'ctrl+shift+r': () => actions.updateTypographySettings({ textAlign: 'right' }),
    'ctrl+shift+j': () => actions.updateTypographySettings({ textAlign: 'justify' }),
    'ctrl+shift+c': () => actions.clearCaches(),
    'ctrl+shift+o': () => actions.optimizePerformance(),
    'escape': () => actions.selectTextObject(''),
    'delete': () => {
      // Delete selected text object
      const selectedId = ''; // This would come from state
      if (selectedId) {
        actions.deleteTextObject(selectedId);
      }
    },
    'ctrl+d': () => {
      // Duplicate selected text object
      const selectedId = ''; // This would come from state
      if (selectedId) {
        actions.duplicateTextObject(selectedId);
      }
    }
  }), [actions]);

  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const key = [
        event.ctrlKey && 'ctrl',
        event.shiftKey && 'shift',
        event.altKey && 'alt',
        event.metaKey && 'meta',
        event.key.toLowerCase()
      ].filter(Boolean).join('+');

      const handler = shortcuts[key];
      if (handler) {
        event.preventDefault();
        handler();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts, enabled]);

  return { shortcuts, enableShortcuts: () => {} };
}

// Main typography hook
export function useTypography(
  canvas: fabric.Canvas | null,
  options: TypographyHookOptions = {}
): TypographyHookReturn {
  const {
    enablePerformanceMonitoring = false,
    enableRealTimePreview = true,
    enableAutoSave = false,
    autoSaveInterval = 30000,
    fontLoadingStrategy = 'lazy',
    enableGPUAcceleration = true,
    enableFontSubsetting = false,
    maxTextObjects = 100,
    cacheSize = 200,
    enableKeyboardShortcuts = true,
    enableGoogleFonts = true,
    enableAdobeFonts = false,
    enableCustomFonts = true
  } = options;

  // Core instances
  const engineRef = useRef<TypographyEngine | null>(null);
  const fontLibraryRef = useRef<FontLibraryManager | null>(null);
  const effectsManagerRef = useRef<TextEffectManager | null>(null);

  // State management
  const [state, setState] = useState<TypographyState>({
    isInitialized: false,
    activeTextObject: null,
    selectedFont: null,
    typographySettings: {
      fontFamily: 'Arial',
      fontSize: 16,
      fontWeight: 'normal',
      fontStyle: 'normal',
      fontVariant: 'normal',
      fontStretch: 'normal',
      textAlign: 'left',
      textDecoration: 'none',
      textTransform: 'none',
      textShadow: 'none',
      letterSpacing: 0,
      wordSpacing: 0,
      lineHeight: 1.2,
      paragraphSpacing: 0,
      indent: 0,
      fill: '#000000',
      stroke: 'transparent',
      strokeWidth: 0,
      kerning: true,
      ligatures: true,
      hyphenation: false,
      direction: 'ltr',
      writingMode: 'horizontal-tb'
    },
    textObjects: [],
    fontLibraryStats: {
      totalFonts: 0,
      loadedFonts: 0,
      categoryCounts: {} as any,
      sourceCounts: {} as any,
      popularFonts: [],
      recentlyUsed: [],
      cacheSize: 0,
      memoryUsage: 0,
      loadingErrors: 0,
      averageLoadTime: 0
    },
    performanceMetrics: {
      textRenderTime: 0,
      measurementTime: 0,
      fontLoadTime: 0,
      objectCount: 0,
      averageComplexity: 0,
      memoryUsage: 0,
      cacheHitRate: 0
    },
    effectsPerformance: {
      totalRenderTime: 0,
      averageRenderTime: 0,
      cacheHitRate: 0,
      gpuAccelerated: 0,
      memoryUsage: 0,
      textureMemory: 0,
      effectsCount: 0,
      animationsCount: 0
    },
    error: null,
    isLoading: false
  });

  // Initialize typography system
  useEffect(() => {
    if (!canvas) return;

    const initializeTypography = async () => {
      setState(prev => ({ ...prev, isLoading: true }));
      
      try {
        // Initialize typography engine
        const typographyConfig: Partial<TypographyEngineConfig> = {
          enableRealTimePreview,
          maxTextObjects,
          enablePerformanceOptimization: enablePerformanceMonitoring
        };
        const engine = new TypographyEngine(canvas, typographyConfig);
        
        // Initialize font library
        const fontLibraryConfig: Partial<FontLibraryConfig> = {
          enableGoogleFonts,
          enableAdobeFonts,
          enableCustomFonts,
          defaultLoadingStrategy: fontLoadingStrategy,
          cacheSize,
          enableSubsetting: enableFontSubsetting
        };
        const fontLibrary = new FontLibraryManager(fontLibraryConfig);
        
        // Initialize effects manager
        const effectsConfig: Partial<TextEffectRenderOptions> = {
          enableGPU: enableGPUAcceleration,
          enableRealTimePreview,
          enableCaching: true
        };
        const effectsManager = new TextEffectManager(canvas, effectsConfig);
        
        // Store references
        engineRef.current = engine;
        fontLibraryRef.current = fontLibrary;
        effectsManagerRef.current = effectsManager;
        
        // Set up event handlers
        const handleTextObjectCreated = (data: any) => {
          setState(prev => ({
            ...prev,
            textObjects: [...prev.textObjects, data.textObject],
            activeTextObject: data.id
          }));
        };
        
        const handleTextObjectUpdated = (data: any) => {
          setState(prev => ({
            ...prev,
            textObjects: prev.textObjects.map(obj => 
              obj.id === data.id ? data.textObject : obj
            )
          }));
        };
        
        const handleTextObjectDeleted = (data: any) => {
          setState(prev => ({
            ...prev,
            textObjects: prev.textObjects.filter(obj => obj.id !== data.id),
            activeTextObject: prev.activeTextObject === data.id ? null : prev.activeTextObject
          }));
        };
        
        const handleError = (error: any) => {
          setState(prev => ({ ...prev, error: error.message || 'Unknown error' }));
        };
        
        // Register event listeners
        engine.on('typography:object:created', handleTextObjectCreated);
        engine.on('typography:object:updated', handleTextObjectUpdated);
        engine.on('typography:object:deleted', handleTextObjectDeleted);
        engine.on('error', handleError);
        
        fontLibrary.on('font:loaded', () => {
          setState(prev => ({ ...prev, fontLibraryStats: fontLibrary.getStats() }));
        });
        
        effectsManager.on('effect:added', () => {
          setState(prev => ({ ...prev, effectsPerformance: effectsManager.getPerformanceMetrics() }));
        });
        
        setState(prev => ({ 
          ...prev, 
          isInitialized: true,
          isLoading: false,
          textObjects: engine.getAllTextObjects(),
          fontLibraryStats: fontLibrary.getStats()
        }));
        
        // Cleanup function
        return () => {
          engine.off('typography:object:created', handleTextObjectCreated);
          engine.off('typography:object:updated', handleTextObjectUpdated);
          engine.off('typography:object:deleted', handleTextObjectDeleted);
          engine.off('error', handleError);
          
          engine.dispose();
          fontLibrary.dispose();
          effectsManager.dispose();
          
          engineRef.current = null;
          fontLibraryRef.current = null;
          effectsManagerRef.current = null;
        };
      } catch (error) {
        setState(prev => ({ 
          ...prev, 
          error: error instanceof Error ? error.message : 'Failed to initialize typography',
          isLoading: false
        }));
      }
    };

    initializeTypography();
  }, [canvas, enableRealTimePreview, maxTextObjects, enablePerformanceMonitoring, enableGoogleFonts, enableAdobeFonts, enableCustomFonts, fontLoadingStrategy, cacheSize, enableFontSubsetting, enableGPUAcceleration]);

  // Hook integrations
  const typographyPerformance = useTypographyPerformance(
    engineRef.current || undefined,
    enablePerformanceMonitoring
  );

  const fontLibrary = useFontLibrary(fontLibraryRef.current || undefined);
  const effects = useTextEffects(effectsManagerRef.current || undefined, state.activeTextObject || undefined);

  useTypographyAutoSave(
    engineRef.current || undefined,
    enableAutoSave,
    autoSaveInterval
  );

  // Actions
  const actions = useMemo<TypographyActions>(() => ({
    // Text object management
    createTextObject: (content: string, settings?: Partial<TypographySettings>, options?: any) => {
      if (!engineRef.current) return '';
      return engineRef.current.createTextObject(content, settings, options);
    },

    updateTextObject: (id: string, updates: Partial<TypographyObject>) => {
      if (!engineRef.current) return false;
      return engineRef.current.updateTextObject(id, updates);
    },

    deleteTextObject: (id: string) => {
      if (!engineRef.current) return false;
      return engineRef.current.deleteTextObject(id);
    },

    selectTextObject: (id: string) => {
      setState(prev => ({ ...prev, activeTextObject: id }));
    },

    duplicateTextObject: (id: string) => {
      if (!engineRef.current) return null;
      const original = engineRef.current.getTextObject(id);
      if (!original) return null;
      
      return engineRef.current.createTextObject(
        original.content,
        original.settings,
        { x: (original.fabricObject?.left || 0) + 20, y: (original.fabricObject?.top || 0) + 20 }
      );
    },

    // Typography settings
    updateTypographySettings: (settings: Partial<TypographySettings>) => {
      setState(prev => ({ 
        ...prev, 
        typographySettings: { ...prev.typographySettings, ...settings }
      }));
    },

    applySettingsToText: (textId: string, settings: Partial<TypographySettings>) => {
      if (!engineRef.current) return false;
      return engineRef.current.updateTextObject(textId, { settings });
    },

    applyStyleToRange: (textId: string, range: TextRange) => {
      if (!engineRef.current) return false;
      return engineRef.current.applyStyleToRange(textId, range);
    },

    // Font management
    loadFont: async (fontId: string) => {
      if (!fontLibraryRef.current) throw new Error('Font library not initialized');
      return fontLibraryRef.current.loadFont(fontId);
    },

    searchFonts: (query: string) => {
      if (!fontLibraryRef.current) return [];
      return fontLibraryRef.current.searchFonts(query);
    },

    filterFonts: (filter: FontFilter) => {
      if (!fontLibraryRef.current) return [];
      return fontLibraryRef.current.filterFonts(filter);
    },

    addToFavorites: (fontId: string) => {
      if (!fontLibraryRef.current) return;
      fontLibraryRef.current.addToFavorites(fontId);
    },

    removeFromFavorites: (fontId: string) => {
      if (!fontLibraryRef.current) return;
      fontLibraryRef.current.removeFromFavorites(fontId);
    },

    uploadCustomFont: async (file: File, metadata?: any) => {
      if (!fontLibraryRef.current) throw new Error('Font library not initialized');
      return fontLibraryRef.current.addCustomFont(file, metadata);
    },

    // Text measurement and layout
    measureText: (textId: string) => {
      if (!engineRef.current) return null;
      return engineRef.current.measureText(textId);
    },

    wrapText: (textId: string, maxWidth: number) => {
      if (!engineRef.current) return [];
      return engineRef.current.wrapText(textId, maxWidth);
    },

    createTextPath: (textId: string, pathData: string) => {
      if (!engineRef.current) return false;
      const path: TextPath = {
        id: `path_${Date.now()}`,
        path: pathData,
        offset: 0,
        side: 'left',
        textAlign: 'left'
      };
      return engineRef.current.updateTextObject(textId, { path });
    },

    createTextBox: (textId: string, box: TextBox) => {
      if (!engineRef.current) return false;
      return engineRef.current.updateTextObject(textId, { box });
    },

    // Effects management
    addEffect: (textId: string, effectId: string, parameters?: any) => {
      if (!effectsManagerRef.current) return '';
      return effectsManagerRef.current.addEffectInstance(textId, effectId, parameters);
    },

    updateEffect: (instanceId: string, updates: any) => {
      if (!effectsManagerRef.current) return false;
      return effectsManagerRef.current.updateEffectInstance(instanceId, updates);
    },

    removeEffect: (instanceId: string) => {
      if (!effectsManagerRef.current) return false;
      return effectsManagerRef.current.removeEffectInstance(instanceId);
    },

    applyEffectPreset: (instanceId: string, presetId: string) => {
      if (!effectsManagerRef.current) return false;
      return effectsManagerRef.current.applyPreset(instanceId, presetId);
    },

    // Performance and utilities
    optimizePerformance: () => {
      if (fontLibraryRef.current) fontLibraryRef.current.clearCaches();
      if (effectsManagerRef.current) effectsManagerRef.current.clearCache();
    },

    clearCaches: () => {
      if (fontLibraryRef.current) fontLibraryRef.current.clearCaches();
      if (effectsManagerRef.current) effectsManagerRef.current.clearCache();
    },

    resetPerformanceMetrics: () => {
      if (engineRef.current) engineRef.current.resetPerformanceMetrics();
    },

    exportTypographyData: () => {
      if (!engineRef.current) return null;
      return engineRef.current.exportTypographyData();
    },

    importTypographyData: (data: any) => {
      if (!engineRef.current) return;
      engineRef.current.importTypographyData(data);
    }
  }), []);

  // Keyboard shortcuts
  const keyboard = useTypographyKeyboard(actions, enableKeyboardShortcuts);

  // Preview functionality
  const preview = useMemo(() => ({
    generatePreview: (fontId: string, text?: string, options?: any) => {
      if (!fontLibraryRef.current) return null;
      return fontLibraryRef.current.generatePreview(fontId, text, options);
    },

    generateVariantPreviews: (fontId: string, text?: string) => {
      if (!fontLibraryRef.current) return [];
      const font = fontLibraryRef.current.getFont(fontId);
      if (!font) return [];
      
      return font.variants.map(variant => 
        fontLibraryRef.current!.generatePreview(fontId, text, {
          weight: variant.weight,
          style: variant.style
        })
      ).filter(Boolean) as FontPreview[];
    }
  }), []);

  // Performance metrics
  const performance = useMemo(() => ({
    typography: typographyPerformance,
    fontLoading: fontLibraryRef.current?.getStats() || { loaded: 0, errors: 0, averageTime: 0 },
    effects: effects.performance,
    memoryUsage: typographyPerformance.memoryUsage + effects.performance.memoryUsage,
    cacheHitRate: typographyPerformance.cacheHitRate
  }), [typographyPerformance, effects.performance]);

  return {
    state,
    actions,
    fontLibrary,
    effects,
    preview,
    performance,
    keyboard
  };
}

// Additional utility hooks
export function useTypographyHistory(engine: TypographyEngine | null) {
  const [historyState, setHistoryState] = useState({
    canUndo: false,
    canRedo: false,
    undoStack: 0,
    redoStack: 0
  });

  useEffect(() => {
    if (!engine) return;

    const handleHistoryUpdate = (data: any) => {
      setHistoryState({
        canUndo: data.canUndo || false,
        canRedo: data.canRedo || false,
        undoStack: data.undoStack || 0,
        redoStack: data.redoStack || 0
      });
    };

    engine.on('history:updated', handleHistoryUpdate);
    
    return () => {
      engine.off('history:updated', handleHistoryUpdate);
    };
  }, [engine]);

  return historyState;
}

export function useTypographyCollaborative(engine: TypographyEngine | null, roomId: string) {
  const [collaborators, setCollaborators] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!engine || !roomId) return;

    // Collaborative features would be implemented here
    // This is a placeholder for future collaborative functionality
    
    console.log('Collaborative typography not yet implemented');
  }, [engine, roomId]);

  return {
    collaborators,
    isConnected,
    sendCursor: () => {},
    sendSelection: () => {},
    sendChange: () => {}
  };
}

// Export all hooks
export {
  useTypographyPerformance,
  useFontLibrary,
  useTextEffects,
  useTypographyAutoSave,
  useTypographyKeyboard,
  useTypographyHistory,
  useTypographyCollaborative
};