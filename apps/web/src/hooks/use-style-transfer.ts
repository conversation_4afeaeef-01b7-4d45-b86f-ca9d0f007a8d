import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { 
  AdvancedStyleTransferService,
  StyleTransferRequest,
  StyleTransferResult,
  StyleReference,
  StyleParameters,
  TransferOptions,
  StylePreviewResult,
  StyleModel
} from '../lib/ai/style-transfer-engine';
import { 
  StyleLibraryManager, 
  StyleLibraryEntry, 
  StyleSearchQuery, 
  StyleSearchResult,
  ExtractedStyle
} from '../lib/ai/style-library-manager';
import { 
  StyleAnalysisService,
  ImageStyleAnalysis,
  StyleMatchingResult
} from '../lib/ai/style-analysis-service';
import { useMultiProviderAI } from './use-multi-provider-ai';

// Hook state types
export interface StyleTransferState {
  isInitialized: boolean;
  isProcessing: boolean;
  isGeneratingPreview: boolean;
  isAnalyzing: boolean;
  availableModels: StyleModel[];
  currentTransfer?: {
    id: string;
    progress: number;
    status: string;
    model: string;
    estimatedTimeRemaining?: number;
  };
  transferHistory: StyleTransferResult[];
  previewCache: Map<string, StylePreviewResult>;
  styleLibrary: {
    totalStyles: number;
    recentStyles: StyleLibraryEntry[];
    popularStyles: StyleLibraryEntry[];
    collections: any[];
  };
  lastError?: string;
  lastAnalysis?: ImageStyleAnalysis;
  styleRecommendations: StyleMatchingResult | null;
}

export interface StyleTransferActions {
  // Core style transfer methods
  transferStyle: (request: Partial<StyleTransferRequest>) => Promise<StyleTransferResult>;
  
  transferStyleWithPreview: (
    contentImage: ImageData | HTMLImageElement | string,
    styleReference: StyleReference,
    parameters: StyleParameters,
    options?: Partial<TransferOptions>
  ) => Promise<{ preview: StylePreviewResult; final?: StyleTransferResult }>;
  
  // Real-time preview methods
  generatePreview: (
    contentImage: ImageData | HTMLImageElement | string,
    styleReference: StyleReference,
    parameters: StyleParameters
  ) => Promise<StylePreviewResult>;
  
  updatePreviewParameters: (parameters: Partial<StyleParameters>) => Promise<StylePreviewResult | null>;
  
  // Style library methods
  searchStyles: (query: StyleSearchQuery) => Promise<StyleSearchResult[]>;
  
  addCustomStyle: (
    image: ImageData | HTMLImageElement | string,
    metadata: {
      name: string;
      description?: string;
      tags: string[];
      categories?: string[];
    }
  ) => Promise<string>;
  
  extractStyleFromImage: (image: ImageData | HTMLImageElement | string) => Promise<ExtractedStyle>;
  
  getStyleRecommendations: (image: ImageData | HTMLImageElement | string) => Promise<StyleMatchingResult>;
  
  // Style management
  saveStyleToLibrary: (style: StyleReference, metadata?: any) => Promise<string>;
  
  removeStyleFromLibrary: (styleId: string) => Promise<boolean>;
  
  rateStyle: (styleId: string, rating: number, comment?: string) => Promise<void>;
  
  // Model and parameter management
  selectModel: (modelId: string) => void;
  
  getModelRecommendation: (
    contentImage: ImageData | HTMLImageElement | string,
    styleReference: StyleReference
  ) => Promise<string>;
  
  // Batch operations
  batchTransfer: (
    requests: Array<{
      contentImage: ImageData | HTMLImageElement | string;
      styleReference: StyleReference;
      parameters: StyleParameters;
    }>
  ) => Promise<StyleTransferResult[]>;
  
  // Analysis methods
  analyzeImage: (image: ImageData | HTMLImageElement | string) => Promise<ImageStyleAnalysis>;
  
  findSimilarStyles: (
    referenceStyle: StyleReference | string,
    limit?: number
  ) => Promise<StyleLibraryEntry[]>;
  
  // Cache and performance
  clearCache: () => void;
  
  preloadStyles: (styleIds: string[]) => Promise<void>;
  
  // Settings
  updateDefaultParameters: (parameters: Partial<StyleParameters>) => void;
  
  updateDefaultOptions: (options: Partial<TransferOptions>) => void;
}

export interface StyleTransferHookOptions {
  multiProviderAI: ReturnType<typeof useMultiProviderAI>;
  enablePreviewCache?: boolean;
  enableAutoAnalysis?: boolean;
  maxCacheSize?: number;
  defaultParameters?: Partial<StyleParameters>;
  defaultOptions?: Partial<TransferOptions>;
  onTransferComplete?: (result: StyleTransferResult) => void;
  onTransferError?: (error: string) => void;
  onPreviewGenerated?: (preview: StylePreviewResult) => void;
  onStyleAdded?: (styleId: string) => void;
}

export interface StyleTransferHookReturn {
  state: StyleTransferState;
  actions: StyleTransferActions;
  services: {
    styleTransfer: AdvancedStyleTransferService | null;
    styleLibrary: StyleLibraryManager | null;
    styleAnalysis: StyleAnalysisService | null;
  };
  isReady: boolean;
}

// Custom hook for style transfer operations
export function useStyleTransfer(
  options: StyleTransferHookOptions
): StyleTransferHookReturn {
  const {
    multiProviderAI,
    enablePreviewCache = true,
    enableAutoAnalysis = true,
    maxCacheSize = 100,
    defaultParameters = {
      strength: 0.7,
      preserveContent: 0.8,
      colorTransfer: true,
      textureTransfer: true,
      edgePreservation: 0.7,
      detailLevel: 'medium',
      blendMode: 'normal'
    },
    defaultOptions = {
      priority: 'balanced',
      enablePreview: true,
      enableCache: true,
      outputFormat: 'png',
      outputQuality: 0.9,
      maxRetries: 3,
      timeoutMs: 120000
    },
    onTransferComplete,
    onTransferError,
    onPreviewGenerated,
    onStyleAdded
  } = options;

  // State management
  const [state, setState] = useState<StyleTransferState>({
    isInitialized: false,
    isProcessing: false,
    isGeneratingPreview: false,
    isAnalyzing: false,
    availableModels: [],
    transferHistory: [],
    previewCache: new Map(),
    styleLibrary: {
      totalStyles: 0,
      recentStyles: [],
      popularStyles: [],
      collections: []
    },
    styleRecommendations: null
  });

  // Service refs
  const styleTransferServiceRef = useRef<AdvancedStyleTransferService | null>(null);
  const styleLibraryManagerRef = useRef<StyleLibraryManager | null>(null);
  const styleAnalysisServiceRef = useRef<StyleAnalysisService | null>(null);
  const activeTransfersRef = useRef<Map<string, Promise<any>>>(new Map());
  const currentParametersRef = useRef<StyleParameters>(defaultParameters as StyleParameters);
  const currentPreviewImageRef = useRef<ImageData | HTMLImageElement | string | null>(null);
  const currentPreviewStyleRef = useRef<StyleReference | null>(null);

  // Initialize services
  useEffect(() => {
    const initializeServices = async () => {
      if (!multiProviderAI.isReady) return;

      try {
        setState(prev => ({ ...prev, lastError: undefined }));

        // Initialize style transfer service
        const styleTransferService = new AdvancedStyleTransferService(multiProviderAI.service!);
        styleTransferServiceRef.current = styleTransferService;

        // Initialize style library manager
        const styleLibraryManager = new StyleLibraryManager();
        await styleLibraryManager.initialize();
        styleLibraryManagerRef.current = styleLibraryManager;

        // Initialize style analysis service
        const styleAnalysisService = new StyleAnalysisService();
        await styleAnalysisService.initialize();
        styleAnalysisServiceRef.current = styleAnalysisService;

        // Set up event listeners
        setupServiceEvents();

        // Load initial data
        const models = styleTransferService.getAvailableModels();
        const analytics = styleLibraryManager.getAnalytics();

        setState(prev => ({
          ...prev,
          isInitialized: true,
          availableModels: models,
          styleLibrary: {
            totalStyles: analytics.totalStyles,
            recentStyles: analytics.recentActivity.slice(0, 10).map(activity => 
              styleLibraryManager.getStyleById(activity.styleId)!
            ).filter(Boolean),
            popularStyles: analytics.popularStyles.slice(0, 10),
            collections: styleLibraryManager.getCollections()
          }
        }));

      } catch (error) {
        console.error('Failed to initialize style transfer services:', error);
        setState(prev => ({
          ...prev,
          lastError: error instanceof Error ? error.message : 'Unknown error',
          isInitialized: false
        }));
      }
    };

    initializeServices();

    return () => {
      // Cleanup
      if (styleTransferServiceRef.current) {
        styleTransferServiceRef.current.dispose();
        styleTransferServiceRef.current = null;
      }
      if (styleLibraryManagerRef.current) {
        styleLibraryManagerRef.current.dispose();
        styleLibraryManagerRef.current = null;
      }
      if (styleAnalysisServiceRef.current) {
        styleAnalysisServiceRef.current.dispose();
        styleAnalysisServiceRef.current = null;
      }
      activeTransfersRef.current.clear();
    };
  }, [multiProviderAI.isReady]);

  // Set up service event listeners
  const setupServiceEvents = useCallback(() => {
    if (!styleTransferServiceRef.current || !styleLibraryManagerRef.current) return;

    const styleTransferService = styleTransferServiceRef.current;
    const styleLibraryManager = styleLibraryManagerRef.current;

    // Style transfer events
    styleTransferService.on('style:transfer:completed', (result) => {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentTransfer: undefined,
        transferHistory: [result, ...prev.transferHistory.slice(0, 49)] // Keep last 50
      }));
      
      if (onTransferComplete) {
        onTransferComplete(result);
      }
    });

    styleTransferService.on('style:transfer:failed', (data) => {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentTransfer: undefined,
        lastError: data.error
      }));
      
      if (onTransferError) {
        onTransferError(data.error);
      }
    });

    styleTransferService.on('model:selected', (data) => {
      setState(prev => ({
        ...prev,
        currentTransfer: prev.currentTransfer ? {
          ...prev.currentTransfer,
          model: data.model
        } : undefined
      }));
    });

    // Style library events
    styleLibraryManager.on('style:added', (style) => {
      updateStyleLibraryState();
      if (onStyleAdded) {
        onStyleAdded(style.id);
      }
    });

    styleLibraryManager.on('style:used', () => {
      updateStyleLibraryState();
    });

  }, [onTransferComplete, onTransferError, onStyleAdded]);

  const updateStyleLibraryState = useCallback(() => {
    if (!styleLibraryManagerRef.current) return;
    
    const analytics = styleLibraryManagerRef.current.getAnalytics();
    setState(prev => ({
      ...prev,
      styleLibrary: {
        totalStyles: analytics.totalStyles,
        recentStyles: analytics.recentActivity.slice(0, 10).map(activity => 
          styleLibraryManagerRef.current!.getStyleById(activity.styleId)!
        ).filter(Boolean),
        popularStyles: analytics.popularStyles.slice(0, 10),
        collections: styleLibraryManagerRef.current!.getCollections()
      }
    }));
  }, []);

  // Action creators
  const actions = useMemo<StyleTransferActions>(() => ({
    // Core style transfer
    transferStyle: async (request: Partial<StyleTransferRequest>): Promise<StyleTransferResult> => {
      if (!styleTransferServiceRef.current) {
        throw new Error('Style transfer service not initialized');
      }

      const fullRequest: StyleTransferRequest = {
        contentImage: new ImageData(1, 1),
        styleReference: { type: 'preset', source: '', tags: [], mood: { energy: 'medium', warmth: 'neutral', mood: 'serious', era: 'contemporary' }, dominantColors: [] },
        parameters: currentParametersRef.current,
        options: defaultOptions as TransferOptions,
        ...request
      };

      const transferId = `transfer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      setState(prev => ({
        ...prev,
        isProcessing: true,
        currentTransfer: {
          id: transferId,
          progress: 0,
          status: 'starting',
          model: 'selecting...'
        }
      }));

      try {
        const promise = styleTransferServiceRef.current.transferStyle(fullRequest);
        activeTransfersRef.current.set(transferId, promise);
        
        const result = await promise;
        activeTransfersRef.current.delete(transferId);
        
        // Increment usage count for style
        if (styleLibraryManagerRef.current && typeof fullRequest.styleReference.source === 'string') {
          await styleLibraryManagerRef.current.incrementUsage(fullRequest.styleReference.source);
        }
        
        return result;
      } catch (error) {
        activeTransfersRef.current.delete(transferId);
        setState(prev => ({
          ...prev,
          isProcessing: false,
          currentTransfer: undefined,
          lastError: error instanceof Error ? error.message : 'Unknown error'
        }));
        throw error;
      }
    },

    transferStyleWithPreview: async (
      contentImage: ImageData | HTMLImageElement | string,
      styleReference: StyleReference,
      parameters: StyleParameters,
      options: Partial<TransferOptions> = {}
    ): Promise<{ preview: StylePreviewResult; final?: StyleTransferResult }> => {
      // Generate preview first
      const preview = await actions.generatePreview(contentImage, styleReference, parameters);
      
      if (!preview.success) {
        return { preview };
      }

      // If preview is satisfactory, generate full resolution
      try {
        const final = await actions.transferStyle({
          contentImage,
          styleReference,
          parameters,
          options: { ...defaultOptions, ...options }
        });
        
        return { preview, final };
      } catch (error) {
        // Return preview even if final transfer fails
        return { 
          preview: {
            ...preview,
            error: `Preview successful, but final transfer failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        };
      }
    },

    // Real-time preview
    generatePreview: async (
      contentImage: ImageData | HTMLImageElement | string,
      styleReference: StyleReference,
      parameters: StyleParameters
    ): Promise<StylePreviewResult> => {
      if (!styleTransferServiceRef.current) {
        throw new Error('Style transfer service not initialized');
      }

      setState(prev => ({ ...prev, isGeneratingPreview: true }));

      try {
        // Check cache first
        if (enablePreviewCache) {
          const cacheKey = generatePreviewCacheKey(contentImage, styleReference, parameters);
          const cached = state.previewCache.get(cacheKey);
          if (cached) {
            setState(prev => ({ ...prev, isGeneratingPreview: false }));
            return cached;
          }
        }

        const preview = await styleTransferServiceRef.current.generatePreview(
          contentImage,
          styleReference,
          parameters
        );

        // Cache result
        if (enablePreviewCache && preview.success) {
          const cacheKey = generatePreviewCacheKey(contentImage, styleReference, parameters);
          setState(prev => {
            const newCache = new Map(prev.previewCache);
            newCache.set(cacheKey, preview);
            
            // Limit cache size
            if (newCache.size > maxCacheSize) {
              const firstKey = newCache.keys().next().value;
              newCache.delete(firstKey);
            }
            
            return { ...prev, previewCache: newCache };
          });
        }

        setState(prev => ({ ...prev, isGeneratingPreview: false }));
        
        if (onPreviewGenerated) {
          onPreviewGenerated(preview);
        }

        // Store current preview context for parameter updates
        currentPreviewImageRef.current = contentImage;
        currentPreviewStyleRef.current = styleReference;
        
        return preview;
      } catch (error) {
        setState(prev => ({ 
          ...prev, 
          isGeneratingPreview: false,
          lastError: error instanceof Error ? error.message : 'Unknown error'
        }));
        throw error;
      }
    },

    updatePreviewParameters: async (parameters: Partial<StyleParameters>): Promise<StylePreviewResult | null> => {
      if (!currentPreviewImageRef.current || !currentPreviewStyleRef.current) {
        return null;
      }

      const newParameters = { ...currentParametersRef.current, ...parameters };
      currentParametersRef.current = newParameters;

      return actions.generatePreview(
        currentPreviewImageRef.current,
        currentPreviewStyleRef.current,
        newParameters
      );
    },

    // Style library operations
    searchStyles: async (query: StyleSearchQuery): Promise<StyleSearchResult[]> => {
      if (!styleLibraryManagerRef.current) {
        throw new Error('Style library manager not initialized');
      }

      return styleLibraryManagerRef.current.searchStyles(query);
    },

    addCustomStyle: async (
      image: ImageData | HTMLImageElement | string,
      metadata: { name: string; description?: string; tags: string[]; categories?: string[] }
    ): Promise<string> => {
      if (!styleLibraryManagerRef.current) {
        throw new Error('Style library manager not initialized');
      }

      const styleReference: StyleReference = {
        type: 'custom',
        source: image,
        name: metadata.name,
        description: metadata.description,
        tags: metadata.tags,
        mood: {
          energy: 'medium',
          warmth: 'neutral',
          mood: 'serious',
          era: 'contemporary'
        },
        dominantColors: []
      };

      return styleLibraryManagerRef.current.addStyle(styleReference, {
        extractFeatures: true,
        generateThumbnails: true,
        isPublic: false,
        categories: metadata.categories
      });
    },

    extractStyleFromImage: async (image: ImageData | HTMLImageElement | string): Promise<ExtractedStyle> => {
      if (!styleLibraryManagerRef.current) {
        throw new Error('Style library manager not initialized');
      }

      return styleLibraryManagerRef.current.extractStyleFromImage(image);
    },

    getStyleRecommendations: async (image: ImageData | HTMLImageElement | string): Promise<StyleMatchingResult> => {
      if (!styleAnalysisServiceRef.current || !styleLibraryManagerRef.current) {
        throw new Error('Services not initialized');
      }

      setState(prev => ({ ...prev, isAnalyzing: true }));

      try {
        const availableStyles = styleLibraryManagerRef.current.getPublicStyles();
        
        // Convert ImageData to required format
        let imageData: ImageData;
        if (image instanceof ImageData) {
          imageData = image;
        } else {
          // Convert other formats to ImageData
          imageData = await convertToImageData(image);
        }

        const matchingResult = await styleAnalysisServiceRef.current.findMatchingStyles(
          imageData,
          availableStyles
        );

        setState(prev => ({ 
          ...prev, 
          isAnalyzing: false,
          styleRecommendations: matchingResult,
          lastAnalysis: matchingResult.analysis
        }));

        return matchingResult;
      } catch (error) {
        setState(prev => ({ 
          ...prev, 
          isAnalyzing: false,
          lastError: error instanceof Error ? error.message : 'Unknown error'
        }));
        throw error;
      }
    },

    // Style management
    saveStyleToLibrary: async (style: StyleReference, metadata?: any): Promise<string> => {
      if (!styleLibraryManagerRef.current) {
        throw new Error('Style library manager not initialized');
      }

      return styleLibraryManagerRef.current.addStyle(style, metadata);
    },

    removeStyleFromLibrary: async (styleId: string): Promise<boolean> => {
      if (!styleLibraryManagerRef.current) {
        throw new Error('Style library manager not initialized');
      }

      return styleLibraryManagerRef.current.deleteStyle(styleId);
    },

    rateStyle: async (styleId: string, rating: number, comment?: string): Promise<void> => {
      if (!styleLibraryManagerRef.current) {
        throw new Error('Style library manager not initialized');
      }

      await styleLibraryManagerRef.current.rateStyle(styleId, rating, comment);
    },

    // Model management
    selectModel: (modelId: string) => {
      // This would be used in the next transfer request
      setState(prev => ({
        ...prev,
        currentTransfer: prev.currentTransfer ? {
          ...prev.currentTransfer,
          model: modelId
        } : undefined
      }));
    },

    getModelRecommendation: async (
      contentImage: ImageData | HTMLImageElement | string,
      styleReference: StyleReference
    ): Promise<string> => {
      if (!styleTransferServiceRef.current) {
        throw new Error('Style transfer service not initialized');
      }

      // This would use the private selectOptimalModel method
      // For now, return the first available model
      const models = styleTransferServiceRef.current.getAvailableModels();
      return models.length > 0 ? models[0].id : 'neural-style-transfer';
    },

    // Batch operations
    batchTransfer: async (
      requests: Array<{
        contentImage: ImageData | HTMLImageElement | string;
        styleReference: StyleReference;
        parameters: StyleParameters;
      }>
    ): Promise<StyleTransferResult[]> => {
      const results: StyleTransferResult[] = [];
      
      // Process requests sequentially to avoid overwhelming the service
      for (const request of requests) {
        try {
          const result = await actions.transferStyle(request);
          results.push(result);
        } catch (error) {
          console.warn('Batch transfer item failed:', error);
          // Continue with other requests
        }
      }
      
      return results;
    },

    // Analysis
    analyzeImage: async (image: ImageData | HTMLImageElement | string): Promise<ImageStyleAnalysis> => {
      if (!styleAnalysisServiceRef.current) {
        throw new Error('Style analysis service not initialized');
      }

      setState(prev => ({ ...prev, isAnalyzing: true }));

      try {
        // Convert to ImageData if needed
        const imageData = image instanceof ImageData ? image : await convertToImageData(image);
        
        const analysis = await styleAnalysisServiceRef.current.analyzeImageStyle(imageData);
        
        setState(prev => ({ 
          ...prev, 
          isAnalyzing: false,
          lastAnalysis: analysis
        }));

        return analysis;
      } catch (error) {
        setState(prev => ({ 
          ...prev, 
          isAnalyzing: false,
          lastError: error instanceof Error ? error.message : 'Unknown error'
        }));
        throw error;
      }
    },

    findSimilarStyles: async (
      referenceStyle: StyleReference | string,
      limit: number = 10
    ): Promise<StyleLibraryEntry[]> => {
      if (!styleLibraryManagerRef.current) {
        throw new Error('Style library manager not initialized');
      }

      const similarities = await styleLibraryManagerRef.current.findSimilarStyles(
        referenceStyle,
        limit
      );

      return similarities.map(sim => sim.style);
    },

    // Cache and performance
    clearCache: () => {
      setState(prev => ({ ...prev, previewCache: new Map() }));
      
      if (styleAnalysisServiceRef.current) {
        styleAnalysisServiceRef.current.clearCache();
      }
    },

    preloadStyles: async (styleIds: string[]): Promise<void> => {
      // Preload style thumbnails or metadata for better performance
      console.log('Preloading styles:', styleIds);
    },

    // Settings
    updateDefaultParameters: (parameters: Partial<StyleParameters>) => {
      currentParametersRef.current = { ...currentParametersRef.current, ...parameters };
    },

    updateDefaultOptions: (options: Partial<TransferOptions>) => {
      Object.assign(defaultOptions, options);
    }

  }), [
    state.previewCache,
    enablePreviewCache,
    maxCacheSize,
    defaultOptions,
    onPreviewGenerated
  ]);

  // Helper function to generate preview cache key
  const generatePreviewCacheKey = (
    contentImage: ImageData | HTMLImageElement | string,
    styleReference: StyleReference,
    parameters: StyleParameters
  ): string => {
    const imageKey = typeof contentImage === 'string' ? contentImage : 'image_data';
    const styleKey = typeof styleReference.source === 'string' ? styleReference.source : 'style_data';
    const paramKey = JSON.stringify(parameters);
    
    return `${imageKey}_${styleKey}_${paramKey}`;
  };

  // Helper function to convert various image formats to ImageData
  const convertToImageData = async (image: HTMLImageElement | string): Promise<ImageData> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      if (typeof image === 'string') {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          resolve(ctx.getImageData(0, 0, canvas.width, canvas.height));
        };
        
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = image;
      } else {
        canvas.width = image.width;
        canvas.height = image.height;
        ctx.drawImage(image, 0, 0);
        resolve(ctx.getImageData(0, 0, canvas.width, canvas.height));
      }
    });
  };

  // Computed values
  const isReady = useMemo(() => {
    return state.isInitialized && 
           styleTransferServiceRef.current !== null && 
           styleLibraryManagerRef.current !== null &&
           styleAnalysisServiceRef.current !== null &&
           multiProviderAI.isReady;
  }, [state.isInitialized, multiProviderAI.isReady]);

  return {
    state,
    actions,
    services: {
      styleTransfer: styleTransferServiceRef.current,
      styleLibrary: styleLibraryManagerRef.current,
      styleAnalysis: styleAnalysisServiceRef.current
    },
    isReady
  };
}