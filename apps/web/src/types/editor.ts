import type { editor } from 'monaco-editor';

export interface EditorConfiguration {
  theme: 'vs' | 'vs-dark' | 'hc-black' | 'unified-dark';
  fontSize: number;
  fontFamily: string;
  lineNumbers: 'on' | 'off' | 'relative' | 'interval';
  wordWrap: 'on' | 'off' | 'wordWrapColumn' | 'bounded';
  minimap: { enabled: boolean; side: 'left' | 'right' };
  scrollBeyondLastLine: boolean;
  automaticLayout: boolean;
  tabSize: number;
  insertSpaces: boolean;
  formatOnSave: boolean;
  formatOnType: boolean;
  autoSave: boolean;
  autoSaveDelay: number;
}

export interface FileSystemState {
  currentProject: string | null;
  openFiles: OpenFile[];
  activeFile: string | null;
  fileTree: FileNode[];
  searchResults: SearchResult[];
  recentFiles: string[];
}

export interface OpenFile {
  id: string;
  path: string;
  name: string;
  content: string;
  originalContent: string;
  isDirty: boolean;
  isLoading: boolean;
  isReadonly: boolean;
  language: string;
  cursorPosition: Position;
  selection?: Range;
  scrollPosition?: { top: number; left: number };
  viewState?: editor.ICodeEditorViewState;
}

export interface FileNode {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified?: Date;
  children?: FileNode[];
  isExpanded?: boolean;
  isLoading?: boolean;
  error?: string;
}

export interface Position {
  line: number;
  column: number;
}

export interface Range {
  startLine: number;
  startColumn: number;
  endLine: number;
  endColumn: number;
}

export interface SearchResult {
  file: string;
  line: number;
  column: number;
  text: string;
  match: string;
}

export interface LanguageDefinition {
  id: string;
  name: string;
  extensions: string[];
  mimeTypes: string[];
  configuration: LanguageConfiguration;
  grammar?: TextMateGrammar;
  languageServer?: LanguageServerConfig;
  features: LanguageFeature[];
}

export interface LanguageConfiguration {
  comments: CommentConfiguration;
  brackets: BracketConfiguration;
  autoClosingPairs: AutoClosingPair[];
  surroundingPairs: SurroundingPair[];
  folding: FoldingConfiguration;
  wordPattern: RegExp;
  indentationRules: IndentationRule[];
}

export interface CommentConfiguration {
  lineComment?: string;
  blockComment?: [string, string];
}

export interface BracketConfiguration {
  pairs: [string, string][];
  colorized: boolean;
}

export interface AutoClosingPair {
  open: string;
  close: string;
  notIn?: string[];
}

export interface SurroundingPair {
  open: string;
  close: string;
}

export interface FoldingConfiguration {
  markers: {
    start: RegExp;
    end: RegExp;
  };
  offSide: boolean;
}

export interface IndentationRule {
  decreaseIndentPattern: RegExp;
  increaseIndentPattern: RegExp;
  indentNextLinePattern?: RegExp;
  unIndentedLinePattern?: RegExp;
}

export interface TextMateGrammar {
  scopeName: string;
  path: string;
  embeddedLanguages?: Record<string, string>;
  tokenTypes?: Record<string, string>;
}

export interface LanguageServerConfig {
  serverPath: string;
  initializationOptions?: any;
  settings?: any;
  capabilities: ServerCapabilities;
  fileEvents: string[];
}

export interface ServerCapabilities {
  textDocumentSync: boolean;
  completionProvider: boolean;
  hoverProvider: boolean;
  definitionProvider: boolean;
  referencesProvider: boolean;
  documentSymbolProvider: boolean;
  workspaceSymbolProvider: boolean;
  codeActionProvider: boolean;
  codeLensProvider: boolean;
  documentFormattingProvider: boolean;
  documentRangeFormattingProvider: boolean;
  documentOnTypeFormattingProvider: boolean;
  renameProvider: boolean;
  documentLinkProvider: boolean;
  colorProvider: boolean;
  foldingRangeProvider: boolean;
  implementationProvider: boolean;
  typeDefinitionProvider: boolean;
  declarationProvider: boolean;
  selectionRangeProvider: boolean;
}

export interface LanguageFeature {
  name: LanguageFeatureName;
  enabled: boolean;
  configuration?: any;
}

export type LanguageFeatureName = 
  | 'syntax-highlighting'
  | 'error-detection'
  | 'auto-completion'
  | 'go-to-definition'
  | 'find-references'
  | 'symbol-outline'
  | 'formatting'
  | 'refactoring'
  | 'hover-info'
  | 'code-lens'
  | 'folding'
  | 'bracket-matching';

export interface LanguageDetectionResult {
  language: string;
  confidence: number;
  alternatives: {
    language: string;
    confidence: number;
  }[];
}

export interface LanguagePattern {
  regex: RegExp;
  weight: number;
  description: string;
}

export interface CodeAction {
  title: string;
  kind: string;
  command?: Command;
  edit?: WorkspaceEdit;
  isPreferred?: boolean;
}

export interface Command {
  title: string;
  command: string;
  arguments?: any[];
}

export interface WorkspaceEdit {
  changes: Record<string, TextEdit[]>;
}

export interface TextEdit {
  range: Range;
  newText: string;
}

export interface Diagnostic {
  range: Range;
  message: string;
  severity: DiagnosticSeverity;
  code?: string | number;
  source?: string;
  relatedInformation?: DiagnosticRelatedInformation[];
}

export enum DiagnosticSeverity {
  Error = 1,
  Warning = 2,
  Information = 3,
  Hint = 4,
}

export interface DiagnosticRelatedInformation {
  location: Location;
  message: string;
}

export interface Location {
  uri: string;
  range: Range;
}

export interface CompletionItem {
  label: string;
  kind: CompletionItemKind;
  detail?: string;
  documentation?: string;
  insertText?: string;
  filterText?: string;
  sortText?: string;
  commitCharacters?: string[];
  command?: Command;
  additionalTextEdits?: TextEdit[];
}

export enum CompletionItemKind {
  Text = 1,
  Method = 2,
  Function = 3,
  Constructor = 4,
  Field = 5,
  Variable = 6,
  Class = 7,
  Interface = 8,
  Module = 9,
  Property = 10,
  Unit = 11,
  Value = 12,
  Enum = 13,
  Keyword = 14,
  Snippet = 15,
  Color = 16,
  File = 17,
  Reference = 18,
  Folder = 19,
  EnumMember = 20,
  Constant = 21,
  Struct = 22,
  Event = 23,
  Operator = 24,
  TypeParameter = 25,
}

export interface HoverInfo {
  contents: string[];
  range?: Range;
}

export interface SymbolInformation {
  name: string;
  kind: SymbolKind;
  location: Location;
  containerName?: string;
}

export enum SymbolKind {
  File = 1,
  Module = 2,
  Namespace = 3,
  Package = 4,
  Class = 5,
  Method = 6,
  Property = 7,
  Field = 8,
  Constructor = 9,
  Enum = 10,
  Interface = 11,
  Function = 12,
  Variable = 13,
  Constant = 14,
  String = 15,
  Number = 16,
  Boolean = 17,
  Array = 18,
  Object = 19,
  Key = 20,
  Null = 21,
  EnumMember = 22,
  Struct = 23,
  Event = 24,
  Operator = 25,
  TypeParameter = 26,
}

export interface FormattingOptions {
  tabSize: number;
  insertSpaces: boolean;
  trimTrailingWhitespace?: boolean;
  insertFinalNewline?: boolean;
  trimFinalNewlines?: boolean;
}