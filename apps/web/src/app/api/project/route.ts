import { NextRequest, NextResponse } from 'next/server';
import { ProjectManagementService } from '@/lib/project/project-management-service';
import { ProjectConfiguration, ProjectTemplate, WorkspaceSettings } from '@/lib/project/project-service';

const projectService = new ProjectManagementService();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const projectId = searchParams.get('projectId');
    const templateId = searchParams.get('templateId');

    switch (action) {
      case 'list_projects':
        const projects = await projectService.listProjects();
        return NextResponse.json({ success: true, projects });

      case 'get_project':
        if (!projectId) {
          return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
        }
        const project = await projectService.getProject(projectId);
        return NextResponse.json({ success: true, project });

      case 'list_templates':
        const templates = await projectService.listTemplates();
        return NextResponse.json({ success: true, templates });

      case 'get_template':
        if (!templateId) {
          return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
        }
        const template = await projectService.getTemplate(templateId);
        return NextResponse.json({ success: true, template });

      case 'get_workspace_settings':
        const settings = await projectService.getWorkspaceSettings();
        return NextResponse.json({ success: true, settings });

      case 'get_build_status':
        if (!projectId) {
          return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
        }
        const buildStatus = await projectService.getBuildStatus(projectId);
        return NextResponse.json({ success: true, buildStatus });

      default:
        return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Project API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, projectId, templateId, config, name, description, buildConfig, workspaceSettings } = body;

    switch (action) {
      case 'create_project':
        if (!name) {
          return NextResponse.json({ error: 'Project name is required' }, { status: 400 });
        }
        const newProject = await projectService.createProject(name, description, config as ProjectConfiguration);
        return NextResponse.json({ success: true, project: newProject });

      case 'create_from_template':
        if (!templateId || !name) {
          return NextResponse.json({ error: 'Template ID and project name are required' }, { status: 400 });
        }
        const projectFromTemplate = await projectService.createProjectFromTemplate(templateId, name, config);
        return NextResponse.json({ success: true, project: projectFromTemplate });

      case 'update_project':
        if (!projectId) {
          return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
        }
        const updatedProject = await projectService.updateProject(projectId, config as ProjectConfiguration);
        return NextResponse.json({ success: true, project: updatedProject });

      case 'delete_project':
        if (!projectId) {
          return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
        }
        await projectService.deleteProject(projectId);
        return NextResponse.json({ success: true });

      case 'create_template':
        if (!name) {
          return NextResponse.json({ error: 'Template name is required' }, { status: 400 });
        }
        const newTemplate = await projectService.createTemplate(name, description, config as ProjectTemplate);
        return NextResponse.json({ success: true, template: newTemplate });

      case 'build_project':
        if (!projectId) {
          return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
        }
        const buildResult = await projectService.buildProject(projectId, buildConfig);
        return NextResponse.json({ success: true, buildResult });

      case 'test_project':
        if (!projectId) {
          return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
        }
        const testResult = await projectService.testProject(projectId);
        return NextResponse.json({ success: true, testResult });

      case 'deploy_project':
        if (!projectId) {
          return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
        }
        const deployResult = await projectService.deployProject(projectId, config);
        return NextResponse.json({ success: true, deployResult });

      case 'update_workspace_settings':
        if (!workspaceSettings) {
          return NextResponse.json({ error: 'Workspace settings are required' }, { status: 400 });
        }
        const updatedSettings = await projectService.updateWorkspaceSettings(workspaceSettings as WorkspaceSettings);
        return NextResponse.json({ success: true, settings: updatedSettings });

      case 'scaffold_project':
        if (!projectId || !templateId) {
          return NextResponse.json({ error: 'Project ID and template ID are required' }, { status: 400 });
        }
        const scaffoldResult = await projectService.scaffoldProject(projectId, templateId, config);
        return NextResponse.json({ success: true, result: scaffoldResult });

      default:
        return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Project API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { projectId, config } = body;

    if (!projectId || !config) {
      return NextResponse.json({ error: 'Project ID and configuration are required' }, { status: 400 });
    }

    const updatedProject = await projectService.updateProject(projectId, config as ProjectConfiguration);
    return NextResponse.json({ success: true, project: updatedProject });
  } catch (error) {
    console.error('Project API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    await projectService.deleteProject(projectId);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Project API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}