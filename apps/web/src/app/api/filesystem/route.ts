import { NextRequest, NextResponse } from 'next/server';
import { FileSystemService } from '@/lib/filesystem/filesystem-backend-service';
import { SearchQuery, FileOperation } from '@/lib/filesystem/filesystem-service';

const fileSystemService = new FileSystemService();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const path = searchParams.get('path');

    switch (action) {
      case 'list':
        if (!path) {
          return NextResponse.json({ error: 'Path is required' }, { status: 400 });
        }
        const entries = await fileSystemService.listDirectory(path);
        return NextResponse.json({ success: true, entries });

      case 'info':
        if (!path) {
          return NextResponse.json({ error: 'Path is required' }, { status: 400 });
        }
        const info = await fileSystemService.getFileInfo(path);
        return NextResponse.json({ success: true, info });

      case 'read':
        if (!path) {
          return NextResponse.json({ error: 'Path is required' }, { status: 400 });
        }
        const content = await fileSystemService.readFile(path);
        return NextResponse.json({ success: true, content: Buffer.from(content).toString('base64') });

      case 'exists':
        if (!path) {
          return NextResponse.json({ error: 'Path is required' }, { status: 400 });
        }
        const exists = await fileSystemService.exists(path);
        return NextResponse.json({ success: true, exists });

      default:
        return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
    }
  } catch (error) {
    console.error('FileSystem API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, path, content, targetPath, operations, query } = body;

    switch (action) {
      case 'write':
        if (!path || content === undefined) {
          return NextResponse.json({ error: 'Path and content are required' }, { status: 400 });
        }
        const data = Buffer.from(content, 'base64');
        await fileSystemService.writeFile(path, data);
        return NextResponse.json({ success: true });

      case 'create_directory':
        if (!path) {
          return NextResponse.json({ error: 'Path is required' }, { status: 400 });
        }
        await fileSystemService.createDirectory(path);
        return NextResponse.json({ success: true });

      case 'delete':
        if (!path) {
          return NextResponse.json({ error: 'Path is required' }, { status: 400 });
        }
        await fileSystemService.deleteFile(path);
        return NextResponse.json({ success: true });

      case 'copy':
        if (!path || !targetPath) {
          return NextResponse.json({ error: 'Source and target paths are required' }, { status: 400 });
        }
        await fileSystemService.copyFile(path, targetPath);
        return NextResponse.json({ success: true });

      case 'move':
        if (!path || !targetPath) {
          return NextResponse.json({ error: 'Source and target paths are required' }, { status: 400 });
        }
        await fileSystemService.renameFile(path, targetPath);
        return NextResponse.json({ success: true });

      case 'search':
        if (!query) {
          return NextResponse.json({ error: 'Search query is required' }, { status: 400 });
        }
        const searchResults = await fileSystemService.searchFiles(query as SearchQuery);
        return NextResponse.json({ success: true, results: searchResults });

      case 'bulk_operation':
        if (!operations) {
          return NextResponse.json({ error: 'Operations are required' }, { status: 400 });
        }
        const results = await fileSystemService.bulkOperation(operations as FileOperation[]);
        return NextResponse.json({ success: true, results });

      case 'watch':
        if (!path) {
          return NextResponse.json({ error: 'Path is required' }, { status: 400 });
        }
        // File watching would be implemented with WebSockets
        return NextResponse.json({ success: true, message: 'File watching started' });

      default:
        return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
    }
  } catch (error) {
    console.error('FileSystem API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { path, permissions } = body;

    if (!path || !permissions) {
      return NextResponse.json({ error: 'Path and permissions are required' }, { status: 400 });
    }

    await fileSystemService.setFilePermissions(path, permissions);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('FileSystem API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}