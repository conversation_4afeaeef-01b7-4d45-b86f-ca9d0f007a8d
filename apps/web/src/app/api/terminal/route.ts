import { NextRequest, NextResponse } from 'next/server';
import { TerminalService } from '@/lib/terminal/terminal-backend-service';
import { TerminalConfiguration, TerminalSession } from '@/lib/terminal/terminal-service';

const terminalService = new TerminalService();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, sessionId, config, command } = body;

    switch (action) {
      case 'create_session':
        const session = await terminalService.createSession(config as TerminalConfiguration);
        return NextResponse.json({ success: true, session });

      case 'execute_command':
        if (!sessionId || !command) {
          return NextResponse.json({ error: 'Missing sessionId or command' }, { status: 400 });
        }
        await terminalService.executeCommand(sessionId, command);
        return NextResponse.json({ success: true });

      case 'resize_terminal':
        if (!sessionId || !body.cols || !body.rows) {
          return NextResponse.json({ error: 'Missing resize parameters' }, { status: 400 });
        }
        await terminalService.resizeTerminal(sessionId, body.cols, body.rows);
        return NextResponse.json({ success: true });

      case 'list_sessions':
        const sessions = await terminalService.listSessions();
        return NextResponse.json({ success: true, sessions });

      case 'get_session':
        if (!sessionId) {
          return NextResponse.json({ error: 'Missing sessionId' }, { status: 400 });
        }
        const sessionData = await terminalService.getSession(sessionId);
        return NextResponse.json({ success: true, session: sessionData });

      case 'delete_session':
        if (!sessionId) {
          return NextResponse.json({ error: 'Missing sessionId' }, { status: 400 });
        }
        await terminalService.deleteSession(sessionId);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Terminal API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const sessions = await terminalService.listSessions();
    return NextResponse.json({ success: true, sessions });
  } catch (error) {
    console.error('Terminal API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}