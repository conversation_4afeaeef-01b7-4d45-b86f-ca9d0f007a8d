@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Monaco Editor customizations */
.monaco-editor {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Ubuntu Mono', monospace !important;
}

.monaco-editor .suggest-widget {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Ubuntu Mono', monospace !important;
}

.monaco-editor .monaco-hover {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Ubuntu Mono', monospace !important;
}

/* File explorer styles */
.file-explorer {
  @apply h-full bg-background border-r border-border;
}

.file-tree-node {
  @apply flex items-center gap-2 py-1 px-2 text-sm cursor-pointer hover:bg-accent rounded-sm;
}

.file-tree-node.active {
  @apply bg-accent text-accent-foreground;
}

.file-tree-node.directory {
  @apply font-medium;
}

.file-tree-node.file {
  @apply font-normal;
}

/* Tab styles */
.editor-tab {
  @apply flex items-center gap-2 px-3 py-2 border-r border-border bg-background hover:bg-accent cursor-pointer;
}

.editor-tab.active {
  @apply bg-accent text-accent-foreground;
}

.editor-tab .close-button {
  @apply ml-auto opacity-0 group-hover:opacity-100 hover:bg-destructive hover:text-destructive-foreground rounded-sm;
}

/* Status bar styles */
.status-bar {
  @apply h-6 bg-primary text-primary-foreground px-2 flex items-center justify-between text-xs;
}

/* Loading states */
.loading-spinner {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-current;
}

/* Error states */
.error-message {
  @apply text-destructive bg-destructive/10 border border-destructive/20 rounded-md p-3 text-sm;
}

/* Responsive design */
@media (max-width: 768px) {
  .file-explorer {
    @apply w-full;
  }
  
  .monaco-editor {
    font-size: 12px !important;
  }
}

/* Accessibility improvements */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* Focus indicators */
.focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .monaco-editor {
    --vscode-editor-background: #000000 !important;
    --vscode-editor-foreground: #ffffff !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}