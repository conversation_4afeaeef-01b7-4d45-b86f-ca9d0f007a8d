import * as monaco from 'monaco-editor';
import { 
  LanguageDefinition, 
  LanguageDetectionResult, 
  LanguagePattern,
  CompletionItem,
  HoverInfo,
  Diagnostic,
  SymbolInformation,
  FormattingOptions,
  CodeAction
} from '@/types/editor';
import { getLanguageFromExtension } from '@/lib/utils';

export class LanguageManager {
  private languages: Map<string, LanguageDefinition> = new Map();
  private detectionPatterns: Map<string, LanguagePattern[]> = new Map();
  private disposables: monaco.IDisposable[] = [];

  constructor() {
    this.initializeBuiltinLanguages();
    this.setupLanguageDetectionPatterns();
  }

  private initializeBuiltinLanguages() {
    // Register built-in languages with enhanced configurations
    this.registerBuiltinLanguages();
    this.enhanceMonacoLanguages();
  }

  private registerBuiltinLanguages() {
    // JavaScript/TypeScript
    this.registerLanguage({
      id: 'javascript',
      name: 'JavaScript',
      extensions: ['js', 'jsx', 'mjs', 'cjs'],
      mimeTypes: ['application/javascript', 'text/javascript'],
      configuration: {
        comments: { lineComment: '//', blockComment: ['/*', '*/'] },
        brackets: { pairs: [['(', ')'], ['{', '}'], ['[', ']']], colorized: true },
        autoClosingPairs: [
          { open: '(', close: ')' },
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '"', close: '"', notIn: ['string'] },
          { open: "'", close: "'", notIn: ['string'] },
          { open: '`', close: '`', notIn: ['string'] },
        ],
        surroundingPairs: [
          { open: '(', close: ')' },
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '"', close: '"' },
          { open: "'", close: "'" },
          { open: '`', close: '`' },
        ],
        folding: {
          markers: { start: /^\s*\/\*\*/, end: /^\s*\*\// },
          offSide: true,
        },
        wordPattern: /(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,
        indentationRules: [
          {
            decreaseIndentPattern: /^((?!.*?\/\*).*\*\/)?\s*[\}\]\)].*$/,
            increaseIndentPattern: /^((?!\/\/).)*(\{[^}"'`]*|\([^)"'`]*|\[[^\]"'`]*)$/,
          },
        ],
      },
      features: [
        { name: 'syntax-highlighting', enabled: true },
        { name: 'error-detection', enabled: true },
        { name: 'auto-completion', enabled: true },
        { name: 'go-to-definition', enabled: true },
        { name: 'find-references', enabled: true },
        { name: 'symbol-outline', enabled: true },
        { name: 'formatting', enabled: true },
        { name: 'refactoring', enabled: true },
        { name: 'hover-info', enabled: true },
        { name: 'code-lens', enabled: true },
        { name: 'folding', enabled: true },
        { name: 'bracket-matching', enabled: true },
      ],
    });

    // TypeScript
    this.registerLanguage({
      id: 'typescript',
      name: 'TypeScript',
      extensions: ['ts', 'tsx', 'cts', 'mts'],
      mimeTypes: ['application/typescript', 'text/typescript'],
      configuration: {
        comments: { lineComment: '//', blockComment: ['/*', '*/'] },
        brackets: { pairs: [['(', ')'], ['{', '}'], ['[', ']'], ['<', '>']], colorized: true },
        autoClosingPairs: [
          { open: '(', close: ')' },
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '<', close: '>', notIn: ['string'] },
          { open: '"', close: '"', notIn: ['string'] },
          { open: "'", close: "'", notIn: ['string'] },
          { open: '`', close: '`', notIn: ['string'] },
        ],
        surroundingPairs: [
          { open: '(', close: ')' },
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '<', close: '>' },
          { open: '"', close: '"' },
          { open: "'", close: "'" },
          { open: '`', close: '`' },
        ],
        folding: {
          markers: { start: /^\s*\/\*\*/, end: /^\s*\*\// },
          offSide: true,
        },
        wordPattern: /(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,
        indentationRules: [
          {
            decreaseIndentPattern: /^((?!.*?\/\*).*\*\/)?\s*[\}\]\)].*$/,
            increaseIndentPattern: /^((?!\/\/).)*(\{[^}"'`]*|\([^)"'`]*|\[[^\]"'`]*)$/,
          },
        ],
      },
      features: [
        { name: 'syntax-highlighting', enabled: true },
        { name: 'error-detection', enabled: true },
        { name: 'auto-completion', enabled: true },
        { name: 'go-to-definition', enabled: true },
        { name: 'find-references', enabled: true },
        { name: 'symbol-outline', enabled: true },
        { name: 'formatting', enabled: true },
        { name: 'refactoring', enabled: true },
        { name: 'hover-info', enabled: true },
        { name: 'code-lens', enabled: true },
        { name: 'folding', enabled: true },
        { name: 'bracket-matching', enabled: true },
      ],
    });

    // Python
    this.registerLanguage({
      id: 'python',
      name: 'Python',
      extensions: ['py', 'pyw', 'pyi'],
      mimeTypes: ['application/x-python', 'text/x-python'],
      configuration: {
        comments: { lineComment: '#', blockComment: ['"""', '"""'] },
        brackets: { pairs: [['(', ')'], ['{', '}'], ['[', ']']], colorized: true },
        autoClosingPairs: [
          { open: '(', close: ')' },
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '"', close: '"', notIn: ['string'] },
          { open: "'", close: "'", notIn: ['string'] },
          { open: '"""', close: '"""' },
          { open: "'''", close: "'''" },
        ],
        surroundingPairs: [
          { open: '(', close: ')' },
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '"', close: '"' },
          { open: "'", close: "'" },
        ],
        folding: {
          markers: { start: /^\s*#\s*region\b/, end: /^\s*#\s*endregion\b/ },
          offSide: true,
        },
        wordPattern: /(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,
        indentationRules: [
          {
            decreaseIndentPattern: /^\s*(elif|else|except|finally)(\s.*)?:\s*$/,
            increaseIndentPattern: /^\s*(if|elif|else|for|while|def|class|with|try|except|finally)(\s.*)?:\s*$/,
          },
        ],
      },
      features: [
        { name: 'syntax-highlighting', enabled: true },
        { name: 'error-detection', enabled: true },
        { name: 'auto-completion', enabled: true },
        { name: 'go-to-definition', enabled: true },
        { name: 'find-references', enabled: true },
        { name: 'symbol-outline', enabled: true },
        { name: 'formatting', enabled: true },
        { name: 'refactoring', enabled: true },
        { name: 'hover-info', enabled: true },
        { name: 'code-lens', enabled: true },
        { name: 'folding', enabled: true },
        { name: 'bracket-matching', enabled: true },
      ],
    });

    // Add more languages as needed...
  }

  private enhanceMonacoLanguages() {
    // Enhanced TypeScript/JavaScript configuration
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.Latest,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types'],
      strict: true,
      noImplicitAny: true,
      strictNullChecks: true,
      strictFunctionTypes: true,
      noImplicitReturns: true,
      noFallthroughCasesInSwitch: true,
    });

    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.Latest,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      allowJs: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      checkJs: true,
    });

    // Enhanced JSON configuration
    monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
      validate: true,
      allowComments: false,
      schemas: [],
      enableSchemaRequest: true,
      schemaRequest: 'warning',
      schemaValidation: 'warning',
      comments: 'error',
      trailingCommas: 'error',
    });

    // Enhanced CSS configuration
    monaco.languages.css.cssDefaults.setOptions({
      validate: true,
      lint: {
        compatibleVendorPrefixes: 'ignore',
        vendorPrefix: 'warning',
        duplicateProperties: 'warning',
        emptyRules: 'warning',
        importStatement: 'ignore',
        boxModel: 'ignore',
        universalSelector: 'ignore',
        zeroUnits: 'ignore',
        fontFaceProperties: 'warning',
        hexColorLength: 'error',
        argumentsInColorFunction: 'error',
        unknownProperties: 'warning',
        ieHack: 'ignore',
        unknownVendorSpecificProperties: 'ignore',
        propertyIgnoredDueToDisplay: 'warning',
        important: 'ignore',
        float: 'ignore',
        idSelector: 'ignore',
      },
    });

    // Enhanced HTML configuration
    monaco.languages.html.htmlDefaults.setOptions({
      format: {
        tabSize: 2,
        insertSpaces: true,
        wrapLineLength: 120,
        unformatted: 'default',
        contentUnformatted: 'pre,code,textarea',
        indentInnerHtml: false,
        preserveNewLines: true,
        maxPreserveNewLines: 2,
        indentHandlebars: false,
        endWithNewline: false,
        extraLiners: 'head, body, /html',
        wrapAttributes: 'auto',
      },
      suggest: {
        html5: true,
        angular1: false,
        ionic: false,
      },
    });
  }

  private setupLanguageDetectionPatterns() {
    // JavaScript patterns
    this.detectionPatterns.set('javascript', [
      { regex: /import\s+.+\s+from\s+['"][^'"]+['"]/, weight: 0.9, description: 'ES6 import' },
      { regex: /export\s+(default\s+)?/, weight: 0.8, description: 'ES6 export' },
      { regex: /console\.log\s*\(/, weight: 0.7, description: 'console.log' },
      { regex: /function\s+\w+\s*\(/, weight: 0.6, description: 'function declaration' },
      { regex: /const\s+\w+\s*=/, weight: 0.6, description: 'const declaration' },
      { regex: /let\s+\w+\s*=/, weight: 0.6, description: 'let declaration' },
      { regex: /var\s+\w+\s*=/, weight: 0.5, description: 'var declaration' },
      { regex: /require\s*\(['"][^'"]+['"]\)/, weight: 0.7, description: 'CommonJS require' },
      { regex: /module\.exports\s*=/, weight: 0.7, description: 'CommonJS exports' },
      { regex: /\$\(.*\)/, weight: 0.4, description: 'jQuery' },
    ]);

    // TypeScript patterns
    this.detectionPatterns.set('typescript', [
      { regex: /interface\s+\w+\s*\{/, weight: 0.9, description: 'interface declaration' },
      { regex: /type\s+\w+\s*=/, weight: 0.9, description: 'type alias' },
      { regex: /:\s*\w+(\[\])?(\s*\|\s*\w+(\[\])?)*\s*[=;]/, weight: 0.8, description: 'type annotation' },
      { regex: /class\s+\w+\s*(<[^>]+>)?\s*(extends|implements)/, weight: 0.8, description: 'class with generics' },
      { regex: /enum\s+\w+\s*\{/, weight: 0.9, description: 'enum declaration' },
      { regex: /namespace\s+\w+\s*\{/, weight: 0.9, description: 'namespace declaration' },
      { regex: /declare\s+(const|let|var|function|class|module|namespace)/, weight: 0.9, description: 'declaration' },
      { regex: /import\s+type\s+/, weight: 0.9, description: 'type import' },
      { regex: /as\s+\w+/, weight: 0.6, description: 'type assertion' },
      { regex: /<[^>]+>/, weight: 0.5, description: 'generics' },
    ]);

    // Python patterns
    this.detectionPatterns.set('python', [
      { regex: /def\s+\w+\s*\(/, weight: 0.9, description: 'function definition' },
      { regex: /class\s+\w+(\([^)]*\))?:/, weight: 0.9, description: 'class definition' },
      { regex: /import\s+\w+/, weight: 0.8, description: 'import statement' },
      { regex: /from\s+\w+\s+import/, weight: 0.8, description: 'from import' },
      { regex: /if\s+__name__\s*==\s*['"]__main__['"]/, weight: 0.9, description: 'main guard' },
      { regex: /print\s*\(/, weight: 0.7, description: 'print function' },
      { regex: /self\.\w+/, weight: 0.6, description: 'self reference' },
      { regex: /lambda\s+\w*:/, weight: 0.7, description: 'lambda function' },
      { regex: /^\s*#.*$/, weight: 0.4, description: 'comments' },
      { regex: /for\s+\w+\s+in\s+/, weight: 0.6, description: 'for loop' },
    ]);

    // Add more language patterns as needed...
  }

  async registerLanguage(definition: LanguageDefinition): Promise<void> {
    // Validate language definition
    this.validateLanguageDefinition(definition);

    // Register with Monaco
    monaco.languages.register({ id: definition.id });

    // Setup language configuration
    monaco.languages.setLanguageConfiguration(definition.id, {
      comments: definition.configuration.comments,
      brackets: definition.configuration.brackets.pairs,
      autoClosingPairs: definition.configuration.autoClosingPairs,
      surroundingPairs: definition.configuration.surroundingPairs,
      folding: definition.configuration.folding,
      wordPattern: definition.configuration.wordPattern,
      indentationRules: definition.configuration.indentationRules[0],
    });

    // Register language features
    this.registerLanguageFeatures(definition.id, definition.features);

    // Store language definition
    this.languages.set(definition.id, definition);
  }

  private validateLanguageDefinition(definition: LanguageDefinition): void {
    if (!definition.id || !definition.name) {
      throw new Error('Language definition must have id and name');
    }
    if (!definition.extensions || definition.extensions.length === 0) {
      throw new Error('Language definition must have at least one extension');
    }
  }

  private registerLanguageFeatures(languageId: string, features: any[]): void {
    features.forEach(feature => {
      if (!feature.enabled) return;

      switch (feature.name) {
        case 'auto-completion':
          this.registerCompletionProvider(languageId);
          break;
        case 'hover-info':
          this.registerHoverProvider(languageId);
          break;
        case 'go-to-definition':
          this.registerDefinitionProvider(languageId);
          break;
        case 'find-references':
          this.registerReferenceProvider(languageId);
          break;
        case 'symbol-outline':
          this.registerDocumentSymbolProvider(languageId);
          break;
        case 'formatting':
          this.registerFormattingProvider(languageId);
          break;
        case 'code-lens':
          this.registerCodeLensProvider(languageId);
          break;
        case 'error-detection':
          this.registerDiagnosticsProvider(languageId);
          break;
      }
    });
  }

  private registerCompletionProvider(languageId: string): void {
    const disposable = monaco.languages.registerCompletionItemProvider(languageId, {
      provideCompletionItems: (model, position) => {
        // Basic completion logic - can be enhanced with language server
        const word = model.getWordUntilPosition(position);
        const range = {
          startLineNumber: position.lineNumber,
          endLineNumber: position.lineNumber,
          startColumn: word.startColumn,
          endColumn: word.endColumn,
        };

        return {
          suggestions: this.getBasicCompletions(languageId, range),
        };
      },
    });

    this.disposables.push(disposable);
  }

  private registerHoverProvider(languageId: string): void {
    const disposable = monaco.languages.registerHoverProvider(languageId, {
      provideHover: (model, position) => {
        // Basic hover info - can be enhanced with language server
        const word = model.getWordAtPosition(position);
        if (!word) return null;

        return {
          range: new monaco.Range(
            position.lineNumber,
            word.startColumn,
            position.lineNumber,
            word.endColumn
          ),
          contents: [
            { value: `**${word.word}**` },
            { value: `Language: ${languageId}` },
          ],
        };
      },
    });

    this.disposables.push(disposable);
  }

  private registerDefinitionProvider(languageId: string): void {
    const disposable = monaco.languages.registerDefinitionProvider(languageId, {
      provideDefinition: (model, position) => {
        // Basic definition logic - would need language server for full functionality
        return null;
      },
    });

    this.disposables.push(disposable);
  }

  private registerReferenceProvider(languageId: string): void {
    const disposable = monaco.languages.registerReferenceProvider(languageId, {
      provideReferences: (model, position, context) => {
        // Basic reference logic - would need language server for full functionality
        return [];
      },
    });

    this.disposables.push(disposable);
  }

  private registerDocumentSymbolProvider(languageId: string): void {
    const disposable = monaco.languages.registerDocumentSymbolProvider(languageId, {
      provideDocumentSymbols: (model) => {
        // Basic symbol extraction - would need language server for full functionality
        return [];
      },
    });

    this.disposables.push(disposable);
  }

  private registerFormattingProvider(languageId: string): void {
    const disposable = monaco.languages.registerDocumentFormattingEditProvider(languageId, {
      provideDocumentFormattingEdits: (model, options) => {
        // Basic formatting - would need language server for full functionality
        return [];
      },
    });

    this.disposables.push(disposable);
  }

  private registerCodeLensProvider(languageId: string): void {
    const disposable = monaco.languages.registerCodeLensProvider(languageId, {
      provideCodeLenses: (model) => {
        // Basic code lens - would need language server for full functionality
        return { lenses: [], dispose: () => {} };
      },
    });

    this.disposables.push(disposable);
  }

  private registerDiagnosticsProvider(languageId: string): void {
    // Diagnostics are typically provided by language servers
    // This is a placeholder for basic validation
  }

  private getBasicCompletions(languageId: string, range: any): any[] {
    // Basic completion items based on language
    const basicCompletions: Record<string, any[]> = {
      javascript: [
        {
          label: 'console.log',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'console.log(${1:value})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          range,
        },
        {
          label: 'function',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'function ${1:name}(${2:params}) {\n\t${3:// body}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          range,
        },
      ],
      typescript: [
        {
          label: 'interface',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'interface ${1:Name} {\n\t${2:// properties}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          range,
        },
        {
          label: 'type',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'type ${1:Name} = ${2:type}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          range,
        },
      ],
      python: [
        {
          label: 'def',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'def ${1:name}(${2:params}):\n\t${3:pass}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          range,
        },
        {
          label: 'class',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'class ${1:Name}:\n\t${2:pass}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          range,
        },
      ],
    };

    return basicCompletions[languageId] || [];
  }

  async detectLanguage(filename: string, content?: string): Promise<string> {
    // First try extension-based detection
    const extension = filename.split('.').pop()?.toLowerCase();
    if (extension) {
      const extensionMatch = getLanguageFromExtension(extension);
      if (extensionMatch !== 'plaintext') {
        return extensionMatch;
      }
    }

    // Use content analysis for ambiguous cases
    if (content) {
      const contentResult = await this.analyzeContent(content);
      if (contentResult.confidence > 0.7) {
        return contentResult.language;
      }
    }

    return 'plaintext';
  }

  private async analyzeContent(content: string): Promise<LanguageDetectionResult> {
    const scores = new Map<string, number>();

    // Analyze content against all language patterns
    for (const [language, patterns] of this.detectionPatterns) {
      let score = 0;
      for (const pattern of patterns) {
        if (pattern.regex.test(content)) {
          score += pattern.weight;
        }
      }
      scores.set(language, Math.min(score, 1.0));
    }

    const sortedScores = Array.from(scores.entries())
      .sort(([, a], [, b]) => b - a);

    return {
      language: sortedScores[0]?.[0] || 'plaintext',
      confidence: sortedScores[0]?.[1] || 0,
      alternatives: sortedScores.slice(1, 4).map(([lang, conf]) => ({
        language: lang,
        confidence: conf,
      })),
    };
  }

  getLanguageDefinition(languageId: string): LanguageDefinition | undefined {
    return this.languages.get(languageId);
  }

  getSupportedLanguages(): LanguageDefinition[] {
    return Array.from(this.languages.values());
  }

  isLanguageSupported(languageId: string): boolean {
    return this.languages.has(languageId);
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.disposables = [];
  }
}

// Global instance
export const languageManager = new LanguageManager();