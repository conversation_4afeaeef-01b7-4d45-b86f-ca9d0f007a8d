import { EventEmitter } from 'events';
import fs from 'fs/promises';
import path from 'path';
import { spawn, ChildProcess } from 'child_process';
import { 
  ProjectConfiguration, 
  ProjectTemplate, 
  Project, 
  BuildConfiguration, 
  BuildResult, 
  TestResult, 
  DeploymentConfiguration, 
  DeploymentResult,
  WorkspaceSettings,
  ProjectScaffoldResult
} from './project-service';

interface ProjectProcess {
  id: string;
  projectId: string;
  type: 'build' | 'test' | 'deploy' | 'dev';
  process: ChildProcess;
  startTime: Date;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  output: string[];
  exitCode?: number;
}

export class ProjectManagementService extends EventEmitter {
  private projects: Map<string, Project> = new Map();
  private templates: Map<string, ProjectTemplate> = new Map();
  private processes: Map<string, ProjectProcess> = new Map();
  private workspaceSettings: WorkspaceSettings;
  private readonly projectsPath: string;
  private readonly templatesPath: string;
  private readonly maxConcurrentBuilds = 3;

  constructor(workspacePath: string = '/tmp/workspace') {
    super();
    this.projectsPath = path.join(workspacePath, 'projects');
    this.templatesPath = path.join(workspacePath, 'templates');
    this.workspaceSettings = {
      defaultTemplate: 'react-typescript',
      autoSave: true,
      buildOnSave: false,
      defaultBuildTool: 'npm',
      testFramework: 'jest',
      linter: 'eslint',
      formatter: 'prettier',
      gitIntegration: true,
      dockerSupport: false
    };
    this.initializeBuiltinTemplates();
    this.setupProcessCleanup();
  }

  // Project Management
  async createProject(name: string, description: string = '', config?: ProjectConfiguration): Promise<Project> {
    const projectId = this.generateId();
    const projectPath = path.join(this.projectsPath, projectId);

    const defaultConfig: ProjectConfiguration = {
      name,
      description,
      type: 'web',
      framework: 'react',
      language: 'typescript',
      packageManager: 'npm',
      buildTool: 'vite',
      testFramework: 'jest',
      linter: 'eslint',
      formatter: 'prettier',
      gitEnabled: true,
      dockerEnabled: false,
      dependencies: {},
      devDependencies: {},
      scripts: {
        start: 'npm run dev',
        dev: 'vite',
        build: 'vite build',
        test: 'jest',
        lint: 'eslint src',
        format: 'prettier --write src'
      },
      environment: {
        NODE_ENV: 'development'
      }
    };

    const projectConfig = { ...defaultConfig, ...config };

    const project: Project = {
      id: projectId,
      name,
      description,
      path: projectPath,
      config: projectConfig,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      lastBuild: undefined,
      lastDeploy: undefined,
      health: {
        status: 'healthy',
        lastCheck: new Date(),
        issues: []
      }
    };

    // Create project directory structure
    await this.createProjectStructure(project);

    this.projects.set(projectId, project);
    this.emit('projectCreated', project);

    return project;
  }

  async createProjectFromTemplate(templateId: string, name: string, config?: any): Promise<Project> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    const projectConfig = { ...template.config, name, ...config };
    const project = await this.createProject(name, template.description, projectConfig);

    // Apply template scaffolding
    await this.applyTemplateScaffolding(project, template);

    this.emit('projectCreatedFromTemplate', project, templateId);
    return project;
  }

  async updateProject(projectId: string, config: ProjectConfiguration): Promise<Project> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error(`Project not found: ${projectId}`);
    }

    project.config = { ...project.config, ...config };
    project.updatedAt = new Date();

    // Update project files if needed
    await this.updateProjectFiles(project);

    this.projects.set(projectId, project);
    this.emit('projectUpdated', project);

    return project;
  }

  async deleteProject(projectId: string): Promise<void> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error(`Project not found: ${projectId}`);
    }

    // Cancel any running processes
    for (const [processId, process] of this.processes.entries()) {
      if (process.projectId === projectId) {
        await this.cancelProcess(processId);
      }
    }

    // Delete project directory
    try {
      await fs.rm(project.path, { recursive: true, force: true });
    } catch (error) {
      console.warn(`Failed to delete project directory: ${error}`);
    }

    this.projects.delete(projectId);
    this.emit('projectDeleted', projectId);
  }

  async getProject(projectId: string): Promise<Project | null> {
    return this.projects.get(projectId) || null;
  }

  async listProjects(): Promise<Project[]> {
    return Array.from(this.projects.values());
  }

  // Template Management
  async createTemplate(name: string, description: string, config: ProjectTemplate): Promise<ProjectTemplate> {
    const templateId = this.generateId();
    const template: ProjectTemplate = {
      id: templateId,
      name,
      description,
      type: config.type,
      config: config.config,
      files: config.files,
      dependencies: config.dependencies,
      scripts: config.scripts,
      hooks: config.hooks,
      metadata: {
        version: '1.0.0',
        author: 'User',
        tags: config.metadata?.tags || [],
        category: config.metadata?.category || 'custom'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.templates.set(templateId, template);
    this.emit('templateCreated', template);

    return template;
  }

  async getTemplate(templateId: string): Promise<ProjectTemplate | null> {
    return this.templates.get(templateId) || null;
  }

  async listTemplates(): Promise<ProjectTemplate[]> {
    return Array.from(this.templates.values());
  }

  // Build System
  async buildProject(projectId: string, config?: BuildConfiguration): Promise<BuildResult> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error(`Project not found: ${projectId}`);
    }

    const buildConfig = config || this.getDefaultBuildConfig(project);
    const processId = this.generateId();

    const buildResult: BuildResult = {
      id: processId,
      projectId,
      status: 'running',
      startTime: new Date(),
      config: buildConfig,
      output: [],
      artifacts: [],
      warnings: [],
      errors: []
    };

    try {
      const process = await this.startBuildProcess(project, buildConfig, processId);
      
      // Store process reference
      this.processes.set(processId, {
        id: processId,
        projectId,
        type: 'build',
        process,
        startTime: new Date(),
        status: 'running',
        output: []
      });

      // Handle process completion
      process.on('close', (code) => {
        buildResult.status = code === 0 ? 'success' : 'failed';
        buildResult.endTime = new Date();
        buildResult.exitCode = code;

        // Update project
        project.lastBuild = buildResult;
        project.updatedAt = new Date();

        this.processes.delete(processId);
        this.emit('buildCompleted', buildResult);
      });

      this.emit('buildStarted', buildResult);
      return buildResult;

    } catch (error) {
      buildResult.status = 'failed';
      buildResult.endTime = new Date();
      buildResult.errors.push(error instanceof Error ? error.message : 'Unknown build error');
      
      this.emit('buildFailed', buildResult);
      throw error;
    }
  }

  async testProject(projectId: string): Promise<TestResult> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error(`Project not found: ${projectId}`);
    }

    const processId = this.generateId();
    const testResult: TestResult = {
      id: processId,
      projectId,
      status: 'running',
      startTime: new Date(),
      framework: project.config.testFramework || 'jest',
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      coverage: undefined,
      output: [],
      failures: []
    };

    try {
      const process = await this.startTestProcess(project, processId);
      
      this.processes.set(processId, {
        id: processId,
        projectId,
        type: 'test',
        process,
        startTime: new Date(),
        status: 'running',
        output: []
      });

      process.on('close', (code) => {
        testResult.status = code === 0 ? 'passed' : 'failed';
        testResult.endTime = new Date();

        this.processes.delete(processId);
        this.emit('testCompleted', testResult);
      });

      this.emit('testStarted', testResult);
      return testResult;

    } catch (error) {
      testResult.status = 'failed';
      testResult.endTime = new Date();
      
      this.emit('testFailed', testResult);
      throw error;
    }
  }

  async deployProject(projectId: string, config: DeploymentConfiguration): Promise<DeploymentResult> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error(`Project not found: ${projectId}`);
    }

    const processId = this.generateId();
    const deployResult: DeploymentResult = {
      id: processId,
      projectId,
      status: 'running',
      startTime: new Date(),
      config,
      url: undefined,
      output: [],
      errors: []
    };

    try {
      const process = await this.startDeployProcess(project, config, processId);
      
      this.processes.set(processId, {
        id: processId,
        projectId,
        type: 'deploy',
        process,
        startTime: new Date(),
        status: 'running',
        output: []
      });

      process.on('close', (code) => {
        deployResult.status = code === 0 ? 'success' : 'failed';
        deployResult.endTime = new Date();

        // Update project
        project.lastDeploy = deployResult;
        project.updatedAt = new Date();

        this.processes.delete(processId);
        this.emit('deployCompleted', deployResult);
      });

      this.emit('deployStarted', deployResult);
      return deployResult;

    } catch (error) {
      deployResult.status = 'failed';
      deployResult.endTime = new Date();
      deployResult.errors.push(error instanceof Error ? error.message : 'Unknown deploy error');
      
      this.emit('deployFailed', deployResult);
      throw error;
    }
  }

  // Project Scaffolding
  async scaffoldProject(projectId: string, templateId: string, config?: any): Promise<ProjectScaffoldResult> {
    const project = this.projects.get(projectId);
    const template = this.templates.get(templateId);

    if (!project) {
      throw new Error(`Project not found: ${projectId}`);
    }
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    const result: ProjectScaffoldResult = {
      projectId,
      templateId,
      status: 'running',
      startTime: new Date(),
      filesCreated: [],
      errors: []
    };

    try {
      // Apply template files
      for (const [filePath, content] of Object.entries(template.files || {})) {
        const targetPath = path.join(project.path, filePath);
        const processedContent = this.processTemplateContent(content, { ...project.config, ...config });
        
        await fs.mkdir(path.dirname(targetPath), { recursive: true });
        await fs.writeFile(targetPath, processedContent, 'utf-8');
        
        result.filesCreated.push(filePath);
      }

      // Update package.json with dependencies
      if (template.dependencies) {
        await this.updatePackageJson(project, template.dependencies);
      }

      // Run post-scaffold hooks
      if (template.hooks?.postScaffold) {
        await this.runTemplateHook(project, template.hooks.postScaffold);
      }

      result.status = 'success';
      result.endTime = new Date();

      this.emit('scaffoldCompleted', result);
      return result;

    } catch (error) {
      result.status = 'failed';
      result.endTime = new Date();
      result.errors.push(error instanceof Error ? error.message : 'Unknown scaffold error');
      
      this.emit('scaffoldFailed', result);
      throw error;
    }
  }

  // Workspace Management
  async getWorkspaceSettings(): Promise<WorkspaceSettings> {
    return this.workspaceSettings;
  }

  async updateWorkspaceSettings(settings: WorkspaceSettings): Promise<WorkspaceSettings> {
    this.workspaceSettings = { ...this.workspaceSettings, ...settings };
    this.emit('workspaceSettingsUpdated', this.workspaceSettings);
    return this.workspaceSettings;
  }

  // Process Management
  async getBuildStatus(projectId: string): Promise<any> {
    const runningProcesses = Array.from(this.processes.values())
      .filter(p => p.projectId === projectId);
    
    return {
      running: runningProcesses.length > 0,
      processes: runningProcesses.map(p => ({
        id: p.id,
        type: p.type,
        status: p.status,
        startTime: p.startTime
      }))
    };
  }

  async cancelProcess(processId: string): Promise<void> {
    const process = this.processes.get(processId);
    if (!process) {
      throw new Error(`Process not found: ${processId}`);
    }

    process.process.kill('SIGTERM');
    process.status = 'cancelled';
    
    setTimeout(() => {
      if (!process.process.killed) {
        process.process.kill('SIGKILL');
      }
    }, 5000);

    this.processes.delete(processId);
    this.emit('processCancelled', processId);
  }

  // Private Methods
  private async createProjectStructure(project: Project): Promise<void> {
    const { path: projectPath, config } = project;

    // Create basic directory structure
    const dirs = [
      'src',
      'public',
      'tests',
      'docs'
    ];

    for (const dir of dirs) {
      await fs.mkdir(path.join(projectPath, dir), { recursive: true });
    }

    // Create package.json
    const packageJson = {
      name: config.name,
      version: '1.0.0',
      description: project.description,
      main: 'src/index.ts',
      scripts: config.scripts,
      dependencies: config.dependencies,
      devDependencies: config.devDependencies
    };

    await fs.writeFile(
      path.join(projectPath, 'package.json'),
      JSON.stringify(packageJson, null, 2),
      'utf-8'
    );

    // Create basic files based on framework
    await this.createFrameworkFiles(project);
  }

  private async createFrameworkFiles(project: Project): Promise<void> {
    const { path: projectPath, config } = project;

    if (config.framework === 'react') {
      // Create React-specific files
      const indexHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${config.name}</title>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.${config.language === 'typescript' ? 'tsx' : 'jsx'}"></script>
</body>
</html>`;

      await fs.writeFile(path.join(projectPath, 'index.html'), indexHtml, 'utf-8');

      const mainFile = config.language === 'typescript' 
        ? `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`
        : `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`;

      await fs.writeFile(
        path.join(projectPath, 'src', `main.${config.language === 'typescript' ? 'tsx' : 'jsx'}`),
        mainFile,
        'utf-8'
      );

      const appFile = config.language === 'typescript'
        ? `import React from 'react';

function App(): JSX.Element {
  return (
    <div>
      <h1>Welcome to ${config.name}</h1>
    </div>
  );
}

export default App;`
        : `import React from 'react';

function App() {
  return (
    <div>
      <h1>Welcome to ${config.name}</h1>
    </div>
  );
}

export default App;`;

      await fs.writeFile(
        path.join(projectPath, 'src', `App.${config.language === 'typescript' ? 'tsx' : 'jsx'}`),
        appFile,
        'utf-8'
      );
    }

    // Create configuration files
    if (config.language === 'typescript') {
      const tsConfig = {
        compilerOptions: {
          target: 'ES2020',
          useDefineForClassFields: true,
          lib: ['ES2020', 'DOM', 'DOM.Iterable'],
          module: 'ESNext',
          skipLibCheck: true,
          moduleResolution: 'bundler',
          allowImportingTsExtensions: true,
          resolveJsonModule: true,
          isolatedModules: true,
          noEmit: true,
          jsx: 'react-jsx',
          strict: true,
          noUnusedLocals: true,
          noUnusedParameters: true,
          noFallthroughCasesInSwitch: true
        },
        include: ['src'],
        references: [{ path: './tsconfig.node.json' }]
      };

      await fs.writeFile(
        path.join(projectPath, 'tsconfig.json'),
        JSON.stringify(tsConfig, null, 2),
        'utf-8'
      );
    }
  }

  private async applyTemplateScaffolding(project: Project, template: ProjectTemplate): Promise<void> {
    // Apply template files
    if (template.files) {
      for (const [filePath, content] of Object.entries(template.files)) {
        const targetPath = path.join(project.path, filePath);
        const processedContent = this.processTemplateContent(content, project.config);
        
        await fs.mkdir(path.dirname(targetPath), { recursive: true });
        await fs.writeFile(targetPath, processedContent, 'utf-8');
      }
    }

    // Install dependencies
    if (template.dependencies) {
      await this.updatePackageJson(project, template.dependencies);
    }
  }

  private processTemplateContent(content: string, variables: any): string {
    return content.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match;
    });
  }

  private async updatePackageJson(project: Project, dependencies: Record<string, string>): Promise<void> {
    const packageJsonPath = path.join(project.path, 'package.json');
    
    try {
      const packageJsonContent = await fs.readFile(packageJsonPath, 'utf-8');
      const packageJson = JSON.parse(packageJsonContent);
      
      packageJson.dependencies = { ...packageJson.dependencies, ...dependencies };
      
      await fs.writeFile(
        packageJsonPath,
        JSON.stringify(packageJson, null, 2),
        'utf-8'
      );
    } catch (error) {
      console.error('Failed to update package.json:', error);
    }
  }

  private async updateProjectFiles(project: Project): Promise<void> {
    // Update package.json with new configuration
    const packageJsonPath = path.join(project.path, 'package.json');
    
    try {
      const packageJsonContent = await fs.readFile(packageJsonPath, 'utf-8');
      const packageJson = JSON.parse(packageJsonContent);
      
      packageJson.scripts = project.config.scripts;
      packageJson.dependencies = project.config.dependencies;
      packageJson.devDependencies = project.config.devDependencies;
      
      await fs.writeFile(
        packageJsonPath,
        JSON.stringify(packageJson, null, 2),
        'utf-8'
      );
    } catch (error) {
      console.error('Failed to update project files:', error);
    }
  }

  private async startBuildProcess(project: Project, config: BuildConfiguration, processId: string): Promise<ChildProcess> {
    const buildCommand = config.command || project.config.scripts?.build || 'npm run build';
    const [command, ...args] = buildCommand.split(' ');

    return spawn(command, args, {
      cwd: project.path,
      env: { ...process.env, ...config.environment },
      stdio: ['pipe', 'pipe', 'pipe']
    });
  }

  private async startTestProcess(project: Project, processId: string): Promise<ChildProcess> {
    const testCommand = project.config.scripts?.test || 'npm test';
    const [command, ...args] = testCommand.split(' ');

    return spawn(command, args, {
      cwd: project.path,
      env: process.env,
      stdio: ['pipe', 'pipe', 'pipe']
    });
  }

  private async startDeployProcess(project: Project, config: DeploymentConfiguration, processId: string): Promise<ChildProcess> {
    // Simplified deployment - in reality this would integrate with cloud providers
    const deployCommand = config.command || 'npm run deploy';
    const [command, ...args] = deployCommand.split(' ');

    return spawn(command, args, {
      cwd: project.path,
      env: { ...process.env, ...config.environment },
      stdio: ['pipe', 'pipe', 'pipe']
    });
  }

  private async runTemplateHook(project: Project, hook: string): Promise<void> {
    const [command, ...args] = hook.split(' ');
    
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, {
        cwd: project.path,
        stdio: 'inherit'
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Hook failed with exit code ${code}`));
        }
      });
    });
  }

  private getDefaultBuildConfig(project: Project): BuildConfiguration {
    return {
      command: project.config.scripts?.build || 'npm run build',
      environment: project.config.environment || {},
      outputDir: 'dist',
      minify: true,
      sourceMaps: true
    };
  }

  private initializeBuiltinTemplates(): void {
    // React TypeScript Template
    const reactTypescriptTemplate: ProjectTemplate = {
      id: 'react-typescript',
      name: 'React TypeScript',
      description: 'A modern React application with TypeScript',
      type: 'web',
      config: {
        name: '{{name}}',
        type: 'web',
        framework: 'react',
        language: 'typescript',
        packageManager: 'npm',
        buildTool: 'vite',
        testFramework: 'jest',
        linter: 'eslint',
        formatter: 'prettier'
      },
      dependencies: {
        'react': '^18.2.0',
        'react-dom': '^18.2.0'
      },
      files: {},
      scripts: {
        dev: 'vite',
        build: 'vite build',
        preview: 'vite preview',
        test: 'jest'
      },
      metadata: {
        version: '1.0.0',
        author: 'System',
        tags: ['react', 'typescript', 'vite'],
        category: 'web'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.templates.set('react-typescript', reactTypescriptTemplate);

    // Node.js API Template
    const nodeApiTemplate: ProjectTemplate = {
      id: 'node-api',
      name: 'Node.js API',
      description: 'A RESTful API with Node.js and Express',
      type: 'api',
      config: {
        name: '{{name}}',
        type: 'api',
        framework: 'express',
        language: 'typescript',
        packageManager: 'npm'
      },
      dependencies: {
        'express': '^4.18.0',
        'cors': '^2.8.5'
      },
      files: {},
      scripts: {
        start: 'node dist/index.js',
        dev: 'ts-node src/index.ts',
        build: 'tsc',
        test: 'jest'
      },
      metadata: {
        version: '1.0.0',
        author: 'System',
        tags: ['node', 'api', 'express'],
        category: 'backend'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.templates.set('node-api', nodeApiTemplate);
  }

  private setupProcessCleanup(): void {
    // Clean up processes on exit
    process.on('exit', () => {
      for (const [, processInfo] of this.processes.entries()) {
        processInfo.process.kill('SIGTERM');
      }
    });

    // Clean up long-running processes
    setInterval(() => {
      const now = Date.now();
      const maxAge = 30 * 60 * 1000; // 30 minutes

      for (const [processId, processInfo] of this.processes.entries()) {
        if (now - processInfo.startTime.getTime() > maxAge) {
          this.cancelProcess(processId).catch(console.error);
        }
      }
    }, 5 * 60 * 1000); // Check every 5 minutes
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
}