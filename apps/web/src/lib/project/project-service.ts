import { EventEmitter } from 'events';

// Core Project Types
export interface Project {
  id: string;
  name: string;
  description: string;
  path: string;
  config: ProjectConfiguration;
  status: 'active' | 'archived' | 'deleted';
  createdAt: Date;
  updatedAt: Date;
  lastBuild?: BuildResult;
  lastDeploy?: DeploymentResult;
  health: ProjectHealth;
}

export interface ProjectConfiguration {
  name: string;
  description?: string;
  type: 'web' | 'mobile' | 'desktop' | 'api' | 'library' | 'cli';
  framework: string;
  language: 'javascript' | 'typescript' | 'python' | 'java' | 'go' | 'rust' | 'other';
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'cargo' | 'go mod' | 'other';
  buildTool?: string;
  testFramework?: string;
  linter?: string;
  formatter?: string;
  gitEnabled: boolean;
  dockerEnabled: boolean;
  dependencies: Record<string, string>;
  devDependencies: Record<string, string>;
  scripts: Record<string, string>;
  environment: Record<string, string>;
  ports?: {
    dev?: number;
    preview?: number;
    api?: number;
  };
  plugins?: string[];
  features?: string[];
}

export interface ProjectHealth {
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  lastCheck: Date;
  issues: ProjectIssue[];
  metrics?: {
    buildTime?: number;
    testCoverage?: number;
    codeQuality?: number;
    securityScore?: number;
  };
}

export interface ProjectIssue {
  id: string;
  type: 'error' | 'warning' | 'info';
  category: 'build' | 'test' | 'security' | 'performance' | 'dependency' | 'config';
  message: string;
  file?: string;
  line?: number;
  severity: 'critical' | 'high' | 'medium' | 'low';
  createdAt: Date;
  resolved: boolean;
}

// Template System
export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  type: 'web' | 'mobile' | 'desktop' | 'api' | 'library' | 'cli';
  config: ProjectConfiguration;
  files?: Record<string, string>;
  dependencies?: Record<string, string>;
  scripts?: Record<string, string>;
  hooks?: {
    preScaffold?: string;
    postScaffold?: string;
    preBuild?: string;
    postBuild?: string;
  };
  metadata: {
    version: string;
    author: string;
    tags: string[];
    category: string;
    featured?: boolean;
    downloads?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectScaffoldResult {
  projectId: string;
  templateId: string;
  status: 'running' | 'success' | 'failed';
  startTime: Date;
  endTime?: Date;
  filesCreated: string[];
  errors: string[];
}

// Build System
export interface BuildConfiguration {
  command?: string;
  environment?: Record<string, string>;
  outputDir?: string;
  minify?: boolean;
  sourceMaps?: boolean;
  target?: string;
  optimization?: 'none' | 'basic' | 'aggressive';
  watch?: boolean;
  parallel?: boolean;
}

export interface BuildResult {
  id: string;
  projectId: string;
  status: 'running' | 'success' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  exitCode?: number;
  config: BuildConfiguration;
  output: string[];
  artifacts: BuildArtifact[];
  warnings: string[];
  errors: string[];
  metrics?: {
    buildTime: number;
    bundleSize?: number;
    chunkSizes?: Record<string, number>;
  };
}

export interface BuildArtifact {
  name: string;
  path: string;
  size: number;
  type: 'js' | 'css' | 'html' | 'image' | 'font' | 'other';
  compressed?: boolean;
  hash?: string;
}

// Testing System
export interface TestConfiguration {
  framework: 'jest' | 'vitest' | 'mocha' | 'jasmine' | 'cypress' | 'playwright' | 'other';
  pattern?: string;
  coverage?: boolean;
  watch?: boolean;
  parallel?: boolean;
  timeout?: number;
  environment?: 'node' | 'jsdom' | 'browser';
}

export interface TestResult {
  id: string;
  projectId: string;
  status: 'running' | 'passed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  framework: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  coverage?: TestCoverage;
  output: string[];
  failures: TestFailure[];
  performance?: {
    totalTime: number;
    slowestTests: Array<{
      name: string;
      time: number;
    }>;
  };
}

export interface TestCoverage {
  statements: number;
  branches: number;
  functions: number;
  lines: number;
  uncoveredLines: number[];
  files: Record<string, FileCoverage>;
}

export interface FileCoverage {
  statements: number;
  branches: number;
  functions: number;
  lines: number;
  uncoveredLines: number[];
}

export interface TestFailure {
  test: string;
  message: string;
  stack?: string;
  file?: string;
  line?: number;
}

// Deployment System
export interface DeploymentConfiguration {
  target: 'vercel' | 'netlify' | 'github-pages' | 'aws' | 'docker' | 'custom';
  environment: 'development' | 'staging' | 'production';
  buildCommand?: string;
  outputDir?: string;
  domain?: string;
  environmentVariables?: Record<string, string>;
  command?: string;
}

export interface DeploymentResult {
  id: string;
  projectId: string;
  status: 'running' | 'success' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  config: DeploymentConfiguration;
  url?: string;
  output: string[];
  errors: string[];
  metadata?: {
    buildId?: string;
    version?: string;
    commit?: string;
  };
}

// Workspace Management
export interface WorkspaceSettings {
  defaultTemplate: string;
  autoSave: boolean;
  buildOnSave: boolean;
  defaultBuildTool: string;
  testFramework: string;
  linter: string;
  formatter: string;
  gitIntegration: boolean;
  dockerSupport: boolean;
  theme?: 'light' | 'dark' | 'auto';
  editorSettings?: {
    fontSize: number;
    tabSize: number;
    wordWrap: boolean;
    lineNumbers: boolean;
  };
  shortcuts?: Record<string, string>;
}

export interface Workspace {
  id: string;
  name: string;
  path: string;
  projects: string[];
  settings: WorkspaceSettings;
  createdAt: Date;
  updatedAt: Date;
}

// Development Server
export interface DevServerConfiguration {
  port: number;
  host: string;
  https: boolean;
  proxy?: Record<string, string>;
  cors?: boolean;
  middleware?: string[];
}

export interface DevServerStatus {
  projectId: string;
  status: 'stopped' | 'starting' | 'running' | 'error';
  url?: string;
  port?: number;
  startTime?: Date;
  error?: string;
}

// Code Quality
export interface CodeQualityResult {
  id: string;
  projectId: string;
  timestamp: Date;
  linting: LintingResult;
  formatting: FormattingResult;
  security: SecurityScanResult;
  dependencies: DependencyAuditResult;
  overall: {
    score: number;
    grade: 'A' | 'B' | 'C' | 'D' | 'F';
    issues: number;
  };
}

export interface LintingResult {
  tool: string;
  errors: number;
  warnings: number;
  info: number;
  files: number;
  fixable: number;
  issues: LintIssue[];
}

export interface LintIssue {
  file: string;
  line: number;
  column: number;
  rule: string;
  severity: 'error' | 'warning' | 'info';
  message: string;
  fixable: boolean;
}

export interface FormattingResult {
  tool: string;
  filesChecked: number;
  filesFormatted: number;
  errors: string[];
}

export interface SecurityScanResult {
  tool: string;
  vulnerabilities: SecurityVulnerability[];
  summary: {
    critical: number;
    high: number;
    moderate: number;
    low: number;
    info: number;
  };
}

export interface SecurityVulnerability {
  id: string;
  severity: 'critical' | 'high' | 'moderate' | 'low' | 'info';
  title: string;
  package: string;
  version: string;
  patchedIn?: string;
  vulnerability: string;
  overview: string;
  recommendation: string;
}

export interface DependencyAuditResult {
  outdated: OutdatedDependency[];
  unused: string[];
  duplicates: DuplicateDependency[];
  security: number;
  total: number;
}

export interface OutdatedDependency {
  name: string;
  current: string;
  wanted: string;
  latest: string;
  type: 'dependencies' | 'devDependencies';
}

export interface DuplicateDependency {
  name: string;
  versions: string[];
  paths: string[];
}

// Project Analytics
export interface ProjectAnalytics {
  projectId: string;
  period: 'day' | 'week' | 'month' | 'year';
  builds: {
    total: number;
    successful: number;
    failed: number;
    averageTime: number;
    trends: Array<{
      date: string;
      count: number;
      avgTime: number;
    }>;
  };
  tests: {
    total: number;
    passed: number;
    failed: number;
    coverage: number;
    trends: Array<{
      date: string;
      count: number;
      coverage: number;
    }>;
  };
  deployments: {
    total: number;
    successful: number;
    failed: number;
    trends: Array<{
      date: string;
      count: number;
    }>;
  };
  activity: {
    commits: number;
    contributors: number;
    filesChanged: number;
    linesAdded: number;
    linesRemoved: number;
  };
}

// Project Events
export type ProjectEvent = 
  | ProjectCreatedEvent
  | ProjectUpdatedEvent
  | ProjectDeletedEvent
  | BuildStartedEvent
  | BuildCompletedEvent
  | TestStartedEvent
  | TestCompletedEvent
  | DeployStartedEvent
  | DeployCompletedEvent;

export interface ProjectCreatedEvent {
  type: 'project.created';
  projectId: string;
  timestamp: Date;
  data: Project;
}

export interface ProjectUpdatedEvent {
  type: 'project.updated';
  projectId: string;
  timestamp: Date;
  data: {
    changes: Partial<ProjectConfiguration>;
  };
}

export interface ProjectDeletedEvent {
  type: 'project.deleted';
  projectId: string;
  timestamp: Date;
}

export interface BuildStartedEvent {
  type: 'build.started';
  projectId: string;
  buildId: string;
  timestamp: Date;
}

export interface BuildCompletedEvent {
  type: 'build.completed';
  projectId: string;
  buildId: string;
  timestamp: Date;
  data: BuildResult;
}

export interface TestStartedEvent {
  type: 'test.started';
  projectId: string;
  testId: string;
  timestamp: Date;
}

export interface TestCompletedEvent {
  type: 'test.completed';
  projectId: string;
  testId: string;
  timestamp: Date;
  data: TestResult;
}

export interface DeployStartedEvent {
  type: 'deploy.started';
  projectId: string;
  deployId: string;
  timestamp: Date;
}

export interface DeployCompletedEvent {
  type: 'deploy.completed';
  projectId: string;
  deployId: string;
  timestamp: Date;
  data: DeploymentResult;
}

// Service Classes
export abstract class ProjectService extends EventEmitter {
  abstract createProject(name: string, config: ProjectConfiguration): Promise<Project>;
  abstract getProject(id: string): Promise<Project | null>;
  abstract updateProject(id: string, config: Partial<ProjectConfiguration>): Promise<Project>;
  abstract deleteProject(id: string): Promise<void>;
  abstract listProjects(): Promise<Project[]>;
}

export abstract class BuildService extends EventEmitter {
  abstract buildProject(projectId: string, config?: BuildConfiguration): Promise<BuildResult>;
  abstract getBuildResult(buildId: string): Promise<BuildResult | null>;
  abstract cancelBuild(buildId: string): Promise<void>;
  abstract watchBuild(buildId: string, callback: (result: BuildResult) => void): void;
}

export abstract class TestService extends EventEmitter {
  abstract runTests(projectId: string, config?: TestConfiguration): Promise<TestResult>;
  abstract getTestResult(testId: string): Promise<TestResult | null>;
  abstract cancelTest(testId: string): Promise<void>;
  abstract watchTest(testId: string, callback: (result: TestResult) => void): void;
}

export abstract class DeploymentService extends EventEmitter {
  abstract deployProject(projectId: string, config: DeploymentConfiguration): Promise<DeploymentResult>;
  abstract getDeploymentResult(deployId: string): Promise<DeploymentResult | null>;
  abstract cancelDeployment(deployId: string): Promise<void>;
  abstract rollbackDeployment(deployId: string): Promise<DeploymentResult>;
}