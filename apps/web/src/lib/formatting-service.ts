'use client';

import * as monaco from 'monaco-editor';
import { EditorConfiguration } from '@/types/editor';

export interface FormatResult {
  success: boolean;
  formattedCode?: string;
  error?: string;
}

export interface LintResult {
  success: boolean;
  issues: LintIssue[];
}

export interface LintIssue {
  line: number;
  column: number;
  severity: 'error' | 'warning' | 'info' | 'hint';
  message: string;
  ruleId?: string;
  source?: string;
}

export interface FormattingOptions {
  tabSize: number;
  insertSpaces: boolean;
  detectIndentation: boolean;
  trimTrailingWhitespace: boolean;
  insertFinalNewline: boolean;
  trimFinalNewlines: boolean;
  keepLineBreaks: boolean;
  maxPreserveNewLines: number;
  wrapLineLength: number;
  semi: boolean;
  singleQuote: boolean;
  trailingComma: 'none' | 'es5' | 'all';
  bracketSpacing: boolean;
  arrowParens: 'avoid' | 'always';
  endOfLine: 'lf' | 'crlf' | 'cr' | 'auto';
  printWidth: number;
  useTabs: boolean;
}

export class CodeFormatter {
  private static defaultOptions: FormattingOptions = {
    tabSize: 2,
    insertSpaces: true,
    detectIndentation: true,
    trimTrailingWhitespace: true,
    insertFinalNewline: true,
    trimFinalNewlines: true,
    keepLineBreaks: true,
    maxPreserveNewLines: 2,
    wrapLineLength: 80,
    semi: true,
    singleQuote: false,
    trailingComma: 'es5',
    bracketSpacing: true,
    arrowParens: 'avoid',
    endOfLine: 'lf',
    printWidth: 80,
    useTabs: false,
  };

  static async formatCode(
    code: string,
    languageId: string,
    options: Partial<FormattingOptions> = {}
  ): Promise<FormatResult> {
    const formatOptions = { ...CodeFormatter.defaultOptions, ...options };
    
    try {
      switch (languageId) {
        case 'javascript':
        case 'typescript':
        case 'javascriptreact':
        case 'typescriptreact':
          return await CodeFormatter.formatJavaScript(code, formatOptions);
        
        case 'python':
          return await CodeFormatter.formatPython(code, formatOptions);
        
        case 'html':
          return await CodeFormatter.formatHTML(code, formatOptions);
        
        case 'css':
        case 'scss':
        case 'less':
          return await CodeFormatter.formatCSS(code, formatOptions);
        
        case 'json':
          return await CodeFormatter.formatJSON(code, formatOptions);
        
        case 'markdown':
          return await CodeFormatter.formatMarkdown(code, formatOptions);
        
        default:
          return await CodeFormatter.formatGeneric(code, formatOptions);
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown formatting error',
      };
    }
  }

  private static async formatJavaScript(
    code: string,
    options: FormattingOptions
  ): Promise<FormatResult> {
    try {
      // Basic JavaScript formatting
      let formatted = code;
      
      // Normalize line endings
      formatted = formatted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      
      // Apply indentation
      formatted = CodeFormatter.applyIndentation(formatted, options, [
        { open: '{', close: '}' },
        { open: '[', close: ']' },
        { open: '(', close: ')' },
      ]);
      
      // Add semicolons if required
      if (options.semi) {
        formatted = CodeFormatter.addSemicolons(formatted);
      }
      
      // Apply quote style
      formatted = CodeFormatter.normalizeQuotes(formatted, options.singleQuote);
      
      // Format spacing
      formatted = CodeFormatter.normalizeSpacing(formatted);
      
      // Handle trailing commas
      formatted = CodeFormatter.handleTrailingCommas(formatted, options.trailingComma);
      
      // Apply final formatting rules
      formatted = CodeFormatter.applyFinalFormatting(formatted, options);
      
      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'JavaScript formatting error',
      };
    }
  }

  private static async formatPython(
    code: string,
    options: FormattingOptions
  ): Promise<FormatResult> {
    try {
      let formatted = code;
      
      // Normalize line endings
      formatted = formatted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      
      // Apply Python-specific indentation (4 spaces by default)
      const pythonOptions = { ...options, tabSize: 4 };
      formatted = CodeFormatter.applyPythonIndentation(formatted, pythonOptions);
      
      // Remove trailing whitespace
      formatted = formatted.replace(/[ \t]+$/gm, '');
      
      // Normalize imports
      formatted = CodeFormatter.normalizePythonImports(formatted);
      
      // Apply PEP 8 spacing rules
      formatted = CodeFormatter.applyPEP8Spacing(formatted);
      
      // Apply final formatting rules
      formatted = CodeFormatter.applyFinalFormatting(formatted, options);
      
      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Python formatting error',
      };
    }
  }

  private static async formatHTML(
    code: string,
    options: FormattingOptions
  ): Promise<FormatResult> {
    try {
      let formatted = code;
      
      // Normalize line endings
      formatted = formatted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      
      // Apply HTML indentation
      formatted = CodeFormatter.applyHTMLIndentation(formatted, options);
      
      // Normalize attributes
      formatted = CodeFormatter.normalizeHTMLAttributes(formatted);
      
      // Apply final formatting rules
      formatted = CodeFormatter.applyFinalFormatting(formatted, options);
      
      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'HTML formatting error',
      };
    }
  }

  private static async formatCSS(
    code: string,
    options: FormattingOptions
  ): Promise<FormatResult> {
    try {
      let formatted = code;
      
      // Normalize line endings
      formatted = formatted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      
      // Apply CSS indentation
      formatted = CodeFormatter.applyCSSIndentation(formatted, options);
      
      // Normalize property formatting
      formatted = CodeFormatter.normalizeCSSProperties(formatted);
      
      // Apply final formatting rules
      formatted = CodeFormatter.applyFinalFormatting(formatted, options);
      
      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'CSS formatting error',
      };
    }
  }

  private static async formatJSON(
    code: string,
    options: FormattingOptions
  ): Promise<FormatResult> {
    try {
      const parsed = JSON.parse(code);
      const indentString = options.insertSpaces ? ' '.repeat(options.tabSize) : '\t';
      const formatted = JSON.stringify(parsed, null, indentString);
      
      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'JSON formatting error',
      };
    }
  }

  private static async formatMarkdown(
    code: string,
    options: FormattingOptions
  ): Promise<FormatResult> {
    try {
      let formatted = code;
      
      // Normalize line endings
      formatted = formatted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      
      // Normalize list formatting
      formatted = CodeFormatter.normalizeMarkdownLists(formatted);
      
      // Apply final formatting rules
      formatted = CodeFormatter.applyFinalFormatting(formatted, options);
      
      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Markdown formatting error',
      };
    }
  }

  private static async formatGeneric(
    code: string,
    options: FormattingOptions
  ): Promise<FormatResult> {
    try {
      let formatted = code;
      
      // Apply basic formatting
      formatted = CodeFormatter.applyFinalFormatting(formatted, options);
      
      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Generic formatting error',
      };
    }
  }

  private static applyIndentation(
    code: string,
    options: FormattingOptions,
    brackets: { open: string; close: string }[]
  ): string {
    const lines = code.split('\n');
    const indentString = options.insertSpaces ? ' '.repeat(options.tabSize) : '\t';
    let indentLevel = 0;
    
    return lines.map(line => {
      const trimmed = line.trim();
      
      // Check for closing brackets
      for (const bracket of brackets) {
        if (trimmed.startsWith(bracket.close)) {
          indentLevel = Math.max(0, indentLevel - 1);
          break;
        }
      }
      
      const indentedLine = trimmed ? indentString.repeat(indentLevel) + trimmed : '';
      
      // Check for opening brackets
      for (const bracket of brackets) {
        if (trimmed.endsWith(bracket.open)) {
          indentLevel++;
          break;
        }
      }
      
      return indentedLine;
    }).join('\n');
  }

  private static applyPythonIndentation(code: string, options: FormattingOptions): string {
    const lines = code.split('\n');
    const indentString = options.insertSpaces ? ' '.repeat(options.tabSize) : '\t';
    let indentLevel = 0;
    
    return lines.map(line => {
      const trimmed = line.trim();
      
      // Check for dedent keywords
      if (trimmed.startsWith('elif ') || trimmed.startsWith('else:') || 
          trimmed.startsWith('except') || trimmed.startsWith('finally:')) {
        indentLevel = Math.max(0, indentLevel - 1);
      }
      
      const indentedLine = trimmed ? indentString.repeat(indentLevel) + trimmed : '';
      
      // Check for indent keywords
      if (trimmed.endsWith(':') && 
          (trimmed.startsWith('if ') || trimmed.startsWith('elif ') || 
           trimmed.startsWith('else:') || trimmed.startsWith('for ') || 
           trimmed.startsWith('while ') || trimmed.startsWith('def ') || 
           trimmed.startsWith('class ') || trimmed.startsWith('try:') || 
           trimmed.startsWith('except') || trimmed.startsWith('finally:') || 
           trimmed.startsWith('with '))) {
        indentLevel++;
      }
      
      return indentedLine;
    }).join('\n');
  }

  private static applyHTMLIndentation(code: string, options: FormattingOptions): string {
    const lines = code.split('\n');
    const indentString = options.insertSpaces ? ' '.repeat(options.tabSize) : '\t';
    let indentLevel = 0;
    
    return lines.map(line => {
      const trimmed = line.trim();
      
      // Check for closing tags
      if (trimmed.startsWith('</') && !trimmed.includes('/>')) {
        indentLevel = Math.max(0, indentLevel - 1);
      }
      
      const indentedLine = trimmed ? indentString.repeat(indentLevel) + trimmed : '';
      
      // Check for opening tags
      if (trimmed.startsWith('<') && !trimmed.startsWith('</') && 
          !trimmed.includes('/>') && !trimmed.includes('<!')) {
        indentLevel++;
      }
      
      return indentedLine;
    }).join('\n');
  }

  private static applyCSSIndentation(code: string, options: FormattingOptions): string {
    const lines = code.split('\n');
    const indentString = options.insertSpaces ? ' '.repeat(options.tabSize) : '\t';
    let indentLevel = 0;
    
    return lines.map(line => {
      const trimmed = line.trim();
      
      // Check for closing brackets
      if (trimmed.startsWith('}')) {
        indentLevel = Math.max(0, indentLevel - 1);
      }
      
      const indentedLine = trimmed ? indentString.repeat(indentLevel) + trimmed : '';
      
      // Check for opening brackets
      if (trimmed.endsWith('{')) {
        indentLevel++;
      }
      
      return indentedLine;
    }).join('\n');
  }

  private static addSemicolons(code: string): string {
    return code.replace(/([^;{}\s])\s*\n/g, '$1;\n');
  }

  private static normalizeQuotes(code: string, singleQuote: boolean): string {
    const target = singleQuote ? "'" : '"';
    const source = singleQuote ? '"' : "'";
    
    return code.replace(new RegExp(`${source}([^${source}]*)${source}`, 'g'), `${target}$1${target}`);
  }

  private static normalizeSpacing(code: string): string {
    return code
      .replace(/\s*{\s*/g, ' { ')
      .replace(/\s*}\s*/g, ' }')
      .replace(/\s*\(\s*/g, '(')
      .replace(/\s*\)\s*/g, ')')
      .replace(/\s*,\s*/g, ', ')
      .replace(/\s*:\s*/g, ': ')
      .replace(/\s*;\s*/g, '; ')
      .replace(/\s*=\s*/g, ' = ')
      .replace(/\s*\+\s*/g, ' + ')
      .replace(/\s*-\s*/g, ' - ')
      .replace(/\s*\*\s*/g, ' * ')
      .replace(/\s*\/\s*/g, ' / ')
      .replace(/\s{2,}/g, ' ');
  }

  private static handleTrailingCommas(code: string, trailingComma: 'none' | 'es5' | 'all'): string {
    if (trailingComma === 'none') {
      return code.replace(/,(\s*[}\]])/g, '$1');
    }
    // For 'es5' and 'all', we would need more sophisticated parsing
    return code;
  }

  private static normalizePythonImports(code: string): string {
    const lines = code.split('\n');
    const importLines: string[] = [];
    const otherLines: string[] = [];
    
    lines.forEach(line => {
      if (line.trim().startsWith('import ') || line.trim().startsWith('from ')) {
        importLines.push(line);
      } else {
        otherLines.push(line);
      }
    });
    
    // Sort imports
    importLines.sort();
    
    return [...importLines, '', ...otherLines].join('\n');
  }

  private static applyPEP8Spacing(code: string): string {
    return code
      .replace(/\s*=\s*/g, ' = ')
      .replace(/\s*\+\s*/g, ' + ')
      .replace(/\s*-\s*/g, ' - ')
      .replace(/\s*\*\s*/g, ' * ')
      .replace(/\s*\/\s*/g, ' / ')
      .replace(/\s*,\s*/g, ', ')
      .replace(/\s*:\s*/g, ': ')
      .replace(/\s{2,}/g, ' ');
  }

  private static normalizeHTMLAttributes(code: string): string {
    return code.replace(/\s*=\s*/g, '=');
  }

  private static normalizeCSSProperties(code: string): string {
    return code.replace(/\s*:\s*/g, ': ').replace(/\s*;\s*/g, '; ');
  }

  private static normalizeMarkdownLists(code: string): string {
    return code
      .replace(/^\s*[\*\-\+]\s+/gm, '- ')
      .replace(/^\s*\d+\.\s+/gm, '1. ');
  }

  private static applyFinalFormatting(code: string, options: FormattingOptions): string {
    let formatted = code;
    
    // Normalize line endings
    if (options.endOfLine === 'crlf') {
      formatted = formatted.replace(/\n/g, '\r\n');
    } else if (options.endOfLine === 'cr') {
      formatted = formatted.replace(/\n/g, '\r');
    }
    
    // Trim trailing whitespace
    if (options.trimTrailingWhitespace) {
      formatted = formatted.replace(/[ \t]+$/gm, '');
    }
    
    // Limit consecutive newlines
    if (options.maxPreserveNewLines > 0) {
      const maxNewlines = '\n'.repeat(options.maxPreserveNewLines + 1);
      formatted = formatted.replace(/\n{3,}/g, maxNewlines);
    }
    
    // Insert final newline
    if (options.insertFinalNewline && !formatted.endsWith('\n')) {
      formatted += '\n';
    }
    
    // Trim final newlines
    if (options.trimFinalNewlines) {
      formatted = formatted.replace(/\n+$/, '\n');
    }
    
    return formatted;
  }
}

export class CodeLinter {
  static async lintCode(code: string, languageId: string): Promise<LintResult> {
    const issues: LintIssue[] = [];
    
    try {
      switch (languageId) {
        case 'javascript':
        case 'typescript':
          issues.push(...await CodeLinter.lintJavaScript(code));
          break;
        
        case 'python':
          issues.push(...await CodeLinter.lintPython(code));
          break;
        
        case 'html':
          issues.push(...await CodeLinter.lintHTML(code));
          break;
        
        case 'css':
          issues.push(...await CodeLinter.lintCSS(code));
          break;
        
        case 'json':
          issues.push(...await CodeLinter.lintJSON(code));
          break;
        
        default:
          issues.push(...await CodeLinter.lintGeneric(code));
          break;
      }
      
      return {
        success: true,
        issues,
      };
    } catch (error) {
      return {
        success: false,
        issues: [{
          line: 1,
          column: 1,
          severity: 'error',
          message: error instanceof Error ? error.message : 'Unknown linting error',
        }],
      };
    }
  }

  private static async lintJavaScript(code: string): Promise<LintIssue[]> {
    const issues: LintIssue[] = [];
    const lines = code.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmed = line.trim();
      
      // Check for console.log
      if (trimmed.includes('console.log')) {
        issues.push({
          line: lineNumber,
          column: line.indexOf('console.log') + 1,
          severity: 'warning',
          message: 'Unexpected console statement',
          ruleId: 'no-console',
          source: 'eslint',
        });
      }
      
      // Check for missing semicolons
      if (trimmed && !trimmed.endsWith(';') && !trimmed.endsWith('{') && !trimmed.endsWith('}')) {
        issues.push({
          line: lineNumber,
          column: line.length,
          severity: 'warning',
          message: 'Missing semicolon',
          ruleId: 'semi',
          source: 'eslint',
        });
      }
      
      // Check for unused variables
      if (trimmed.startsWith('var ') || trimmed.startsWith('let ') || trimmed.startsWith('const ')) {
        const varMatch = trimmed.match(/^(var|let|const)\s+(\w+)/);
        if (varMatch && !code.includes(varMatch[2] + ' ')) {
          issues.push({
            line: lineNumber,
            column: line.indexOf(varMatch[2]) + 1,
            severity: 'warning',
            message: `'${varMatch[2]}' is defined but never used`,
            ruleId: 'no-unused-vars',
            source: 'eslint',
          });
        }
      }
      
      // Check for double equality
      if (trimmed.includes('==') && !trimmed.includes('===')) {
        issues.push({
          line: lineNumber,
          column: line.indexOf('==') + 1,
          severity: 'warning',
          message: 'Expected === and instead saw ==',
          ruleId: 'eqeqeq',
          source: 'eslint',
        });
      }
    });
    
    return issues;
  }

  private static async lintPython(code: string): Promise<LintIssue[]> {
    const issues: LintIssue[] = [];
    const lines = code.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmed = line.trim();
      
      // Check for print statements without parentheses
      if (trimmed.includes('print ') && !trimmed.includes('print(')) {
        issues.push({
          line: lineNumber,
          column: line.indexOf('print') + 1,
          severity: 'error',
          message: 'print is a function in Python 3',
          ruleId: 'print-function',
          source: 'flake8',
        });
      }
      
      // Check for missing colons
      if (trimmed.startsWith(('if ', 'for ', 'while ', 'def ', 'class ', 'try:', 'except', 'finally:', 'with ')) && !trimmed.includes(':')) {
        issues.push({
          line: lineNumber,
          column: line.length,
          severity: 'error',
          message: 'Missing colon',
          ruleId: 'syntax-error',
          source: 'flake8',
        });
      }
      
      // Check line length
      if (line.length > 79) {
        issues.push({
          line: lineNumber,
          column: 80,
          severity: 'warning',
          message: 'Line too long (79 characters)',
          ruleId: 'E501',
          source: 'flake8',
        });
      }
      
      // Check for unused imports
      if (trimmed.startsWith('import ') || trimmed.startsWith('from ')) {
        const importMatch = trimmed.match(/import\s+(\w+)/);
        if (importMatch && !code.includes(importMatch[1] + '.')) {
          issues.push({
            line: lineNumber,
            column: line.indexOf(importMatch[1]) + 1,
            severity: 'warning',
            message: `'${importMatch[1]}' imported but unused`,
            ruleId: 'F401',
            source: 'flake8',
          });
        }
      }
    });
    
    return issues;
  }

  private static async lintHTML(code: string): Promise<LintIssue[]> {
    const issues: LintIssue[] = [];
    const lines = code.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmed = line.trim();
      
      // Check for missing closing tags
      const openTags = trimmed.match(/<(\w+)[^>]*>/g);
      const closeTags = trimmed.match(/<\/(\w+)>/g);
      
      if (openTags && closeTags && openTags.length !== closeTags.length) {
        issues.push({
          line: lineNumber,
          column: 1,
          severity: 'error',
          message: 'Unclosed HTML tag',
          ruleId: 'tag-pair',
          source: 'htmlhint',
        });
      }
      
      // Check for inline styles
      if (trimmed.includes('style=')) {
        issues.push({
          line: lineNumber,
          column: line.indexOf('style=') + 1,
          severity: 'warning',
          message: 'Inline style should be avoided',
          ruleId: 'inline-style-disabled',
          source: 'htmlhint',
        });
      }
    });
    
    return issues;
  }

  private static async lintCSS(code: string): Promise<LintIssue[]> {
    const issues: LintIssue[] = [];
    const lines = code.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmed = line.trim();
      
      // Check for missing semicolons
      if (trimmed.includes(':') && !trimmed.endsWith(';') && !trimmed.endsWith('{') && !trimmed.endsWith('}')) {
        issues.push({
          line: lineNumber,
          column: line.length,
          severity: 'error',
          message: 'Missing semicolon',
          ruleId: 'declaration-block-semicolon-newline-after',
          source: 'stylelint',
        });
      }
      
      // Check for unknown properties
      if (trimmed.includes(':') && !trimmed.includes('/*') && !trimmed.includes('*/')) {
        const property = trimmed.split(':')[0].trim();
        if (property && !CodeLinter.isValidCSSProperty(property)) {
          issues.push({
            line: lineNumber,
            column: 1,
            severity: 'error',
            message: `Unknown property '${property}'`,
            ruleId: 'property-no-unknown',
            source: 'stylelint',
          });
        }
      }
    });
    
    return issues;
  }

  private static async lintJSON(code: string): Promise<LintIssue[]> {
    const issues: LintIssue[] = [];
    
    try {
      JSON.parse(code);
    } catch (error) {
      if (error instanceof SyntaxError) {
        issues.push({
          line: 1,
          column: 1,
          severity: 'error',
          message: error.message,
          ruleId: 'syntax-error',
          source: 'json',
        });
      }
    }
    
    return issues;
  }

  private static async lintGeneric(code: string): Promise<LintIssue[]> {
    const issues: LintIssue[] = [];
    const lines = code.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Check for trailing whitespace
      if (line.endsWith(' ') || line.endsWith('\t')) {
        issues.push({
          line: lineNumber,
          column: line.length,
          severity: 'info',
          message: 'Trailing whitespace',
          ruleId: 'trailing-whitespace',
          source: 'generic',
        });
      }
      
      // Check for mixed tabs and spaces
      if (line.includes('\t') && line.includes('  ')) {
        issues.push({
          line: lineNumber,
          column: 1,
          severity: 'warning',
          message: 'Mixed tabs and spaces',
          ruleId: 'mixed-indentation',
          source: 'generic',
        });
      }
    });
    
    return issues;
  }

  private static isValidCSSProperty(property: string): boolean {
    const validProperties = [
      'align-content', 'align-items', 'align-self', 'animation', 'animation-delay',
      'animation-direction', 'animation-duration', 'animation-fill-mode',
      'animation-iteration-count', 'animation-name', 'animation-play-state',
      'animation-timing-function', 'background', 'background-attachment',
      'background-clip', 'background-color', 'background-image', 'background-origin',
      'background-position', 'background-repeat', 'background-size', 'border',
      'border-bottom', 'border-bottom-color', 'border-bottom-left-radius',
      'border-bottom-right-radius', 'border-bottom-style', 'border-bottom-width',
      'border-collapse', 'border-color', 'border-left', 'border-left-color',
      'border-left-style', 'border-left-width', 'border-radius', 'border-right',
      'border-right-color', 'border-right-style', 'border-right-width',
      'border-spacing', 'border-style', 'border-top', 'border-top-color',
      'border-top-left-radius', 'border-top-right-radius', 'border-top-style',
      'border-top-width', 'border-width', 'bottom', 'box-shadow', 'box-sizing',
      'caption-side', 'clear', 'clip', 'color', 'content', 'counter-increment',
      'counter-reset', 'cursor', 'direction', 'display', 'empty-cells', 'float',
      'font', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch',
      'font-style', 'font-variant', 'font-weight', 'height', 'justify-content',
      'left', 'letter-spacing', 'line-height', 'list-style', 'list-style-image',
      'list-style-position', 'list-style-type', 'margin', 'margin-bottom',
      'margin-left', 'margin-right', 'margin-top', 'max-height', 'max-width',
      'min-height', 'min-width', 'opacity', 'order', 'outline', 'outline-color',
      'outline-style', 'outline-width', 'overflow', 'overflow-x', 'overflow-y',
      'padding', 'padding-bottom', 'padding-left', 'padding-right', 'padding-top',
      'page-break-after', 'page-break-before', 'page-break-inside', 'position',
      'right', 'table-layout', 'text-align', 'text-decoration', 'text-indent',
      'text-transform', 'top', 'transform', 'transition', 'unicode-bidi',
      'vertical-align', 'visibility', 'white-space', 'width', 'word-spacing',
      'z-index'
    ];
    
    return validProperties.includes(property) || property.startsWith('-webkit-') || 
           property.startsWith('-moz-') || property.startsWith('-ms-') || 
           property.startsWith('-o-');
  }
}

export class FormattingService {
  static async formatDocument(
    model: monaco.editor.ITextModel,
    options: monaco.languages.FormattingOptions,
    config: EditorConfiguration
  ): Promise<monaco.editor.ITextEdit[]> {
    const code = model.getValue();
    const languageId = model.getLanguageId();
    
    const formatOptions: Partial<FormattingOptions> = {
      tabSize: options.tabSize,
      insertSpaces: options.insertSpaces,
      trimTrailingWhitespace: config.trimTrailingWhitespace,
      insertFinalNewline: config.insertFinalNewline,
      printWidth: config.printWidth || 80,
      semi: config.semi !== false,
      singleQuote: config.singleQuote === true,
      trailingComma: config.trailingComma || 'es5',
      bracketSpacing: config.bracketSpacing !== false,
      arrowParens: config.arrowParens || 'avoid',
    };
    
    const result = await CodeFormatter.formatCode(code, languageId, formatOptions);
    
    if (result.success && result.formattedCode) {
      return [{
        range: model.getFullModelRange(),
        text: result.formattedCode,
      }];
    }
    
    return [];
  }

  static async lintDocument(model: monaco.editor.ITextModel): Promise<monaco.editor.IMarkerData[]> {
    const code = model.getValue();
    const languageId = model.getLanguageId();
    
    const result = await CodeLinter.lintCode(code, languageId);
    
    if (result.success) {
      return result.issues.map(issue => ({
        severity: this.convertSeverity(issue.severity),
        message: issue.message,
        startLineNumber: issue.line,
        startColumn: issue.column,
        endLineNumber: issue.line,
        endColumn: issue.column + 1,
        source: issue.source,
        code: issue.ruleId,
      }));
    }
    
    return [];
  }

  private static convertSeverity(severity: string): monaco.MarkerSeverity {
    switch (severity) {
      case 'error':
        return monaco.MarkerSeverity.Error;
      case 'warning':
        return monaco.MarkerSeverity.Warning;
      case 'info':
        return monaco.MarkerSeverity.Info;
      case 'hint':
        return monaco.MarkerSeverity.Hint;
      default:
        return monaco.MarkerSeverity.Info;
    }
  }
}

export default FormattingService;