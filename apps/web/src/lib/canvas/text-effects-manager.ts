import { fabric } from 'fabric';
import { EventEmitter } from 'events';
import { FilterPipelineEngine, FilterInstance } from './filter-pipeline-engine';

// Text effects types and interfaces
export interface TextEffect {
  id: string;
  name: string;
  type: TextEffectType;
  category: TextEffectCategory;
  description: string;
  parameters: TextEffectParameters;
  enabled: boolean;
  intensity: number;
  blendMode: BlendMode;
  opacity: number;
  order: number;
  animatable: boolean;
  realTimePreview: boolean;
  gpuAccelerated: boolean;
  presets: TextEffectPreset[];
}

export type TextEffectType = 
  | 'shadow' | 'glow' | 'outline' | 'gradient' | 'texture' | 'distortion' 
  | 'blur' | 'emboss' | 'bevel' | 'extrude' | 'warp' | 'transform'
  | 'noise' | 'halftone' | 'pixelate' | 'posterize' | 'threshold'
  | 'chromatic' | 'vintage' | 'neon' | 'metallic' | 'glass';

export type TextEffectCategory = 
  | 'lighting' | 'dimensional' | 'artistic' | 'retro' | 'modern' 
  | 'organic' | 'geometric' | 'cinematic' | 'decorative' | 'functional';

export type BlendMode = 
  | 'normal' | 'multiply' | 'screen' | 'overlay' | 'soft-light' | 'hard-light'
  | 'color-dodge' | 'color-burn' | 'darken' | 'lighten' | 'difference' 
  | 'exclusion' | 'hue' | 'saturation' | 'color' | 'luminosity';

export interface TextEffectParameters {
  // Shadow parameters
  shadowColor?: string;
  shadowBlur?: number;
  shadowOffsetX?: number;
  shadowOffsetY?: number;
  shadowSpread?: number;
  shadowInner?: boolean;
  
  // Glow parameters
  glowColor?: string;
  glowSize?: number;
  glowIntensity?: number;
  glowQuality?: number;
  glowInner?: boolean;
  
  // Outline parameters
  outlineColor?: string;
  outlineWidth?: number;
  outlinePosition?: 'inside' | 'center' | 'outside';
  outlineStyle?: 'solid' | 'dashed' | 'dotted';
  outlineDashArray?: number[];
  
  // Gradient parameters
  gradientType?: 'linear' | 'radial' | 'conic' | 'diamond';
  gradientStops?: GradientStop[];
  gradientAngle?: number;
  gradientCenter?: { x: number; y: number };
  gradientRadius?: number;
  
  // Texture parameters
  textureUrl?: string;
  textureScale?: number;
  textureOpacity?: number;
  textureBlendMode?: BlendMode;
  textureOffset?: { x: number; y: number };
  textureRepeat?: 'repeat' | 'repeat-x' | 'repeat-y' | 'no-repeat';
  
  // Distortion parameters
  distortionType?: 'wave' | 'ripple' | 'twist' | 'pinch' | 'bulge';
  distortionStrength?: number;
  distortionFrequency?: number;
  distortionPhase?: number;
  
  // Blur parameters
  blurType?: 'gaussian' | 'motion' | 'radial' | 'box';
  blurRadius?: number;
  blurAngle?: number;
  blurQuality?: number;
  
  // Transform parameters
  transformMatrix?: number[];
  transformOrigin?: { x: number; y: number };
  transformPerspective?: number;
  
  // Color parameters
  brightness?: number;
  contrast?: number;
  saturation?: number;
  hue?: number;
  gamma?: number;
  
  // Noise parameters
  noiseType?: 'uniform' | 'gaussian' | 'perlin' | 'turbulence';
  noiseAmount?: number;
  noiseScale?: number;
  noiseOctaves?: number;
  
  // Animation parameters
  animationDuration?: number;
  animationDelay?: number;
  animationEasing?: string;
  animationLoop?: boolean;
  animationDirection?: 'normal' | 'reverse' | 'alternate';
}

export interface GradientStop {
  offset: number;
  color: string;
  opacity?: number;
}

export interface TextEffectPreset {
  id: string;
  name: string;
  description: string;
  parameters: TextEffectParameters;
  thumbnail?: string;
  tags: string[];
  category: string;
  popularity: number;
}

export interface TextEffectInstance {
  id: string;
  effectId: string;
  textObjectId: string;
  parameters: TextEffectParameters;
  enabled: boolean;
  intensity: number;
  blendMode: BlendMode;
  opacity: number;
  order: number;
  cachedResult?: HTMLCanvasElement;
  renderTime?: number;
  gpuAccelerated?: boolean;
}

export interface TextEffectGroup {
  id: string;
  name: string;
  description: string;
  effectIds: string[];
  enabled: boolean;
  opacity: number;
  blendMode: BlendMode;
  collapsed?: boolean;
  locked?: boolean;
}

export interface TextEffectAnimation {
  id: string;
  effectInstanceId: string;
  property: string;
  keyframes: AnimationKeyframe[];
  duration: number;
  delay: number;
  easing: string;
  loop: boolean;
  direction: 'normal' | 'reverse' | 'alternate';
  playState: 'running' | 'paused' | 'finished';
}

export interface AnimationKeyframe {
  time: number;
  value: any;
  easing?: string;
}

export interface TextEffectRenderOptions {
  quality: 'low' | 'medium' | 'high' | 'ultra';
  enableGPU: boolean;
  enableCaching: boolean;
  enableRealTimePreview: boolean;
  maxTextureSize: number;
  antialias: boolean;
  preserveAlpha: boolean;
  outputFormat: 'canvas' | 'imageData' | 'texture';
}

export interface TextEffectPerformance {
  totalRenderTime: number;
  averageRenderTime: number;
  cacheHitRate: number;
  gpuAccelerated: number;
  memoryUsage: number;
  textureMemory: number;
  effectsCount: number;
  animationsCount: number;
}

// Text effect shader templates
export const TextEffectShaders = {
  shadow: {
    vertex: `
      attribute vec2 a_position;
      attribute vec2 a_texCoord;
      uniform mat3 u_matrix;
      varying vec2 v_texCoord;
      
      void main() {
        vec3 position = u_matrix * vec3(a_position, 1.0);
        gl_Position = vec4(position.xy, 0.0, 1.0);
        v_texCoord = a_texCoord;
      }
    `,
    fragment: `
      precision mediump float;
      uniform sampler2D u_texture;
      uniform vec4 u_shadowColor;
      uniform vec2 u_shadowOffset;
      uniform float u_shadowBlur;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec4 textColor = texture2D(u_texture, v_texCoord);
        vec4 shadowColor = texture2D(u_texture, v_texCoord + u_shadowOffset);
        
        // Apply shadow blur (simplified)
        vec4 blurredShadow = shadowColor;
        for (float i = -2.0; i <= 2.0; i++) {
          for (float j = -2.0; j <= 2.0; j++) {
            blurredShadow += texture2D(u_texture, v_texCoord + u_shadowOffset + vec2(i, j) * u_shadowBlur);
          }
        }
        blurredShadow /= 25.0;
        
        vec4 finalColor = mix(blurredShadow * u_shadowColor, textColor, textColor.a);
        gl_FragColor = vec4(finalColor.rgb, finalColor.a * u_opacity);
      }
    `
  },
  
  glow: {
    vertex: `
      attribute vec2 a_position;
      attribute vec2 a_texCoord;
      uniform mat3 u_matrix;
      varying vec2 v_texCoord;
      
      void main() {
        vec3 position = u_matrix * vec3(a_position, 1.0);
        gl_Position = vec4(position.xy, 0.0, 1.0);
        v_texCoord = a_texCoord;
      }
    `,
    fragment: `
      precision mediump float;
      uniform sampler2D u_texture;
      uniform vec4 u_glowColor;
      uniform float u_glowSize;
      uniform float u_glowIntensity;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec4 textColor = texture2D(u_texture, v_texCoord);
        vec4 glow = vec4(0.0);
        
        // Sample surrounding pixels for glow
        for (float i = -u_glowSize; i <= u_glowSize; i++) {
          for (float j = -u_glowSize; j <= u_glowSize; j++) {
            vec2 offset = vec2(i, j) / 512.0;
            vec4 sample = texture2D(u_texture, v_texCoord + offset);
            float distance = length(offset);
            float weight = exp(-distance * distance / (u_glowSize * u_glowSize));
            glow += sample * weight;
          }
        }
        
        glow *= u_glowColor * u_glowIntensity;
        vec4 finalColor = mix(glow, textColor, textColor.a);
        gl_FragColor = vec4(finalColor.rgb, finalColor.a * u_opacity);
      }
    `
  },
  
  gradient: {
    vertex: `
      attribute vec2 a_position;
      attribute vec2 a_texCoord;
      uniform mat3 u_matrix;
      varying vec2 v_texCoord;
      
      void main() {
        vec3 position = u_matrix * vec3(a_position, 1.0);
        gl_Position = vec4(position.xy, 0.0, 1.0);
        v_texCoord = a_texCoord;
      }
    `,
    fragment: `
      precision mediump float;
      uniform sampler2D u_texture;
      uniform vec4 u_gradientColors[8];
      uniform float u_gradientStops[8];
      uniform int u_gradientStopsCount;
      uniform vec2 u_gradientStart;
      uniform vec2 u_gradientEnd;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      vec4 sampleGradient(float t) {
        t = clamp(t, 0.0, 1.0);
        
        for (int i = 0; i < 7; i++) {
          if (i >= u_gradientStopsCount - 1) break;
          
          float stop1 = u_gradientStops[i];
          float stop2 = u_gradientStops[i + 1];
          
          if (t >= stop1 && t <= stop2) {
            float localT = (t - stop1) / (stop2 - stop1);
            return mix(u_gradientColors[i], u_gradientColors[i + 1], localT);
          }
        }
        
        return u_gradientColors[0];
      }
      
      void main() {
        vec4 textColor = texture2D(u_texture, v_texCoord);
        
        // Calculate gradient position
        vec2 gradientDir = u_gradientEnd - u_gradientStart;
        vec2 pos = v_texCoord - u_gradientStart;
        float t = dot(pos, gradientDir) / dot(gradientDir, gradientDir);
        
        vec4 gradientColor = sampleGradient(t);
        vec4 finalColor = mix(vec4(0.0), gradientColor, textColor.a);
        gl_FragColor = vec4(finalColor.rgb, finalColor.a * u_opacity);
      }
    `
  }
};

// Text effect library
export class TextEffectLibrary {
  private effects: Map<string, TextEffect> = new Map();
  private presets: Map<string, TextEffectPreset> = new Map();
  private categories: Map<string, TextEffect[]> = new Map();

  constructor() {
    this.initializeDefaultEffects();
  }

  private initializeDefaultEffects(): void {
    // Shadow effects
    this.addEffect({
      id: 'drop-shadow',
      name: 'Drop Shadow',
      type: 'shadow',
      category: 'lighting',
      description: 'Classic drop shadow effect',
      parameters: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 4,
        shadowOffsetX: 2,
        shadowOffsetY: 2,
        shadowSpread: 0,
        shadowInner: false
      },
      enabled: true,
      intensity: 1.0,
      blendMode: 'normal',
      opacity: 1.0,
      order: 0,
      animatable: true,
      realTimePreview: true,
      gpuAccelerated: true,
      presets: [
        {
          id: 'soft-shadow',
          name: 'Soft Shadow',
          description: 'Soft, subtle shadow',
          parameters: { shadowBlur: 8, shadowOffsetX: 3, shadowOffsetY: 3 },
          tags: ['soft', 'subtle'],
          category: 'lighting',
          popularity: 85
        },
        {
          id: 'hard-shadow',
          name: 'Hard Shadow',
          description: 'Sharp, defined shadow',
          parameters: { shadowBlur: 0, shadowOffsetX: 4, shadowOffsetY: 4 },
          tags: ['sharp', 'defined'],
          category: 'lighting',
          popularity: 70
        }
      ]
    });

    // Glow effects
    this.addEffect({
      id: 'outer-glow',
      name: 'Outer Glow',
      type: 'glow',
      category: 'lighting',
      description: 'Outer glow effect',
      parameters: {
        glowColor: 'rgba(255, 255, 255, 0.8)',
        glowSize: 10,
        glowIntensity: 0.8,
        glowQuality: 1,
        glowInner: false
      },
      enabled: true,
      intensity: 1.0,
      blendMode: 'screen',
      opacity: 1.0,
      order: 1,
      animatable: true,
      realTimePreview: true,
      gpuAccelerated: true,
      presets: [
        {
          id: 'neon-glow',
          name: 'Neon Glow',
          description: 'Bright neon glow',
          parameters: { glowColor: 'rgba(0, 255, 255, 1)', glowSize: 15, glowIntensity: 1.2 },
          tags: ['neon', 'bright', 'cyberpunk'],
          category: 'modern',
          popularity: 90
        }
      ]
    });

    // Gradient effects
    this.addEffect({
      id: 'gradient-fill',
      name: 'Gradient Fill',
      type: 'gradient',
      category: 'artistic',
      description: 'Fill text with gradient',
      parameters: {
        gradientType: 'linear',
        gradientStops: [
          { offset: 0, color: '#ff0000' },
          { offset: 1, color: '#0000ff' }
        ],
        gradientAngle: 0,
        gradientCenter: { x: 0.5, y: 0.5 }
      },
      enabled: true,
      intensity: 1.0,
      blendMode: 'normal',
      opacity: 1.0,
      order: 2,
      animatable: true,
      realTimePreview: true,
      gpuAccelerated: true,
      presets: [
        {
          id: 'sunset-gradient',
          name: 'Sunset Gradient',
          description: 'Warm sunset colors',
          parameters: {
            gradientStops: [
              { offset: 0, color: '#ff4500' },
              { offset: 0.5, color: '#ff8c00' },
              { offset: 1, color: '#ffd700' }
            ]
          },
          tags: ['warm', 'sunset', 'orange'],
          category: 'artistic',
          popularity: 80
        }
      ]
    });

    // Outline effects
    this.addEffect({
      id: 'stroke-outline',
      name: 'Stroke Outline',
      type: 'outline',
      category: 'functional',
      description: 'Add stroke outline to text',
      parameters: {
        outlineColor: '#000000',
        outlineWidth: 2,
        outlinePosition: 'outside',
        outlineStyle: 'solid'
      },
      enabled: true,
      intensity: 1.0,
      blendMode: 'normal',
      opacity: 1.0,
      order: 3,
      animatable: true,
      realTimePreview: true,
      gpuAccelerated: false,
      presets: []
    });

    // Organize effects by category
    this.organizeEffectsByCategory();
  }

  private organizeEffectsByCategory(): void {
    this.categories.clear();
    
    for (const effect of this.effects.values()) {
      if (!this.categories.has(effect.category)) {
        this.categories.set(effect.category, []);
      }
      this.categories.get(effect.category)!.push(effect);
    }
  }

  addEffect(effect: TextEffect): void {
    this.effects.set(effect.id, effect);
    
    // Add presets
    effect.presets.forEach(preset => {
      this.presets.set(preset.id, preset);
    });
    
    this.organizeEffectsByCategory();
  }

  getEffect(id: string): TextEffect | undefined {
    return this.effects.get(id);
  }

  getAllEffects(): TextEffect[] {
    return Array.from(this.effects.values());
  }

  getEffectsByCategory(category: TextEffectCategory): TextEffect[] {
    return this.categories.get(category) || [];
  }

  getPreset(id: string): TextEffectPreset | undefined {
    return this.presets.get(id);
  }

  getAllPresets(): TextEffectPreset[] {
    return Array.from(this.presets.values());
  }

  searchEffects(query: string): TextEffect[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.effects.values()).filter(effect =>
      effect.name.toLowerCase().includes(lowerQuery) ||
      effect.description.toLowerCase().includes(lowerQuery) ||
      effect.type.toLowerCase().includes(lowerQuery) ||
      effect.category.toLowerCase().includes(lowerQuery)
    );
  }
}

// Main Text Effects Manager
export class TextEffectManager extends EventEmitter {
  private canvas: fabric.Canvas;
  private effectLibrary: TextEffectLibrary;
  private filterPipeline?: FilterPipelineEngine;
  private renderOptions: TextEffectRenderOptions;
  
  // Effects management
  private effectInstances: Map<string, TextEffectInstance> = new Map();
  private effectGroups: Map<string, TextEffectGroup> = new Map();
  private animations: Map<string, TextEffectAnimation> = new Map();
  private renderCache: Map<string, HTMLCanvasElement> = new Map();
  
  // Performance tracking
  private performance: TextEffectPerformance = {
    totalRenderTime: 0,
    averageRenderTime: 0,
    cacheHitRate: 0,
    gpuAccelerated: 0,
    memoryUsage: 0,
    textureMemory: 0,
    effectsCount: 0,
    animationsCount: 0
  };

  constructor(canvas: fabric.Canvas, options: Partial<TextEffectRenderOptions> = {}) {
    super();
    
    this.canvas = canvas;
    this.effectLibrary = new TextEffectLibrary();
    this.renderOptions = {
      quality: 'medium',
      enableGPU: true,
      enableCaching: true,
      enableRealTimePreview: true,
      maxTextureSize: 2048,
      antialias: true,
      preserveAlpha: true,
      outputFormat: 'canvas',
      ...options
    };

    this.initializeFilterPipeline();
    this.setupCanvasIntegration();
  }

  private initializeFilterPipeline(): void {
    if (this.renderOptions.enableGPU) {
      try {
        const offscreenCanvas = document.createElement('canvas');
        offscreenCanvas.width = 512;
        offscreenCanvas.height = 512;
        this.filterPipeline = new FilterPipelineEngine(offscreenCanvas);
      } catch (error) {
        console.warn('GPU acceleration not available, falling back to CPU rendering');
        this.renderOptions.enableGPU = false;
      }
    }
  }

  private setupCanvasIntegration(): void {
    // Override fabric text rendering to apply effects
    const originalRenderText = fabric.Text.prototype._renderText;
    const self = this;
    
    fabric.Text.prototype._renderText = function(ctx: CanvasRenderingContext2D) {
      const textObject = this as any;
      
      // Check if this text object has effects
      const effectInstances = self.getEffectInstancesForText(textObject.id);
      
      if (effectInstances.length > 0) {
        // Render with effects
        self.renderTextWithEffects(textObject, ctx, effectInstances);
      } else {
        // Normal rendering
        originalRenderText.call(this, ctx);
      }
    };
  }

  private getEffectInstancesForText(textObjectId: string): TextEffectInstance[] {
    return Array.from(this.effectInstances.values())
      .filter(instance => instance.textObjectId === textObjectId && instance.enabled)
      .sort((a, b) => a.order - b.order);
  }

  private renderTextWithEffects(textObject: any, ctx: CanvasRenderingContext2D, instances: TextEffectInstance[]): void {
    const startTime = performance.now();
    
    // Create temporary canvas for effect rendering
    const effectCanvas = document.createElement('canvas');
    effectCanvas.width = textObject.width + 100; // Add padding for effects
    effectCanvas.height = textObject.height + 100;
    const effectCtx = effectCanvas.getContext('2d')!;
    
    // Save original context state
    ctx.save();
    
    try {
      // Render each effect
      for (const instance of instances) {
        this.renderEffectInstance(instance, textObject, effectCtx);
      }
      
      // Composite final result
      ctx.drawImage(effectCanvas, -50, -50); // Offset for padding
      
    } catch (error) {
      console.error('Error rendering text effects:', error);
      // Fallback to normal rendering
      fabric.Text.prototype._renderText.call(textObject, ctx);
    }
    
    ctx.restore();
    
    // Update performance metrics
    const renderTime = performance.now() - startTime;
    this.performance.totalRenderTime += renderTime;
    this.performance.averageRenderTime = this.performance.totalRenderTime / this.effectInstances.size;
    
    this.emit('effects:rendered', { textObjectId: textObject.id, renderTime });
  }

  private renderEffectInstance(instance: TextEffectInstance, textObject: any, ctx: CanvasRenderingContext2D): void {
    const effect = this.effectLibrary.getEffect(instance.effectId);
    if (!effect) return;

    // Check cache
    const cacheKey = this.getCacheKey(instance, textObject);
    if (this.renderOptions.enableCaching && this.renderCache.has(cacheKey)) {
      const cachedCanvas = this.renderCache.get(cacheKey)!;
      this.applyBlendMode(ctx, instance.blendMode, instance.opacity);
      ctx.drawImage(cachedCanvas, 0, 0);
      return;
    }

    // Render effect based on type
    switch (effect.type) {
      case 'shadow':
        this.renderShadowEffect(instance, textObject, ctx);
        break;
      case 'glow':
        this.renderGlowEffect(instance, textObject, ctx);
        break;
      case 'outline':
        this.renderOutlineEffect(instance, textObject, ctx);
        break;
      case 'gradient':
        this.renderGradientEffect(instance, textObject, ctx);
        break;
      case 'blur':
        this.renderBlurEffect(instance, textObject, ctx);
        break;
      default:
        console.warn(`Unsupported effect type: ${effect.type}`);
    }

    // Cache the result
    if (this.renderOptions.enableCaching) {
      const cacheCanvas = document.createElement('canvas');
      cacheCanvas.width = ctx.canvas.width;
      cacheCanvas.height = ctx.canvas.height;
      const cacheCtx = cacheCanvas.getContext('2d')!;
      cacheCtx.drawImage(ctx.canvas, 0, 0);
      this.renderCache.set(cacheKey, cacheCanvas);
    }
  }

  private renderShadowEffect(instance: TextEffectInstance, textObject: any, ctx: CanvasRenderingContext2D): void {
    const params = instance.parameters;
    
    ctx.save();
    
    // Set shadow properties
    ctx.shadowColor = params.shadowColor || 'rgba(0, 0, 0, 0.5)';
    ctx.shadowBlur = (params.shadowBlur || 4) * instance.intensity;
    ctx.shadowOffsetX = (params.shadowOffsetX || 2) * instance.intensity;
    ctx.shadowOffsetY = (params.shadowOffsetY || 2) * instance.intensity;
    
    // Apply blend mode and opacity
    this.applyBlendMode(ctx, instance.blendMode, instance.opacity);
    
    // Render text with shadow
    ctx.fillStyle = textObject.fill || '#000000';
    ctx.font = `${textObject.fontWeight || 'normal'} ${textObject.fontSize || 16}px ${textObject.fontFamily || 'Arial'}`;
    ctx.fillText(textObject.text, 50, 50);
    
    ctx.restore();
  }

  private renderGlowEffect(instance: TextEffectInstance, textObject: any, ctx: CanvasRenderingContext2D): void {
    const params = instance.parameters;
    const glowSize = (params.glowSize || 10) * instance.intensity;
    const glowColor = params.glowColor || 'rgba(255, 255, 255, 0.8)';
    
    ctx.save();
    
    // Create glow by rendering text multiple times with blur
    this.applyBlendMode(ctx, instance.blendMode, instance.opacity);
    
    for (let i = 0; i < glowSize; i++) {
      ctx.shadowColor = glowColor;
      ctx.shadowBlur = i * 2;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
      
      ctx.fillStyle = glowColor;
      ctx.font = `${textObject.fontWeight || 'normal'} ${textObject.fontSize || 16}px ${textObject.fontFamily || 'Arial'}`;
      ctx.fillText(textObject.text, 50, 50);
    }
    
    ctx.restore();
  }

  private renderOutlineEffect(instance: TextEffectInstance, textObject: any, ctx: CanvasRenderingContext2D): void {
    const params = instance.parameters;
    
    ctx.save();
    
    // Set outline properties
    ctx.strokeStyle = params.outlineColor || '#000000';
    ctx.lineWidth = (params.outlineWidth || 2) * instance.intensity;
    ctx.lineJoin = 'round';
    ctx.lineCap = 'round';
    
    // Apply blend mode and opacity
    this.applyBlendMode(ctx, instance.blendMode, instance.opacity);
    
    // Render outline
    ctx.font = `${textObject.fontWeight || 'normal'} ${textObject.fontSize || 16}px ${textObject.fontFamily || 'Arial'}`;
    ctx.strokeText(textObject.text, 50, 50);
    
    ctx.restore();
  }

  private renderGradientEffect(instance: TextEffectInstance, textObject: any, ctx: CanvasRenderingContext2D): void {
    const params = instance.parameters;
    
    ctx.save();
    
    // Create gradient
    let gradient: CanvasGradient;
    
    if (params.gradientType === 'linear') {
      gradient = ctx.createLinearGradient(0, 0, textObject.width, 0);
    } else {
      gradient = ctx.createRadialGradient(
        textObject.width / 2, textObject.height / 2, 0,
        textObject.width / 2, textObject.height / 2, textObject.width / 2
      );
    }
    
    // Add gradient stops
    if (params.gradientStops) {
      for (const stop of params.gradientStops) {
        gradient.addColorStop(stop.offset, stop.color);
      }
    }
    
    // Apply gradient
    ctx.fillStyle = gradient;
    this.applyBlendMode(ctx, instance.blendMode, instance.opacity);
    
    ctx.font = `${textObject.fontWeight || 'normal'} ${textObject.fontSize || 16}px ${textObject.fontFamily || 'Arial'}`;
    ctx.fillText(textObject.text, 50, 50);
    
    ctx.restore();
  }

  private renderBlurEffect(instance: TextEffectInstance, textObject: any, ctx: CanvasRenderingContext2D): void {
    const params = instance.parameters;
    const blurRadius = (params.blurRadius || 4) * instance.intensity;
    
    ctx.save();
    
    // Apply blur using filter (if supported)
    if (ctx.filter !== undefined) {
      ctx.filter = `blur(${blurRadius}px)`;
    }
    
    this.applyBlendMode(ctx, instance.blendMode, instance.opacity);
    
    ctx.fillStyle = textObject.fill || '#000000';
    ctx.font = `${textObject.fontWeight || 'normal'} ${textObject.fontSize || 16}px ${textObject.fontFamily || 'Arial'}`;
    ctx.fillText(textObject.text, 50, 50);
    
    ctx.restore();
  }

  private applyBlendMode(ctx: CanvasRenderingContext2D, blendMode: BlendMode, opacity: number): void {
    ctx.globalAlpha = opacity;
    ctx.globalCompositeOperation = blendMode as GlobalCompositeOperation;
  }

  private getCacheKey(instance: TextEffectInstance, textObject: any): string {
    return `${instance.id}_${textObject.text}_${JSON.stringify(instance.parameters)}`;
  }

  // Public API methods

  // Effect instance management
  addEffectInstance(textObjectId: string, effectId: string, parameters: TextEffectParameters = {}): string {
    const effect = this.effectLibrary.getEffect(effectId);
    if (!effect) {
      throw new Error(`Effect not found: ${effectId}`);
    }

    const instanceId = `instance_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const instance: TextEffectInstance = {
      id: instanceId,
      effectId,
      textObjectId,
      parameters: { ...effect.parameters, ...parameters },
      enabled: true,
      intensity: 1.0,
      blendMode: effect.blendMode,
      opacity: effect.opacity,
      order: this.getNextOrder(textObjectId),
      gpuAccelerated: effect.gpuAccelerated && this.renderOptions.enableGPU
    };

    this.effectInstances.set(instanceId, instance);
    this.updatePerformanceMetrics();
    
    if (this.renderOptions.enableRealTimePreview) {
      this.renderTextObject(textObjectId);
    }
    
    this.emit('effect:added', { instanceId, instance });
    return instanceId;
  }

  private getNextOrder(textObjectId: string): number {
    const instances = Array.from(this.effectInstances.values())
      .filter(instance => instance.textObjectId === textObjectId);
    
    return instances.length > 0 ? Math.max(...instances.map(i => i.order)) + 1 : 0;
  }

  updateEffectInstance(instanceId: string, updates: Partial<TextEffectInstance>): boolean {
    const instance = this.effectInstances.get(instanceId);
    if (!instance) return false;

    Object.assign(instance, updates);
    
    // Clear cache for this instance
    this.clearCacheForInstance(instanceId);
    
    if (this.renderOptions.enableRealTimePreview) {
      this.renderTextObject(instance.textObjectId);
    }
    
    this.emit('effect:updated', { instanceId, instance });
    return true;
  }

  removeEffectInstance(instanceId: string): boolean {
    const instance = this.effectInstances.get(instanceId);
    if (!instance) return false;

    this.effectInstances.delete(instanceId);
    this.clearCacheForInstance(instanceId);
    
    if (this.renderOptions.enableRealTimePreview) {
      this.renderTextObject(instance.textObjectId);
    }
    
    this.emit('effect:removed', { instanceId });
    return true;
  }

  getEffectInstance(instanceId: string): TextEffectInstance | undefined {
    return this.effectInstances.get(instanceId);
  }

  getEffectInstancesForText(textObjectId: string): TextEffectInstance[] {
    return Array.from(this.effectInstances.values())
      .filter(instance => instance.textObjectId === textObjectId)
      .sort((a, b) => a.order - b.order);
  }

  // Effect library access
  getEffectLibrary(): TextEffectLibrary {
    return this.effectLibrary;
  }

  // Preset management
  applyPreset(instanceId: string, presetId: string): boolean {
    const instance = this.effectInstances.get(instanceId);
    const preset = this.effectLibrary.getPreset(presetId);
    
    if (!instance || !preset) return false;

    instance.parameters = { ...instance.parameters, ...preset.parameters };
    
    this.clearCacheForInstance(instanceId);
    
    if (this.renderOptions.enableRealTimePreview) {
      this.renderTextObject(instance.textObjectId);
    }
    
    this.emit('preset:applied', { instanceId, presetId });
    return true;
  }

  // Group management
  createEffectGroup(name: string, description: string, effectInstanceIds: string[]): string {
    const groupId = `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const group: TextEffectGroup = {
      id: groupId,
      name,
      description,
      effectIds: [...effectInstanceIds],
      enabled: true,
      opacity: 1.0,
      blendMode: 'normal'
    };

    this.effectGroups.set(groupId, group);
    this.emit('group:created', { groupId, group });
    
    return groupId;
  }

  // Animation management
  animateEffectProperty(instanceId: string, property: string, keyframes: AnimationKeyframe[], options: Partial<TextEffectAnimation> = {}): string {
    const animationId = `animation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const animation: TextEffectAnimation = {
      id: animationId,
      effectInstanceId: instanceId,
      property,
      keyframes,
      duration: options.duration || 1000,
      delay: options.delay || 0,
      easing: options.easing || 'ease-in-out',
      loop: options.loop || false,
      direction: options.direction || 'normal',
      playState: 'running'
    };

    this.animations.set(animationId, animation);
    this.startAnimation(animation);
    
    this.emit('animation:started', { animationId, animation });
    return animationId;
  }

  private startAnimation(animation: TextEffectAnimation): void {
    const startTime = performance.now();
    const instance = this.effectInstances.get(animation.effectInstanceId);
    
    if (!instance) return;

    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime - animation.delay;
      const progress = Math.min(elapsed / animation.duration, 1);
      
      if (progress >= 0) {
        const value = this.interpolateKeyframes(animation.keyframes, progress);
        this.updateEffectProperty(instance, animation.property, value);
        
        if (this.renderOptions.enableRealTimePreview) {
          this.renderTextObject(instance.textObjectId);
        }
      }
      
      if (progress < 1 && animation.playState === 'running') {
        requestAnimationFrame(animate);
      } else {
        animation.playState = 'finished';
        this.emit('animation:finished', { animationId: animation.id });
      }
    };
    
    requestAnimationFrame(animate);
  }

  private interpolateKeyframes(keyframes: AnimationKeyframe[], progress: number): any {
    if (keyframes.length === 0) return null;
    if (keyframes.length === 1) return keyframes[0].value;
    
    // Find surrounding keyframes
    let startFrame = keyframes[0];
    let endFrame = keyframes[keyframes.length - 1];
    
    for (let i = 0; i < keyframes.length - 1; i++) {
      if (progress >= keyframes[i].time && progress <= keyframes[i + 1].time) {
        startFrame = keyframes[i];
        endFrame = keyframes[i + 1];
        break;
      }
    }
    
    // Calculate local progress
    const localProgress = (progress - startFrame.time) / (endFrame.time - startFrame.time);
    
    // Interpolate values
    return this.interpolateValues(startFrame.value, endFrame.value, localProgress);
  }

  private interpolateValues(start: any, end: any, progress: number): any {
    if (typeof start === 'number' && typeof end === 'number') {
      return start + (end - start) * progress;
    }
    
    if (typeof start === 'string' && typeof end === 'string') {
      // Color interpolation
      if (start.startsWith('#') && end.startsWith('#')) {
        return this.interpolateColor(start, end, progress);
      }
    }
    
    return progress < 0.5 ? start : end;
  }

  private interpolateColor(start: string, end: string, progress: number): string {
    const startRGB = this.hexToRgb(start);
    const endRGB = this.hexToRgb(end);
    
    if (!startRGB || !endRGB) return start;
    
    const r = Math.round(startRGB.r + (endRGB.r - startRGB.r) * progress);
    const g = Math.round(startRGB.g + (endRGB.g - startRGB.g) * progress);
    const b = Math.round(startRGB.b + (endRGB.b - startRGB.b) * progress);
    
    return `rgb(${r}, ${g}, ${b})`;
  }

  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  private updateEffectProperty(instance: TextEffectInstance, property: string, value: any): void {
    const propertyPath = property.split('.');
    let target = instance.parameters as any;
    
    for (let i = 0; i < propertyPath.length - 1; i++) {
      target = target[propertyPath[i]];
    }
    
    target[propertyPath[propertyPath.length - 1]] = value;
  }

  // Rendering
  renderTextObject(textObjectId: string): void {
    const textObject = this.canvas.getObjects().find((obj: any) => obj.id === textObjectId);
    if (textObject) {
      textObject.dirty = true;
      this.canvas.renderAll();
    }
  }

  // Performance and caching
  private clearCacheForInstance(instanceId: string): void {
    const keysToDelete = Array.from(this.renderCache.keys())
      .filter(key => key.startsWith(instanceId));
    
    keysToDelete.forEach(key => this.renderCache.delete(key));
  }

  clearCache(): void {
    this.renderCache.clear();
    this.emit('cache:cleared');
  }

  private updatePerformanceMetrics(): void {
    this.performance.effectsCount = this.effectInstances.size;
    this.performance.animationsCount = this.animations.size;
    this.performance.gpuAccelerated = Array.from(this.effectInstances.values())
      .filter(instance => instance.gpuAccelerated).length;
    this.performance.memoryUsage = this.estimateMemoryUsage();
  }

  private estimateMemoryUsage(): number {
    let totalMemory = 0;
    
    // Effect instances
    totalMemory += this.effectInstances.size * 1024; // ~1KB per instance
    
    // Cache
    for (const canvas of this.renderCache.values()) {
      totalMemory += canvas.width * canvas.height * 4; // RGBA bytes
    }
    
    // Animations
    totalMemory += this.animations.size * 512; // ~512B per animation
    
    return totalMemory;
  }

  getPerformanceMetrics(): TextEffectPerformance {
    this.updatePerformanceMetrics();
    return { ...this.performance };
  }

  // Configuration
  updateRenderOptions(options: Partial<TextEffectRenderOptions>): void {
    Object.assign(this.renderOptions, options);
    
    // Reinitialize filter pipeline if GPU setting changed
    if (options.enableGPU !== undefined) {
      this.initializeFilterPipeline();
    }
    
    this.emit('options:updated', options);
  }

  getRenderOptions(): TextEffectRenderOptions {
    return { ...this.renderOptions };
  }

  // Cleanup
  dispose(): void {
    // Clear caches
    this.clearCache();
    
    // Stop animations
    this.animations.forEach(animation => {
      animation.playState = 'finished';
    });
    
    // Dispose filter pipeline
    if (this.filterPipeline) {
      this.filterPipeline.dispose();
    }
    
    // Clear collections
    this.effectInstances.clear();
    this.effectGroups.clear();
    this.animations.clear();
    
    this.removeAllListeners();
    this.emit('effects:disposed');
  }
}