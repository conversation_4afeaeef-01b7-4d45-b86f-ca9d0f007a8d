import { EventEmitter } from 'events';
import { CanvasStateManager, StateSnapshot, StateDelta } from './canvas-state-manager';

export interface SyncConfig {
  enabled: boolean;
  serverUrl?: string;
  websocketUrl?: string;
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  conflictResolutionStrategy: 'manual' | 'auto-merge' | 'last-write-wins' | 'operational-transform';
  enableOperationalTransform: boolean;
  enableConflictDetection: boolean;
  maxOperationQueueSize: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
}

export interface SyncOperation {
  id: string;
  type: 'state-change' | 'cursor-move' | 'selection-change' | 'user-join' | 'user-leave';
  userId: string;
  timestamp: number;
  data: any;
  delta?: StateDelta;
  dependencies: string[]; // Operation IDs this operation depends on
  acknowledged: boolean;
  retryCount: number;
}

export interface SyncUser {
  id: string;
  name: string;
  avatar?: string;
  color: string;
  cursor?: { x: number; y: number };
  selection?: string[];
  lastActivity: number;
  status: 'active' | 'idle' | 'disconnected';
  permissions: UserPermissions;
}

export interface UserPermissions {
  canEdit: boolean;
  canView: boolean;
  canComment: boolean;
  canInvite: boolean;
  isOwner: boolean;
  restrictedLayers?: string[];
}

export interface SyncSession {
  id: string;
  name: string;
  ownerId: string;
  users: Map<string, SyncUser>;
  operations: SyncOperation[];
  lastSyncTime: number;
  version: number;
  settings: SessionSettings;
}

export interface SessionSettings {
  allowAnonymous: boolean;
  maxUsers: number;
  requireInvitation: boolean;
  enableVoiceChat: boolean;
  enableVideo: boolean;
  lockingEnabled: boolean;
  autoSaveInterval: number;
}

export interface ConflictInfo {
  id: string;
  type: 'concurrent-edit' | 'version-mismatch' | 'permission-denied' | 'data-corruption';
  operations: SyncOperation[];
  affectedLayers: string[];
  timestamp: number;
  users: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface MergeResult {
  success: boolean;
  resultingState: any;
  conflicts: ConflictInfo[];
  appliedOperations: SyncOperation[];
  discardedOperations: SyncOperation[];
}

export interface NetworkStatus {
  connected: boolean;
  latency: number;
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  lastHeartbeat: number;
  reconnectAttempts: number;
  bandwidth?: number;
}

// Operational Transformation for collaborative editing
export class OperationalTransform {
  // Transform operation A against operation B when they are concurrent
  transform(opA: SyncOperation, opB: SyncOperation): { opA_prime: SyncOperation; opB_prime: SyncOperation } {
    // Simple operational transformation implementation
    // In production, this would need sophisticated OT algorithms
    
    const opA_prime = { ...opA };
    const opB_prime = { ...opB };

    if (opA.type === 'state-change' && opB.type === 'state-change') {
      // Transform based on the type of changes
      if (this.operationsConflict(opA, opB)) {
        opA_prime.data = this.resolveConflict(opA.data, opB.data, 'prefer-a');
        opB_prime.data = this.resolveConflict(opA.data, opB.data, 'prefer-b');
      }
    }

    return { opA_prime, opB_prime };
  }

  // Check if two operations conflict
  private operationsConflict(opA: SyncOperation, opB: SyncOperation): boolean {
    if (!opA.delta || !opB.delta) return false;

    // Check if operations affect the same paths
    const pathsA = new Set(opA.delta.changes.map(c => c.path));
    const pathsB = new Set(opB.delta.changes.map(c => c.path));

    // Check for intersection
    for (const pathA of pathsA) {
      for (const pathB of pathsB) {
        if (pathA === pathB || this.pathsOverlap(pathA, pathB)) {
          return true;
        }
      }
    }

    return false;
  }

  private pathsOverlap(pathA: string, pathB: string): boolean {
    const partsA = pathA.split('.');
    const partsB = pathB.split('.');
    const minLength = Math.min(partsA.length, partsB.length);

    for (let i = 0; i < minLength; i++) {
      if (partsA[i] !== partsB[i]) {
        return false;
      }
    }

    return true;
  }

  private resolveConflict(dataA: any, dataB: any, strategy: 'prefer-a' | 'prefer-b' | 'merge'): any {
    switch (strategy) {
      case 'prefer-a':
        return dataA;
      case 'prefer-b':
        return dataB;
      case 'merge':
        // Simple merge strategy - in production would be more sophisticated
        return { ...dataA, ...dataB };
      default:
        return dataA;
    }
  }

  // Apply a sequence of operations with transformation
  applyOperations(baseState: any, operations: SyncOperation[]): { state: any; conflicts: ConflictInfo[] } {
    let currentState = JSON.parse(JSON.stringify(baseState));
    const conflicts: ConflictInfo[] = [];

    // Sort operations by timestamp
    const sortedOps = operations.sort((a, b) => a.timestamp - b.timestamp);

    for (let i = 0; i < sortedOps.length; i++) {
      const op = sortedOps[i];
      
      try {
        // Check for conflicts with previous operations
        for (let j = 0; j < i; j++) {
          const prevOp = sortedOps[j];
          if (this.operationsConflict(op, prevOp)) {
            const conflict: ConflictInfo = {
              id: `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
              type: 'concurrent-edit',
              operations: [prevOp, op],
              affectedLayers: this.extractAffectedLayers(op),
              timestamp: Date.now(),
              users: [prevOp.userId, op.userId],
              severity: 'medium'
            };
            conflicts.push(conflict);
          }
        }

        // Apply operation to state
        currentState = this.applyOperation(currentState, op);

      } catch (error) {
        console.error('Failed to apply operation:', op, error);
      }
    }

    return { state: currentState, conflicts };
  }

  private applyOperation(state: any, operation: SyncOperation): any {
    if (!operation.delta) return state;

    // Apply delta changes to state
    const newState = JSON.parse(JSON.stringify(state));

    operation.delta.changes.forEach(change => {
      this.applyChange(newState, change);
    });

    return newState;
  }

  private applyChange(state: any, change: any): void {
    const pathParts = change.path.split('.');
    let current = state;

    // Navigate to parent object
    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i];
      if (current[part] === undefined) {
        current[part] = {};
      }
      current = current[part];
    }

    const lastPart = pathParts[pathParts.length - 1];

    switch (change.operation) {
      case 'set':
        current[lastPart] = change.newValue;
        break;
      case 'delete':
        delete current[lastPart];
        break;
      case 'insert':
        if (Array.isArray(current[lastPart])) {
          current[lastPart].push(change.newValue);
        }
        break;
      case 'move':
        if (Array.isArray(current[lastPart])) {
          const item = current[lastPart].splice(change.oldValue, 1)[0];
          current[lastPart].splice(change.newValue, 0, item);
        }
        break;
    }
  }

  private extractAffectedLayers(operation: SyncOperation): string[] {
    if (!operation.delta) return [];

    const layerIds = new Set<string>();

    operation.delta.changes.forEach(change => {
      if (change.path.startsWith('layerData.layers.')) {
        const pathParts = change.path.split('.');
        if (pathParts.length >= 3) {
          layerIds.add(pathParts[2]);
        }
      }
    });

    return Array.from(layerIds);
  }
}

// Conflict resolution system
export class ConflictResolver {
  private strategy: SyncConfig['conflictResolutionStrategy'];

  constructor(strategy: SyncConfig['conflictResolutionStrategy'] = 'manual') {
    this.strategy = strategy;
  }

  resolveConflicts(conflicts: ConflictInfo[], currentState: any): MergeResult {
    const result: MergeResult = {
      success: true,
      resultingState: JSON.parse(JSON.stringify(currentState)),
      conflicts: [],
      appliedOperations: [],
      discardedOperations: []
    };

    for (const conflict of conflicts) {
      try {
        const resolution = this.resolveConflict(conflict, result.resultingState);
        
        if (resolution.success) {
          result.resultingState = resolution.state;
          result.appliedOperations.push(...resolution.appliedOperations);
          result.discardedOperations.push(...resolution.discardedOperations);
        } else {
          result.conflicts.push(conflict);
        }
      } catch (error) {
        console.error('Failed to resolve conflict:', conflict, error);
        result.conflicts.push(conflict);
        result.success = false;
      }
    }

    return result;
  }

  private resolveConflict(conflict: ConflictInfo, state: any): {
    success: boolean;
    state: any;
    appliedOperations: SyncOperation[];
    discardedOperations: SyncOperation[];
  } {
    switch (this.strategy) {
      case 'last-write-wins':
        return this.resolveLastWriteWins(conflict, state);
      
      case 'auto-merge':
        return this.resolveAutoMerge(conflict, state);
      
      case 'operational-transform':
        return this.resolveOperationalTransform(conflict, state);
      
      case 'manual':
      default:
        return {
          success: false,
          state,
          appliedOperations: [],
          discardedOperations: []
        };
    }
  }

  private resolveLastWriteWins(conflict: ConflictInfo, state: any): any {
    // Apply the operation with the latest timestamp
    const sortedOps = conflict.operations.sort((a, b) => b.timestamp - a.timestamp);
    const winningOp = sortedOps[0];
    const discardedOps = sortedOps.slice(1);

    // Apply winning operation
    const ot = new OperationalTransform();
    const { state: newState } = ot.applyOperations(state, [winningOp]);

    return {
      success: true,
      state: newState,
      appliedOperations: [winningOp],
      discardedOperations: discardedOps
    };
  }

  private resolveAutoMerge(conflict: ConflictInfo, state: any): any {
    // Attempt to merge all operations
    const ot = new OperationalTransform();
    
    try {
      const { state: mergedState, conflicts: remainingConflicts } = ot.applyOperations(state, conflict.operations);
      
      return {
        success: remainingConflicts.length === 0,
        state: mergedState,
        appliedOperations: conflict.operations,
        discardedOperations: []
      };
    } catch (error) {
      return {
        success: false,
        state,
        appliedOperations: [],
        discardedOperations: conflict.operations
      };
    }
  }

  private resolveOperationalTransform(conflict: ConflictInfo, state: any): any {
    const ot = new OperationalTransform();
    
    // Apply operations with transformation
    let currentState = state;
    const appliedOps: SyncOperation[] = [];
    const discardedOps: SyncOperation[] = [];

    for (let i = 0; i < conflict.operations.length; i++) {
      const op = conflict.operations[i];
      
      try {
        // Transform against already applied operations
        let transformedOp = op;
        
        for (const appliedOp of appliedOps) {
          const { opA_prime } = ot.transform(transformedOp, appliedOp);
          transformedOp = opA_prime;
        }

        const { state: newState } = ot.applyOperations(currentState, [transformedOp]);
        currentState = newState;
        appliedOps.push(transformedOp);

      } catch (error) {
        console.error('Failed to transform operation:', op, error);
        discardedOps.push(op);
      }
    }

    return {
      success: discardedOps.length === 0,
      state: currentState,
      appliedOperations: appliedOps,
      discardedOperations: discardedOps
    };
  }

  setStrategy(strategy: SyncConfig['conflictResolutionStrategy']): void {
    this.strategy = strategy;
  }
}

// Network manager for real-time synchronization
export class NetworkManager extends EventEmitter {
  private websocket?: WebSocket;
  private config: SyncConfig;
  private reconnectAttempts: number = 0;
  private heartbeatInterval?: NodeJS.Timeout;
  private status: NetworkStatus;

  constructor(config: SyncConfig) {
    super();
    this.config = config;
    this.status = {
      connected: false,
      latency: 0,
      quality: 'poor',
      lastHeartbeat: 0,
      reconnectAttempts: 0
    };
  }

  connect(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!this.config.websocketUrl) {
        reject(new Error('WebSocket URL not configured'));
        return;
      }

      try {
        this.websocket = new WebSocket(this.config.websocketUrl);

        this.websocket.onopen = () => {
          this.status.connected = true;
          this.status.reconnectAttempts = 0;
          this.reconnectAttempts = 0;
          
          this.startHeartbeat();
          this.emit('connected');
          resolve(true);
        };

        this.websocket.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.websocket.onclose = () => {
          this.status.connected = false;
          this.stopHeartbeat();
          this.emit('disconnected');
          this.attemptReconnect();
        };

        this.websocket.onerror = (error) => {
          this.emit('error', error);
          reject(error);
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = undefined;
    }
    this.stopHeartbeat();
    this.status.connected = false;
  }

  send(operation: SyncOperation): boolean {
    if (!this.isConnected()) {
      this.emit('send:failed', { operation, reason: 'not_connected' });
      return false;
    }

    try {
      const message = JSON.stringify(operation);
      this.websocket!.send(message);
      this.emit('operation:sent', { operation });
      return true;
    } catch (error) {
      this.emit('send:failed', { operation, error });
      return false;
    }
  }

  private handleMessage(data: string): void {
    try {
      const operation = JSON.parse(data) as SyncOperation;
      
      // Update latency measurement
      if (operation.type === 'heartbeat') {
        this.updateLatency(operation.timestamp);
        return;
      }

      this.emit('operation:received', { operation });
    } catch (error) {
      console.error('Failed to parse message:', data, error);
    }
  }

  private startHeartbeat(): void {
    if (this.heartbeatInterval) return;

    this.heartbeatInterval = setInterval(() => {
      const heartbeat: SyncOperation = {
        id: `heartbeat_${Date.now()}`,
        type: 'heartbeat' as any,
        userId: 'system',
        timestamp: Date.now(),
        data: {},
        dependencies: [],
        acknowledged: false,
        retryCount: 0
      };

      this.send(heartbeat);
      this.status.lastHeartbeat = Date.now();
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }
  }

  private updateLatency(timestamp: number): void {
    this.status.latency = Date.now() - timestamp;
    
    if (this.status.latency < 100) {
      this.status.quality = 'excellent';
    } else if (this.status.latency < 300) {
      this.status.quality = 'good';
    } else if (this.status.latency < 1000) {
      this.status.quality = 'fair';
    } else {
      this.status.quality = 'poor';
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.config.reconnectAttempts) {
      this.emit('reconnect:failed');
      return;
    }

    this.reconnectAttempts++;
    this.status.reconnectAttempts = this.reconnectAttempts;

    setTimeout(() => {
      this.emit('reconnect:attempt', { attempt: this.reconnectAttempts });
      this.connect().catch(error => {
        console.error('Reconnect attempt failed:', error);
      });
    }, this.config.reconnectDelay * this.reconnectAttempts);
  }

  isConnected(): boolean {
    return this.status.connected && this.websocket?.readyState === WebSocket.OPEN;
  }

  getStatus(): NetworkStatus {
    return { ...this.status };
  }
}

// Advanced State Synchronization Manager
export class StateSyncManager extends EventEmitter {
  private stateManager: CanvasStateManager;
  private networkManager: NetworkManager;
  private conflictResolver: ConflictResolver;
  private operationalTransform: OperationalTransform;
  private config: SyncConfig;
  
  private currentSession?: SyncSession;
  private currentUser?: SyncUser;
  private operationQueue: SyncOperation[] = [];
  private pendingOperations: Map<string, SyncOperation> = new Map();
  private lastSyncTimestamp: number = 0;
  
  constructor(stateManager: CanvasStateManager, config: Partial<SyncConfig> = {}) {
    super();
    
    this.stateManager = stateManager;
    this.config = this.mergeDefaultConfig(config);
    this.networkManager = new NetworkManager(this.config);
    this.conflictResolver = new ConflictResolver(this.config.conflictResolutionStrategy);
    this.operationalTransform = new OperationalTransform();

    this.setupEventHandlers();
  }

  private mergeDefaultConfig(config: Partial<SyncConfig>): SyncConfig {
    return {
      enabled: false,
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      heartbeatInterval: 30000,
      conflictResolutionStrategy: 'operational-transform',
      enableOperationalTransform: true,
      enableConflictDetection: true,
      maxOperationQueueSize: 1000,
      compressionEnabled: true,
      encryptionEnabled: false,
      ...config
    };
  }

  private setupEventHandlers(): void {
    // Listen to state manager changes
    this.stateManager.on('state:captured', (data) => {
      if (this.config.enabled && this.currentSession) {
        this.broadcastStateChange(data.snapshot);
      }
    });

    // Network events
    this.networkManager.on('connected', () => {
      this.onNetworkConnected();
    });

    this.networkManager.on('disconnected', () => {
      this.onNetworkDisconnected();
    });

    this.networkManager.on('operation:received', (data) => {
      this.handleRemoteOperation(data.operation);
    });

    this.networkManager.on('operation:sent', (data) => {
      this.onOperationSent(data.operation);
    });
  }

  // Session management
  async createSession(name: string, settings: Partial<SessionSettings> = {}): Promise<SyncSession> {
    const sessionId = this.generateSessionId();
    const userId = this.generateUserId();

    const session: SyncSession = {
      id: sessionId,
      name,
      ownerId: userId,
      users: new Map(),
      operations: [],
      lastSyncTime: Date.now(),
      version: 1,
      settings: {
        allowAnonymous: false,
        maxUsers: 10,
        requireInvitation: true,
        enableVoiceChat: false,
        enableVideo: false,
        lockingEnabled: true,
        autoSaveInterval: 30000,
        ...settings
      }
    };

    const user: SyncUser = {
      id: userId,
      name: 'Owner',
      color: this.generateUserColor(),
      lastActivity: Date.now(),
      status: 'active',
      permissions: {
        canEdit: true,
        canView: true,
        canComment: true,
        canInvite: true,
        isOwner: true
      }
    };

    session.users.set(userId, user);
    
    this.currentSession = session;
    this.currentUser = user;

    this.emit('session:created', { session, user });
    
    return session;
  }

  async joinSession(sessionId: string, userName: string = 'Anonymous'): Promise<boolean> {
    try {
      if (!this.config.enabled) {
        throw new Error('Synchronization is disabled');
      }

      // Connect to network
      await this.networkManager.connect();

      // Request to join session
      const joinOperation: SyncOperation = {
        id: this.generateOperationId(),
        type: 'user-join',
        userId: this.generateUserId(),
        timestamp: Date.now(),
        data: {
          sessionId,
          userName,
          userAgent: navigator.userAgent
        },
        dependencies: [],
        acknowledged: false,
        retryCount: 0
      };

      const success = this.networkManager.send(joinOperation);
      
      if (success) {
        this.emit('session:join:requested', { sessionId, userName });
      }

      return success;
    } catch (error) {
      this.emit('session:join:failed', { sessionId, error });
      return false;
    }
  }

  leaveSession(): void {
    if (!this.currentSession || !this.currentUser) return;

    const leaveOperation: SyncOperation = {
      id: this.generateOperationId(),
      type: 'user-leave',
      userId: this.currentUser.id,
      timestamp: Date.now(),
      data: {
        sessionId: this.currentSession.id
      },
      dependencies: [],
      acknowledged: false,
      retryCount: 0
    };

    this.networkManager.send(leaveOperation);
    this.networkManager.disconnect();

    this.currentSession = undefined;
    this.currentUser = undefined;
    this.operationQueue = [];
    this.pendingOperations.clear();

    this.emit('session:left');
  }

  // Real-time synchronization
  private broadcastStateChange(snapshot: StateSnapshot): void {
    if (!this.currentSession || !this.currentUser) return;

    // Create operation for state change
    const operation: SyncOperation = {
      id: this.generateOperationId(),
      type: 'state-change',
      userId: this.currentUser.id,
      timestamp: Date.now(),
      data: {
        snapshotId: snapshot.id,
        state: snapshot.state
      },
      delta: snapshot.delta,
      dependencies: this.getOperationDependencies(),
      acknowledged: false,
      retryCount: 0
    };

    this.queueOperation(operation);
    this.networkManager.send(operation);
  }

  private handleRemoteOperation(operation: SyncOperation): void {
    if (!this.currentSession) return;

    // Ignore own operations
    if (operation.userId === this.currentUser?.id) {
      this.acknowledgeOperation(operation.id);
      return;
    }

    switch (operation.type) {
      case 'state-change':
        this.handleRemoteStateChange(operation);
        break;
      case 'user-join':
        this.handleUserJoin(operation);
        break;
      case 'user-leave':
        this.handleUserLeave(operation);
        break;
      case 'cursor-move':
        this.handleCursorMove(operation);
        break;
      case 'selection-change':
        this.handleSelectionChange(operation);
        break;
    }

    // Add to session operations
    this.currentSession.operations.push(operation);
    
    // Keep operation history within limits
    if (this.currentSession.operations.length > this.config.maxOperationQueueSize) {
      this.currentSession.operations = this.currentSession.operations.slice(-this.config.maxOperationQueueSize / 2);
    }
  }

  private handleRemoteStateChange(operation: SyncOperation): void {
    if (!this.config.enableConflictDetection) {
      // Simple case: apply directly
      this.applyRemoteState(operation);
      return;
    }

    // Check for conflicts with pending operations
    const conflicts = this.detectConflicts(operation);
    
    if (conflicts.length === 0) {
      this.applyRemoteState(operation);
    } else {
      this.resolveAndApplyConflicts(conflicts, operation);
    }
  }

  private detectConflicts(remoteOperation: SyncOperation): ConflictInfo[] {
    const conflicts: ConflictInfo[] = [];
    
    // Check against pending operations
    for (const [opId, pendingOp] of this.pendingOperations.entries()) {
      if (this.operationsConflict(pendingOp, remoteOperation)) {
        const conflict: ConflictInfo = {
          id: `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
          type: 'concurrent-edit',
          operations: [pendingOp, remoteOperation],
          affectedLayers: this.extractAffectedLayers(remoteOperation),
          timestamp: Date.now(),
          users: [pendingOp.userId, remoteOperation.userId],
          severity: this.assessConflictSeverity(pendingOp, remoteOperation)
        };
        conflicts.push(conflict);
      }
    }

    return conflicts;
  }

  private operationsConflict(opA: SyncOperation, opB: SyncOperation): boolean {
    // Use operational transform to check conflicts
    return this.operationalTransform.operationsConflict(opA, opB);
  }

  private resolveAndApplyConflicts(conflicts: ConflictInfo[], remoteOperation: SyncOperation): void {
    const currentState = this.stateManager.getCurrentSnapshot()?.state;
    if (!currentState) return;

    // Resolve conflicts
    const mergeResult = this.conflictResolver.resolveConflicts(conflicts, currentState);

    if (mergeResult.success) {
      // Apply resolved state
      this.emit('state:merge:requested', { 
        state: mergeResult.resultingState,
        appliedOperations: mergeResult.appliedOperations,
        discardedOperations: mergeResult.discardedOperations
      });

      // Remove resolved pending operations
      mergeResult.appliedOperations.forEach(op => {
        this.pendingOperations.delete(op.id);
      });

      this.emit('conflicts:resolved', { 
        conflicts, 
        mergeResult,
        strategy: this.config.conflictResolutionStrategy 
      });
    } else {
      // Manual resolution required
      this.emit('conflicts:manual_resolution_required', {
        conflicts: mergeResult.conflicts,
        remoteOperation
      });
    }
  }

  private applyRemoteState(operation: SyncOperation): void {
    // Apply remote state change
    this.emit('state:remote:apply', { operation });
  }

  private handleUserJoin(operation: SyncOperation): void {
    if (!this.currentSession) return;

    const userData = operation.data;
    const user: SyncUser = {
      id: operation.userId,
      name: userData.userName,
      color: this.generateUserColor(),
      lastActivity: Date.now(),
      status: 'active',
      permissions: {
        canEdit: true,
        canView: true,
        canComment: true,
        canInvite: false,
        isOwner: false
      }
    };

    this.currentSession.users.set(operation.userId, user);
    this.emit('user:joined', { user, session: this.currentSession });
  }

  private handleUserLeave(operation: SyncOperation): void {
    if (!this.currentSession) return;

    const user = this.currentSession.users.get(operation.userId);
    if (user) {
      this.currentSession.users.delete(operation.userId);
      this.emit('user:left', { user, session: this.currentSession });
    }
  }

  private handleCursorMove(operation: SyncOperation): void {
    if (!this.currentSession) return;

    const user = this.currentSession.users.get(operation.userId);
    if (user) {
      user.cursor = operation.data.cursor;
      user.lastActivity = Date.now();
      this.emit('user:cursor:updated', { user, cursor: user.cursor });
    }
  }

  private handleSelectionChange(operation: SyncOperation): void {
    if (!this.currentSession) return;

    const user = this.currentSession.users.get(operation.userId);
    if (user) {
      user.selection = operation.data.selection;
      user.lastActivity = Date.now();
      this.emit('user:selection:updated', { user, selection: user.selection });
    }
  }

  // Operation management
  private queueOperation(operation: SyncOperation): void {
    this.operationQueue.push(operation);
    this.pendingOperations.set(operation.id, operation);

    // Cleanup old operations
    if (this.operationQueue.length > this.config.maxOperationQueueSize) {
      const oldOp = this.operationQueue.shift();
      if (oldOp) {
        this.pendingOperations.delete(oldOp.id);
      }
    }
  }

  private acknowledgeOperation(operationId: string): void {
    const operation = this.pendingOperations.get(operationId);
    if (operation) {
      operation.acknowledged = true;
      this.pendingOperations.delete(operationId);
      this.emit('operation:acknowledged', { operation });
    }
  }

  private getOperationDependencies(): string[] {
    // Return IDs of recent operations this operation depends on
    return Array.from(this.pendingOperations.keys()).slice(-5);
  }

  // User interaction broadcasting
  broadcastCursorMove(x: number, y: number): void {
    if (!this.currentSession || !this.currentUser) return;

    const operation: SyncOperation = {
      id: this.generateOperationId(),
      type: 'cursor-move',
      userId: this.currentUser.id,
      timestamp: Date.now(),
      data: {
        cursor: { x, y }
      },
      dependencies: [],
      acknowledged: false,
      retryCount: 0
    };

    this.networkManager.send(operation);
  }

  broadcastSelectionChange(selectedLayerIds: string[]): void {
    if (!this.currentSession || !this.currentUser) return;

    const operation: SyncOperation = {
      id: this.generateOperationId(),
      type: 'selection-change',
      userId: this.currentUser.id,
      timestamp: Date.now(),
      data: {
        selection: selectedLayerIds
      },
      dependencies: [],
      acknowledged: false,
      retryCount: 0
    };

    this.networkManager.send(operation);
  }

  // Network event handlers
  private onNetworkConnected(): void {
    this.emit('sync:connected', { 
      status: this.networkManager.getStatus() 
    });
  }

  private onNetworkDisconnected(): void {
    this.emit('sync:disconnected', { 
      status: this.networkManager.getStatus() 
    });
  }

  private onOperationSent(operation: SyncOperation): void {
    this.emit('operation:sent', { operation });
  }

  // Utility methods
  private extractAffectedLayers(operation: SyncOperation): string[] {
    if (!operation.delta) return [];

    const layerIds = new Set<string>();
    operation.delta.changes.forEach(change => {
      if (change.path.startsWith('layerData.layers.')) {
        const pathParts = change.path.split('.');
        if (pathParts.length >= 3) {
          layerIds.add(pathParts[2]);
        }
      }
    });

    return Array.from(layerIds);
  }

  private assessConflictSeverity(opA: SyncOperation, opB: SyncOperation): ConflictInfo['severity'] {
    const affectedLayersA = this.extractAffectedLayers(opA);
    const affectedLayersB = this.extractAffectedLayers(opB);
    
    const overlappingLayers = affectedLayersA.filter(id => affectedLayersB.includes(id));
    
    if (overlappingLayers.length === 0) return 'low';
    if (overlappingLayers.length <= 2) return 'medium';
    return 'high';
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateUserColor(): string {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  // Public API methods
  isEnabled(): boolean {
    return this.config.enabled;
  }

  setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
    this.emit('sync:enabled_changed', { enabled });
  }

  getCurrentSession(): SyncSession | undefined {
    return this.currentSession;
  }

  getCurrentUser(): SyncUser | undefined {
    return this.currentUser;
  }

  getNetworkStatus(): NetworkStatus {
    return this.networkManager.getStatus();
  }

  getSessionUsers(): SyncUser[] {
    return this.currentSession 
      ? Array.from(this.currentSession.users.values())
      : [];
  }

  getPendingOperations(): SyncOperation[] {
    return Array.from(this.pendingOperations.values());
  }

  updateConflictResolutionStrategy(strategy: SyncConfig['conflictResolutionStrategy']): void {
    this.config.conflictResolutionStrategy = strategy;
    this.conflictResolver.setStrategy(strategy);
    this.emit('conflict_resolution:strategy_changed', { strategy });
  }

  // Manual conflict resolution
  resolveConflictManually(conflictId: string, resolution: 'accept-local' | 'accept-remote' | 'merge'): void {
    this.emit('conflict:manual_resolution', { conflictId, resolution });
  }

  // Cleanup
  dispose(): void {
    this.leaveSession();
    this.networkManager.removeAllListeners();
    this.removeAllListeners();
  }
}