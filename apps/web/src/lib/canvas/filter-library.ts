import { FilterShader, FilterCategory, FilterUniform } from './filter-pipeline-engine';

// Professional Filter Library with 20+ filters across categories

export interface FilterPreset {
  id: string;
  name: string;
  category: FilterCategory;
  parameters: Record<string, any>;
  description: string;
  tags: string[];
  thumbnail?: string;
}

export interface FilterGroup {
  category: FilterCategory;
  name: string;
  description: string;
  filters: FilterShader[];
}

// Shader source constants
const VERTEX_SHADER_SOURCE = `
  attribute vec2 a_position;
  attribute vec2 a_texCoord;
  varying vec2 v_texCoord;
  
  void main() {
    gl_Position = vec4(a_position, 0.0, 1.0);
    v_texCoord = a_texCoord;
  }
`;

// Color Filters
const COLOR_FILTERS: FilterShader[] = [
  {
    id: 'brightness-contrast',
    name: 'Brightness/Contrast',
    category: 'color',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_brightness;
      uniform float u_contrast;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec4 color = texture2D(u_image, v_texCoord);
        
        // Apply brightness
        color.rgb += u_brightness;
        
        // Apply contrast
        color.rgb = (color.rgb - 0.5) * u_contrast + 0.5;
        
        gl_FragColor = vec4(color.rgb, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_brightness: {
        type: 'float',
        value: 0.0,
        min: -1.0,
        max: 1.0,
        step: 0.01,
        description: 'Brightness adjustment'
      },
      u_contrast: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 3.0,
        step: 0.01,
        description: 'Contrast adjustment'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  },

  {
    id: 'hue-saturation',
    name: 'Hue/Saturation',
    category: 'color',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_hue;
      uniform float u_saturation;
      uniform float u_lightness;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      vec3 rgb2hsv(vec3 c) {
        vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
        vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
        vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
        float d = q.x - min(q.w, q.y);
        float e = 1.0e-10;
        return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
      }
      
      vec3 hsv2rgb(vec3 c) {
        vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
        vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
        return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
      }
      
      void main() {
        vec4 color = texture2D(u_image, v_texCoord);
        vec3 hsv = rgb2hsv(color.rgb);
        
        hsv.x += u_hue;
        hsv.y *= u_saturation;
        hsv.z += u_lightness;
        
        hsv.x = mod(hsv.x, 1.0);
        hsv.y = clamp(hsv.y, 0.0, 1.0);
        hsv.z = clamp(hsv.z, 0.0, 1.0);
        
        vec3 rgb = hsv2rgb(hsv);
        gl_FragColor = vec4(rgb, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_hue: {
        type: 'float',
        value: 0.0,
        min: -0.5,
        max: 0.5,
        step: 0.01,
        description: 'Hue shift'
      },
      u_saturation: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 2.0,
        step: 0.01,
        description: 'Saturation multiplier'
      },
      u_lightness: {
        type: 'float',
        value: 0.0,
        min: -0.5,
        max: 0.5,
        step: 0.01,
        description: 'Lightness adjustment'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  },

  {
    id: 'color-balance',
    name: 'Color Balance',
    category: 'color',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_shadows;
      uniform float u_midtones;
      uniform float u_highlights;
      uniform vec3 u_shadowColor;
      uniform vec3 u_midtoneColor;
      uniform vec3 u_highlightColor;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec4 color = texture2D(u_image, v_texCoord);
        float luminance = dot(color.rgb, vec3(0.299, 0.587, 0.114));
        
        vec3 shadows = mix(color.rgb, u_shadowColor, u_shadows * (1.0 - smoothstep(0.0, 0.3, luminance)));
        vec3 midtones = mix(shadows, u_midtoneColor, u_midtones * (1.0 - abs(luminance - 0.5) * 2.0));
        vec3 highlights = mix(midtones, u_highlightColor, u_highlights * smoothstep(0.7, 1.0, luminance));
        
        gl_FragColor = vec4(highlights, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_shadows: {
        type: 'float',
        value: 0.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Shadow intensity'
      },
      u_midtones: {
        type: 'float',
        value: 0.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Midtone intensity'
      },
      u_highlights: {
        type: 'float',
        value: 0.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Highlight intensity'
      },
      u_shadowColor: {
        type: 'vec3',
        value: [1.0, 0.8, 0.6],
        description: 'Shadow color tint'
      },
      u_midtoneColor: {
        type: 'vec3',
        value: [1.0, 1.0, 1.0],
        description: 'Midtone color tint'
      },
      u_highlightColor: {
        type: 'vec3',
        value: [0.8, 0.9, 1.0],
        description: 'Highlight color tint'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  },

  {
    id: 'curves',
    name: 'Curves',
    category: 'color',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_shadows;
      uniform float u_midtones;
      uniform float u_highlights;
      uniform float u_blackPoint;
      uniform float u_whitePoint;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      vec3 applyLevels(vec3 color, float blackPoint, float whitePoint, float gamma) {
        color = clamp((color - blackPoint) / (whitePoint - blackPoint), 0.0, 1.0);
        return pow(color, vec3(1.0 / gamma));
      }
      
      void main() {
        vec4 color = texture2D(u_image, v_texCoord);
        
        // Apply levels adjustment
        vec3 adjusted = applyLevels(color.rgb, u_blackPoint, u_whitePoint, 1.0 + u_midtones);
        
        // Apply shadow/highlight adjustments
        float luminance = dot(adjusted, vec3(0.299, 0.587, 0.114));
        float shadowMask = 1.0 - smoothstep(0.0, 0.5, luminance);
        float highlightMask = smoothstep(0.5, 1.0, luminance);
        
        adjusted = mix(adjusted, adjusted * (1.0 + u_shadows), shadowMask);
        adjusted = mix(adjusted, adjusted * (1.0 + u_highlights), highlightMask);
        
        gl_FragColor = vec4(adjusted, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_shadows: {
        type: 'float',
        value: 0.0,
        min: -1.0,
        max: 1.0,
        step: 0.01,
        description: 'Shadow adjustment'
      },
      u_midtones: {
        type: 'float',
        value: 0.0,
        min: -1.0,
        max: 1.0,
        step: 0.01,
        description: 'Midtone gamma'
      },
      u_highlights: {
        type: 'float',
        value: 0.0,
        min: -1.0,
        max: 1.0,
        step: 0.01,
        description: 'Highlight adjustment'
      },
      u_blackPoint: {
        type: 'float',
        value: 0.0,
        min: 0.0,
        max: 0.3,
        step: 0.01,
        description: 'Black point'
      },
      u_whitePoint: {
        type: 'float',
        value: 1.0,
        min: 0.7,
        max: 1.0,
        step: 0.01,
        description: 'White point'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  }
];

// Blur Filters
const BLUR_FILTERS: FilterShader[] = [
  {
    id: 'gaussian-blur',
    name: 'Gaussian Blur',
    category: 'blur',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform vec2 u_resolution;
      uniform float u_radius;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec2 texelSize = 1.0 / u_resolution;
        vec4 result = vec4(0.0);
        float total = 0.0;
        
        for (float x = -4.0; x <= 4.0; x += 1.0) {
          for (float y = -4.0; y <= 4.0; y += 1.0) {
            vec2 offset = vec2(x, y) * texelSize * u_radius;
            float weight = exp(-(x*x + y*y) / (2.0 * u_radius * u_radius));
            result += texture2D(u_image, v_texCoord + offset) * weight;
            total += weight;
          }
        }
        
        result /= total;
        gl_FragColor = vec4(result.rgb, result.a * u_opacity);
      }
    `,
    uniforms: {
      u_radius: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 20.0,
        step: 0.1,
        description: 'Blur radius'
      },
      u_resolution: {
        type: 'vec2',
        value: [512, 512],
        description: 'Image resolution'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  },

  {
    id: 'motion-blur',
    name: 'Motion Blur',
    category: 'blur',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform vec2 u_resolution;
      uniform float u_angle;
      uniform float u_distance;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec2 texelSize = 1.0 / u_resolution;
        float radian = u_angle * 3.14159 / 180.0;
        vec2 direction = vec2(cos(radian), sin(radian)) * texelSize * u_distance;
        
        vec4 result = vec4(0.0);
        int samples = int(u_distance + 1.0);
        
        for (int i = 0; i < 20; i++) {
          if (i >= samples) break;
          float t = float(i) / float(samples - 1);
          vec2 offset = direction * (t - 0.5);
          result += texture2D(u_image, v_texCoord + offset);
        }
        
        result /= float(samples);
        gl_FragColor = vec4(result.rgb, result.a * u_opacity);
      }
    `,
    uniforms: {
      u_angle: {
        type: 'float',
        value: 0.0,
        min: 0.0,
        max: 360.0,
        step: 1.0,
        description: 'Motion angle (degrees)'
      },
      u_distance: {
        type: 'float',
        value: 5.0,
        min: 0.0,
        max: 50.0,
        step: 0.5,
        description: 'Motion distance'
      },
      u_resolution: {
        type: 'vec2',
        value: [512, 512],
        description: 'Image resolution'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  },

  {
    id: 'radial-blur',
    name: 'Radial Blur',
    category: 'blur',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform vec2 u_center;
      uniform float u_strength;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec2 toCenter = u_center - v_texCoord;
        float distance = length(toCenter);
        
        vec4 result = vec4(0.0);
        int samples = 8;
        
        for (int i = 0; i < 8; i++) {
          float t = float(i) / float(samples - 1);
          float angle = t * u_strength * distance;
          float cos_angle = cos(angle);
          float sin_angle = sin(angle);
          
          vec2 rotated = vec2(
            toCenter.x * cos_angle - toCenter.y * sin_angle,
            toCenter.x * sin_angle + toCenter.y * cos_angle
          );
          
          vec2 samplePos = u_center - rotated;
          result += texture2D(u_image, samplePos);
        }
        
        result /= float(samples);
        gl_FragColor = vec4(result.rgb, result.a * u_opacity);
      }
    `,
    uniforms: {
      u_center: {
        type: 'vec2',
        value: [0.5, 0.5],
        description: 'Blur center point'
      },
      u_strength: {
        type: 'float',
        value: 0.1,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Blur strength'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  }
];

// Distortion Filters
const DISTORTION_FILTERS: FilterShader[] = [
  {
    id: 'wave-distortion',
    name: 'Wave Distortion',
    category: 'distortion',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_amplitude;
      uniform float u_frequency;
      uniform float u_phase;
      uniform vec2 u_direction;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec2 dir = normalize(u_direction);
        float wave = sin(dot(v_texCoord, dir) * u_frequency + u_phase) * u_amplitude;
        vec2 displaced = v_texCoord + vec2(-dir.y, dir.x) * wave;
        
        vec4 color = texture2D(u_image, displaced);
        gl_FragColor = vec4(color.rgb, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_amplitude: {
        type: 'float',
        value: 0.05,
        min: 0.0,
        max: 0.2,
        step: 0.001,
        description: 'Wave amplitude'
      },
      u_frequency: {
        type: 'float',
        value: 10.0,
        min: 1.0,
        max: 50.0,
        step: 0.5,
        description: 'Wave frequency'
      },
      u_phase: {
        type: 'float',
        value: 0.0,
        min: 0.0,
        max: 6.28,
        step: 0.1,
        description: 'Wave phase'
      },
      u_direction: {
        type: 'vec2',
        value: [1.0, 0.0],
        description: 'Wave direction'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  },

  {
    id: 'lens-distortion',
    name: 'Lens Distortion',
    category: 'distortion',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_strength;
      uniform vec2 u_center;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec2 coord = v_texCoord - u_center;
        float distance = length(coord);
        float radius = distance;
        
        // Barrel/pincushion distortion
        float distortion = 1.0 + u_strength * (radius * radius);
        coord *= distortion;
        coord += u_center;
        
        vec4 color = texture2D(u_image, coord);
        gl_FragColor = vec4(color.rgb, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_strength: {
        type: 'float',
        value: 0.0,
        min: -1.0,
        max: 1.0,
        step: 0.01,
        description: 'Distortion strength'
      },
      u_center: {
        type: 'vec2',
        value: [0.5, 0.5],
        description: 'Distortion center'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  }
];

// Artistic Filters
const ARTISTIC_FILTERS: FilterShader[] = [
  {
    id: 'oil-painting',
    name: 'Oil Painting',
    category: 'artistic',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform vec2 u_resolution;
      uniform float u_radius;
      uniform int u_intensity;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec2 texelSize = 1.0 / u_resolution;
        vec3 result = vec3(0.0);
        float totalWeight = 0.0;
        
        for (float x = -4.0; x <= 4.0; x += 1.0) {
          for (float y = -4.0; y <= 4.0; y += 1.0) {
            vec2 offset = vec2(x, y) * texelSize * u_radius;
            vec3 sample = texture2D(u_image, v_texCoord + offset).rgb;
            
            // Quantize colors for oil painting effect
            sample = floor(sample * float(u_intensity)) / float(u_intensity);
            
            float weight = 1.0 / (1.0 + length(vec2(x, y)));
            result += sample * weight;
            totalWeight += weight;
          }
        }
        
        result /= totalWeight;
        vec4 original = texture2D(u_image, v_texCoord);
        gl_FragColor = vec4(result, original.a * u_opacity);
      }
    `,
    uniforms: {
      u_radius: {
        type: 'float',
        value: 2.0,
        min: 1.0,
        max: 8.0,
        step: 0.5,
        description: 'Brush radius'
      },
      u_intensity: {
        type: 'int',
        value: 8,
        min: 4,
        max: 16,
        step: 1,
        description: 'Color intensity levels'
      },
      u_resolution: {
        type: 'vec2',
        value: [512, 512],
        description: 'Image resolution'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  },

  {
    id: 'watercolor',
    name: 'Watercolor',
    category: 'artistic',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform vec2 u_resolution;
      uniform float u_bleeding;
      uniform float u_paper;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      float random(vec2 st) {
        return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
      }
      
      void main() {
        vec2 texelSize = 1.0 / u_resolution;
        vec4 color = texture2D(u_image, v_texCoord);
        
        // Simulate color bleeding
        vec3 bleeding = vec3(0.0);
        for (float x = -2.0; x <= 2.0; x += 1.0) {
          for (float y = -2.0; y <= 2.0; y += 1.0) {
            vec2 offset = vec2(x, y) * texelSize * u_bleeding;
            vec3 sample = texture2D(u_image, v_texCoord + offset).rgb;
            bleeding += sample * (1.0 - length(vec2(x, y)) / 3.0);
          }
        }
        bleeding /= 25.0;
        
        // Add paper texture
        float noise = random(v_texCoord * u_resolution) * u_paper;
        color.rgb = mix(color.rgb, bleeding, 0.3);
        color.rgb += noise * 0.1;
        
        // Increase contrast slightly
        color.rgb = (color.rgb - 0.5) * 1.2 + 0.5;
        
        gl_FragColor = vec4(color.rgb, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_bleeding: {
        type: 'float',
        value: 2.0,
        min: 0.0,
        max: 5.0,
        step: 0.1,
        description: 'Color bleeding amount'
      },
      u_paper: {
        type: 'float',
        value: 0.1,
        min: 0.0,
        max: 0.5,
        step: 0.01,
        description: 'Paper texture intensity'
      },
      u_resolution: {
        type: 'vec2',
        value: [512, 512],
        description: 'Image resolution'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  }
];

// Noise Filters
const NOISE_FILTERS: FilterShader[] = [
  {
    id: 'film-grain',
    name: 'Film Grain',
    category: 'noise',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_amount;
      uniform float u_size;
      uniform float u_time;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      float random(vec2 st, float seed) {
        return fract(sin(dot(st.xy + seed, vec2(12.9898, 78.233))) * 43758.5453123);
      }
      
      void main() {
        vec4 color = texture2D(u_image, v_texCoord);
        
        // Generate grain
        vec2 grainCoord = v_texCoord * u_size;
        float grain = random(grainCoord, u_time) * 2.0 - 1.0;
        
        // Apply grain based on luminance
        float luminance = dot(color.rgb, vec3(0.299, 0.587, 0.114));
        float grainIntensity = luminance * (1.0 - luminance) * 4.0; // Peak at mid-tones
        
        color.rgb += grain * u_amount * grainIntensity;
        gl_FragColor = vec4(color.rgb, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_amount: {
        type: 'float',
        value: 0.1,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Grain amount'
      },
      u_size: {
        type: 'float',
        value: 100.0,
        min: 10.0,
        max: 500.0,
        step: 10.0,
        description: 'Grain size'
      },
      u_time: {
        type: 'float',
        value: 0.0,
        min: 0.0,
        max: 1000.0,
        step: 1.0,
        description: 'Animation time'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  }
];

// Edge Detection Filters
const EDGE_FILTERS: FilterShader[] = [
  {
    id: 'edge-detection',
    name: 'Edge Detection',
    category: 'edge',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform vec2 u_resolution;
      uniform float u_threshold;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec2 texelSize = 1.0 / u_resolution;
        
        // Sobel operators
        vec3 horizontal = vec3(0.0);
        vec3 vertical = vec3(0.0);
        
        // Sample surrounding pixels
        for (int x = -1; x <= 1; x++) {
          for (int y = -1; y <= 1; y++) {
            vec2 offset = vec2(float(x), float(y)) * texelSize;
            vec3 sample = texture2D(u_image, v_texCoord + offset).rgb;
            
            // Apply Sobel kernel
            float hWeight = float(x);
            float vWeight = float(y);
            
            horizontal += sample * hWeight;
            vertical += sample * vWeight;
          }
        }
        
        // Calculate edge magnitude
        float magnitude = length(horizontal) + length(vertical);
        magnitude = smoothstep(u_threshold, u_threshold + 0.1, magnitude);
        
        vec4 original = texture2D(u_image, v_texCoord);
        gl_FragColor = vec4(vec3(magnitude), original.a * u_opacity);
      }
    `,
    uniforms: {
      u_threshold: {
        type: 'float',
        value: 0.1,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Edge threshold'
      },
      u_resolution: {
        type: 'vec2',
        value: [512, 512],
        description: 'Image resolution'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  }
];

// Lighting Filters
const LIGHTING_FILTERS: FilterShader[] = [
  {
    id: 'vignette',
    name: 'Vignette',
    category: 'lighting',
    vertexSource: VERTEX_SHADER_SOURCE,
    fragmentSource: `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_intensity;
      uniform float u_radius;
      uniform vec2 u_center;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec4 color = texture2D(u_image, v_texCoord);
        
        vec2 position = v_texCoord - u_center;
        float distance = length(position);
        float vignette = smoothstep(u_radius, u_radius - 0.3, distance);
        vignette = mix(1.0 - u_intensity, 1.0, vignette);
        
        color.rgb *= vignette;
        gl_FragColor = vec4(color.rgb, color.a * u_opacity);
      }
    `,
    uniforms: {
      u_intensity: {
        type: 'float',
        value: 0.5,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Vignette intensity'
      },
      u_radius: {
        type: 'float',
        value: 0.8,
        min: 0.1,
        max: 1.5,
        step: 0.01,
        description: 'Vignette radius'
      },
      u_center: {
        type: 'vec2',
        value: [0.5, 0.5],
        description: 'Vignette center'
      },
      u_opacity: {
        type: 'float',
        value: 1.0,
        min: 0.0,
        max: 1.0,
        step: 0.01,
        description: 'Filter opacity'
      }
    },
    attributes: {
      a_position: { name: 'a_position', type: 'vec2', normalized: false },
      a_texCoord: { name: 'a_texCoord', type: 'vec2', normalized: false }
    }
  }
];

// Export organized filter library
export class FilterLibrary {
  private static instance: FilterLibrary;
  private filterGroups: Map<FilterCategory, FilterGroup> = new Map();
  private allFilters: Map<string, FilterShader> = new Map();

  private constructor() {
    this.initializeFilters();
  }

  static getInstance(): FilterLibrary {
    if (!FilterLibrary.instance) {
      FilterLibrary.instance = new FilterLibrary();
    }
    return FilterLibrary.instance;
  }

  private initializeFilters(): void {
    // Organize filters by category
    const groups = [
      {
        category: 'color' as FilterCategory,
        name: 'Color Adjustments',
        description: 'Filters for color correction and enhancement',
        filters: COLOR_FILTERS
      },
      {
        category: 'blur' as FilterCategory,
        name: 'Blur Effects',
        description: 'Various blur and motion effects',
        filters: BLUR_FILTERS
      },
      {
        category: 'distortion' as FilterCategory,
        name: 'Distortion Effects',
        description: 'Geometric and perspective distortions',
        filters: DISTORTION_FILTERS
      },
      {
        category: 'artistic' as FilterCategory,
        name: 'Artistic Effects',
        description: 'Creative artistic transformations',
        filters: ARTISTIC_FILTERS
      },
      {
        category: 'noise' as FilterCategory,
        name: 'Noise & Texture',
        description: 'Noise, grain, and texture effects',
        filters: NOISE_FILTERS
      },
      {
        category: 'edge' as FilterCategory,
        name: 'Edge Detection',
        description: 'Edge enhancement and detection',
        filters: EDGE_FILTERS
      },
      {
        category: 'lighting' as FilterCategory,
        name: 'Lighting Effects',
        description: 'Lighting and atmosphere effects',
        filters: LIGHTING_FILTERS
      }
    ];

    groups.forEach(group => {
      this.filterGroups.set(group.category, group);
      group.filters.forEach(filter => {
        this.allFilters.set(filter.id, filter);
      });
    });
  }

  getFilterById(id: string): FilterShader | undefined {
    return this.allFilters.get(id);
  }

  getFiltersByCategory(category: FilterCategory): FilterShader[] {
    const group = this.filterGroups.get(category);
    return group ? group.filters : [];
  }

  getAllCategories(): FilterCategory[] {
    return Array.from(this.filterGroups.keys());
  }

  getFilterGroup(category: FilterCategory): FilterGroup | undefined {
    return this.filterGroups.get(category);
  }

  getAllFilters(): FilterShader[] {
    return Array.from(this.allFilters.values());
  }

  searchFilters(query: string): FilterShader[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getAllFilters().filter(filter =>
      filter.name.toLowerCase().includes(lowercaseQuery) ||
      filter.category.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Preset management
  getFilterPresets(): FilterPreset[] {
    return [
      {
        id: 'vintage',
        name: 'Vintage Look',
        category: 'color',
        parameters: {
          u_brightness: -0.1,
          u_contrast: 1.2,
          u_saturation: 0.8,
          u_hue: 0.05
        },
        description: 'Classic vintage photo effect',
        tags: ['vintage', 'retro', 'classic']
      },
      {
        id: 'dramatic',
        name: 'Dramatic',
        category: 'color',
        parameters: {
          u_contrast: 1.5,
          u_shadows: 0.3,
          u_highlights: -0.2,
          u_saturation: 1.3
        },
        description: 'High contrast dramatic look',
        tags: ['dramatic', 'contrast', 'bold']
      },
      {
        id: 'soft-glow',
        name: 'Soft Glow',
        category: 'blur',
        parameters: {
          u_radius: 3.0,
          u_opacity: 0.3
        },
        description: 'Soft dreamy glow effect',
        tags: ['soft', 'glow', 'dreamy']
      }
    ];
  }

  createFilterPreset(
    id: string,
    name: string,
    category: FilterCategory,
    parameters: Record<string, any>,
    description: string,
    tags: string[] = []
  ): FilterPreset {
    return {
      id,
      name,
      category,
      parameters,
      description,
      tags
    };
  }
}