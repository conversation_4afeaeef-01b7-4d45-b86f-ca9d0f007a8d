import { EventEmitter } from 'events';
import { CanvasStateManager, StateSnapshot } from './canvas-state-manager';

export interface AutoSaveConfig {
  enabled: boolean;
  interval: number; // milliseconds
  maxAutoSaves: number;
  storageQuota: number; // MB
  enableChangeDetection: boolean;
  enableCrashRecovery: boolean;
  enableProgressIndication: boolean;
  compressionEnabled: boolean;
  localStorageKey: string;
  debounceDelay: number; // milliseconds
}

export interface SavedProject {
  id: string;
  name: string;
  timestamp: number;
  size: number;
  version: string;
  isAutoSave: boolean;
  thumbnail?: string;
  metadata: {
    deviceInfo: string;
    userAgent: string;
    sessionId: string;
    changeCount: number;
    lastActivity: number;
  };
  state: any; // Compressed or raw state data
}

export interface RecoverySession {
  sessionId: string;
  timestamp: number;
  projectId: string;
  unsavedChanges: StateSnapshot[];
  activityLog: ActivityLogEntry[];
  crashInfo?: CrashInfo;
}

export interface ActivityLogEntry {
  timestamp: number;
  action: string;
  details: any;
  snapshotId?: string;
}

export interface CrashInfo {
  timestamp: number;
  error: string;
  stackTrace: string;
  browserInfo: string;
  lastAction: string;
}

export interface AutoSaveStatus {
  enabled: boolean;
  lastSaveTime: number;
  nextSaveTime: number;
  saveInProgress: boolean;
  changesSinceLastSave: number;
  storageUsed: number;
  storageQuota: number;
  recoverySessionId?: string;
}

export interface SaveProgress {
  stage: 'preparing' | 'compressing' | 'saving' | 'verifying' | 'complete' | 'error';
  progress: number; // 0-100
  message: string;
  estimatedTimeRemaining?: number;
}

// Local storage manager with compression and cleanup
export class LocalStorageManager {
  private compressionEnabled: boolean;

  constructor(compressionEnabled: boolean = true) {
    this.compressionEnabled = compressionEnabled;
  }

  save(key: string, data: any): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        let serializedData = JSON.stringify(data);
        
        if (this.compressionEnabled) {
          serializedData = this.compress(serializedData);
        }

        localStorage.setItem(key, serializedData);
        resolve(true);
      } catch (error) {
        console.error('Failed to save to localStorage:', error);
        resolve(false);
      }
    });
  }

  load(key: string): Promise<any | null> {
    return new Promise((resolve) => {
      try {
        const data = localStorage.getItem(key);
        if (!data) {
          resolve(null);
          return;
        }

        let parsedData = data;
        if (this.compressionEnabled) {
          parsedData = this.decompress(data);
        }

        resolve(JSON.parse(parsedData));
      } catch (error) {
        console.error('Failed to load from localStorage:', error);
        resolve(null);
      }
    });
  }

  remove(key: string): void {
    localStorage.removeItem(key);
  }

  getSize(key: string): number {
    const data = localStorage.getItem(key);
    return data ? data.length : 0;
  }

  getTotalUsage(): number {
    let total = 0;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        total += this.getSize(key);
      }
    }
    return total;
  }

  getAvailableSpace(): number {
    // Estimate available localStorage space (usually 5-10MB)
    const testKey = '__storage_test__';
    let size = 0;
    
    try {
      // Test storage by adding increasingly large strings
      for (let i = 0; i < 1000; i++) {
        const data = 'x'.repeat(1024); // 1KB chunks
        localStorage.setItem(testKey, data.repeat(i));
        size = data.repeat(i).length;
      }
    } catch (e) {
      localStorage.removeItem(testKey);
    }
    
    return size;
  }

  cleanup(retainKeys: string[] = []): number {
    let cleaned = 0;
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && !retainKeys.includes(key) && key.startsWith('canvas_auto_save_')) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => {
      cleaned += this.getSize(key);
      localStorage.removeItem(key);
    });

    return cleaned;
  }

  private compress(data: string): string {
    // Simple compression - in production would use proper compression library
    try {
      return btoa(encodeURIComponent(data));
    } catch {
      return data; // Fallback to uncompressed if compression fails
    }
  }

  private decompress(data: string): string {
    try {
      return decodeURIComponent(atob(data));
    } catch {
      return data; // Assume uncompressed if decompression fails
    }
  }
}

// Change detection system
export class ChangeDetector {
  private lastStateHash: string = '';
  private changeCount: number = 0;
  private significantChangeThreshold: number = 5;

  detectChanges(currentStateHash: string): boolean {
    if (this.lastStateHash !== currentStateHash) {
      this.lastStateHash = currentStateHash;
      this.changeCount++;
      return true;
    }
    return false;
  }

  hasSignificantChanges(): boolean {
    return this.changeCount >= this.significantChangeThreshold;
  }

  resetChangeCount(): void {
    this.changeCount = 0;
  }

  getChangeCount(): number {
    return this.changeCount;
  }
}

// Progress indication system
export class ProgressIndicator extends EventEmitter {
  private currentProgress: SaveProgress = {
    stage: 'complete',
    progress: 100,
    message: 'Ready'
  };

  startProgress(message: string = 'Starting save...'): void {
    this.currentProgress = {
      stage: 'preparing',
      progress: 0,
      message
    };
    this.emit('progress:started', this.currentProgress);
  }

  updateProgress(
    stage: SaveProgress['stage'], 
    progress: number, 
    message: string,
    estimatedTimeRemaining?: number
  ): void {
    this.currentProgress = {
      stage,
      progress: Math.max(0, Math.min(100, progress)),
      message,
      estimatedTimeRemaining
    };
    this.emit('progress:updated', this.currentProgress);
  }

  completeProgress(message: string = 'Save complete'): void {
    this.currentProgress = {
      stage: 'complete',
      progress: 100,
      message
    };
    this.emit('progress:completed', this.currentProgress);
  }

  errorProgress(message: string, error?: Error): void {
    this.currentProgress = {
      stage: 'error',
      progress: 0,
      message
    };
    this.emit('progress:error', { progress: this.currentProgress, error });
  }

  getCurrentProgress(): SaveProgress {
    return { ...this.currentProgress };
  }
}

// Advanced Auto-save Manager with crash recovery
export class AutoSaveManager extends EventEmitter {
  private stateManager: CanvasStateManager;
  private config: AutoSaveConfig;
  private storage: LocalStorageManager;
  private changeDetector: ChangeDetector;
  private progressIndicator: ProgressIndicator;
  private activityLog: ActivityLogEntry[] = [];
  
  private saveTimer?: NodeJS.Timeout;
  private lastSaveTime: number = 0;
  private saveInProgress: boolean = false;
  private sessionId: string;
  private currentProjectId?: string;
  private unsavedChanges: StateSnapshot[] = [];
  private paused: boolean = false;

  constructor(stateManager: CanvasStateManager, config: Partial<AutoSaveConfig> = {}) {
    super();
    
    this.stateManager = stateManager;
    this.config = this.mergeDefaultConfig(config);
    this.storage = new LocalStorageManager(this.config.compressionEnabled);
    this.changeDetector = new ChangeDetector();
    this.progressIndicator = new ProgressIndicator();
    this.sessionId = this.generateSessionId();

    this.setupEventHandlers();
    this.initializeRecoverySystem();
    
    if (this.config.enabled) {
      this.start();
    }
  }

  private mergeDefaultConfig(config: Partial<AutoSaveConfig>): AutoSaveConfig {
    return {
      enabled: true,
      interval: 30000, // 30 seconds
      maxAutoSaves: 10,
      storageQuota: 50, // 50MB
      enableChangeDetection: true,
      enableCrashRecovery: true,
      enableProgressIndication: true,
      compressionEnabled: true,
      localStorageKey: 'canvas_auto_save',
      debounceDelay: 2000, // 2 seconds
      ...config
    };
  }

  private setupEventHandlers(): void {
    // Listen to state manager events
    this.stateManager.on('state:captured', (data) => {
      this.onStateChanged(data.snapshot);
    });

    // Listen to progress indicator events
    this.progressIndicator.on('progress:started', (progress) => {
      this.emit('save:progress', progress);
    });

    this.progressIndicator.on('progress:updated', (progress) => {
      this.emit('save:progress', progress);
    });

    this.progressIndicator.on('progress:completed', (progress) => {
      this.emit('save:progress', progress);
    });

    this.progressIndicator.on('progress:error', (data) => {
      this.emit('save:error', data);
    });

    // Listen for page visibility changes to pause/resume
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pause();
      } else {
        this.resume();
      }
    });

    // Listen for beforeunload to perform emergency save
    window.addEventListener('beforeunload', (e) => {
      this.performEmergencySave();
    });

    // Listen for unhandled errors for crash recovery
    window.addEventListener('error', (e) => {
      this.handleCrash(e.error, e.message, e.filename, e.lineno);
    });

    window.addEventListener('unhandledrejection', (e) => {
      this.handleCrash(e.reason, 'Unhandled Promise Rejection');
    });
  }

  private initializeRecoverySystem(): void {
    if (!this.config.enableCrashRecovery) return;

    // Check for previous crash
    this.checkForCrashRecovery();

    // Initialize activity tracking
    this.logActivity('session:started', { sessionId: this.sessionId });
  }

  // Core auto-save functionality
  start(): void {
    if (!this.config.enabled || this.saveTimer) return;

    this.logActivity('auto_save:started', { interval: this.config.interval });
    
    this.saveTimer = setInterval(() => {
      if (!this.paused && !this.saveInProgress) {
        this.performAutoSave();
      }
    }, this.config.interval);

    this.emit('auto_save:started', { config: this.config });
  }

  stop(): void {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
      this.saveTimer = undefined;
    }

    this.logActivity('auto_save:stopped', {});
    this.emit('auto_save:stopped');
  }

  pause(): void {
    this.paused = true;
    this.logActivity('auto_save:paused', {});
    this.emit('auto_save:paused');
  }

  resume(): void {
    this.paused = false;
    this.logActivity('auto_save:resumed', {});
    this.emit('auto_save:resumed');
  }

  private async performAutoSave(): Promise<void> {
    if (!this.shouldPerformAutoSave()) return;

    this.saveInProgress = true;
    
    try {
      if (this.config.enableProgressIndication) {
        this.progressIndicator.startProgress('Preparing auto-save...');
      }

      const snapshot = this.stateManager.getCurrentSnapshot();
      if (!snapshot) {
        this.saveInProgress = false;
        return;
      }

      await this.saveSnapshot(snapshot, true);
      
      this.lastSaveTime = Date.now();
      this.changeDetector.resetChangeCount();
      this.unsavedChanges = [];

      if (this.config.enableProgressIndication) {
        this.progressIndicator.completeProgress('Auto-save complete');
      }

      this.emit('auto_save:completed', { 
        timestamp: this.lastSaveTime,
        snapshotId: snapshot.id
      });

    } catch (error) {
      console.error('Auto-save failed:', error);
      
      if (this.config.enableProgressIndication) {
        this.progressIndicator.errorProgress('Auto-save failed', error as Error);
      }

      this.emit('auto_save:failed', { error, timestamp: Date.now() });
    } finally {
      this.saveInProgress = false;
    }
  }

  private shouldPerformAutoSave(): boolean {
    if (!this.config.enableChangeDetection) return true;

    // Check if there are significant changes
    const currentSnapshot = this.stateManager.getCurrentSnapshot();
    if (!currentSnapshot) return false;

    const stateHash = currentSnapshot.state.checksums.combined;
    const hasChanges = this.changeDetector.detectChanges(stateHash);
    
    return hasChanges && this.changeDetector.hasSignificantChanges();
  }

  // Manual save functionality
  async saveProject(name: string, isManual: boolean = true): Promise<SavedProject | null> {
    if (this.saveInProgress) {
      throw new Error('Save already in progress');
    }

    this.saveInProgress = true;

    try {
      if (this.config.enableProgressIndication) {
        this.progressIndicator.startProgress('Saving project...');
      }

      const snapshot = this.stateManager.getCurrentSnapshot();
      if (!snapshot) {
        throw new Error('No current state to save');
      }

      const project = await this.createSavedProject(snapshot, name, !isManual);
      
      await this.saveProjectToStorage(project);
      
      if (isManual) {
        this.currentProjectId = project.id;
        this.lastSaveTime = Date.now();
        this.changeDetector.resetChangeCount();
        this.unsavedChanges = [];
      }

      if (this.config.enableProgressIndication) {
        this.progressIndicator.completeProgress('Project saved successfully');
      }

      this.logActivity('project:saved', { 
        projectId: project.id, 
        name: project.name,
        isManual 
      });

      this.emit('project:saved', { project, isManual });
      
      return project;

    } catch (error) {
      if (this.config.enableProgressIndication) {
        this.progressIndicator.errorProgress('Save failed', error as Error);
      }

      this.emit('save:error', { error });
      throw error;
    } finally {
      this.saveInProgress = false;
    }
  }

  private async saveSnapshot(snapshot: StateSnapshot, isAutoSave: boolean): Promise<void> {
    const project = await this.createSavedProject(snapshot, `Auto-save ${new Date().toLocaleTimeString()}`, isAutoSave);
    await this.saveProjectToStorage(project);
    
    if (isAutoSave) {
      await this.cleanupOldAutoSaves();
    }
  }

  private async createSavedProject(snapshot: StateSnapshot, name: string, isAutoSave: boolean): Promise<SavedProject> {
    const projectId = `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    if (this.config.enableProgressIndication) {
      this.progressIndicator.updateProgress('preparing', 20, 'Preparing project data...');
    }

    const project: SavedProject = {
      id: projectId,
      name,
      timestamp: Date.now(),
      size: 0, // Will be calculated after compression
      version: '1.0',
      isAutoSave,
      metadata: {
        deviceInfo: this.getDeviceInfo(),
        userAgent: navigator.userAgent,
        sessionId: this.sessionId,
        changeCount: this.changeDetector.getChangeCount(),
        lastActivity: Date.now()
      },
      state: snapshot.state
    };

    if (this.config.enableProgressIndication) {
      this.progressIndicator.updateProgress('compressing', 50, 'Compressing project data...');
    }

    // Calculate size
    const serializedState = JSON.stringify(project.state);
    project.size = serializedState.length;

    // Generate thumbnail if possible
    if (this.canGenerateThumbnail()) {
      project.thumbnail = await this.generateThumbnail();
    }

    if (this.config.enableProgressIndication) {
      this.progressIndicator.updateProgress('preparing', 80, 'Finalizing project...');
    }

    return project;
  }

  private async saveProjectToStorage(project: SavedProject): Promise<void> {
    const storageKey = `${this.config.localStorageKey}_${project.id}`;
    
    if (this.config.enableProgressIndication) {
      this.progressIndicator.updateProgress('saving', 90, 'Saving to storage...');
    }

    // Check storage quota
    const currentUsage = this.storage.getTotalUsage();
    const projectSize = project.size;
    const quotaBytes = this.config.storageQuota * 1024 * 1024;

    if (currentUsage + projectSize > quotaBytes) {
      await this.freeUpStorage(projectSize);
    }

    const success = await this.storage.save(storageKey, project);
    
    if (!success) {
      throw new Error('Failed to save project to storage');
    }

    // Update project index
    await this.updateProjectIndex(project);

    if (this.config.enableProgressIndication) {
      this.progressIndicator.updateProgress('verifying', 95, 'Verifying save...');
    }

    // Verify save
    const saved = await this.storage.load(storageKey);
    if (!saved) {
      throw new Error('Save verification failed');
    }
  }

  // Project loading functionality
  async loadProject(projectId: string): Promise<boolean> {
    try {
      const storageKey = `${this.config.localStorageKey}_${projectId}`;
      const project = await this.storage.load(storageKey);

      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      // Restore state
      await this.restoreProjectState(project);
      
      this.currentProjectId = projectId;
      this.lastSaveTime = project.timestamp;
      this.changeDetector.resetChangeCount();
      this.unsavedChanges = [];

      this.logActivity('project:loaded', { projectId, name: project.name });
      this.emit('project:loaded', { project });

      return true;

    } catch (error) {
      console.error('Failed to load project:', error);
      this.emit('load:error', { error, projectId });
      return false;
    }
  }

  private async restoreProjectState(project: SavedProject): Promise<void> {
    // This would require coordination with the state manager
    // For now, emit an event for the state manager to handle
    this.emit('state:restore:requested', { 
      state: project.state,
      project 
    });
  }

  // Project management
  async getAllProjects(): Promise<SavedProject[]> {
    const projects: SavedProject[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.config.localStorageKey)) {
        const project = await this.storage.load(key);
        if (project) {
          projects.push(project);
        }
      }
    }

    return projects.sort((a, b) => b.timestamp - a.timestamp);
  }

  async deleteProject(projectId: string): Promise<boolean> {
    try {
      const storageKey = `${this.config.localStorageKey}_${projectId}`;
      this.storage.remove(storageKey);
      
      await this.removeFromProjectIndex(projectId);
      
      this.logActivity('project:deleted', { projectId });
      this.emit('project:deleted', { projectId });

      return true;
    } catch (error) {
      console.error('Failed to delete project:', error);
      return false;
    }
  }

  // Crash recovery system
  private async checkForCrashRecovery(): Promise<void> {
    const recoveryKey = `${this.config.localStorageKey}_recovery_${this.sessionId}`;
    const recoveryData = await this.storage.load(recoveryKey);

    if (recoveryData) {
      this.emit('crash:recovery:available', { 
        recoverySession: recoveryData,
        sessionId: this.sessionId 
      });
    }
  }

  private handleCrash(error: any, message: string, filename?: string, lineno?: number): void {
    if (!this.config.enableCrashRecovery) return;

    const crashInfo: CrashInfo = {
      timestamp: Date.now(),
      error: error?.toString() || message,
      stackTrace: error?.stack || '',
      browserInfo: navigator.userAgent,
      lastAction: this.getLastActivity()?.action || 'unknown'
    };

    const recoverySession: RecoverySession = {
      sessionId: this.sessionId,
      timestamp: Date.now(),
      projectId: this.currentProjectId || 'unknown',
      unsavedChanges: this.unsavedChanges,
      activityLog: this.activityLog.slice(-20), // Keep last 20 activities
      crashInfo
    };

    const recoveryKey = `${this.config.localStorageKey}_recovery_${this.sessionId}`;
    this.storage.save(recoveryKey, recoverySession);

    this.emit('crash:detected', { crashInfo, recoverySession });
  }

  async recoverFromCrash(sessionId: string): Promise<boolean> {
    try {
      const recoveryKey = `${this.config.localStorageKey}_recovery_${sessionId}`;
      const recoverySession = await this.storage.load(recoveryKey);

      if (!recoverySession) return false;

      // Restore unsaved changes
      for (const snapshot of recoverySession.unsavedChanges) {
        // This would require state manager integration
        this.emit('state:restore:requested', { state: snapshot.state });
      }

      // Clean up recovery data
      this.storage.remove(recoveryKey);

      this.emit('crash:recovery:completed', { recoverySession });
      return true;

    } catch (error) {
      console.error('Crash recovery failed:', error);
      this.emit('crash:recovery:failed', { error, sessionId });
      return false;
    }
  }

  private performEmergencySave(): void {
    if (this.saveInProgress || !this.stateManager.getCurrentSnapshot()) return;

    try {
      const snapshot = this.stateManager.getCurrentSnapshot();
      if (snapshot) {
        const emergencyKey = `${this.config.localStorageKey}_emergency_${Date.now()}`;
        this.storage.save(emergencyKey, {
          snapshot,
          timestamp: Date.now(),
          sessionId: this.sessionId
        });
      }
    } catch (error) {
      console.error('Emergency save failed:', error);
    }
  }

  // Storage management
  private async freeUpStorage(requiredSpace: number): Promise<void> {
    const autoSaveProjects = await this.getAllAutoSaveProjects();
    
    // Sort by timestamp (oldest first)
    autoSaveProjects.sort((a, b) => a.timestamp - b.timestamp);

    let freedSpace = 0;
    const projectsToDelete = [];

    for (const project of autoSaveProjects) {
      if (freedSpace >= requiredSpace) break;
      projectsToDelete.push(project);
      freedSpace += project.size;
    }

    for (const project of projectsToDelete) {
      await this.deleteProject(project.id);
    }
  }

  private async getAllAutoSaveProjects(): Promise<SavedProject[]> {
    const allProjects = await this.getAllProjects();
    return allProjects.filter(p => p.isAutoSave);
  }

  private async cleanupOldAutoSaves(): Promise<void> {
    const autoSaves = await this.getAllAutoSaveProjects();
    
    if (autoSaves.length <= this.config.maxAutoSaves) return;

    // Sort by timestamp (oldest first)
    autoSaves.sort((a, b) => a.timestamp - b.timestamp);
    
    // Delete excess auto-saves
    const toDelete = autoSaves.slice(0, autoSaves.length - this.config.maxAutoSaves);
    
    for (const autoSave of toDelete) {
      await this.deleteProject(autoSave.id);
    }
  }

  // Utility methods
  private onStateChanged(snapshot: StateSnapshot): void {
    this.unsavedChanges.push(snapshot);
    
    // Keep only recent unsaved changes
    if (this.unsavedChanges.length > 10) {
      this.unsavedChanges = this.unsavedChanges.slice(-5);
    }

    this.logActivity('state:changed', { snapshotId: snapshot.id });
  }

  private logActivity(action: string, details: any): void {
    const entry: ActivityLogEntry = {
      timestamp: Date.now(),
      action,
      details,
      snapshotId: this.stateManager.getCurrentSnapshot()?.id
    };

    this.activityLog.push(entry);
    
    // Keep only recent activities
    if (this.activityLog.length > 100) {
      this.activityLog = this.activityLog.slice(-50);
    }
  }

  private getLastActivity(): ActivityLogEntry | undefined {
    return this.activityLog[this.activityLog.length - 1];
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDeviceInfo(): string {
    return `${navigator.platform} - ${navigator.language}`;
  }

  private canGenerateThumbnail(): boolean {
    // Check if canvas can generate thumbnails
    return this.stateManager.canvasManager?.isReady || false;
  }

  private async generateThumbnail(): Promise<string> {
    // This would generate a thumbnail from the canvas
    // For now, return a placeholder
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y0ZjRmNCIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iMC4zZW0iPnByZXZpZXc8L3RleHQ+PC9zdmc+';
  }

  private async updateProjectIndex(project: SavedProject): Promise<void> {
    // Maintain an index of all projects for faster retrieval
    const indexKey = `${this.config.localStorageKey}_index`;
    const index = await this.storage.load(indexKey) || [];
    
    // Remove existing entry if updating
    const existingIndex = index.findIndex((p: any) => p.id === project.id);
    if (existingIndex >= 0) {
      index.splice(existingIndex, 1);
    }

    // Add new entry
    index.push({
      id: project.id,
      name: project.name,
      timestamp: project.timestamp,
      size: project.size,
      isAutoSave: project.isAutoSave
    });

    await this.storage.save(indexKey, index);
  }

  private async removeFromProjectIndex(projectId: string): Promise<void> {
    const indexKey = `${this.config.localStorageKey}_index`;
    const index = await this.storage.load(indexKey) || [];
    
    const filteredIndex = index.filter((p: any) => p.id !== projectId);
    await this.storage.save(indexKey, filteredIndex);
  }

  // Public API methods
  getStatus(): AutoSaveStatus {
    const nextSaveTime = this.lastSaveTime + this.config.interval;
    
    return {
      enabled: this.config.enabled,
      lastSaveTime: this.lastSaveTime,
      nextSaveTime,
      saveInProgress: this.saveInProgress,
      changesSinceLastSave: this.changeDetector.getChangeCount(),
      storageUsed: this.storage.getTotalUsage() / (1024 * 1024), // Convert to MB
      storageQuota: this.config.storageQuota,
      recoverySessionId: this.sessionId
    };
  }

  getCurrentProgress(): SaveProgress {
    return this.progressIndicator.getCurrentProgress();
  }

  getActivityLog(): ActivityLogEntry[] {
    return [...this.activityLog];
  }

  isEnabled(): boolean {
    return this.config.enabled;
  }

  setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
    
    if (enabled) {
      this.start();
    } else {
      this.stop();
    }

    this.emit('auto_save:enabled_changed', { enabled });
  }

  updateConfig(newConfig: Partial<AutoSaveConfig>): void {
    const wasEnabled = this.config.enabled;
    this.config = { ...this.config, ...newConfig };

    if (wasEnabled !== this.config.enabled) {
      this.setEnabled(this.config.enabled);
    }

    this.emit('config:updated', { config: this.config });
  }

  // Cleanup
  dispose(): void {
    this.stop();
    this.logActivity('session:ended', { sessionId: this.sessionId });
    this.removeAllListeners();
    this.progressIndicator.removeAllListeners();
  }
}