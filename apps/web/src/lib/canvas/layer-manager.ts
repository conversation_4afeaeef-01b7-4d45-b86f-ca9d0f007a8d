// Layer Management System
// ✅ Story V1.2a: Layer Management - Multi-layer system with ordering and visibility controls

import { fabric } from 'fabric';
import { EventEmitter } from 'events';

export interface CanvasLayer {
  id: string;
  name: string;
  zIndex: number;
  opacity: number;
  visible: boolean;
  locked: boolean;
  blendMode: string;
  objects: Set<string>; // Object IDs in this layer
  fabricGroup?: fabric.Group;
  thumbnail?: string;
  color?: string; // UI color for layer identification
}

export interface LayerCreateOptions {
  name?: string;
  opacity?: number;
  visible?: boolean;
  locked?: boolean;
  blendMode?: string;
  insertAt?: number; // Z-index position
}

export interface LayerUpdateOptions {
  name?: string;
  opacity?: number;
  visible?: boolean;
  locked?: boolean;
  blendMode?: string;
  zIndex?: number;
}

export class LayerManager extends EventEmitter {
  private canvas!: fabric.Canvas;
  private layers: Map<string, CanvasLayer> = new Map();
  private layerOrder: string[] = []; // Ordered list of layer IDs (bottom to top)
  private activeLayerId: string | null = null;
  private layerColors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', 
    '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];
  private colorIndex = 0;

  constructor() {
    super();
  }

  public initialize(canvas: fabric.Canvas): void {
    this.canvas = canvas;
    this.setupEventListeners();
    
    // Create default layer
    this.createLayer({
      name: 'Background',
      opacity: 1,
      visible: true,
      locked: false,
    });
  }

  // ✅ Layer Creation and Management
  public createLayer(options: LayerCreateOptions = {}): string {
    const id = this.generateLayerId();
    const zIndex = options.insertAt ?? this.layerOrder.length;
    
    const layer: CanvasLayer = {
      id,
      name: options.name || `Layer ${this.layers.size + 1}`,
      zIndex,
      opacity: options.opacity ?? 1,
      visible: options.visible ?? true,
      locked: options.locked ?? false,
      blendMode: options.blendMode || 'normal',
      objects: new Set(),
      color: this.getNextLayerColor(),
    };

    // Create Fabric.js group for the layer
    layer.fabricGroup = new fabric.Group([], {
      selectable: false,
      evented: !layer.locked,
      opacity: layer.opacity,
      visible: layer.visible,
    });

    // Set layer-specific properties
    layer.fabricGroup.set('layerId', id);
    this.canvas.add(layer.fabricGroup);

    // Update layer order
    this.layers.set(id, layer);
    if (options.insertAt !== undefined) {
      this.layerOrder.splice(options.insertAt, 0, id);
      this.reorderAllLayers();
    } else {
      this.layerOrder.push(id);
    }

    // Set as active if it's the first layer or no active layer
    if (!this.activeLayerId || this.layers.size === 1) {
      this.setActiveLayer(id);
    }

    this.updateCanvasLayerOrder();
    this.emit('layer:created', { layer });
    
    return id;
  }

  public deleteLayer(layerId: string): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    // Can't delete the last layer
    if (this.layers.size <= 1) {
      throw new Error('Cannot delete the last layer');
    }

    // Move all objects to the layer below
    const layerIndex = this.layerOrder.indexOf(layerId);
    const targetLayerIndex = Math.max(0, layerIndex - 1);
    const targetLayerId = this.layerOrder[targetLayerIndex === layerIndex ? layerIndex + 1 : targetLayerIndex];

    this.moveObjectsToLayer(Array.from(layer.objects), targetLayerId);

    // Remove from canvas
    if (layer.fabricGroup) {
      this.canvas.remove(layer.fabricGroup);
    }

    // Clean up references
    this.layers.delete(layerId);
    const orderIndex = this.layerOrder.indexOf(layerId);
    if (orderIndex > -1) {
      this.layerOrder.splice(orderIndex, 1);
    }

    // Update active layer if needed
    if (this.activeLayerId === layerId) {
      this.setActiveLayer(targetLayerId);
    }

    this.reorderAllLayers();
    this.emit('layer:deleted', { layerId, targetLayerId });
    
    return true;
  }

  public updateLayer(layerId: string, options: LayerUpdateOptions): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    const oldLayer = { ...layer };

    // Update properties
    if (options.name !== undefined) layer.name = options.name;
    if (options.opacity !== undefined) {
      layer.opacity = Math.max(0, Math.min(1, options.opacity));
      layer.fabricGroup?.set('opacity', layer.opacity);
    }
    if (options.visible !== undefined) {
      layer.visible = options.visible;
      layer.fabricGroup?.set('visible', layer.visible);
      this.updateObjectsVisibility(layerId, layer.visible);
    }
    if (options.locked !== undefined) {
      layer.locked = options.locked;
      layer.fabricGroup?.set('evented', !layer.locked);
      this.updateObjectsLock(layerId, layer.locked);
    }
    if (options.blendMode !== undefined) {
      layer.blendMode = options.blendMode;
      // Apply blend mode to fabric group
      this.applyBlendMode(layer.fabricGroup!, options.blendMode);
    }

    // Handle z-index change
    if (options.zIndex !== undefined && options.zIndex !== layer.zIndex) {
      this.moveLayerToIndex(layerId, options.zIndex);
    }

    this.canvas.requestRenderAll();
    this.emit('layer:updated', { layer, oldLayer });
    
    return true;
  }

  // ✅ Layer Ordering and Z-Index Management
  public moveLayerToIndex(layerId: string, newIndex: number): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    const currentIndex = this.layerOrder.indexOf(layerId);
    if (currentIndex === -1) return false;

    // Clamp to valid range
    newIndex = Math.max(0, Math.min(newIndex, this.layerOrder.length - 1));
    
    if (currentIndex === newIndex) return true;

    // Remove from current position
    this.layerOrder.splice(currentIndex, 1);
    
    // Insert at new position
    this.layerOrder.splice(newIndex, 0, layerId);
    
    // Update z-indices
    this.reorderAllLayers();
    this.updateCanvasLayerOrder();
    
    this.emit('layer:reordered', { layerId, oldIndex: currentIndex, newIndex });
    
    return true;
  }

  public moveLayerUp(layerId: string): boolean {
    const currentIndex = this.layerOrder.indexOf(layerId);
    if (currentIndex === -1 || currentIndex >= this.layerOrder.length - 1) return false;
    
    return this.moveLayerToIndex(layerId, currentIndex + 1);
  }

  public moveLayerDown(layerId: string): boolean {
    const currentIndex = this.layerOrder.indexOf(layerId);
    if (currentIndex <= 0) return false;
    
    return this.moveLayerToIndex(layerId, currentIndex - 1);
  }

  public bringLayerToFront(layerId: string): boolean {
    return this.moveLayerToIndex(layerId, this.layerOrder.length - 1);
  }

  public sendLayerToBack(layerId: string): boolean {
    return this.moveLayerToIndex(layerId, 0);
  }

  // ✅ Layer Visibility and Locking Controls
  public setLayerVisibility(layerId: string, visible: boolean): boolean {
    return this.updateLayer(layerId, { visible });
  }

  public toggleLayerVisibility(layerId: string): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;
    
    return this.setLayerVisibility(layerId, !layer.visible);
  }

  public setLayerLock(layerId: string, locked: boolean): boolean {
    return this.updateLayer(layerId, { locked });
  }

  public toggleLayerLock(layerId: string): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;
    
    return this.setLayerLock(layerId, !layer.locked);
  }

  public setLayerOpacity(layerId: string, opacity: number): boolean {
    return this.updateLayer(layerId, { opacity });
  }

  // Object-Layer Association
  public addObjectToLayer(objectId: string, layerId: string): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    // Remove from other layers first
    this.removeObjectFromAllLayers(objectId);
    
    // Add to target layer
    layer.objects.add(objectId);
    
    // Find the object in canvas and set layer reference
    const canvasObject = this.findCanvasObject(objectId);
    if (canvasObject) {
      canvasObject.set('layerId', layerId);
      
      // Update object properties based on layer state
      this.applyLayerPropertiesToObject(canvasObject, layer);
    }

    this.emit('object:layer:changed', { objectId, layerId });
    return true;
  }

  public removeObjectFromLayer(objectId: string, layerId: string): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    const removed = layer.objects.delete(objectId);
    if (removed) {
      this.emit('object:layer:removed', { objectId, layerId });
    }
    
    return removed;
  }

  public moveObjectsToLayer(objectIds: string[], targetLayerId: string): boolean {
    const targetLayer = this.layers.get(targetLayerId);
    if (!targetLayer) return false;

    objectIds.forEach(objectId => {
      this.addObjectToLayer(objectId, targetLayerId);
    });

    this.emit('objects:moved:layer', { objectIds, targetLayerId });
    return true;
  }

  public getObjectLayer(objectId: string): CanvasLayer | null {
    for (const layer of this.layers.values()) {
      if (layer.objects.has(objectId)) {
        return layer;
      }
    }
    return null;
  }

  // Active Layer Management
  public setActiveLayer(layerId: string): boolean {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    this.activeLayerId = layerId;
    this.emit('layer:active:changed', { layerId });
    
    return true;
  }

  public getActiveLayerId(): string {
    return this.activeLayerId || this.layerOrder[this.layerOrder.length - 1] || '';
  }

  public getActiveLayer(): CanvasLayer | null {
    const activeId = this.getActiveLayerId();
    return activeId ? this.layers.get(activeId) || null : null;
  }

  // Layer Queries and Access
  public getLayer(layerId: string): CanvasLayer | null {
    return this.layers.get(layerId) || null;
  }

  public getAllLayers(): CanvasLayer[] {
    return this.layerOrder.map(id => this.layers.get(id)!).filter(Boolean);
  }

  public getLayersByVisibility(visible: boolean): CanvasLayer[] {
    return this.getAllLayers().filter(layer => layer.visible === visible);
  }

  public getLayerCount(): number {
    return this.layers.size;
  }

  public duplicateLayer(layerId: string): string | null {
    const layer = this.layers.get(layerId);
    if (!layer) return null;

    const newLayerId = this.createLayer({
      name: `${layer.name} Copy`,
      opacity: layer.opacity,
      visible: layer.visible,
      locked: layer.locked,
      blendMode: layer.blendMode,
      insertAt: this.layerOrder.indexOf(layerId) + 1,
    });

    // Copy all objects from original layer
    const objectIds = Array.from(layer.objects);
    // Note: This would need integration with object duplication system
    
    this.emit('layer:duplicated', { originalLayerId: layerId, newLayerId });
    
    return newLayerId;
  }

  // Layer Merging
  public mergeLayers(layerIds: string[]): string | null {
    if (layerIds.length < 2) return null;

    const layers = layerIds.map(id => this.layers.get(id)).filter(Boolean);
    if (layers.length !== layerIds.length) return null;

    // Create new merged layer
    const mergedLayerId = this.createLayer({
      name: `Merged Layer`,
      insertAt: Math.min(...layerIds.map(id => this.layerOrder.indexOf(id))),
    });

    // Move all objects to merged layer
    const allObjectIds: string[] = [];
    layers.forEach(layer => {
      allObjectIds.push(...Array.from(layer.objects));
    });

    this.moveObjectsToLayer(allObjectIds, mergedLayerId);

    // Delete original layers (except the first one which becomes the merged layer)
    layerIds.slice(1).forEach(layerId => {
      this.deleteLayer(layerId);
    });

    this.emit('layers:merged', { originalLayerIds: layerIds, mergedLayerId });
    
    return mergedLayerId;
  }

  // Private Helper Methods
  private generateLayerId(): string {
    return `layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getNextLayerColor(): string {
    const color = this.layerColors[this.colorIndex % this.layerColors.length];
    this.colorIndex++;
    return color;
  }

  private reorderAllLayers(): void {
    this.layerOrder.forEach((layerId, index) => {
      const layer = this.layers.get(layerId);
      if (layer) {
        layer.zIndex = index;
      }
    });
  }

  private updateCanvasLayerOrder(): void {
    // Update Fabric.js canvas object ordering based on layer order
    this.layerOrder.forEach((layerId, index) => {
      const layer = this.layers.get(layerId);
      if (layer?.fabricGroup) {
        layer.fabricGroup.moveTo(index);
      }
    });
    
    this.canvas.requestRenderAll();
  }

  private removeObjectFromAllLayers(objectId: string): void {
    this.layers.forEach(layer => {
      layer.objects.delete(objectId);
    });
  }

  private findCanvasObject(objectId: string): fabric.Object | null {
    const objects = this.canvas.getObjects();
    return objects.find(obj => obj.get('id') === objectId) || null;
  }

  private applyLayerPropertiesToObject(object: fabric.Object, layer: CanvasLayer): void {
    // Apply layer opacity, visibility, and lock state to individual object
    const currentOpacity = object.get('opacity') || 1;
    object.set({
      opacity: currentOpacity * layer.opacity,
      visible: layer.visible,
      selectable: !layer.locked,
      evented: !layer.locked,
    });
  }

  private updateObjectsVisibility(layerId: string, visible: boolean): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.objects.forEach(objectId => {
      const object = this.findCanvasObject(objectId);
      if (object) {
        object.set('visible', visible);
      }
    });
  }

  private updateObjectsLock(layerId: string, locked: boolean): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.objects.forEach(objectId => {
      const object = this.findCanvasObject(objectId);
      if (object) {
        object.set({
          selectable: !locked,
          evented: !locked,
        });
      }
    });
  }

  private applyBlendMode(fabricGroup: fabric.Group, blendMode: string): void {
    // Apply CSS blend mode to the group
    const canvasElement = fabricGroup.canvas?.getElement();
    if (canvasElement) {
      const ctx = canvasElement.getContext('2d');
      if (ctx) {
        // Set global composite operation based on blend mode
        switch (blendMode) {
          case 'multiply': ctx.globalCompositeOperation = 'multiply'; break;
          case 'screen': ctx.globalCompositeOperation = 'screen'; break;
          case 'overlay': ctx.globalCompositeOperation = 'overlay'; break;
          case 'darken': ctx.globalCompositeOperation = 'darken'; break;
          case 'lighten': ctx.globalCompositeOperation = 'lighten'; break;
          case 'color-dodge': ctx.globalCompositeOperation = 'color-dodge'; break;
          case 'color-burn': ctx.globalCompositeOperation = 'color-burn'; break;
          case 'hard-light': ctx.globalCompositeOperation = 'hard-light'; break;
          case 'soft-light': ctx.globalCompositeOperation = 'soft-light'; break;
          case 'difference': ctx.globalCompositeOperation = 'difference'; break;
          case 'exclusion': ctx.globalCompositeOperation = 'exclusion'; break;
          default: ctx.globalCompositeOperation = 'source-over'; break;
        }
      }
    }
  }

  private setupEventListeners(): void {
    // Listen for object additions to assign to active layer
    this.canvas.on('object:added', (e) => {
      if (e.target && !e.target.get('layerId')) {
        const activeLayerId = this.getActiveLayerId();
        if (activeLayerId) {
          const objectId = e.target.get('id');
          if (objectId) {
            this.addObjectToLayer(objectId, activeLayerId);
          }
        }
      }
    });

    // Listen for object removal to clean up layer references
    this.canvas.on('object:removed', (e) => {
      if (e.target) {
        const objectId = e.target.get('id');
        if (objectId) {
          this.removeObjectFromAllLayers(objectId);
        }
      }
    });
  }

  // Export and Import
  public exportLayersData(): any {
    return {
      layers: Array.from(this.layers.values()),
      layerOrder: this.layerOrder,
      activeLayerId: this.activeLayerId,
    };
  }

  public importLayersData(data: any): void {
    this.layers.clear();
    this.layerOrder = [];
    
    if (data.layers) {
      data.layers.forEach((layerData: CanvasLayer) => {
        this.layers.set(layerData.id, {
          ...layerData,
          objects: new Set(layerData.objects),
        });
      });
    }
    
    if (data.layerOrder) {
      this.layerOrder = data.layerOrder;
    }
    
    if (data.activeLayerId) {
      this.activeLayerId = data.activeLayerId;
    }
    
    this.updateCanvasLayerOrder();
    this.emit('layers:imported', { data });
  }
}

export default LayerManager;