import * as PIXI from 'pixi.js';
import { fabric } from 'fabric';
import { EventEmitter } from 'events';

export interface PIXIPerformanceConfig {
  width: number;
  height: number;
  backgroundColor?: number;
  antialias?: boolean;
  resolution?: number;
  autoDensity?: boolean;
  powerPreference?: 'default' | 'high-performance' | 'low-power';
  forceCanvas?: boolean;
}

export interface RenderingStrategy {
  useWebGL: boolean;
  batchRendering: boolean;
  culling: boolean;
  textureOptimization: boolean;
}

export class PIXIPerformanceLayer extends EventEmitter {
  private pixiApp: PIXI.Application;
  private pixiContainer: PIXI.Container;
  private fabricCanvas: fabric.Canvas;
  private renderingStrategy: RenderingStrategy;
  private performanceMonitor: PerformanceMonitor;
  private objectOptimizer: ObjectOptimizer;
  private isWebGLSupported: boolean;
  private renderQueue: RenderTask[] = [];
  private isRendering = false;

  constructor(
    canvasElement: HTMLCanvasElement,
    fabricCanvas: fabric.Canvas,
    config: PIXIPerformanceConfig
  ) {
    super();
    this.fabricCanvas = fabricCanvas;
    this.isWebGLSupported = this.detectWebGLSupport();
    
    this.initializePIXI(canvasElement, config);
    this.initializeRenderingStrategy();
    this.setupPerformanceMonitoring();
    this.initializeObjectOptimizer();
    this.setupHybridRendering();
  }

  private detectWebGLSupport(): boolean {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  private initializePIXI(canvasElement: HTMLCanvasElement, config: PIXIPerformanceConfig): void {
    const pixiConfig: Partial<PIXI.ApplicationOptions> = {
      width: config.width,
      height: config.height,
      backgroundColor: config.backgroundColor || 0xffffff,
      antialias: config.antialias !== false,
      resolution: config.resolution || window.devicePixelRatio || 1,
      autoDensity: config.autoDensity !== false,
      powerPreference: config.powerPreference || 'high-performance',
      forceCanvas: config.forceCanvas || !this.isWebGLSupported
    };

    this.pixiApp = new PIXI.Application(pixiConfig);
    this.pixiContainer = new PIXI.Container();
    this.pixiApp.stage.addChild(this.pixiContainer);

    // Mount PIXI canvas behind Fabric.js canvas
    const pixiCanvas = this.pixiApp.view as HTMLCanvasElement;
    pixiCanvas.style.position = 'absolute';
    pixiCanvas.style.zIndex = '1';
    canvasElement.style.position = 'absolute';
    canvasElement.style.zIndex = '2';
    canvasElement.style.backgroundColor = 'transparent';

    if (canvasElement.parentNode) {
      canvasElement.parentNode.insertBefore(pixiCanvas, canvasElement);
    }
  }

  private initializeRenderingStrategy(): void {
    this.renderingStrategy = {
      useWebGL: this.isWebGLSupported,
      batchRendering: true,
      culling: true,
      textureOptimization: true
    };

    // Adjust strategy based on device capabilities
    const gpuInfo = this.getGPUInfo();
    if (gpuInfo.tier < 2) {
      this.renderingStrategy.batchRendering = false;
      this.renderingStrategy.textureOptimization = false;
    }
  }

  private setupPerformanceMonitoring(): void {
    this.performanceMonitor = new PerformanceMonitor();
    
    // Monitor frame rate
    this.pixiApp.ticker.add(() => {
      this.performanceMonitor.recordFrame();
    });

    // Monitor memory usage
    setInterval(() => {
      this.performanceMonitor.recordMemoryUsage();
    }, 1000);
  }

  private initializeObjectOptimizer(): void {
    this.objectOptimizer = new ObjectOptimizer(this.renderingStrategy);
  }

  private setupHybridRendering(): void {
    // Listen to Fabric.js events for hybrid rendering decisions
    this.fabricCanvas.on('object:added', (e) => {
      this.handleObjectAdded(e.target as fabric.Object);
    });

    this.fabricCanvas.on('object:removed', (e) => {
      this.handleObjectRemoved(e.target as fabric.Object);
    });

    this.fabricCanvas.on('object:modified', (e) => {
      this.handleObjectModified(e.target as fabric.Object);
    });

    // Start render loop
    this.startRenderLoop();
  }

  private handleObjectAdded(object: fabric.Object): void {
    const shouldUsePixi = this.shouldRenderWithPIXI(object);
    
    if (shouldUsePixi) {
      this.queueRenderTask({
        type: 'add',
        object,
        priority: this.getObjectPriority(object)
      });
    }
  }

  private handleObjectRemoved(object: fabric.Object): void {
    this.queueRenderTask({
      type: 'remove',
      object,
      priority: 1
    });
  }

  private handleObjectModified(object: fabric.Object): void {
    const shouldUsePixi = this.shouldRenderWithPIXI(object);
    
    if (shouldUsePixi) {
      this.queueRenderTask({
        type: 'update',
        object,
        priority: this.getObjectPriority(object)
      });
    }
  }

  private shouldRenderWithPIXI(object: fabric.Object): boolean {
    // Determine optimal rendering path based on object type and complexity
    if (!this.renderingStrategy.useWebGL) return false;

    // Use PIXI for complex objects that benefit from GPU acceleration
    if (object.type === 'image' && this.isLargeImage(object)) return true;
    if (object.type === 'group' && this.hasComplexEffects(object)) return true;
    if (this.hasFiltersOrEffects(object)) return true;

    return false;
  }

  private isLargeImage(object: fabric.Object): boolean {
    const image = object as fabric.Image;
    return (image.width || 0) * (image.height || 0) > 1000000; // 1MP threshold
  }

  private hasComplexEffects(object: fabric.Object): boolean {
    return !!(object as any).filters && (object as any).filters.length > 0;
  }

  private hasFiltersOrEffects(object: fabric.Object): boolean {
    return !!(object as any).shadow || !!(object as any).filters;
  }

  private getObjectPriority(object: fabric.Object): number {
    // Higher priority for visible objects in viewport
    if (this.isInViewport(object)) return 3;
    if (object.visible) return 2;
    return 1;
  }

  private isInViewport(object: fabric.Object): boolean {
    const bounds = object.getBoundingRect();
    const viewport = this.fabricCanvas.getViewportTransform();
    
    // Simple viewport intersection check
    return bounds.left < this.pixiApp.screen.width && 
           bounds.top < this.pixiApp.screen.height &&
           bounds.left + bounds.width > 0 && 
           bounds.top + bounds.height > 0;
  }

  private queueRenderTask(task: RenderTask): void {
    this.renderQueue.push(task);
    this.renderQueue.sort((a, b) => b.priority - a.priority);
    
    if (!this.isRendering) {
      this.processRenderQueue();
    }
  }

  private async processRenderQueue(): Promise<void> {
    if (this.isRendering || this.renderQueue.length === 0) return;
    
    this.isRendering = true;
    
    while (this.renderQueue.length > 0) {
      const task = this.renderQueue.shift()!;
      await this.executeRenderTask(task);
      
      // Yield control to prevent blocking
      if (this.renderQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }
    
    this.isRendering = false;
  }

  private async executeRenderTask(task: RenderTask): Promise<void> {
    try {
      switch (task.type) {
        case 'add':
          await this.addObjectToPIXI(task.object);
          break;
        case 'remove':
          this.removeObjectFromPIXI(task.object);
          break;
        case 'update':
          await this.updateObjectInPIXI(task.object);
          break;
      }
    } catch (error) {
      console.error('Render task failed:', error);
      this.emit('render:error', error, task);
    }
  }

  private async addObjectToPIXI(object: fabric.Object): Promise<void> {
    const pixiObject = await this.objectOptimizer.convertToPixi(object);
    if (pixiObject) {
      this.pixiContainer.addChild(pixiObject);
      this.emit('object:added:pixi', object, pixiObject);
    }
  }

  private removeObjectFromPIXI(object: fabric.Object): void {
    const pixiObject = this.findPixiObject(object);
    if (pixiObject) {
      this.pixiContainer.removeChild(pixiObject);
      this.emit('object:removed:pixi', object, pixiObject);
    }
  }

  private async updateObjectInPIXI(object: fabric.Object): Promise<void> {
    this.removeObjectFromPIXI(object);
    await this.addObjectToPIXI(object);
  }

  private findPixiObject(fabricObject: fabric.Object): PIXI.DisplayObject | null {
    return this.pixiContainer.children.find(child => 
      (child as any).fabricId === (fabricObject as any).id
    ) || null;
  }

  private startRenderLoop(): void {
    this.pixiApp.ticker.add(() => {
      this.performanceMonitor.startFrame();
      
      // Sync transformations between Fabric.js and PIXI
      this.syncTransformations();
      
      this.performanceMonitor.endFrame();
    });
  }

  private syncTransformations(): void {
    const viewport = this.fabricCanvas.getViewportTransform();
    if (viewport) {
      this.pixiContainer.setTransform(
        viewport[4], // x
        viewport[5], // y
        viewport[0], // scaleX
        viewport[3], // scaleY
        0, // rotation
        0, // skewX
        0  // skewY
      );
    }
  }

  private getGPUInfo(): { tier: number; renderer: string } {
    if (!this.isWebGLSupported) {
      return { tier: 0, renderer: 'software' };
    }

    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl');
    
    if (!gl) {
      return { tier: 0, renderer: 'software' };
    }

    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    const renderer = debugInfo ? 
      gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'unknown';

    // Simple GPU tier classification
    let tier = 1;
    if (renderer.includes('NVIDIA') || renderer.includes('AMD')) tier = 3;
    else if (renderer.includes('Intel')) tier = 2;

    return { tier, renderer };
  }

  public getPerformanceMetrics(): PerformanceMetrics {
    return this.performanceMonitor.getMetrics();
  }

  public updateRenderingStrategy(strategy: Partial<RenderingStrategy>): void {
    this.renderingStrategy = { ...this.renderingStrategy, ...strategy };
    this.objectOptimizer.updateStrategy(this.renderingStrategy);
  }

  public dispose(): void {
    this.pixiApp.destroy(true, true);
    this.performanceMonitor.dispose();
    this.removeAllListeners();
  }
}

interface RenderTask {
  type: 'add' | 'remove' | 'update';
  object: fabric.Object;
  priority: number;
}

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memoryUsage: number;
  renderCalls: number;
  objectCount: number;
}

class PerformanceMonitor {
  private frameCount = 0;
  private lastTime = performance.now();
  private frameTime = 0;
  private fps = 60;
  private memoryUsage = 0;

  recordFrame(): void {
    const now = performance.now();
    this.frameTime = now - this.lastTime;
    this.lastTime = now;
    this.frameCount++;
    
    if (this.frameCount % 60 === 0) {
      this.fps = 1000 / this.frameTime;
    }
  }

  startFrame(): void {
    this.lastTime = performance.now();
  }

  endFrame(): void {
    this.frameTime = performance.now() - this.lastTime;
  }

  recordMemoryUsage(): void {
    if ('memory' in performance) {
      this.memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024;
    }
  }

  getMetrics(): PerformanceMetrics {
    return {
      fps: Math.round(this.fps),
      frameTime: Math.round(this.frameTime * 100) / 100,
      memoryUsage: Math.round(this.memoryUsage * 100) / 100,
      renderCalls: 0, // Would be tracked by renderer
      objectCount: 0  // Would be tracked by container
    };
  }

  dispose(): void {
    // Cleanup monitoring
  }
}

class ObjectOptimizer {
  private strategy: RenderingStrategy;
  private textureCache = new Map<string, PIXI.Texture>();

  constructor(strategy: RenderingStrategy) {
    this.strategy = strategy;
  }

  async convertToPixi(fabricObject: fabric.Object): Promise<PIXI.DisplayObject | null> {
    switch (fabricObject.type) {
      case 'image':
        return this.convertImage(fabricObject as fabric.Image);
      case 'rect':
        return this.convertRectangle(fabricObject as fabric.Rect);
      case 'circle':
        return this.convertCircle(fabricObject as fabric.Circle);
      case 'text':
        return this.convertText(fabricObject as fabric.Text);
      default:
        return null;
    }
  }

  private async convertImage(fabricImage: fabric.Image): Promise<PIXI.Sprite | null> {
    const element = fabricImage.getElement();
    if (!element) return null;

    let texture: PIXI.Texture;
    const cacheKey = (element as any).src || 'unknown';
    
    if (this.strategy.textureOptimization && this.textureCache.has(cacheKey)) {
      texture = this.textureCache.get(cacheKey)!;
    } else {
      texture = PIXI.Texture.from(element);
      if (this.strategy.textureOptimization) {
        this.textureCache.set(cacheKey, texture);
      }
    }

    const sprite = new PIXI.Sprite(texture);
    this.applyTransform(sprite, fabricImage);
    (sprite as any).fabricId = (fabricImage as any).id;
    
    return sprite;
  }

  private convertRectangle(fabricRect: fabric.Rect): PIXI.Graphics {
    const graphics = new PIXI.Graphics();
    graphics.beginFill(this.parseColor(fabricRect.fill as string));
    graphics.drawRect(0, 0, fabricRect.width || 100, fabricRect.height || 100);
    graphics.endFill();
    
    this.applyTransform(graphics, fabricRect);
    (graphics as any).fabricId = (fabricRect as any).id;
    
    return graphics;
  }

  private convertCircle(fabricCircle: fabric.Circle): PIXI.Graphics {
    const graphics = new PIXI.Graphics();
    graphics.beginFill(this.parseColor(fabricCircle.fill as string));
    graphics.drawCircle(0, 0, fabricCircle.radius || 50);
    graphics.endFill();
    
    this.applyTransform(graphics, fabricCircle);
    (graphics as any).fabricId = (fabricCircle as any).id;
    
    return graphics;
  }

  private convertText(fabricText: fabric.Text): PIXI.Text {
    const pixiText = new PIXI.Text(fabricText.text || '', {
      fontSize: fabricText.fontSize || 16,
      fill: this.parseColor(fabricText.fill as string),
      fontFamily: fabricText.fontFamily || 'Arial'
    });
    
    this.applyTransform(pixiText, fabricText);
    (pixiText as any).fabricId = (fabricText as any).id;
    
    return pixiText;
  }

  private applyTransform(pixiObject: PIXI.DisplayObject, fabricObject: fabric.Object): void {
    pixiObject.x = fabricObject.left || 0;
    pixiObject.y = fabricObject.top || 0;
    pixiObject.rotation = (fabricObject.angle || 0) * Math.PI / 180;
    pixiObject.scale.set(fabricObject.scaleX || 1, fabricObject.scaleY || 1);
    pixiObject.alpha = fabricObject.opacity || 1;
    pixiObject.visible = fabricObject.visible !== false;
  }

  private parseColor(color: string): number {
    if (!color || color === 'transparent') return 0x000000;
    if (color.startsWith('#')) {
      return parseInt(color.slice(1), 16);
    }
    return 0x000000;
  }

  updateStrategy(strategy: RenderingStrategy): void {
    this.strategy = strategy;
  }
}
