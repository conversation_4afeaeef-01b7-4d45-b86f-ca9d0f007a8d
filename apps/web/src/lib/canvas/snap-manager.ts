import { fabric } from 'fabric';

export interface SnapSettings {
  enabled: boolean;
  snapToGrid: boolean;
  snapToObjects: boolean;
  snapToGuides: boolean;
  snapTolerance: number;
  gridSize: number;
}

export class SnapManager {
  private canvas: fabric.Canvas;
  private settings: SnapSettings;
  private guides: fabric.Line[] = [];

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.settings = {
      enabled: true,
      snapToGrid: true,
      snapToObjects: true,
      snapToGuides: true,
      snapTolerance: 10,
      gridSize: 20
    };
    
    this.initializeSnapping();
  }

  private initializeSnapping(): void {
    this.canvas.on('object:moving', (e) => {
      if (!this.settings.enabled) return;
      
      const obj = e.target as fabric.Object;
      this.snapObject(obj);
    });
  }

  private snapObject(obj: fabric.Object): void {
    const objBounds = obj.getBoundingRect();
    let snapX = obj.left || 0;
    let snapY = obj.top || 0;

    // Snap to grid
    if (this.settings.snapToGrid) {
      snapX = Math.round(snapX / this.settings.gridSize) * this.settings.gridSize;
      snapY = Math.round(snapY / this.settings.gridSize) * this.settings.gridSize;
    }

    // Snap to other objects
    if (this.settings.snapToObjects) {
      const otherObjects = this.canvas.getObjects().filter(o => o !== obj);
      
      for (const other of otherObjects) {
        const otherBounds = other.getBoundingRect();
        
        // Horizontal alignment
        if (Math.abs(objBounds.left - otherBounds.left) < this.settings.snapTolerance) {
          snapX = otherBounds.left;
        }
        if (Math.abs(objBounds.left - otherBounds.left - otherBounds.width) < this.settings.snapTolerance) {
          snapX = otherBounds.left + otherBounds.width;
        }
        
        // Vertical alignment
        if (Math.abs(objBounds.top - otherBounds.top) < this.settings.snapTolerance) {
          snapY = otherBounds.top;
        }
        if (Math.abs(objBounds.top - otherBounds.top - otherBounds.height) < this.settings.snapTolerance) {
          snapY = otherBounds.top + otherBounds.height;
        }
      }
    }

    obj.set({ left: snapX, top: snapY });
  }

  public updateSettings(newSettings: Partial<SnapSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
  }

  public addGuide(x1: number, y1: number, x2: number, y2: number): void {
    const guide = new fabric.Line([x1, y1, x2, y2], {
      stroke: '#00ff00',
      strokeWidth: 1,
      selectable: false,
      evented: false,
      strokeDashArray: [5, 5]
    });
    
    this.guides.push(guide);
    this.canvas.add(guide);
  }

  public clearGuides(): void {
    this.guides.forEach(guide => this.canvas.remove(guide));
    this.guides = [];
  }
}