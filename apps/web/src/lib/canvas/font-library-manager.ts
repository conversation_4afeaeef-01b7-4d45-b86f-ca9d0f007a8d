import { EventEmitter } from 'events';

// Font library types and interfaces
export interface FontVariant {
  weight: number | string;
  style: 'normal' | 'italic' | 'oblique';
  stretch: 'normal' | 'condensed' | 'expanded' | string;
  unicodeRange?: string;
  featureSettings?: string;
}

export interface FontFamily {
  id: string;
  name: string;
  displayName: string;
  category: FontCategory;
  variants: FontVariant[];
  subsets: string[];
  version: string;
  lastModified: string;
  kind: string;
  menu?: string;
  files: Record<string, string>;
  popularity?: number;
  trending?: boolean;
  license: FontLicense;
  designer?: string;
  foundry?: string;
  description?: string;
  specimen?: string;
  tags: string[];
  classification: FontClassification;
  metrics: FontMetrics;
  loadingStatus: FontLoadingStatus;
  customFont?: boolean;
  source: FontSource;
}

export interface FontMetrics {
  xHeight: number;
  capHeight: number;
  ascender: number;
  descender: number;
  lineGap: number;
  unitsPerEm: number;
  avgCharWidth: number;
  maxCharWidth: number;
  weightClass: number;
  widthClass: number;
}

export interface FontLicense {
  type: 'open' | 'commercial' | 'free' | 'custom';
  url?: string;
  restrictions?: string[];
  attribution?: string;
}

export interface FontClassification {
  primary: string;
  secondary: string[];
  mood: string[];
  usage: string[];
  legibility: 'high' | 'medium' | 'low';
  readability: 'high' | 'medium' | 'low';
}

export type FontCategory = 'serif' | 'sans-serif' | 'display' | 'handwriting' | 'monospace' | 'script' | 'decorative';
export type FontLoadingStatus = 'not-loaded' | 'loading' | 'loaded' | 'error';
export type FontSource = 'google' | 'adobe' | 'system' | 'custom' | 'web' | 'local';

export interface FontFilter {
  category?: FontCategory[];
  weight?: number[];
  style?: string[];
  license?: string[];
  source?: FontSource[];
  popularity?: 'high' | 'medium' | 'low';
  trending?: boolean;
  customOnly?: boolean;
  loadedOnly?: boolean;
  search?: string;
  tags?: string[];
  classification?: string[];
  subset?: string[];
}

export interface FontCollection {
  id: string;
  name: string;
  description: string;
  fontIds: string[];
  tags: string[];
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  owner?: string;
}

export interface FontPreview {
  fontId: string;
  text: string;
  fontSize: number;
  weight: number | string;
  style: string;
  canvas: HTMLCanvasElement;
  dataUrl: string;
  width: number;
  height: number;
}

export interface FontLoadingOptions {
  display: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
  timeout: number;
  strategy: 'eager' | 'lazy' | 'preload';
  fallback: string[];
  subset?: string[];
  variants?: FontVariant[];
  crossOrigin?: 'anonymous' | 'use-credentials';
}

export interface FontLibraryConfig {
  enableGoogleFonts: boolean;
  enableAdobeFonts: boolean;
  enableSystemFonts: boolean;
  enableCustomFonts: boolean;
  maxConcurrentLoads: number;
  cacheSize: number;
  previewCacheSize: number;
  defaultLoadingStrategy: 'eager' | 'lazy' | 'preload';
  fallbackFonts: string[];
  enableFontDisplay: boolean;
  fontDisplayValue: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
  enableSubsetting: boolean;
  enableFeatureSettings: boolean;
  enableVariableFonts: boolean;
  apiKeys: {
    google?: string;
    adobe?: string;
  };
}

export interface FontLibraryStats {
  totalFonts: number;
  loadedFonts: number;
  categoryCounts: Record<FontCategory, number>;
  sourceCounts: Record<FontSource, number>;
  popularFonts: string[];
  recentlyUsed: string[];
  cacheSize: number;
  memoryUsage: number;
  loadingErrors: number;
  averageLoadTime: number;
}

// Font loading utilities
export class FontLoader {
  private loadingPromises: Map<string, Promise<FontFace>> = new Map();
  private loadedFonts: Set<string> = new Set();
  private loadingErrors: Map<string, Error> = new Map();
  private loadingTimes: Map<string, number> = new Map();
  private maxConcurrentLoads: number;
  private currentLoads: number = 0;
  private loadQueue: Array<{ fontFamily: FontFamily; resolve: Function; reject: Function }> = [];

  constructor(maxConcurrentLoads: number = 5) {
    this.maxConcurrentLoads = maxConcurrentLoads;
  }

  async loadFont(fontFamily: FontFamily, options: FontLoadingOptions = { display: 'swap', timeout: 5000, strategy: 'lazy', fallback: [] }): Promise<FontFace> {
    const fontKey = this.getFontKey(fontFamily);
    
    // Return existing promise if already loading
    if (this.loadingPromises.has(fontKey)) {
      return this.loadingPromises.get(fontKey)!;
    }

    // Return immediately if already loaded
    if (this.loadedFonts.has(fontKey)) {
      const existingFontFace = this.findExistingFontFace(fontFamily);
      if (existingFontFace) {
        return existingFontFace;
      }
    }

    // Create loading promise
    const loadingPromise = this.createLoadingPromise(fontFamily, options);
    this.loadingPromises.set(fontKey, loadingPromise);

    try {
      const fontFace = await loadingPromise;
      this.loadedFonts.add(fontKey);
      this.loadingPromises.delete(fontKey);
      return fontFace;
    } catch (error) {
      this.loadingErrors.set(fontKey, error as Error);
      this.loadingPromises.delete(fontKey);
      throw error;
    }
  }

  private createLoadingPromise(fontFamily: FontFamily, options: FontLoadingOptions): Promise<FontFace> {
    return new Promise((resolve, reject) => {
      // Add to queue if at capacity
      if (this.currentLoads >= this.maxConcurrentLoads) {
        this.loadQueue.push({ fontFamily, resolve, reject });
        return;
      }

      this.processLoadRequest(fontFamily, options, resolve, reject);
    });
  }

  private async processLoadRequest(
    fontFamily: FontFamily, 
    options: FontLoadingOptions, 
    resolve: Function, 
    reject: Function
  ): Promise<void> {
    this.currentLoads++;
    const startTime = performance.now();

    try {
      const fontFace = await this.loadFontFace(fontFamily, options);
      
      // Add to document fonts
      document.fonts.add(fontFace);
      await document.fonts.load(`16px ${fontFamily.name}`);

      const loadTime = performance.now() - startTime;
      this.loadingTimes.set(this.getFontKey(fontFamily), loadTime);

      resolve(fontFace);
    } catch (error) {
      reject(error);
    } finally {
      this.currentLoads--;
      this.processQueue();
    }
  }

  private async loadFontFace(fontFamily: FontFamily, options: FontLoadingOptions): Promise<FontFace> {
    // Get font URL based on source
    const fontUrl = this.getFontUrl(fontFamily, options);
    
    // Create FontFace object
    const fontFace = new FontFace(
      fontFamily.name,
      `url(${fontUrl})`,
      {
        style: 'normal',
        weight: 'normal',
        display: options.display,
        unicodeRange: options.subset?.join(','),
        featureSettings: fontFamily.variants[0]?.featureSettings,
        ...this.getFontFaceDescriptors(fontFamily, options)
      }
    );

    // Load the font
    const loadedFontFace = await fontFace.load();
    
    // Validate font loading
    if (loadedFontFace.status !== 'loaded') {
      throw new Error(`Font failed to load: ${fontFamily.name}`);
    }

    return loadedFontFace;
  }

  private getFontUrl(fontFamily: FontFamily, options: FontLoadingOptions): string {
    switch (fontFamily.source) {
      case 'google':
        return this.buildGoogleFontUrl(fontFamily, options);
      case 'adobe':
        return this.buildAdobeFontUrl(fontFamily, options);
      case 'custom':
      case 'web':
        return fontFamily.files[Object.keys(fontFamily.files)[0]];
      case 'local':
        return `local(${fontFamily.name})`;
      default:
        throw new Error(`Unsupported font source: ${fontFamily.source}`);
    }
  }

  private buildGoogleFontUrl(fontFamily: FontFamily, options: FontLoadingOptions): string {
    const baseUrl = 'https://fonts.googleapis.com/css2';
    const familyParam = `family=${encodeURIComponent(fontFamily.name)}`;
    
    const params = [familyParam];
    
    if (options.subset?.length) {
      params.push(`subset=${options.subset.join(',')}`);
    }
    
    params.push(`display=${options.display}`);
    
    return `${baseUrl}?${params.join('&')}`;
  }

  private buildAdobeFontUrl(fontFamily: FontFamily, options: FontLoadingOptions): string {
    // Adobe Fonts (Typekit) URL structure
    const baseUrl = 'https://use.typekit.net';
    const kitId = fontFamily.files.kit || 'default';
    return `${baseUrl}/${kitId}.css`;
  }

  private getFontFaceDescriptors(fontFamily: FontFamily, options: FontLoadingOptions): any {
    const descriptors: any = {};
    
    if (fontFamily.variants.length > 0) {
      const variant = fontFamily.variants[0];
      descriptors.weight = variant.weight;
      descriptors.style = variant.style;
      descriptors.stretch = variant.stretch;
    }
    
    if (options.unicodeRange) {
      descriptors.unicodeRange = options.unicodeRange;
    }
    
    return descriptors;
  }

  private processQueue(): void {
    if (this.loadQueue.length > 0 && this.currentLoads < this.maxConcurrentLoads) {
      const { fontFamily, resolve, reject } = this.loadQueue.shift()!;
      this.processLoadRequest(fontFamily, { display: 'swap', timeout: 5000, strategy: 'lazy', fallback: [] }, resolve, reject);
    }
  }

  private findExistingFontFace(fontFamily: FontFamily): FontFace | undefined {
    for (const fontFace of document.fonts) {
      if (fontFace.family === fontFamily.name) {
        return fontFace;
      }
    }
    return undefined;
  }

  private getFontKey(fontFamily: FontFamily): string {
    return `${fontFamily.source}_${fontFamily.name}_${fontFamily.version}`;
  }

  // Check if font is loaded
  isFontLoaded(fontFamily: FontFamily): boolean {
    return this.loadedFonts.has(this.getFontKey(fontFamily));
  }

  // Get loading statistics
  getLoadingStats(): { loaded: number; errors: number; averageTime: number } {
    const times = Array.from(this.loadingTimes.values());
    const averageTime = times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
    
    return {
      loaded: this.loadedFonts.size,
      errors: this.loadingErrors.size,
      averageTime
    };
  }

  // Clear caches
  clearCaches(): void {
    this.loadingPromises.clear();
    this.loadedFonts.clear();
    this.loadingErrors.clear();
    this.loadingTimes.clear();
  }
}

// Font preview generator
export class FontPreviewGenerator {
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;
  private previewCache: Map<string, FontPreview> = new Map();
  private maxCacheSize: number;

  constructor(maxCacheSize: number = 100) {
    this.maxCacheSize = maxCacheSize;
    this.canvas = document.createElement('canvas');
    this.canvas.width = 400;
    this.canvas.height = 80;
    this.context = this.canvas.getContext('2d')!;
  }

  generatePreview(
    fontFamily: FontFamily,
    text: string = 'The quick brown fox jumps over the lazy dog',
    fontSize: number = 24,
    weight: number | string = 'normal',
    style: string = 'normal'
  ): FontPreview {
    const cacheKey = `${fontFamily.name}_${text}_${fontSize}_${weight}_${style}`;
    
    if (this.previewCache.has(cacheKey)) {
      return this.previewCache.get(cacheKey)!;
    }

    // Clear canvas
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Set font properties
    this.context.font = `${style} ${weight} ${fontSize}px ${fontFamily.name}`;
    this.context.fillStyle = '#000000';
    this.context.textAlign = 'left';
    this.context.textBaseline = 'middle';
    
    // Calculate text position
    const textMetrics = this.context.measureText(text);
    const textWidth = textMetrics.width;
    const textHeight = fontSize;
    
    // Adjust canvas size if needed
    const padding = 20;
    const canvasWidth = Math.max(400, textWidth + padding * 2);
    const canvasHeight = Math.max(80, textHeight + padding * 2);
    
    if (canvasWidth !== this.canvas.width || canvasHeight !== this.canvas.height) {
      this.canvas.width = canvasWidth;
      this.canvas.height = canvasHeight;
      this.context.font = `${style} ${weight} ${fontSize}px ${fontFamily.name}`;
      this.context.fillStyle = '#000000';
      this.context.textAlign = 'left';
      this.context.textBaseline = 'middle';
    }
    
    // Draw text
    this.context.fillText(text, padding, canvasHeight / 2);
    
    // Create preview object
    const preview: FontPreview = {
      fontId: fontFamily.id,
      text,
      fontSize,
      weight,
      style,
      canvas: this.canvas.cloneNode(true) as HTMLCanvasElement,
      dataUrl: this.canvas.toDataURL(),
      width: canvasWidth,
      height: canvasHeight
    };

    // Cache the preview
    this.cachePreview(cacheKey, preview);
    
    return preview;
  }

  private cachePreview(key: string, preview: FontPreview): void {
    // Remove oldest entries if cache is full
    if (this.previewCache.size >= this.maxCacheSize) {
      const firstKey = this.previewCache.keys().next().value;
      this.previewCache.delete(firstKey);
    }
    
    this.previewCache.set(key, preview);
  }

  generateVariantPreviews(fontFamily: FontFamily, text?: string): FontPreview[] {
    const previews: FontPreview[] = [];
    
    for (const variant of fontFamily.variants) {
      const preview = this.generatePreview(
        fontFamily,
        text,
        24,
        variant.weight,
        variant.style
      );
      previews.push(preview);
    }
    
    return previews;
  }

  clearCache(): void {
    this.previewCache.clear();
  }

  getCacheSize(): number {
    return this.previewCache.size;
  }
}

// Main Font Library Manager
export class FontLibraryManager extends EventEmitter {
  private config: FontLibraryConfig;
  private fontLoader: FontLoader;
  private previewGenerator: FontPreviewGenerator;
  
  // Font management
  private fonts: Map<string, FontFamily> = new Map();
  private collections: Map<string, FontCollection> = new Map();
  private recentlyUsed: string[] = [];
  private favorites: Set<string> = new Set();
  
  // Caching
  private searchCache: Map<string, FontFamily[]> = new Map();
  private filterCache: Map<string, FontFamily[]> = new Map();
  
  // Statistics
  private stats: FontLibraryStats = {
    totalFonts: 0,
    loadedFonts: 0,
    categoryCounts: {} as Record<FontCategory, number>,
    sourceCounts: {} as Record<FontSource, number>,
    popularFonts: [],
    recentlyUsed: [],
    cacheSize: 0,
    memoryUsage: 0,
    loadingErrors: 0,
    averageLoadTime: 0
  };

  constructor(config: Partial<FontLibraryConfig> = {}) {
    super();
    
    this.config = {
      enableGoogleFonts: true,
      enableAdobeFonts: false,
      enableSystemFonts: true,
      enableCustomFonts: true,
      maxConcurrentLoads: 5,
      cacheSize: 200,
      previewCacheSize: 100,
      defaultLoadingStrategy: 'lazy',
      fallbackFonts: ['Arial', 'Helvetica', 'sans-serif'],
      enableFontDisplay: true,
      fontDisplayValue: 'swap',
      enableSubsetting: true,
      enableFeatureSettings: true,
      enableVariableFonts: true,
      apiKeys: {},
      ...config
    };

    this.fontLoader = new FontLoader(this.config.maxConcurrentLoads);
    this.previewGenerator = new FontPreviewGenerator(this.config.previewCacheSize);
    
    this.initializeLibrary();
  }

  private async initializeLibrary(): Promise<void> {
    // Load system fonts
    if (this.config.enableSystemFonts) {
      await this.loadSystemFonts();
    }
    
    // Load Google Fonts
    if (this.config.enableGoogleFonts) {
      await this.loadGoogleFonts();
    }
    
    // Load Adobe Fonts
    if (this.config.enableAdobeFonts && this.config.apiKeys.adobe) {
      await this.loadAdobeFonts();
    }
    
    // Load custom fonts
    if (this.config.enableCustomFonts) {
      await this.loadCustomFonts();
    }
    
    // Create default collections
    this.createDefaultCollections();
    
    // Load user preferences
    this.loadUserPreferences();
    
    this.updateStats();
    this.emit('library:initialized', this.stats);
  }

  private async loadSystemFonts(): Promise<void> {
    const systemFonts = [
      'Arial', 'Helvetica', 'Times New Roman', 'Georgia', 'Verdana',
      'Trebuchet MS', 'Impact', 'Comic Sans MS', 'Courier New', 'Lucida Console'
    ];

    for (const fontName of systemFonts) {
      const fontFamily: FontFamily = {
        id: `system_${fontName.toLowerCase().replace(/\s+/g, '_')}`,
        name: fontName,
        displayName: fontName,
        category: this.categorizeFont(fontName),
        variants: [{ weight: 'normal', style: 'normal', stretch: 'normal' }],
        subsets: ['latin'],
        version: '1.0',
        lastModified: new Date().toISOString(),
        kind: 'webfont',
        files: { regular: `local(${fontName})` },
        license: { type: 'free' },
        tags: ['system'],
        classification: {
          primary: this.categorizeFont(fontName),
          secondary: [],
          mood: ['neutral'],
          usage: ['text', 'display'],
          legibility: 'high',
          readability: 'high'
        },
        metrics: this.getDefaultMetrics(),
        loadingStatus: 'loaded',
        source: 'system'
      };

      this.fonts.set(fontFamily.id, fontFamily);
    }
  }

  private async loadGoogleFonts(): Promise<void> {
    try {
      // This would typically fetch from Google Fonts API
      // For now, we'll include a subset of popular fonts
      const popularGoogleFonts = [
        {
          family: 'Roboto',
          variants: ['100', '300', '400', '500', '700', '900'],
          subsets: ['latin', 'latin-ext'],
          category: 'sans-serif'
        },
        {
          family: 'Open Sans',
          variants: ['300', '400', '600', '700'],
          subsets: ['latin', 'latin-ext'],
          category: 'sans-serif'
        },
        {
          family: 'Lato',
          variants: ['100', '300', '400', '700', '900'],
          subsets: ['latin', 'latin-ext'],
          category: 'sans-serif'
        },
        {
          family: 'Montserrat',
          variants: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
          subsets: ['latin', 'latin-ext'],
          category: 'sans-serif'
        },
        {
          family: 'Poppins',
          variants: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
          subsets: ['latin', 'latin-ext'],
          category: 'sans-serif'
        },
        {
          family: 'Playfair Display',
          variants: ['400', '700'],
          subsets: ['latin', 'latin-ext'],
          category: 'serif'
        },
        {
          family: 'Source Code Pro',
          variants: ['200', '300', '400', '500', '600', '700'],
          subsets: ['latin', 'latin-ext'],
          category: 'monospace'
        }
      ];

      for (const googleFont of popularGoogleFonts) {
        const fontFamily: FontFamily = {
          id: `google_${googleFont.family.toLowerCase().replace(/\s+/g, '_')}`,
          name: googleFont.family,
          displayName: googleFont.family,
          category: googleFont.category as FontCategory,
          variants: googleFont.variants.map(weight => ({
            weight,
            style: 'normal',
            stretch: 'normal'
          })),
          subsets: googleFont.subsets,
          version: '1.0',
          lastModified: new Date().toISOString(),
          kind: 'webfont',
          files: { regular: `https://fonts.googleapis.com/css2?family=${googleFont.family.replace(/\s+/g, '+')}` },
          license: { type: 'open', url: 'https://scripts.sil.org/OFL' },
          tags: ['google', 'web'],
          classification: {
            primary: googleFont.category,
            secondary: [],
            mood: ['modern'],
            usage: ['text', 'display'],
            legibility: 'high',
            readability: 'high'
          },
          metrics: this.getDefaultMetrics(),
          loadingStatus: 'not-loaded',
          source: 'google'
        };

        this.fonts.set(fontFamily.id, fontFamily);
      }
    } catch (error) {
      console.error('Failed to load Google Fonts:', error);
      this.emit('library:error', { type: 'google_fonts', error });
    }
  }

  private async loadAdobeFonts(): Promise<void> {
    // Adobe Fonts integration would go here
    // This requires Adobe Creative SDK or Typekit API access
    console.log('Adobe Fonts integration not yet implemented');
  }

  private async loadCustomFonts(): Promise<void> {
    // Load custom fonts from localStorage or user uploads
    const customFonts = this.getStoredCustomFonts();
    
    for (const customFont of customFonts) {
      this.fonts.set(customFont.id, customFont);
    }
  }

  private getStoredCustomFonts(): FontFamily[] {
    try {
      const stored = localStorage.getItem('custom_fonts');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to load custom fonts:', error);
      return [];
    }
  }

  private categorizeFont(fontName: string): FontCategory {
    const serifFonts = ['Times New Roman', 'Georgia', 'Baskerville', 'Garamond'];
    const monospaceFonts = ['Courier New', 'Lucida Console', 'Monaco', 'Source Code Pro'];
    const displayFonts = ['Impact', 'Bebas Neue', 'Oswald'];
    const handwritingFonts = ['Comic Sans MS', 'Brush Script MT'];

    if (serifFonts.some(serif => fontName.includes(serif))) return 'serif';
    if (monospaceFonts.some(mono => fontName.includes(mono))) return 'monospace';
    if (displayFonts.some(display => fontName.includes(display))) return 'display';
    if (handwritingFonts.some(hand => fontName.includes(hand))) return 'handwriting';
    
    return 'sans-serif';
  }

  private getDefaultMetrics(): FontMetrics {
    return {
      xHeight: 0.5,
      capHeight: 0.7,
      ascender: 0.8,
      descender: -0.2,
      lineGap: 0.1,
      unitsPerEm: 1000,
      avgCharWidth: 0.6,
      maxCharWidth: 1.2,
      weightClass: 400,
      widthClass: 5
    };
  }

  private createDefaultCollections(): void {
    const collections: FontCollection[] = [
      {
        id: 'serif',
        name: 'Serif Fonts',
        description: 'Traditional serif fonts for body text',
        fontIds: Array.from(this.fonts.values())
          .filter(f => f.category === 'serif')
          .map(f => f.id),
        tags: ['serif', 'traditional', 'readable'],
        isSystem: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isPublic: true
      },
      {
        id: 'sans-serif',
        name: 'Sans Serif Fonts',
        description: 'Modern sans serif fonts',
        fontIds: Array.from(this.fonts.values())
          .filter(f => f.category === 'sans-serif')
          .map(f => f.id),
        tags: ['sans-serif', 'modern', 'clean'],
        isSystem: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isPublic: true
      },
      {
        id: 'monospace',
        name: 'Monospace Fonts',
        description: 'Fixed-width fonts for code',
        fontIds: Array.from(this.fonts.values())
          .filter(f => f.category === 'monospace')
          .map(f => f.id),
        tags: ['monospace', 'code', 'fixed-width'],
        isSystem: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isPublic: true
      }
    ];

    collections.forEach(collection => {
      this.collections.set(collection.id, collection);
    });
  }

  private loadUserPreferences(): void {
    try {
      const recentlyUsed = localStorage.getItem('font_recently_used');
      if (recentlyUsed) {
        this.recentlyUsed = JSON.parse(recentlyUsed);
      }

      const favorites = localStorage.getItem('font_favorites');
      if (favorites) {
        this.favorites = new Set(JSON.parse(favorites));
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    }
  }

  private updateStats(): void {
    const fonts = Array.from(this.fonts.values());
    
    this.stats.totalFonts = fonts.length;
    this.stats.loadedFonts = fonts.filter(f => f.loadingStatus === 'loaded').length;
    
    // Update category counts
    this.stats.categoryCounts = {} as Record<FontCategory, number>;
    fonts.forEach(font => {
      this.stats.categoryCounts[font.category] = (this.stats.categoryCounts[font.category] || 0) + 1;
    });
    
    // Update source counts
    this.stats.sourceCounts = {} as Record<FontSource, number>;
    fonts.forEach(font => {
      this.stats.sourceCounts[font.source] = (this.stats.sourceCounts[font.source] || 0) + 1;
    });
    
    this.stats.recentlyUsed = [...this.recentlyUsed];
    this.stats.cacheSize = this.searchCache.size + this.filterCache.size;
    
    const loaderStats = this.fontLoader.getLoadingStats();
    this.stats.loadingErrors = loaderStats.errors;
    this.stats.averageLoadTime = loaderStats.averageTime;
  }

  // Public API methods

  // Get all fonts
  getAllFonts(): FontFamily[] {
    return Array.from(this.fonts.values());
  }

  // Get font by ID
  getFont(id: string): FontFamily | undefined {
    return this.fonts.get(id);
  }

  // Search fonts
  searchFonts(query: string): FontFamily[] {
    const cacheKey = `search_${query.toLowerCase()}`;
    
    if (this.searchCache.has(cacheKey)) {
      return this.searchCache.get(cacheKey)!;
    }

    const results = Array.from(this.fonts.values()).filter(font => {
      const searchText = `${font.name} ${font.displayName} ${font.tags.join(' ')} ${font.category}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    });

    this.searchCache.set(cacheKey, results);
    return results;
  }

  // Filter fonts
  filterFonts(filter: FontFilter): FontFamily[] {
    const cacheKey = `filter_${JSON.stringify(filter)}`;
    
    if (this.filterCache.has(cacheKey)) {
      return this.filterCache.get(cacheKey)!;
    }

    let results = Array.from(this.fonts.values());

    if (filter.category?.length) {
      results = results.filter(font => filter.category!.includes(font.category));
    }

    if (filter.source?.length) {
      results = results.filter(font => filter.source!.includes(font.source));
    }

    if (filter.license?.length) {
      results = results.filter(font => filter.license!.includes(font.license.type));
    }

    if (filter.customOnly) {
      results = results.filter(font => font.customFont);
    }

    if (filter.loadedOnly) {
      results = results.filter(font => font.loadingStatus === 'loaded');
    }

    if (filter.trending) {
      results = results.filter(font => font.trending);
    }

    if (filter.search) {
      const searchQuery = filter.search.toLowerCase();
      results = results.filter(font => {
        const searchText = `${font.name} ${font.displayName} ${font.tags.join(' ')} ${font.category}`.toLowerCase();
        return searchText.includes(searchQuery);
      });
    }

    if (filter.tags?.length) {
      results = results.filter(font => 
        filter.tags!.some(tag => font.tags.includes(tag))
      );
    }

    this.filterCache.set(cacheKey, results);
    return results;
  }

  // Load font
  async loadFont(fontId: string, options?: FontLoadingOptions): Promise<FontFace> {
    const font = this.fonts.get(fontId);
    if (!font) {
      throw new Error(`Font not found: ${fontId}`);
    }

    // Update loading status
    font.loadingStatus = 'loading';
    this.emit('font:loading', { fontId, font });

    try {
      const fontFace = await this.fontLoader.loadFont(font, options);
      
      // Update status and track usage
      font.loadingStatus = 'loaded';
      this.trackFontUsage(fontId);
      
      this.emit('font:loaded', { fontId, font, fontFace });
      return fontFace;
    } catch (error) {
      font.loadingStatus = 'error';
      this.emit('font:error', { fontId, font, error });
      throw error;
    }
  }

  // Preview generation
  generatePreview(fontId: string, text?: string, options?: { fontSize?: number; weight?: number | string; style?: string }): FontPreview | null {
    const font = this.fonts.get(fontId);
    if (!font) return null;

    return this.previewGenerator.generatePreview(
      font,
      text,
      options?.fontSize,
      options?.weight,
      options?.style
    );
  }

  // Font collection management
  createCollection(name: string, description: string, fontIds: string[]): string {
    const id = `collection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const collection: FontCollection = {
      id,
      name,
      description,
      fontIds: [...fontIds],
      tags: [],
      isSystem: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isPublic: false
    };

    this.collections.set(id, collection);
    this.emit('collection:created', { id, collection });
    
    return id;
  }

  getCollection(id: string): FontCollection | undefined {
    return this.collections.get(id);
  }

  getAllCollections(): FontCollection[] {
    return Array.from(this.collections.values());
  }

  updateCollection(id: string, updates: Partial<FontCollection>): boolean {
    const collection = this.collections.get(id);
    if (!collection) return false;

    Object.assign(collection, updates, { updatedAt: new Date().toISOString() });
    this.emit('collection:updated', { id, collection });
    
    return true;
  }

  deleteCollection(id: string): boolean {
    const collection = this.collections.get(id);
    if (!collection || collection.isSystem) return false;

    this.collections.delete(id);
    this.emit('collection:deleted', { id });
    
    return true;
  }

  // Favorites management
  addToFavorites(fontId: string): void {
    this.favorites.add(fontId);
    this.saveFavorites();
    this.emit('favorites:updated', { fontId, action: 'added' });
  }

  removeFromFavorites(fontId: string): void {
    this.favorites.delete(fontId);
    this.saveFavorites();
    this.emit('favorites:updated', { fontId, action: 'removed' });
  }

  getFavorites(): FontFamily[] {
    return Array.from(this.favorites)
      .map(id => this.fonts.get(id))
      .filter(font => font) as FontFamily[];
  }

  isFavorite(fontId: string): boolean {
    return this.favorites.has(fontId);
  }

  // Recently used tracking
  private trackFontUsage(fontId: string): void {
    // Remove if already in recently used
    const existingIndex = this.recentlyUsed.indexOf(fontId);
    if (existingIndex !== -1) {
      this.recentlyUsed.splice(existingIndex, 1);
    }

    // Add to beginning
    this.recentlyUsed.unshift(fontId);

    // Keep only last 20
    if (this.recentlyUsed.length > 20) {
      this.recentlyUsed = this.recentlyUsed.slice(0, 20);
    }

    this.saveRecentlyUsed();
  }

  getRecentlyUsed(): FontFamily[] {
    return this.recentlyUsed
      .map(id => this.fonts.get(id))
      .filter(font => font) as FontFamily[];
  }

  // Custom font management
  async addCustomFont(fontFile: File, metadata: Partial<FontFamily> = {}): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const fontData = e.target?.result as ArrayBuffer;
        
        try {
          const fontFamily = this.createCustomFontFamily(fontFile, fontData, metadata);
          this.fonts.set(fontFamily.id, fontFamily);
          this.saveCustomFonts();
          
          this.emit('custom:font:added', { fontFamily });
          resolve(fontFamily.id);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(new Error('Failed to read font file'));
      reader.readAsArrayBuffer(fontFile);
    });
  }

  private createCustomFontFamily(file: File, data: ArrayBuffer, metadata: Partial<FontFamily>): FontFamily {
    const fileName = file.name.replace(/\.[^/.]+$/, '');
    const fontUrl = URL.createObjectURL(new Blob([data], { type: file.type }));
    
    const fontFamily: FontFamily = {
      id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: metadata.name || fileName,
      displayName: metadata.displayName || fileName,
      category: metadata.category || 'sans-serif',
      variants: metadata.variants || [{ weight: 'normal', style: 'normal', stretch: 'normal' }],
      subsets: metadata.subsets || ['latin'],
      version: '1.0',
      lastModified: new Date().toISOString(),
      kind: 'webfont',
      files: { regular: fontUrl },
      license: metadata.license || { type: 'custom' },
      tags: [...(metadata.tags || []), 'custom'],
      classification: metadata.classification || {
        primary: 'custom',
        secondary: [],
        mood: ['custom'],
        usage: ['text'],
        legibility: 'medium',
        readability: 'medium'
      },
      metrics: metadata.metrics || this.getDefaultMetrics(),
      loadingStatus: 'not-loaded',
      customFont: true,
      source: 'custom'
    };

    return fontFamily;
  }

  removeCustomFont(fontId: string): boolean {
    const font = this.fonts.get(fontId);
    if (!font || !font.customFont) return false;

    // Revoke object URL
    if (font.files.regular.startsWith('blob:')) {
      URL.revokeObjectURL(font.files.regular);
    }

    this.fonts.delete(fontId);
    this.saveCustomFonts();
    
    this.emit('custom:font:removed', { fontId });
    return true;
  }

  // Persistence
  private saveFavorites(): void {
    localStorage.setItem('font_favorites', JSON.stringify(Array.from(this.favorites)));
  }

  private saveRecentlyUsed(): void {
    localStorage.setItem('font_recently_used', JSON.stringify(this.recentlyUsed));
  }

  private saveCustomFonts(): void {
    const customFonts = Array.from(this.fonts.values()).filter(f => f.customFont);
    localStorage.setItem('custom_fonts', JSON.stringify(customFonts));
  }

  // Configuration
  updateConfig(config: Partial<FontLibraryConfig>): void {
    Object.assign(this.config, config);
    this.emit('config:updated', config);
  }

  getConfig(): FontLibraryConfig {
    return { ...this.config };
  }

  // Statistics
  getStats(): FontLibraryStats {
    this.updateStats();
    return { ...this.stats };
  }

  // Performance
  clearCaches(): void {
    this.searchCache.clear();
    this.filterCache.clear();
    this.fontLoader.clearCaches();
    this.previewGenerator.clearCache();
    this.emit('caches:cleared');
  }

  // Cleanup
  dispose(): void {
    // Clear all caches
    this.clearCaches();
    
    // Revoke custom font URLs
    this.fonts.forEach(font => {
      if (font.customFont && font.files.regular.startsWith('blob:')) {
        URL.revokeObjectURL(font.files.regular);
      }
    });
    
    // Clear collections
    this.fonts.clear();
    this.collections.clear();
    
    this.removeAllListeners();
    this.emit('library:disposed');
  }
}