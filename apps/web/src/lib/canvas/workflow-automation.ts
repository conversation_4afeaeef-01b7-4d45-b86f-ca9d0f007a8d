import { EventEmitter } from 'events';
import { AdvancedCanvasOperationsManager, AdvancedCanvasWorkflow, AdvancedCanvasOperation } from './advanced-canvas-operations';

// Workflow automation types
export interface AutomationTrigger {
  id: string;
  type: AutomationTriggerType;
  name: string;
  description: string;
  conditions: AutomationCondition[];
  parameters: Record<string, any>;
  enabled: boolean;
  priority: number;
}

export type AutomationTriggerType = 
  | 'layer-created'
  | 'layer-modified'
  | 'ai-generation-completed'
  | 'style-transfer-completed'
  | 'effect-applied'
  | 'typography-changed'
  | 'user-action'
  | 'time-based'
  | 'performance-threshold'
  | 'content-analysis';

export interface AutomationCondition {
  id: string;
  type: 'equals' | 'contains' | 'greater-than' | 'less-than' | 'matches' | 'custom';
  property: string;
  value: any;
  operator: 'and' | 'or';
}

export interface AutomationAction {
  id: string;
  type: AutomationActionType;
  name: string;
  description: string;
  parameters: Record<string, any>;
  workflowId?: string;
  operationId?: string;
  customFunction?: (context: AutomationContext) => Promise<void>;
  timeout: number;
  retryCount: number;
}

export type AutomationActionType = 
  | 'execute-workflow'
  | 'execute-operation'
  | 'create-layer'
  | 'apply-effect'
  | 'adjust-typography'
  | 'generate-ai-content'
  | 'send-notification'
  | 'save-state'
  | 'export-content'
  | 'custom-function';

export interface AutomationRule {
  id: string;
  name: string;
  description: string;
  triggers: AutomationTrigger[];
  actions: AutomationAction[];
  conditions: AutomationCondition[];
  enabled: boolean;
  priority: number;
  category: 'productivity' | 'quality' | 'performance' | 'creative' | 'maintenance';
  executionMode: 'immediate' | 'queued' | 'background';
  maxExecutions: number;
  executionCount: number;
  lastExecuted: number;
  averageExecutionTime: number;
}

export interface AutomationContext {
  triggerId: string;
  triggerData: any;
  canvasState: any;
  userContext: any;
  sessionData: any;
  timestamp: number;
  executionId: string;
}

export interface AutomationSchedule {
  id: string;
  name: string;
  description: string;
  ruleId: string;
  schedule: {
    type: 'interval' | 'cron' | 'once';
    expression: string;
    timezone?: string;
  };
  enabled: boolean;
  nextExecution: number;
  lastExecution: number;
  executionHistory: Array<{
    timestamp: number;
    success: boolean;
    duration: number;
    error?: string;
  }>;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'design' | 'ai' | 'effects' | 'typography' | 'export' | 'maintenance';
  workflow: AdvancedCanvasWorkflow;
  parameters: Array<{
    name: string;
    type: 'string' | 'number' | 'boolean' | 'color' | 'layer' | 'image';
    description: string;
    required: boolean;
    default?: any;
    validation?: {
      min?: number;
      max?: number;
      pattern?: string;
      options?: string[];
    };
  }>;
  examples: Array<{
    name: string;
    description: string;
    parameters: Record<string, any>;
  }>;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  estimatedTime: number;
  requiredSystems: string[];
}

export interface AutomationMetrics {
  totalRules: number;
  activeRules: number;
  totalExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  errorRate: number;
  performanceImpact: number;
  mostUsedRules: Array<{
    ruleId: string;
    name: string;
    executionCount: number;
  }>;
  recentErrors: Array<{
    ruleId: string;
    error: string;
    timestamp: number;
  }>;
}

export interface WorkflowAutomationConfig {
  enableAutomation: boolean;
  maxConcurrentRules: number;
  maxQueuedActions: number;
  defaultTimeout: number;
  enablePerformanceMonitoring: boolean;
  enableLogging: boolean;
  enableNotifications: boolean;
  autoSaveInterval: number;
  maxExecutionHistory: number;
}

/**
 * Workflow Automation Engine
 * Manages automated workflows and intelligent content processing
 */
export class WorkflowAutomationEngine extends EventEmitter {
  private operationsManager: AdvancedCanvasOperationsManager;
  private config: WorkflowAutomationConfig;
  
  private rules: Map<string, AutomationRule> = new Map();
  private triggers: Map<string, AutomationTrigger> = new Map();
  private actions: Map<string, AutomationAction> = new Map();
  private schedules: Map<string, AutomationSchedule> = new Map();
  private templates: Map<string, WorkflowTemplate> = new Map();
  
  private triggerListeners: Map<string, Function[]> = new Map();
  private actionQueue: Array<{
    ruleId: string;
    actionId: string;
    context: AutomationContext;
    priority: number;
    timestamp: number;
  }> = [];
  
  private metrics: AutomationMetrics;
  private isProcessing: boolean = false;
  private scheduledJobs: Map<string, NodeJS.Timeout> = new Map();
  
  constructor(
    operationsManager: AdvancedCanvasOperationsManager,
    config: Partial<WorkflowAutomationConfig> = {}
  ) {
    super();
    
    this.operationsManager = operationsManager;
    this.config = {
      enableAutomation: true,
      maxConcurrentRules: 10,
      maxQueuedActions: 50,
      defaultTimeout: 30000,
      enablePerformanceMonitoring: true,
      enableLogging: true,
      enableNotifications: true,
      autoSaveInterval: 60000,
      maxExecutionHistory: 100,
      ...config
    };
    
    this.metrics = {
      totalRules: 0,
      activeRules: 0,
      totalExecutions: 0,
      successRate: 0,
      averageExecutionTime: 0,
      errorRate: 0,
      performanceImpact: 0,
      mostUsedRules: [],
      recentErrors: []
    };
    
    this.initializeBuiltInTriggers();
    this.initializeBuiltInActions();
    this.initializeWorkflowTemplates();
    this.setupOperationsManagerListeners();
    this.startActionProcessor();
  }

  /**
   * Initialize the workflow automation engine
   */
  async initialize(): Promise<void> {
    if (!this.config.enableAutomation) {
      return;
    }
    
    try {
      // Load saved rules and schedules
      await this.loadSavedRules();
      await this.loadSavedSchedules();
      
      // Start scheduled jobs
      this.startScheduledJobs();
      
      // Start performance monitoring
      if (this.config.enablePerformanceMonitoring) {
        this.startPerformanceMonitoring();
      }
      
      this.emit('automation:initialized');
      
    } catch (error) {
      console.error('Failed to initialize workflow automation:', error);
      throw error;
    }
  }

  /**
   * Create a new automation rule
   */
  createRule(
    name: string,
    description: string,
    triggers: string[],
    actions: string[],
    conditions: AutomationCondition[] = [],
    options: Partial<AutomationRule> = {}
  ): string {
    const ruleId = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const rule: AutomationRule = {
      id: ruleId,
      name,
      description,
      triggers: triggers.map(id => this.triggers.get(id)!).filter(Boolean),
      actions: actions.map(id => this.actions.get(id)!).filter(Boolean),
      conditions,
      enabled: true,
      priority: 5,
      category: 'productivity',
      executionMode: 'immediate',
      maxExecutions: -1,
      executionCount: 0,
      lastExecuted: 0,
      averageExecutionTime: 0,
      ...options
    };
    
    this.rules.set(ruleId, rule);
    this.updateMetrics();
    
    // Set up trigger listeners
    this.setupRuleTriggerListeners(rule);
    
    this.emit('rule:created', { ruleId, rule });
    
    return ruleId;
  }

  /**
   * Execute a workflow template with parameters
   */
  async executeTemplate(
    templateId: string,
    parameters: Record<string, any>
  ): Promise<any> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }
    
    // Validate parameters
    this.validateTemplateParameters(template, parameters);
    
    // Create execution context
    const context: AutomationContext = {
      triggerId: `template_${templateId}`,
      triggerData: { templateId, parameters },
      canvasState: {},
      userContext: {},
      sessionData: {},
      timestamp: Date.now(),
      executionId: `template_${templateId}_${Date.now()}`
    };
    
    try {
      // Execute the template's workflow
      const result = await this.operationsManager.executeWorkflow(
        template.workflow.id,
        parameters
      );
      
      this.emit('template:executed', { templateId, parameters, result });
      
      return result;
      
    } catch (error) {
      this.emit('template:failed', { templateId, parameters, error });
      throw error;
    }
  }

  /**
   * Create a custom workflow template
   */
  createTemplate(
    name: string,
    description: string,
    category: WorkflowTemplate['category'],
    workflow: AdvancedCanvasWorkflow,
    parameters: WorkflowTemplate['parameters'],
    options: Partial<WorkflowTemplate> = {}
  ): string {
    const templateId = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const template: WorkflowTemplate = {
      id: templateId,
      name,
      description,
      category,
      workflow,
      parameters,
      examples: [],
      tags: [],
      difficulty: 'intermediate',
      estimatedTime: 60000,
      requiredSystems: [],
      ...options
    };
    
    this.templates.set(templateId, template);
    
    this.emit('template:created', { templateId, template });
    
    return templateId;
  }

  /**
   * Schedule a rule for automated execution
   */
  scheduleRule(
    ruleId: string,
    schedule: AutomationSchedule['schedule'],
    options: Partial<AutomationSchedule> = {}
  ): string {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      throw new Error(`Rule ${ruleId} not found`);
    }
    
    const scheduleId = `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const automationSchedule: AutomationSchedule = {
      id: scheduleId,
      name: `${rule.name} Schedule`,
      description: `Automated schedule for ${rule.name}`,
      ruleId,
      schedule,
      enabled: true,
      nextExecution: this.calculateNextExecution(schedule),
      lastExecution: 0,
      executionHistory: [],
      ...options
    };
    
    this.schedules.set(scheduleId, automationSchedule);
    this.setupScheduledJob(automationSchedule);
    
    this.emit('schedule:created', { scheduleId, schedule: automationSchedule });
    
    return scheduleId;
  }

  /**
   * Get available workflow templates
   */
  getTemplates(category?: WorkflowTemplate['category']): WorkflowTemplate[] {
    const templates = Array.from(this.templates.values());
    return category ? templates.filter(t => t.category === category) : templates;
  }

  /**
   * Get automation rules
   */
  getRules(): AutomationRule[] {
    return Array.from(this.rules.values());
  }

  /**
   * Get automation metrics
   */
  getMetrics(): AutomationMetrics {
    return { ...this.metrics };
  }

  /**
   * Enable or disable a rule
   */
  toggleRule(ruleId: string, enabled: boolean): void {
    const rule = this.rules.get(ruleId);
    if (rule) {
      rule.enabled = enabled;
      this.emit('rule:toggled', { ruleId, enabled });
    }
  }

  /**
   * Delete a rule
   */
  deleteRule(ruleId: string): boolean {
    const rule = this.rules.get(ruleId);
    if (rule) {
      this.rules.delete(ruleId);
      this.updateMetrics();
      this.emit('rule:deleted', { ruleId });
      return true;
    }
    return false;
  }

  /**
   * Dispose of resources
   */
  dispose(): void {
    // Clear scheduled jobs
    this.scheduledJobs.forEach(job => clearTimeout(job));
    this.scheduledJobs.clear();
    
    // Clear trigger listeners
    this.triggerListeners.clear();
    
    // Clear queues
    this.actionQueue = [];
    
    this.removeAllListeners();
  }

  // Private methods
  private initializeBuiltInTriggers(): void {
    // Layer created trigger
    this.triggers.set('layer-created', {
      id: 'layer-created',
      type: 'layer-created',
      name: 'Layer Created',
      description: 'Triggered when a new layer is created',
      conditions: [],
      parameters: {
        layerType: 'any',
        minSize: 0,
        maxSize: Infinity
      },
      enabled: true,
      priority: 5
    });

    // AI generation completed trigger
    this.triggers.set('ai-generation-completed', {
      id: 'ai-generation-completed',
      type: 'ai-generation-completed',
      name: 'AI Generation Completed',
      description: 'Triggered when AI image generation completes',
      conditions: [],
      parameters: {
        provider: 'any',
        quality: 'any',
        minConfidence: 0.5
      },
      enabled: true,
      priority: 7
    });

    // Performance threshold trigger
    this.triggers.set('performance-threshold', {
      id: 'performance-threshold',
      type: 'performance-threshold',
      name: 'Performance Threshold',
      description: 'Triggered when performance metrics exceed thresholds',
      conditions: [],
      parameters: {
        metric: 'memory',
        threshold: 80,
        operator: 'greater-than'
      },
      enabled: true,
      priority: 9
    });

    // Add more built-in triggers...
  }

  private initializeBuiltInActions(): void {
    // Execute workflow action
    this.actions.set('execute-workflow', {
      id: 'execute-workflow',
      type: 'execute-workflow',
      name: 'Execute Workflow',
      description: 'Execute a predefined workflow',
      parameters: {
        workflowId: '',
        parameters: {}
      },
      timeout: 120000,
      retryCount: 2
    });

    // Apply effect action
    this.actions.set('apply-effect', {
      id: 'apply-effect',
      type: 'apply-effect',
      name: 'Apply Effect',
      description: 'Apply an effect to a layer',
      parameters: {
        layerId: '',
        effectId: '',
        parameters: {}
      },
      timeout: 30000,
      retryCount: 1
    });

    // Generate AI content action
    this.actions.set('generate-ai-content', {
      id: 'generate-ai-content',
      type: 'generate-ai-content',
      name: 'Generate AI Content',
      description: 'Generate content using AI',
      parameters: {
        prompt: '',
        provider: 'auto',
        quality: 'high'
      },
      timeout: 120000,
      retryCount: 3
    });

    // Add more built-in actions...
  }

  private initializeWorkflowTemplates(): void {
    // Professional Photo Enhancement Template
    this.templates.set('photo-enhancement', {
      id: 'photo-enhancement',
      name: 'Professional Photo Enhancement',
      description: 'Enhance photos with AI-powered adjustments and effects',
      category: 'design',
      workflow: {
        id: 'photo-enhancement-workflow',
        name: 'Photo Enhancement Workflow',
        description: 'Professional photo enhancement process',
        operations: [], // Would be populated with actual operations
        globalParameters: {},
        executionOrder: [],
        parallelizable: false,
        category: 'professional'
      },
      parameters: [
        {
          name: 'inputImage',
          type: 'image',
          description: 'Source image to enhance',
          required: true
        },
        {
          name: 'enhancement_level',
          type: 'string',
          description: 'Level of enhancement to apply',
          required: false,
          default: 'moderate',
          validation: {
            options: ['light', 'moderate', 'heavy', 'custom']
          }
        },
        {
          name: 'preserve_original',
          type: 'boolean',
          description: 'Keep original image as backup layer',
          required: false,
          default: true
        }
      ],
      examples: [
        {
          name: 'Portrait Enhancement',
          description: 'Enhance a portrait photo',
          parameters: {
            enhancement_level: 'moderate',
            preserve_original: true
          }
        }
      ],
      tags: ['photo', 'enhancement', 'ai', 'professional'],
      difficulty: 'intermediate',
      estimatedTime: 45000,
      requiredSystems: ['ai', 'effects', 'layers']
    });

    // Add more templates...
  }

  private setupOperationsManagerListeners(): void {
    this.operationsManager.on('operation:completed', (data) => {
      this.triggerEvent('operation-completed', data);
    });

    this.operationsManager.on('cross-system:layer-created', (data) => {
      this.triggerEvent('layer-created', data);
    });

    this.operationsManager.on('cross-system:ai-generated', (data) => {
      this.triggerEvent('ai-generation-completed', data);
    });

    // Add more listeners...
  }

  private setupRuleTriggerListeners(rule: AutomationRule): void {
    rule.triggers.forEach(trigger => {
      if (!this.triggerListeners.has(trigger.id)) {
        this.triggerListeners.set(trigger.id, []);
      }
      
      const listener = (data: any) => {
        this.evaluateRule(rule, trigger, data);
      };
      
      this.triggerListeners.get(trigger.id)!.push(listener);
    });
  }

  private triggerEvent(eventType: string, data: any): void {
    const listeners = this.triggerListeners.get(eventType) || [];
    listeners.forEach(listener => listener(data));
  }

  private evaluateRule(rule: AutomationRule, trigger: AutomationTrigger, data: any): void {
    if (!rule.enabled || !trigger.enabled) return;
    
    // Check execution limits
    if (rule.maxExecutions > 0 && rule.executionCount >= rule.maxExecutions) {
      return;
    }
    
    // Evaluate conditions
    const conditionsMet = this.evaluateConditions(rule.conditions, data);
    if (!conditionsMet) return;
    
    // Create execution context
    const context: AutomationContext = {
      triggerId: trigger.id,
      triggerData: data,
      canvasState: {},
      userContext: {},
      sessionData: {},
      timestamp: Date.now(),
      executionId: `${rule.id}_${Date.now()}`
    };
    
    // Queue actions for execution
    rule.actions.forEach(action => {
      this.queueAction(rule.id, action.id, context, rule.priority);
    });
  }

  private evaluateConditions(conditions: AutomationCondition[], data: any): boolean {
    if (conditions.length === 0) return true;
    
    let result = true;
    let currentOperator: 'and' | 'or' = 'and';
    
    for (const condition of conditions) {
      const conditionResult = this.evaluateCondition(condition, data);
      
      if (currentOperator === 'and') {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }
      
      currentOperator = condition.operator;
    }
    
    return result;
  }

  private evaluateCondition(condition: AutomationCondition, data: any): boolean {
    const value = this.getPropertyValue(data, condition.property);
    
    switch (condition.type) {
      case 'equals':
        return value === condition.value;
      case 'contains':
        return String(value).includes(condition.value);
      case 'greater-than':
        return Number(value) > Number(condition.value);
      case 'less-than':
        return Number(value) < Number(condition.value);
      case 'matches':
        return new RegExp(condition.value).test(String(value));
      default:
        return false;
    }
  }

  private getPropertyValue(obj: any, property: string): any {
    return property.split('.').reduce((current, key) => current?.[key], obj);
  }

  private queueAction(
    ruleId: string,
    actionId: string,
    context: AutomationContext,
    priority: number
  ): void {
    if (this.actionQueue.length >= this.config.maxQueuedActions) {
      console.warn('Action queue full, dropping action');
      return;
    }
    
    this.actionQueue.push({
      ruleId,
      actionId,
      context,
      priority,
      timestamp: Date.now()
    });
    
    // Sort by priority and timestamp
    this.actionQueue.sort((a, b) => {
      if (a.priority !== b.priority) return b.priority - a.priority;
      return a.timestamp - b.timestamp;
    });
  }

  private startActionProcessor(): void {
    setInterval(async () => {
      if (this.isProcessing || this.actionQueue.length === 0) return;
      
      this.isProcessing = true;
      
      try {
        const action = this.actionQueue.shift()!;
        await this.executeAction(action);
      } catch (error) {
        console.error('Action execution failed:', error);
      } finally {
        this.isProcessing = false;
      }
    }, 100);
  }

  private async executeAction(queuedAction: any): Promise<void> {
    const rule = this.rules.get(queuedAction.ruleId);
    const action = this.actions.get(queuedAction.actionId);
    
    if (!rule || !action) return;
    
    const startTime = performance.now();
    
    try {
      await this.executeActionByType(action, queuedAction.context);
      
      // Update metrics
      rule.executionCount++;
      rule.lastExecuted = Date.now();
      
      const executionTime = performance.now() - startTime;
      rule.averageExecutionTime = (rule.averageExecutionTime + executionTime) / 2;
      
      this.emit('action:executed', {
        ruleId: rule.id,
        actionId: action.id,
        executionTime,
        context: queuedAction.context
      });
      
    } catch (error) {
      this.metrics.recentErrors.push({
        ruleId: rule.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      });
      
      this.emit('action:failed', {
        ruleId: rule.id,
        actionId: action.id,
        error,
        context: queuedAction.context
      });
    }
  }

  private async executeActionByType(action: AutomationAction, context: AutomationContext): Promise<void> {
    switch (action.type) {
      case 'execute-workflow':
        await this.operationsManager.executeWorkflow(
          action.parameters.workflowId,
          action.parameters.parameters
        );
        break;
        
      case 'execute-operation':
        await this.operationsManager.executeOperation(
          action.parameters.operationId,
          action.parameters.parameters
        );
        break;
        
      case 'custom-function':
        if (action.customFunction) {
          await action.customFunction(context);
        }
        break;
        
      default:
        console.warn(`Unknown action type: ${action.type}`);
    }
  }

  private validateTemplateParameters(template: WorkflowTemplate, parameters: Record<string, any>): void {
    for (const param of template.parameters) {
      if (param.required && !(param.name in parameters)) {
        throw new Error(`Required parameter ${param.name} is missing`);
      }
      
      const value = parameters[param.name];
      if (value !== undefined && param.validation) {
        this.validateParameterValue(param, value);
      }
    }
  }

  private validateParameterValue(param: WorkflowTemplate['parameters'][0], value: any): void {
    if (param.validation) {
      const validation = param.validation;
      
      if (validation.min !== undefined && Number(value) < validation.min) {
        throw new Error(`Parameter ${param.name} must be at least ${validation.min}`);
      }
      
      if (validation.max !== undefined && Number(value) > validation.max) {
        throw new Error(`Parameter ${param.name} must be at most ${validation.max}`);
      }
      
      if (validation.pattern && !new RegExp(validation.pattern).test(String(value))) {
        throw new Error(`Parameter ${param.name} does not match required pattern`);
      }
      
      if (validation.options && !validation.options.includes(value)) {
        throw new Error(`Parameter ${param.name} must be one of: ${validation.options.join(', ')}`);
      }
    }
  }

  private calculateNextExecution(schedule: AutomationSchedule['schedule']): number {
    const now = Date.now();
    
    switch (schedule.type) {
      case 'interval':
        return now + parseInt(schedule.expression);
      case 'once':
        return parseInt(schedule.expression);
      case 'cron':
        // Implement cron parsing
        return now + 60000; // Placeholder
      default:
        return now + 60000;
    }
  }

  private setupScheduledJob(schedule: AutomationSchedule): void {
    const delay = schedule.nextExecution - Date.now();
    
    if (delay > 0) {
      const timeout = setTimeout(() => {
        this.executeScheduledRule(schedule);
      }, delay);
      
      this.scheduledJobs.set(schedule.id, timeout);
    }
  }

  private executeScheduledRule(schedule: AutomationSchedule): void {
    const rule = this.rules.get(schedule.ruleId);
    if (!rule || !rule.enabled) return;
    
    const context: AutomationContext = {
      triggerId: 'scheduled',
      triggerData: { scheduleId: schedule.id },
      canvasState: {},
      userContext: {},
      sessionData: {},
      timestamp: Date.now(),
      executionId: `scheduled_${schedule.id}_${Date.now()}`
    };
    
    rule.actions.forEach(action => {
      this.queueAction(rule.id, action.id, context, rule.priority);
    });
    
    // Update schedule
    schedule.lastExecution = Date.now();
    schedule.nextExecution = this.calculateNextExecution(schedule.schedule);
    
    // Schedule next execution
    this.setupScheduledJob(schedule);
  }

  private startScheduledJobs(): void {
    this.schedules.forEach(schedule => {
      if (schedule.enabled) {
        this.setupScheduledJob(schedule);
      }
    });
  }

  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.updateMetrics();
    }, 5000);
  }

  private updateMetrics(): void {
    const rules = Array.from(this.rules.values());
    
    this.metrics.totalRules = rules.length;
    this.metrics.activeRules = rules.filter(r => r.enabled).length;
    this.metrics.totalExecutions = rules.reduce((sum, r) => sum + r.executionCount, 0);
    
    const totalTime = rules.reduce((sum, r) => sum + r.averageExecutionTime, 0);
    this.metrics.averageExecutionTime = totalTime / rules.length || 0;
    
    this.metrics.mostUsedRules = rules
      .sort((a, b) => b.executionCount - a.executionCount)
      .slice(0, 5)
      .map(r => ({
        ruleId: r.id,
        name: r.name,
        executionCount: r.executionCount
      }));
    
    // Keep only recent errors
    this.metrics.recentErrors = this.metrics.recentErrors
      .filter(e => Date.now() - e.timestamp < 300000) // 5 minutes
      .slice(0, 10);
    
    this.emit('metrics:updated', this.metrics);
  }

  private async loadSavedRules(): Promise<void> {
    try {
      const saved = localStorage.getItem('workflow-automation-rules');
      if (saved) {
        const rules = JSON.parse(saved);
        rules.forEach((rule: AutomationRule) => {
          this.rules.set(rule.id, rule);
          this.setupRuleTriggerListeners(rule);
        });
      }
    } catch (error) {
      console.warn('Failed to load saved rules:', error);
    }
  }

  private async loadSavedSchedules(): Promise<void> {
    try {
      const saved = localStorage.getItem('workflow-automation-schedules');
      if (saved) {
        const schedules = JSON.parse(saved);
        schedules.forEach((schedule: AutomationSchedule) => {
          this.schedules.set(schedule.id, schedule);
        });
      }
    } catch (error) {
      console.warn('Failed to load saved schedules:', error);
    }
  }
}