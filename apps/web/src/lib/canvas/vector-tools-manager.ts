import { fabric } from 'fabric';
import { EventEmitter } from 'events';
import { VectorGraphicsEngine, VectorTool, VectorSelection, VectorEditingOptions } from './vector-graphics-engine';
import { ShapeLibrary, ShapeCreationOptions } from './shape-library';

// Tool configuration and state management
export interface VectorToolConfig {
  id: string;
  name: string;
  icon: string;
  description: string;
  category: 'selection' | 'drawing' | 'shape' | 'text' | 'modification';
  shortcuts: string[];
  cursor: string;
  options: ToolOption[];
  enabled: boolean;
}

export interface ToolOption {
  name: string;
  type: 'number' | 'boolean' | 'color' | 'select' | 'range' | 'text';
  defaultValue: any;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{ value: any; label: string }>;
  description?: string;
  group?: string;
}

export interface ToolState {
  activeTool: VectorTool;
  toolOptions: Record<string, any>;
  isDrawing: boolean;
  selectionMode: 'single' | 'multiple' | 'additive';
  snapSettings: {
    snapToGrid: boolean;
    snapToGuides: boolean;
    snapToObjects: boolean;
    snapTolerance: number;
  };
  editingOptions: VectorEditingOptions;
}

export interface ToolWorkflow {
  id: string;
  name: string;
  description: string;
  steps: ToolWorkflowStep[];
  category: string;
}

export interface ToolWorkflowStep {
  tool: VectorTool;
  action: string;
  options?: Record<string, any>;
  description: string;
  autoAdvance?: boolean;
}

// Professional vector tool operations
export class VectorToolOperations {
  private vectorEngine: VectorGraphicsEngine;
  private canvas: fabric.Canvas;

  constructor(vectorEngine: VectorGraphicsEngine, canvas: fabric.Canvas) {
    this.vectorEngine = vectorEngine;
    this.canvas = canvas;
  }

  // Selection operations
  selectAll(): void {
    const allObjects = this.canvas.getObjects().filter(obj => (obj as any).vectorId);
    const selection = new fabric.ActiveSelection(allObjects, {
      canvas: this.canvas
    });
    this.canvas.setActiveObject(selection);
    this.canvas.renderAll();
  }

  selectNone(): void {
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
  }

  selectInvert(): void {
    const allObjects = this.canvas.getObjects().filter(obj => (obj as any).vectorId);
    const activeObjects = this.canvas.getActiveObjects();
    const invertedSelection = allObjects.filter(obj => !activeObjects.includes(obj));
    
    if (invertedSelection.length > 0) {
      const selection = new fabric.ActiveSelection(invertedSelection, {
        canvas: this.canvas
      });
      this.canvas.setActiveObject(selection);
    } else {
      this.canvas.discardActiveObject();
    }
    this.canvas.renderAll();
  }

  selectByType(objectType: string): void {
    const objectsOfType = this.canvas.getObjects().filter(obj => 
      (obj as any).vectorId && obj.type === objectType
    );
    
    if (objectsOfType.length > 0) {
      const selection = new fabric.ActiveSelection(objectsOfType, {
        canvas: this.canvas
      });
      this.canvas.setActiveObject(selection);
      this.canvas.renderAll();
    }
  }

  // Transform operations
  alignObjects(alignment: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom'): void {
    const activeObjects = this.canvas.getActiveObjects();
    if (activeObjects.length < 2) return;

    const bounds = this.getSelectionBounds(activeObjects);
    
    activeObjects.forEach(obj => {
      switch (alignment) {
        case 'left':
          obj.set({ left: bounds.left });
          break;
        case 'center':
          obj.set({ left: bounds.left + (bounds.width - (obj.width || 0)) / 2 });
          break;
        case 'right':
          obj.set({ left: bounds.left + bounds.width - (obj.width || 0) });
          break;
        case 'top':
          obj.set({ top: bounds.top });
          break;
        case 'middle':
          obj.set({ top: bounds.top + (bounds.height - (obj.height || 0)) / 2 });
          break;
        case 'bottom':
          obj.set({ top: bounds.top + bounds.height - (obj.height || 0) });
          break;
      }
      obj.setCoords();
    });

    this.canvas.renderAll();
  }

  distributeObjects(distribution: 'horizontal' | 'vertical'): void {
    const activeObjects = this.canvas.getActiveObjects();
    if (activeObjects.length < 3) return;

    // Sort objects by position
    const sortedObjects = [...activeObjects].sort((a, b) => {
      if (distribution === 'horizontal') {
        return (a.left || 0) - (b.left || 0);
      } else {
        return (a.top || 0) - (b.top || 0);
      }
    });

    const first = sortedObjects[0];
    const last = sortedObjects[sortedObjects.length - 1];
    
    if (distribution === 'horizontal') {
      const totalSpace = (last.left || 0) - (first.left || 0);
      const spacing = totalSpace / (sortedObjects.length - 1);
      
      sortedObjects.forEach((obj, index) => {
        if (index > 0 && index < sortedObjects.length - 1) {
          obj.set({ left: (first.left || 0) + spacing * index });
          obj.setCoords();
        }
      });
    } else {
      const totalSpace = (last.top || 0) - (first.top || 0);
      const spacing = totalSpace / (sortedObjects.length - 1);
      
      sortedObjects.forEach((obj, index) => {
        if (index > 0 && index < sortedObjects.length - 1) {
          obj.set({ top: (first.top || 0) + spacing * index });
          obj.setCoords();
        }
      });
    }

    this.canvas.renderAll();
  }

  // Path operations
  combinePaths(): void {
    const activeObjects = this.canvas.getActiveObjects();
    const pathObjects = activeObjects.filter(obj => obj.type === 'path' && (obj as any).vectorId);
    
    if (pathObjects.length < 2) return;

    // This would combine multiple paths into a single path object
    // Implementation would depend on specific path combination logic
    console.log('Combining paths:', pathObjects.map(obj => (obj as any).vectorId));
  }

  breakPath(): void {
    const activeObject = this.canvas.getActiveObject();
    if (!activeObject || activeObject.type !== 'path' || !(activeObject as any).vectorId) return;

    // Break path at selected nodes
    const vectorId = (activeObject as any).vectorId;
    console.log('Breaking path:', vectorId);
  }

  joinPaths(): void {
    const activeObjects = this.canvas.getActiveObjects();
    const pathObjects = activeObjects.filter(obj => obj.type === 'path' && (obj as any).vectorId);
    
    if (pathObjects.length < 2) return;

    // Join endpoints of paths
    console.log('Joining paths:', pathObjects.map(obj => (obj as any).vectorId));
  }

  // Utility methods
  private getSelectionBounds(objects: fabric.Object[]): { left: number; top: number; width: number; height: number } {
    let left = Infinity, top = Infinity, right = -Infinity, bottom = -Infinity;

    objects.forEach(obj => {
      const objLeft = obj.left || 0;
      const objTop = obj.top || 0;
      const objRight = objLeft + (obj.width || 0);
      const objBottom = objTop + (obj.height || 0);

      left = Math.min(left, objLeft);
      top = Math.min(top, objTop);
      right = Math.max(right, objRight);
      bottom = Math.max(bottom, objBottom);
    });

    return {
      left,
      top,
      width: right - left,
      height: bottom - top
    };
  }
}

// Keyboard shortcuts manager
export class VectorKeyboardManager {
  private shortcuts: Map<string, () => void> = new Map();
  private isEnabled: boolean = true;

  constructor() {
    this.setupDefaultShortcuts();
    this.bindEventListeners();
  }

  private setupDefaultShortcuts(): void {
    // Selection shortcuts
    this.shortcuts.set('ctrl+a', this.selectAll.bind(this));
    this.shortcuts.set('ctrl+d', this.selectNone.bind(this));
    this.shortcuts.set('ctrl+shift+i', this.selectInvert.bind(this));

    // Tool shortcuts
    this.shortcuts.set('v', () => this.emit('tool:change', 'select'));
    this.shortcuts.set('p', () => this.emit('tool:change', 'pen'));
    this.shortcuts.set('r', () => this.emit('tool:change', 'rectangle'));
    this.shortcuts.set('o', () => this.emit('tool:change', 'ellipse'));
    this.shortcuts.set('l', () => this.emit('tool:change', 'line'));

    // Edit shortcuts
    this.shortcuts.set('ctrl+z', this.undo.bind(this));
    this.shortcuts.set('ctrl+y', this.redo.bind(this));
    this.shortcuts.set('ctrl+c', this.copy.bind(this));
    this.shortcuts.set('ctrl+v', this.paste.bind(this));
    this.shortcuts.set('delete', this.deleteSelected.bind(this));
    this.shortcuts.set('backspace', this.deleteSelected.bind(this));

    // Transform shortcuts
    this.shortcuts.set('ctrl+g', this.group.bind(this));
    this.shortcuts.set('ctrl+shift+g', this.ungroup.bind(this));
    this.shortcuts.set('ctrl+]', this.bringForward.bind(this));
    this.shortcuts.set('ctrl+[', this.sendBackward.bind(this));
  }

  private bindEventListeners(): void {
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
  }

  private handleKeyDown(event: KeyboardEvent): void {
    if (!this.isEnabled) return;

    // Don't handle shortcuts when typing in input fields
    if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
      return;
    }

    const key = this.getKeyString(event);
    const handler = this.shortcuts.get(key);
    
    if (handler) {
      event.preventDefault();
      handler();
    }
  }

  private getKeyString(event: KeyboardEvent): string {
    const parts: string[] = [];
    
    if (event.ctrlKey || event.metaKey) parts.push('ctrl');
    if (event.shiftKey) parts.push('shift');
    if (event.altKey) parts.push('alt');
    
    const key = event.key.toLowerCase();
    parts.push(key);
    
    return parts.join('+');
  }

  addShortcut(key: string, handler: () => void): void {
    this.shortcuts.set(key, handler);
  }

  removeShortcut(key: string): void {
    this.shortcuts.delete(key);
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  // Shortcut handlers (these would emit events to be handled by the tool manager)
  private selectAll(): void { this.emit('selection:all'); }
  private selectNone(): void { this.emit('selection:none'); }
  private selectInvert(): void { this.emit('selection:invert'); }
  private undo(): void { this.emit('edit:undo'); }
  private redo(): void { this.emit('edit:redo'); }
  private copy(): void { this.emit('edit:copy'); }
  private paste(): void { this.emit('edit:paste'); }
  private deleteSelected(): void { this.emit('edit:delete'); }
  private group(): void { this.emit('transform:group'); }
  private ungroup(): void { this.emit('transform:ungroup'); }
  private bringForward(): void { this.emit('transform:bring-forward'); }
  private sendBackward(): void { this.emit('transform:send-backward'); }

  private emit(event: string, data?: any): void {
    // This would emit events to the tool manager
    console.log('Keyboard shortcut:', event, data);
  }

  dispose(): void {
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    this.shortcuts.clear();
  }
}

// Main Vector Tools Manager
export class VectorToolsManager extends EventEmitter {
  private vectorEngine: VectorGraphicsEngine;
  private shapeLibrary: ShapeLibrary;
  private canvas: fabric.Canvas;
  private operations: VectorToolOperations;
  private keyboardManager: VectorKeyboardManager;
  private toolConfigs: Map<VectorTool, VectorToolConfig> = new Map();
  private toolState: ToolState;
  private workflows: Map<string, ToolWorkflow> = new Map();
  private undoStack: any[] = [];
  private redoStack: any[] = [];
  private maxHistorySize: number = 50;

  constructor(
    vectorEngine: VectorGraphicsEngine,
    shapeLibrary: ShapeLibrary,
    canvas: fabric.Canvas
  ) {
    super();
    
    this.vectorEngine = vectorEngine;
    this.shapeLibrary = shapeLibrary;
    this.canvas = canvas;
    this.operations = new VectorToolOperations(vectorEngine, canvas);
    this.keyboardManager = new VectorKeyboardManager();

    this.toolState = {
      activeTool: 'select',
      toolOptions: {},
      isDrawing: false,
      selectionMode: 'single',
      snapSettings: {
        snapToGrid: true,
        snapToGuides: true,
        snapToObjects: true,
        snapTolerance: 5
      },
      editingOptions: {
        snapToGrid: true,
        snapToGuides: true,
        snapToObjects: true,
        snapTolerance: 5,
        showGrid: true,
        gridSize: 20,
        showGuides: true,
        showNodes: true,
        showHandles: true,
        nodeSize: 6,
        handleSize: 4
      }
    };

    this.initializeTools();
    this.setupEventHandlers();
    this.loadWorkflows();
  }

  private initializeTools(): void {
    // Selection tools
    this.toolConfigs.set('select', {
      id: 'select',
      name: 'Selection Tool',
      icon: 'cursor-default-outline',
      description: 'Select and transform objects',
      category: 'selection',
      shortcuts: ['v'],
      cursor: 'default',
      options: [
        {
          name: 'selectionMode',
          type: 'select',
          defaultValue: 'single',
          options: [
            { value: 'single', label: 'Single Selection' },
            { value: 'multiple', label: 'Multiple Selection' },
            { value: 'additive', label: 'Additive Selection' }
          ],
          description: 'Selection behavior mode',
          group: 'Selection'
        }
      ],
      enabled: true
    });

    // Drawing tools
    this.toolConfigs.set('pen', {
      id: 'pen',
      name: 'Pen Tool',
      icon: 'pen',
      description: 'Draw vector paths with bezier curves',
      category: 'drawing',
      shortcuts: ['p'],
      cursor: 'crosshair',
      options: [
        {
          name: 'strokeWidth',
          type: 'range',
          defaultValue: 1,
          min: 0.1,
          max: 20,
          step: 0.1,
          description: 'Stroke width',
          group: 'Appearance'
        },
        {
          name: 'strokeColor',
          type: 'color',
          defaultValue: '#000000',
          description: 'Stroke color',
          group: 'Appearance'
        },
        {
          name: 'autoClose',
          type: 'boolean',
          defaultValue: false,
          description: 'Automatically close paths',
          group: 'Behavior'
        }
      ],
      enabled: true
    });

    this.toolConfigs.set('pencil', {
      id: 'pencil',
      name: 'Pencil Tool',
      icon: 'pencil',
      description: 'Draw freehand paths',
      category: 'drawing',
      shortcuts: ['b'],
      cursor: 'crosshair',
      options: [
        {
          name: 'strokeWidth',
          type: 'range',
          defaultValue: 2,
          min: 1,
          max: 50,
          step: 1,
          description: 'Brush size',
          group: 'Appearance'
        },
        {
          name: 'strokeColor',
          type: 'color',
          defaultValue: '#000000',
          description: 'Brush color',
          group: 'Appearance'
        },
        {
          name: 'smoothing',
          type: 'range',
          defaultValue: 0.5,
          min: 0,
          max: 1,
          step: 0.1,
          description: 'Path smoothing',
          group: 'Behavior'
        }
      ],
      enabled: true
    });

    // Shape tools
    this.toolConfigs.set('rectangle', {
      id: 'rectangle',
      name: 'Rectangle Tool',
      icon: 'rectangle-outline',
      description: 'Create rectangle shapes',
      category: 'shape',
      shortcuts: ['r'],
      cursor: 'crosshair',
      options: [
        {
          name: 'cornerRadius',
          type: 'range',
          defaultValue: 0,
          min: 0,
          max: 50,
          step: 1,
          description: 'Corner radius',
          group: 'Shape'
        },
        {
          name: 'constrainProportions',
          type: 'boolean',
          defaultValue: false,
          description: 'Constrain proportions (hold Shift)',
          group: 'Behavior'
        }
      ],
      enabled: true
    });

    this.toolConfigs.set('ellipse', {
      id: 'ellipse',
      name: 'Ellipse Tool',
      icon: 'ellipse-outline',
      description: 'Create ellipse and circle shapes',
      category: 'shape',
      shortcuts: ['o'],
      cursor: 'crosshair',
      options: [
        {
          name: 'constrainProportions',
          type: 'boolean',
          defaultValue: false,
          description: 'Create perfect circles (hold Shift)',
          group: 'Behavior'
        }
      ],
      enabled: true
    });

    this.toolConfigs.set('polygon', {
      id: 'polygon',
      name: 'Polygon Tool',
      icon: 'hexagon-outline',
      description: 'Create polygon shapes',
      category: 'shape',
      shortcuts: ['alt+p'],
      cursor: 'crosshair',
      options: [
        {
          name: 'sides',
          type: 'range',
          defaultValue: 6,
          min: 3,
          max: 20,
          step: 1,
          description: 'Number of sides',
          group: 'Shape'
        }
      ],
      enabled: true
    });

    this.toolConfigs.set('line', {
      id: 'line',
      name: 'Line Tool',
      icon: 'minus',
      description: 'Create straight lines',
      category: 'drawing',
      shortcuts: ['l'],
      cursor: 'crosshair',
      options: [
        {
          name: 'constrainAngles',
          type: 'boolean',
          defaultValue: false,
          description: 'Constrain to 45° angles (hold Shift)',
          group: 'Behavior'
        }
      ],
      enabled: true
    });

    // Initialize tool options with defaults
    this.toolConfigs.forEach((config, tool) => {
      this.toolState.toolOptions[tool] = {};
      config.options.forEach(option => {
        this.toolState.toolOptions[tool][option.name] = option.defaultValue;
      });
    });
  }

  private setupEventHandlers(): void {
    // Vector engine events
    this.vectorEngine.on('tool:changed', (tool: VectorTool) => {
      this.toolState.activeTool = tool;
      this.emit('tool:changed', tool);
    });

    this.vectorEngine.on('selection:changed', (selection: VectorSelection) => {
      this.emit('selection:changed', selection);
    });

    this.vectorEngine.on('shape:created', (shape: any) => {
      this.saveState();
      this.emit('shape:created', shape);
    });

    this.vectorEngine.on('shape:updated', (shape: any) => {
      this.saveState();
      this.emit('shape:updated', shape);
    });

    this.vectorEngine.on('shape:deleted', (data: any) => {
      this.saveState();
      this.emit('shape:deleted', data);
    });

    // Keyboard shortcuts
    this.keyboardManager.on = (event: string, handler: any) => {
      if (event === 'selection:all') this.operations.selectAll();
      if (event === 'selection:none') this.operations.selectNone();
      if (event === 'selection:invert') this.operations.selectInvert();
      if (event === 'tool:change') this.setTool(handler);
      if (event === 'edit:undo') this.undo();
      if (event === 'edit:redo') this.redo();
      if (event === 'edit:copy') this.copySelection();
      if (event === 'edit:paste') this.pasteSelection();
      if (event === 'edit:delete') this.deleteSelection();
    };

    // Canvas events for tool-specific behavior
    this.canvas.on('mouse:down', this.handleCanvasMouseDown.bind(this));
    this.canvas.on('mouse:move', this.handleCanvasMouseMove.bind(this));
    this.canvas.on('mouse:up', this.handleCanvasMouseUp.bind(this));
  }

  private loadWorkflows(): void {
    // Logo creation workflow
    this.workflows.set('logo-creation', {
      id: 'logo-creation',
      name: 'Logo Creation',
      description: 'Step-by-step logo creation workflow',
      category: 'design',
      steps: [
        {
          tool: 'select',
          action: 'setup-canvas',
          description: 'Set up canvas and guides',
          autoAdvance: false
        },
        {
          tool: 'pen',
          action: 'create-outline',
          description: 'Create main logo outline',
          autoAdvance: false
        },
        {
          tool: 'rectangle',
          action: 'add-shapes',
          options: { cornerRadius: 5 },
          description: 'Add supporting shapes',
          autoAdvance: false
        },
        {
          tool: 'select',
          action: 'refine-design',
          description: 'Refine and align elements',
          autoAdvance: false
        }
      ]
    });

    // Icon design workflow
    this.workflows.set('icon-design', {
      id: 'icon-design',
      name: 'Icon Design',
      description: 'Create scalable icons',
      category: 'design',
      steps: [
        {
          tool: 'select',
          action: 'setup-grid',
          description: 'Set up icon grid (24x24 or 32x32)',
          autoAdvance: false
        },
        {
          tool: 'rectangle',
          action: 'base-shape',
          description: 'Create base shape',
          autoAdvance: false
        },
        {
          tool: 'pen',
          action: 'add-details',
          description: 'Add icon details and features',
          autoAdvance: false
        },
        {
          tool: 'select',
          action: 'optimize',
          description: 'Optimize for different sizes',
          autoAdvance: false
        }
      ]
    });
  }

  // Tool management
  setTool(tool: VectorTool): void {
    if (!this.toolConfigs.has(tool)) {
      console.warn(`Unknown tool: ${tool}`);
      return;
    }

    const config = this.toolConfigs.get(tool)!;
    if (!config.enabled) {
      console.warn(`Tool is disabled: ${tool}`);
      return;
    }

    this.vectorEngine.setTool(tool);
    this.canvas.defaultCursor = config.cursor;
    
    // Update canvas drawing mode
    this.canvas.isDrawingMode = tool === 'pencil' || tool === 'brush';
    
    this.emit('tool:activated', { tool, config });
  }

  getCurrentTool(): VectorTool {
    return this.toolState.activeTool;
  }

  getToolConfig(tool: VectorTool): VectorToolConfig | undefined {
    return this.toolConfigs.get(tool);
  }

  getAllTools(): VectorToolConfig[] {
    return Array.from(this.toolConfigs.values());
  }

  getToolsByCategory(category: VectorToolConfig['category']): VectorToolConfig[] {
    return Array.from(this.toolConfigs.values()).filter(config => config.category === category);
  }

  // Tool options management
  setToolOption(tool: VectorTool, optionName: string, value: any): void {
    if (!this.toolState.toolOptions[tool]) {
      this.toolState.toolOptions[tool] = {};
    }
    
    this.toolState.toolOptions[tool][optionName] = value;
    
    // Apply option to vector engine if it's the active tool
    if (tool === this.toolState.activeTool) {
      this.applyToolOptions(tool);
    }
    
    this.emit('tool:option:changed', { tool, optionName, value });
  }

  getToolOption(tool: VectorTool, optionName: string): any {
    return this.toolState.toolOptions[tool]?.[optionName];
  }

  getToolOptions(tool: VectorTool): Record<string, any> {
    return { ...this.toolState.toolOptions[tool] };
  }

  private applyToolOptions(tool: VectorTool): void {
    const options = this.toolState.toolOptions[tool];
    if (!options) return;

    // Apply tool-specific options to the vector engine
    this.vectorEngine.setEditingOptions({
      ...this.toolState.editingOptions,
      ...options
    });
  }

  // Shape creation shortcuts
  createShape(templateId: string, options: ShapeCreationOptions): void {
    const result = this.shapeLibrary.createShape(templateId, {
      ...options,
      style: {
        ...options.style,
        stroke: this.getToolOption(this.toolState.activeTool, 'strokeColor') || '#000000',
        strokeWidth: this.getToolOption(this.toolState.activeTool, 'strokeWidth') || 1
      }
    });

    if (result) {
      this.saveState();
      this.emit('shape:created:from:template', { templateId, shape: result.shape });
    }
  }

  // Operations delegation
  selectAll(): void {
    this.operations.selectAll();
  }

  selectNone(): void {
    this.operations.selectNone();
  }

  selectInvert(): void {
    this.operations.selectInvert();
  }

  selectByType(objectType: string): void {
    this.operations.selectByType(objectType);
  }

  alignObjects(alignment: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom'): void {
    this.operations.alignObjects(alignment);
    this.saveState();
  }

  distributeObjects(distribution: 'horizontal' | 'vertical'): void {
    this.operations.distributeObjects(distribution);
    this.saveState();
  }

  // History management
  private saveState(): void {
    const state = JSON.stringify(this.canvas.toJSON());
    this.undoStack.push(state);
    
    if (this.undoStack.length > this.maxHistorySize) {
      this.undoStack.shift();
    }
    
    // Clear redo stack when new action is performed
    this.redoStack = [];
    
    this.emit('history:updated', {
      canUndo: this.undoStack.length > 0,
      canRedo: this.redoStack.length > 0
    });
  }

  undo(): void {
    if (this.undoStack.length === 0) return;

    const currentState = JSON.stringify(this.canvas.toJSON());
    this.redoStack.push(currentState);

    const previousState = this.undoStack.pop()!;
    this.canvas.loadFromJSON(previousState, () => {
      this.canvas.renderAll();
      this.emit('history:undo');
      this.emit('history:updated', {
        canUndo: this.undoStack.length > 0,
        canRedo: this.redoStack.length > 0
      });
    });
  }

  redo(): void {
    if (this.redoStack.length === 0) return;

    const currentState = JSON.stringify(this.canvas.toJSON());
    this.undoStack.push(currentState);

    const nextState = this.redoStack.pop()!;
    this.canvas.loadFromJSON(nextState, () => {
      this.canvas.renderAll();
      this.emit('history:redo');
      this.emit('history:updated', {
        canUndo: this.undoStack.length > 0,
        canRedo: this.redoStack.length > 0
      });
    });
  }

  canUndo(): boolean {
    return this.undoStack.length > 0;
  }

  canRedo(): boolean {
    return this.redoStack.length > 0;
  }

  // Clipboard operations
  private clipboard: any = null;

  copySelection(): void {
    const activeObject = this.canvas.getActiveObject();
    if (!activeObject) return;

    activeObject.clone((cloned: fabric.Object) => {
      this.clipboard = cloned;
      this.emit('clipboard:copied');
    });
  }

  pasteSelection(): void {
    if (!this.clipboard) return;

    this.clipboard.clone((cloned: fabric.Object) => {
      cloned.set({
        left: (cloned.left || 0) + 20,
        top: (cloned.top || 0) + 20,
        evented: true
      });

      if (cloned.type === 'activeSelection') {
        // Handle pasting multiple objects
        (cloned as fabric.ActiveSelection).canvas = this.canvas;
        (cloned as fabric.ActiveSelection).forEachObject((obj: fabric.Object) => {
          this.canvas.add(obj);
        });
      } else {
        this.canvas.add(cloned);
      }

      this.canvas.setActiveObject(cloned);
      this.canvas.renderAll();
      this.saveState();
      this.emit('clipboard:pasted');
    });
  }

  deleteSelection(): void {
    const activeObjects = this.canvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    activeObjects.forEach(obj => {
      this.canvas.remove(obj);
      
      // Also remove from vector engine if it's a vector object
      const vectorId = (obj as any).vectorId;
      if (vectorId) {
        this.vectorEngine.deleteVectorShape(vectorId);
      }
    });

    this.canvas.discardActiveObject();
    this.canvas.renderAll();
    this.saveState();
    this.emit('selection:deleted');
  }

  // Canvas event handlers
  private handleCanvasMouseDown(e: fabric.IEvent): void {
    if (!e.pointer) return;

    this.toolState.isDrawing = true;
    
    // Tool-specific mouse down handling
    switch (this.toolState.activeTool) {
      case 'rectangle':
        this.handleRectangleMouseDown(e);
        break;
      case 'ellipse':
        this.handleEllipseMouseDown(e);
        break;
      case 'line':
        this.handleLineMouseDown(e);
        break;
    }
  }

  private handleCanvasMouseMove(e: fabric.IEvent): void {
    if (!this.toolState.isDrawing || !e.pointer) return;

    // Tool-specific mouse move handling
  }

  private handleCanvasMouseUp(e: fabric.IEvent): void {
    this.toolState.isDrawing = false;
    
    // Tool-specific mouse up handling
  }

  private handleRectangleMouseDown(e: fabric.IEvent): void {
    const pointer = e.pointer!;
    const options: ShapeCreationOptions = {
      x: pointer.x - 50,
      y: pointer.y - 30,
      width: 100,
      height: 60,
      parameters: {
        cornerRadius: this.getToolOption('rectangle', 'cornerRadius')
      }
    };
    
    this.createShape('rectangle', options);
  }

  private handleEllipseMouseDown(e: fabric.IEvent): void {
    const pointer = e.pointer!;
    const options: ShapeCreationOptions = {
      x: pointer.x - 50,
      y: pointer.y - 50,
      width: 100,
      height: 100
    };
    
    this.createShape('ellipse', options);
  }

  private handleLineMouseDown(e: fabric.IEvent): void {
    // Line tool would create a temporary line that follows the mouse
    // until the second click
  }

  // Workflow management
  getWorkflows(): ToolWorkflow[] {
    return Array.from(this.workflows.values());
  }

  getWorkflow(id: string): ToolWorkflow | undefined {
    return this.workflows.get(id);
  }

  startWorkflow(workflowId: string): void {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return;

    this.emit('workflow:started', workflow);
    
    // Set the first tool in the workflow
    if (workflow.steps.length > 0) {
      this.setTool(workflow.steps[0].tool);
    }
  }

  // State management
  getToolState(): ToolState {
    return { ...this.toolState };
  }

  setSnapSettings(settings: Partial<ToolState['snapSettings']>): void {
    Object.assign(this.toolState.snapSettings, settings);
    this.vectorEngine.setEditingOptions({
      ...this.toolState.editingOptions,
      snapToGrid: settings.snapToGrid ?? this.toolState.snapSettings.snapToGrid,
      snapToGuides: settings.snapToGuides ?? this.toolState.snapSettings.snapToGuides,
      snapToObjects: settings.snapToObjects ?? this.toolState.snapSettings.snapToObjects,
      snapTolerance: settings.snapTolerance ?? this.toolState.snapSettings.snapTolerance
    });
    
    this.emit('snap:settings:changed', this.toolState.snapSettings);
  }

  setEditingOptions(options: Partial<VectorEditingOptions>): void {
    Object.assign(this.toolState.editingOptions, options);
    this.vectorEngine.setEditingOptions(this.toolState.editingOptions);
    this.emit('editing:options:changed', this.toolState.editingOptions);
  }

  // Import/Export
  exportToolSettings(): string {
    return JSON.stringify({
      toolOptions: this.toolState.toolOptions,
      snapSettings: this.toolState.snapSettings,
      editingOptions: this.toolState.editingOptions
    }, null, 2);
  }

  importToolSettings(data: string): void {
    try {
      const settings = JSON.parse(data);
      
      if (settings.toolOptions) {
        this.toolState.toolOptions = settings.toolOptions;
      }
      
      if (settings.snapSettings) {
        this.setSnapSettings(settings.snapSettings);
      }
      
      if (settings.editingOptions) {
        this.setEditingOptions(settings.editingOptions);
      }
      
      this.emit('settings:imported');
    } catch (error) {
      console.error('Failed to import tool settings:', error);
    }
  }

  // Cleanup
  dispose(): void {
    this.keyboardManager.dispose();
    this.vectorEngine.dispose();
    this.toolConfigs.clear();
    this.workflows.clear();
    this.undoStack = [];
    this.redoStack = [];
    this.removeAllListeners();
  }
}