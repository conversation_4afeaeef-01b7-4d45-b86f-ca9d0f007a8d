import { fabric } from 'fabric';
import { EventEmitter } from 'events';

export interface Layer {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  opacity: number;
  blendMode: string;
  objects: fabric.Object[];
  zIndex: number;
}

export class AdvancedLayerManager extends EventEmitter {
  private canvas: fabric.Canvas;
  private layers: Map<string, Layer>;
  private activeLayerId: string | null = null;

  constructor(canvas: fabric.Canvas) {
    super();
    this.canvas = canvas;
    this.layers = new Map();
    this.createDefaultLayer();
  }

  private createDefaultLayer(): void {
    const defaultLayer: Layer = {
      id: 'default',
      name: 'Background',
      visible: true,
      locked: false,
      opacity: 1,
      blendMode: 'normal',
      objects: [],
      zIndex: 0
    };
    
    this.layers.set('default', defaultLayer);
    this.activeLayerId = 'default';
  }

  public createLayer(name: string): string {
    const id = `layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const layer: Layer = {
      id,
      name,
      visible: true,
      locked: false,
      opacity: 1,
      blendMode: 'normal',
      objects: [],
      zIndex: this.layers.size
    };
    
    this.layers.set(id, layer);
    this.emit('layer:created', layer);
    return id;
  }

  public deleteLayer(layerId: string): void {
    if (layerId === 'default') return; // Cannot delete default layer
    
    const layer = this.layers.get(layerId);
    if (layer) {
      // Remove all objects from canvas
      layer.objects.forEach(obj => this.canvas.remove(obj));
      this.layers.delete(layerId);
      
      if (this.activeLayerId === layerId) {
        this.activeLayerId = 'default';
      }
      
      this.emit('layer:deleted', layerId);
      this.canvas.renderAll();
    }
  }

  public setActiveLayer(layerId: string): void {
    if (this.layers.has(layerId)) {
      this.activeLayerId = layerId;
      this.emit('layer:activated', layerId);
    }
  }

  public addObjectToLayer(object: fabric.Object, layerId?: string): void {
    const targetLayerId = layerId || this.activeLayerId || 'default';
    const layer = this.layers.get(targetLayerId);
    
    if (layer) {
      layer.objects.push(object);
      object.set('layerId', targetLayerId);
      this.updateLayerZIndex(layer);
    }
  }

  public setLayerVisibility(layerId: string, visible: boolean): void {
    const layer = this.layers.get(layerId);
    if (layer) {
      layer.visible = visible;
      layer.objects.forEach(obj => {
        obj.set('visible', visible);
      });
      this.canvas.renderAll();
      this.emit('layer:visibility:changed', layerId, visible);
    }
  }

  public setLayerOpacity(layerId: string, opacity: number): void {
    const layer = this.layers.get(layerId);
    if (layer) {
      layer.opacity = Math.max(0, Math.min(1, opacity));
      layer.objects.forEach(obj => {
        obj.set('opacity', layer.opacity);
      });
      this.canvas.renderAll();
      this.emit('layer:opacity:changed', layerId, opacity);
    }
  }

  public separateVectorRaster(): void {
    // Prepare for PIXI.js integration by separating vector and raster objects
    const vectorLayer = this.createLayer('Vector Objects');
    const rasterLayer = this.createLayer('Raster Objects');
    
    this.canvas.getObjects().forEach(obj => {
      if (obj.type === 'image') {
        this.addObjectToLayer(obj, rasterLayer);
      } else {
        this.addObjectToLayer(obj, vectorLayer);
      }
    });
  }

  private updateLayerZIndex(layer: Layer): void {
    layer.objects.forEach(obj => {
      this.canvas.bringToFront(obj);
    });
  }

  public getLayers(): Layer[] {
    return Array.from(this.layers.values()).sort((a, b) => a.zIndex - b.zIndex);
  }

  public getActiveLayer(): Layer | null {
    return this.activeLayerId ? this.layers.get(this.activeLayerId) || null : null;
  }
}
