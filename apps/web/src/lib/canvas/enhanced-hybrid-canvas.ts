import { fabric } from 'fabric';
import { EventEmitter } from 'events';
import { AdvancedLayerManager } from './advanced-layer-manager';
import { SnapManager } from './snap-manager';
import { HistoryManager } from './history-manager';

export interface FabricCanvasConfig {
  width: number;
  height: number;
  backgroundColor?: string;
  selection?: boolean;
  preserveObjectStacking?: boolean;
  renderOnAddRemove?: boolean;
  skipTargetFind?: boolean;
  perPixelTargetFind?: boolean;
  targetFindTolerance?: number;
}

export interface CanvasTool {
  setCanvas(canvas: fabric.Canvas): void;
  setObjectRegistry(registry: Map<string, fabric.Object>): void;
  setLayerManager(manager: AdvancedLayerManager): void;
  on(event: string, handler: Function): void;
}

export class EnhancedHybridCanvasManager extends EventEmitter {
  private fabricCanvas: fabric.Canvas;
  private canvasElement: HTMLCanvasElement;
  private objectRegistry: Map<string, fabric.Object>;
  private layerManager: AdvancedLayerManager;
  private snapManager: SnapManager;
  private historyManager: HistoryManager;
  private isInitialized = false;

  constructor(canvasElement: HTMLCanvasElement, options: FabricCanvasConfig) {
    super();
    this.canvasElement = canvasElement;
    this.objectRegistry = new Map();
    
    // Initialize Fabric.js with performance optimizations
    this.fabricCanvas = new fabric.Canvas(canvasElement, {
      ...options,
      renderOnAddRemove: false,
      skipTargetFind: false,
      perPixelTargetFind: true,
      targetFindTolerance: 4
    });
    
    this.layerManager = new AdvancedLayerManager(this.fabricCanvas);
    this.snapManager = new SnapManager(this.fabricCanvas);
    this.historyManager = new HistoryManager(this.fabricCanvas);
    
    this.initializeEventHandlers();
    this.setupPerformanceOptimizations();
    this.isInitialized = true;
  }

  private initializeEventHandlers(): void {
    // Object lifecycle events
    this.fabricCanvas.on('object:added', (e) => {
      const obj = e.target as fabric.Object;
      this.objectRegistry.set(obj.id || this.generateObjectId(), obj);
      this.emit('object:added', obj);
    });

    this.fabricCanvas.on('object:removed', (e) => {
      const obj = e.target as fabric.Object;
      this.objectRegistry.delete(obj.id || '');
      this.emit('object:removed', obj);
    });

    // Selection events
    this.fabricCanvas.on('selection:created', (e) => {
      this.emit('selection:created', e.selected);
    });

    this.fabricCanvas.on('selection:updated', (e) => {
      this.emit('selection:updated', e.selected);
    });
  }

  private setupPerformanceOptimizations(): void {
    // Enable hardware acceleration
    this.fabricCanvas.contextType = '2d';
    this.fabricCanvas.interactive = true;
    
    // Optimize rendering for complex scenes
    this.fabricCanvas.renderOnAddRemove = false;
    this.fabricCanvas.skipTargetFind = false;
  }

  public addObject(object: fabric.Object, options: { renderImmediately?: boolean } = {}): void {
    const id = this.generateObjectId();
    object.set('id', id);
    
    this.fabricCanvas.add(object);
    this.objectRegistry.set(id, object);
    
    if (options.renderImmediately) {
      this.fabricCanvas.renderAll();
    }
    
    this.historyManager.saveState();
  }

  public removeObject(objectId: string): void {
    const object = this.objectRegistry.get(objectId);
    if (object) {
      this.fabricCanvas.remove(object);
      this.objectRegistry.delete(objectId);
      this.fabricCanvas.renderAll();
      this.historyManager.saveState();
    }
  }

  public updateObject(object: fabric.Object): void {
    this.fabricCanvas.renderAll();
    this.historyManager.saveState();
    this.emit('object:modified', object);
  }

  public integrateWithTool(tool: CanvasTool): void {
    tool.setCanvas(this.fabricCanvas);
    tool.setObjectRegistry(this.objectRegistry);
    tool.setLayerManager(this.layerManager);
    
    tool.on('object:created', (object: fabric.Object) => {
      this.addObject(object, { renderImmediately: true });
    });
    
    tool.on('object:modified', (object: fabric.Object) => {
      this.updateObject(object);
    });
  }

  public enableHybridRendering(): void {
    this.fabricCanvas.contextType = '2d';
    this.fabricCanvas.interactive = true;
    this.layerManager.separateVectorRaster();
  }

  public getCanvas(): fabric.Canvas {
    return this.fabricCanvas;
  }

  public getObjectRegistry(): Map<string, fabric.Object> {
    return this.objectRegistry;
  }

  public getLayerManager(): AdvancedLayerManager {
    return this.layerManager;
  }

  private generateObjectId(): string {
    return `obj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public dispose(): void {
    this.fabricCanvas.dispose();
    this.objectRegistry.clear();
    this.removeAllListeners();
  }
}
