import { fabric } from 'fabric';
import { EventEmitter } from 'events';

// Typography types and interfaces
export interface FontMetrics {
  ascent: number;
  descent: number;
  lineHeight: number;
  capHeight: number;
  xHeight: number;
  baseline: number;
  unitsPerEm: number;
}

export interface TypographySettings {
  // Font properties
  fontFamily: string;
  fontSize: number;
  fontWeight: number | string;
  fontStyle: 'normal' | 'italic' | 'oblique';
  fontVariant: 'normal' | 'small-caps';
  fontStretch: 'normal' | 'condensed' | 'expanded' | string;
  
  // Text properties
  textAlign: 'left' | 'center' | 'right' | 'justify';
  textDecoration: 'none' | 'underline' | 'overline' | 'line-through';
  textTransform: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  textShadow: string;
  
  // Layout properties
  letterSpacing: number;
  wordSpacing: number;
  lineHeight: number;
  paragraphSpacing: number;
  indent: number;
  
  // Color properties
  fill: string;
  stroke: string;
  strokeWidth: number;
  
  // Advanced properties
  kerning: boolean;
  ligatures: boolean;
  hyphenation: boolean;
  direction: 'ltr' | 'rtl';
  writingMode: 'horizontal-tb' | 'vertical-rl' | 'vertical-lr';
}

export interface TextRange {
  start: number;
  end: number;
  settings: Partial<TypographySettings>;
}

export interface TextPath {
  id: string;
  path: string;
  offset: number;
  side: 'left' | 'right';
  textAlign: 'left' | 'center' | 'right';
}

export interface TextBox {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  padding: { top: number; right: number; bottom: number; left: number };
  overflow: 'visible' | 'hidden' | 'scroll' | 'auto';
  columns: number;
  columnGap: number;
}

export interface TypographyObject {
  id: string;
  type: 'text' | 'textPath' | 'textBox';
  content: string;
  settings: TypographySettings;
  ranges: TextRange[];
  path?: TextPath;
  box?: TextBox;
  fabricObject?: fabric.Text | fabric.Textbox;
  metrics?: FontMetrics;
  boundingBox?: fabric.Rect;
  baseline?: number;
  ascent?: number;
  descent?: number;
}

export interface TypographyEngineConfig {
  enableKerning: boolean;
  enableLigatures: boolean;
  enableHyphenation: boolean;
  enableBaseline: boolean;
  enableMetrics: boolean;
  enableRealTimePreview: boolean;
  fontLoadingStrategy: 'eager' | 'lazy' | 'preload';
  maxTextObjects: number;
  measurementAccuracy: 'low' | 'medium' | 'high';
  enablePerformanceOptimization: boolean;
}

export interface TypographyMeasurement {
  width: number;
  height: number;
  actualBoundingBoxLeft: number;
  actualBoundingBoxRight: number;
  actualBoundingBoxAscent: number;
  actualBoundingBoxDescent: number;
  fontBoundingBoxAscent: number;
  fontBoundingBoxDescent: number;
  emHeightAscent: number;
  emHeightDescent: number;
  hangingBaseline: number;
  alphabeticBaseline: number;
  ideographicBaseline: number;
}

export interface TypographyPerformanceMetrics {
  textRenderTime: number;
  measurementTime: number;
  fontLoadTime: number;
  objectCount: number;
  averageComplexity: number;
  memoryUsage: number;
  cacheHitRate: number;
}

// Typography calculation utilities
export class TypographyCalculator {
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;
  private fontCache: Map<string, FontMetrics> = new Map();
  private measurementCache: Map<string, TypographyMeasurement> = new Map();

  constructor() {
    this.canvas = document.createElement('canvas');
    this.canvas.width = 1000;
    this.canvas.height = 1000;
    this.context = this.canvas.getContext('2d')!;
  }

  // Font metrics calculation
  calculateFontMetrics(fontFamily: string, fontSize: number): FontMetrics {
    const cacheKey = `${fontFamily}_${fontSize}`;
    
    if (this.fontCache.has(cacheKey)) {
      return this.fontCache.get(cacheKey)!;
    }

    this.context.font = `${fontSize}px ${fontFamily}`;
    
    // Measure baseline metrics using standard characters
    const testString = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const metrics = this.context.measureText(testString);
    
    // Calculate derived metrics
    const ascent = metrics.actualBoundingBoxAscent || fontSize * 0.75;
    const descent = metrics.actualBoundingBoxDescent || fontSize * 0.25;
    const lineHeight = ascent + descent;
    
    // Estimate cap height and x-height
    const capHeight = this.estimateCapHeight(fontFamily, fontSize);
    const xHeight = this.estimateXHeight(fontFamily, fontSize);
    
    const fontMetrics: FontMetrics = {
      ascent,
      descent,
      lineHeight,
      capHeight,
      xHeight,
      baseline: 0,
      unitsPerEm: fontSize
    };

    this.fontCache.set(cacheKey, fontMetrics);
    return fontMetrics;
  }

  private estimateCapHeight(fontFamily: string, fontSize: number): number {
    this.context.font = `${fontSize}px ${fontFamily}`;
    const metrics = this.context.measureText('H');
    return metrics.actualBoundingBoxAscent || fontSize * 0.7;
  }

  private estimateXHeight(fontFamily: string, fontSize: number): number {
    this.context.font = `${fontSize}px ${fontFamily}`;
    const metrics = this.context.measureText('x');
    return metrics.actualBoundingBoxAscent || fontSize * 0.5;
  }

  // Precise text measurement
  measureText(text: string, settings: TypographySettings): TypographyMeasurement {
    const cacheKey = `${text}_${JSON.stringify(settings)}`;
    
    if (this.measurementCache.has(cacheKey)) {
      return this.measurementCache.get(cacheKey)!;
    }

    // Apply font settings
    this.context.font = `${settings.fontStyle} ${settings.fontVariant} ${settings.fontWeight} ${settings.fontSize}px ${settings.fontFamily}`;
    this.context.letterSpacing = `${settings.letterSpacing}px`;
    this.context.textAlign = settings.textAlign;

    // Measure text
    const metrics = this.context.measureText(text);
    
    const measurement: TypographyMeasurement = {
      width: metrics.width,
      height: metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent,
      actualBoundingBoxLeft: metrics.actualBoundingBoxLeft || 0,
      actualBoundingBoxRight: metrics.actualBoundingBoxRight || metrics.width,
      actualBoundingBoxAscent: metrics.actualBoundingBoxAscent || settings.fontSize * 0.75,
      actualBoundingBoxDescent: metrics.actualBoundingBoxDescent || settings.fontSize * 0.25,
      fontBoundingBoxAscent: metrics.fontBoundingBoxAscent || settings.fontSize * 0.8,
      fontBoundingBoxDescent: metrics.fontBoundingBoxDescent || settings.fontSize * 0.2,
      emHeightAscent: metrics.emHeightAscent || settings.fontSize * 0.75,
      emHeightDescent: metrics.emHeightDescent || settings.fontSize * 0.25,
      hangingBaseline: metrics.hangingBaseline || settings.fontSize * 0.8,
      alphabeticBaseline: metrics.alphabeticBaseline || 0,
      ideographicBaseline: metrics.ideographicBaseline || -settings.fontSize * 0.25
    };

    this.measurementCache.set(cacheKey, measurement);
    return measurement;
  }

  // Letter spacing calculation
  calculateLetterSpacing(text: string, settings: TypographySettings): number[] {
    const chars = text.split('');
    const spacings: number[] = [];
    
    for (let i = 0; i < chars.length - 1; i++) {
      const currentChar = chars[i];
      const nextChar = chars[i + 1];
      
      let spacing = settings.letterSpacing;
      
      // Apply kerning if enabled
      if (settings.kerning) {
        spacing += this.calculateKerning(currentChar, nextChar, settings);
      }
      
      spacings.push(spacing);
    }
    
    return spacings;
  }

  private calculateKerning(char1: string, char2: string, settings: TypographySettings): number {
    // Simplified kerning calculation
    // In a real implementation, this would use font-specific kerning tables
    const kerningPairs: Record<string, number> = {
      'AV': -2,
      'AW': -2,
      'AY': -2,
      'AO': -1,
      'FA': -1,
      'LY': -2,
      'LV': -2,
      'LW': -2,
      'PA': -2,
      'RA': -1,
      'RV': -1,
      'RW': -1,
      'RY': -1,
      'TA': -2,
      'TO': -1,
      'TY': -1,
      'VA': -2,
      'VO': -1,
      'VY': -1,
      'WA': -2,
      'WO': -1,
      'YA': -2,
      'YO': -1,
    };

    const pair = char1 + char2;
    return kerningPairs[pair] || 0;
  }

  // Line breaking and wrapping
  calculateLineBreaks(text: string, maxWidth: number, settings: TypographySettings): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const measurement = this.measureText(testLine, settings);
      
      if (measurement.width <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // Word is too long, break it
          const brokenWord = this.breakLongWord(word, maxWidth, settings);
          lines.push(...brokenWord);
          currentLine = '';
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  private breakLongWord(word: string, maxWidth: number, settings: TypographySettings): string[] {
    const result: string[] = [];
    let currentPart = '';

    for (const char of word) {
      const testPart = currentPart + char;
      const measurement = this.measureText(testPart, settings);
      
      if (measurement.width <= maxWidth) {
        currentPart = testPart;
      } else {
        if (currentPart) {
          result.push(currentPart);
          currentPart = char;
        } else {
          // Single character is too wide
          result.push(char);
        }
      }
    }

    if (currentPart) {
      result.push(currentPart);
    }

    return result;
  }

  // Hyphenation helper
  calculateHyphenation(text: string, settings: TypographySettings): string[] {
    // Simplified hyphenation - in practice, use a proper hyphenation library
    const hyphenationPatterns = [
      { pattern: /ing$/, replacement: 'ing' },
      { pattern: /tion$/, replacement: 'tion' },
      { pattern: /ment$/, replacement: 'ment' },
      { pattern: /able$/, replacement: 'able' },
    ];

    const words = text.split(' ');
    const result: string[] = [];

    for (const word of words) {
      if (word.length > 6) {
        let hyphenated = false;
        for (const { pattern, replacement } of hyphenationPatterns) {
          if (pattern.test(word)) {
            const parts = word.split(pattern);
            if (parts.length > 1) {
              result.push(parts[0] + '-');
              result.push(replacement);
              hyphenated = true;
              break;
            }
          }
        }
        if (!hyphenated) {
          result.push(word);
        }
      } else {
        result.push(word);
      }
    }

    return result;
  }

  // Clear caches
  clearCaches(): void {
    this.fontCache.clear();
    this.measurementCache.clear();
  }

  // Get cache statistics
  getCacheStats(): { fontCacheSize: number; measurementCacheSize: number } {
    return {
      fontCacheSize: this.fontCache.size,
      measurementCacheSize: this.measurementCache.size
    };
  }
}

// Main Typography Engine
export class TypographyEngine extends EventEmitter {
  private canvas: fabric.Canvas;
  private calculator: TypographyCalculator;
  private config: TypographyEngineConfig;
  
  // Typography objects management
  private textObjects: Map<string, TypographyObject> = new Map();
  private styleCache: Map<string, fabric.TextStyle> = new Map();
  private renderQueue: string[] = [];
  
  // Performance tracking
  private performanceMetrics: TypographyPerformanceMetrics = {
    textRenderTime: 0,
    measurementTime: 0,
    fontLoadTime: 0,
    objectCount: 0,
    averageComplexity: 0,
    memoryUsage: 0,
    cacheHitRate: 0
  };

  constructor(canvas: fabric.Canvas, config: Partial<TypographyEngineConfig> = {}) {
    super();
    
    this.canvas = canvas;
    this.calculator = new TypographyCalculator();
    this.config = {
      enableKerning: true,
      enableLigatures: true,
      enableHyphenation: false,
      enableBaseline: true,
      enableMetrics: true,
      enableRealTimePreview: true,
      fontLoadingStrategy: 'lazy',
      maxTextObjects: 100,
      measurementAccuracy: 'medium',
      enablePerformanceOptimization: true,
      ...config
    };

    this.initializeEngine();
  }

  private initializeEngine(): void {
    // Set up canvas text handling
    this.setupCanvasTextHandling();
    
    // Initialize font loading
    this.initializeFontLoading();
    
    // Set up performance monitoring
    if (this.config.enablePerformanceOptimization) {
      this.startPerformanceMonitoring();
    }
    
    this.emit('typography:initialized');
  }

  private setupCanvasTextHandling(): void {
    // Override fabric text rendering for enhanced typography
    const originalRenderText = fabric.Text.prototype._renderText;
    
    fabric.Text.prototype._renderText = function(ctx: CanvasRenderingContext2D) {
      // Enhanced text rendering with typography features
      const textObject = this as any;
      
      // Apply advanced typography settings if available
      if (textObject.typographySettings) {
        const settings = textObject.typographySettings;
        
        // Apply letter spacing
        if (settings.letterSpacing) {
          ctx.letterSpacing = `${settings.letterSpacing}px`;
        }
        
        // Apply word spacing
        if (settings.wordSpacing) {
          ctx.wordSpacing = `${settings.wordSpacing}px`;
        }
        
        // Apply text decoration
        if (settings.textDecoration && settings.textDecoration !== 'none') {
          textObject.textDecoration = settings.textDecoration;
        }
        
        // Apply text transform
        if (settings.textTransform && settings.textTransform !== 'none') {
          textObject.text = this.applyTextTransform(textObject.text, settings.textTransform);
        }
      }
      
      // Call original rendering
      originalRenderText.call(this, ctx);
    };
  }

  private applyTextTransform(text: string, transform: string): string {
    switch (transform) {
      case 'uppercase':
        return text.toUpperCase();
      case 'lowercase':
        return text.toLowerCase();
      case 'capitalize':
        return text.replace(/\b\w/g, l => l.toUpperCase());
      default:
        return text;
    }
  }

  private initializeFontLoading(): void {
    // Font loading strategy implementation
    document.addEventListener('DOMContentLoaded', () => {
      if (this.config.fontLoadingStrategy === 'preload') {
        this.preloadCommonFonts();
      }
    });
  }

  private preloadCommonFonts(): void {
    const commonFonts = [
      'Arial', 'Helvetica', 'Times New Roman', 'Georgia', 
      'Verdana', 'Trebuchet MS', 'Impact', 'Comic Sans MS'
    ];

    commonFonts.forEach(font => {
      const testElement = document.createElement('span');
      testElement.style.fontFamily = font;
      testElement.style.fontSize = '12px';
      testElement.style.visibility = 'hidden';
      testElement.style.position = 'absolute';
      testElement.textContent = 'ABCabc';
      document.body.appendChild(testElement);
      
      // Force font loading
      testElement.offsetWidth;
      document.body.removeChild(testElement);
    });
  }

  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.updatePerformanceMetrics();
      this.optimizePerformance();
    }, 5000);
  }

  private updatePerformanceMetrics(): void {
    this.performanceMetrics.objectCount = this.textObjects.size;
    this.performanceMetrics.memoryUsage = this.estimateMemoryUsage();
    this.performanceMetrics.cacheHitRate = this.calculateCacheHitRate();
    this.performanceMetrics.averageComplexity = this.calculateAverageComplexity();
    
    this.emit('typography:performance:updated', this.performanceMetrics);
  }

  private estimateMemoryUsage(): number {
    let totalMemory = 0;
    
    // Calculate text objects memory
    for (const [, textObj] of this.textObjects) {
      totalMemory += textObj.content.length * 2; // Character data
      totalMemory += JSON.stringify(textObj.settings).length; // Settings
      totalMemory += textObj.ranges.length * 100; // Ranges
    }
    
    // Add cache memory
    totalMemory += this.styleCache.size * 200; // Style cache
    const calcStats = this.calculator.getCacheStats();
    totalMemory += calcStats.fontCacheSize * 300;
    totalMemory += calcStats.measurementCacheSize * 150;
    
    return totalMemory;
  }

  private calculateCacheHitRate(): number {
    // Simplified cache hit rate calculation
    return Math.random() * 0.3 + 0.7; // 70-100% simulated
  }

  private calculateAverageComplexity(): number {
    if (this.textObjects.size === 0) return 0;
    
    let totalComplexity = 0;
    for (const [, textObj] of this.textObjects) {
      let complexity = 1;
      complexity += textObj.ranges.length * 0.5;
      complexity += textObj.content.length * 0.01;
      complexity += textObj.path ? 2 : 0;
      complexity += textObj.box ? 1 : 0;
      totalComplexity += complexity;
    }
    
    return totalComplexity / this.textObjects.size;
  }

  private optimizePerformance(): void {
    // Clear old cache entries
    if (this.styleCache.size > 100) {
      const entries = Array.from(this.styleCache.entries());
      const toRemove = entries.slice(0, 20);
      toRemove.forEach(([key]) => this.styleCache.delete(key));
    }
    
    // Optimize render queue
    if (this.renderQueue.length > 10) {
      this.renderQueue = this.renderQueue.slice(-5);
    }
    
    // Clear calculator caches if too large
    const calcStats = this.calculator.getCacheStats();
    if (calcStats.fontCacheSize > 50 || calcStats.measurementCacheSize > 200) {
      this.calculator.clearCaches();
    }
  }

  // Public API methods

  // Create text object
  createTextObject(content: string, settings: Partial<TypographySettings>, options: any = {}): string {
    const id = `text_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const defaultSettings: TypographySettings = {
      fontFamily: 'Arial',
      fontSize: 16,
      fontWeight: 'normal',
      fontStyle: 'normal',
      fontVariant: 'normal',
      fontStretch: 'normal',
      textAlign: 'left',
      textDecoration: 'none',
      textTransform: 'none',
      textShadow: 'none',
      letterSpacing: 0,
      wordSpacing: 0,
      lineHeight: 1.2,
      paragraphSpacing: 0,
      indent: 0,
      fill: '#000000',
      stroke: 'transparent',
      strokeWidth: 0,
      kerning: this.config.enableKerning,
      ligatures: this.config.enableLigatures,
      hyphenation: this.config.enableHyphenation,
      direction: 'ltr',
      writingMode: 'horizontal-tb'
    };

    const finalSettings = { ...defaultSettings, ...settings };
    
    // Create Fabric.js text object
    const fabricObject = new fabric.Text(content, {
      fontFamily: finalSettings.fontFamily,
      fontSize: finalSettings.fontSize,
      fontWeight: finalSettings.fontWeight,
      fontStyle: finalSettings.fontStyle,
      textAlign: finalSettings.textAlign,
      fill: finalSettings.fill,
      stroke: finalSettings.stroke,
      strokeWidth: finalSettings.strokeWidth,
      left: options.x || 0,
      top: options.y || 0,
      ...options
    });

    // Add typography enhancements
    (fabricObject as any).typographySettings = finalSettings;
    
    // Calculate metrics
    const metrics = this.calculator.calculateFontMetrics(
      finalSettings.fontFamily, 
      finalSettings.fontSize
    );

    const textObject: TypographyObject = {
      id,
      type: 'text',
      content,
      settings: finalSettings,
      ranges: [],
      fabricObject,
      metrics
    };

    this.textObjects.set(id, textObject);
    
    // Add to canvas
    this.canvas.add(fabricObject);
    
    this.emit('typography:object:created', { id, textObject });
    return id;
  }

  // Update text object
  updateTextObject(id: string, updates: Partial<TypographyObject>): boolean {
    const textObject = this.textObjects.get(id);
    if (!textObject) return false;

    const startTime = performance.now();

    // Update content
    if (updates.content !== undefined) {
      textObject.content = updates.content;
      if (textObject.fabricObject) {
        (textObject.fabricObject as fabric.Text).set('text', updates.content);
      }
    }

    // Update settings
    if (updates.settings) {
      Object.assign(textObject.settings, updates.settings);
      
      if (textObject.fabricObject) {
        const fabricObj = textObject.fabricObject as any;
        fabricObj.set({
          fontFamily: textObject.settings.fontFamily,
          fontSize: textObject.settings.fontSize,
          fontWeight: textObject.settings.fontWeight,
          fontStyle: textObject.settings.fontStyle,
          textAlign: textObject.settings.textAlign,
          fill: textObject.settings.fill,
          stroke: textObject.settings.stroke,
          strokeWidth: textObject.settings.strokeWidth
        });
        
        fabricObj.typographySettings = textObject.settings;
      }
      
      // Recalculate metrics
      textObject.metrics = this.calculator.calculateFontMetrics(
        textObject.settings.fontFamily,
        textObject.settings.fontSize
      );
    }

    // Update ranges
    if (updates.ranges) {
      textObject.ranges = updates.ranges;
    }

    // Update path or box
    if (updates.path) {
      textObject.path = updates.path;
      textObject.type = 'textPath';
    }

    if (updates.box) {
      textObject.box = updates.box;
      textObject.type = 'textBox';
    }

    // Re-render if needed
    if (this.config.enableRealTimePreview) {
      this.renderTextObject(id);
    }

    const endTime = performance.now();
    this.performanceMetrics.textRenderTime += endTime - startTime;

    this.emit('typography:object:updated', { id, textObject, updates });
    return true;
  }

  // Delete text object
  deleteTextObject(id: string): boolean {
    const textObject = this.textObjects.get(id);
    if (!textObject) return false;

    // Remove from canvas
    if (textObject.fabricObject) {
      this.canvas.remove(textObject.fabricObject);
    }

    // Remove from internal storage
    this.textObjects.delete(id);
    
    // Remove from render queue
    const queueIndex = this.renderQueue.indexOf(id);
    if (queueIndex !== -1) {
      this.renderQueue.splice(queueIndex, 1);
    }

    this.emit('typography:object:deleted', { id });
    return true;
  }

  // Get text object
  getTextObject(id: string): TypographyObject | undefined {
    return this.textObjects.get(id);
  }

  // Get all text objects
  getAllTextObjects(): TypographyObject[] {
    return Array.from(this.textObjects.values());
  }

  // Apply text style to range
  applyStyleToRange(id: string, range: TextRange): boolean {
    const textObject = this.textObjects.get(id);
    if (!textObject) return false;

    // Remove overlapping ranges
    textObject.ranges = textObject.ranges.filter(r => 
      !(r.start <= range.end && r.end >= range.start)
    );

    // Add new range
    textObject.ranges.push(range);
    textObject.ranges.sort((a, b) => a.start - b.start);

    // Update fabric object with rich text styling
    if (textObject.fabricObject && textObject.fabricObject instanceof fabric.Text) {
      this.applyRichTextStyling(textObject);
    }

    this.emit('typography:style:applied', { id, range });
    return true;
  }

  private applyRichTextStyling(textObject: TypographyObject): void {
    const fabricObj = textObject.fabricObject as fabric.Text;
    
    // Clear existing styles
    fabricObj.setSelectionStyles({}, 0, textObject.content.length);
    
    // Apply range styles
    for (const range of textObject.ranges) {
      const styles: any = {};
      
      if (range.settings.fontFamily) styles.fontFamily = range.settings.fontFamily;
      if (range.settings.fontSize) styles.fontSize = range.settings.fontSize;
      if (range.settings.fontWeight) styles.fontWeight = range.settings.fontWeight;
      if (range.settings.fontStyle) styles.fontStyle = range.settings.fontStyle;
      if (range.settings.fill) styles.fill = range.settings.fill;
      if (range.settings.stroke) styles.stroke = range.settings.stroke;
      if (range.settings.strokeWidth) styles.strokeWidth = range.settings.strokeWidth;
      if (range.settings.textDecoration) styles.textDecoration = range.settings.textDecoration;
      
      fabricObj.setSelectionStyles(styles, range.start, range.end);
    }
  }

  // Text measurement and layout
  measureText(id: string): TypographyMeasurement | null {
    const textObject = this.textObjects.get(id);
    if (!textObject) return null;

    const startTime = performance.now();
    const measurement = this.calculator.measureText(textObject.content, textObject.settings);
    const endTime = performance.now();
    
    this.performanceMetrics.measurementTime += endTime - startTime;
    
    return measurement;
  }

  // Line breaking
  wrapText(id: string, maxWidth: number): string[] {
    const textObject = this.textObjects.get(id);
    if (!textObject) return [];

    return this.calculator.calculateLineBreaks(textObject.content, maxWidth, textObject.settings);
  }

  // Render text object
  renderTextObject(id: string): boolean {
    const textObject = this.textObjects.get(id);
    if (!textObject) return false;

    const startTime = performance.now();
    
    // Add to render queue
    if (!this.renderQueue.includes(id)) {
      this.renderQueue.push(id);
    }

    // Render based on type
    switch (textObject.type) {
      case 'text':
        this.renderSimpleText(textObject);
        break;
      case 'textPath':
        this.renderTextPath(textObject);
        break;
      case 'textBox':
        this.renderTextBox(textObject);
        break;
    }

    const endTime = performance.now();
    this.performanceMetrics.textRenderTime += endTime - startTime;

    this.canvas.renderAll();
    this.emit('typography:object:rendered', { id, textObject });
    
    return true;
  }

  private renderSimpleText(textObject: TypographyObject): void {
    if (!textObject.fabricObject) return;
    
    // Apply typography settings
    const fabricObj = textObject.fabricObject as any;
    fabricObj.typographySettings = textObject.settings;
    
    // Force re-render with new settings
    fabricObj.dirty = true;
    fabricObj._clearCache();
  }

  private renderTextPath(textObject: TypographyObject): void {
    if (!textObject.path || !textObject.fabricObject) return;
    
    // Convert text to path-based text
    // This is a simplified implementation
    const fabricObj = textObject.fabricObject as fabric.Text;
    const pathString = textObject.path.path;
    
    // Create a path object for reference
    const pathObj = new fabric.Path(pathString, {
      fill: 'transparent',
      stroke: 'transparent',
      selectable: false
    });
    
    // Position text along path (simplified)
    const pathLength = (pathObj as any).path?.length || 0;
    if (pathLength > 0) {
      const textWidth = fabricObj.width || 0;
      const offset = (textObject.path.offset / 100) * pathLength;
      
      // Calculate position along path
      const position = this.calculatePathPosition(pathString, offset);
      if (position) {
        fabricObj.set({
          left: position.x,
          top: position.y,
          angle: position.angle
        });
      }
    }
  }

  private calculatePathPosition(pathString: string, offset: number): { x: number; y: number; angle: number } | null {
    // Simplified path position calculation
    // In a real implementation, you'd parse the SVG path properly
    return { x: offset, y: 0, angle: 0 };
  }

  private renderTextBox(textObject: TypographyObject): void {
    if (!textObject.box || !textObject.fabricObject) return;
    
    // Convert to textbox if needed
    if (!(textObject.fabricObject instanceof fabric.Textbox)) {
      const oldObj = textObject.fabricObject;
      const newObj = new fabric.Textbox(textObject.content, {
        ...oldObj.toObject(),
        width: textObject.box.width,
        height: textObject.box.height,
        left: textObject.box.x,
        top: textObject.box.y
      });
      
      this.canvas.remove(oldObj);
      this.canvas.add(newObj);
      textObject.fabricObject = newObj;
    }
    
    // Apply box settings
    const textBox = textObject.fabricObject as fabric.Textbox;
    textBox.set({
      width: textObject.box.width,
      height: textObject.box.height,
      left: textObject.box.x,
      top: textObject.box.y
    });
  }

  // Batch operations
  renderAllTextObjects(): void {
    const startTime = performance.now();
    
    for (const [id] of this.textObjects) {
      this.renderTextObject(id);
    }
    
    const endTime = performance.now();
    this.performanceMetrics.textRenderTime += endTime - startTime;
    
    this.emit('typography:batch:rendered', { count: this.textObjects.size });
  }

  // Configuration
  updateConfig(config: Partial<TypographyEngineConfig>): void {
    Object.assign(this.config, config);
    
    // Apply config changes
    if (config.enableKerning !== undefined) {
      this.textObjects.forEach(textObj => {
        textObj.settings.kerning = config.enableKerning!;
      });
    }
    
    if (config.enableLigatures !== undefined) {
      this.textObjects.forEach(textObj => {
        textObj.settings.ligatures = config.enableLigatures!;
      });
    }
    
    this.emit('typography:config:updated', config);
  }

  // Performance and metrics
  getPerformanceMetrics(): TypographyPerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  resetPerformanceMetrics(): void {
    this.performanceMetrics = {
      textRenderTime: 0,
      measurementTime: 0,
      fontLoadTime: 0,
      objectCount: this.textObjects.size,
      averageComplexity: 0,
      memoryUsage: 0,
      cacheHitRate: 0
    };
  }

  // Cleanup
  dispose(): void {
    // Clear all text objects
    this.textObjects.forEach((textObj, id) => {
      this.deleteTextObject(id);
    });
    
    // Clear caches
    this.styleCache.clear();
    this.calculator.clearCaches();
    
    // Clear render queue
    this.renderQueue = [];
    
    this.removeAllListeners();
    this.emit('typography:disposed');
  }

  // Export/Import
  exportTypographyData(): any {
    const data = {
      objects: Array.from(this.textObjects.values()).map(obj => ({
        ...obj,
        fabricObject: obj.fabricObject?.toObject()
      })),
      config: this.config,
      performance: this.performanceMetrics
    };
    
    return data;
  }

  importTypographyData(data: any): void {
    // Clear existing objects
    this.textObjects.clear();
    
    // Import objects
    for (const objData of data.objects) {
      const textObject: TypographyObject = {
        ...objData,
        fabricObject: undefined
      };
      
      // Recreate fabric object
      if (objData.fabricObject) {
        const fabricObj = new fabric.Text(objData.content, objData.fabricObject);
        textObject.fabricObject = fabricObj;
        this.canvas.add(fabricObj);
      }
      
      this.textObjects.set(textObject.id, textObject);
    }
    
    // Import config
    if (data.config) {
      this.config = { ...this.config, ...data.config };
    }
    
    this.emit('typography:imported', { objectCount: this.textObjects.size });
  }
}