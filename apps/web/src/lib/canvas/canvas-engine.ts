import { fabric } from 'fabric';
import { EventEmitter } from 'events';

// Core Canvas Types
export interface FabricCanvasConfig {
  width: number;
  height: number;
  backgroundColor: string | fabric.Pattern | fabric.Gradient;
  selection: boolean;
  preserveObjectStacking: boolean;
  renderOnAddRemove: boolean;
  controlsAboveOverlay: boolean;
  allowTouchScrolling: boolean;
  imageSmoothingEnabled: boolean;
  enableRetinaScaling: boolean;
  devicePixelRatio: number;
}

export interface CanvasObjectProperties {
  id: string;
  type: string;
  left: number;
  top: number;
  width: number;
  height: number;
  scaleX: number;
  scaleY: number;
  angle: number;
  opacity: number;
  visible: boolean;
  selectable: boolean;
  evented: boolean;
  metadata: Record<string, any>;
  layerId: string;
  locked: boolean;
}

export interface AddObjectOptions {
  id?: string;
  metadata?: Record<string, any>;
  renderImmediately?: boolean;
  layerId?: string;
}

// Command Pattern for Undo/Redo
export abstract class CanvasCommand {
  abstract execute(): void;
  abstract undo(): void;
  abstract getDescription(): string;
}

export class AddObjectCommand extends CanvasCommand {
  constructor(
    private object: fabric.Object,
    private canvas: fabric.Canvas
  ) {
    super();
  }

  execute(): void {
    this.canvas.add(this.object);
    this.canvas.renderAll();
  }

  undo(): void {
    this.canvas.remove(this.object);
    this.canvas.renderAll();
  }

  getDescription(): string {
    return `Add ${this.object.type || 'object'}`;
  }
}

export class RemoveObjectCommand extends CanvasCommand {
  constructor(
    private object: fabric.Object,
    private canvas: fabric.Canvas
  ) {
    super();
  }

  execute(): void {
    this.canvas.remove(this.object);
    this.canvas.renderAll();
  }

  undo(): void {
    this.canvas.add(this.object);
    this.canvas.renderAll();
  }

  getDescription(): string {
    return `Remove ${this.object.type || 'object'}`;
  }
}

// History Manager for Undo/Redo
export class HistoryManager extends EventEmitter {
  private commands: CanvasCommand[] = [];
  private currentIndex: number = -1;
  private maxCommands: number = 50;

  addCommand(command: CanvasCommand): void {
    // Remove any commands after current index
    this.commands = this.commands.slice(0, this.currentIndex + 1);
    
    // Add new command
    this.commands.push(command);
    this.currentIndex++;
    
    // Maintain max commands limit
    if (this.commands.length > this.maxCommands) {
      this.commands.shift();
      this.currentIndex--;
    }
    
    this.emit('history:updated', {
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      currentCommand: command.getDescription()
    });
  }

  undo(): boolean {
    if (!this.canUndo()) return false;
    
    const command = this.commands[this.currentIndex];
    command.undo();
    this.currentIndex--;
    
    this.emit('history:undo', command.getDescription());
    this.emit('history:updated', {
      canUndo: this.canUndo(),
      canRedo: this.canRedo()
    });
    
    return true;
  }

  redo(): boolean {
    if (!this.canRedo()) return false;
    
    this.currentIndex++;
    const command = this.commands[this.currentIndex];
    command.execute();
    
    this.emit('history:redo', command.getDescription());
    this.emit('history:updated', {
      canUndo: this.canUndo(),
      canRedo: this.canRedo()
    });
    
    return true;
  }

  canUndo(): boolean {
    return this.currentIndex >= 0;
  }

  canRedo(): boolean {
    return this.currentIndex < this.commands.length - 1;
  }

  clear(): void {
    this.commands = [];
    this.currentIndex = -1;
    this.emit('history:cleared');
  }
}

// Snap Manager for Professional Alignment
export class SnapManager {
  private canvas: fabric.Canvas;
  private snapDistance: number = 10;
  private guidelines: fabric.Line[] = [];

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.setupSnapEvents();
  }

  private setupSnapEvents(): void {
    this.canvas.on('object:moving', (e) => {
      this.handleObjectSnapping(e.target as fabric.Object);
    });

    this.canvas.on('object:scaling', (e) => {
      this.handleObjectSnapping(e.target as fabric.Object);
    });
  }

  private handleObjectSnapping(target: fabric.Object): void {
    const objects = this.canvas.getObjects().filter(obj => obj !== target);
    this.clearGuidelines();

    const targetBounds = target.getBoundingRect();
    const snapPoints = this.calculateSnapPoints(objects);

    // Horizontal snapping
    const horizontalSnap = this.findNearestSnap(targetBounds.left, snapPoints.vertical);
    if (horizontalSnap) {
      target.set('left', horizontalSnap.value);
      this.showVerticalGuideline(horizontalSnap.value);
    }

    // Vertical snapping
    const verticalSnap = this.findNearestSnap(targetBounds.top, snapPoints.horizontal);
    if (verticalSnap) {
      target.set('top', verticalSnap.value);
      this.showHorizontalGuideline(verticalSnap.value);
    }

    // Center snapping
    const canvasCenter = this.canvas.getCenter();
    const objectCenter = target.getCenterPoint();

    if (Math.abs(objectCenter.x - canvasCenter.left) < this.snapDistance) {
      target.centerH();
      this.showVerticalGuideline(canvasCenter.left);
    }

    if (Math.abs(objectCenter.y - canvasCenter.top) < this.snapDistance) {
      target.centerV();
      this.showHorizontalGuideline(canvasCenter.top);
    }
  }

  private calculateSnapPoints(objects: fabric.Object[]): { vertical: number[], horizontal: number[] } {
    const vertical: number[] = [];
    const horizontal: number[] = [];

    objects.forEach(obj => {
      const bounds = obj.getBoundingRect();
      vertical.push(bounds.left, bounds.left + bounds.width, bounds.left + bounds.width / 2);
      horizontal.push(bounds.top, bounds.top + bounds.height, bounds.top + bounds.height / 2);
    });

    return { vertical, horizontal };
  }

  private findNearestSnap(value: number, snapPoints: number[]): { value: number } | null {
    let nearestDistance = this.snapDistance;
    let nearestValue: number | null = null;

    snapPoints.forEach(point => {
      const distance = Math.abs(value - point);
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestValue = point;
      }
    });

    return nearestValue !== null ? { value: nearestValue } : null;
  }

  private showVerticalGuideline(x: number): void {
    const line = new fabric.Line([x, 0, x, this.canvas.height!], {
      stroke: '#ff0000',
      strokeWidth: 1,
      strokeDashArray: [5, 5],
      selectable: false,
      evented: false,
      excludeFromExport: true
    });

    this.guidelines.push(line);
    this.canvas.add(line);
    this.canvas.bringToFront(line);
  }

  private showHorizontalGuideline(y: number): void {
    const line = new fabric.Line([0, y, this.canvas.width!, y], {
      stroke: '#ff0000',
      strokeWidth: 1,
      strokeDashArray: [5, 5],
      selectable: false,
      evented: false,
      excludeFromExport: true
    });

    this.guidelines.push(line);
    this.canvas.add(line);
    this.canvas.bringToFront(line);
  }

  private clearGuidelines(): void {
    this.guidelines.forEach(line => this.canvas.remove(line));
    this.guidelines = [];
  }

  setSnapDistance(distance: number): void {
    this.snapDistance = distance;
  }
}

// Layer Manager for Professional Layer Control
export class LayerManager extends EventEmitter {
  private canvas: fabric.Canvas;
  private layers: Map<string, { name: string, objects: Set<string>, visible: boolean, locked: boolean }> = new Map();
  private activeLayerId: string = 'default';

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.createDefaultLayer();
  }

  private createDefaultLayer(): void {
    this.layers.set('default', {
      name: 'Layer 1',
      objects: new Set(),
      visible: true,
      locked: false
    });
  }

  createLayer(name: string): string {
    const layerId = `layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.layers.set(layerId, {
      name,
      objects: new Set(),
      visible: true,
      locked: false
    });
    
    this.emit('layer:created', { layerId, name });
    return layerId;
  }

  deleteLayer(layerId: string): boolean {
    if (layerId === 'default' || !this.layers.has(layerId)) {
      return false;
    }

    const layer = this.layers.get(layerId)!;
    
    // Move objects to default layer
    layer.objects.forEach(objectId => {
      this.moveObjectToLayer(objectId, 'default');
    });

    this.layers.delete(layerId);
    
    if (this.activeLayerId === layerId) {
      this.activeLayerId = 'default';
    }
    
    this.emit('layer:deleted', { layerId });
    return true;
  }

  addObjectToLayer(objectId: string, layerId: string = this.activeLayerId): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    // Remove from other layers
    this.layers.forEach((l, id) => {
      if (id !== layerId) {
        l.objects.delete(objectId);
      }
    });

    layer.objects.add(objectId);
    this.emit('layer:object:added', { layerId, objectId });
  }

  removeObjectFromLayer(objectId: string): void {
    this.layers.forEach((layer, layerId) => {
      if (layer.objects.has(objectId)) {
        layer.objects.delete(objectId);
        this.emit('layer:object:removed', { layerId, objectId });
      }
    });
  }

  moveObjectToLayer(objectId: string, targetLayerId: string): boolean {
    const targetLayer = this.layers.get(targetLayerId);
    if (!targetLayer) return false;

    this.removeObjectFromLayer(objectId);
    this.addObjectToLayer(objectId, targetLayerId);
    
    this.emit('layer:object:moved', { objectId, targetLayerId });
    return true;
  }

  setLayerVisibility(layerId: string, visible: boolean): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.visible = visible;
    
    // Update object visibility
    layer.objects.forEach(objectId => {
      const object = this.canvas.getObjects().find(obj => (obj as any).id === objectId);
      if (object) {
        object.set('visible', visible);
      }
    });

    this.canvas.renderAll();
    this.emit('layer:visibility:changed', { layerId, visible });
  }

  setLayerLocked(layerId: string, locked: boolean): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.locked = locked;
    
    // Update object selectability
    layer.objects.forEach(objectId => {
      const object = this.canvas.getObjects().find(obj => (obj as any).id === objectId);
      if (object) {
        object.set('selectable', !locked);
        object.set('evented', !locked);
      }
    });

    this.canvas.renderAll();
    this.emit('layer:lock:changed', { layerId, locked });
  }

  getLayers(): Array<{ id: string, name: string, visible: boolean, locked: boolean, objectCount: number }> {
    return Array.from(this.layers.entries()).map(([id, layer]) => ({
      id,
      name: layer.name,
      visible: layer.visible,
      locked: layer.locked,
      objectCount: layer.objects.size
    }));
  }

  setActiveLayer(layerId: string): boolean {
    if (!this.layers.has(layerId)) return false;
    
    this.activeLayerId = layerId;
    this.emit('layer:active:changed', { layerId });
    return true;
  }

  getActiveLayerId(): string {
    return this.activeLayerId;
  }
}

// Enhanced Canvas Manager with Fabric.js Integration
export class EnhancedCanvasManager extends EventEmitter {
  private fabricCanvas: fabric.Canvas;
  private canvasElement: HTMLCanvasElement;
  private objectRegistry: Map<string, fabric.Object> = new Map();
  private layerManager: LayerManager;
  private snapManager: SnapManager;
  private historyManager: HistoryManager;
  private isInitialized: boolean = false;

  constructor(canvasElement: HTMLCanvasElement, options: Partial<FabricCanvasConfig> = {}) {
    super();
    this.canvasElement = canvasElement;
    
    const defaultOptions: FabricCanvasConfig = {
      width: canvasElement.clientWidth || 800,
      height: canvasElement.clientHeight || 600,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true,
      renderOnAddRemove: false,
      controlsAboveOverlay: true,
      allowTouchScrolling: false,
      imageSmoothingEnabled: true,
      enableRetinaScaling: true,
      devicePixelRatio: window.devicePixelRatio || 1
    };

    const finalOptions = { ...defaultOptions, ...options };

    this.fabricCanvas = new fabric.Canvas(canvasElement, {
      ...finalOptions,
      // Performance optimizations
      skipTargetFind: false,
      perPixelTargetFind: true,
      targetFindTolerance: 4
    });
    
    this.layerManager = new LayerManager(this.fabricCanvas);
    this.snapManager = new SnapManager(this.fabricCanvas);
    this.historyManager = new HistoryManager();
    
    this.initializeEventHandlers();
    this.setupPerformanceOptimizations();
    this.setupCustomControls();
    
    this.isInitialized = true;
    this.emit('canvas:initialized');
  }

  private initializeEventHandlers(): void {
    // Object events
    this.fabricCanvas.on('object:added', (e) => {
      const object = e.target as fabric.Object;
      if (object && (object as any).id) {
        this.objectRegistry.set((object as any).id, object);
        this.layerManager.addObjectToLayer((object as any).id);
      }
    });

    this.fabricCanvas.on('object:removed', (e) => {
      const object = e.target as fabric.Object;
      if (object && (object as any).id) {
        this.objectRegistry.delete((object as any).id);
        this.layerManager.removeObjectFromLayer((object as any).id);
      }
    });

    this.fabricCanvas.on('selection:created', (e) => {
      this.emit('selection:changed', {
        selected: this.getSelectedObjects(),
        count: e.selected?.length || 0
      });
    });

    this.fabricCanvas.on('selection:updated', (e) => {
      this.emit('selection:changed', {
        selected: this.getSelectedObjects(),
        count: e.selected?.length || 0
      });
    });

    this.fabricCanvas.on('selection:cleared', () => {
      this.emit('selection:changed', {
        selected: [],
        count: 0
      });
    });

    // Canvas events
    this.fabricCanvas.on('mouse:down', (e) => {
      this.emit('canvas:mouse:down', e);
    });

    this.fabricCanvas.on('mouse:up', (e) => {
      this.emit('canvas:mouse:up', e);
    });

    this.fabricCanvas.on('mouse:move', (e) => {
      this.emit('canvas:mouse:move', e);
    });
  }

  private setupPerformanceOptimizations(): void {
    // Enable object caching for better performance
    fabric.Object.prototype.objectCaching = true;
    fabric.Object.prototype.statefullCache = true;

    // Use RAF for smooth rendering
    this.fabricCanvas.requestRenderAll = () => {
      requestAnimationFrame(() => {
        this.fabricCanvas.renderAll();
      });
    };

    // Optimize viewport rendering
    this.fabricCanvas.on('viewport:transform', () => {
      this.optimizeViewportRendering();
    });
  }

  private setupCustomControls(): void {
    // Custom control styling
    fabric.Object.prototype.set({
      borderColor: '#2563eb',
      cornerColor: '#2563eb',
      cornerStyle: 'circle',
      cornerSize: 8,
      transparentCorners: false,
      borderScaleFactor: 2,
      borderOpacityWhenMoving: 0.8
    });

    // Custom delete control
    fabric.Object.prototype.controls.deleteControl = new fabric.Control({
      x: 0.5,
      y: -0.5,
      offsetY: -16,
      cursorStyle: 'pointer',
      mouseUpHandler: (eventData, transform, x, y) => {
        const object = transform.target;
        this.removeObject((object as any).id);
        return true;
      },
      render: (ctx, left, top, styleOverride, fabricObject) => {
        const size = 24;
        ctx.save();
        ctx.translate(left, top);
        ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle || 0));
        ctx.fillStyle = '#ff4444';
        ctx.beginPath();
        ctx.arc(0, 0, size / 2, 0, 2 * Math.PI);
        ctx.fill();
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('×', 0, 0);
        ctx.restore();
      },
      cornerSize: 24
    });
  }

  private optimizeViewportRendering(): void {
    // Implement viewport culling for large numbers of objects
    const viewport = this.fabricCanvas.viewportTransform;
    if (!viewport) return;

    const objects = this.fabricCanvas.getObjects();
    const visibleBounds = this.getVisibleBounds();

    objects.forEach(obj => {
      const objBounds = obj.getBoundingRect();
      const isVisible = this.isObjectInViewport(objBounds, visibleBounds);
      
      // Only render objects that are visible
      obj.set('skipOffscreen', !isVisible);
    });
  }

  private getVisibleBounds(): fabric.Rect {
    const vpt = this.fabricCanvas.viewportTransform!;
    const zoom = this.fabricCanvas.getZoom();
    
    return new fabric.Rect({
      left: -vpt[4] / zoom,
      top: -vpt[5] / zoom,
      width: this.fabricCanvas.width! / zoom,
      height: this.fabricCanvas.height! / zoom
    });
  }

  private isObjectInViewport(objBounds: fabric.Rect, viewport: fabric.Rect): boolean {
    return !(objBounds.left > viewport.left + viewport.width ||
             objBounds.left + objBounds.width < viewport.left ||
             objBounds.top > viewport.top + viewport.height ||
             objBounds.top + objBounds.height < viewport.top);
  }

  // Object Management Methods
  addObject(object: fabric.Object, options: AddObjectOptions = {}): void {
    // Add unique ID and metadata
    const id = options.id || this.generateUniqueId();
    const metadata = {
      createdAt: Date.now(),
      tool: 'unknown',
      type: object.type || 'object',
      ...options.metadata
    };

    object.set({
      id,
      metadata
    } as any);

    this.objectRegistry.set(id, object);
    this.fabricCanvas.add(object);

    if (options.layerId) {
      this.layerManager.addObjectToLayer(id, options.layerId);
    }

    if (options.renderImmediately !== false) {
      this.renderCanvas();
    }

    this.historyManager.addCommand(new AddObjectCommand(object, this.fabricCanvas));
    this.emit('object:added', { id, object });
  }

  removeObject(objectId: string): boolean {
    const object = this.objectRegistry.get(objectId);
    if (!object) return false;

    this.fabricCanvas.remove(object);
    this.objectRegistry.delete(objectId);
    this.layerManager.removeObjectFromLayer(objectId);
    
    this.renderCanvas();
    this.historyManager.addCommand(new RemoveObjectCommand(object, this.fabricCanvas));
    this.emit('object:removed', { id: objectId });
    
    return true;
  }

  selectObjects(objectIds: string[]): void {
    const objects = objectIds
      .map(id => this.objectRegistry.get(id))
      .filter(Boolean) as fabric.Object[];

    if (objects.length === 0) {
      this.fabricCanvas.discardActiveObject();
    } else if (objects.length === 1) {
      this.fabricCanvas.setActiveObject(objects[0]);
    } else {
      const selection = new fabric.ActiveSelection(objects, {
        canvas: this.fabricCanvas
      });
      this.fabricCanvas.setActiveObject(selection);
    }

    this.renderCanvas();
  }

  getSelectedObjects(): Array<{ id: string, object: fabric.Object }> {
    const activeObject = this.fabricCanvas.getActiveObject();
    if (!activeObject) return [];

    if (activeObject.type === 'activeSelection') {
      const selection = activeObject as fabric.ActiveSelection;
      return selection.getObjects().map(obj => ({
        id: (obj as any).id,
        object: obj
      })).filter(item => item.id);
    } else {
      const id = (activeObject as any).id;
      return id ? [{ id, object: activeObject }] : [];
    }
  }

  duplicateObject(objectId: string): string | null {
    const object = this.objectRegistry.get(objectId);
    if (!object) return null;

    return new Promise<string>((resolve) => {
      object.clone((cloned: fabric.Object) => {
        const newId = this.generateUniqueId();
        cloned.set({
          id: newId,
          left: (object.left || 0) + 20,
          top: (object.top || 0) + 20,
          metadata: {
            ...(object as any).metadata,
            createdAt: Date.now(),
            clonedFrom: objectId
          }
        } as any);

        this.addObject(cloned, { renderImmediately: true });
        resolve(newId);
      });
    }) as any;
  }

  // Canvas Control Methods
  setCanvasSize(width: number, height: number): void {
    this.fabricCanvas.setWidth(width);
    this.fabricCanvas.setHeight(height);
    this.renderCanvas();
  }

  setBackgroundColor(color: string): void {
    this.fabricCanvas.setBackgroundColor(color, () => {
      this.renderCanvas();
    });
  }

  zoomToFit(): void {
    const objects = this.fabricCanvas.getObjects();
    if (objects.length === 0) return;

    const bbox = this.getBoundingBoxOfObjects(objects);
    const canvasAspect = this.fabricCanvas.width! / this.fabricCanvas.height!;
    const bboxAspect = bbox.width / bbox.height;

    let zoom: number;
    if (bboxAspect > canvasAspect) {
      zoom = (this.fabricCanvas.width! * 0.8) / bbox.width;
    } else {
      zoom = (this.fabricCanvas.height! * 0.8) / bbox.height;
    }

    const center = this.fabricCanvas.getCenter();
    this.fabricCanvas.zoomToPoint(new fabric.Point(center.left, center.top), zoom);

    const bboxCenter = new fabric.Point(
      bbox.left + bbox.width / 2,
      bbox.top + bbox.height / 2
    );
    this.fabricCanvas.absolutePan(new fabric.Point(
      center.left - bboxCenter.x * zoom,
      center.top - bboxCenter.y * zoom
    ));

    this.renderCanvas();
  }

  private getBoundingBoxOfObjects(objects: fabric.Object[]): { left: number, top: number, width: number, height: number } {
    if (objects.length === 0) {
      return { left: 0, top: 0, width: 0, height: 0 };
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    objects.forEach(obj => {
      const bounds = obj.getBoundingRect();
      minX = Math.min(minX, bounds.left);
      minY = Math.min(minY, bounds.top);
      maxX = Math.max(maxX, bounds.left + bounds.width);
      maxY = Math.max(maxY, bounds.top + bounds.height);
    });

    return {
      left: minX,
      top: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  // Rendering and Performance
  renderCanvas(): void {
    requestAnimationFrame(() => {
      this.fabricCanvas.renderAll();
    });
  }

  enableHybridRendering(): void {
    // Prepare for PIXI.js integration
    this.fabricCanvas.contextType = '2d';
    this.fabricCanvas.interactive = true;

    // Set up layer separation for optimal performance
    this.layerManager.emit('hybrid:enabled');
  }

  // Utility Methods
  private generateUniqueId(): string {
    return `obj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public Getters
  get canvas(): fabric.Canvas {
    return this.fabricCanvas;
  }

  get layers(): LayerManager {
    return this.layerManager;
  }

  get snap(): SnapManager {
    return this.snapManager;
  }

  get history(): HistoryManager {
    return this.historyManager;
  }

  get objects(): Map<string, fabric.Object> {
    return this.objectRegistry;
  }

  get isReady(): boolean {
    return this.isInitialized;
  }

  // Cleanup
  dispose(): void {
    this.fabricCanvas.dispose();
    this.objectRegistry.clear();
    this.historyManager.clear();
    this.removeAllListeners();
  }
}