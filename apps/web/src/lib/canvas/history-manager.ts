import { fabric } from 'fabric';

export interface HistoryState {
  id: string;
  timestamp: number;
  canvasState: string;
  description?: string;
}

export class HistoryManager {
  private canvas: fabric.Canvas;
  private history: HistoryState[] = [];
  private currentIndex = -1;
  private maxHistorySize = 50;

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.saveState('Initial state');
  }

  public saveState(description?: string): void {
    // Remove any states after current index (when undoing then making new changes)
    this.history = this.history.slice(0, this.currentIndex + 1);
    
    const state: HistoryState = {
      id: `state_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      canvasState: JSON.stringify(this.canvas.toJSON()),
      description
    };
    
    this.history.push(state);
    this.currentIndex = this.history.length - 1;
    
    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.currentIndex--;
    }
  }

  public undo(): boolean {
    if (this.currentIndex > 0) {
      this.currentIndex--;
      this.restoreState(this.history[this.currentIndex]);
      return true;
    }
    return false;
  }

  public redo(): boolean {
    if (this.currentIndex < this.history.length - 1) {
      this.currentIndex++;
      this.restoreState(this.history[this.currentIndex]);
      return true;
    }
    return false;
  }

  private restoreState(state: HistoryState): void {
    this.canvas.loadFromJSON(state.canvasState, () => {
      this.canvas.renderAll();
    });
  }

  public canUndo(): boolean {
    return this.currentIndex > 0;
  }

  public canRedo(): boolean {
    return this.currentIndex < this.history.length - 1;
  }

  public getHistory(): HistoryState[] {
    return [...this.history];
  }

  public getCurrentState(): HistoryState | null {
    return this.history[this.currentIndex] || null;
  }
}