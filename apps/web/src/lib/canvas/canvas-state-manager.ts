import { EventEmitter } from 'events';
import { fabric } from 'fabric';
import { LayerNode, AdvancedLayerManager } from './advanced-layer-manager';
import { EnhancedHybridCanvasManager } from './enhanced-hybrid-canvas';

// Core state management types
export interface CanvasState {
  id: string;
  name: string;
  timestamp: number;
  version: number;
  canvasData: {
    objects: any[];
    background: string;
    width: number;
    height: number;
    zoom: number;
    viewportTransform: number[];
  };
  layerData: {
    layers: Map<string, LayerNode>;
    rootLayers: string[];
    selectedLayers: Set<string>;
    activeLayer?: string;
  };
  metadata: {
    author?: string;
    description?: string;
    tags: string[];
    thumbnail?: string;
    changeDescription?: string;
  };
  checksums: {
    canvas: string;
    layers: string;
    combined: string;
  };
}

export interface StateSnapshot {
  id: string;
  parentId?: string;
  branchName?: string;
  state: CanvasState;
  timestamp: number;
  compressed: boolean;
  size: number;
  delta?: StateDelta;
}

export interface StateDelta {
  type: 'add' | 'remove' | 'modify' | 'reorder';
  target: 'canvas' | 'layer' | 'selection';
  changes: Array<{
    path: string;
    oldValue?: any;
    newValue?: any;
    operation: 'set' | 'delete' | 'insert' | 'move';
  }>;
}

export interface StateBranch {
  id: string;
  name: string;
  parentSnapshotId: string;
  snapshots: string[];
  isActive: boolean;
  mergedTo?: string;
  createdAt: number;
  lastUpdated: number;
}

export interface StateHistory {
  snapshots: Map<string, StateSnapshot>;
  branches: Map<string, StateBranch>;
  currentSnapshotId?: string;
  currentBranchId: string;
  maxSnapshots: number;
  compressionThreshold: number;
}

export interface StateManagerConfig {
  maxUndoSteps: number;
  compressionThreshold: number;
  autoSaveInterval: number;
  enableBranching: boolean;
  enableCompression: boolean;
  enableDifferentialEncoding: boolean;
  memoryLimit: number; // MB
  enablePerformanceOptimization: boolean;
}

export interface StateOperation {
  type: 'undo' | 'redo' | 'branch' | 'merge' | 'save' | 'load';
  snapshotId: string;
  branchId?: string;
  timestamp: number;
  metadata?: any;
}

// Memory-efficient state compression
export class StateCompressor {
  private compressionLevel: number = 6;

  compress(data: any): { compressed: string; originalSize: number; compressedSize: number } {
    const jsonString = JSON.stringify(data);
    const originalSize = jsonString.length;
    
    // Simple compression - in production would use proper compression algorithm
    const compressed = this.deflateString(jsonString);
    const compressedSize = compressed.length;

    return {
      compressed,
      originalSize,
      compressedSize
    };
  }

  decompress(compressed: string): any {
    const decompressed = this.inflateString(compressed);
    return JSON.parse(decompressed);
  }

  private deflateString(str: string): string {
    // Simplified compression - replace with actual compression library
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
      return String.fromCharCode(parseInt(p1, 16));
    }));
  }

  private inflateString(compressed: string): string {
    // Simplified decompression - replace with actual compression library
    return decodeURIComponent(Array.prototype.map.call(atob(compressed), (c) => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
  }

  getCompressionRatio(originalSize: number, compressedSize: number): number {
    return originalSize > 0 ? compressedSize / originalSize : 0;
  }
}

// Differential state encoding for memory efficiency
export class StateDifferentialEncoder {
  calculateDelta(previousState: CanvasState, currentState: CanvasState): StateDelta {
    const changes: StateDelta['changes'] = [];

    // Compare canvas data
    this.compareObjects(previousState.canvasData, currentState.canvasData, 'canvasData', changes);
    
    // Compare layer data
    this.compareLayerData(previousState.layerData, currentState.layerData, changes);

    return {
      type: this.determineDeltaType(changes),
      target: this.determinePrimaryTarget(changes),
      changes
    };
  }

  applyDelta(baseState: CanvasState, delta: StateDelta): CanvasState {
    const newState = JSON.parse(JSON.stringify(baseState));

    delta.changes.forEach(change => {
      this.applyChange(newState, change);
    });

    return newState;
  }

  private compareObjects(obj1: any, obj2: any, path: string, changes: StateDelta['changes']): void {
    const keys1 = Object.keys(obj1 || {});
    const keys2 = Object.keys(obj2 || {});
    const allKeys = new Set([...keys1, ...keys2]);

    for (const key of allKeys) {
      const currentPath = `${path}.${key}`;
      const val1 = obj1?.[key];
      const val2 = obj2?.[key];

      if (val1 === undefined && val2 !== undefined) {
        changes.push({
          path: currentPath,
          newValue: val2,
          operation: 'set'
        });
      } else if (val1 !== undefined && val2 === undefined) {
        changes.push({
          path: currentPath,
          oldValue: val1,
          operation: 'delete'
        });
      } else if (JSON.stringify(val1) !== JSON.stringify(val2)) {
        if (typeof val1 === 'object' && typeof val2 === 'object') {
          this.compareObjects(val1, val2, currentPath, changes);
        } else {
          changes.push({
            path: currentPath,
            oldValue: val1,
            newValue: val2,
            operation: 'set'
          });
        }
      }
    }
  }

  private compareLayerData(layerData1: any, layerData2: any, changes: StateDelta['changes']): void {
    // Compare layers maps
    const layers1 = layerData1.layers || new Map();
    const layers2 = layerData2.layers || new Map();

    // Convert Maps to objects for comparison
    const layersObj1 = Object.fromEntries(layers1);
    const layersObj2 = Object.fromEntries(layers2);

    this.compareObjects(layersObj1, layersObj2, 'layerData.layers', changes);

    // Compare other layer data properties
    const otherProps = ['rootLayers', 'selectedLayers', 'activeLayer'];
    otherProps.forEach(prop => {
      if (JSON.stringify(layerData1[prop]) !== JSON.stringify(layerData2[prop])) {
        changes.push({
          path: `layerData.${prop}`,
          oldValue: layerData1[prop],
          newValue: layerData2[prop],
          operation: 'set'
        });
      }
    });
  }

  private determineDeltaType(changes: StateDelta['changes']): StateDelta['type'] {
    const hasAdditions = changes.some(c => c.operation === 'set' && c.oldValue === undefined);
    const hasDeletions = changes.some(c => c.operation === 'delete');
    const hasModifications = changes.some(c => c.operation === 'set' && c.oldValue !== undefined);
    const hasMoves = changes.some(c => c.operation === 'move');

    if (hasMoves) return 'reorder';
    if (hasAdditions && !hasDeletions && !hasModifications) return 'add';
    if (hasDeletions && !hasAdditions && !hasModifications) return 'remove';
    return 'modify';
  }

  private determinePrimaryTarget(changes: StateDelta['changes']): StateDelta['target'] {
    const canvasChanges = changes.filter(c => c.path.startsWith('canvasData'));
    const layerChanges = changes.filter(c => c.path.startsWith('layerData'));

    if (canvasChanges.length > layerChanges.length) return 'canvas';
    if (layerChanges.length > 0) return 'layer';
    return 'selection';
  }

  private applyChange(state: any, change: StateDelta['changes'][0]): void {
    const pathParts = change.path.split('.');
    let current = state;

    // Navigate to parent object
    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i];
      if (current[part] === undefined) {
        current[part] = {};
      }
      current = current[part];
    }

    const lastPart = pathParts[pathParts.length - 1];

    switch (change.operation) {
      case 'set':
        current[lastPart] = change.newValue;
        break;
      case 'delete':
        delete current[lastPart];
        break;
      case 'insert':
        if (Array.isArray(current[lastPart])) {
          current[lastPart].push(change.newValue);
        }
        break;
      case 'move':
        // Handle array reordering
        if (Array.isArray(current[lastPart]) && change.oldValue !== undefined && change.newValue !== undefined) {
          const item = current[lastPart].splice(change.oldValue, 1)[0];
          current[lastPart].splice(change.newValue, 0, item);
        }
        break;
    }
  }
}

// Advanced Canvas State Manager with memory optimization and branching
export class CanvasStateManager extends EventEmitter {
  private canvasManager: EnhancedHybridCanvasManager;
  private layerManager: AdvancedLayerManager;
  private history: StateHistory;
  private config: StateManagerConfig;
  private compressor: StateCompressor;
  private deltaEncoder: StateDifferentialEncoder;
  private isCapturingState: boolean = false;
  private pendingOperations: StateOperation[] = [];
  private memoryUsage: number = 0;
  private lastAutoSave: number = 0;

  constructor(
    canvasManager: EnhancedHybridCanvasManager,
    layerManager: AdvancedLayerManager,
    config: Partial<StateManagerConfig> = {}
  ) {
    super();
    
    this.canvasManager = canvasManager;
    this.layerManager = layerManager;
    this.config = this.mergeDefaultConfig(config);
    this.compressor = new StateCompressor();
    this.deltaEncoder = new StateDifferentialEncoder();
    this.history = this.initializeHistory();

    this.setupEventHandlers();
    this.startPerformanceMonitoring();
  }

  private mergeDefaultConfig(config: Partial<StateManagerConfig>): StateManagerConfig {
    return {
      maxUndoSteps: 50,
      compressionThreshold: 1024 * 1024, // 1MB
      autoSaveInterval: 30000, // 30 seconds
      enableBranching: true,
      enableCompression: true,
      enableDifferentialEncoding: true,
      memoryLimit: 100, // 100MB
      enablePerformanceOptimization: true,
      ...config
    };
  }

  private initializeHistory(): StateHistory {
    return {
      snapshots: new Map(),
      branches: new Map(),
      currentBranchId: 'main',
      maxSnapshots: this.config.maxUndoSteps,
      compressionThreshold: this.config.compressionThreshold
    };
  }

  private setupEventHandlers(): void {
    // Canvas events that trigger state capture
    this.canvasManager.on('object:added', () => this.debounceStateCapture('object:added'));
    this.canvasManager.on('object:removed', () => this.debounceStateCapture('object:removed'));
    this.canvasManager.on('object:modified', () => this.debounceStateCapture('object:modified'));
    
    // Layer events that trigger state capture
    this.layerManager.on('layer:created', () => this.debounceStateCapture('layer:created'));
    this.layerManager.on('layer:deleted', () => this.debounceStateCapture('layer:deleted'));
    this.layerManager.on('layer:property:changed', () => this.debounceStateCapture('layer:modified'));
    this.layerManager.on('layer:moved', () => this.debounceStateCapture('layer:moved'));

    // Auto-save timer
    if (this.config.autoSaveInterval > 0) {
      setInterval(() => {
        this.performAutoSave();
      }, this.config.autoSaveInterval);
    }
  }

  private debounceStateCapture(reason: string): void {
    // Debounce rapid state changes
    clearTimeout((this as any).captureTimeout);
    (this as any).captureTimeout = setTimeout(() => {
      this.captureCurrentState(reason);
    }, 100);
  }

  // Core state management methods

  captureCurrentState(description?: string): StateSnapshot {
    if (this.isCapturingState) {
      console.warn('State capture already in progress, skipping...');
      return this.getCurrentSnapshot()!;
    }

    this.isCapturingState = true;

    try {
      const currentState = this.extractCurrentState();
      const snapshot = this.createSnapshot(currentState, description);
      
      this.addSnapshotToHistory(snapshot);
      this.cleanupOldSnapshots();
      this.updateMemoryUsage();

      this.emit('state:captured', { snapshot, memoryUsage: this.memoryUsage });
      
      return snapshot;
    } finally {
      this.isCapturingState = false;
    }
  }

  private extractCurrentState(): CanvasState {
    // Extract canvas state
    const canvasData = {
      objects: this.canvasManager.fabricCanvasManager.exportToJSON().objects || [],
      background: this.canvasManager.fabricCanvasManager.canvas?.backgroundColor || '#ffffff',
      width: this.canvasManager.fabricCanvasManager.canvas?.width || 800,
      height: this.canvasManager.fabricCanvasManager.canvas?.height || 600,
      zoom: this.canvasManager.fabricCanvasManager.canvas?.getZoom() || 1,
      viewportTransform: this.canvasManager.fabricCanvasManager.canvas?.viewportTransform || [1, 0, 0, 1, 0, 0]
    };

    // Extract layer state
    const layerData = {
      layers: this.layerManager.layers,
      rootLayers: this.layerManager.rootLayers,
      selectedLayers: this.layerManager.selectedLayers,
      activeLayer: this.layerManager.activeLayer
    };

    // Calculate checksums for integrity
    const checksums = {
      canvas: this.calculateChecksum(canvasData),
      layers: this.calculateChecksum(layerData),
      combined: ''
    };
    checksums.combined = this.calculateChecksum(checksums.canvas + checksums.layers);

    return {
      id: this.generateStateId(),
      name: `State ${Date.now()}`,
      timestamp: Date.now(),
      version: 1,
      canvasData,
      layerData,
      metadata: {
        tags: [],
        changeDescription: ''
      },
      checksums
    };
  }

  private createSnapshot(state: CanvasState, description?: string): StateSnapshot {
    const snapshotId = this.generateSnapshotId();
    let compressed = false;
    let size = 0;
    let delta: StateDelta | undefined;

    // Calculate delta if differential encoding is enabled
    if (this.config.enableDifferentialEncoding && this.history.currentSnapshotId) {
      const currentSnapshot = this.history.snapshots.get(this.history.currentSnapshotId);
      if (currentSnapshot) {
        delta = this.deltaEncoder.calculateDelta(currentSnapshot.state, state);
      }
    }

    // Determine if compression is needed
    const stateString = JSON.stringify(state);
    size = stateString.length;

    if (this.config.enableCompression && size > this.config.compressionThreshold) {
      compressed = true;
    }

    if (description) {
      state.metadata.changeDescription = description;
    }

    return {
      id: snapshotId,
      parentId: this.history.currentSnapshotId,
      branchName: this.getCurrentBranch()?.name,
      state,
      timestamp: Date.now(),
      compressed,
      size,
      delta
    };
  }

  private addSnapshotToHistory(snapshot: StateSnapshot): void {
    this.history.snapshots.set(snapshot.id, snapshot);
    this.history.currentSnapshotId = snapshot.id;

    // Add to current branch
    const currentBranch = this.getCurrentBranch();
    if (currentBranch) {
      currentBranch.snapshots.push(snapshot.id);
      currentBranch.lastUpdated = Date.now();
    }

    this.addOperation({
      type: 'save',
      snapshotId: snapshot.id,
      timestamp: Date.now()
    });
  }

  // Undo/Redo with branching support
  undo(): boolean {
    if (!this.canUndo()) return false;

    const currentSnapshot = this.getCurrentSnapshot();
    if (!currentSnapshot?.parentId) return false;

    const previousSnapshot = this.history.snapshots.get(currentSnapshot.parentId);
    if (!previousSnapshot) return false;

    this.restoreSnapshot(previousSnapshot);
    this.history.currentSnapshotId = previousSnapshot.id;

    this.addOperation({
      type: 'undo',
      snapshotId: previousSnapshot.id,
      timestamp: Date.now()
    });

    this.emit('state:undo', { 
      currentSnapshot: previousSnapshot,
      previousSnapshot: currentSnapshot,
      canUndo: this.canUndo(),
      canRedo: this.canRedo()
    });

    return true;
  }

  redo(): boolean {
    if (!this.canRedo()) return false;

    const currentSnapshot = this.getCurrentSnapshot();
    if (!currentSnapshot) return false;

    // Find next snapshot in current branch
    const nextSnapshot = this.findNextSnapshotInBranch(currentSnapshot.id);
    if (!nextSnapshot) return false;

    this.restoreSnapshot(nextSnapshot);
    this.history.currentSnapshotId = nextSnapshot.id;

    this.addOperation({
      type: 'redo',
      snapshotId: nextSnapshot.id,
      timestamp: Date.now()
    });

    this.emit('state:redo', { 
      currentSnapshot: nextSnapshot,
      previousSnapshot: currentSnapshot,
      canUndo: this.canUndo(),
      canRedo: this.canRedo()
    });

    return true;
  }

  canUndo(): boolean {
    const currentSnapshot = this.getCurrentSnapshot();
    return currentSnapshot?.parentId !== undefined;
  }

  canRedo(): boolean {
    const currentSnapshot = this.getCurrentSnapshot();
    return currentSnapshot ? this.findNextSnapshotInBranch(currentSnapshot.id) !== null : false;
  }

  private findNextSnapshotInBranch(snapshotId: string): StateSnapshot | null {
    const currentBranch = this.getCurrentBranch();
    if (!currentBranch) return null;

    const currentIndex = currentBranch.snapshots.indexOf(snapshotId);
    if (currentIndex === -1 || currentIndex === currentBranch.snapshots.length - 1) {
      return null;
    }

    const nextSnapshotId = currentBranch.snapshots[currentIndex + 1];
    return this.history.snapshots.get(nextSnapshotId) || null;
  }

  // Branch management
  createBranch(name: string, fromSnapshotId?: string): StateBranch {
    if (!this.config.enableBranching) {
      throw new Error('Branching is disabled in configuration');
    }

    const branchId = this.generateBranchId();
    const parentSnapshotId = fromSnapshotId || this.history.currentSnapshotId;
    
    if (!parentSnapshotId) {
      throw new Error('No snapshot available to branch from');
    }

    const branch: StateBranch = {
      id: branchId,
      name,
      parentSnapshotId,
      snapshots: [],
      isActive: false,
      createdAt: Date.now(),
      lastUpdated: Date.now()
    };

    this.history.branches.set(branchId, branch);

    this.addOperation({
      type: 'branch',
      snapshotId: parentSnapshotId,
      branchId,
      timestamp: Date.now(),
      metadata: { name }
    });

    this.emit('branch:created', { branch });
    
    return branch;
  }

  switchToBranch(branchId: string): boolean {
    const branch = this.history.branches.get(branchId);
    if (!branch) return false;

    // Deactivate current branch
    const currentBranch = this.getCurrentBranch();
    if (currentBranch) {
      currentBranch.isActive = false;
    }

    // Activate new branch
    branch.isActive = true;
    this.history.currentBranchId = branchId;

    // Switch to latest snapshot in branch
    if (branch.snapshots.length > 0) {
      const latestSnapshotId = branch.snapshots[branch.snapshots.length - 1];
      const latestSnapshot = this.history.snapshots.get(latestSnapshotId);
      if (latestSnapshot) {
        this.restoreSnapshot(latestSnapshot);
        this.history.currentSnapshotId = latestSnapshot.id;
      }
    } else {
      // Switch to parent snapshot
      const parentSnapshot = this.history.snapshots.get(branch.parentSnapshotId);
      if (parentSnapshot) {
        this.restoreSnapshot(parentSnapshot);
        this.history.currentSnapshotId = parentSnapshot.id;
      }
    }

    this.emit('branch:switched', { branch, branchId });
    
    return true;
  }

  mergeBranch(sourceBranchId: string, targetBranchId: string): boolean {
    const sourceBranch = this.history.branches.get(sourceBranchId);
    const targetBranch = this.history.branches.get(targetBranchId);
    
    if (!sourceBranch || !targetBranch) return false;

    // Simple merge - take latest snapshot from source branch
    if (sourceBranch.snapshots.length === 0) return false;

    const latestSourceSnapshotId = sourceBranch.snapshots[sourceBranch.snapshots.length - 1];
    const latestSourceSnapshot = this.history.snapshots.get(latestSourceSnapshotId);
    
    if (!latestSourceSnapshot) return false;

    // Create merge snapshot
    const mergeSnapshot = this.createSnapshot(
      latestSourceSnapshot.state,
      `Merge branch '${sourceBranch.name}' into '${targetBranch.name}'`
    );
    
    mergeSnapshot.parentId = targetBranch.snapshots.length > 0 
      ? targetBranch.snapshots[targetBranch.snapshots.length - 1]
      : targetBranch.parentSnapshotId;

    // Add to target branch
    this.history.snapshots.set(mergeSnapshot.id, mergeSnapshot);
    targetBranch.snapshots.push(mergeSnapshot.id);
    targetBranch.lastUpdated = Date.now();

    // Mark source branch as merged
    sourceBranch.mergedTo = targetBranchId;

    this.addOperation({
      type: 'merge',
      snapshotId: mergeSnapshot.id,
      branchId: targetBranchId,
      timestamp: Date.now(),
      metadata: { sourceBranchId, mergeSnapshot: mergeSnapshot.id }
    });

    this.emit('branch:merged', { 
      sourceBranch, 
      targetBranch, 
      mergeSnapshot 
    });

    return true;
  }

  // State restoration
  private restoreSnapshot(snapshot: StateSnapshot): void {
    try {
      let state = snapshot.state;

      // Decompress if needed
      if (snapshot.compressed) {
        state = this.compressor.decompress(snapshot.state as any);
      }

      // Apply delta if needed
      if (snapshot.delta && snapshot.parentId) {
        const parentSnapshot = this.history.snapshots.get(snapshot.parentId);
        if (parentSnapshot) {
          state = this.deltaEncoder.applyDelta(parentSnapshot.state, snapshot.delta);
        }
      }

      // Restore canvas state
      this.restoreCanvasState(state.canvasData);

      // Restore layer state
      this.restoreLayerState(state.layerData);

      this.emit('state:restored', { snapshot, state });

    } catch (error) {
      console.error('Error restoring snapshot:', error);
      this.emit('state:restore:error', { snapshot, error });
    }
  }

  private restoreCanvasState(canvasData: any): void {
    const canvas = this.canvasManager.fabricCanvasManager.canvas;
    if (!canvas) return;

    // Clear current objects
    canvas.clear();

    // Set canvas properties
    canvas.setBackgroundColor(canvasData.background, () => {});
    canvas.setDimensions({
      width: canvasData.width,
      height: canvasData.height
    });
    canvas.setZoom(canvasData.zoom);
    canvas.setViewportTransform(canvasData.viewportTransform);

    // Load objects
    fabric.util.enlivenObjects(canvasData.objects, (objects: fabric.Object[]) => {
      objects.forEach(obj => canvas.add(obj));
      canvas.renderAll();
    });
  }

  private restoreLayerState(layerData: any): void {
    // This would require methods in AdvancedLayerManager to restore full state
    // For now, we'll emit an event for the layer manager to handle
    this.emit('layer:state:restore:requested', { layerData });
  }

  // Memory management and cleanup
  private cleanupOldSnapshots(): void {
    if (this.history.snapshots.size <= this.config.maxUndoSteps) return;

    // Keep recent snapshots and branch points
    const snapshotsToKeep = new Set<string>();
    
    // Keep current snapshot chain
    let current = this.history.currentSnapshotId;
    while (current) {
      snapshotsToKeep.add(current);
      const snapshot = this.history.snapshots.get(current);
      current = snapshot?.parentId;
    }

    // Keep branch parent snapshots
    this.history.branches.forEach(branch => {
      snapshotsToKeep.add(branch.parentSnapshotId);
      branch.snapshots.forEach(id => snapshotsToKeep.add(id));
    });

    // Remove old snapshots
    const allSnapshots = Array.from(this.history.snapshots.keys());
    const snapshotsToRemove = allSnapshots
      .filter(id => !snapshotsToKeep.has(id))
      .sort((a, b) => {
        const snapA = this.history.snapshots.get(a);
        const snapB = this.history.snapshots.get(b);
        return (snapA?.timestamp || 0) - (snapB?.timestamp || 0);
      })
      .slice(0, Math.max(0, this.history.snapshots.size - this.config.maxUndoSteps));

    snapshotsToRemove.forEach(id => {
      this.history.snapshots.delete(id);
    });

    if (snapshotsToRemove.length > 0) {
      this.emit('state:cleanup', { 
        removedCount: snapshotsToRemove.length,
        remainingCount: this.history.snapshots.size
      });
    }
  }

  private updateMemoryUsage(): void {
    let totalSize = 0;
    
    this.history.snapshots.forEach(snapshot => {
      totalSize += snapshot.size;
    });

    this.memoryUsage = totalSize / (1024 * 1024); // Convert to MB

    if (this.memoryUsage > this.config.memoryLimit) {
      this.performMemoryOptimization();
    }
  }

  private performMemoryOptimization(): void {
    console.log('Performing memory optimization...');
    
    // Compress large uncompressed snapshots
    const uncompressedSnapshots = Array.from(this.history.snapshots.values())
      .filter(s => !s.compressed && s.size > this.config.compressionThreshold)
      .sort((a, b) => b.size - a.size);

    uncompressedSnapshots.slice(0, 10).forEach(snapshot => {
      const compressed = this.compressor.compress(snapshot.state);
      (snapshot.state as any) = compressed.compressed;
      snapshot.compressed = true;
      snapshot.size = compressed.compressedSize;
    });

    // Force cleanup of old snapshots
    const oldMaxSnapshots = this.config.maxUndoSteps;
    this.config.maxUndoSteps = Math.floor(this.config.maxUndoSteps * 0.7);
    this.cleanupOldSnapshots();
    this.config.maxUndoSteps = oldMaxSnapshots;

    this.updateMemoryUsage();
    
    this.emit('memory:optimized', { 
      memoryUsage: this.memoryUsage,
      snapshotCount: this.history.snapshots.size
    });
  }

  // Auto-save functionality
  private performAutoSave(): void {
    const now = Date.now();
    if (now - this.lastAutoSave < this.config.autoSaveInterval) return;

    this.lastAutoSave = now;
    
    // Only auto-save if there have been changes
    if (this.hasUnsavedChanges()) {
      const snapshot = this.captureCurrentState('Auto-save');
      this.emit('auto:save:completed', { snapshot });
    }
  }

  private hasUnsavedChanges(): boolean {
    // Check if current state differs from last saved state
    const currentSnapshot = this.getCurrentSnapshot();
    if (!currentSnapshot) return true;

    const currentState = this.extractCurrentState();
    return currentState.checksums.combined !== currentSnapshot.state.checksums.combined;
  }

  // Performance monitoring
  private startPerformanceMonitoring(): void {
    if (!this.config.enablePerformanceOptimization) return;

    setInterval(() => {
      this.checkPerformanceMetrics();
    }, 5000);
  }

  private checkPerformanceMetrics(): void {
    const metrics = {
      memoryUsage: this.memoryUsage,
      snapshotCount: this.history.snapshots.size,
      branchCount: this.history.branches.size,
      operationQueueLength: this.pendingOperations.length
    };

    this.emit('performance:metrics', metrics);

    // Auto-optimize if needed
    if (metrics.memoryUsage > this.config.memoryLimit * 0.8) {
      this.performMemoryOptimization();
    }
  }

  // Utility methods
  private calculateChecksum(data: any): string {
    const str = typeof data === 'string' ? data : JSON.stringify(data);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString(16);
  }

  private generateStateId(): string {
    return `state_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSnapshotId(): string {
    return `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBranchId(): string {
    return `branch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private addOperation(operation: StateOperation): void {
    this.pendingOperations.push(operation);
    
    // Keep only recent operations
    if (this.pendingOperations.length > 100) {
      this.pendingOperations = this.pendingOperations.slice(-50);
    }
  }

  // Public API methods
  getCurrentSnapshot(): StateSnapshot | undefined {
    return this.history.currentSnapshotId 
      ? this.history.snapshots.get(this.history.currentSnapshotId)
      : undefined;
  }

  getCurrentBranch(): StateBranch | undefined {
    return this.history.branches.get(this.history.currentBranchId);
  }

  getSnapshot(snapshotId: string): StateSnapshot | undefined {
    return this.history.snapshots.get(snapshotId);
  }

  getBranch(branchId: string): StateBranch | undefined {
    return this.history.branches.get(branchId);
  }

  getAllBranches(): StateBranch[] {
    return Array.from(this.history.branches.values());
  }

  getSnapshotHistory(): StateSnapshot[] {
    return Array.from(this.history.snapshots.values())
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  getMemoryUsage(): number {
    return this.memoryUsage;
  }

  getPerformanceReport() {
    return {
      memoryUsage: this.memoryUsage,
      snapshotCount: this.history.snapshots.size,
      branchCount: this.history.branches.size,
      compressionRatio: this.calculateAverageCompressionRatio(),
      operationCount: this.pendingOperations.length
    };
  }

  private calculateAverageCompressionRatio(): number {
    const compressedSnapshots = Array.from(this.history.snapshots.values())
      .filter(s => s.compressed);
    
    if (compressedSnapshots.length === 0) return 1;

    const totalRatio = compressedSnapshots.reduce((sum, snapshot) => {
      // Estimate original size from compressed size
      return sum + 0.3; // Assume 30% compression ratio on average
    }, 0);

    return totalRatio / compressedSnapshots.length;
  }

  // Cleanup
  dispose(): void {
    clearTimeout((this as any).captureTimeout);
    this.history.snapshots.clear();
    this.history.branches.clear();
    this.pendingOperations = [];
    this.removeAllListeners();
  }
}