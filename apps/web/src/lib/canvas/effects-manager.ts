import { EventEmitter } from 'events';
import { FilterPipelineEngine, FilterInstance, BlendMode } from './filter-pipeline-engine';
import { FilterLibrary, FilterPreset } from './filter-library';

// Layer-based Effects System
export interface EffectLayer {
  id: string;
  name: string;
  enabled: boolean;
  opacity: number;
  blendMode: BlendMode;
  filters: FilterInstance[];
  mask?: EffectMask;
  animationProps?: EffectAnimation;
  groupId?: string;
  locked: boolean;
  solo: boolean;
  order: number;
  bounds?: EffectBounds;
  metadata: {
    createdAt: number;
    updatedAt: number;
    author?: string;
    tags: string[];
    description?: string;
  };
}

export interface EffectMask {
  id: string;
  type: 'alpha' | 'luminance' | 'vector' | 'gradient';
  data: MaskData;
  inverted: boolean;
  feather: number;
  density: number;
  enabled: boolean;
}

export interface MaskData {
  // For alpha/luminance masks
  imageData?: ImageData;
  
  // For vector masks
  path?: string; // SVG path
  
  // For gradient masks
  gradient?: {
    type: 'linear' | 'radial';
    start: { x: number; y: number };
    end: { x: number; y: number };
    stops: Array<{ offset: number; color: string; alpha: number }>;
  };
}

export interface EffectAnimation {
  enabled: boolean;
  duration: number; // in milliseconds
  loop: boolean;
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-bezier';
  keyframes: EffectKeyframe[];
  currentTime: number;
  playing: boolean;
}

export interface EffectKeyframe {
  time: number; // 0-1 normalized time
  properties: Record<string, any>;
  easing?: string;
}

export interface EffectBounds {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
}

export interface EffectGroup {
  id: string;
  name: string;
  layers: string[];
  collapsed: boolean;
  enabled: boolean;
  opacity: number;
  blendMode: BlendMode;
  locked: boolean;
}

export interface EffectPreset {
  id: string;
  name: string;
  description: string;
  thumbnail?: string;
  tags: string[];
  layers: Omit<EffectLayer, 'id' | 'metadata'>[];
  parameters: Record<string, any>;
}

export interface EffectStackState {
  layers: Map<string, EffectLayer>;
  groups: Map<string, EffectGroup>;
  renderOrder: string[];
  version: number;
  globalOpacity: number;
  globalBlendMode: BlendMode;
}

export interface EffectProcessingOptions {
  enableGPUAcceleration: boolean;
  useFloatPrecision: boolean;
  enableMultisampling: boolean;
  tileRendering: boolean;
  preserveAlpha: boolean;
  outputFormat: 'rgba' | 'rgb' | 'alpha';
}

export interface EffectPerformanceMetrics {
  layerCount: number;
  activeEffectCount: number;
  renderTime: number;
  memoryUsage: number;
  gpuMemoryUsage: number;
  frameRate: number;
  bottlenecks: string[];
}

// Effect Animation System
export class EffectAnimationController {
  private animations: Map<string, EffectAnimation> = new Map();
  private animationFrameId?: number;
  private startTime: number = 0;
  private isPlaying: boolean = false;

  startAnimation(layerId: string, animation: EffectAnimation): void {
    this.animations.set(layerId, { ...animation, playing: true, currentTime: 0 });
    
    if (!this.isPlaying) {
      this.isPlaying = true;
      this.startTime = performance.now();
      this.animate();
    }
  }

  stopAnimation(layerId: string): void {
    const animation = this.animations.get(layerId);
    if (animation) {
      animation.playing = false;
    }
  }

  pauseAnimation(layerId: string): void {
    const animation = this.animations.get(layerId);
    if (animation) {
      animation.playing = false;
    }
  }

  resumeAnimation(layerId: string): void {
    const animation = this.animations.get(layerId);
    if (animation) {
      animation.playing = true;
      if (!this.isPlaying) {
        this.isPlaying = true;
        this.animate();
      }
    }
  }

  private animate = (): void => {
    const currentTime = performance.now();
    let anyPlaying = false;

    this.animations.forEach((animation, layerId) => {
      if (!animation.playing) return;

      const elapsed = currentTime - this.startTime;
      const normalizedTime = (elapsed % animation.duration) / animation.duration;
      
      animation.currentTime = normalizedTime;
      
      // Calculate interpolated properties
      const interpolatedProps = this.interpolateKeyframes(animation.keyframes, normalizedTime);
      
      // Emit update event
      this.emit('animation:update', { layerId, properties: interpolatedProps, time: normalizedTime });
      
      anyPlaying = true;
      
      // Check if animation should stop
      if (!animation.loop && normalizedTime >= 1.0) {
        animation.playing = false;
        this.emit('animation:complete', { layerId });
      }
    });

    if (anyPlaying) {
      this.animationFrameId = requestAnimationFrame(this.animate);
    } else {
      this.isPlaying = false;
    }
  };

  private interpolateKeyframes(keyframes: EffectKeyframe[], time: number): Record<string, any> {
    if (keyframes.length === 0) return {};
    if (keyframes.length === 1) return keyframes[0].properties;

    // Find surrounding keyframes
    let startFrame: EffectKeyframe | undefined;
    let endFrame: EffectKeyframe | undefined;

    for (let i = 0; i < keyframes.length - 1; i++) {
      if (time >= keyframes[i].time && time <= keyframes[i + 1].time) {
        startFrame = keyframes[i];
        endFrame = keyframes[i + 1];
        break;
      }
    }

    if (!startFrame || !endFrame) {
      return keyframes[keyframes.length - 1].properties;
    }

    // Interpolate between keyframes
    const t = (time - startFrame.time) / (endFrame.time - startFrame.time);
    const easedT = this.applyEasing(t, endFrame.easing || 'linear');

    const result: Record<string, any> = {};
    Object.keys(startFrame.properties).forEach(key => {
      const startValue = startFrame!.properties[key];
      const endValue = endFrame!.properties[key];

      if (typeof startValue === 'number' && typeof endValue === 'number') {
        result[key] = startValue + (endValue - startValue) * easedT;
      } else if (Array.isArray(startValue) && Array.isArray(endValue)) {
        result[key] = startValue.map((val, index) => 
          val + (endValue[index] - val) * easedT
        );
      } else {
        result[key] = easedT < 0.5 ? startValue : endValue;
      }
    });

    return result;
  }

  private applyEasing(t: number, easing: string): number {
    switch (easing) {
      case 'ease-in':
        return t * t;
      case 'ease-out':
        return 1 - (1 - t) * (1 - t);
      case 'ease-in-out':
        return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
      case 'linear':
      default:
        return t;
    }
  }

  dispose(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }
    this.animations.clear();
    this.isPlaying = false;
  }

  // EventEmitter methods
  emit(event: string, data: any): boolean {
    // This would be implemented if extending EventEmitter
    return true;
  }
}

// Main Effects Manager
export class EffectsManager extends EventEmitter {
  private pipelineEngine: FilterPipelineEngine;
  private filterLibrary: FilterLibrary;
  private effectStack: EffectStackState;
  private animationController: EffectAnimationController;
  private processingOptions: EffectProcessingOptions;
  private performanceMetrics: EffectPerformanceMetrics;
  
  // Caching and optimization
  private layerCache: Map<string, WebGLTexture> = new Map();
  private presetCache: Map<string, EffectPreset> = new Map();
  private renderCache: Map<string, { texture: WebGLTexture; version: number }> = new Map();

  constructor(
    pipelineEngine: FilterPipelineEngine,
    options: Partial<EffectProcessingOptions> = {}
  ) {
    super();
    
    this.pipelineEngine = pipelineEngine;
    this.filterLibrary = FilterLibrary.getInstance();
    this.animationController = new EffectAnimationController();
    
    this.processingOptions = {
      enableGPUAcceleration: true,
      useFloatPrecision: true,
      enableMultisampling: false,
      tileRendering: false,
      preserveAlpha: true,
      outputFormat: 'rgba',
      ...options
    };

    this.effectStack = this.initializeEffectStack();
    this.performanceMetrics = this.initializePerformanceMetrics();
    
    this.setupEventHandlers();
  }

  private initializeEffectStack(): EffectStackState {
    return {
      layers: new Map(),
      groups: new Map(),
      renderOrder: [],
      version: 1,
      globalOpacity: 1.0,
      globalBlendMode: 'normal'
    };
  }

  private initializePerformanceMetrics(): EffectPerformanceMetrics {
    return {
      layerCount: 0,
      activeEffectCount: 0,
      renderTime: 0,
      memoryUsage: 0,
      gpuMemoryUsage: 0,
      frameRate: 60,
      bottlenecks: []
    };
  }

  private setupEventHandlers(): void {
    // Listen to pipeline events
    this.pipelineEngine.on('pipeline:processed', (data) => {
      this.updatePerformanceMetrics(data);
    });

    // Listen to animation events
    this.animationController.on('animation:update', (data) => {
      this.handleAnimationUpdate(data);
    });
  }

  // Layer Management
  createEffectLayer(name: string, options: Partial<EffectLayer> = {}): EffectLayer {
    const layer: EffectLayer = {
      id: this.generateId(),
      name,
      enabled: true,
      opacity: 1.0,
      blendMode: 'normal',
      filters: [],
      locked: false,
      solo: false,
      order: this.effectStack.layers.size,
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        tags: [],
        ...options.metadata
      },
      ...options
    };

    this.effectStack.layers.set(layer.id, layer);
    this.updateRenderOrder();
    this.incrementVersion();

    this.emit('layer:created', layer);
    return layer;
  }

  deleteEffectLayer(layerId: string): boolean {
    const layer = this.effectStack.layers.get(layerId);
    if (!layer) return false;

    // Remove from any groups
    this.effectStack.groups.forEach(group => {
      group.layers = group.layers.filter(id => id !== layerId);
    });

    // Clean up cache
    this.clearLayerCache(layerId);

    // Remove layer
    this.effectStack.layers.delete(layerId);
    this.updateRenderOrder();
    this.incrementVersion();

    this.emit('layer:deleted', { layerId, layer });
    return true;
  }

  updateEffectLayer(layerId: string, updates: Partial<EffectLayer>): boolean {
    const layer = this.effectStack.layers.get(layerId);
    if (!layer) return false;

    // Update layer properties
    Object.assign(layer, updates);
    layer.metadata.updatedAt = Date.now();

    // Invalidate cache if visual properties changed
    if (this.isVisualUpdate(updates)) {
      this.clearLayerCache(layerId);
    }

    this.incrementVersion();
    this.emit('layer:updated', layer);
    return true;
  }

  duplicateEffectLayer(layerId: string): EffectLayer | null {
    const original = this.effectStack.layers.get(layerId);
    if (!original) return null;

    const duplicate: EffectLayer = {
      ...original,
      id: this.generateId(),
      name: `${original.name} Copy`,
      order: original.order + 1,
      metadata: {
        ...original.metadata,
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      filters: original.filters.map(filter => ({ ...filter, id: this.generateId() }))
    };

    this.effectStack.layers.set(duplicate.id, duplicate);
    this.updateRenderOrder();
    this.incrementVersion();

    this.emit('layer:duplicated', { originalId: layerId, duplicate });
    return duplicate;
  }

  // Filter Management
  addFilterToLayer(layerId: string, filterId: string, parameters: Record<string, any> = {}): FilterInstance | null {
    const layer = this.effectStack.layers.get(layerId);
    const filterShader = this.filterLibrary.getFilterById(filterId);
    
    if (!layer || !filterShader) return null;

    const filterInstance: FilterInstance = {
      id: this.generateId(),
      shaderId: filterId,
      name: filterShader.name,
      enabled: true,
      opacity: 1.0,
      blendMode: 'normal',
      parameters: { ...this.getDefaultParameters(filterShader), ...parameters }
    };

    layer.filters.push(filterInstance);
    layer.metadata.updatedAt = Date.now();
    this.clearLayerCache(layerId);
    this.incrementVersion();

    this.emit('filter:added', { layerId, filter: filterInstance });
    return filterInstance;
  }

  removeFilterFromLayer(layerId: string, filterId: string): boolean {
    const layer = this.effectStack.layers.get(layerId);
    if (!layer) return false;

    const initialLength = layer.filters.length;
    layer.filters = layer.filters.filter(filter => filter.id !== filterId);
    
    if (layer.filters.length === initialLength) return false;

    layer.metadata.updatedAt = Date.now();
    this.clearLayerCache(layerId);
    this.incrementVersion();

    this.emit('filter:removed', { layerId, filterId });
    return true;
  }

  updateFilterParameters(layerId: string, filterId: string, parameters: Record<string, any>): boolean {
    const layer = this.effectStack.layers.get(layerId);
    if (!layer) return false;

    const filter = layer.filters.find(f => f.id === filterId);
    if (!filter) return false;

    Object.assign(filter.parameters, parameters);
    layer.metadata.updatedAt = Date.now();
    this.clearLayerCache(layerId);
    this.incrementVersion();

    this.emit('filter:updated', { layerId, filterId, parameters });
    return true;
  }

  // Group Management
  createEffectGroup(name: string, layerIds: string[] = []): EffectGroup {
    const group: EffectGroup = {
      id: this.generateId(),
      name,
      layers: [...layerIds],
      collapsed: false,
      enabled: true,
      opacity: 1.0,
      blendMode: 'normal',
      locked: false
    };

    this.effectStack.groups.set(group.id, group);
    this.incrementVersion();

    this.emit('group:created', group);
    return group;
  }

  addLayerToGroup(groupId: string, layerId: string): boolean {
    const group = this.effectStack.groups.get(groupId);
    if (!group || group.layers.includes(layerId)) return false;

    group.layers.push(layerId);
    this.incrementVersion();

    this.emit('group:layer:added', { groupId, layerId });
    return true;
  }

  removeLayerFromGroup(groupId: string, layerId: string): boolean {
    const group = this.effectStack.groups.get(groupId);
    if (!group) return false;

    const initialLength = group.layers.length;
    group.layers = group.layers.filter(id => id !== layerId);
    
    if (group.layers.length === initialLength) return false;

    this.incrementVersion();
    this.emit('group:layer:removed', { groupId, layerId });
    return true;
  }

  // Animation Management
  createLayerAnimation(layerId: string, animation: Omit<EffectAnimation, 'currentTime' | 'playing'>): boolean {
    const layer = this.effectStack.layers.get(layerId);
    if (!layer) return false;

    layer.animationProps = {
      ...animation,
      currentTime: 0,
      playing: false
    };

    this.emit('animation:created', { layerId, animation: layer.animationProps });
    return true;
  }

  startLayerAnimation(layerId: string): boolean {
    const layer = this.effectStack.layers.get(layerId);
    if (!layer || !layer.animationProps) return false;

    this.animationController.startAnimation(layerId, layer.animationProps);
    this.emit('animation:started', { layerId });
    return true;
  }

  stopLayerAnimation(layerId: string): boolean {
    const layer = this.effectStack.layers.get(layerId);
    if (!layer || !layer.animationProps) return false;

    this.animationController.stopAnimation(layerId);
    this.emit('animation:stopped', { layerId });
    return true;
  }

  // Processing Pipeline
  processEffects(sourceTexture: WebGLTexture): WebGLTexture {
    const startTime = performance.now();
    
    // Get enabled layers in render order
    const enabledLayers = this.getEnabledLayers();
    
    if (enabledLayers.length === 0) {
      return sourceTexture;
    }

    let currentTexture = sourceTexture;
    let processedCount = 0;

    for (const layer of enabledLayers) {
      if (!layer.enabled || layer.solo && !this.hasSoloLayers()) continue;

      // Check cache first
      const cacheKey = this.generateCacheKey(layer);
      const cached = this.renderCache.get(cacheKey);
      
      if (cached && cached.version === this.effectStack.version) {
        currentTexture = cached.texture;
        continue;
      }

      // Process layer
      currentTexture = this.processLayer(layer, currentTexture);
      processedCount++;

      // Update cache
      this.renderCache.set(cacheKey, {
        texture: currentTexture,
        version: this.effectStack.version
      });
    }

    // Update performance metrics
    const endTime = performance.now();
    this.performanceMetrics.renderTime = endTime - startTime;
    this.performanceMetrics.activeEffectCount = processedCount;

    this.emit('effects:processed', {
      layerCount: enabledLayers.length,
      processedCount,
      renderTime: this.performanceMetrics.renderTime
    });

    return currentTexture;
  }

  private processLayer(layer: EffectLayer, inputTexture: WebGLTexture): WebGLTexture {
    if (layer.filters.length === 0) {
      return inputTexture;
    }

    // Add filters to pipeline
    layer.filters.forEach(filter => {
      if (filter.enabled) {
        this.pipelineEngine.addFilter(filter);
      }
    });

    // Process with pipeline
    const result = this.pipelineEngine.process(inputTexture, this.processingOptions);

    // Clear filters from pipeline
    this.pipelineEngine.clearFilters();

    return result;
  }

  // Preset Management
  saveEffectPreset(name: string, description: string, tags: string[] = []): EffectPreset {
    const preset: EffectPreset = {
      id: this.generateId(),
      name,
      description,
      tags,
      layers: Array.from(this.effectStack.layers.values()).map(layer => {
        const { id, metadata, ...layerData } = layer;
        return layerData;
      }),
      parameters: this.extractGlobalParameters()
    };

    this.presetCache.set(preset.id, preset);
    this.emit('preset:saved', preset);
    return preset;
  }

  loadEffectPreset(presetId: string): boolean {
    const preset = this.presetCache.get(presetId);
    if (!preset) return false;

    // Clear current stack
    this.clearEffectStack();

    // Load preset layers
    preset.layers.forEach((layerData, index) => {
      const layer: EffectLayer = {
        ...layerData,
        id: this.generateId(),
        order: index,
        metadata: {
          createdAt: Date.now(),
          updatedAt: Date.now(),
          tags: []
        }
      };

      this.effectStack.layers.set(layer.id, layer);
    });

    // Apply global parameters
    Object.assign(this.effectStack, preset.parameters);

    this.updateRenderOrder();
    this.incrementVersion();

    this.emit('preset:loaded', { presetId, preset });
    return true;
  }

  // Utility Methods
  private getEnabledLayers(): EffectLayer[] {
    return this.effectStack.renderOrder
      .map(id => this.effectStack.layers.get(id))
      .filter((layer): layer is EffectLayer => layer !== undefined && layer.enabled);
  }

  private hasSoloLayers(): boolean {
    return Array.from(this.effectStack.layers.values()).some(layer => layer.solo);
  }

  private updateRenderOrder(): void {
    this.effectStack.renderOrder = Array.from(this.effectStack.layers.values())
      .sort((a, b) => a.order - b.order)
      .map(layer => layer.id);
  }

  private incrementVersion(): void {
    this.effectStack.version++;
  }

  private isVisualUpdate(updates: Partial<EffectLayer>): boolean {
    const visualProps = ['enabled', 'opacity', 'blendMode', 'filters', 'mask'];
    return Object.keys(updates).some(key => visualProps.includes(key));
  }

  private clearLayerCache(layerId: string): void {
    const keysToDelete: string[] = [];
    this.renderCache.forEach((_, key) => {
      if (key.includes(layerId)) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach(key => this.renderCache.delete(key));
  }

  private generateCacheKey(layer: EffectLayer): string {
    return `${layer.id}_${layer.metadata.updatedAt}_${this.effectStack.version}`;
  }

  private getDefaultParameters(filterShader: any): Record<string, any> {
    const defaults: Record<string, any> = {};
    Object.entries(filterShader.uniforms).forEach(([name, uniform]: [string, any]) => {
      defaults[name] = uniform.value;
    });
    return defaults;
  }

  private extractGlobalParameters(): Record<string, any> {
    return {
      globalOpacity: this.effectStack.globalOpacity,
      globalBlendMode: this.effectStack.globalBlendMode
    };
  }

  private clearEffectStack(): void {
    this.effectStack.layers.clear();
    this.effectStack.groups.clear();
    this.effectStack.renderOrder = [];
    this.renderCache.clear();
    this.layerCache.clear();
  }

  private updatePerformanceMetrics(data: any): void {
    this.performanceMetrics.layerCount = this.effectStack.layers.size;
    this.performanceMetrics.renderTime = data.processingTime || 0;
    this.performanceMetrics.frameRate = 1000 / (this.performanceMetrics.renderTime || 16.67);
  }

  private handleAnimationUpdate(data: { layerId: string; properties: Record<string, any> }): void {
    this.updateFilterParameters(data.layerId, 'animated', data.properties);
  }

  private generateId(): string {
    return `effect_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public API
  getEffectStack(): EffectStackState {
    return { ...this.effectStack };
  }

  getLayer(layerId: string): EffectLayer | undefined {
    return this.effectStack.layers.get(layerId);
  }

  getAllLayers(): EffectLayer[] {
    return Array.from(this.effectStack.layers.values());
  }

  getGroup(groupId: string): EffectGroup | undefined {
    return this.effectStack.groups.get(groupId);
  }

  getAllGroups(): EffectGroup[] {
    return Array.from(this.effectStack.groups.values());
  }

  getPerformanceMetrics(): EffectPerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  setProcessingOptions(options: Partial<EffectProcessingOptions>): void {
    Object.assign(this.processingOptions, options);
    this.emit('options:updated', this.processingOptions);
  }

  // Cleanup
  dispose(): void {
    this.animationController.dispose();
    this.clearEffectStack();
    this.presetCache.clear();
    this.removeAllListeners();
  }
}