import { fabric } from 'fabric';
import * as PIXI from 'pixi.js';
import { EventEmitter } from 'events';
import { LayerNode, LayerType, BlendMode, AdvancedLayerManager } from './advanced-layer-manager';
import { HybridCanvasManager } from './pixi-performance-layer';

// Types for hybrid integration
export interface HybridLayerConfig {
  preferPIXI: boolean;
  autoRoute: boolean;
  performanceThreshold: number;
  pixiComplexityThreshold: number;
  fabricInteractionThreshold: number;
}

export interface LayerRenderContext {
  renderEngine: 'fabric' | 'pixi';
  performanceScore: number;
  isInteractive: boolean;
  hasComplexEffects: boolean;
  isAnimated: boolean;
  objectCount: number;
}

export interface LayerSyncState {
  fabricVersion: number;
  pixiVersion: number;
  lastSync: number;
  isDirty: boolean;
  syncDirection: 'fabric-to-pixi' | 'pixi-to-fabric' | 'bidirectional';
}

export interface HybridRenderPipeline {
  fabricLayers: Set<string>;
  pixiLayers: Set<string>;
  syncQueue: Map<string, LayerSyncState>;
  renderOrder: { layerId: string; engine: 'fabric' | 'pixi' }[];
}

// Layer render strategy analyzer
export class LayerRenderAnalyzer {
  static analyzeLayer(layer: LayerNode, fabricObject?: fabric.Object): LayerRenderContext {
    let performanceScore = 0;
    let isInteractive = false;
    let hasComplexEffects = false;
    let isAnimated = false;
    let objectCount = 1;

    // Analyze layer type complexity
    switch (layer.type) {
      case 'image':
      case 'shape':
        performanceScore += 30; // PIXI.js better for these
        break;
      case 'text':
        performanceScore += 10; // Fabric.js generally better for text editing
        isInteractive = true;
        break;
      case 'group':
        objectCount = layer.children.length;
        performanceScore += objectCount > 10 ? 40 : 20;
        break;
      default:
        performanceScore += 15;
    }

    // Analyze effects complexity
    if (layer.effects.length > 0) {
      hasComplexEffects = true;
      performanceScore += layer.effects.length * 15;
      
      // Complex effects benefit from GPU acceleration
      const complexEffects = ['outer-glow', 'inner-glow', 'gradient-overlay', 'pattern-overlay'];
      if (layer.effects.some(e => complexEffects.includes(e.type))) {
        performanceScore += 25;
      }
    }

    // Analyze blend modes
    if (layer.blendMode !== 'normal') {
      performanceScore += 20;
      hasComplexEffects = true;
    }

    // Analyze interaction requirements
    if (fabricObject) {
      isInteractive = fabricObject.selectable !== false || fabricObject.evented !== false;
      
      // Check if object has complex paths or many points
      if (fabricObject.type === 'path' && (fabricObject as any).path) {
        const pathLength = (fabricObject as any).path.length;
        if (pathLength > 100) {
          performanceScore += 30;
        }
      }
      
      // Check for animation potential
      isAnimated = !!(fabricObject as any).isMoving || !!(fabricObject as any).isScaling;
    }

    // Determine optimal render engine
    const renderEngine: 'fabric' | 'pixi' = this.determineOptimalEngine(
      performanceScore,
      isInteractive,
      hasComplexEffects,
      layer.type
    );

    return {
      renderEngine,
      performanceScore,
      isInteractive,
      hasComplexEffects,
      isAnimated,
      objectCount
    };
  }

  private static determineOptimalEngine(
    performanceScore: number,
    isInteractive: boolean,
    hasComplexEffects: boolean,
    layerType: LayerType
  ): 'fabric' | 'pixi' {
    // Interactive text should generally stay in Fabric.js
    if (layerType === 'text' && isInteractive) {
      return 'fabric';
    }

    // Complex effects benefit from PIXI.js GPU acceleration
    if (hasComplexEffects && performanceScore > 50) {
      return 'pixi';
    }

    // High performance score suggests PIXI.js
    if (performanceScore > 60) {
      return 'pixi';
    }

    // Interactive elements benefit from Fabric.js
    if (isInteractive && performanceScore < 40) {
      return 'fabric';
    }

    // Default to PIXI.js for performance
    return performanceScore > 30 ? 'pixi' : 'fabric';
  }
}

// Hybrid layer synchronization system
export class LayerHybridSynchronizer extends EventEmitter {
  private fabricCanvas: fabric.Canvas;
  private pixiApp: PIXI.Application;
  private layerManager: AdvancedLayerManager;
  private syncQueue: Map<string, LayerSyncState> = new Map();
  private renderPipeline: HybridRenderPipeline;
  private pixiLayerContainer: PIXI.Container;
  private fabricLayerMap: Map<string, fabric.Object> = new Map();
  private pixiLayerMap: Map<string, PIXI.DisplayObject> = new Map();

  constructor(
    fabricCanvas: fabric.Canvas,
    pixiApp: PIXI.Application,
    layerManager: AdvancedLayerManager
  ) {
    super();
    this.fabricCanvas = fabricCanvas;
    this.pixiApp = pixiApp;
    this.layerManager = layerManager;
    
    this.renderPipeline = {
      fabricLayers: new Set(),
      pixiLayers: new Set(),
      syncQueue: new Map(),
      renderOrder: []
    };

    // Create dedicated container for hybrid layers in PIXI
    this.pixiLayerContainer = new PIXI.Container();
    this.pixiLayerContainer.name = 'hybrid-layers';
    this.pixiApp.stage.addChild(this.pixiLayerContainer);

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Layer manager events
    this.layerManager.on('layer:created', (layer: LayerNode) => {
      this.routeLayer(layer);
    });

    this.layerManager.on('layer:property:changed', ({ layerId, property }: any) => {
      this.markLayerForSync(layerId, 'fabric-to-pixi');
    });

    this.layerManager.on('layer:effect:added', ({ layerId }: any) => {
      this.reassessLayerRouting(layerId);
    });

    this.layerManager.on('layer:moved', ({ layerId }: any) => {
      this.updateRenderOrder();
    });

    // Fabric.js events
    this.fabricCanvas.on('object:modified', (e) => {
      const object = e.target as fabric.Object;
      if (object && (object as any).layerId) {
        this.markLayerForSync((object as any).layerId, 'fabric-to-pixi');
      }
    });

    this.fabricCanvas.on('object:moving', (e) => {
      const object = e.target as fabric.Object;
      if (object && (object as any).layerId) {
        this.queueRealtimeSync((object as any).layerId);
      }
    });

    // PIXI.js events (for interactive PIXI objects)
    this.pixiApp.stage.interactive = true;
    this.pixiApp.stage.on('pointermove', () => {
      this.processRealtimeSyncs();
    });
  }

  // Route layer to optimal rendering engine
  routeLayer(layer: LayerNode): 'fabric' | 'pixi' {
    const fabricObject = this.layerManager.getFabricObject(layer.id);
    const context = LayerRenderAnalyzer.analyzeLayer(layer, fabricObject);
    
    if (context.renderEngine === 'pixi') {
      this.moveLayerToPIXI(layer.id);
    } else {
      this.moveLayerToFabric(layer.id);
    }

    this.updateRenderOrder();
    this.emit('layer:routed', { layerId: layer.id, engine: context.renderEngine, context });
    
    return context.renderEngine;
  }

  // Move layer from Fabric.js to PIXI.js
  private moveLayerToPIXI(layerId: string): boolean {
    const layer = this.layerManager.getLayer(layerId);
    const fabricObject = this.layerManager.getFabricObject(layerId);
    
    if (!layer || !fabricObject) return false;

    // Create PIXI equivalent
    const pixiObject = this.createPIXIFromFabric(layer, fabricObject);
    if (!pixiObject) return false;

    // Store mapping
    this.pixiLayerMap.set(layerId, pixiObject);
    this.pixiLayerContainer.addChild(pixiObject);

    // Update pipeline
    this.renderPipeline.fabricLayers.delete(layerId);
    this.renderPipeline.pixiLayers.add(layerId);

    // Hide fabric object but keep for reference
    fabricObject.set({ visible: false });
    this.fabricCanvas.renderAll();

    this.emit('layer:moved:to:pixi', { layerId, layer, pixiObject });
    return true;
  }

  // Move layer from PIXI.js to Fabric.js
  private moveLayerToFabric(layerId: string): boolean {
    const layer = this.layerManager.getLayer(layerId);
    const pixiObject = this.pixiLayerMap.get(layerId);
    
    if (!layer || !pixiObject) return false;

    // Sync PIXI state back to fabric object
    const fabricObject = this.layerManager.getFabricObject(layerId);
    if (fabricObject) {
      this.syncPIXIToFabric(layer, pixiObject, fabricObject);
      fabricObject.set({ visible: layer.visible });
    }

    // Remove from PIXI
    this.pixiLayerContainer.removeChild(pixiObject);
    this.pixiLayerMap.delete(layerId);

    // Update pipeline
    this.renderPipeline.pixiLayers.delete(layerId);
    this.renderPipeline.fabricLayers.add(layerId);

    this.fabricCanvas.renderAll();
    this.emit('layer:moved:to:fabric', { layerId, layer, fabricObject });
    return true;
  }

  // Create PIXI object from Fabric object
  private createPIXIFromFabric(layer: LayerNode, fabricObject: fabric.Object): PIXI.DisplayObject | null {
    switch (layer.type) {
      case 'image':
        return this.createPIXISprite(layer, fabricObject);
      case 'shape':
        return this.createPIXIGraphics(layer, fabricObject);
      case 'text':
        return this.createPIXIText(layer, fabricObject);
      case 'group':
        return this.createPIXIContainer(layer, fabricObject);
      default:
        return this.createPIXIGraphics(layer, fabricObject);
    }
  }

  private createPIXISprite(layer: LayerNode, fabricObject: fabric.Object): PIXI.Sprite | null {
    if (fabricObject.type !== 'image') return null;

    const image = fabricObject as fabric.Image;
    if (!image.getElement()) return null;

    const texture = PIXI.Texture.from(image.getElement() as HTMLImageElement);
    const sprite = new PIXI.Sprite(texture);

    this.applyCommonPIXIProperties(sprite, layer, fabricObject);
    
    return sprite;
  }

  private createPIXIGraphics(layer: LayerNode, fabricObject: fabric.Object): PIXI.Graphics {
    const graphics = new PIXI.Graphics();

    // Convert Fabric.js shape to PIXI.js graphics
    switch (fabricObject.type) {
      case 'rect':
        const rect = fabricObject as fabric.Rect;
        graphics.beginFill(this.parseColor(rect.fill as string));
        graphics.drawRect(0, 0, rect.width || 100, rect.height || 100);
        break;
        
      case 'circle':
        const circle = fabricObject as fabric.Circle;
        graphics.beginFill(this.parseColor(circle.fill as string));
        graphics.drawCircle(0, 0, circle.radius || 50);
        break;
        
      case 'path':
        // Complex path conversion would go here
        graphics.beginFill(0x3b82f6);
        graphics.drawRect(0, 0, 100, 100);
        break;
        
      default:
        graphics.beginFill(0x6b7280);
        graphics.drawRect(0, 0, fabricObject.width || 100, fabricObject.height || 100);
    }

    graphics.endFill();
    this.applyCommonPIXIProperties(graphics, layer, fabricObject);
    
    return graphics;
  }

  private createPIXIText(layer: LayerNode, fabricObject: fabric.Object): PIXI.Text {
    const text = fabricObject as fabric.Text;
    
    const style = new PIXI.TextStyle({
      fontFamily: text.fontFamily || 'Inter',
      fontSize: text.fontSize || 20,
      fill: this.parseColor(text.fill as string) || '#000000',
      align: text.textAlign || 'left',
      fontWeight: text.fontWeight || 'normal',
      fontStyle: text.fontStyle || 'normal'
    });

    const pixiText = new PIXI.Text(text.text || '', style);
    this.applyCommonPIXIProperties(pixiText, layer, fabricObject);
    
    return pixiText;
  }

  private createPIXIContainer(layer: LayerNode, fabricObject: fabric.Object): PIXI.Container {
    const container = new PIXI.Container();
    
    // Add child layers to container
    layer.children.forEach(childId => {
      const childLayer = this.layerManager.getLayer(childId);
      const childFabric = this.layerManager.getFabricObject(childId);
      
      if (childLayer && childFabric) {
        const childPIXI = this.createPIXIFromFabric(childLayer, childFabric);
        if (childPIXI) {
          container.addChild(childPIXI);
        }
      }
    });

    this.applyCommonPIXIProperties(container, layer, fabricObject);
    return container;
  }

  private applyCommonPIXIProperties(
    pixiObject: PIXI.DisplayObject,
    layer: LayerNode,
    fabricObject: fabric.Object
  ): void {
    // Position and transform
    pixiObject.x = fabricObject.left || 0;
    pixiObject.y = fabricObject.top || 0;
    pixiObject.scale.x = fabricObject.scaleX || 1;
    pixiObject.scale.y = fabricObject.scaleY || 1;
    pixiObject.rotation = (fabricObject.angle || 0) * (Math.PI / 180);
    pixiObject.alpha = layer.opacity;
    pixiObject.visible = layer.visible;

    // Apply blend mode
    pixiObject.blendMode = this.convertBlendMode(layer.blendMode);

    // Store layer reference
    (pixiObject as any).layerId = layer.id;

    // Apply effects
    this.applyPIXIEffects(pixiObject, layer);
  }

  private applyPIXIEffects(pixiObject: PIXI.DisplayObject, layer: LayerNode): void {
    const filters: PIXI.Filter[] = [];

    layer.effects.forEach(effect => {
      if (!effect.enabled) return;

      switch (effect.type) {
        case 'drop-shadow':
          const dropShadowFilter = new PIXI.DropShadowFilter({
            color: this.parseColor(effect.parameters.color) || 0x000000,
            alpha: effect.parameters.alpha || 0.5,
            blur: effect.parameters.blur || 4,
            distance: effect.parameters.distance || 5,
            angle: (effect.parameters.angle || 45) * (Math.PI / 180)
          });
          filters.push(dropShadowFilter);
          break;

        case 'outer-glow':
          const glowFilter = new PIXI.GlowFilter({
            color: this.parseColor(effect.parameters.color) || 0xffffff,
            alpha: effect.parameters.alpha || 0.8,
            blur: effect.parameters.size || 8,
            quality: effect.parameters.quality || 0.5
          });
          filters.push(glowFilter);
          break;

        case 'color-overlay':
          const colorMatrix = new PIXI.ColorMatrixFilter();
          // Apply color overlay effect
          filters.push(colorMatrix);
          break;
      }
    });

    if (filters.length > 0) {
      pixiObject.filters = filters;
    }
  }

  // Synchronization methods
  private markLayerForSync(layerId: string, direction: LayerSyncState['syncDirection']): void {
    const syncState: LayerSyncState = {
      fabricVersion: Date.now(),
      pixiVersion: Date.now(),
      lastSync: Date.now(),
      isDirty: true,
      syncDirection: direction
    };

    this.syncQueue.set(layerId, syncState);
    this.scheduleSync();
  }

  private queueRealtimeSync(layerId: string): void {
    // For real-time syncing during interactions
    this.markLayerForSync(layerId, 'bidirectional');
  }

  private scheduleSync(): void {
    requestAnimationFrame(() => {
      this.processSyncQueue();
    });
  }

  private processSyncQueue(): void {
    this.syncQueue.forEach((syncState, layerId) => {
      if (syncState.isDirty) {
        this.syncLayer(layerId, syncState);
        syncState.isDirty = false;
        syncState.lastSync = Date.now();
      }
    });
  }

  private processRealtimeSyncs(): void {
    // Process only layers that need real-time sync
    this.syncQueue.forEach((syncState, layerId) => {
      if (syncState.syncDirection === 'bidirectional' && syncState.isDirty) {
        this.syncLayer(layerId, syncState);
        syncState.isDirty = false;
      }
    });
  }

  private syncLayer(layerId: string, syncState: LayerSyncState): void {
    const layer = this.layerManager.getLayer(layerId);
    if (!layer) return;

    const fabricObject = this.layerManager.getFabricObject(layerId);
    const pixiObject = this.pixiLayerMap.get(layerId);

    if (this.renderPipeline.pixiLayers.has(layerId) && fabricObject && pixiObject) {
      // Sync from Fabric to PIXI
      if (syncState.syncDirection === 'fabric-to-pixi' || syncState.syncDirection === 'bidirectional') {
        this.syncFabricToPIXI(layer, fabricObject, pixiObject);
      }
    }

    if (this.renderPipeline.fabricLayers.has(layerId) && fabricObject && pixiObject) {
      // Sync from PIXI to Fabric
      if (syncState.syncDirection === 'pixi-to-fabric' || syncState.syncDirection === 'bidirectional') {
        this.syncPIXIToFabric(layer, pixiObject, fabricObject);
      }
    }
  }

  private syncFabricToPIXI(layer: LayerNode, fabricObject: fabric.Object, pixiObject: PIXI.DisplayObject): void {
    // Sync transform
    pixiObject.x = fabricObject.left || 0;
    pixiObject.y = fabricObject.top || 0;
    pixiObject.scale.x = fabricObject.scaleX || 1;
    pixiObject.scale.y = fabricObject.scaleY || 1;
    pixiObject.rotation = (fabricObject.angle || 0) * (Math.PI / 180);
    
    // Sync visibility and opacity
    pixiObject.alpha = fabricObject.opacity || 1;
    pixiObject.visible = fabricObject.visible !== false;

    // Update layer data
    layer.transform = {
      x: fabricObject.left || 0,
      y: fabricObject.top || 0,
      scaleX: fabricObject.scaleX || 1,
      scaleY: fabricObject.scaleY || 1,
      rotation: fabricObject.angle || 0,
      skewX: fabricObject.skewX || 0,
      skewY: fabricObject.skewY || 0
    };
  }

  private syncPIXIToFabric(layer: LayerNode, pixiObject: PIXI.DisplayObject, fabricObject: fabric.Object): void {
    // Sync transform back to Fabric
    fabricObject.set({
      left: pixiObject.x,
      top: pixiObject.y,
      scaleX: pixiObject.scale.x,
      scaleY: pixiObject.scale.y,
      angle: pixiObject.rotation * (180 / Math.PI),
      opacity: pixiObject.alpha,
      visible: pixiObject.visible
    });

    // Update layer data
    layer.transform = {
      x: pixiObject.x,
      y: pixiObject.y,
      scaleX: pixiObject.scale.x,
      scaleY: pixiObject.scale.y,
      rotation: pixiObject.rotation * (180 / Math.PI),
      skewX: 0,
      skewY: 0
    };
  }

  // Reassess layer routing based on performance changes
  reassessLayerRouting(layerId: string): void {
    const layer = this.layerManager.getLayer(layerId);
    if (!layer) return;

    const fabricObject = this.layerManager.getFabricObject(layerId);
    const context = LayerRenderAnalyzer.analyzeLayer(layer, fabricObject);
    
    const currentEngine = this.renderPipeline.pixiLayers.has(layerId) ? 'pixi' : 'fabric';
    
    if (context.renderEngine !== currentEngine) {
      // Need to move to different engine
      if (context.renderEngine === 'pixi') {
        this.moveLayerToPIXI(layerId);
      } else {
        this.moveLayerToFabric(layerId);
      }
      
      this.emit('layer:rerouted', { layerId, from: currentEngine, to: context.renderEngine });
    }
  }

  // Update render order for both engines
  private updateRenderOrder(): void {
    const renderOrder = this.layerManager.getRenderOrder();
    
    this.renderPipeline.renderOrder = renderOrder.map(layerId => ({
      layerId,
      engine: this.renderPipeline.pixiLayers.has(layerId) ? 'pixi' : 'fabric'
    }));

    // Update z-indices in PIXI
    this.updatePIXIZIndices();
    
    // Fabric.js handles its own z-order through object order
    this.fabricCanvas.renderAll();
  }

  private updatePIXIZIndices(): void {
    this.renderPipeline.renderOrder.forEach((item, index) => {
      if (item.engine === 'pixi') {
        const pixiObject = this.pixiLayerMap.get(item.layerId);
        if (pixiObject) {
          pixiObject.zIndex = index;
        }
      }
    });

    // Sort PIXI container children by zIndex
    this.pixiLayerContainer.children.sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0));
  }

  // Utility methods
  private parseColor(color: string | fabric.Pattern | fabric.Gradient | null | undefined): number {
    if (typeof color === 'string') {
      if (color.startsWith('#')) {
        return parseInt(color.slice(1), 16);
      } else if (color.startsWith('rgb')) {
        // Parse RGB color
        const matches = color.match(/\d+/g);
        if (matches && matches.length >= 3) {
          return (parseInt(matches[0]) << 16) + (parseInt(matches[1]) << 8) + parseInt(matches[2]);
        }
      }
    }
    return 0x000000; // Default to black
  }

  private convertBlendMode(blendMode: BlendMode): PIXI.BLEND_MODES {
    const blendModeMap: Record<BlendMode, PIXI.BLEND_MODES> = {
      'normal': PIXI.BLEND_MODES.NORMAL,
      'multiply': PIXI.BLEND_MODES.MULTIPLY,
      'screen': PIXI.BLEND_MODES.SCREEN,
      'overlay': PIXI.BLEND_MODES.OVERLAY,
      'soft-light': PIXI.BLEND_MODES.SOFT_LIGHT,
      'hard-light': PIXI.BLEND_MODES.HARD_LIGHT,
      'color-dodge': PIXI.BLEND_MODES.COLOR_DODGE,
      'color-burn': PIXI.BLEND_MODES.COLOR_BURN,
      'darken': PIXI.BLEND_MODES.DARKEN,
      'lighten': PIXI.BLEND_MODES.LIGHTEN,
      'difference': PIXI.BLEND_MODES.DIFFERENCE,
      'exclusion': PIXI.BLEND_MODES.EXCLUSION,
      'hue': PIXI.BLEND_MODES.HUE,
      'saturation': PIXI.BLEND_MODES.SATURATION,
      'color': PIXI.BLEND_MODES.COLOR,
      'luminosity': PIXI.BLEND_MODES.LUMINOSITY,
      'add': PIXI.BLEND_MODES.ADD,
      'subtract': PIXI.BLEND_MODES.SUBTRACT,
      'divide': PIXI.BLEND_MODES.NORMAL, // PIXI doesn't have divide
      'pass-through': PIXI.BLEND_MODES.NORMAL
    };

    return blendModeMap[blendMode] || PIXI.BLEND_MODES.NORMAL;
  }

  // Public API
  getLayerEngine(layerId: string): 'fabric' | 'pixi' | null {
    if (this.renderPipeline.fabricLayers.has(layerId)) return 'fabric';
    if (this.renderPipeline.pixiLayers.has(layerId)) return 'pixi';
    return null;
  }

  getEngineDistribution(): { fabric: number; pixi: number } {
    return {
      fabric: this.renderPipeline.fabricLayers.size,
      pixi: this.renderPipeline.pixiLayers.size
    };
  }

  forceLayerToEngine(layerId: string, engine: 'fabric' | 'pixi'): boolean {
    if (engine === 'pixi') {
      return this.moveLayerToPIXI(layerId);
    } else {
      return this.moveLayerToFabric(layerId);
    }
  }

  optimizeForPerformance(): void {
    // Reassess all layers for optimal performance
    this.layerManager.layers.forEach((layer, layerId) => {
      this.reassessLayerRouting(layerId);
    });
  }

  // Cleanup
  dispose(): void {
    this.syncQueue.clear();
    this.fabricLayerMap.clear();
    this.pixiLayerMap.clear();
    this.pixiApp.stage.removeChild(this.pixiLayerContainer);
    this.removeAllListeners();
  }
}

// Main hybrid integration class
export class LayerHybridIntegration extends EventEmitter {
  private layerManager: AdvancedLayerManager;
  private hybridSynchronizer: LayerHybridSynchronizer;
  private fabricCanvas: fabric.Canvas;
  private pixiApp: PIXI.Application;
  private config: HybridLayerConfig;

  constructor(
    fabricCanvas: fabric.Canvas,
    pixiApp: PIXI.Application,
    layerManager: AdvancedLayerManager,
    config: Partial<HybridLayerConfig> = {}
  ) {
    super();
    
    this.fabricCanvas = fabricCanvas;
    this.pixiApp = pixiApp;
    this.layerManager = layerManager;
    
    this.config = {
      preferPIXI: true,
      autoRoute: true,
      performanceThreshold: 60, // Target FPS
      pixiComplexityThreshold: 50,
      fabricInteractionThreshold: 30,
      ...config
    };

    this.hybridSynchronizer = new LayerHybridSynchronizer(
      fabricCanvas,
      pixiApp,
      layerManager
    );

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Forward synchronizer events
    this.hybridSynchronizer.on('layer:routed', (data) => {
      this.emit('layer:routed', data);
    });

    this.hybridSynchronizer.on('layer:rerouted', (data) => {
      this.emit('layer:rerouted', data);
    });

    this.hybridSynchronizer.on('layer:moved:to:pixi', (data) => {
      this.emit('layer:moved:to:pixi', data);
    });

    this.hybridSynchronizer.on('layer:moved:to:fabric', (data) => {
      this.emit('layer:moved:to:fabric', data);
    });

    // Auto-optimization based on performance
    if (this.config.autoRoute) {
      this.setupPerformanceMonitoring();
    }
  }

  private setupPerformanceMonitoring(): void {
    let frameCount = 0;
    let lastTime = performance.now();

    const measurePerformance = () => {
      const currentTime = performance.now();
      frameCount++;

      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        if (fps < this.config.performanceThreshold) {
          this.optimizeForPerformance();
        }

        frameCount = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(measurePerformance);
    };

    requestAnimationFrame(measurePerformance);
  }

  // Public API
  routeLayer(layerId: string): 'fabric' | 'pixi' {
    return this.hybridSynchronizer.routeLayer(this.layerManager.getLayer(layerId)!);
  }

  forceLayerToEngine(layerId: string, engine: 'fabric' | 'pixi'): boolean {
    return this.hybridSynchronizer.forceLayerToEngine(layerId, engine);
  }

  getLayerEngine(layerId: string): 'fabric' | 'pixi' | null {
    return this.hybridSynchronizer.getLayerEngine(layerId);
  }

  getEngineDistribution(): { fabric: number; pixi: number } {
    return this.hybridSynchronizer.getEngineDistribution();
  }

  optimizeForPerformance(): void {
    this.hybridSynchronizer.optimizeForPerformance();
    this.emit('performance:optimized', this.getEngineDistribution());
  }

  updateConfig(newConfig: Partial<HybridLayerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config:updated', this.config);
  }

  getPerformanceMetrics(): any {
    return {
      engineDistribution: this.getEngineDistribution(),
      config: this.config,
      totalLayers: this.layerManager.layerCount
    };
  }

  // Cleanup
  dispose(): void {
    this.hybridSynchronizer.dispose();
    this.removeAllListeners();
  }
}