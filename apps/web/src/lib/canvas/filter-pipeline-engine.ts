import { EventEmitter } from 'events';

// WebGL Filter Pipeline Types
export interface WebGLFilterContext {
  gl: WebGLRenderingContext | WebGL2RenderingContext;
  canvas: HTMLCanvasElement;
  isWebGL2: boolean;
  maxTextureSize: number;
  extensions: Record<string, any>;
  shaderCache: Map<string, WebGLProgram>;
}

export interface FilterShader {
  id: string;
  name: string;
  category: FilterCategory;
  vertexSource: string;
  fragmentSource: string;
  uniforms: Record<string, FilterUniform>;
  attributes: Record<string, FilterAttribute>;
  cached?: WebGLProgram;
}

export interface FilterUniform {
  type: 'float' | 'vec2' | 'vec3' | 'vec4' | 'int' | 'sampler2D' | 'mat3' | 'mat4';
  value: any;
  min?: number;
  max?: number;
  step?: number;
  description: string;
}

export interface FilterAttribute {
  name: string;
  type: 'float' | 'vec2' | 'vec3' | 'vec4';
  normalized: boolean;
}

export type FilterCategory = 'color' | 'blur' | 'distortion' | 'artistic' | 'noise' | 'edge' | 'composite' | 'lighting';

export interface FilterInstance {
  id: string;
  shaderId: string;
  name: string;
  enabled: boolean;
  opacity: number;
  blendMode: BlendMode;
  parameters: Record<string, any>;
  bounds?: FilterBounds;
  mask?: FilterMask;
  animatable?: boolean;
  keyframes?: FilterKeyframe[];
}

export interface FilterBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FilterMask {
  type: 'alpha' | 'luminance' | 'custom';
  texture?: WebGLTexture;
  inverted: boolean;
  feather: number;
}

export interface FilterKeyframe {
  time: number;
  parameters: Record<string, any>;
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-bezier';
}

export type BlendMode = 'normal' | 'multiply' | 'screen' | 'overlay' | 'soft-light' | 'hard-light' | 
  'color-dodge' | 'color-burn' | 'darken' | 'lighten' | 'difference' | 'exclusion' | 
  'hue' | 'saturation' | 'color' | 'luminosity' | 'add' | 'subtract';

export interface FilterPipelineConfig {
  enableWebGL2: boolean;
  enableFloatTextures: boolean;
  enableMipmaps: boolean;
  maxFilterInstances: number;
  texturePoolSize: number;
  enablePipelineOptimization: boolean;
  enableTileRendering: boolean;
  tileSize: number;
  enableAsyncProcessing: boolean;
}

export interface FilterPerformanceMetrics {
  frameTime: number;
  filterTime: number;
  textureMemory: number;
  shaderCompilations: number;
  drawCalls: number;
  primitiveCount: number;
  averageFPS: number;
  memoryUsage: number;
}

export interface FilterProcessingOptions {
  outputFormat: 'rgba' | 'rgb' | 'alpha';
  preserveAlpha: boolean;
  premultiplyAlpha: boolean;
  flipY: boolean;
  quality: 'low' | 'medium' | 'high' | 'ultra';
  enableMultisampling: boolean;
  useHalfFloat: boolean;
}

// WebGL Resource Management
export class WebGLResourceManager {
  private gl: WebGLRenderingContext | WebGL2RenderingContext;
  private texturePool: WebGLTexture[] = [];
  private framebufferPool: WebGLFramebuffer[] = [];
  private bufferPool: WebGLBuffer[] = [];
  private maxPoolSize: number;
  private memoryUsage: number = 0;

  constructor(gl: WebGLRenderingContext | WebGL2RenderingContext, maxPoolSize: number = 50) {
    this.gl = gl;
    this.maxPoolSize = maxPoolSize;
  }

  acquireTexture(width: number, height: number, format: number = this.gl.RGBA): WebGLTexture {
    let texture = this.texturePool.pop();
    
    if (!texture) {
      texture = this.gl.createTexture();
      if (!texture) throw new Error('Failed to create WebGL texture');
    }

    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
    this.gl.texImage2D(this.gl.TEXTURE_2D, 0, format, width, height, 0, format, this.gl.UNSIGNED_BYTE, null);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);

    this.memoryUsage += width * height * 4; // Assume RGBA
    return texture;
  }

  releaseTexture(texture: WebGLTexture): void {
    if (this.texturePool.length < this.maxPoolSize) {
      this.texturePool.push(texture);
    } else {
      this.gl.deleteTexture(texture);
    }
  }

  acquireFramebuffer(): WebGLFramebuffer {
    let framebuffer = this.framebufferPool.pop();
    
    if (!framebuffer) {
      framebuffer = this.gl.createFramebuffer();
      if (!framebuffer) throw new Error('Failed to create WebGL framebuffer');
    }

    return framebuffer;
  }

  releaseFramebuffer(framebuffer: WebGLFramebuffer): void {
    if (this.framebufferPool.length < this.maxPoolSize) {
      this.framebufferPool.push(framebuffer);
    } else {
      this.gl.deleteFramebuffer(framebuffer);
    }
  }

  acquireBuffer(): WebGLBuffer {
    let buffer = this.bufferPool.pop();
    
    if (!buffer) {
      buffer = this.gl.createBuffer();
      if (!buffer) throw new Error('Failed to create WebGL buffer');
    }

    return buffer;
  }

  releaseBuffer(buffer: WebGLBuffer): void {
    if (this.bufferPool.length < this.maxPoolSize) {
      this.bufferPool.push(buffer);
    } else {
      this.gl.deleteBuffer(buffer);
    }
  }

  getMemoryUsage(): number {
    return this.memoryUsage;
  }

  cleanup(): void {
    // Delete all pooled resources
    this.texturePool.forEach(texture => this.gl.deleteTexture(texture));
    this.framebufferPool.forEach(fb => this.gl.deleteFramebuffer(fb));
    this.bufferPool.forEach(buffer => this.gl.deleteBuffer(buffer));
    
    this.texturePool = [];
    this.framebufferPool = [];
    this.bufferPool = [];
    this.memoryUsage = 0;
  }
}

// Shader Compilation and Management
export class ShaderManager {
  private gl: WebGLRenderingContext | WebGL2RenderingContext;
  private shaderCache: Map<string, WebGLProgram> = new Map();
  private compilationStats = { success: 0, errors: 0 };

  constructor(gl: WebGLRenderingContext | WebGL2RenderingContext) {
    this.gl = gl;
  }

  compileShader(source: string, type: number): WebGLShader {
    const shader = this.gl.createShader(type);
    if (!shader) throw new Error('Failed to create shader');

    this.gl.shaderSource(shader, source);
    this.gl.compileShader(shader);

    if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
      const error = this.gl.getShaderInfoLog(shader);
      this.gl.deleteShader(shader);
      this.compilationStats.errors++;
      throw new Error(`Shader compilation failed: ${error}`);
    }

    this.compilationStats.success++;
    return shader;
  }

  createProgram(vertexSource: string, fragmentSource: string, cacheKey?: string): WebGLProgram {
    if (cacheKey && this.shaderCache.has(cacheKey)) {
      return this.shaderCache.get(cacheKey)!;
    }

    const vertexShader = this.compileShader(vertexSource, this.gl.VERTEX_SHADER);
    const fragmentShader = this.compileShader(fragmentSource, this.gl.FRAGMENT_SHADER);

    const program = this.gl.createProgram();
    if (!program) throw new Error('Failed to create shader program');

    this.gl.attachShader(program, vertexShader);
    this.gl.attachShader(program, fragmentShader);
    this.gl.linkProgram(program);

    if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
      const error = this.gl.getProgramInfoLog(program);
      this.gl.deleteProgram(program);
      this.gl.deleteShader(vertexShader);
      this.gl.deleteShader(fragmentShader);
      throw new Error(`Program linking failed: ${error}`);
    }

    // Clean up shaders (they're no longer needed once linked)
    this.gl.deleteShader(vertexShader);
    this.gl.deleteShader(fragmentShader);

    if (cacheKey) {
      this.shaderCache.set(cacheKey, program);
    }

    return program;
  }

  getProgram(cacheKey: string): WebGLProgram | undefined {
    return this.shaderCache.get(cacheKey);
  }

  deleteProgram(cacheKey: string): boolean {
    const program = this.shaderCache.get(cacheKey);
    if (program) {
      this.gl.deleteProgram(program);
      this.shaderCache.delete(cacheKey);
      return true;
    }
    return false;
  }

  getCompilationStats() {
    return { ...this.compilationStats };
  }

  clearCache(): void {
    this.shaderCache.forEach(program => this.gl.deleteProgram(program));
    this.shaderCache.clear();
  }
}

// Main Filter Pipeline Engine
export class FilterPipelineEngine extends EventEmitter {
  private canvas: HTMLCanvasElement;
  private gl: WebGLRenderingContext | WebGL2RenderingContext;
  private context: WebGLFilterContext;
  private resourceManager: WebGLResourceManager;
  private shaderManager: ShaderManager;
  private config: FilterPipelineConfig;
  
  // Pipeline state
  private filterInstances: Map<string, FilterInstance> = new Map();
  private renderTargets: WebGLTexture[] = [];
  private currentRenderTarget: number = 0;
  private sourceTexture?: WebGLTexture;
  private outputTexture?: WebGLTexture;
  
  // Performance tracking
  private performanceMetrics: FilterPerformanceMetrics = {
    frameTime: 0,
    filterTime: 0,
    textureMemory: 0,
    shaderCompilations: 0,
    drawCalls: 0,
    primitiveCount: 0,
    averageFPS: 60,
    memoryUsage: 0
  };
  private frameTimes: number[] = [];
  private lastFrameTime: number = 0;

  // Geometry buffers
  private quadBuffer?: WebGLBuffer;
  private indexBuffer?: WebGLBuffer;

  constructor(canvas: HTMLCanvasElement, config: Partial<FilterPipelineConfig> = {}) {
    super();
    
    this.canvas = canvas;
    this.config = {
      enableWebGL2: true,
      enableFloatTextures: true,
      enableMipmaps: false,
      maxFilterInstances: 32,
      texturePoolSize: 20,
      enablePipelineOptimization: true,
      enableTileRendering: false,
      tileSize: 512,
      enableAsyncProcessing: false,
      ...config
    };

    this.initializeWebGL();
    this.setupContext();
    this.createGeometry();
    this.createRenderTargets();
  }

  private initializeWebGL(): void {
    // Try WebGL2 first if enabled
    if (this.config.enableWebGL2) {
      this.gl = this.canvas.getContext('webgl2', {
        premultipliedAlpha: false,
        preserveDrawingBuffer: true,
        antialias: false
      }) as WebGL2RenderingContext;
    }

    // Fallback to WebGL1
    if (!this.gl) {
      this.gl = this.canvas.getContext('webgl', {
        premultipliedAlpha: false,
        preserveDrawingBuffer: true,
        antialias: false
      }) as WebGLRenderingContext;
    }

    if (!this.gl) {
      throw new Error('WebGL not supported');
    }

    // Set initial state
    this.gl.clearColor(0, 0, 0, 0);
    this.gl.disable(this.gl.DEPTH_TEST);
    this.gl.disable(this.gl.CULL_FACE);
    this.gl.enable(this.gl.BLEND);
    this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);
  }

  private setupContext(): void {
    const isWebGL2 = this.gl instanceof WebGL2RenderingContext;
    
    // Detect available extensions
    const extensions: Record<string, any> = {};
    
    if (this.config.enableFloatTextures) {
      if (isWebGL2) {
        extensions.floatTextures = this.gl.getExtension('EXT_color_buffer_float');
      } else {
        extensions.floatTextures = this.gl.getExtension('OES_texture_float');
        extensions.floatLinear = this.gl.getExtension('OES_texture_float_linear');
      }
    }

    this.context = {
      gl: this.gl,
      canvas: this.canvas,
      isWebGL2,
      maxTextureSize: this.gl.getParameter(this.gl.MAX_TEXTURE_SIZE),
      extensions,
      shaderCache: new Map()
    };

    this.resourceManager = new WebGLResourceManager(this.gl, this.config.texturePoolSize);
    this.shaderManager = new ShaderManager(this.gl);
  }

  private createGeometry(): void {
    // Create full-screen quad
    const vertices = new Float32Array([
      -1, -1, 0, 0,  // Bottom-left
       1, -1, 1, 0,  // Bottom-right
       1,  1, 1, 1,  // Top-right
      -1,  1, 0, 1   // Top-left
    ]);

    const indices = new Uint16Array([0, 1, 2, 0, 2, 3]);

    this.quadBuffer = this.resourceManager.acquireBuffer();
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.quadBuffer);
    this.gl.bufferData(this.gl.ARRAY_BUFFER, vertices, this.gl.STATIC_DRAW);

    this.indexBuffer = this.resourceManager.acquireBuffer();
    this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
    this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER, indices, this.gl.STATIC_DRAW);
  }

  private createRenderTargets(): void {
    const width = this.canvas.width;
    const height = this.canvas.height;

    // Create ping-pong render targets for multi-pass filtering
    for (let i = 0; i < 2; i++) {
      const texture = this.resourceManager.acquireTexture(width, height);
      this.renderTargets.push(texture);
    }
  }

  // Filter management
  addFilter(filter: FilterInstance): void {
    if (this.filterInstances.size >= this.config.maxFilterInstances) {
      throw new Error(`Maximum filter instances exceeded (${this.config.maxFilterInstances})`);
    }

    this.filterInstances.set(filter.id, filter);
    this.emit('filter:added', filter);
  }

  removeFilter(filterId: string): boolean {
    const removed = this.filterInstances.delete(filterId);
    if (removed) {
      this.emit('filter:removed', { filterId });
    }
    return removed;
  }

  updateFilter(filterId: string, updates: Partial<FilterInstance>): boolean {
    const filter = this.filterInstances.get(filterId);
    if (!filter) return false;

    Object.assign(filter, updates);
    this.emit('filter:updated', filter);
    return true;
  }

  getFilter(filterId: string): FilterInstance | undefined {
    return this.filterInstances.get(filterId);
  }

  getAllFilters(): FilterInstance[] {
    return Array.from(this.filterInstances.values());
  }

  clearFilters(): void {
    this.filterInstances.clear();
    this.emit('filters:cleared');
  }

  // Pipeline processing
  process(sourceTexture: WebGLTexture, options: Partial<FilterProcessingOptions> = {}): WebGLTexture {
    const startTime = performance.now();
    
    this.sourceTexture = sourceTexture;
    this.performanceMetrics.drawCalls = 0;
    this.performanceMetrics.primitiveCount = 0;

    // Get enabled filters sorted by order
    const enabledFilters = Array.from(this.filterInstances.values())
      .filter(filter => filter.enabled)
      .sort((a, b) => (a as any).order || 0 - (b as any).order || 0);

    if (enabledFilters.length === 0) {
      this.outputTexture = sourceTexture;
      return sourceTexture;
    }

    // Setup viewport
    this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);

    // Process filters
    let currentInput = sourceTexture;
    let renderTargetIndex = 0;

    for (let i = 0; i < enabledFilters.length; i++) {
      const filter = enabledFilters[i];
      const isLastFilter = i === enabledFilters.length - 1;
      
      // Determine output target
      let outputTarget: WebGLTexture | null;
      if (isLastFilter) {
        outputTarget = null; // Render to canvas
      } else {
        outputTarget = this.renderTargets[renderTargetIndex % this.renderTargets.length];
        renderTargetIndex++;
      }

      // Apply filter
      currentInput = this.applyFilter(filter, currentInput, outputTarget, options);
    }

    this.outputTexture = currentInput;

    // Update performance metrics
    const endTime = performance.now();
    this.performanceMetrics.filterTime = endTime - startTime;
    this.performanceMetrics.frameTime = endTime - this.lastFrameTime;
    this.lastFrameTime = endTime;

    this.updateFPSMetrics();

    this.emit('pipeline:processed', {
      filterCount: enabledFilters.length,
      processingTime: this.performanceMetrics.filterTime,
      metrics: this.performanceMetrics
    });

    return this.outputTexture;
  }

  private applyFilter(
    filter: FilterInstance, 
    inputTexture: WebGLTexture, 
    outputTarget: WebGLTexture | null,
    options: Partial<FilterProcessingOptions>
  ): WebGLTexture {
    // Get or create shader program
    const program = this.getOrCreateFilterProgram(filter);
    this.gl.useProgram(program);

    // Setup framebuffer
    if (outputTarget) {
      const framebuffer = this.resourceManager.acquireFramebuffer();
      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, framebuffer);
      this.gl.framebufferTexture2D(
        this.gl.FRAMEBUFFER, 
        this.gl.COLOR_ATTACHMENT0, 
        this.gl.TEXTURE_2D, 
        outputTarget, 
        0
      );
    } else {
      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, null);
    }

    // Bind input texture
    this.gl.activeTexture(this.gl.TEXTURE0);
    this.gl.bindTexture(this.gl.TEXTURE_2D, inputTexture);

    // Set uniforms
    this.setFilterUniforms(program, filter, inputTexture);

    // Setup geometry
    this.setupFilterGeometry(program);

    // Apply blend mode
    this.applyBlendMode(filter.blendMode);

    // Draw
    this.gl.drawElements(this.gl.TRIANGLES, 6, this.gl.UNSIGNED_SHORT, 0);
    this.performanceMetrics.drawCalls++;
    this.performanceMetrics.primitiveCount += 2; // 2 triangles

    // Clean up
    if (outputTarget) {
      const framebuffer = this.gl.getParameter(this.gl.FRAMEBUFFER_BINDING);
      this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, null);
      this.resourceManager.releaseFramebuffer(framebuffer);
      return outputTarget;
    } else {
      return inputTexture; // When rendering to canvas
    }
  }

  private getOrCreateFilterProgram(filter: FilterInstance): WebGLProgram {
    const cacheKey = `filter_${filter.shaderId}`;
    let program = this.shaderManager.getProgram(cacheKey);

    if (!program) {
      // This would typically load shader source from a registry
      // For now, we'll use a simple pass-through shader
      const vertexSource = this.getDefaultVertexShader();
      const fragmentSource = this.getFilterFragmentShader(filter);
      
      program = this.shaderManager.createProgram(vertexSource, fragmentSource, cacheKey);
      this.performanceMetrics.shaderCompilations++;
    }

    return program;
  }

  private getDefaultVertexShader(): string {
    return `
      attribute vec2 a_position;
      attribute vec2 a_texCoord;
      varying vec2 v_texCoord;
      
      void main() {
        gl_Position = vec4(a_position, 0.0, 1.0);
        v_texCoord = a_texCoord;
      }
    `;
  }

  private getFilterFragmentShader(filter: FilterInstance): string {
    // This is a simplified implementation
    // In practice, this would load shader source from a filter library
    return `
      precision mediump float;
      uniform sampler2D u_image;
      uniform float u_opacity;
      varying vec2 v_texCoord;
      
      void main() {
        vec4 color = texture2D(u_image, v_texCoord);
        gl_FragColor = vec4(color.rgb, color.a * u_opacity);
      }
    `;
  }

  private setFilterUniforms(program: WebGLProgram, filter: FilterInstance, inputTexture: WebGLTexture): void {
    // Set default uniforms
    const imageLocation = this.gl.getUniformLocation(program, 'u_image');
    if (imageLocation) {
      this.gl.uniform1i(imageLocation, 0);
    }

    const opacityLocation = this.gl.getUniformLocation(program, 'u_opacity');
    if (opacityLocation) {
      this.gl.uniform1f(opacityLocation, filter.opacity);
    }

    // Set filter-specific parameters
    Object.entries(filter.parameters).forEach(([name, value]) => {
      const location = this.gl.getUniformLocation(program, `u_${name}`);
      if (location) {
        this.setUniformValue(location, value);
      }
    });
  }

  private setUniformValue(location: WebGLUniformLocation, value: any): void {
    if (typeof value === 'number') {
      this.gl.uniform1f(location, value);
    } else if (Array.isArray(value)) {
      switch (value.length) {
        case 2:
          this.gl.uniform2fv(location, value);
          break;
        case 3:
          this.gl.uniform3fv(location, value);
          break;
        case 4:
          this.gl.uniform4fv(location, value);
          break;
      }
    }
  }

  private setupFilterGeometry(program: WebGLProgram): void {
    const positionLocation = this.gl.getAttribLocation(program, 'a_position');
    const texCoordLocation = this.gl.getAttribLocation(program, 'a_texCoord');

    // Bind and setup vertex buffer
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.quadBuffer!);
    
    if (positionLocation >= 0) {
      this.gl.enableVertexAttribArray(positionLocation);
      this.gl.vertexAttribPointer(positionLocation, 2, this.gl.FLOAT, false, 16, 0);
    }

    if (texCoordLocation >= 0) {
      this.gl.enableVertexAttribArray(texCoordLocation);
      this.gl.vertexAttribPointer(texCoordLocation, 2, this.gl.FLOAT, false, 16, 8);
    }

    // Bind index buffer
    this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer!);
  }

  private applyBlendMode(blendMode: BlendMode): void {
    switch (blendMode) {
      case 'normal':
        this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);
        break;
      case 'multiply':
        this.gl.blendFunc(this.gl.DST_COLOR, this.gl.ZERO);
        break;
      case 'screen':
        this.gl.blendFunc(this.gl.ONE, this.gl.ONE_MINUS_SRC_COLOR);
        break;
      case 'add':
        this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE);
        break;
      default:
        this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);
    }
  }

  private updateFPSMetrics(): void {
    this.frameTimes.push(this.performanceMetrics.frameTime);
    if (this.frameTimes.length > 60) {
      this.frameTimes.shift();
    }

    const averageFrameTime = this.frameTimes.reduce((a, b) => a + b, 0) / this.frameTimes.length;
    this.performanceMetrics.averageFPS = 1000 / averageFrameTime;
  }

  // Texture utilities
  createTextureFromImageData(imageData: ImageData): WebGLTexture {
    const texture = this.resourceManager.acquireTexture(imageData.width, imageData.height);
    
    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
    this.gl.texImage2D(
      this.gl.TEXTURE_2D, 
      0, 
      this.gl.RGBA, 
      imageData.width, 
      imageData.height, 
      0, 
      this.gl.RGBA, 
      this.gl.UNSIGNED_BYTE, 
      imageData.data
    );

    return texture;
  }

  createTextureFromCanvas(canvas: HTMLCanvasElement): WebGLTexture {
    const texture = this.resourceManager.acquireTexture(canvas.width, canvas.height);
    
    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
    this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, canvas);

    return texture;
  }

  readPixels(x: number = 0, y: number = 0, width?: number, height?: number): ImageData {
    width = width || this.canvas.width;
    height = height || this.canvas.height;

    const pixels = new Uint8Array(width * height * 4);
    this.gl.readPixels(x, y, width, height, this.gl.RGBA, this.gl.UNSIGNED_BYTE, pixels);

    return new ImageData(new Uint8ClampedArray(pixels), width, height);
  }

  // Performance and debugging
  getPerformanceMetrics(): FilterPerformanceMetrics {
    this.performanceMetrics.textureMemory = this.resourceManager.getMemoryUsage();
    this.performanceMetrics.memoryUsage = this.getApproximateMemoryUsage();
    
    return { ...this.performanceMetrics };
  }

  private getApproximateMemoryUsage(): number {
    // Rough estimate of total memory usage
    return this.resourceManager.getMemoryUsage() + 
           (this.shaderManager.getCompilationStats().success * 1024); // Rough shader memory estimate
  }

  resetPerformanceMetrics(): void {
    this.performanceMetrics = {
      frameTime: 0,
      filterTime: 0,
      textureMemory: 0,
      shaderCompilations: 0,
      drawCalls: 0,
      primitiveCount: 0,
      averageFPS: 60,
      memoryUsage: 0
    };
    this.frameTimes = [];
  }

  // Resource management
  resizeRenderTargets(width: number, height: number): void {
    this.canvas.width = width;
    this.canvas.height = height;
    
    // Recreate render targets with new dimensions
    this.renderTargets.forEach(texture => {
      this.resourceManager.releaseTexture(texture);
    });
    this.renderTargets = [];
    
    this.createRenderTargets();
    this.emit('pipeline:resized', { width, height });
  }

  dispose(): void {
    // Clean up resources
    this.resourceManager.cleanup();
    this.shaderManager.clearCache();
    
    if (this.quadBuffer) {
      this.resourceManager.releaseBuffer(this.quadBuffer);
    }
    if (this.indexBuffer) {
      this.resourceManager.releaseBuffer(this.indexBuffer);
    }

    this.filterInstances.clear();
    this.renderTargets = [];
    
    this.removeAllListeners();
    this.emit('pipeline:disposed');
  }
}