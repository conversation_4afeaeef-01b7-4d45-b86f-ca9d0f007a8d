import { fabric } from 'fabric';

// Utility function for unique ID generation
function generateUniqueId(): string {
  return `obj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Base metadata interface
export interface ObjectMetadata {
  createdAt: number;
  tool: string;
  type: string;
  version?: string;
  author?: string;
  tags?: string[];
  [key: string]: any;
}

// Shape creation options
export interface ShapeOptions {
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  opacity?: number;
  left?: number;
  top?: number;
  width?: number;
  height?: number;
  radius?: number;
  angle?: number;
  scaleX?: number;
  scaleY?: number;
  selectable?: boolean;
  evented?: boolean;
  metadata?: Partial<ObjectMetadata>;
}

// Text creation options
export interface TextOptions {
  fontFamily?: string;
  fontSize?: number;
  fontWeight?: string | number;
  fontStyle?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  textAlign?: string;
  underline?: boolean;
  linethrough?: boolean;
  overline?: boolean;
  left?: number;
  top?: number;
  angle?: number;
  opacity?: number;
  metadata?: Partial<ObjectMetadata>;
}

// Image creation options
export interface ImageOptions {
  left?: number;
  top?: number;
  scaleX?: number;
  scaleY?: number;
  angle?: number;
  opacity?: number;
  cropX?: number;
  cropY?: number;
  crossOrigin?: string;
  filters?: fabric.IBaseFilter[];
  metadata?: Partial<ObjectMetadata>;
}

// Path creation options
export interface PathOptions {
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  strokeLineCap?: string;
  strokeLineJoin?: string;
  strokeDashArray?: number[];
  opacity?: number;
  left?: number;
  top?: number;
  scaleX?: number;
  scaleY?: number;
  metadata?: Partial<ObjectMetadata>;
}

// Advanced shape options
export interface PolygonOptions extends ShapeOptions {
  points: fabric.Point[];
}

export interface StarOptions extends ShapeOptions {
  points: number;
  innerRadius: number;
  outerRadius: number;
}

export interface ArrowOptions extends ShapeOptions {
  headLength?: number;
  headWidth?: number;
  tailWidth?: number;
}

// Factory for creating Fabric.js objects with metadata and professional features
export class FabricObjectFactory {
  
  // Basic Shape Creation
  static createRectangle(options: ShapeOptions = {}): fabric.Rect {
    const baseOptions = this.getBaseOptions('rectangle', options);
    
    return new fabric.Rect({
      width: options.width || 100,
      height: options.height || 60,
      fill: options.fill || '#3b82f6',
      stroke: options.stroke || '#1e40af',
      strokeWidth: options.strokeWidth || 1,
      rx: 0, // rounded corners
      ry: 0,
      ...baseOptions
    });
  }

  static createCircle(options: ShapeOptions = {}): fabric.Circle {
    const baseOptions = this.getBaseOptions('circle', options);
    
    return new fabric.Circle({
      radius: options.radius || 50,
      fill: options.fill || '#10b981',
      stroke: options.stroke || '#047857',
      strokeWidth: options.strokeWidth || 1,
      ...baseOptions
    });
  }

  static createEllipse(options: ShapeOptions & { rx?: number, ry?: number } = {}): fabric.Ellipse {
    const baseOptions = this.getBaseOptions('ellipse', options);
    
    return new fabric.Ellipse({
      rx: options.rx || 60,
      ry: options.ry || 40,
      fill: options.fill || '#8b5cf6',
      stroke: options.stroke || '#7c3aed',
      strokeWidth: options.strokeWidth || 1,
      ...baseOptions
    });
  }

  static createTriangle(options: ShapeOptions = {}): fabric.Triangle {
    const baseOptions = this.getBaseOptions('triangle', options);
    
    return new fabric.Triangle({
      width: options.width || 80,
      height: options.height || 80,
      fill: options.fill || '#f59e0b',
      stroke: options.stroke || '#d97706',
      strokeWidth: options.strokeWidth || 1,
      ...baseOptions
    });
  }

  static createPolygon(points: fabric.Point[], options: PolygonOptions): fabric.Polygon {
    const baseOptions = this.getBaseOptions('polygon', options);
    
    return new fabric.Polygon(points, {
      fill: options.fill || '#ef4444',
      stroke: options.stroke || '#dc2626',
      strokeWidth: options.strokeWidth || 1,
      ...baseOptions
    });
  }

  // Advanced Shapes
  static createStar(options: StarOptions): fabric.Polygon {
    const { points = 5, innerRadius = 20, outerRadius = 40 } = options;
    const starPoints: fabric.Point[] = [];
    
    for (let i = 0; i < points * 2; i++) {
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const angle = (i * Math.PI) / points;
      starPoints.push(new fabric.Point(
        radius * Math.cos(angle),
        radius * Math.sin(angle)
      ));
    }

    const baseOptions = this.getBaseOptions('star', options);
    
    return new fabric.Polygon(starPoints, {
      fill: options.fill || '#f59e0b',
      stroke: options.stroke || '#d97706',
      strokeWidth: options.strokeWidth || 1,
      ...baseOptions,
      metadata: {
        ...baseOptions.metadata,
        starPoints: points,
        innerRadius,
        outerRadius
      }
    });
  }

  static createArrow(options: ArrowOptions): fabric.Group {
    const {
      width = 100,
      height = 20,
      headLength = 30,
      headWidth = 40,
      tailWidth = 20
    } = options;

    // Create arrow shaft
    const shaft = new fabric.Rect({
      width: width - headLength,
      height: tailWidth,
      fill: options.fill || '#6366f1',
      stroke: options.stroke,
      strokeWidth: options.strokeWidth || 0,
      originX: 'left',
      originY: 'center'
    });

    // Create arrow head (triangle)
    const headPoints = [
      new fabric.Point(width - headLength, -headWidth / 2),
      new fabric.Point(width, 0),
      new fabric.Point(width - headLength, headWidth / 2)
    ];

    const head = new fabric.Polygon(headPoints, {
      fill: options.fill || '#6366f1',
      stroke: options.stroke,
      strokeWidth: options.strokeWidth || 0,
      originX: 'left',
      originY: 'center'
    });

    const baseOptions = this.getBaseOptions('arrow', options);
    
    return new fabric.Group([shaft, head], {
      ...baseOptions,
      metadata: {
        ...baseOptions.metadata,
        headLength,
        headWidth,
        tailWidth
      }
    });
  }

  // Text Creation
  static createText(text: string, options: TextOptions = {}): fabric.Text {
    const baseOptions = this.getBaseOptions('text', options);
    
    return new fabric.Text(text, {
      fontFamily: options.fontFamily || 'Inter',
      fontSize: options.fontSize || 20,
      fontWeight: options.fontWeight || 'normal',
      fontStyle: options.fontStyle || 'normal',
      fill: options.fill || '#1f2937',
      stroke: options.stroke,
      strokeWidth: options.strokeWidth || 0,
      textAlign: options.textAlign || 'left',
      underline: options.underline || false,
      linethrough: options.linethrough || false,
      overline: options.overline || false,
      ...baseOptions
    });
  }

  static createTextbox(text: string, options: TextOptions & { width?: number } = {}): fabric.Textbox {
    const baseOptions = this.getBaseOptions('textbox', options);
    
    return new fabric.Textbox(text, {
      width: options.width || 200,
      fontFamily: options.fontFamily || 'Inter',
      fontSize: options.fontSize || 16,
      fontWeight: options.fontWeight || 'normal',
      fontStyle: options.fontStyle || 'normal',
      fill: options.fill || '#1f2937',
      stroke: options.stroke,
      strokeWidth: options.strokeWidth || 0,
      textAlign: options.textAlign || 'left',
      underline: options.underline || false,
      linethrough: options.linethrough || false,
      overline: options.overline || false,
      ...baseOptions
    });
  }

  // Image Creation
  static createImage(imageElement: HTMLImageElement, options: ImageOptions = {}): fabric.Image {
    const baseOptions = this.getBaseOptions('image', options);
    
    const image = new fabric.Image(imageElement, {
      crossOrigin: options.crossOrigin || 'anonymous',
      ...baseOptions
    });

    // Apply crop if specified
    if (options.cropX !== undefined || options.cropY !== undefined) {
      const cropX = options.cropX || 0;
      const cropY = options.cropY || 0;
      image.set({
        cropX,
        cropY
      });
    }

    // Apply filters if specified
    if (options.filters && options.filters.length > 0) {
      image.filters = options.filters;
      image.applyFilters();
    }

    return image;
  }

  static async createImageFromURL(url: string, options: ImageOptions = {}): Promise<fabric.Image> {
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(url, (img) => {
        if (!img) {
          reject(new Error('Failed to load image'));
          return;
        }

        const baseOptions = this.getBaseOptions('image', options);
        img.set({
          crossOrigin: 'anonymous',
          ...baseOptions
        });

        // Apply filters if specified
        if (options.filters && options.filters.length > 0) {
          img.filters = options.filters;
          img.applyFilters();
        }

        resolve(img);
      }, {
        crossOrigin: 'anonymous'
      });
    });
  }

  // Path Creation
  static createPath(pathData: string, options: PathOptions = {}): fabric.Path {
    const baseOptions = this.getBaseOptions('path', options);
    
    return new fabric.Path(pathData, {
      fill: options.fill || '#6b7280',
      stroke: options.stroke || '#374151',
      strokeWidth: options.strokeWidth || 1,
      strokeLineCap: options.strokeLineCap || 'round',
      strokeLineJoin: options.strokeLineJoin || 'round',
      strokeDashArray: options.strokeDashArray,
      ...baseOptions
    });
  }

  // Line Creation
  static createLine(x1: number, y1: number, x2: number, y2: number, options: PathOptions = {}): fabric.Line {
    const baseOptions = this.getBaseOptions('line', options);
    
    return new fabric.Line([x1, y1, x2, y2], {
      stroke: options.stroke || '#374151',
      strokeWidth: options.strokeWidth || 2,
      strokeLineCap: options.strokeLineCap || 'round',
      strokeDashArray: options.strokeDashArray,
      ...baseOptions
    });
  }

  // Brush/Free Drawing Path
  static createBrushPath(pathData: string, options: PathOptions = {}): fabric.Path {
    const baseOptions = this.getBaseOptions('brush', options);
    
    return new fabric.Path(pathData, {
      fill: '',
      stroke: options.stroke || '#1f2937',
      strokeWidth: options.strokeWidth || 3,
      strokeLineCap: 'round',
      strokeLineJoin: 'round',
      ...baseOptions
    });
  }

  // Group Creation
  static createGroup(objects: fabric.Object[], options: Partial<fabric.IGroupOptions> = {}): fabric.Group {
    const baseOptions = this.getBaseOptions('group', options);
    
    return new fabric.Group(objects, {
      ...baseOptions,
      metadata: {
        ...baseOptions.metadata,
        objectCount: objects.length,
        childTypes: objects.map(obj => obj.type)
      }
    });
  }

  // Utility Methods
  private static getBaseOptions(type: string, options: any) {
    const metadata: ObjectMetadata = {
      createdAt: Date.now(),
      tool: options.metadata?.tool || 'shape',
      type,
      version: '1.0.0',
      ...options.metadata
    };

    return {
      id: generateUniqueId(),
      left: options.left || 50,
      top: options.top || 50,
      opacity: options.opacity !== undefined ? options.opacity : 1,
      angle: options.angle || 0,
      scaleX: options.scaleX || 1,
      scaleY: options.scaleY || 1,
      selectable: options.selectable !== undefined ? options.selectable : true,
      evented: options.evented !== undefined ? options.evented : true,
      metadata
    };
  }

  // Professional Shape Templates
  static createButton(text: string, options: ShapeOptions & TextOptions = {}): fabric.Group {
    const buttonBg = this.createRectangle({
      width: options.width || 120,
      height: options.height || 40,
      fill: options.fill || '#3b82f6',
      stroke: options.stroke || '#2563eb',
      strokeWidth: 1,
      left: 0,
      top: 0,
      metadata: { tool: 'ui', type: 'button-background' }
    });

    // Round the corners
    buttonBg.set({ rx: 6, ry: 6 });

    const buttonText = this.createText(text, {
      fontSize: options.fontSize || 14,
      fontWeight: 500,
      fill: '#ffffff',
      left: 0,
      top: 0,
      textAlign: 'center',
      metadata: { tool: 'ui', type: 'button-text' }
    });

    // Center the text
    buttonText.set({
      originX: 'center',
      originY: 'center'
    });

    const baseOptions = this.getBaseOptions('button', options);
    
    return new fabric.Group([buttonBg, buttonText], {
      ...baseOptions,
      metadata: {
        ...baseOptions.metadata,
        tool: 'ui',
        type: 'button',
        text
      }
    });
  }

  static createCard(options: ShapeOptions & { title?: string, content?: string } = {}): fabric.Group {
    const cardBg = this.createRectangle({
      width: options.width || 200,
      height: options.height || 120,
      fill: options.fill || '#ffffff',
      stroke: options.stroke || '#e5e7eb',
      strokeWidth: 1,
      left: 0,
      top: 0,
      metadata: { tool: 'ui', type: 'card-background' }
    });

    // Round the corners
    cardBg.set({ rx: 8, ry: 8 });

    const objects: fabric.Object[] = [cardBg];

    if (options.title) {
      const titleText = this.createText(options.title, {
        fontSize: 16,
        fontWeight: 600,
        fill: '#1f2937',
        left: 16,
        top: 16,
        metadata: { tool: 'ui', type: 'card-title' }
      });
      objects.push(titleText);
    }

    if (options.content) {
      const contentText = this.createTextbox(options.content, {
        width: (options.width || 200) - 32,
        fontSize: 14,
        fill: '#6b7280',
        left: 16,
        top: options.title ? 48 : 16,
        metadata: { tool: 'ui', type: 'card-content' }
      });
      objects.push(contentText);
    }

    const baseOptions = this.getBaseOptions('card', options);
    
    return new fabric.Group(objects, {
      ...baseOptions,
      metadata: {
        ...baseOptions.metadata,
        tool: 'ui',
        type: 'card',
        title: options.title,
        content: options.content
      }
    });
  }

  // Clone object with new ID
  static async cloneObject(object: fabric.Object): Promise<fabric.Object> {
    return new Promise((resolve) => {
      object.clone((cloned: fabric.Object) => {
        const newId = generateUniqueId();
        const originalMetadata = (object as any).metadata || {};
        
        cloned.set({
          id: newId,
          metadata: {
            ...originalMetadata,
            createdAt: Date.now(),
            clonedFrom: (object as any).id,
            type: originalMetadata.type,
            tool: originalMetadata.tool
          }
        } as any);
        
        resolve(cloned);
      });
    });
  }

  // Get object info for debugging
  static getObjectInfo(object: fabric.Object): {
    id: string;
    type: string;
    bounds: fabric.Rect;
    metadata: ObjectMetadata;
    properties: Record<string, any>;
  } {
    return {
      id: (object as any).id || 'no-id',
      type: object.type || 'unknown',
      bounds: object.getBoundingRect(),
      metadata: (object as any).metadata || {},
      properties: {
        left: object.left,
        top: object.top,
        width: object.width,
        height: object.height,
        scaleX: object.scaleX,
        scaleY: object.scaleY,
        angle: object.angle,
        opacity: object.opacity,
        visible: object.visible,
        selectable: object.selectable
      }
    };
  }
}

// Export common shape creation shortcuts
export const Shapes = {
  rect: (options?: ShapeOptions) => FabricObjectFactory.createRectangle(options),
  circle: (options?: ShapeOptions) => FabricObjectFactory.createCircle(options),
  ellipse: (options?: ShapeOptions & { rx?: number, ry?: number }) => FabricObjectFactory.createEllipse(options),
  triangle: (options?: ShapeOptions) => FabricObjectFactory.createTriangle(options),
  star: (options: StarOptions) => FabricObjectFactory.createStar(options),
  arrow: (options?: ArrowOptions) => FabricObjectFactory.createArrow(options),
  line: (x1: number, y1: number, x2: number, y2: number, options?: PathOptions) => 
    FabricObjectFactory.createLine(x1, y1, x2, y2, options)
};

export const Text = {
  text: (text: string, options?: TextOptions) => FabricObjectFactory.createText(text, options),
  textbox: (text: string, options?: TextOptions & { width?: number }) => 
    FabricObjectFactory.createTextbox(text, options)
};

export const UI = {
  button: (text: string, options?: ShapeOptions & TextOptions) => 
    FabricObjectFactory.createButton(text, options),
  card: (options?: ShapeOptions & { title?: string, content?: string }) => 
    FabricObjectFactory.createCard(options)
};