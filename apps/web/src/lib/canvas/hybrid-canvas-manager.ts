import { fabric } from 'fabric';
import { EnhancedHybridCanvasManager } from './enhanced-hybrid-canvas';
import { PIXIPerformanceLayer, PIXIPerformanceConfig } from './pixi-performance-layer';
import { EventEmitter } from 'events';

export interface HybridCanvasConfig {
  width: number;
  height: number;
  backgroundColor?: string;
  enableWebGL?: boolean;
  pixiConfig?: Partial<PIXIPerformanceConfig>;
}

export class HybridCanvasManager extends EventEmitter {
  private fabricManager: EnhancedHybridCanvasManager;
  private pixiLayer: PIXIPerformanceLayer | null = null;
  private canvasElement: HTMLCanvasElement;
  private config: HybridCanvasConfig;
  private isWebGLEnabled = false;

  constructor(canvasElement: HTMLCanvasElement, config: HybridCanvasConfig) {
    super();
    this.canvasElement = canvasElement;
    this.config = config;

    // Initialize Fabric.js layer
    this.fabricManager = new EnhancedHybridCanvasManager(canvasElement, {
      width: config.width,
      height: config.height,
      backgroundColor: config.backgroundColor || '#ffffff',
      selection: true,
      preserveObjectStacking: true
    });

    // Initialize PIXI.js layer if WebGL is enabled
    if (config.enableWebGL !== false) {
      this.initializePIXILayer();
    }

    this.setupEventForwarding();
  }

  private initializePIXILayer(): void {
    try {
      const pixiConfig: PIXIPerformanceConfig = {
        width: this.config.width,
        height: this.config.height,
        backgroundColor: 0xffffff,
        antialias: true,
        resolution: window.devicePixelRatio || 1,
        autoDensity: true,
        powerPreference: 'high-performance',
        ...this.config.pixiConfig
      };

      this.pixiLayer = new PIXIPerformanceLayer(
        this.canvasElement,
        this.fabricManager.getCanvas(),
        pixiConfig
      );

      this.isWebGLEnabled = true;
      this.emit('webgl:enabled');

      // Forward PIXI events
      this.pixiLayer.on('render:error', (error, task) => {
        this.emit('render:error', error, task);
      });

      this.pixiLayer.on('object:added:pixi', (fabricObj, pixiObj) => {
        this.emit('object:optimized', fabricObj, pixiObj);
      });

    } catch (error) {
      console.warn('WebGL initialization failed, falling back to Canvas 2D:', error);
      this.isWebGLEnabled = false;
      this.emit('webgl:fallback', error);
    }
  }

  private setupEventForwarding(): void {
    // Forward Fabric.js events
    this.fabricManager.on('object:added', (obj) => {
      this.emit('object:added', obj);
    });

    this.fabricManager.on('object:removed', (obj) => {
      this.emit('object:removed', obj);
    });

    this.fabricManager.on('object:modified', (obj) => {
      this.emit('object:modified', obj);
    });

    this.fabricManager.on('selection:created', (objects) => {
      this.emit('selection:created', objects);
    });

    this.fabricManager.on('selection:updated', (objects) => {
      this.emit('selection:updated', objects);
    });
  }

  // Fabric.js methods delegation
  public addObject(object: fabric.Object, options?: { renderImmediately?: boolean }): void {
    this.fabricManager.addObject(object, options);
  }

  public removeObject(objectId: string): void {
    this.fabricManager.removeObject(objectId);
  }

  public updateObject(object: fabric.Object): void {
    this.fabricManager.updateObject(object);
  }

  public integrateWithTool(tool: any): void {
    this.fabricManager.integrateWithTool(tool);
  }

  // Hybrid-specific methods
  public enableHybridRendering(): void {
    this.fabricManager.enableHybridRendering();
    if (this.pixiLayer) {
      this.emit('hybrid:enabled');
    }
  }

  public getPerformanceMetrics(): any {
    if (this.pixiLayer) {
      return this.pixiLayer.getPerformanceMetrics();
    }
    return {
      fps: 60,
      frameTime: 16.67,
      memoryUsage: 0,
      renderCalls: 0,
      objectCount: this.fabricManager.getObjectRegistry().size
    };
  }

  public updateRenderingStrategy(strategy: any): void {
    if (this.pixiLayer) {
      this.pixiLayer.updateRenderingStrategy(strategy);
    }
  }

  public isWebGLSupported(): boolean {
    return this.isWebGLEnabled;
  }

  // Getters for compatibility
  public getCanvas(): fabric.Canvas {
    return this.fabricManager.getCanvas();
  }

  public getCanvasManager(): EnhancedHybridCanvasManager {
    return this.fabricManager;
  }

  public getPixiLayer(): PIXIPerformanceLayer | null {
    return this.pixiLayer;
  }

  public getObjectRegistry(): Map<string, fabric.Object> {
    return this.fabricManager.getObjectRegistry();
  }

  public getLayerManager(): any {
    return this.fabricManager.getLayerManager();
  }

  public dispose(): void {
    this.fabricManager.dispose();
    if (this.pixiLayer) {
      this.pixiLayer.dispose();
    }
    this.removeAllListeners();
  }
}