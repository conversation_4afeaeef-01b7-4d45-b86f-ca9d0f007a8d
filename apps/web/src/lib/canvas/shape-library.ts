import { VectorShape, VectorPoint, VectorPath } from './vector-graphics-engine';

// Shape library types and interfaces
export type ShapeCategory = 'basic' | 'arrows' | 'symbols' | 'icons' | 'decorative' | 'technical' | 'custom';
export type ShapeComplexity = 'simple' | 'medium' | 'complex';

export interface ShapeTemplate {
  id: string;
  name: string;
  description?: string;
  category: ShapeCategory;
  complexity: ShapeComplexity;
  tags: string[];
  thumbnail?: string;
  vectorData: VectorPath | VectorShape;
  parameters?: ShapeParameter[];
  constraints?: ShapeConstraint[];
  metadata: {
    createdAt: number;
    updatedAt: number;
    author?: string;
    version: string;
    downloads: number;
    rating: number;
    size: { width: number; height: number };
  };
}

export interface ShapeParameter {
  name: string;
  type: 'number' | 'boolean' | 'color' | 'select' | 'range';
  defaultValue: any;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{ value: any; label: string }>;
  description?: string;
  affects: string[]; // Which shape properties this parameter affects
}

export interface ShapeConstraint {
  type: 'aspect-ratio' | 'min-size' | 'max-size' | 'symmetric' | 'proportional';
  value: any;
  description?: string;
}

export interface ShapeCreationOptions {
  x: number;
  y: number;
  width?: number;
  height?: number;
  parameters?: Record<string, any>;
  style?: Partial<VectorShape['style']>;
  maintainAspectRatio?: boolean;
}

export interface SmartShapeResult {
  shape: VectorShape;
  parameters: Record<string, any>;
  constraints: ShapeConstraint[];
}

export interface ShapeLibraryConfig {
  enableBuiltInShapes: boolean;
  enableCustomShapes: boolean;
  enableSmartCreation: boolean;
  maxCustomShapes: number;
  categoriesOrder: ShapeCategory[];
  defaultStyle: VectorShape['style'];
}

// Built-in shape generators
export class ShapeGenerators {
  // Basic shapes
  static createRectangle(width: number, height: number, cornerRadius: number = 0): VectorPath {
    const points: VectorPoint[] = [];
    
    if (cornerRadius === 0) {
      // Simple rectangle
      points.push(
        { x: 0, y: 0, type: 'corner' },
        { x: width, y: 0, type: 'corner' },
        { x: width, y: height, type: 'corner' },
        { x: 0, y: height, type: 'corner' }
      );
    } else {
      // Rounded rectangle with bezier curves
      const r = Math.min(cornerRadius, width / 2, height / 2);
      const c = r * 0.552284749831; // Control point offset for circular arc approximation
      
      points.push(
        { x: r, y: 0, type: 'smooth' },
        { 
          x: width - r, 
          y: 0, 
          type: 'smooth',
          handleIn: { x: -c, y: 0 },
          handleOut: { x: c, y: 0 }
        },
        {
          x: width,
          y: r,
          type: 'smooth',
          handleIn: { x: 0, y: -c },
          handleOut: { x: 0, y: c }
        },
        { x: width, y: height - r, type: 'smooth' },
        {
          x: width - r,
          y: height,
          type: 'smooth',
          handleIn: { x: c, y: 0 },
          handleOut: { x: -c, y: 0 }
        },
        {
          x: r,
          y: height,
          type: 'smooth',
          handleIn: { x: c, y: 0 },
          handleOut: { x: -c, y: 0 }
        },
        {
          x: 0,
          y: height - r,
          type: 'smooth',
          handleIn: { x: 0, y: c },
          handleOut: { x: 0, y: -c }
        },
        {
          x: 0,
          y: r,
          type: 'smooth',
          handleIn: { x: 0, y: c },
          handleOut: { x: 0, y: -c }
        }
      );
    }
    
    return {
      id: `rect_${Date.now()}`,
      points,
      closed: true
    };
  }

  static createEllipse(width: number, height: number): VectorPath {
    const rx = width / 2;
    const ry = height / 2;
    const cx = rx;
    const cy = ry;
    const c = 0.552284749831; // Control point offset for circular arc approximation
    
    const points: VectorPoint[] = [
      {
        x: cx + rx,
        y: cy,
        type: 'smooth',
        handleIn: { x: 0, y: -ry * c },
        handleOut: { x: 0, y: ry * c }
      },
      {
        x: cx,
        y: cy + ry,
        type: 'smooth',
        handleIn: { x: rx * c, y: 0 },
        handleOut: { x: -rx * c, y: 0 }
      },
      {
        x: cx - rx,
        y: cy,
        type: 'smooth',
        handleIn: { x: 0, y: ry * c },
        handleOut: { x: 0, y: -ry * c }
      },
      {
        x: cx,
        y: cy - ry,
        type: 'smooth',
        handleIn: { x: -rx * c, y: 0 },
        handleOut: { x: rx * c, y: 0 }
      }
    ];
    
    return {
      id: `ellipse_${Date.now()}`,
      points,
      closed: true
    };
  }

  static createPolygon(sides: number, radius: number, centerX: number = 0, centerY: number = 0): VectorPath {
    const points: VectorPoint[] = [];
    const angleStep = (2 * Math.PI) / sides;
    
    for (let i = 0; i < sides; i++) {
      const angle = i * angleStep - Math.PI / 2; // Start from top
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      
      points.push({
        x,
        y,
        type: 'corner'
      });
    }
    
    return {
      id: `polygon_${sides}_${Date.now()}`,
      points,
      closed: true
    };
  }

  static createStar(outerRadius: number, innerRadius: number, points: number, centerX: number = 0, centerY: number = 0): VectorPath {
    const starPoints: VectorPoint[] = [];
    const angleStep = Math.PI / points;
    
    for (let i = 0; i < points * 2; i++) {
      const angle = i * angleStep - Math.PI / 2;
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      
      starPoints.push({
        x,
        y,
        type: 'corner'
      });
    }
    
    return {
      id: `star_${points}_${Date.now()}`,
      points: starPoints,
      closed: true
    };
  }

  // Arrow shapes
  static createArrow(length: number, width: number, headLength: number, headWidth: number): VectorPath {
    const bodyWidth = width;
    const points: VectorPoint[] = [
      { x: 0, y: bodyWidth / 2, type: 'corner' },
      { x: length - headLength, y: bodyWidth / 2, type: 'corner' },
      { x: length - headLength, y: headWidth / 2, type: 'corner' },
      { x: length, y: 0, type: 'corner' },
      { x: length - headLength, y: -headWidth / 2, type: 'corner' },
      { x: length - headLength, y: -bodyWidth / 2, type: 'corner' },
      { x: 0, y: -bodyWidth / 2, type: 'corner' }
    ];
    
    return {
      id: `arrow_${Date.now()}`,
      points,
      closed: true
    };
  }

  static createDoubleArrow(length: number, width: number, headLength: number, headWidth: number): VectorPath {
    const bodyWidth = width;
    const points: VectorPoint[] = [
      { x: headLength, y: bodyWidth / 2, type: 'corner' },
      { x: length - headLength, y: bodyWidth / 2, type: 'corner' },
      { x: length - headLength, y: headWidth / 2, type: 'corner' },
      { x: length, y: 0, type: 'corner' },
      { x: length - headLength, y: -headWidth / 2, type: 'corner' },
      { x: length - headLength, y: -bodyWidth / 2, type: 'corner' },
      { x: headLength, y: -bodyWidth / 2, type: 'corner' },
      { x: headLength, y: -headWidth / 2, type: 'corner' },
      { x: 0, y: 0, type: 'corner' },
      { x: headLength, y: headWidth / 2, type: 'corner' }
    ];
    
    return {
      id: `double_arrow_${Date.now()}`,
      points,
      closed: true
    };
  }

  // Symbol shapes
  static createHeart(width: number, height: number): VectorPath {
    const w = width;
    const h = height;
    const points: VectorPoint[] = [
      { x: w * 0.5, y: h, type: 'corner' },
      {
        x: w,
        y: h * 0.4,
        type: 'smooth',
        handleIn: { x: w * 0.2, y: h * 0.3 },
        handleOut: { x: 0, y: -h * 0.1 }
      },
      {
        x: w * 0.75,
        y: h * 0.1,
        type: 'smooth',
        handleIn: { x: w * 0.1, y: 0 },
        handleOut: { x: -w * 0.1, y: 0 }
      },
      {
        x: w * 0.5,
        y: h * 0.3,
        type: 'smooth',
        handleIn: { x: 0, y: -h * 0.1 },
        handleOut: { x: 0, y: -h * 0.1 }
      },
      {
        x: w * 0.25,
        y: h * 0.1,
        type: 'smooth',
        handleIn: { x: w * 0.1, y: 0 },
        handleOut: { x: -w * 0.1, y: 0 }
      },
      {
        x: 0,
        y: h * 0.4,
        type: 'smooth',
        handleIn: { x: 0, y: -h * 0.1 },
        handleOut: { x: -w * 0.2, y: h * 0.3 }
      }
    ];
    
    return {
      id: `heart_${Date.now()}`,
      points,
      closed: true
    };
  }

  static createSpeechBubble(width: number, height: number, tailWidth: number, tailHeight: number): VectorPath {
    const cornerRadius = 10;
    const c = cornerRadius * 0.552284749831;
    
    const points: VectorPoint[] = [
      { x: cornerRadius, y: 0, type: 'smooth' },
      {
        x: width - cornerRadius,
        y: 0,
        type: 'smooth',
        handleIn: { x: -c, y: 0 },
        handleOut: { x: c, y: 0 }
      },
      {
        x: width,
        y: cornerRadius,
        type: 'smooth',
        handleIn: { x: 0, y: -c },
        handleOut: { x: 0, y: c }
      },
      { x: width, y: height - cornerRadius - tailHeight, type: 'smooth' },
      {
        x: width - cornerRadius,
        y: height - tailHeight,
        type: 'smooth',
        handleIn: { x: c, y: 0 },
        handleOut: { x: -c, y: 0 }
      },
      { x: width * 0.6, y: height - tailHeight, type: 'corner' },
      { x: width * 0.4, y: height, type: 'corner' },
      { x: width * 0.3, y: height - tailHeight, type: 'corner' },
      {
        x: cornerRadius,
        y: height - tailHeight,
        type: 'smooth',
        handleIn: { x: c, y: 0 },
        handleOut: { x: -c, y: 0 }
      },
      {
        x: 0,
        y: height - cornerRadius - tailHeight,
        type: 'smooth',
        handleIn: { x: 0, y: c },
        handleOut: { x: 0, y: -c }
      },
      { x: 0, y: cornerRadius, type: 'smooth' },
      {
        x: cornerRadius,
        y: 0,
        type: 'smooth',
        handleIn: { x: 0, y: c },
        handleOut: { x: 0, y: -c }
      }
    ];
    
    return {
      id: `speech_bubble_${Date.now()}`,
      points,
      closed: true
    };
  }
}

// Smart shape creation with constraints
export class SmartShapeCreator {
  private constraints: Map<string, ShapeConstraint[]> = new Map();
  
  constructor() {
    this.setupDefaultConstraints();
  }

  private setupDefaultConstraints(): void {
    // Rectangle constraints
    this.constraints.set('rectangle', [
      { type: 'min-size', value: { width: 10, height: 10 }, description: 'Minimum size 10x10' }
    ]);
    
    // Circle/Ellipse constraints
    this.constraints.set('ellipse', [
      { type: 'min-size', value: { width: 10, height: 10 }, description: 'Minimum size 10x10' }
    ]);
    
    // Polygon constraints
    this.constraints.set('polygon', [
      { type: 'min-size', value: { radius: 5 }, description: 'Minimum radius 5' }
    ]);
    
    // Star constraints
    this.constraints.set('star', [
      { type: 'min-size', value: { outerRadius: 10 }, description: 'Minimum outer radius 10' },
      { type: 'proportional', value: 'innerRadius < outerRadius', description: 'Inner radius must be smaller than outer radius' }
    ]);
  }

  createSmartShape(templateId: string, options: ShapeCreationOptions): SmartShapeResult {
    const { x, y, width = 100, height = 100, parameters = {}, style = {} } = options;
    
    let path: VectorPath;
    let appliedConstraints: ShapeConstraint[] = [];
    let finalParameters = { ...parameters };

    switch (templateId) {
      case 'rectangle':
        const cornerRadius = Math.max(0, Math.min(finalParameters.cornerRadius || 0, width / 2, height / 2));
        finalParameters.cornerRadius = cornerRadius;
        path = ShapeGenerators.createRectangle(width, height, cornerRadius);
        appliedConstraints = this.constraints.get('rectangle') || [];
        break;
        
      case 'ellipse':
        path = ShapeGenerators.createEllipse(width, height);
        appliedConstraints = this.constraints.get('ellipse') || [];
        break;
        
      case 'polygon':
        const sides = Math.max(3, finalParameters.sides || 6);
        const radius = Math.max(width, height) / 2;
        finalParameters.sides = sides;
        finalParameters.radius = radius;
        path = ShapeGenerators.createPolygon(sides, radius, width / 2, height / 2);
        appliedConstraints = this.constraints.get('polygon') || [];
        break;
        
      case 'star':
        const starPoints = Math.max(3, finalParameters.points || 5);
        const outerRadius = Math.max(width, height) / 2;
        const innerRadius = Math.min(outerRadius * 0.5, finalParameters.innerRadius || outerRadius * 0.4);
        finalParameters.points = starPoints;
        finalParameters.outerRadius = outerRadius;
        finalParameters.innerRadius = innerRadius;
        path = ShapeGenerators.createStar(outerRadius, innerRadius, starPoints, width / 2, height / 2);
        appliedConstraints = this.constraints.get('star') || [];
        break;
        
      case 'arrow':
        const arrowLength = width;
        const arrowWidth = height * 0.3;
        const headLength = width * 0.3;
        const headWidth = height;
        path = ShapeGenerators.createArrow(arrowLength, arrowWidth, headLength, headWidth);
        break;
        
      case 'heart':
        path = ShapeGenerators.createHeart(width, height);
        break;
        
      case 'speech-bubble':
        const tailWidth = finalParameters.tailWidth || width * 0.2;
        const tailHeight = finalParameters.tailHeight || height * 0.2;
        path = ShapeGenerators.createSpeechBubble(width, height, tailWidth, tailHeight);
        break;
        
      default:
        // Default to rectangle
        path = ShapeGenerators.createRectangle(width, height);
        break;
    }

    // Adjust path position
    path.points.forEach(point => {
      point.x += x;
      point.y += y;
    });

    const shape: VectorShape = {
      id: path.id,
      type: 'path',
      path,
      properties: finalParameters,
      transform: { x, y, scaleX: 1, scaleY: 1, rotation: 0, skewX: 0, skewY: 0 },
      style: {
        stroke: '#000000',
        strokeWidth: 1,
        fill: 'transparent',
        opacity: 1,
        ...style
      },
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        tags: ['smart-shape', templateId]
      }
    };

    return {
      shape,
      parameters: finalParameters,
      constraints: appliedConstraints
    };
  }

  validateConstraints(templateId: string, parameters: Record<string, any>): boolean {
    const constraints = this.constraints.get(templateId);
    if (!constraints) return true;

    return constraints.every(constraint => {
      switch (constraint.type) {
        case 'min-size':
          if (constraint.value.width && parameters.width < constraint.value.width) return false;
          if (constraint.value.height && parameters.height < constraint.value.height) return false;
          if (constraint.value.radius && parameters.radius < constraint.value.radius) return false;
          return true;
          
        case 'proportional':
          // This would require more complex evaluation logic
          return true;
          
        default:
          return true;
      }
    });
  }
}

// Main Shape Library System
export class ShapeLibrary {
  private templates: Map<string, ShapeTemplate> = new Map();
  private categories: Map<ShapeCategory, ShapeTemplate[]> = new Map();
  private customShapes: Map<string, ShapeTemplate> = new Map();
  private smartCreator: SmartShapeCreator;
  private config: ShapeLibraryConfig;

  constructor(config: Partial<ShapeLibraryConfig> = {}) {
    this.config = {
      enableBuiltInShapes: true,
      enableCustomShapes: true,
      enableSmartCreation: true,
      maxCustomShapes: 100,
      categoriesOrder: ['basic', 'arrows', 'symbols', 'icons', 'decorative', 'technical', 'custom'],
      defaultStyle: {
        stroke: '#000000',
        strokeWidth: 1,
        fill: 'transparent',
        opacity: 1
      },
      ...config
    };

    this.smartCreator = new SmartShapeCreator();
    this.initializeCategories();
    
    if (this.config.enableBuiltInShapes) {
      this.loadBuiltInShapes();
    }
  }

  private initializeCategories(): void {
    this.config.categoriesOrder.forEach(category => {
      this.categories.set(category, []);
    });
  }

  private loadBuiltInShapes(): void {
    // Basic shapes
    this.addTemplate({
      id: 'rectangle',
      name: 'Rectangle',
      description: 'Basic rectangle shape with optional rounded corners',
      category: 'basic',
      complexity: 'simple',
      tags: ['rectangle', 'square', 'basic'],
      vectorData: ShapeGenerators.createRectangle(100, 60),
      parameters: [
        {
          name: 'cornerRadius',
          type: 'range',
          defaultValue: 0,
          min: 0,
          max: 50,
          step: 1,
          description: 'Corner radius for rounded corners',
          affects: ['path']
        }
      ],
      constraints: [
        { type: 'min-size', value: { width: 10, height: 10 } }
      ],
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
        downloads: 0,
        rating: 5,
        size: { width: 100, height: 60 }
      }
    });

    this.addTemplate({
      id: 'ellipse',
      name: 'Ellipse',
      description: 'Ellipse and circle shapes',
      category: 'basic',
      complexity: 'simple',
      tags: ['ellipse', 'circle', 'oval', 'basic'],
      vectorData: ShapeGenerators.createEllipse(100, 100),
      constraints: [
        { type: 'min-size', value: { width: 10, height: 10 } }
      ],
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
        downloads: 0,
        rating: 5,
        size: { width: 100, height: 100 }
      }
    });

    this.addTemplate({
      id: 'polygon',
      name: 'Polygon',
      description: 'Regular polygon with configurable number of sides',
      category: 'basic',
      complexity: 'simple',
      tags: ['polygon', 'triangle', 'hexagon', 'basic'],
      vectorData: ShapeGenerators.createPolygon(6, 50),
      parameters: [
        {
          name: 'sides',
          type: 'range',
          defaultValue: 6,
          min: 3,
          max: 20,
          step: 1,
          description: 'Number of sides',
          affects: ['path']
        }
      ],
      constraints: [
        { type: 'min-size', value: { radius: 5 } }
      ],
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
        downloads: 0,
        rating: 5,
        size: { width: 100, height: 100 }
      }
    });

    // Arrow shapes
    this.addTemplate({
      id: 'arrow',
      name: 'Arrow',
      description: 'Basic arrow shape',
      category: 'arrows',
      complexity: 'simple',
      tags: ['arrow', 'direction', 'pointer'],
      vectorData: ShapeGenerators.createArrow(120, 20, 30, 40),
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
        downloads: 0,
        rating: 5,
        size: { width: 120, height: 40 }
      }
    });

    this.addTemplate({
      id: 'double-arrow',
      name: 'Double Arrow',
      description: 'Double-headed arrow shape',
      category: 'arrows',
      complexity: 'simple',
      tags: ['arrow', 'double', 'bidirectional'],
      vectorData: ShapeGenerators.createDoubleArrow(120, 20, 30, 40),
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
        downloads: 0,
        rating: 5,
        size: { width: 120, height: 40 }
      }
    });

    // Symbol shapes
    this.addTemplate({
      id: 'star',
      name: 'Star',
      description: 'Star shape with configurable points',
      category: 'symbols',
      complexity: 'medium',
      tags: ['star', 'symbol', 'rating'],
      vectorData: ShapeGenerators.createStar(50, 20, 5),
      parameters: [
        {
          name: 'points',
          type: 'range',
          defaultValue: 5,
          min: 3,
          max: 12,
          step: 1,
          description: 'Number of star points',
          affects: ['path']
        },
        {
          name: 'innerRadius',
          type: 'range',
          defaultValue: 20,
          min: 5,
          max: 45,
          step: 1,
          description: 'Inner radius',
          affects: ['path']
        }
      ],
      constraints: [
        { type: 'proportional', value: 'innerRadius < outerRadius' }
      ],
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
        downloads: 0,
        rating: 5,
        size: { width: 100, height: 100 }
      }
    });

    this.addTemplate({
      id: 'heart',
      name: 'Heart',
      description: 'Heart symbol shape',
      category: 'symbols',
      complexity: 'medium',
      tags: ['heart', 'love', 'symbol'],
      vectorData: ShapeGenerators.createHeart(80, 70),
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
        downloads: 0,
        rating: 5,
        size: { width: 80, height: 70 }
      }
    });

    this.addTemplate({
      id: 'speech-bubble',
      name: 'Speech Bubble',
      description: 'Speech bubble for text',
      category: 'symbols',
      complexity: 'medium',
      tags: ['speech', 'bubble', 'text', 'communication'],
      vectorData: ShapeGenerators.createSpeechBubble(120, 80, 20, 15),
      parameters: [
        {
          name: 'tailWidth',
          type: 'range',
          defaultValue: 20,
          min: 10,
          max: 40,
          step: 1,
          description: 'Width of speech tail',
          affects: ['path']
        },
        {
          name: 'tailHeight',
          type: 'range',
          defaultValue: 15,
          min: 5,
          max: 30,
          step: 1,
          description: 'Height of speech tail',
          affects: ['path']
        }
      ],
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
        downloads: 0,
        rating: 5,
        size: { width: 120, height: 80 }
      }
    });
  }

  // Template management
  addTemplate(template: Omit<ShapeTemplate, 'id'> & { id?: string }): string {
    const id = template.id || this.generateId();
    const fullTemplate: ShapeTemplate = {
      ...template,
      id,
      metadata: {
        ...template.metadata,
        updatedAt: Date.now()
      }
    };

    this.templates.set(id, fullTemplate);
    
    // Add to category
    const categoryShapes = this.categories.get(template.category) || [];
    categoryShapes.push(fullTemplate);
    this.categories.set(template.category, categoryShapes);

    return id;
  }

  removeTemplate(id: string): boolean {
    const template = this.templates.get(id);
    if (!template) return false;

    // Remove from templates
    this.templates.delete(id);

    // Remove from category
    const categoryShapes = this.categories.get(template.category) || [];
    const index = categoryShapes.findIndex(t => t.id === id);
    if (index !== -1) {
      categoryShapes.splice(index, 1);
    }

    return true;
  }

  updateTemplate(id: string, updates: Partial<ShapeTemplate>): boolean {
    const template = this.templates.get(id);
    if (!template) return false;

    Object.assign(template, {
      ...updates,
      metadata: {
        ...template.metadata,
        ...updates.metadata,
        updatedAt: Date.now()
      }
    });

    return true;
  }

  // Shape creation
  createShape(templateId: string, options: ShapeCreationOptions): SmartShapeResult | null {
    if (!this.config.enableSmartCreation) {
      return null;
    }

    const template = this.templates.get(templateId);
    if (!template) return null;

    // Apply default style
    const mergedOptions = {
      ...options,
      style: {
        ...this.config.defaultStyle,
        ...options.style
      }
    };

    return this.smartCreator.createSmartShape(templateId, mergedOptions);
  }

  // Search and filtering
  searchTemplates(query: string, options: {
    categories?: ShapeCategory[];
    complexity?: ShapeComplexity[];
    tags?: string[];
    includeCustom?: boolean;
  } = {}): ShapeTemplate[] {
    const searchTerms = query.toLowerCase();
    const results: ShapeTemplate[] = [];

    this.templates.forEach(template => {
      // Category filter
      if (options.categories && !options.categories.includes(template.category)) {
        return;
      }

      // Complexity filter
      if (options.complexity && !options.complexity.includes(template.complexity)) {
        return;
      }

      // Custom shapes filter
      if (!options.includeCustom && template.category === 'custom') {
        return;
      }

      // Text search
      const searchableText = [
        template.name,
        template.description || '',
        ...template.tags
      ].join(' ').toLowerCase();

      if (searchableText.includes(searchTerms)) {
        results.push(template);
      }

      // Tag filter
      if (options.tags && options.tags.length > 0) {
        const hasMatchingTag = options.tags.some(tag => 
          template.tags.includes(tag.toLowerCase())
        );
        if (!hasMatchingTag) {
          results.splice(results.indexOf(template), 1);
        }
      }
    });

    return results.sort((a, b) => {
      // Sort by downloads and rating
      const aScore = a.metadata.downloads * a.metadata.rating;
      const bScore = b.metadata.downloads * b.metadata.rating;
      return bScore - aScore;
    });
  }

  getTemplatesByCategory(category: ShapeCategory): ShapeTemplate[] {
    return this.categories.get(category) || [];
  }

  getAllCategories(): Array<{ category: ShapeCategory; count: number }> {
    return this.config.categoriesOrder.map(category => ({
      category,
      count: (this.categories.get(category) || []).length
    }));
  }

  // Custom shapes
  saveCustomShape(shape: VectorShape, metadata: Partial<ShapeTemplate['metadata']> = {}): string {
    if (!this.config.enableCustomShapes) {
      throw new Error('Custom shapes are disabled');
    }

    if (this.customShapes.size >= this.config.maxCustomShapes) {
      throw new Error(`Maximum custom shapes limit reached (${this.config.maxCustomShapes})`);
    }

    const template: ShapeTemplate = {
      id: this.generateId(),
      name: metadata.author ? `${metadata.author}'s Shape` : 'Custom Shape',
      description: 'User-created custom shape',
      category: 'custom',
      complexity: 'simple',
      tags: ['custom', 'user-created'],
      vectorData: shape,
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        downloads: 0,
        rating: 5,
        size: { width: 100, height: 100 },
        ...metadata
      }
    };

    this.customShapes.set(template.id, template);
    this.addTemplate(template);

    return template.id;
  }

  exportCustomShapes(): string {
    const customShapes = Array.from(this.customShapes.values());
    return JSON.stringify(customShapes, null, 2);
  }

  importCustomShapes(data: string): number {
    try {
      const shapes: ShapeTemplate[] = JSON.parse(data);
      let imported = 0;

      shapes.forEach(shape => {
        if (this.customShapes.size < this.config.maxCustomShapes) {
          shape.id = this.generateId(); // Generate new ID to avoid conflicts
          shape.category = 'custom';
          this.customShapes.set(shape.id, shape);
          this.addTemplate(shape);
          imported++;
        }
      });

      return imported;
    } catch (error) {
      throw new Error('Invalid custom shapes data format');
    }
  }

  // Utility methods
  getTemplate(id: string): ShapeTemplate | undefined {
    return this.templates.get(id);
  }

  getAllTemplates(): ShapeTemplate[] {
    return Array.from(this.templates.values());
  }

  getTemplateCount(): number {
    return this.templates.size;
  }

  getConfig(): ShapeLibraryConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<ShapeLibraryConfig>): void {
    Object.assign(this.config, updates);
  }

  private generateId(): string {
    return `shape_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Cleanup
  dispose(): void {
    this.templates.clear();
    this.categories.clear();
    this.customShapes.clear();
  }
}