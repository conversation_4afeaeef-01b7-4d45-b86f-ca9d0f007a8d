import { fabric } from 'fabric';
import { EventEmitter } from 'events';

// Vector graphics types and interfaces
export type VectorTool = 'select' | 'pen' | 'bezier' | 'rectangle' | 'ellipse' | 'polygon' | 'line' | 'pencil' | 'brush';
export type VectorNodeType = 'anchor' | 'control' | 'curve' | 'corner' | 'smooth';
export type PathOperation = 'union' | 'subtract' | 'intersect' | 'exclude' | 'divide';

export interface VectorPoint {
  x: number;
  y: number;
  type?: VectorNodeType;
  handleIn?: { x: number; y: number };
  handleOut?: { x: number; y: number };
  selected?: boolean;
}

export interface VectorPath {
  id: string;
  points: VectorPoint[];
  closed: boolean;
  strokeColor?: string;
  strokeWidth?: number;
  fillColor?: string;
  strokeDashArray?: number[];
  strokeLineCap?: 'round' | 'square' | 'butt';
  strokeLineJoin?: 'round' | 'bevel' | 'miter';
}

export interface VectorShape {
  id: string;
  type: 'path' | 'rectangle' | 'ellipse' | 'polygon' | 'polyline';
  path?: VectorPath;
  properties: Record<string, any>;
  transform: {
    x: number;
    y: number;
    scaleX: number;
    scaleY: number;
    rotation: number;
    skewX: number;
    skewY: number;
  };
  style: {
    stroke?: string;
    strokeWidth?: number;
    fill?: string;
    opacity?: number;
    strokeDashArray?: number[];
    strokeLineCap?: 'round' | 'square' | 'butt';
    strokeLineJoin?: 'round' | 'bevel' | 'miter';
  };
  metadata: {
    createdAt: number;
    updatedAt: number;
    author?: string;
    tags: string[];
  };
}

export interface BezierCurve {
  start: VectorPoint;
  control1: VectorPoint;
  control2: VectorPoint;
  end: VectorPoint;
}

export interface VectorSelection {
  selectedObjects: string[];
  selectedNodes: Array<{ objectId: string; nodeIndex: number }>;
  bounds?: fabric.Rect;
  manipulationMode: 'object' | 'node' | 'path';
}

export interface VectorEditingOptions {
  snapToGrid: boolean;
  snapToGuides: boolean;
  snapToObjects: boolean;
  snapTolerance: number;
  showGrid: boolean;
  gridSize: number;
  showGuides: boolean;
  showNodes: boolean;
  showHandles: boolean;
  nodeSize: number;
  handleSize: number;
}

export interface VectorOperationResult {
  success: boolean;
  resultPaths?: VectorPath[];
  error?: string;
  metadata?: Record<string, any>;
}

// Advanced path editing and manipulation
export class PathEditor {
  private path: VectorPath;
  private isDirty: boolean = false;

  constructor(path: VectorPath) {
    this.path = { ...path };
  }

  // Node manipulation
  addNode(index: number, point: VectorPoint): void {
    this.path.points.splice(index, 0, point);
    this.isDirty = true;
  }

  removeNode(index: number): boolean {
    if (index < 0 || index >= this.path.points.length) return false;
    this.path.points.splice(index, 1);
    this.isDirty = true;
    return true;
  }

  updateNode(index: number, updates: Partial<VectorPoint>): boolean {
    if (index < 0 || index >= this.path.points.length) return false;
    Object.assign(this.path.points[index], updates);
    this.isDirty = true;
    return true;
  }

  // Bezier curve manipulation
  updateBezierHandle(nodeIndex: number, handleType: 'in' | 'out', position: { x: number; y: number }): void {
    const node = this.path.points[nodeIndex];
    if (!node) return;

    if (handleType === 'in') {
      node.handleIn = position;
    } else {
      node.handleOut = position;
    }

    // Maintain smooth curves if node type is smooth
    if (node.type === 'smooth') {
      this.maintainSmoothHandles(nodeIndex);
    }

    this.isDirty = true;
  }

  private maintainSmoothHandles(nodeIndex: number): void {
    const node = this.path.points[nodeIndex];
    if (!node || !node.handleIn || !node.handleOut) return;

    // Calculate opposite handle to maintain smooth transition
    const inLength = Math.sqrt(node.handleIn.x ** 2 + node.handleIn.y ** 2);
    const outLength = Math.sqrt(node.handleOut.x ** 2 + node.handleOut.y ** 2);
    
    if (inLength > 0) {
      const ratio = outLength / inLength;
      node.handleOut = {
        x: -node.handleIn.x * ratio,
        y: -node.handleIn.y * ratio
      };
    }
  }

  // Path operations
  closePath(): void {
    this.path.closed = true;
    this.isDirty = true;
  }

  openPath(): void {
    this.path.closed = false;
    this.isDirty = true;
  }

  reversePath(): void {
    this.path.points.reverse();
    // Swap handle directions
    this.path.points.forEach(point => {
      if (point.handleIn && point.handleOut) {
        const temp = point.handleIn;
        point.handleIn = point.handleOut;
        point.handleOut = temp;
      }
    });
    this.isDirty = true;
  }

  // Path simplification
  simplifyPath(tolerance: number = 1.0): void {
    if (this.path.points.length <= 2) return;

    const simplified: VectorPoint[] = [this.path.points[0]];
    
    for (let i = 1; i < this.path.points.length - 1; i++) {
      const prev = this.path.points[i - 1];
      const curr = this.path.points[i];
      const next = this.path.points[i + 1];
      
      // Calculate perpendicular distance from current point to line between prev and next
      const distance = this.perpendicularDistance(curr, prev, next);
      
      if (distance > tolerance) {
        simplified.push(curr);
      }
    }
    
    simplified.push(this.path.points[this.path.points.length - 1]);
    this.path.points = simplified;
    this.isDirty = true;
  }

  private perpendicularDistance(point: VectorPoint, lineStart: VectorPoint, lineEnd: VectorPoint): number {
    const dx = lineEnd.x - lineStart.x;
    const dy = lineEnd.y - lineStart.y;
    const length = Math.sqrt(dx * dx + dy * dy);
    
    if (length === 0) return 0;
    
    return Math.abs((dy * point.x - dx * point.y + lineEnd.x * lineStart.y - lineEnd.y * lineStart.x) / length);
  }

  // Convert to SVG path string
  toSVGPath(): string {
    if (this.path.points.length === 0) return '';

    let d = `M ${this.path.points[0].x} ${this.path.points[0].y}`;

    for (let i = 1; i < this.path.points.length; i++) {
      const prev = this.path.points[i - 1];
      const curr = this.path.points[i];

      if (prev.handleOut && curr.handleIn) {
        // Bezier curve
        d += ` C ${prev.x + prev.handleOut.x} ${prev.y + prev.handleOut.y} ${curr.x + curr.handleIn.x} ${curr.y + curr.handleIn.y} ${curr.x} ${curr.y}`;
      } else {
        // Linear line
        d += ` L ${curr.x} ${curr.y}`;
      }
    }

    if (this.path.closed) {
      d += ' Z';
    }

    return d;
  }

  getPath(): VectorPath {
    return { ...this.path };
  }

  isDirtyPath(): boolean {
    return this.isDirty;
  }

  markClean(): void {
    this.isDirty = false;
  }
}

// Boolean path operations
export class PathOperations {
  static union(path1: VectorPath, path2: VectorPath): VectorOperationResult {
    try {
      // Convert paths to fabric.js objects for boolean operations
      const fabricPath1 = PathOperations.createFabricPath(path1);
      const fabricPath2 = PathOperations.createFabricPath(path2);

      // Perform union operation using fabric.js path utilities
      // This is a simplified implementation - in practice would use clipper-lib or similar
      const result = PathOperations.performBooleanOperation(fabricPath1, fabricPath2, 'union');
      
      return {
        success: true,
        resultPaths: result ? [PathOperations.fabricPathToVectorPath(result)] : [],
        metadata: { operation: 'union', timestamp: Date.now() }
      };
    } catch (error) {
      return {
        success: false,
        error: `Union operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  static subtract(path1: VectorPath, path2: VectorPath): VectorOperationResult {
    try {
      const fabricPath1 = PathOperations.createFabricPath(path1);
      const fabricPath2 = PathOperations.createFabricPath(path2);

      const result = PathOperations.performBooleanOperation(fabricPath1, fabricPath2, 'subtract');
      
      return {
        success: true,
        resultPaths: result ? [PathOperations.fabricPathToVectorPath(result)] : [],
        metadata: { operation: 'subtract', timestamp: Date.now() }
      };
    } catch (error) {
      return {
        success: false,
        error: `Subtract operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  static intersect(path1: VectorPath, path2: VectorPath): VectorOperationResult {
    try {
      const fabricPath1 = PathOperations.createFabricPath(path1);
      const fabricPath2 = PathOperations.createFabricPath(path2);

      const result = PathOperations.performBooleanOperation(fabricPath1, fabricPath2, 'intersect');
      
      return {
        success: true,
        resultPaths: result ? [PathOperations.fabricPathToVectorPath(result)] : [],
        metadata: { operation: 'intersect', timestamp: Date.now() }
      };
    } catch (error) {
      return {
        success: false,
        error: `Intersect operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  static exclude(path1: VectorPath, path2: VectorPath): VectorOperationResult {
    try {
      const fabricPath1 = PathOperations.createFabricPath(path1);
      const fabricPath2 = PathOperations.createFabricPath(path2);

      const result = PathOperations.performBooleanOperation(fabricPath1, fabricPath2, 'exclude');
      
      return {
        success: true,
        resultPaths: result ? [PathOperations.fabricPathToVectorPath(result)] : [],
        metadata: { operation: 'exclude', timestamp: Date.now() }
      };
    } catch (error) {
      return {
        success: false,
        error: `Exclude operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private static createFabricPath(vectorPath: VectorPath): fabric.Path {
    const pathEditor = new PathEditor(vectorPath);
    const svgPath = pathEditor.toSVGPath();
    
    return new fabric.Path(svgPath, {
      fill: vectorPath.fillColor || '',
      stroke: vectorPath.strokeColor || '',
      strokeWidth: vectorPath.strokeWidth || 1
    });
  }

  private static performBooleanOperation(path1: fabric.Path, path2: fabric.Path, operation: PathOperation): fabric.Path | null {
    // This is a placeholder for actual boolean path operations
    // In a real implementation, you would use libraries like:
    // - clipper-lib for polygon clipping
    // - paper.js for path operations
    // - or implement custom path intersection algorithms
    
    console.log(`Performing ${operation} operation between paths`);
    
    // For now, return the first path as a placeholder
    return path1;
  }

  private static fabricPathToVectorPath(fabricPath: fabric.Path): VectorPath {
    // Convert fabric.js path back to VectorPath format
    // This is a simplified conversion
    const pathData = fabricPath.path;
    const points: VectorPoint[] = [];
    
    // Parse SVG path data and convert to VectorPoint array
    // This would require a proper SVG path parser
    
    return {
      id: `path_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      points,
      closed: true,
      strokeColor: fabricPath.stroke as string,
      strokeWidth: fabricPath.strokeWidth,
      fillColor: fabricPath.fill as string
    };
  }
}

// Main Vector Graphics Engine
export class VectorGraphicsEngine extends EventEmitter {
  private canvas: fabric.Canvas;
  private vectorObjects: Map<string, VectorShape> = new Map();
  private pathEditors: Map<string, PathEditor> = new Map();
  private currentTool: VectorTool = 'select';
  private selection: VectorSelection = {
    selectedObjects: [],
    selectedNodes: [],
    manipulationMode: 'object'
  };
  private editingOptions: VectorEditingOptions;
  private isDrawing: boolean = false;
  private currentPath?: VectorPath;
  private snapGuides: fabric.Line[] = [];

  constructor(canvas: fabric.Canvas, options: Partial<VectorEditingOptions> = {}) {
    super();
    this.canvas = canvas;
    this.editingOptions = {
      snapToGrid: true,
      snapToGuides: true,
      snapToObjects: true,
      snapTolerance: 5,
      showGrid: true,
      gridSize: 20,
      showGuides: true,
      showNodes: true,
      showHandles: true,
      nodeSize: 6,
      handleSize: 4,
      ...options
    };

    this.setupCanvasEvents();
    this.setupGrid();
  }

  private setupCanvasEvents(): void {
    this.canvas.on('mouse:down', this.handleMouseDown.bind(this));
    this.canvas.on('mouse:move', this.handleMouseMove.bind(this));
    this.canvas.on('mouse:up', this.handleMouseUp.bind(this));
    this.canvas.on('path:created', this.handlePathCreated.bind(this));
    this.canvas.on('selection:created', this.handleSelectionCreated.bind(this));
    this.canvas.on('selection:updated', this.handleSelectionUpdated.bind(this));
    this.canvas.on('selection:cleared', this.handleSelectionCleared.bind(this));
  }

  private setupGrid(): void {
    if (!this.editingOptions.showGrid) return;

    const { gridSize } = this.editingOptions;
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();

    // Create grid lines
    for (let i = 0; i <= canvasWidth; i += gridSize) {
      const line = new fabric.Line([i, 0, i, canvasHeight], {
        stroke: '#e0e0e0',
        strokeWidth: 0.5,
        selectable: false,
        evented: false,
        excludeFromExport: true
      });
      this.canvas.add(line);
      this.canvas.sendToBack(line);
    }

    for (let i = 0; i <= canvasHeight; i += gridSize) {
      const line = new fabric.Line([0, i, canvasWidth, i], {
        stroke: '#e0e0e0',
        strokeWidth: 0.5,
        selectable: false,
        evented: false,
        excludeFromExport: true
      });
      this.canvas.add(line);
      this.canvas.sendToBack(line);
    }
  }

  // Tool management
  setTool(tool: VectorTool): void {
    this.currentTool = tool;
    this.canvas.isDrawingMode = tool === 'pencil' || tool === 'brush';
    
    if (tool === 'pen') {
      this.canvas.defaultCursor = 'crosshair';
    } else if (tool === 'select') {
      this.canvas.defaultCursor = 'default';
    }

    this.emit('tool:changed', tool);
  }

  getCurrentTool(): VectorTool {
    return this.currentTool;
  }

  // Shape creation methods
  createRectangle(x: number, y: number, width: number, height: number, style: Partial<VectorShape['style']> = {}): VectorShape {
    const shape: VectorShape = {
      id: this.generateId(),
      type: 'rectangle',
      properties: { x, y, width, height },
      transform: { x, y, scaleX: 1, scaleY: 1, rotation: 0, skewX: 0, skewY: 0 },
      style: {
        stroke: '#000000',
        strokeWidth: 1,
        fill: 'transparent',
        opacity: 1,
        ...style
      },
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        tags: ['rectangle', 'shape']
      }
    };

    const fabricRect = new fabric.Rect({
      left: x,
      top: y,
      width,
      height,
      stroke: shape.style.stroke,
      strokeWidth: shape.style.strokeWidth,
      fill: shape.style.fill,
      opacity: shape.style.opacity
    });

    (fabricRect as any).vectorId = shape.id;
    this.canvas.add(fabricRect);
    this.vectorObjects.set(shape.id, shape);

    this.emit('shape:created', shape);
    return shape;
  }

  createEllipse(x: number, y: number, rx: number, ry: number, style: Partial<VectorShape['style']> = {}): VectorShape {
    const shape: VectorShape = {
      id: this.generateId(),
      type: 'ellipse',
      properties: { x, y, rx, ry },
      transform: { x, y, scaleX: 1, scaleY: 1, rotation: 0, skewX: 0, skewY: 0 },
      style: {
        stroke: '#000000',
        strokeWidth: 1,
        fill: 'transparent',
        opacity: 1,
        ...style
      },
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        tags: ['ellipse', 'shape']
      }
    };

    const fabricEllipse = new fabric.Ellipse({
      left: x - rx,
      top: y - ry,
      rx,
      ry,
      stroke: shape.style.stroke,
      strokeWidth: shape.style.strokeWidth,
      fill: shape.style.fill,
      opacity: shape.style.opacity
    });

    (fabricEllipse as any).vectorId = shape.id;
    this.canvas.add(fabricEllipse);
    this.vectorObjects.set(shape.id, shape);

    this.emit('shape:created', shape);
    return shape;
  }

  createPolygon(points: VectorPoint[], style: Partial<VectorShape['style']> = {}): VectorShape {
    const shape: VectorShape = {
      id: this.generateId(),
      type: 'polygon',
      properties: { points },
      transform: { x: 0, y: 0, scaleX: 1, scaleY: 1, rotation: 0, skewX: 0, skewY: 0 },
      style: {
        stroke: '#000000',
        strokeWidth: 1,
        fill: 'transparent',
        opacity: 1,
        ...style
      },
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        tags: ['polygon', 'shape']
      }
    };

    const fabricPoints = points.map(p => ({ x: p.x, y: p.y }));
    const fabricPolygon = new fabric.Polygon(fabricPoints, {
      stroke: shape.style.stroke,
      strokeWidth: shape.style.strokeWidth,
      fill: shape.style.fill,
      opacity: shape.style.opacity
    });

    (fabricPolygon as any).vectorId = shape.id;
    this.canvas.add(fabricPolygon);
    this.vectorObjects.set(shape.id, shape);

    this.emit('shape:created', shape);
    return shape;
  }

  // Path creation and editing
  startPath(startPoint: VectorPoint): void {
    this.currentPath = {
      id: this.generateId(),
      points: [startPoint],
      closed: false
    };
    this.isDrawing = true;
    this.emit('path:started', this.currentPath);
  }

  addPathPoint(point: VectorPoint): void {
    if (!this.currentPath) return;
    
    this.currentPath.points.push(point);
    this.emit('path:point:added', { path: this.currentPath, point });
  }

  finishPath(): VectorShape | null {
    if (!this.currentPath || this.currentPath.points.length < 2) {
      this.isDrawing = false;
      this.currentPath = undefined;
      return null;
    }

    const shape: VectorShape = {
      id: this.currentPath.id,
      type: 'path',
      path: this.currentPath,
      properties: {},
      transform: { x: 0, y: 0, scaleX: 1, scaleY: 1, rotation: 0, skewX: 0, skewY: 0 },
      style: {
        stroke: '#000000',
        strokeWidth: 1,
        fill: 'transparent',
        opacity: 1
      },
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        tags: ['path', 'vector']
      }
    };

    // Create fabric.js path object
    const pathEditor = new PathEditor(this.currentPath);
    const svgPath = pathEditor.toSVGPath();
    
    const fabricPath = new fabric.Path(svgPath, {
      stroke: shape.style.stroke,
      strokeWidth: shape.style.strokeWidth,
      fill: shape.style.fill,
      opacity: shape.style.opacity
    });

    (fabricPath as any).vectorId = shape.id;
    this.canvas.add(fabricPath);
    this.vectorObjects.set(shape.id, shape);
    this.pathEditors.set(shape.id, pathEditor);

    this.isDrawing = false;
    this.currentPath = undefined;

    this.emit('path:finished', shape);
    return shape;
  }

  // Path editing
  editPath(shapeId: string): PathEditor | null {
    const shape = this.vectorObjects.get(shapeId);
    if (!shape || !shape.path) return null;

    let pathEditor = this.pathEditors.get(shapeId);
    if (!pathEditor) {
      pathEditor = new PathEditor(shape.path);
      this.pathEditors.set(shapeId, pathEditor);
    }

    this.selection.manipulationMode = 'path';
    this.showPathNodes(shape.path);

    return pathEditor;
  }

  private showPathNodes(path: VectorPath): void {
    if (!this.editingOptions.showNodes) return;

    // Remove existing node visuals
    this.clearNodeVisuals();

    // Create node visuals
    path.points.forEach((point, index) => {
      const nodeCircle = new fabric.Circle({
        left: point.x - this.editingOptions.nodeSize / 2,
        top: point.y - this.editingOptions.nodeSize / 2,
        radius: this.editingOptions.nodeSize / 2,
        fill: point.selected ? '#ff6b35' : '#ffffff',
        stroke: '#333333',
        strokeWidth: 1,
        selectable: true,
        hasControls: false,
        hasBorders: false,
        excludeFromExport: true
      });

      (nodeCircle as any).isVectorNode = true;
      (nodeCircle as any).nodeIndex = index;
      this.canvas.add(nodeCircle);

      // Show bezier handles if they exist
      if (this.editingOptions.showHandles) {
        if (point.handleIn) {
          this.createBezierHandle(point, point.handleIn, 'in', index);
        }
        if (point.handleOut) {
          this.createBezierHandle(point, point.handleOut, 'out', index);
        }
      }
    });

    this.canvas.renderAll();
  }

  private createBezierHandle(anchor: VectorPoint, handle: { x: number; y: number }, type: 'in' | 'out', nodeIndex: number): void {
    const handleX = anchor.x + handle.x;
    const handleY = anchor.y + handle.y;

    // Handle line
    const handleLine = new fabric.Line([anchor.x, anchor.y, handleX, handleY], {
      stroke: '#666666',
      strokeWidth: 1,
      selectable: false,
      evented: false,
      excludeFromExport: true
    });

    // Handle point
    const handleCircle = new fabric.Circle({
      left: handleX - this.editingOptions.handleSize / 2,
      top: handleY - this.editingOptions.handleSize / 2,
      radius: this.editingOptions.handleSize / 2,
      fill: '#ffffff',
      stroke: '#666666',
      strokeWidth: 1,
      selectable: true,
      hasControls: false,
      hasBorders: false,
      excludeFromExport: true
    });

    (handleLine as any).isVectorHandle = true;
    (handleCircle as any).isVectorHandle = true;
    (handleCircle as any).handleType = type;
    (handleCircle as any).nodeIndex = nodeIndex;

    this.canvas.add(handleLine);
    this.canvas.add(handleCircle);
  }

  private clearNodeVisuals(): void {
    const nodesToRemove = this.canvas.getObjects().filter(obj => 
      (obj as any).isVectorNode || (obj as any).isVectorHandle
    );
    
    nodesToRemove.forEach(node => this.canvas.remove(node));
  }

  // Boolean operations
  performBooleanOperation(shape1Id: string, shape2Id: string, operation: PathOperation): VectorOperationResult {
    const shape1 = this.vectorObjects.get(shape1Id);
    const shape2 = this.vectorObjects.get(shape2Id);

    if (!shape1?.path || !shape2?.path) {
      return {
        success: false,
        error: 'Both shapes must have paths for boolean operations'
      };
    }

    let result: VectorOperationResult;

    switch (operation) {
      case 'union':
        result = PathOperations.union(shape1.path, shape2.path);
        break;
      case 'subtract':
        result = PathOperations.subtract(shape1.path, shape2.path);
        break;
      case 'intersect':
        result = PathOperations.intersect(shape1.path, shape2.path);
        break;
      case 'exclude':
        result = PathOperations.exclude(shape1.path, shape2.path);
        break;
      default:
        return {
          success: false,
          error: `Unknown operation: ${operation}`
        };
    }

    if (result.success && result.resultPaths) {
      // Create new shapes from result paths
      result.resultPaths.forEach(path => {
        const newShape: VectorShape = {
          id: path.id,
          type: 'path',
          path,
          properties: {},
          transform: { x: 0, y: 0, scaleX: 1, scaleY: 1, rotation: 0, skewX: 0, skewY: 0 },
          style: {
            stroke: shape1.style.stroke,
            strokeWidth: shape1.style.strokeWidth,
            fill: shape1.style.fill,
            opacity: shape1.style.opacity
          },
          metadata: {
            createdAt: Date.now(),
            updatedAt: Date.now(),
            tags: ['path', 'boolean-result', operation]
          }
        };

        this.vectorObjects.set(newShape.id, newShape);
        this.emit('boolean:operation:completed', { operation, result: newShape });
      });
    }

    return result;
  }

  // Event handlers
  private handleMouseDown(e: fabric.IEvent): void {
    if (!e.pointer) return;

    const pointer = this.snapToGrid(e.pointer);

    switch (this.currentTool) {
      case 'pen':
        this.handlePenTool(pointer);
        break;
      case 'rectangle':
        this.handleRectangleTool(pointer);
        break;
      case 'ellipse':
        this.handleEllipseTool(pointer);
        break;
    }
  }

  private handleMouseMove(e: fabric.IEvent): void {
    if (!this.isDrawing || !e.pointer) return;

    const pointer = this.snapToGrid(e.pointer);
    
    if (this.currentTool === 'pen' && this.currentPath) {
      // Update current path preview
      this.emit('path:preview:update', { path: this.currentPath, pointer });
    }
  }

  private handleMouseUp(e: fabric.IEvent): void {
    // Handle mouse up events for different tools
  }

  private handlePenTool(pointer: fabric.Point): void {
    if (!this.isDrawing) {
      const startPoint: VectorPoint = {
        x: pointer.x,
        y: pointer.y,
        type: 'anchor'
      };
      this.startPath(startPoint);
    } else if (this.currentPath) {
      const newPoint: VectorPoint = {
        x: pointer.x,
        y: pointer.y,
        type: 'anchor'
      };
      this.addPathPoint(newPoint);
    }
  }

  private handleRectangleTool(pointer: fabric.Point): void {
    // Create rectangle at click position with default size
    this.createRectangle(pointer.x - 50, pointer.y - 30, 100, 60);
  }

  private handleEllipseTool(pointer: fabric.Point): void {
    // Create ellipse at click position with default size
    this.createEllipse(pointer.x, pointer.y, 50, 30);
  }

  private handlePathCreated(e: any): void {
    // Handle fabric.js path creation (for pencil/brush tools)
    if (e.path) {
      const shape: VectorShape = {
        id: this.generateId(),
        type: 'path',
        properties: {},
        transform: { x: 0, y: 0, scaleX: 1, scaleY: 1, rotation: 0, skewX: 0, skewY: 0 },
        style: {
          stroke: e.path.stroke || '#000000',
          strokeWidth: e.path.strokeWidth || 1,
          fill: 'transparent',
          opacity: 1
        },
        metadata: {
          createdAt: Date.now(),
          updatedAt: Date.now(),
          tags: ['path', 'freehand']
        }
      };

      (e.path as any).vectorId = shape.id;
      this.vectorObjects.set(shape.id, shape);
      this.emit('shape:created', shape);
    }
  }

  private handleSelectionCreated(e: any): void {
    this.updateSelectionFromFabric(e);
  }

  private handleSelectionUpdated(e: any): void {
    this.updateSelectionFromFabric(e);
  }

  private handleSelectionCleared(): void {
    this.selection.selectedObjects = [];
    this.selection.selectedNodes = [];
    this.clearNodeVisuals();
    this.emit('selection:changed', this.selection);
  }

  private updateSelectionFromFabric(e: any): void {
    const selectedObjects: string[] = [];
    
    if (e.selected) {
      e.selected.forEach((obj: any) => {
        if (obj.vectorId) {
          selectedObjects.push(obj.vectorId);
        }
      });
    }

    this.selection.selectedObjects = selectedObjects;
    this.emit('selection:changed', this.selection);
  }

  // Utility methods
  private snapToGrid(point: fabric.Point): fabric.Point {
    if (!this.editingOptions.snapToGrid) return point;

    const { gridSize } = this.editingOptions;
    return new fabric.Point(
      Math.round(point.x / gridSize) * gridSize,
      Math.round(point.y / gridSize) * gridSize
    );
  }

  private generateId(): string {
    return `vector_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public API
  getVectorShape(id: string): VectorShape | undefined {
    return this.vectorObjects.get(id);
  }

  getAllVectorShapes(): VectorShape[] {
    return Array.from(this.vectorObjects.values());
  }

  deleteVectorShape(id: string): boolean {
    const shape = this.vectorObjects.get(id);
    if (!shape) return false;

    // Remove from canvas
    const fabricObjects = this.canvas.getObjects().filter(obj => (obj as any).vectorId === id);
    fabricObjects.forEach(obj => this.canvas.remove(obj));

    // Clean up
    this.vectorObjects.delete(id);
    this.pathEditors.delete(id);

    this.emit('shape:deleted', { id, shape });
    return true;
  }

  updateVectorShape(id: string, updates: Partial<VectorShape>): boolean {
    const shape = this.vectorObjects.get(id);
    if (!shape) return false;

    Object.assign(shape, updates);
    shape.metadata.updatedAt = Date.now();

    // Update corresponding fabric.js object
    const fabricObjects = this.canvas.getObjects().filter(obj => (obj as any).vectorId === id);
    fabricObjects.forEach(obj => {
      if (updates.style) {
        obj.set({
          stroke: updates.style.stroke,
          strokeWidth: updates.style.strokeWidth,
          fill: updates.style.fill,
          opacity: updates.style.opacity
        });
      }
    });

    this.canvas.renderAll();
    this.emit('shape:updated', shape);
    return true;
  }

  getSelection(): VectorSelection {
    return { ...this.selection };
  }

  setEditingOptions(options: Partial<VectorEditingOptions>): void {
    Object.assign(this.editingOptions, options);
    
    if (options.showGrid !== undefined) {
      this.setupGrid();
    }
    
    this.emit('options:updated', this.editingOptions);
  }

  exportToSVG(): string {
    const shapes = Array.from(this.vectorObjects.values());
    let svgContent = '';

    shapes.forEach(shape => {
      if (shape.path) {
        const pathEditor = new PathEditor(shape.path);
        const pathData = pathEditor.toSVGPath();
        svgContent += `<path d="${pathData}" stroke="${shape.style.stroke}" stroke-width="${shape.style.strokeWidth}" fill="${shape.style.fill}" opacity="${shape.style.opacity}"/>`;
      }
    });

    return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${this.canvas.getWidth()} ${this.canvas.getHeight()}">${svgContent}</svg>`;
  }

  // Cleanup
  dispose(): void {
    this.clearNodeVisuals();
    this.vectorObjects.clear();
    this.pathEditors.clear();
    this.removeAllListeners();
  }
}