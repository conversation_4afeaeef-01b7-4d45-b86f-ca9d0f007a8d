import { EventEmitter } from 'events';
import { AdvancedCanvasOperationsManager } from './advanced-canvas-operations';
import { EnhancedHybridCanvasManager } from './enhanced-hybrid-canvas';
import { AdvancedLayerManager } from './advanced-layer-manager';
import { FilterPipelineEngine } from './filter-pipeline-engine';
import { EffectsManager } from './effects-manager';

// Performance optimization types
export interface PerformanceProfile {
  id: string;
  name: string;
  description: string;
  settings: {
    renderMode: 'high-quality' | 'balanced' | 'performance';
    textureQuality: 'full' | 'half' | 'quarter';
    antiAliasing: 'none' | 'fxaa' | 'msaa-2x' | 'msaa-4x';
    shadows: 'none' | 'low' | 'medium' | 'high';
    postProcessing: 'none' | 'basic' | 'full';
    particleQuality: 'low' | 'medium' | 'high';
    maxLayers: number;
    maxEffects: number;
    cacheSize: number;
    enableGPUAcceleration: boolean;
    enableWebGL2: boolean;
    enableOffscreenCanvas: boolean;
    enableParallelProcessing: boolean;
    memoryLimit: number;
    frameRateTarget: number;
  };
  triggers: {
    memoryThreshold: number;
    frameRateThreshold: number;
    renderTimeThreshold: number;
    loadTimeThreshold: number;
  };
  optimizations: PerformanceOptimization[];
}

export interface PerformanceOptimization {
  id: string;
  name: string;
  description: string;
  category: 'memory' | 'rendering' | 'processing' | 'network' | 'storage';
  priority: number;
  enabled: boolean;
  conditions: Array<{
    metric: string;
    operator: 'greater-than' | 'less-than' | 'equals';
    value: number;
    duration?: number;
  }>;
  actions: Array<{
    type: 'reduce-quality' | 'clear-cache' | 'optimize-textures' | 'disable-features' | 'custom';
    parameters: Record<string, any>;
    duration?: number;
    reversible: boolean;
  }>;
}

export interface PerformanceMetrics {
  timestamp: number;
  frameRate: number;
  renderTime: number;
  memoryUsage: {
    total: number;
    textures: number;
    objects: number;
    cache: number;
  };
  gpuMemory: {
    used: number;
    total: number;
    textures: number;
    buffers: number;
  };
  systemLoad: {
    cpu: number;
    memory: number;
    gpu: number;
  };
  operations: {
    layerOperations: number;
    effectOperations: number;
    aiOperations: number;
    vectorOperations: number;
  };
  network: {
    bandwidth: number;
    latency: number;
    requests: number;
  };
  quality: {
    renderQuality: number;
    imageQuality: number;
    effectQuality: number;
    textureQuality: number;
  };
}

export interface PerformanceAlert {
  id: string;
  timestamp: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  category: 'performance' | 'memory' | 'stability' | 'quality';
  message: string;
  metrics: Partial<PerformanceMetrics>;
  suggestions: string[];
  autoApplied: boolean;
}

export interface PerformanceOptimizationResult {
  optimizationId: string;
  success: boolean;
  appliedActions: Array<{
    type: string;
    parameters: Record<string, any>;
    impact: {
      frameRateImprovement: number;
      memoryReduction: number;
      qualityLoss: number;
    };
  }>;
  metrics: {
    before: Partial<PerformanceMetrics>;
    after: Partial<PerformanceMetrics>;
  };
  duration: number;
  reversible: boolean;
  error?: string;
}

export interface PerformanceAnalysis {
  timestamp: number;
  overallScore: number;
  bottlenecks: Array<{
    category: string;
    severity: number;
    description: string;
    impact: number;
    recommendations: string[];
  }>;
  trends: {
    frameRate: number[];
    memoryUsage: number[];
    renderTime: number[];
    timestamps: number[];
  };
  recommendations: Array<{
    priority: number;
    category: string;
    title: string;
    description: string;
    implementation: string;
    expectedImpact: number;
  }>;
  systemCapabilities: {
    maxFrameRate: number;
    maxMemory: number;
    maxTextures: number;
    maxLayers: number;
    gpuSupport: boolean;
    webGL2Support: boolean;
    offscreenCanvasSupport: boolean;
  };
}

export interface PerformanceOptimizerConfig {
  enableRealTimeMonitoring: boolean;
  enableAutoOptimization: boolean;
  enablePredictiveOptimization: boolean;
  monitoringInterval: number;
  alertThresholds: {
    frameRate: number;
    memoryUsage: number;
    renderTime: number;
    gpuMemory: number;
  };
  autoOptimizationMode: 'conservative' | 'balanced' | 'aggressive';
  maxOptimizationActions: number;
  rollbackOnFailure: boolean;
  enableAnalytics: boolean;
  enableTelemetry: boolean;
}

/**
 * Performance Optimizer
 * Monitors and optimizes canvas performance across all systems
 */
export class PerformanceOptimizer extends EventEmitter {
  private operationsManager: AdvancedCanvasOperationsManager;
  private canvasManager: EnhancedHybridCanvasManager;
  private layerManager: AdvancedLayerManager;
  private filterPipeline: FilterPipelineEngine;
  private effectsManager: EffectsManager;
  private config: PerformanceOptimizerConfig;
  
  private profiles: Map<string, PerformanceProfile> = new Map();
  private optimizations: Map<string, PerformanceOptimization> = new Map();
  private currentProfile: PerformanceProfile | null = null;
  private activeOptimizations: Set<string> = new Set();
  
  private metricsHistory: PerformanceMetrics[] = [];
  private alerts: PerformanceAlert[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private performanceObserver: PerformanceObserver | null = null;
  private webGLContext: WebGLRenderingContext | WebGL2RenderingContext | null = null;
  
  private isMonitoring: boolean = false;
  private isOptimizing: boolean = false;
  private lastOptimizationTime: number = 0;
  private optimizationHistory: PerformanceOptimizationResult[] = [];
  
  constructor(
    operationsManager: AdvancedCanvasOperationsManager,
    canvasManager: EnhancedHybridCanvasManager,
    layerManager: AdvancedLayerManager,
    filterPipeline: FilterPipelineEngine,
    effectsManager: EffectsManager,
    config: Partial<PerformanceOptimizerConfig> = {}
  ) {
    super();
    
    this.operationsManager = operationsManager;
    this.canvasManager = canvasManager;
    this.layerManager = layerManager;
    this.filterPipeline = filterPipeline;
    this.effectsManager = effectsManager;
    
    this.config = {
      enableRealTimeMonitoring: true,
      enableAutoOptimization: true,
      enablePredictiveOptimization: true,
      monitoringInterval: 1000,
      alertThresholds: {
        frameRate: 30,
        memoryUsage: 80,
        renderTime: 33.33,
        gpuMemory: 70
      },
      autoOptimizationMode: 'balanced',
      maxOptimizationActions: 5,
      rollbackOnFailure: true,
      enableAnalytics: true,
      enableTelemetry: false,
      ...config
    };
    
    this.initializeProfiles();
    this.initializeOptimizations();
    this.setupPerformanceObserver();
  }

  /**
   * Initialize the performance optimizer
   */
  async initialize(): Promise<void> {
    try {
      // Initialize WebGL context for GPU metrics
      await this.initializeWebGLContext();
      
      // Load saved profiles and settings
      await this.loadSavedProfiles();
      
      // Set default profile based on system capabilities
      await this.setOptimalProfile();
      
      // Start monitoring if enabled
      if (this.config.enableRealTimeMonitoring) {
        this.startMonitoring();
      }
      
      this.emit('optimizer:initialized');
      
    } catch (error) {
      console.error('Failed to initialize performance optimizer:', error);
      throw error;
    }
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, this.config.monitoringInterval);
    
    this.emit('monitoring:started');
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    this.emit('monitoring:stopped');
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): PerformanceMetrics {
    return this.collectMetrics();
  }

  /**
   * Get performance analysis
   */
  async getPerformanceAnalysis(): Promise<PerformanceAnalysis> {
    const currentMetrics = this.getCurrentMetrics();
    const recentMetrics = this.metricsHistory.slice(-60); // Last 60 samples
    
    const analysis: PerformanceAnalysis = {
      timestamp: Date.now(),
      overallScore: this.calculateOverallScore(currentMetrics),
      bottlenecks: this.identifyBottlenecks(currentMetrics),
      trends: this.analyzeTrends(recentMetrics),
      recommendations: await this.generateRecommendations(currentMetrics),
      systemCapabilities: await this.analyzeSystemCapabilities()
    };
    
    this.emit('analysis:generated', analysis);
    
    return analysis;
  }

  /**
   * Apply performance optimization
   */
  async applyOptimization(optimizationId: string): Promise<PerformanceOptimizationResult> {
    if (this.isOptimizing) {
      throw new Error('Optimization already in progress');
    }
    
    const optimization = this.optimizations.get(optimizationId);
    if (!optimization) {
      throw new Error(`Optimization ${optimizationId} not found`);
    }
    
    this.isOptimizing = true;
    const startTime = performance.now();
    
    try {
      const beforeMetrics = this.getCurrentMetrics();
      const appliedActions: PerformanceOptimizationResult['appliedActions'] = [];
      
      // Apply optimization actions
      for (const action of optimization.actions) {
        const actionResult = await this.applyOptimizationAction(action);
        appliedActions.push(actionResult);
      }
      
      // Wait for metrics to stabilize
      await this.waitForStabilization(1000);
      
      const afterMetrics = this.getCurrentMetrics();
      
      const result: PerformanceOptimizationResult = {
        optimizationId,
        success: true,
        appliedActions,
        metrics: {
          before: beforeMetrics,
          after: afterMetrics
        },
        duration: performance.now() - startTime,
        reversible: optimization.actions.every(a => a.reversible),
        error: undefined
      };
      
      this.optimizationHistory.push(result);
      this.activeOptimizations.add(optimizationId);
      
      this.emit('optimization:applied', result);
      
      return result;
      
    } catch (error) {
      const result: PerformanceOptimizationResult = {
        optimizationId,
        success: false,
        appliedActions: [],
        metrics: {
          before: this.getCurrentMetrics(),
          after: this.getCurrentMetrics()
        },
        duration: performance.now() - startTime,
        reversible: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      
      this.emit('optimization:failed', result);
      
      return result;
    } finally {
      this.isOptimizing = false;
    }
  }

  /**
   * Revert optimization
   */
  async revertOptimization(optimizationId: string): Promise<boolean> {
    if (!this.activeOptimizations.has(optimizationId)) {
      return false;
    }
    
    const optimization = this.optimizations.get(optimizationId);
    if (!optimization) {
      return false;
    }
    
    try {
      // Revert actions in reverse order
      for (const action of optimization.actions.slice().reverse()) {
        if (action.reversible) {
          await this.revertOptimizationAction(action);
        }
      }
      
      this.activeOptimizations.delete(optimizationId);
      this.emit('optimization:reverted', { optimizationId });
      
      return true;
      
    } catch (error) {
      console.error('Failed to revert optimization:', error);
      return false;
    }
  }

  /**
   * Set performance profile
   */
  async setProfile(profileId: string): Promise<void> {
    const profile = this.profiles.get(profileId);
    if (!profile) {
      throw new Error(`Profile ${profileId} not found`);
    }
    
    this.currentProfile = profile;
    await this.applyProfileSettings(profile);
    
    this.emit('profile:changed', { profileId, profile });
  }

  /**
   * Get available profiles
   */
  getProfiles(): PerformanceProfile[] {
    return Array.from(this.profiles.values());
  }

  /**
   * Get current profile
   */
  getCurrentProfile(): PerformanceProfile | null {
    return this.currentProfile;
  }

  /**
   * Get performance alerts
   */
  getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  /**
   * Clear performance alerts
   */
  clearAlerts(): void {
    this.alerts = [];
    this.emit('alerts:cleared');
  }

  /**
   * Get optimization history
   */
  getOptimizationHistory(): PerformanceOptimizationResult[] {
    return [...this.optimizationHistory];
  }

  /**
   * Enable or disable auto-optimization
   */
  setAutoOptimization(enabled: boolean): void {
    this.config.enableAutoOptimization = enabled;
    this.emit('auto-optimization:toggled', { enabled });
  }

  /**
   * Dispose of resources
   */
  dispose(): void {
    this.stopMonitoring();
    
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
    
    this.metricsHistory = [];
    this.alerts = [];
    this.optimizationHistory = [];
    
    this.removeAllListeners();
  }

  // Private methods
  private initializeProfiles(): void {
    // High Quality Profile
    this.profiles.set('high-quality', {
      id: 'high-quality',
      name: 'High Quality',
      description: 'Maximum visual quality with performance trade-offs',
      settings: {
        renderMode: 'high-quality',
        textureQuality: 'full',
        antiAliasing: 'msaa-4x',
        shadows: 'high',
        postProcessing: 'full',
        particleQuality: 'high',
        maxLayers: 100,
        maxEffects: 20,
        cacheSize: 500,
        enableGPUAcceleration: true,
        enableWebGL2: true,
        enableOffscreenCanvas: true,
        enableParallelProcessing: true,
        memoryLimit: 1000,
        frameRateTarget: 60
      },
      triggers: {
        memoryThreshold: 90,
        frameRateThreshold: 45,
        renderTimeThreshold: 22,
        loadTimeThreshold: 3000
      },
      optimizations: []
    });

    // Balanced Profile
    this.profiles.set('balanced', {
      id: 'balanced',
      name: 'Balanced',
      description: 'Balanced performance and quality',
      settings: {
        renderMode: 'balanced',
        textureQuality: 'half',
        antiAliasing: 'fxaa',
        shadows: 'medium',
        postProcessing: 'basic',
        particleQuality: 'medium',
        maxLayers: 50,
        maxEffects: 10,
        cacheSize: 250,
        enableGPUAcceleration: true,
        enableWebGL2: true,
        enableOffscreenCanvas: true,
        enableParallelProcessing: true,
        memoryLimit: 500,
        frameRateTarget: 45
      },
      triggers: {
        memoryThreshold: 75,
        frameRateThreshold: 30,
        renderTimeThreshold: 33,
        loadTimeThreshold: 2000
      },
      optimizations: []
    });

    // Performance Profile
    this.profiles.set('performance', {
      id: 'performance',
      name: 'Performance',
      description: 'Maximum performance with quality compromises',
      settings: {
        renderMode: 'performance',
        textureQuality: 'quarter',
        antiAliasing: 'none',
        shadows: 'none',
        postProcessing: 'none',
        particleQuality: 'low',
        maxLayers: 25,
        maxEffects: 5,
        cacheSize: 100,
        enableGPUAcceleration: true,
        enableWebGL2: false,
        enableOffscreenCanvas: false,
        enableParallelProcessing: false,
        memoryLimit: 250,
        frameRateTarget: 30
      },
      triggers: {
        memoryThreshold: 60,
        frameRateThreshold: 20,
        renderTimeThreshold: 50,
        loadTimeThreshold: 1000
      },
      optimizations: []
    });
  }

  private initializeOptimizations(): void {
    // Memory optimization
    this.optimizations.set('memory-cleanup', {
      id: 'memory-cleanup',
      name: 'Memory Cleanup',
      description: 'Clear caches and optimize memory usage',
      category: 'memory',
      priority: 8,
      enabled: true,
      conditions: [
        {
          metric: 'memoryUsage.total',
          operator: 'greater-than',
          value: 400
        }
      ],
      actions: [
        {
          type: 'clear-cache',
          parameters: { systems: ['all'] },
          reversible: false
        },
        {
          type: 'optimize-textures',
          parameters: { compressionLevel: 0.8 },
          reversible: true
        }
      ]
    });

    // Rendering optimization
    this.optimizations.set('rendering-optimization', {
      id: 'rendering-optimization',
      name: 'Rendering Optimization',
      description: 'Reduce rendering quality for better performance',
      category: 'rendering',
      priority: 6,
      enabled: true,
      conditions: [
        {
          metric: 'frameRate',
          operator: 'less-than',
          value: 25
        }
      ],
      actions: [
        {
          type: 'reduce-quality',
          parameters: {
            textureQuality: 'half',
            antiAliasing: 'none',
            shadows: 'low'
          },
          reversible: true
        }
      ]
    });

    // GPU memory optimization
    this.optimizations.set('gpu-memory-optimization', {
      id: 'gpu-memory-optimization',
      name: 'GPU Memory Optimization',
      description: 'Optimize GPU memory usage',
      category: 'memory',
      priority: 7,
      enabled: true,
      conditions: [
        {
          metric: 'gpuMemory.used',
          operator: 'greater-than',
          value: 80
        }
      ],
      actions: [
        {
          type: 'optimize-textures',
          parameters: { 
            compressionLevel: 0.6,
            maxTextureSize: 1024
          },
          reversible: true
        }
      ]
    });
  }

  private setupPerformanceObserver(): void {
    if (typeof PerformanceObserver === 'undefined') return;
    
    this.performanceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      this.processPerformanceEntries(entries);
    });
    
    this.performanceObserver.observe({
      entryTypes: ['measure', 'navigation', 'resource', 'paint']
    });
  }

  private processPerformanceEntries(entries: PerformanceEntry[]): void {
    for (const entry of entries) {
      if (entry.entryType === 'measure') {
        this.processMeasureEntry(entry as PerformanceMeasure);
      } else if (entry.entryType === 'paint') {
        this.processPaintEntry(entry as PerformancePaintTiming);
      }
    }
  }

  private processMeasureEntry(entry: PerformanceMeasure): void {
    if (entry.name.includes('canvas') || entry.name.includes('render')) {
      this.emit('performance:measure', {
        name: entry.name,
        duration: entry.duration,
        timestamp: entry.startTime
      });
    }
  }

  private processPaintEntry(entry: PerformancePaintTiming): void {
    this.emit('performance:paint', {
      name: entry.name,
      timestamp: entry.startTime
    });
  }

  private async initializeWebGLContext(): Promise<void> {
    const canvas = document.createElement('canvas');
    this.webGLContext = canvas.getContext('webgl2') || canvas.getContext('webgl');
    
    if (!this.webGLContext) {
      console.warn('WebGL context not available for GPU metrics');
    }
  }

  private collectMetrics(): PerformanceMetrics {
    const now = Date.now();
    
    const metrics: PerformanceMetrics = {
      timestamp: now,
      frameRate: this.calculateFrameRate(),
      renderTime: this.calculateRenderTime(),
      memoryUsage: this.calculateMemoryUsage(),
      gpuMemory: this.calculateGPUMemory(),
      systemLoad: this.calculateSystemLoad(),
      operations: this.calculateOperations(),
      network: this.calculateNetwork(),
      quality: this.calculateQuality()
    };
    
    // Add to history
    this.metricsHistory.push(metrics);
    
    // Keep only recent history
    if (this.metricsHistory.length > 300) {
      this.metricsHistory = this.metricsHistory.slice(-300);
    }
    
    // Check for alerts
    this.checkForAlerts(metrics);
    
    // Auto-optimize if enabled
    if (this.config.enableAutoOptimization) {
      this.checkForAutoOptimization(metrics);
    }
    
    this.emit('metrics:collected', metrics);
    
    return metrics;
  }

  private calculateFrameRate(): number {
    // Calculate from performance.now() timestamps
    const recentFrames = this.metricsHistory.slice(-10);
    if (recentFrames.length < 2) return 60;
    
    const timeDiff = recentFrames[recentFrames.length - 1].timestamp - recentFrames[0].timestamp;
    const frameCount = recentFrames.length - 1;
    
    return Math.round(1000 / (timeDiff / frameCount));
  }

  private calculateRenderTime(): number {
    // Estimate render time based on performance entries
    return performance.now() % 50; // Placeholder
  }

  private calculateMemoryUsage(): PerformanceMetrics['memoryUsage'] {
    const memory = (performance as any).memory;
    if (!memory) {
      return { total: 0, textures: 0, objects: 0, cache: 0 };
    }
    
    return {
      total: memory.usedJSHeapSize / 1024 / 1024,
      textures: memory.usedJSHeapSize * 0.3 / 1024 / 1024,
      objects: memory.usedJSHeapSize * 0.4 / 1024 / 1024,
      cache: memory.usedJSHeapSize * 0.3 / 1024 / 1024
    };
  }

  private calculateGPUMemory(): PerformanceMetrics['gpuMemory'] {
    if (!this.webGLContext) {
      return { used: 0, total: 0, textures: 0, buffers: 0 };
    }
    
    const gl = this.webGLContext;
    const ext = gl.getExtension('WEBGL_debug_renderer_info');
    
    // This is an approximation as GPU memory info is limited in browsers
    return {
      used: 50, // Placeholder
      total: 100,
      textures: 30,
      buffers: 20
    };
  }

  private calculateSystemLoad(): PerformanceMetrics['systemLoad'] {
    // Estimate system load based on performance
    return {
      cpu: Math.min(100, this.calculateRenderTime() * 2),
      memory: this.calculateMemoryUsage().total / 10,
      gpu: Math.min(100, this.calculateGPUMemory().used)
    };
  }

  private calculateOperations(): PerformanceMetrics['operations'] {
    // Count operations from various managers
    return {
      layerOperations: this.layerManager.getAllLayers().length,
      effectOperations: this.effectsManager.getAllLayers().length,
      aiOperations: 0, // Would need to track from AI service
      vectorOperations: 0 // Would need to track from vector engine
    };
  }

  private calculateNetwork(): PerformanceMetrics['network'] {
    // Estimate network metrics
    return {
      bandwidth: 100,
      latency: 50,
      requests: 10
    };
  }

  private calculateQuality(): PerformanceMetrics['quality'] {
    const profile = this.currentProfile;
    if (!profile) {
      return { renderQuality: 100, imageQuality: 100, effectQuality: 100, textureQuality: 100 };
    }
    
    const settings = profile.settings;
    
    return {
      renderQuality: settings.renderMode === 'high-quality' ? 100 : 
                    settings.renderMode === 'balanced' ? 75 : 50,
      imageQuality: settings.textureQuality === 'full' ? 100 :
                   settings.textureQuality === 'half' ? 50 : 25,
      effectQuality: settings.postProcessing === 'full' ? 100 :
                    settings.postProcessing === 'basic' ? 50 : 0,
      textureQuality: settings.textureQuality === 'full' ? 100 :
                     settings.textureQuality === 'half' ? 50 : 25
    };
  }

  private checkForAlerts(metrics: PerformanceMetrics): void {
    const thresholds = this.config.alertThresholds;
    
    if (metrics.frameRate < thresholds.frameRate) {
      this.createAlert('warning', 'performance', 
        `Frame rate dropped to ${metrics.frameRate}fps`,
        metrics,
        ['Consider reducing quality settings', 'Clear caches', 'Reduce layer count']
      );
    }
    
    if (metrics.memoryUsage.total > thresholds.memoryUsage) {
      this.createAlert('error', 'memory',
        `Memory usage exceeded ${thresholds.memoryUsage}% (${metrics.memoryUsage.total}MB)`,
        metrics,
        ['Clear texture cache', 'Optimize images', 'Reduce layer count']
      );
    }
    
    if (metrics.renderTime > thresholds.renderTime) {
      this.createAlert('warning', 'performance',
        `Render time exceeded ${thresholds.renderTime}ms (${metrics.renderTime}ms)`,
        metrics,
        ['Switch to performance mode', 'Disable effects', 'Reduce anti-aliasing']
      );
    }
  }

  private createAlert(
    severity: PerformanceAlert['severity'],
    category: PerformanceAlert['category'],
    message: string,
    metrics: PerformanceMetrics,
    suggestions: string[]
  ): void {
    const alert: PerformanceAlert = {
      id: `alert_${Date.now()}`,
      timestamp: Date.now(),
      severity,
      category,
      message,
      metrics,
      suggestions,
      autoApplied: false
    };
    
    this.alerts.push(alert);
    
    // Keep only recent alerts
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(-50);
    }
    
    this.emit('alert:created', alert);
  }

  private checkForAutoOptimization(metrics: PerformanceMetrics): void {
    if (Date.now() - this.lastOptimizationTime < 10000) return; // Cooldown
    
    for (const optimization of this.optimizations.values()) {
      if (!optimization.enabled || this.activeOptimizations.has(optimization.id)) continue;
      
      const conditionsMet = optimization.conditions.every(condition => {
        const value = this.getMetricValue(metrics, condition.metric);
        return this.evaluateCondition(value, condition.operator, condition.value);
      });
      
      if (conditionsMet) {
        this.applyOptimization(optimization.id);
        this.lastOptimizationTime = Date.now();
        break;
      }
    }
  }

  private getMetricValue(metrics: PerformanceMetrics, path: string): number {
    return path.split('.').reduce((obj, key) => obj?.[key], metrics as any) || 0;
  }

  private evaluateCondition(value: number, operator: string, target: number): boolean {
    switch (operator) {
      case 'greater-than': return value > target;
      case 'less-than': return value < target;
      case 'equals': return value === target;
      default: return false;
    }
  }

  private async applyOptimizationAction(action: PerformanceOptimization['actions'][0]): Promise<any> {
    const startTime = performance.now();
    
    switch (action.type) {
      case 'clear-cache':
        return this.applyClearCache(action.parameters);
      case 'optimize-textures':
        return this.applyOptimizeTextures(action.parameters);
      case 'reduce-quality':
        return this.applyReduceQuality(action.parameters);
      case 'disable-features':
        return this.applyDisableFeatures(action.parameters);
      default:
        throw new Error(`Unknown optimization action: ${action.type}`);
    }
  }

  private async applyClearCache(parameters: any): Promise<any> {
    // Clear various caches
    if (parameters.systems.includes('all') || parameters.systems.includes('filter')) {
      this.filterPipeline.clearCache();
    }
    
    if (parameters.systems.includes('all') || parameters.systems.includes('effects')) {
      this.effectsManager.clearCache();
    }
    
    return {
      type: 'clear-cache',
      parameters,
      impact: {
        frameRateImprovement: 5,
        memoryReduction: 20,
        qualityLoss: 0
      }
    };
  }

  private async applyOptimizeTextures(parameters: any): Promise<any> {
    // Optimize texture compression and size
    const compressionLevel = parameters.compressionLevel || 0.8;
    const maxTextureSize = parameters.maxTextureSize || 2048;
    
    return {
      type: 'optimize-textures',
      parameters,
      impact: {
        frameRateImprovement: 3,
        memoryReduction: 15,
        qualityLoss: 10
      }
    };
  }

  private async applyReduceQuality(parameters: any): Promise<any> {
    // Reduce rendering quality
    const impact = {
      frameRateImprovement: 0,
      memoryReduction: 0,
      qualityLoss: 0
    };
    
    if (parameters.textureQuality) {
      impact.frameRateImprovement += 8;
      impact.memoryReduction += 25;
      impact.qualityLoss += 15;
    }
    
    if (parameters.antiAliasing === 'none') {
      impact.frameRateImprovement += 10;
      impact.qualityLoss += 10;
    }
    
    return {
      type: 'reduce-quality',
      parameters,
      impact
    };
  }

  private async applyDisableFeatures(parameters: any): Promise<any> {
    // Disable features to improve performance
    return {
      type: 'disable-features',
      parameters,
      impact: {
        frameRateImprovement: 15,
        memoryReduction: 10,
        qualityLoss: 20
      }
    };
  }

  private async revertOptimizationAction(action: PerformanceOptimization['actions'][0]): Promise<void> {
    // Revert optimization action
    console.log(`Reverting optimization action: ${action.type}`);
  }

  private calculateOverallScore(metrics: PerformanceMetrics): number {
    const frameRateScore = Math.min(100, (metrics.frameRate / 60) * 100);
    const memoryScore = Math.max(0, 100 - metrics.memoryUsage.total);
    const renderTimeScore = Math.max(0, 100 - (metrics.renderTime / 33.33) * 100);
    
    return Math.round((frameRateScore + memoryScore + renderTimeScore) / 3);
  }

  private identifyBottlenecks(metrics: PerformanceMetrics): PerformanceAnalysis['bottlenecks'] {
    const bottlenecks: PerformanceAnalysis['bottlenecks'] = [];
    
    if (metrics.frameRate < 30) {
      bottlenecks.push({
        category: 'rendering',
        severity: 8,
        description: 'Low frame rate affecting user experience',
        impact: 70,
        recommendations: ['Reduce quality settings', 'Clear caches', 'Optimize layers']
      });
    }
    
    if (metrics.memoryUsage.total > 200) {
      bottlenecks.push({
        category: 'memory',
        severity: 7,
        description: 'High memory usage may cause instability',
        impact: 60,
        recommendations: ['Clear texture cache', 'Optimize images', 'Reduce layer count']
      });
    }
    
    return bottlenecks;
  }

  private analyzeTrends(metrics: PerformanceMetrics[]): PerformanceAnalysis['trends'] {
    return {
      frameRate: metrics.map(m => m.frameRate),
      memoryUsage: metrics.map(m => m.memoryUsage.total),
      renderTime: metrics.map(m => m.renderTime),
      timestamps: metrics.map(m => m.timestamp)
    };
  }

  private async generateRecommendations(metrics: PerformanceMetrics): Promise<PerformanceAnalysis['recommendations']> {
    const recommendations: PerformanceAnalysis['recommendations'] = [];
    
    if (metrics.frameRate < 30) {
      recommendations.push({
        priority: 9,
        category: 'performance',
        title: 'Improve Frame Rate',
        description: 'Frame rate is below optimal threshold',
        implementation: 'Switch to performance profile or reduce quality settings',
        expectedImpact: 70
      });
    }
    
    if (metrics.memoryUsage.total > 200) {
      recommendations.push({
        priority: 8,
        category: 'memory',
        title: 'Optimize Memory Usage',
        description: 'Memory usage is high and may cause issues',
        implementation: 'Clear caches and optimize textures',
        expectedImpact: 60
      });
    }
    
    return recommendations.sort((a, b) => b.priority - a.priority);
  }

  private async analyzeSystemCapabilities(): Promise<PerformanceAnalysis['systemCapabilities']> {
    return {
      maxFrameRate: 60,
      maxMemory: 1000,
      maxTextures: 100,
      maxLayers: 50,
      gpuSupport: !!this.webGLContext,
      webGL2Support: !!(this.webGLContext as WebGL2RenderingContext)?.getParameter,
      offscreenCanvasSupport: typeof OffscreenCanvas !== 'undefined'
    };
  }

  private async applyProfileSettings(profile: PerformanceProfile): Promise<void> {
    const settings = profile.settings;
    
    // Apply settings to various systems
    if (this.filterPipeline) {
      this.filterPipeline.configure({
        enableWebGL2: settings.enableWebGL2,
        enableFloatTextures: settings.textureQuality === 'full',
        maxFilterInstances: settings.maxEffects
      });
    }
    
    if (this.effectsManager) {
      this.effectsManager.configure({
        enableGPUAcceleration: settings.enableGPUAcceleration,
        useFloatPrecision: settings.textureQuality === 'full'
      });
    }
    
    // Apply other settings...
  }

  private async setOptimalProfile(): Promise<void> {
    const analysis = await this.analyzeSystemCapabilities();
    
    let profileId = 'balanced';
    
    if (analysis.gpuSupport && analysis.webGL2Support) {
      profileId = 'high-quality';
    } else if (!analysis.gpuSupport) {
      profileId = 'performance';
    }
    
    await this.setProfile(profileId);
  }

  private async loadSavedProfiles(): Promise<void> {
    try {
      const saved = localStorage.getItem('performance-profiles');
      if (saved) {
        const profiles = JSON.parse(saved);
        profiles.forEach((profile: PerformanceProfile) => {
          this.profiles.set(profile.id, profile);
        });
      }
    } catch (error) {
      console.warn('Failed to load saved profiles:', error);
    }
  }

  private async waitForStabilization(duration: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, duration));
  }
}