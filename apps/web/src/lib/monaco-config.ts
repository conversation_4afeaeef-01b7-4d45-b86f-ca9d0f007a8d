import * as monaco from 'monaco-editor';
import { EditorConfiguration } from '@/types/editor';

export const defaultEditorConfig: EditorConfiguration = {
  theme: 'vs-dark',
  fontSize: 14,
  fontFamily: '"Fira Code", "Cascadia Code", "JetBrains Mono", "SF Mono", Monaco, Menlo, "Ubuntu Mono", monospace',
  lineNumbers: 'on',
  wordWrap: 'on',
  minimap: { enabled: true, side: 'right' },
  scrollBeyondLastLine: false,
  automaticLayout: true,
  tabSize: 2,
  insertSpaces: true,
  formatOnSave: true,
  formatOnType: false,
  autoSave: true,
  autoSaveDelay: 1000,
};

export const getMonacoEditorOptions = (
  config: Partial<EditorConfiguration> = {}
): monaco.editor.IStandaloneEditorConstructionOptions => {
  const finalConfig = { ...defaultEditorConfig, ...config };

  return {
    theme: finalConfig.theme,
    fontSize: finalConfig.fontSize,
    fontFamily: finalConfig.fontFamily,
    lineNumbers: finalConfig.lineNumbers,
    wordWrap: finalConfig.wordWrap,
    wordWrapColumn: 120,
    wrappingIndent: 'indent',
    
    minimap: {
      enabled: finalConfig.minimap.enabled,
      side: finalConfig.minimap.side,
      showSlider: 'mouseover',
      renderCharacters: true,
      maxColumn: 120,
    },
    
    scrollBeyondLastLine: finalConfig.scrollBeyondLastLine,
    automaticLayout: finalConfig.automaticLayout,
    tabSize: finalConfig.tabSize,
    insertSpaces: finalConfig.insertSpaces,
    detectIndentation: true,
    trimAutoWhitespace: true,
    
    // Code intelligence
    quickSuggestions: {
      other: true,
      comments: false,
      strings: false,
    },
    quickSuggestionsDelay: 100,
    suggestOnTriggerCharacters: true,
    acceptSuggestionOnCommitCharacter: true,
    acceptSuggestionOnEnter: 'on',
    tabCompletion: 'on',
    
    suggest: {
      showIcons: true,
      showSnippets: true,
      showWords: true,
      showColors: true,
      showFiles: true,
      showReferences: true,
      showFolders: true,
      showTypeParameters: true,
      showIssues: true,
      showUsers: false,
      showValues: true,
      filterGraceful: true,
      localityBonus: true,
      snippetsPreventQuickSuggestions: false,
    },
    
    // Visual enhancements
    bracketPairColorization: {
      enabled: true,
      independentColorPoolPerBracketType: true,
    },
    
    guides: {
      bracketPairs: true,
      bracketPairsHorizontal: true,
      highlightActiveBracketPair: true,
      indentation: true,
      highlightActiveIndentation: true,
    },
    
    renderWhitespace: 'selection',
    renderControlCharacters: false,
    renderLineHighlight: 'line',
    renderLineHighlightOnlyWhenFocus: false,
    
    // Cursor and selection
    cursorBlinking: 'blink',
    cursorSmoothCaretAnimation: 'on',
    cursorWidth: 2,
    selectOnLineNumbers: true,
    selectionHighlight: true,
    occurrencesHighlight: 'singleFile',
    
    // Scrolling
    scrollBeyondLastColumn: 5,
    smoothScrolling: true,
    mouseWheelZoom: true,
    
    // Find and replace
    find: {
      addExtraSpaceOnTop: false,
      autoFindInSelection: 'never',
      seedSearchStringFromSelection: 'always',
    },
    
    // Folding
    folding: true,
    foldingStrategy: 'indentation',
    showFoldingControls: 'mouseover',
    foldingHighlight: true,
    foldingImportsByDefault: false,
    
    // Editor behavior
    contextmenu: true,
    copyWithSyntaxHighlighting: true,
    emptySelectionClipboard: true,
    multiCursorModifier: 'alt',
    multiCursorMergeOverlapping: true,
    showUnused: true,
    
    // Hover
    hover: {
      enabled: true,
      delay: 300,
      sticky: true,
    },
    
    // Links
    links: true,
    
    // Parameter hints
    parameterHints: {
      enabled: true,
      cycle: false,
    },
    
    // Code lens
    codeLens: true,
    
    // Color decorators
    colorDecorators: true,
    
    // Glyph margin
    glyphMargin: false,
    
    // Overview ruler
    overviewRulerLanes: 3,
    overviewRulerBorder: false,
    
    // Scrollbar
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto',
      useShadows: true,
      verticalHasArrows: false,
      horizontalHasArrows: false,
      verticalScrollbarSize: 14,
      horizontalScrollbarSize: 14,
    },
    
    // Performance
    stopRenderingLineAfter: 10000,
    
    // Accessibility
    accessibilitySupport: 'auto',
    
    // Format on paste/type
    formatOnPaste: true,
    formatOnType: finalConfig.formatOnType,
    
    // Auto indent
    autoIndent: 'advanced',
    
    // Auto closing
    autoClosingBrackets: 'always',
    autoClosingComments: 'always',
    autoClosingDelete: 'always',
    autoClosingOvertype: 'always',
    autoClosingQuotes: 'always',
    
    // Auto surrounding
    autoSurround: 'languageDefined',
    
    // Comments
    comments: {
      insertSpace: true,
      ignoreEmptyLines: true,
    },
    
    // Drag and drop
    dragAndDrop: true,
    
    // Inline suggestions
    inlineSuggest: {
      enabled: true,
    },
    
    // Semantic highlighting
    'semanticHighlighting.enabled': true,
    
    // Sticky scroll
    stickyScroll: {
      enabled: false,
    },
    
    // Linked editing
    linkedEditing: false,
    
    // Rename on type
    renameOnType: false,
    
    // Word based suggestions
    wordBasedSuggestions: 'matchingDocuments',
    
    // Experimental features
    experimentalWhitespaceRendering: 'svg',
  };
};

export const setupMonacoEnvironment = () => {
  // Set up Monaco environment for web workers
  self.MonacoEnvironment = {
    getWorker: function (_, label) {
      if (label === 'json') {
        return new Worker(new URL('monaco-editor/esm/vs/language/json/json.worker', import.meta.url));
      }
      if (label === 'css' || label === 'scss' || label === 'less') {
        return new Worker(new URL('monaco-editor/esm/vs/language/css/css.worker', import.meta.url));
      }
      if (label === 'html' || label === 'handlebars' || label === 'razor') {
        return new Worker(new URL('monaco-editor/esm/vs/language/html/html.worker', import.meta.url));
      }
      if (label === 'typescript' || label === 'javascript') {
        return new Worker(new URL('monaco-editor/esm/vs/language/typescript/ts.worker', import.meta.url));
      }
      return new Worker(new URL('monaco-editor/esm/vs/editor/editor.worker', import.meta.url));
    },
  };
};

export const registerCustomTheme = () => {
  monaco.editor.defineTheme('unified-dark', {
    base: 'vs-dark',
    inherit: true,
    rules: [
      { token: 'comment', foreground: '6A9955' },
      { token: 'keyword', foreground: 'C586C0' },
      { token: 'string', foreground: 'CE9178' },
      { token: 'number', foreground: 'B5CEA8' },
      { token: 'type', foreground: '4EC9B0' },
      { token: 'class', foreground: '4EC9B0' },
      { token: 'function', foreground: 'DCDCAA' },
      { token: 'variable', foreground: '9CDCFE' },
      { token: 'property', foreground: '9CDCFE' },
      { token: 'operator', foreground: 'D4D4D4' },
      { token: 'delimiter', foreground: 'D4D4D4' },
      { token: 'tag', foreground: '569CD6' },
      { token: 'attribute.name', foreground: '92C5F8' },
      { token: 'attribute.value', foreground: 'CE9178' },
      { token: 'regexp', foreground: 'D16969' },
      { token: 'annotation', foreground: 'CC6633' },
      { token: 'preproc', foreground: 'C586C0' },
      { token: 'namespace', foreground: '4EC9B0' },
      { token: 'constant', foreground: 'B5CEA8' },
      { token: 'error', foreground: 'F44747' },
      { token: 'warning', foreground: 'FF8C00' },
      { token: 'info', foreground: '1E90FF' },
      { token: 'hint', foreground: '808080' },
    ],
    colors: {
      'editor.background': '#1E1E1E',
      'editor.foreground': '#D4D4D4',
      'editorCursor.foreground': '#AEAFAD',
      'editor.lineHighlightBackground': '#2A2D2E',
      'editor.selectionBackground': '#264F78',
      'editor.selectionHighlightBackground': '#ADD6FF26',
      'editor.wordHighlightBackground': '#575757B8',
      'editor.wordHighlightStrongBackground': '#004972B8',
      'editor.findMatchBackground': '#515C6A',
      'editor.findMatchHighlightBackground': '#EA5C0055',
      'editor.findRangeHighlightBackground': '#3A3D4166',
      'editor.hoverHighlightBackground': '#264F7840',
      'editor.inactiveSelectionBackground': '#3A3D41',
      'editor.rangeHighlightBackground': '#FFFFFF0B',
      'editorBracketMatch.background': '#0064001A',
      'editorBracketMatch.border': '#888888',
      'editorCodeLens.foreground': '#999999',
      'editorError.foreground': '#F44747',
      'editorWarning.foreground': '#FF8C00',
      'editorInfo.foreground': '#1E90FF',
      'editorHint.foreground': '#808080',
      'editorGutter.background': '#1E1E1E',
      'editorGutter.modifiedBackground': '#1E90FF',
      'editorGutter.addedBackground': '#587C0C',
      'editorGutter.deletedBackground': '#94151B',
      'editorLineNumber.foreground': '#858585',
      'editorLineNumber.activeForeground': '#C6C6C6',
      'editorOverviewRuler.border': '#7F7F7F4D',
      'editorOverviewRuler.currentContentForeground': '#40C8AE',
      'editorOverviewRuler.incomingContentForeground': '#40A6FF',
      'editorOverviewRuler.findMatchForeground': '#F6B94D',
      'editorOverviewRuler.rangeHighlightForeground': '#007ACC99',
      'editorOverviewRuler.selectionHighlightForeground': '#A0A0A0CC',
      'editorOverviewRuler.wordHighlightForeground': '#A0A0A0CC',
      'editorOverviewRuler.wordHighlightStrongForeground': '#C0A0C0CC',
      'editorOverviewRuler.modifiedForeground': '#1E90FF',
      'editorOverviewRuler.addedForeground': '#587C0C',
      'editorOverviewRuler.deletedForeground': '#94151B',
      'editorOverviewRuler.errorForeground': '#F44747',
      'editorOverviewRuler.warningForeground': '#FF8C00',
      'editorOverviewRuler.infoForeground': '#1E90FF',
      'editorRuler.foreground': '#5A5A5A',
      'editorSuggestWidget.background': '#252526',
      'editorSuggestWidget.border': '#454545',
      'editorSuggestWidget.foreground': '#D4D4D4',
      'editorSuggestWidget.selectedBackground': '#062F4A',
      'editorSuggestWidget.highlightForeground': '#18A3FF',
      'editorHoverWidget.background': '#252526',
      'editorHoverWidget.border': '#454545',
      'editorHoverWidget.foreground': '#CCCCCC',
      'editorHoverWidget.statusBarBackground': '#2C2C2D',
      'editorMarkerNavigation.background': '#252526',
      'editorMarkerNavigationError.background': '#F44747',
      'editorMarkerNavigationWarning.background': '#FF8C00',
      'editorMarkerNavigationInfo.background': '#1E90FF',
      'peekView.border': '#007ACC',
      'peekViewEditor.background': '#001F33',
      'peekViewEditor.matchHighlightBackground': '#FF8F0099',
      'peekViewResult.background': '#252526',
      'peekViewResult.fileForeground': '#FFFFFF',
      'peekViewResult.lineForeground': '#BBBBBB',
      'peekViewResult.matchHighlightBackground': '#EA5C0055',
      'peekViewResult.selectionBackground': '#3399FF33',
      'peekViewResult.selectionForeground': '#FFFFFF',
      'peekViewTitle.background': '#1E1E1E',
      'peekViewTitleDescription.foreground': '#CCCCCCB3',
      'peekViewTitleLabel.foreground': '#FFFFFF',
    },
  });
};