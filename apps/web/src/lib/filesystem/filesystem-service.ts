import { EventEmitter } from 'events';

export interface FileSystemEntry {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  modified: Date;
  created: Date;
  permissions: FilePermissions;
  isHidden: boolean;
  isSymlink: boolean;
  mimeType?: string;
  encoding?: string;
  hash?: string;
}

export interface FilePermissions {
  read: boolean;
  write: boolean;
  execute: boolean;
  owner: string;
  group: string;
  mode: string;
}

export interface FileInfo extends FileSystemEntry {
  lineCount?: number;
  version: number;
  metadata: Record<string, any>;
}

export interface SearchQuery {
  path: string;
  pattern: string;
  includeContent: boolean;
  fileTypes: string[];
  excludePatterns: string[];
  maxResults: number;
  caseSensitive: boolean;
  useRegex: boolean;
}

export interface SearchResult {
  entry: FileSystemEntry;
  relevance: number;
  matches: SearchMatch[];
}

export interface SearchMatch {
  line: number;
  column: number;
  text: string;
  context: string;
}

export interface FileOperation {
  type: 'create' | 'read' | 'write' | 'delete' | 'copy' | 'move' | 'rename';
  sourcePath: string;
  targetPath?: string;
  data?: Uint8Array;
  options?: Record<string, any>;
}

export interface OperationResult {
  success: boolean;
  error?: string;
  data?: any;
}

export interface FileWatcher {
  id: string;
  path: string;
  callback: FileWatchCallback;
  options: WatchOptions;
  isActive: boolean;
}

export interface WatchOptions {
  recursive?: boolean;
  ignoreInitial?: boolean;
  persistent?: boolean;
  ignored?: string[];
}

export type FileWatchCallback = (event: FileWatchEvent, path: string, stats?: FileInfo) => void;
export type FileWatchEvent = 'add' | 'change' | 'unlink' | 'addDir' | 'unlinkDir';

class FileSystemError extends Error {
  constructor(message: string, public code: string = 'FILESYSTEM_ERROR') {
    super(message);
    this.name = 'FileSystemError';
  }
}

export class WebFileSystemService extends EventEmitter {
  private rootHandle: FileSystemDirectoryHandle | null = null;
  private cache: Map<string, CachedEntry> = new Map();
  private watchers: Map<string, FileWatcher[]> = new Map();
  private virtualFiles: Map<string, VirtualFile> = new Map();
  private maxFileSize: number = 100 * 1024 * 1024; // 100MB
  private blockedExtensions: string[] = ['exe', 'dll', 'so', 'dylib', 'app'];

  constructor() {
    super();
    this.setupVirtualFileSystem();
  }

  async initialize(): Promise<void> {
    try {
      // Try to use File System Access API if available
      if ('showDirectoryPicker' in window) {
        // Request directory access permission
        this.rootHandle = await (window as any).showDirectoryPicker({
          mode: 'readwrite'
        });
        await this.validateRootDirectory();
        await this.buildInitialCache();
      } else {
        // Fall back to virtual file system
        await this.initializeVirtualFileSystem();
      }
    } catch (error) {
      console.warn('Failed to initialize native file system, using virtual file system:', error);
      await this.initializeVirtualFileSystem();
    }
  }

  // Basic file operations
  async readFile(path: string): Promise<Uint8Array> {
    await this.validatePath(path);

    const cachedEntry = this.cache.get(path);
    if (cachedEntry && !cachedEntry.isExpired()) {
      return cachedEntry.content;
    }

    try {
      if (this.rootHandle) {
        const fileHandle = await this.getFileHandle(path);
        const file = await fileHandle.getFile();
        const content = new Uint8Array(await file.arrayBuffer());
        
        // Update cache
        this.cache.set(path, {
          content,
          lastModified: file.lastModified,
          size: file.size,
          cachedAt: Date.now()
        });
        
        return content;
      } else {
        // Virtual file system
        const virtualFile = this.virtualFiles.get(path);
        if (!virtualFile) {
          throw new FileSystemError(`File not found: ${path}`, 'ENOENT');
        }
        return virtualFile.content;
      }
    } catch (error) {
      throw new FileSystemError(`Failed to read file: ${error.message}`, 'EACCES');
    }
  }

  async writeFile(path: string, data: Uint8Array): Promise<void> {
    await this.validatePath(path);
    await this.validateFileWrite(path, data);

    try {
      if (this.rootHandle) {
        const fileHandle = await this.getFileHandle(path, { create: true });
        const writable = await fileHandle.createWritable();
        
        try {
          await writable.write(data);
          await writable.close();
        } catch (error) {
          await writable.abort();
          throw error;
        }
      } else {
        // Virtual file system
        this.virtualFiles.set(path, {
          content: data,
          lastModified: Date.now(),
          size: data.length,
          mimeType: this.getMimeType(path)
        });
      }

      // Update cache
      this.cache.set(path, {
        content: data,
        lastModified: Date.now(),
        size: data.length,
        cachedAt: Date.now()
      });

      // Notify watchers
      this.notifyWatchers(path, 'change');
      
      // Log operation
      await this.logFileOperation('write', path, data.length);
      
      this.emit('fileChanged', path);
    } catch (error) {
      throw new FileSystemError(`Failed to write file: ${error.message}`, 'EACCES');
    }
  }

  async deleteFile(path: string): Promise<void> {
    await this.validatePath(path);

    try {
      if (this.rootHandle) {
        const parentPath = this.getParentPath(path);
        const fileName = this.getFileName(path);
        const parentHandle = await this.getDirectoryHandle(parentPath);
        await parentHandle.removeEntry(fileName);
      } else {
        // Virtual file system
        if (!this.virtualFiles.has(path)) {
          throw new FileSystemError(`File not found: ${path}`, 'ENOENT');
        }
        this.virtualFiles.delete(path);
      }

      // Remove from cache
      this.cache.delete(path);
      
      // Notify watchers
      this.notifyWatchers(path, 'unlink');
      
      // Log operation
      await this.logFileOperation('delete', path);
      
      this.emit('fileDeleted', path);
    } catch (error) {
      throw new FileSystemError(`Failed to delete file: ${error.message}`, 'EACCES');
    }
  }

  async copyFile(sourcePath: string, targetPath: string): Promise<void> {
    await this.validatePath(sourcePath);
    await this.validatePath(targetPath);

    try {
      const content = await this.readFile(sourcePath);
      await this.writeFile(targetPath, content);
      
      // Log operation
      await this.logFileOperation('copy', sourcePath, content.length, targetPath);
      
      this.emit('fileCopied', sourcePath, targetPath);
    } catch (error) {
      throw new FileSystemError(`Failed to copy file: ${error.message}`, 'EACCES');
    }
  }

  async moveFile(sourcePath: string, targetPath: string): Promise<void> {
    await this.validatePath(sourcePath);
    await this.validatePath(targetPath);

    try {
      await this.copyFile(sourcePath, targetPath);
      await this.deleteFile(sourcePath);
      
      // Log operation
      await this.logFileOperation('move', sourcePath, 0, targetPath);
      
      this.emit('fileMoved', sourcePath, targetPath);
    } catch (error) {
      throw new FileSystemError(`Failed to move file: ${error.message}`, 'EACCES');
    }
  }

  // Directory operations
  async createDirectory(path: string): Promise<void> {
    await this.validatePath(path);

    try {
      if (this.rootHandle) {
        const parts = path.split('/').filter(p => p);
        let currentHandle = this.rootHandle;
        
        for (const part of parts) {
          try {
            currentHandle = await currentHandle.getDirectoryHandle(part);
          } catch {
            currentHandle = await currentHandle.getDirectoryHandle(part, { create: true });
          }
        }
      } else {
        // Virtual file system - just mark as directory
        this.virtualFiles.set(path, {
          content: new Uint8Array(),
          lastModified: Date.now(),
          size: 0,
          mimeType: 'inode/directory',
          isDirectory: true
        });
      }

      // Notify watchers
      this.notifyWatchers(path, 'addDir');
      
      // Log operation
      await this.logFileOperation('mkdir', path);
      
      this.emit('directoryCreated', path);
    } catch (error) {
      throw new FileSystemError(`Failed to create directory: ${error.message}`, 'EACCES');
    }
  }

  async listDirectory(path: string): Promise<FileSystemEntry[]> {
    await this.validatePath(path);

    try {
      if (this.rootHandle) {
        const dirHandle = await this.getDirectoryHandle(path);
        const entries: FileSystemEntry[] = [];
        
        for await (const [name, handle] of dirHandle.entries()) {
          const entry = await this.createFileSystemEntry(handle, `${path}/${name}`);
          entries.push(entry);
        }
        
        return this.sortEntries(entries);
      } else {
        // Virtual file system
        const entries: FileSystemEntry[] = [];
        const pathPrefix = path === '/' ? '' : path;
        
        for (const [filePath, virtualFile] of this.virtualFiles) {
          if (filePath.startsWith(pathPrefix + '/')) {
            const relativePath = filePath.substring(pathPrefix.length + 1);
            if (!relativePath.includes('/')) {
              // Direct child
              const entry = await this.createFileSystemEntryFromVirtual(filePath, virtualFile);
              entries.push(entry);
            }
          }
        }
        
        return this.sortEntries(entries);
      }
    } catch (error) {
      throw new FileSystemError(`Failed to list directory: ${error.message}`, 'EACCES');
    }
  }

  // Search functionality
  async searchFiles(query: SearchQuery): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    await this.searchDirectory(query.path, query, results);
    
    return results
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, query.maxResults);
  }

  // File watching
  async watchFile(path: string, callback: FileWatchCallback, options: WatchOptions = {}): Promise<FileWatcher> {
    const watcher: FileWatcher = {
      id: this.generateId(),
      path,
      callback,
      options,
      isActive: true
    };

    const existingWatchers = this.watchers.get(path) || [];
    existingWatchers.push(watcher);
    this.watchers.set(path, existingWatchers);

    if (existingWatchers.length === 1) {
      await this.startWatching(path);
    }

    return watcher;
  }

  async unwatchFile(watcherId: string): Promise<void> {
    for (const [path, watchers] of this.watchers.entries()) {
      const index = watchers.findIndex(w => w.id === watcherId);
      if (index !== -1) {
        watchers[index].isActive = false;
        watchers.splice(index, 1);
        
        if (watchers.length === 0) {
          this.watchers.delete(path);
        }
        break;
      }
    }
  }

  // Utility methods
  private async validatePath(path: string): Promise<void> {
    if (!path || path.includes('..') || path.includes('//')) {
      throw new FileSystemError('Invalid path', 'EINVAL');
    }
  }

  private async validateFileWrite(path: string, data: Uint8Array): Promise<void> {
    if (data.length > this.maxFileSize) {
      throw new FileSystemError(`File size exceeds limit: ${data.length} bytes`, 'EFBIG');
    }

    const extension = path.split('.').pop()?.toLowerCase();
    if (extension && this.blockedExtensions.includes(extension)) {
      throw new FileSystemError(`File type not allowed: ${extension}`, 'EPERM');
    }
  }

  private async getFileHandle(path: string, options: { create?: boolean } = {}): Promise<FileSystemFileHandle> {
    if (!this.rootHandle) {
      throw new FileSystemError('File system not initialized', 'ENOTCONN');
    }

    const parts = path.split('/').filter(p => p);
    let currentHandle = this.rootHandle;
    
    for (let i = 0; i < parts.length - 1; i++) {
      currentHandle = await currentHandle.getDirectoryHandle(parts[i], { create: options.create });
    }
    
    return await currentHandle.getFileHandle(parts[parts.length - 1], options);
  }

  private async getDirectoryHandle(path: string): Promise<FileSystemDirectoryHandle> {
    if (!this.rootHandle) {
      throw new FileSystemError('File system not initialized', 'ENOTCONN');
    }

    if (path === '/' || path === '') {
      return this.rootHandle;
    }

    const parts = path.split('/').filter(p => p);
    let currentHandle = this.rootHandle;
    
    for (const part of parts) {
      currentHandle = await currentHandle.getDirectoryHandle(part);
    }
    
    return currentHandle;
  }

  private async createFileSystemEntry(handle: FileSystemHandle, path: string): Promise<FileSystemEntry> {
    const isFile = handle.kind === 'file';
    const name = handle.name;
    
    if (isFile) {
      const fileHandle = handle as FileSystemFileHandle;
      const file = await fileHandle.getFile();
      
      return {
        name,
        path,
        type: 'file',
        size: file.size,
        modified: new Date(file.lastModified),
        created: new Date(file.lastModified), // File API doesn't provide creation time
        permissions: {
          read: true,
          write: true,
          execute: false,
          owner: 'user',
          group: 'user',
          mode: 'rw-r--r--'
        },
        isHidden: name.startsWith('.'),
        isSymlink: false,
        mimeType: file.type || this.getMimeType(name)
      };
    } else {
      return {
        name,
        path,
        type: 'directory',
        size: 0,
        modified: new Date(),
        created: new Date(),
        permissions: {
          read: true,
          write: true,
          execute: true,
          owner: 'user',
          group: 'user',
          mode: 'rwxr-xr-x'
        },
        isHidden: name.startsWith('.'),
        isSymlink: false
      };
    }
  }

  private async createFileSystemEntryFromVirtual(path: string, virtualFile: VirtualFile): Promise<FileSystemEntry> {
    const name = this.getFileName(path);
    
    return {
      name,
      path,
      type: virtualFile.isDirectory ? 'directory' : 'file',
      size: virtualFile.size,
      modified: new Date(virtualFile.lastModified),
      created: new Date(virtualFile.lastModified),
      permissions: {
        read: true,
        write: true,
        execute: virtualFile.isDirectory,
        owner: 'user',
        group: 'user',
        mode: virtualFile.isDirectory ? 'rwxr-xr-x' : 'rw-r--r--'
      },
      isHidden: name.startsWith('.'),
      isSymlink: false,
      mimeType: virtualFile.mimeType
    };
  }

  private setupVirtualFileSystem(): void {
    // Create some sample files for demonstration
    this.virtualFiles.set('/README.md', {
      content: new TextEncoder().encode('# Welcome to Virtual File System\n\nThis is a demo file system.'),
      lastModified: Date.now(),
      size: 55,
      mimeType: 'text/markdown'
    });

    this.virtualFiles.set('/src', {
      content: new Uint8Array(),
      lastModified: Date.now(),
      size: 0,
      mimeType: 'inode/directory',
      isDirectory: true
    });

    this.virtualFiles.set('/src/main.js', {
      content: new TextEncoder().encode('console.log("Hello, World!");'),
      lastModified: Date.now(),
      size: 28,
      mimeType: 'application/javascript'
    });
  }

  private async initializeVirtualFileSystem(): Promise<void> {
    console.log('Virtual file system initialized');
    this.emit('initialized');
  }

  private sortEntries(entries: FileSystemEntry[]): FileSystemEntry[] {
    return entries.sort((a, b) => {
      // Directories first, then files
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });
  }

  private getMimeType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'js': 'application/javascript',
      'ts': 'application/typescript',
      'json': 'application/json',
      'html': 'text/html',
      'css': 'text/css',
      'py': 'text/x-python',
      'java': 'text/x-java-source',
      'cpp': 'text/x-c++src',
      'c': 'text/x-csrc',
      'go': 'text/x-go',
      'rs': 'text/x-rust',
      'php': 'text/x-php',
      'rb': 'text/x-ruby',
      'xml': 'application/xml',
      'yml': 'application/x-yaml',
      'yaml': 'application/x-yaml'
    };
    return mimeTypes[ext || ''] || 'application/octet-stream';
  }

  private getParentPath(path: string): string {
    const parts = path.split('/').filter(p => p);
    return parts.length > 1 ? '/' + parts.slice(0, -1).join('/') : '/';
  }

  private getFileName(path: string): string {
    return path.split('/').pop() || '';
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  private async logFileOperation(operation: string, path: string, size?: number, targetPath?: string): Promise<void> {
    console.log(`File operation: ${operation} on ${path}`, { size, targetPath });
  }

  private async startWatching(path: string): Promise<void> {
    // Implement file watching logic
    console.log(`Started watching: ${path}`);
  }

  private notifyWatchers(path: string, event: FileWatchEvent): void {
    const watchers = this.watchers.get(path) || [];
    watchers.forEach(watcher => {
      if (watcher.isActive) {
        try {
          watcher.callback(event, path);
        } catch (error) {
          console.error('File watcher callback error:', error);
        }
      }
    });
  }

  private async searchDirectory(dirPath: string, query: SearchQuery, results: SearchResult[]): Promise<void> {
    try {
      const entries = await this.listDirectory(dirPath);
      
      for (const entry of entries) {
        if (this.shouldSkipEntry(entry, query)) continue;
        
        if (entry.type === 'directory') {
          await this.searchDirectory(entry.path, query, results);
        } else {
          const relevance = await this.calculateRelevance(entry, query);
          if (relevance > 0) {
            results.push({
              entry,
              relevance,
              matches: await this.findMatches(entry, query)
            });
          }
        }
      }
    } catch (error) {
      console.error(`Failed to search directory ${dirPath}:`, error);
    }
  }

  private shouldSkipEntry(entry: FileSystemEntry, query: SearchQuery): boolean {
    // Skip hidden files unless explicitly searching for them
    if (entry.isHidden && !query.pattern.includes('.')) {
      return true;
    }

    // Check exclude patterns
    for (const pattern of query.excludePatterns) {
      if (entry.name.includes(pattern)) {
        return true;
      }
    }

    // Check file types
    if (query.fileTypes.length > 0) {
      const ext = entry.name.split('.').pop()?.toLowerCase();
      if (!ext || !query.fileTypes.includes(ext)) {
        return true;
      }
    }

    return false;
  }

  private async calculateRelevance(entry: FileSystemEntry, query: SearchQuery): Promise<number> {
    let relevance = 0;
    
    // Name matching
    if (entry.name.toLowerCase().includes(query.pattern.toLowerCase())) {
      relevance += 10;
    }

    // Content matching (if enabled)
    if (query.includeContent && entry.type === 'file') {
      try {
        const content = await this.readFile(entry.path);
        const contentStr = new TextDecoder().decode(content);
        const matches = contentStr.toLowerCase().includes(query.pattern.toLowerCase());
        if (matches) {
          relevance += 5;
        }
      } catch {
        // Ignore content search errors
      }
    }

    return relevance;
  }

  private async findMatches(entry: FileSystemEntry, query: SearchQuery): Promise<SearchMatch[]> {
    const matches: SearchMatch[] = [];
    
    if (query.includeContent && entry.type === 'file') {
      try {
        const content = await this.readFile(entry.path);
        const contentStr = new TextDecoder().decode(content);
        const lines = contentStr.split('\n');
        
        lines.forEach((line, index) => {
          const match = line.toLowerCase().indexOf(query.pattern.toLowerCase());
          if (match !== -1) {
            matches.push({
              line: index + 1,
              column: match + 1,
              text: query.pattern,
              context: line.trim()
            });
          }
        });
      } catch {
        // Ignore content search errors
      }
    }

    return matches;
  }

  private async validateRootDirectory(): Promise<void> {
    if (!this.rootHandle) {
      throw new FileSystemError('No root directory handle', 'ENOTCONN');
    }
  }

  private async buildInitialCache(): Promise<void> {
    // Build cache of frequently accessed files
    console.log('Building initial file cache');
  }
}

interface CachedEntry {
  content: Uint8Array;
  lastModified: number;
  size: number;
  cachedAt: number;
}

interface VirtualFile {
  content: Uint8Array;
  lastModified: number;
  size: number;
  mimeType: string;
  isDirectory?: boolean;
}

export default WebFileSystemService;