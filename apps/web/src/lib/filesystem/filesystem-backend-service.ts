import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { EventEmitter } from 'events';
import { FileSystemEntry, FileInfo, SearchQuery, SearchResult, FileOperation, OperationResult, FilePermissions } from './filesystem-service';

interface CachedEntry {
  content: Uint8Array;
  lastModified: number;
  size: number;
  cachedAt: number;
  hash: string;
}

interface FileWatcher {
  id: string;
  path: string;
  callback: (event: string, path: string, info?: FileInfo) => void;
  isActive: boolean;
}

export class FileSystemService extends EventEmitter {
  private cache: Map<string, CachedEntry> = new Map();
  private watchers: Map<string, FileWatcher[]> = new Map();
  private readonly maxFileSize = 100 * 1024 * 1024; // 100MB
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes
  private readonly allowedExtensions = new Set([
    '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.json', '.html', '.css', '.scss', '.sass',
    '.py', '.java', '.cpp', '.c', '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs', '.kt',
    '.swift', '.sql', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.log',
    '.sh', '.bat', '.ps1', '.dockerfile', '.dockerignore', '.gitignore', '.env'
  ]);
  private readonly blockedExtensions = new Set([
    '.exe', '.dll', '.so', '.dylib', '.app', '.dmg', '.pkg', '.msi', '.deb', '.rpm',
    '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.iso', '.img', '.bin'
  ]);
  private readonly maxDepth = 10;
  private readonly rootPath: string;

  constructor(rootPath: string = '/tmp/workspace') {
    super();
    this.rootPath = path.resolve(rootPath);
    this.setupCacheCleanup();
  }

  async readFile(filePath: string): Promise<Uint8Array> {
    const absolutePath = this.resolveAndValidatePath(filePath);
    
    // Check cache
    const cached = this.cache.get(absolutePath);
    if (cached && !this.isCacheExpired(cached)) {
      return cached.content;
    }

    try {
      // Validate file extension
      this.validateFileExtension(absolutePath);

      // Check file size
      const stats = await fs.stat(absolutePath);
      if (stats.size > this.maxFileSize) {
        throw new Error(`File too large: ${stats.size} bytes (max: ${this.maxFileSize})`);
      }

      // Read file
      const buffer = await fs.readFile(absolutePath);
      const content = new Uint8Array(buffer);

      // Update cache
      const hash = this.calculateHash(content);
      this.cache.set(absolutePath, {
        content,
        lastModified: stats.mtime.getTime(),
        size: stats.size,
        cachedAt: Date.now(),
        hash
      });

      this.emit('fileRead', { path: filePath, size: stats.size });
      return content;
    } catch (error) {
      this.emit('error', { operation: 'readFile', path: filePath, error: error.message });
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }

  async writeFile(filePath: string, data: Uint8Array): Promise<void> {
    const absolutePath = this.resolveAndValidatePath(filePath);
    
    try {
      // Validate file size
      if (data.length > this.maxFileSize) {
        throw new Error(`File too large: ${data.length} bytes (max: ${this.maxFileSize})`);
      }

      // Validate file extension
      this.validateFileExtension(absolutePath);

      // Ensure directory exists
      const dir = path.dirname(absolutePath);
      await fs.mkdir(dir, { recursive: true });

      // Write file
      await fs.writeFile(absolutePath, data);

      // Update cache
      const hash = this.calculateHash(data);
      this.cache.set(absolutePath, {
        content: data,
        lastModified: Date.now(),
        size: data.length,
        cachedAt: Date.now(),
        hash
      });

      // Notify watchers
      this.notifyWatchers(absolutePath, 'modified');

      this.emit('fileWritten', { path: filePath, size: data.length });
    } catch (error) {
      this.emit('error', { operation: 'writeFile', path: filePath, error: error.message });
      throw new Error(`Failed to write file: ${error.message}`);
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    const absolutePath = this.resolveAndValidatePath(filePath);
    
    try {
      const stats = await fs.stat(absolutePath);
      
      if (stats.isDirectory()) {
        await fs.rmdir(absolutePath, { recursive: true });
      } else {
        await fs.unlink(absolutePath);
      }

      // Remove from cache
      this.cache.delete(absolutePath);

      // Notify watchers
      this.notifyWatchers(absolutePath, 'deleted');

      this.emit('fileDeleted', { path: filePath });
    } catch (error) {
      this.emit('error', { operation: 'deleteFile', path: filePath, error: error.message });
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  async renameFile(oldPath: string, newPath: string): Promise<void> {
    const absoluteOldPath = this.resolveAndValidatePath(oldPath);
    const absoluteNewPath = this.resolveAndValidatePath(newPath);
    
    try {
      // Ensure target directory exists
      const dir = path.dirname(absoluteNewPath);
      await fs.mkdir(dir, { recursive: true });

      await fs.rename(absoluteOldPath, absoluteNewPath);

      // Update cache
      const cached = this.cache.get(absoluteOldPath);
      if (cached) {
        this.cache.delete(absoluteOldPath);
        this.cache.set(absoluteNewPath, cached);
      }

      // Notify watchers
      this.notifyWatchers(absoluteOldPath, 'deleted');
      this.notifyWatchers(absoluteNewPath, 'created');

      this.emit('fileRenamed', { oldPath, newPath });
    } catch (error) {
      this.emit('error', { operation: 'renameFile', oldPath, newPath, error: error.message });
      throw new Error(`Failed to rename file: ${error.message}`);
    }
  }

  async copyFile(sourcePath: string, targetPath: string): Promise<void> {
    const absoluteSourcePath = this.resolveAndValidatePath(sourcePath);
    const absoluteTargetPath = this.resolveAndValidatePath(targetPath);
    
    try {
      // Ensure target directory exists
      const dir = path.dirname(absoluteTargetPath);
      await fs.mkdir(dir, { recursive: true });

      await fs.copyFile(absoluteSourcePath, absoluteTargetPath);

      // Notify watchers
      this.notifyWatchers(absoluteTargetPath, 'created');

      this.emit('fileCopied', { sourcePath, targetPath });
    } catch (error) {
      this.emit('error', { operation: 'copyFile', sourcePath, targetPath, error: error.message });
      throw new Error(`Failed to copy file: ${error.message}`);
    }
  }

  async createDirectory(dirPath: string): Promise<void> {
    const absolutePath = this.resolveAndValidatePath(dirPath);
    
    try {
      await fs.mkdir(absolutePath, { recursive: true });
      
      this.emit('directoryCreated', { path: dirPath });
    } catch (error) {
      this.emit('error', { operation: 'createDirectory', path: dirPath, error: error.message });
      throw new Error(`Failed to create directory: ${error.message}`);
    }
  }

  async listDirectory(dirPath: string): Promise<FileSystemEntry[]> {
    const absolutePath = this.resolveAndValidatePath(dirPath);
    
    try {
      const entries = await fs.readdir(absolutePath, { withFileTypes: true });
      const results: FileSystemEntry[] = [];

      for (const entry of entries) {
        const entryPath = path.join(dirPath, entry.name);
        const absoluteEntryPath = path.join(absolutePath, entry.name);
        
        try {
          const stats = await fs.stat(absoluteEntryPath);
          
          results.push({
            name: entry.name,
            path: entryPath,
            type: entry.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime,
            created: stats.birthtime,
            permissions: {
              read: true,
              write: true,
              execute: entry.isDirectory()
            },
            isHidden: entry.name.startsWith('.'),
            isSymlink: stats.isSymbolicLink(),
            mimeType: entry.isFile() ? this.getMimeType(entry.name) : undefined
          });
        } catch (error) {
          console.warn(`Failed to get stats for ${entry.name}:`, error);
        }
      }

      return results.sort((a, b) => {
        // Directories first, then files
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
      });
    } catch (error) {
      this.emit('error', { operation: 'listDirectory', path: dirPath, error: error.message });
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  }

  async getFileInfo(filePath: string): Promise<FileInfo> {
    const absolutePath = this.resolveAndValidatePath(filePath);
    
    try {
      const stats = await fs.stat(absolutePath);
      const cached = this.cache.get(absolutePath);
      
      let hash = '';
      let lineCount: number | undefined;
      
      if (stats.isFile()) {
        if (cached) {
          hash = cached.hash;
        } else {
          const content = await this.readFile(filePath);
          hash = this.calculateHash(content);
          
          // Count lines for text files
          if (this.isTextFile(filePath)) {
            const text = new TextDecoder().decode(content);
            lineCount = text.split('\n').length;
          }
        }
      }

      return {
        name: path.basename(filePath),
        path: filePath,
        type: stats.isDirectory() ? 'directory' : 'file',
        size: stats.size,
        modified: stats.mtime,
        created: stats.birthtime,
        permissions: {
          read: true,
          write: true,
          execute: stats.isDirectory()
        },
        isHidden: path.basename(filePath).startsWith('.'),
        isSymlink: stats.isSymbolicLink(),
        mimeType: stats.isFile() ? this.getMimeType(filePath) : undefined,
        encoding: this.isTextFile(filePath) ? 'utf-8' : undefined,
        lineCount,
        hash,
        version: 1,
        metadata: {}
      };
    } catch (error) {
      this.emit('error', { operation: 'getFileInfo', path: filePath, error: error.message });
      throw new Error(`Failed to get file info: ${error.message}`);
    }
  }

  async setFilePermissions(filePath: string, permissions: FilePermissions): Promise<void> {
    const absolutePath = this.resolveAndValidatePath(filePath);
    
    try {
      // Convert permissions to mode
      let mode = 0;
      if (permissions.read) mode |= 0o444;
      if (permissions.write) mode |= 0o222;
      if (permissions.execute) mode |= 0o111;
      
      await fs.chmod(absolutePath, mode);
      
      this.emit('permissionsChanged', { path: filePath, permissions });
    } catch (error) {
      this.emit('error', { operation: 'setFilePermissions', path: filePath, error: error.message });
      throw new Error(`Failed to set permissions: ${error.message}`);
    }
  }

  async exists(filePath: string): Promise<boolean> {
    const absolutePath = this.resolveAndValidatePath(filePath);
    
    try {
      await fs.access(absolutePath);
      return true;
    } catch {
      return false;
    }
  }

  async searchFiles(query: SearchQuery): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    
    try {
      await this.searchDirectory(query.path, query, results, 0);
      
      return results
        .sort((a, b) => b.relevance - a.relevance)
        .slice(0, query.maxResults);
    } catch (error) {
      this.emit('error', { operation: 'searchFiles', query, error: error.message });
      throw new Error(`Failed to search files: ${error.message}`);
    }
  }

  async bulkOperation(operations: FileOperation[]): Promise<OperationResult[]> {
    const results: OperationResult[] = [];
    
    for (const operation of operations) {
      try {
        switch (operation.type) {
          case 'copy':
            await this.copyFile(operation.source, operation.target!);
            results.push({ success: true, operation });
            break;
          case 'move':
            await this.renameFile(operation.source, operation.target!);
            results.push({ success: true, operation });
            break;
          case 'delete':
            await this.deleteFile(operation.source);
            results.push({ success: true, operation });
            break;
          default:
            results.push({ 
              success: false, 
              operation, 
              error: `Unknown operation type: ${operation.type}` 
            });
        }
      } catch (error) {
        results.push({ 
          success: false, 
          operation, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }
    
    return results;
  }

  // File watching
  async watchFile(filePath: string, callback: (event: string, path: string, info?: FileInfo) => void): Promise<string> {
    const watcherId = crypto.randomUUID();
    const watcher: FileWatcher = {
      id: watcherId,
      path: filePath,
      callback,
      isActive: true
    };

    const existingWatchers = this.watchers.get(filePath) || [];
    existingWatchers.push(watcher);
    this.watchers.set(filePath, existingWatchers);

    return watcherId;
  }

  async unwatchFile(watcherId: string): Promise<void> {
    for (const [filePath, watchers] of this.watchers.entries()) {
      const index = watchers.findIndex(w => w.id === watcherId);
      if (index > -1) {
        watchers[index].isActive = false;
        watchers.splice(index, 1);
        if (watchers.length === 0) {
          this.watchers.delete(filePath);
        }
        break;
      }
    }
  }

  // Private methods
  private resolveAndValidatePath(filePath: string): string {
    const absolutePath = path.resolve(this.rootPath, filePath.replace(/^\/+/, ''));
    
    // Ensure the path is within the root directory
    if (!absolutePath.startsWith(this.rootPath)) {
      throw new Error('Path traversal attempt detected');
    }
    
    return absolutePath;
  }

  private validateFileExtension(filePath: string): void {
    const ext = path.extname(filePath).toLowerCase();
    
    if (ext && this.blockedExtensions.has(ext)) {
      throw new Error(`File type not allowed: ${ext}`);
    }
  }

  private isCacheExpired(cached: CachedEntry): boolean {
    return Date.now() - cached.cachedAt > this.cacheTimeout;
  }

  private calculateHash(data: Uint8Array): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private getMimeType(fileName: string): string {
    const ext = path.extname(fileName).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.js': 'text/javascript',
      '.ts': 'text/typescript',
      '.json': 'application/json',
      '.html': 'text/html',
      '.css': 'text/css',
      '.py': 'text/x-python',
      '.java': 'text/x-java-source',
      '.cpp': 'text/x-c++src',
      '.c': 'text/x-csrc',
      '.php': 'text/x-php',
      '.rb': 'text/x-ruby',
      '.go': 'text/x-go',
      '.rs': 'text/x-rust',
      '.sql': 'text/x-sql',
      '.xml': 'application/xml',
      '.yaml': 'text/x-yaml',
      '.yml': 'text/x-yaml'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
  }

  private isTextFile(fileName: string): boolean {
    const ext = path.extname(fileName).toLowerCase();
    return this.allowedExtensions.has(ext);
  }

  private async searchDirectory(dirPath: string, query: SearchQuery, results: SearchResult[], depth: number): Promise<void> {
    if (depth > this.maxDepth) return;
    
    try {
      const entries = await this.listDirectory(dirPath);
      
      for (const entry of entries) {
        // Skip if excluded
        if (this.shouldSkipEntry(entry, query)) continue;
        
        if (entry.type === 'directory') {
          await this.searchDirectory(entry.path, query, results, depth + 1);
        } else {
          const relevance = await this.calculateRelevance(entry, query);
          if (relevance > 0) {
            const matches = await this.findMatches(entry, query);
            results.push({
              entry,
              relevance,
              matches
            });
          }
        }
      }
    } catch (error) {
      // Skip directories that can't be read
      console.warn(`Failed to search directory ${dirPath}:`, error);
    }
  }

  private shouldSkipEntry(entry: FileSystemEntry, query: SearchQuery): boolean {
    // Check exclude patterns
    for (const pattern of query.excludePatterns) {
      if (entry.name.includes(pattern)) {
        return true;
      }
    }
    
    // Check file types
    if (query.fileTypes.length > 0) {
      const ext = path.extname(entry.name).toLowerCase().substring(1);
      if (!query.fileTypes.includes(ext)) {
        return true;
      }
    }
    
    return false;
  }

  private async calculateRelevance(entry: FileSystemEntry, query: SearchQuery): Promise<number> {
    let relevance = 0;
    
    // Name matching
    const nameMatch = this.matchString(entry.name, query.pattern, query.caseSensitive, query.useRegex);
    relevance += nameMatch * 10;
    
    // Content matching (if enabled and is text file)
    if (query.includeContent && entry.type === 'file' && this.isTextFile(entry.name)) {
      try {
        const content = await this.readFile(entry.path);
        const text = new TextDecoder().decode(content);
        const contentMatch = this.matchString(text, query.pattern, query.caseSensitive, query.useRegex);
        relevance += contentMatch * 5;
      } catch {
        // Ignore content read errors
      }
    }
    
    return relevance;
  }

  private matchString(text: string, pattern: string, caseSensitive: boolean, useRegex: boolean): number {
    if (!caseSensitive) {
      text = text.toLowerCase();
      pattern = pattern.toLowerCase();
    }
    
    if (useRegex) {
      try {
        const regex = new RegExp(pattern, caseSensitive ? 'g' : 'gi');
        const matches = text.match(regex);
        return matches ? matches.length : 0;
      } catch {
        return 0;
      }
    } else {
      const matches = text.split(pattern).length - 1;
      return matches;
    }
  }

  private async findMatches(entry: FileSystemEntry, query: SearchQuery): Promise<string[]> {
    const matches: string[] = [];
    
    // Add name if it matches
    if (this.matchString(entry.name, query.pattern, query.caseSensitive, query.useRegex) > 0) {
      matches.push(`name: ${entry.name}`);
    }
    
    // Add content matches if enabled
    if (query.includeContent && entry.type === 'file' && this.isTextFile(entry.name)) {
      try {
        const content = await this.readFile(entry.path);
        const text = new TextDecoder().decode(content);
        const lines = text.split('\n');
        
        for (let i = 0; i < lines.length && matches.length < 10; i++) {
          if (this.matchString(lines[i], query.pattern, query.caseSensitive, query.useRegex) > 0) {
            matches.push(`line ${i + 1}: ${lines[i].trim().substring(0, 100)}`);
          }
        }
      } catch {
        // Ignore content read errors
      }
    }
    
    return matches;
  }

  private notifyWatchers(filePath: string, event: string): void {
    const watchers = this.watchers.get(filePath) || [];
    for (const watcher of watchers) {
      if (watcher.isActive) {
        try {
          watcher.callback(event, filePath);
        } catch (error) {
          console.error('Watcher callback error:', error);
        }
      }
    }
  }

  private setupCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [path, cached] of this.cache.entries()) {
        if (now - cached.cachedAt > this.cacheTimeout) {
          this.cache.delete(path);
        }
      }
    }, this.cacheTimeout);
  }
}