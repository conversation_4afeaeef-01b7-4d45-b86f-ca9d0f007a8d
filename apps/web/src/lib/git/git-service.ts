import git from 'isomorphic-git';
import { EventEmitter } from 'events';

export interface GitStatus {
  branch: string;
  staged: string[];
  unstaged: string[];
  untracked: string[];
  conflicted: string[];
  ahead: number;
  behind: number;
  remoteTrackingBranch?: string;
}

export interface GitCommit {
  hash: string;
  author: GitAuthor;
  committer: GitAuthor;
  message: string;
  date: Date;
  parents: string[];
  files: GitFileChange[];
  branches: string[];
  tags: string[];
}

export interface GitAuthor {
  name: string;
  email: string;
  timestamp?: number;
  timezoneOffset?: number;
}

export interface GitFileChange {
  filename: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed';
  additions: number;
  deletions: number;
  oldFilename?: string;
}

export interface GitBranch {
  name: string;
  isLocal: boolean;
  isRemote: boolean;
  isCurrent: boolean;
  upstream?: string;
  ahead: number;
  behind: number;
  lastCommit: GitCommit;
}

export interface GitDiff {
  filename: string;
  oldFilename?: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed';
  hunks: GitDiffHunk[];
}

export interface GitDiffHunk {
  oldStart: number;
  oldLines: number;
  newStart: number;
  newLines: number;
  header: string;
  lines: GitDiffLine[];
}

export interface GitDiffLine {
  type: 'context' | 'added' | 'deleted';
  content: string;
  oldLineNumber?: number;
  newLineNumber?: number;
}

export interface CloneOptions {
  branch?: string;
  singleBranch?: boolean;
  depth?: number;
  noCheckout?: boolean;
}

export interface CommitOptions {
  author?: GitAuthor;
  committer?: GitAuthor;
  amend?: boolean;
  allowEmpty?: boolean;
}

export interface PushOptions {
  force?: boolean;
  setUpstream?: boolean;
  followTags?: boolean;
}

export interface PullOptions {
  rebase?: boolean;
  fastForwardOnly?: boolean;
  strategy?: 'ours' | 'theirs' | 'recursive';
}

export interface FetchOptions {
  prune?: boolean;
  tags?: boolean;
  depth?: number;
}

export interface MergeOptions {
  strategy?: 'ours' | 'theirs' | 'recursive';
  noCommit?: boolean;
  squash?: boolean;
}

export interface MergeResult {
  success: boolean;
  conflicts: string[];
  commitHash?: string;
}

export interface RebaseOptions {
  onto?: string;
  interactive?: boolean;
  preserveMerges?: boolean;
}

export interface RebaseResult {
  success: boolean;
  conflicts: string[];
  currentCommit?: string;
}

export interface ConflictResolution {
  filename: string;
  content: string;
  resolution: 'ours' | 'theirs' | 'manual';
}

export interface GitAuth {
  username?: string;
  password?: string;
  token?: string;
  publicKey?: string;
  privateKey?: string;
  passphrase?: string;
}

export interface LogOptions {
  maxCount?: number;
  since?: Date;
  until?: Date;
  author?: string;
  grep?: string;
  follow?: boolean;
  filepath?: string;
}

class GitError extends Error {
  constructor(message: string, public code: string = 'GIT_ERROR') {
    super(message);
    this.name = 'GitError';
  }
}

export class WebGitService extends EventEmitter {
  private fs: any;
  private http: any;
  private workdir: string = '/';
  private credentials: GitCredentialManager;
  private repoCache: Map<string, any> = new Map();

  constructor() {
    super();
    this.credentials = new GitCredentialManager();
    this.initializeFilesystem();
  }

  private async initializeFilesystem(): Promise<void> {
    try {
      // Try to use native filesystem if available
      if (typeof window !== 'undefined' && 'showDirectoryPicker' in window) {
        // Browser environment with File System Access API
        this.fs = {
          readFile: async (filepath: string) => {
            // Implement using File System Access API
            throw new Error('File System Access API integration not implemented');
          },
          writeFile: async (filepath: string, data: any) => {
            // Implement using File System Access API
            throw new Error('File System Access API integration not implemented');
          },
          mkdir: async (dirpath: string) => {
            // Implement using File System Access API
            throw new Error('File System Access API integration not implemented');
          },
          readdir: async (dirpath: string) => {
            // Implement using File System Access API
            throw new Error('File System Access API integration not implemented');
          },
          stat: async (filepath: string) => {
            // Implement using File System Access API
            throw new Error('File System Access API integration not implemented');
          },
          lstat: async (filepath: string) => {
            // Implement using File System Access API
            throw new Error('File System Access API integration not implemented');
          },
          unlink: async (filepath: string) => {
            // Implement using File System Access API
            throw new Error('File System Access API integration not implemented');
          },
          rmdir: async (dirpath: string) => {
            // Implement using File System Access API
            throw new Error('File System Access API integration not implemented');
          }
        };
      } else {
        // Fallback to virtual filesystem or mock implementation
        this.fs = this.createMockFilesystem();
      }

      // HTTP client for Git operations
      this.http = {
        request: async (url: any) => {
          // Mock HTTP implementation for demo
          return { statusCode: 200, body: [] };
        }
      };
    } catch (error) {
      console.warn('Failed to initialize filesystem for Git, using mock implementation:', error);
      this.fs = this.createMockFilesystem();
    }
  }

  private createMockFilesystem(): any {
    const mockFiles: Map<string, any> = new Map();
    
    return {
      readFile: async (filepath: string) => {
        const file = mockFiles.get(filepath);
        if (!file) throw new Error(`File not found: ${filepath}`);
        return file.content;
      },
      writeFile: async (filepath: string, data: any) => {
        mockFiles.set(filepath, { content: data, stat: { mode: 33188, size: data.length } });
      },
      mkdir: async (dirpath: string) => {
        mockFiles.set(dirpath, { isDirectory: true, stat: { mode: 16877 } });
      },
      readdir: async (dirpath: string) => {
        const entries = [];
        for (const [path, file] of mockFiles) {
          if (path.startsWith(dirpath + '/') && !path.substring(dirpath.length + 1).includes('/')) {
            entries.push(path.split('/').pop());
          }
        }
        return entries;
      },
      stat: async (filepath: string) => {
        const file = mockFiles.get(filepath);
        if (!file) throw new Error(`File not found: ${filepath}`);
        return file.stat || { mode: 33188, size: 0 };
      },
      lstat: async (filepath: string) => {
        return await this.fs.stat(filepath);
      },
      unlink: async (filepath: string) => {
        mockFiles.delete(filepath);
      },
      rmdir: async (dirpath: string) => {
        mockFiles.delete(dirpath);
      }
    };
  }

  // Repository operations
  async initRepository(path: string): Promise<void> {
    try {
      this.workdir = path;
      
      await git.init({
        fs: this.fs,
        dir: path,
        defaultBranch: 'main'
      });

      await this.setupDefaultConfig(path);
      this.emit('repositoryInitialized', path);
    } catch (error) {
      throw new GitError(`Failed to initialize repository: ${error.message}`);
    }
  }

  async cloneRepository(url: string, path: string, options: CloneOptions = {}): Promise<void> {
    try {
      this.workdir = path;
      const auth = await this.credentials.getAuthForUrl(url);

      await git.clone({
        fs: this.fs,
        http: this.http,
        dir: path,
        url,
        ref: options.branch || 'main',
        singleBranch: options.singleBranch || false,
        depth: options.depth,
        noCheckout: options.noCheckout || false,
        ...auth
      });

      await this.setupRemoteTracking(path, url);
      this.emit('repositoryCloned', url, path);
    } catch (error) {
      throw new GitError(`Failed to clone repository: ${error.message}`);
    }
  }

  async addRemote(name: string, url: string): Promise<void> {
    try {
      await git.addRemote({
        fs: this.fs,
        dir: this.workdir,
        remote: name,
        url
      });

      this.emit('remoteAdded', name, url);
    } catch (error) {
      throw new GitError(`Failed to add remote: ${error.message}`);
    }
  }

  async removeRemote(name: string): Promise<void> {
    try {
      await git.deleteRemote({
        fs: this.fs,
        dir: this.workdir,
        remote: name
      });

      this.emit('remoteRemoved', name);
    } catch (error) {
      throw new GitError(`Failed to remove remote: ${error.message}`);
    }
  }

  // File operations
  async stageFiles(files: string[]): Promise<void> {
    try {
      for (const file of files) {
        await git.add({
          fs: this.fs,
          dir: this.workdir,
          filepath: file
        });
      }

      this.emit('filesStaged', files);
    } catch (error) {
      throw new GitError(`Failed to stage files: ${error.message}`);
    }
  }

  async unstageFiles(files: string[]): Promise<void> {
    try {
      for (const file of files) {
        await git.resetIndex({
          fs: this.fs,
          dir: this.workdir,
          filepath: file
        });
      }

      this.emit('filesUnstaged', files);
    } catch (error) {
      throw new GitError(`Failed to unstage files: ${error.message}`);
    }
  }

  async stageAllChanges(): Promise<void> {
    try {
      const status = await this.getStatus();
      const allFiles = [...status.unstaged, ...status.untracked];
      
      if (allFiles.length > 0) {
        await this.stageFiles(allFiles);
      }
    } catch (error) {
      throw new GitError(`Failed to stage all changes: ${error.message}`);
    }
  }

  async getStatus(): Promise<GitStatus> {
    try {
      const status = await git.statusMatrix({
        fs: this.fs,
        dir: this.workdir
      });

      const staged: string[] = [];
      const unstaged: string[] = [];
      const untracked: string[] = [];

      for (const [filepath, headStatus, workdirStatus, stageStatus] of status) {
        if (headStatus === 1 && workdirStatus === 1 && stageStatus === 1) {
          // No changes
          continue;
        } else if (headStatus === 1 && workdirStatus === 1 && stageStatus !== 1) {
          staged.push(filepath);
        } else if (headStatus === 1 && workdirStatus !== 1 && stageStatus === 1) {
          unstaged.push(filepath);
        } else if (headStatus === 0 && workdirStatus === 1 && stageStatus !== 0) {
          if (stageStatus === 1) {
            staged.push(filepath);
          } else {
            untracked.push(filepath);
          }
        } else if (headStatus === 0 && workdirStatus === 1 && stageStatus === 0) {
          untracked.push(filepath);
        }
      }

      const currentBranch = await git.currentBranch({
        fs: this.fs,
        dir: this.workdir,
        fullname: false
      });

      const branchInfo = await this.getBranchInfo(currentBranch || 'main');

      return {
        branch: currentBranch || 'main',
        staged,
        unstaged,
        untracked,
        conflicted: [], // TODO: Implement conflict detection
        ahead: branchInfo.ahead,
        behind: branchInfo.behind,
        remoteTrackingBranch: branchInfo.upstream
      };
    } catch (error) {
      throw new GitError(`Failed to get status: ${error.message}`);
    }
  }

  async getDiff(file?: string, staged: boolean = false): Promise<GitDiff[]> {
    try {
      // isomorphic-git doesn't have direct diff support, so we'll implement a basic version
      const status = await this.getStatus();
      const diffs: GitDiff[] = [];

      const filesToCheck = file ? [file] : (staged ? status.staged : status.unstaged);

      for (const filepath of filesToCheck) {
        try {
          const diff = await this.getFileDiff(filepath, staged);
          if (diff) {
            diffs.push(diff);
          }
        } catch (error) {
          console.warn(`Failed to get diff for ${filepath}:`, error);
        }
      }

      return diffs;
    } catch (error) {
      throw new GitError(`Failed to get diff: ${error.message}`);
    }
  }

  // Commit operations
  async commit(message: string, options: CommitOptions = {}): Promise<string> {
    try {
      const author = options.author || await this.getDefaultAuthor();
      const committer = options.committer || author;

      const hash = await git.commit({
        fs: this.fs,
        dir: this.workdir,
        message,
        author,
        committer
      });

      this.emit('committed', hash, message);
      return hash;
    } catch (error) {
      throw new GitError(`Failed to commit: ${error.message}`);
    }
  }

  async getCommitHistory(options: LogOptions = {}): Promise<GitCommit[]> {
    try {
      const commits = await git.log({
        fs: this.fs,
        dir: this.workdir,
        depth: options.maxCount || 100,
        since: options.since,
        until: options.until
      });

      return commits.map(commit => ({
        hash: commit.oid,
        author: {
          name: commit.commit.author.name,
          email: commit.commit.author.email,
          timestamp: commit.commit.author.timestamp,
          timezoneOffset: commit.commit.author.timezoneOffset
        },
        committer: {
          name: commit.commit.committer.name,
          email: commit.commit.committer.email,
          timestamp: commit.commit.committer.timestamp,
          timezoneOffset: commit.commit.committer.timezoneOffset
        },
        message: commit.commit.message,
        date: new Date(commit.commit.author.timestamp * 1000),
        parents: commit.commit.parent,
        files: [], // TODO: Implement file changes
        branches: [],
        tags: []
      }));
    } catch (error) {
      throw new GitError(`Failed to get commit history: ${error.message}`);
    }
  }

  async getCommit(hash: string): Promise<GitCommit> {
    try {
      const commit = await git.readCommit({
        fs: this.fs,
        dir: this.workdir,
        oid: hash
      });

      return {
        hash: commit.oid,
        author: {
          name: commit.commit.author.name,
          email: commit.commit.author.email,
          timestamp: commit.commit.author.timestamp,
          timezoneOffset: commit.commit.author.timezoneOffset
        },
        committer: {
          name: commit.commit.committer.name,
          email: commit.commit.committer.email,
          timestamp: commit.commit.committer.timestamp,
          timezoneOffset: commit.commit.committer.timezoneOffset
        },
        message: commit.commit.message,
        date: new Date(commit.commit.author.timestamp * 1000),
        parents: commit.commit.parent,
        files: [], // TODO: Implement file changes
        branches: [],
        tags: []
      };
    } catch (error) {
      throw new GitError(`Failed to get commit: ${error.message}`);
    }
  }

  // Branch operations
  async createBranch(name: string, startPoint?: string): Promise<void> {
    try {
      await git.branch({
        fs: this.fs,
        dir: this.workdir,
        ref: name,
        object: startPoint
      });

      this.emit('branchCreated', name, startPoint);
    } catch (error) {
      throw new GitError(`Failed to create branch: ${error.message}`);
    }
  }

  async switchBranch(name: string): Promise<void> {
    try {
      await git.checkout({
        fs: this.fs,
        dir: this.workdir,
        ref: name
      });

      this.emit('branchSwitched', name);
    } catch (error) {
      throw new GitError(`Failed to switch branch: ${error.message}`);
    }
  }

  async deleteBranch(name: string, force: boolean = false): Promise<void> {
    try {
      await git.deleteBranch({
        fs: this.fs,
        dir: this.workdir,
        ref: name
      });

      this.emit('branchDeleted', name);
    } catch (error) {
      throw new GitError(`Failed to delete branch: ${error.message}`);
    }
  }

  async getBranches(): Promise<GitBranch[]> {
    try {
      const branches = await git.listBranches({
        fs: this.fs,
        dir: this.workdir
      });

      const currentBranch = await git.currentBranch({
        fs: this.fs,
        dir: this.workdir,
        fullname: false
      });

      const gitBranches: GitBranch[] = [];

      for (const branchName of branches) {
        const branchInfo = await this.getBranchInfo(branchName);
        const lastCommit = await this.getLastCommitForBranch(branchName);

        gitBranches.push({
          name: branchName,
          isLocal: true,
          isRemote: false,
          isCurrent: branchName === currentBranch,
          upstream: branchInfo.upstream,
          ahead: branchInfo.ahead,
          behind: branchInfo.behind,
          lastCommit
        });
      }

      return gitBranches;
    } catch (error) {
      throw new GitError(`Failed to get branches: ${error.message}`);
    }
  }

  async getCurrentBranch(): Promise<string> {
    try {
      const branch = await git.currentBranch({
        fs: this.fs,
        dir: this.workdir,
        fullname: false
      });

      return branch || 'main';
    } catch (error) {
      throw new GitError(`Failed to get current branch: ${error.message}`);
    }
  }

  // Remote operations
  async push(remote: string, branch: string, options: PushOptions = {}): Promise<void> {
    try {
      const auth = await this.credentials.getAuthForRemote(this.workdir, remote);

      await git.push({
        fs: this.fs,
        http: this.http,
        dir: this.workdir,
        remote,
        ref: branch,
        force: options.force || false,
        ...auth
      });

      this.emit('pushed', remote, branch);
    } catch (error) {
      throw new GitError(`Failed to push: ${error.message}`);
    }
  }

  async pull(remote: string, branch: string, options: PullOptions = {}): Promise<void> {
    try {
      const auth = await this.credentials.getAuthForRemote(this.workdir, remote);

      // Fetch first
      await git.fetch({
        fs: this.fs,
        http: this.http,
        dir: this.workdir,
        remote,
        ref: branch,
        ...auth
      });

      // Then merge
      await git.merge({
        fs: this.fs,
        dir: this.workdir,
        ours: branch,
        theirs: `${remote}/${branch}`
      });

      this.emit('pulled', remote, branch);
    } catch (error) {
      throw new GitError(`Failed to pull: ${error.message}`);
    }
  }

  async fetch(remote: string, options: FetchOptions = {}): Promise<void> {
    try {
      const auth = await this.credentials.getAuthForRemote(this.workdir, remote);

      await git.fetch({
        fs: this.fs,
        http: this.http,
        dir: this.workdir,
        remote,
        prune: options.prune || false,
        ...auth
      });

      this.emit('fetched', remote);
    } catch (error) {
      throw new GitError(`Failed to fetch: ${error.message}`);
    }
  }

  // Merge operations
  async merge(branch: string, options: MergeOptions = {}): Promise<MergeResult> {
    try {
      await git.merge({
        fs: this.fs,
        dir: this.workdir,
        ours: await this.getCurrentBranch(),
        theirs: branch
      });

      this.emit('merged', branch);
      return {
        success: true,
        conflicts: []
      };
    } catch (error) {
      if (error.message.includes('conflict')) {
        return {
          success: false,
          conflicts: [] // TODO: Parse conflict files
        };
      }
      throw new GitError(`Failed to merge: ${error.message}`);
    }
  }

  async rebase(branch: string, options: RebaseOptions = {}): Promise<RebaseResult> {
    try {
      // isomorphic-git doesn't support rebase yet
      throw new GitError('Rebase operation not supported in browser environment');
    } catch (error) {
      throw new GitError(`Failed to rebase: ${error.message}`);
    }
  }

  async resolveConflicts(resolutions: ConflictResolution[]): Promise<void> {
    try {
      for (const resolution of resolutions) {
        await this.fs.writeFile(resolution.filename, resolution.content);
        await this.stageFiles([resolution.filename]);
      }

      this.emit('conflictsResolved', resolutions.map(r => r.filename));
    } catch (error) {
      throw new GitError(`Failed to resolve conflicts: ${error.message}`);
    }
  }

  // Private helper methods
  private async setupDefaultConfig(path: string): Promise<void> {
    try {
      await git.setConfig({
        fs: this.fs,
        dir: path,
        path: 'user.name',
        value: 'Developer'
      });

      await git.setConfig({
        fs: this.fs,
        dir: path,
        path: 'user.email',
        value: '<EMAIL>'
      });
    } catch (error) {
      console.warn('Failed to setup default Git config:', error);
    }
  }

  private async setupRemoteTracking(path: string, url: string): Promise<void> {
    try {
      await git.addRemote({
        fs: this.fs,
        dir: path,
        remote: 'origin',
        url
      });
    } catch (error) {
      console.warn('Failed to setup remote tracking:', error);
    }
  }

  private async getBranchInfo(branchName: string): Promise<{ ahead: number; behind: number; upstream?: string }> {
    try {
      // Mock implementation - isomorphic-git doesn't have direct support for ahead/behind
      return {
        ahead: 0,
        behind: 0,
        upstream: `origin/${branchName}`
      };
    } catch (error) {
      return { ahead: 0, behind: 0 };
    }
  }

  private async getLastCommitForBranch(branchName: string): Promise<GitCommit> {
    try {
      const commits = await git.log({
        fs: this.fs,
        dir: this.workdir,
        ref: branchName,
        depth: 1
      });

      if (commits.length === 0) {
        throw new Error('No commits found');
      }

      const commit = commits[0];
      return {
        hash: commit.oid,
        author: {
          name: commit.commit.author.name,
          email: commit.commit.author.email,
          timestamp: commit.commit.author.timestamp,
          timezoneOffset: commit.commit.author.timezoneOffset
        },
        committer: {
          name: commit.commit.committer.name,
          email: commit.commit.committer.email,
          timestamp: commit.commit.committer.timestamp,
          timezoneOffset: commit.commit.committer.timezoneOffset
        },
        message: commit.commit.message,
        date: new Date(commit.commit.author.timestamp * 1000),
        parents: commit.commit.parent,
        files: [],
        branches: [],
        tags: []
      };
    } catch (error) {
      // Return a default commit if none found
      return {
        hash: '',
        author: { name: 'Unknown', email: '<EMAIL>' },
        committer: { name: 'Unknown', email: '<EMAIL>' },
        message: 'No commits',
        date: new Date(),
        parents: [],
        files: [],
        branches: [],
        tags: []
      };
    }
  }

  private async getDefaultAuthor(): Promise<GitAuthor> {
    try {
      const name = await git.getConfig({
        fs: this.fs,
        dir: this.workdir,
        path: 'user.name'
      }) || 'Developer';

      const email = await git.getConfig({
        fs: this.fs,
        dir: this.workdir,
        path: 'user.email'
      }) || '<EMAIL>';

      return {
        name,
        email,
        timestamp: Math.floor(Date.now() / 1000),
        timezoneOffset: new Date().getTimezoneOffset()
      };
    } catch (error) {
      return {
        name: 'Developer',
        email: '<EMAIL>',
        timestamp: Math.floor(Date.now() / 1000),
        timezoneOffset: new Date().getTimezoneOffset()
      };
    }
  }

  private async getFileDiff(filepath: string, staged: boolean): Promise<GitDiff | null> {
    try {
      // Basic diff implementation - isomorphic-git doesn't have built-in diff
      // This is a simplified version
      
      let oldContent = '';
      let newContent = '';

      try {
        oldContent = await this.fs.readFile(filepath, { encoding: 'utf8' });
      } catch {
        // File might be new
      }

      try {
        if (staged) {
          // Get staged content - simplified
          newContent = await this.fs.readFile(filepath, { encoding: 'utf8' });
        } else {
          // Get working directory content
          newContent = await this.fs.readFile(filepath, { encoding: 'utf8' });
        }
      } catch {
        // File might be deleted
      }

      if (oldContent === newContent) {
        return null;
      }

      // Create a simple diff
      const lines = this.createSimpleDiff(oldContent, newContent);
      
      return {
        filename: filepath,
        status: oldContent === '' ? 'added' : newContent === '' ? 'deleted' : 'modified',
        hunks: [{
          oldStart: 1,
          oldLines: oldContent.split('\n').length,
          newStart: 1,
          newLines: newContent.split('\n').length,
          header: `@@ -1,${oldContent.split('\n').length} +1,${newContent.split('\n').length} @@`,
          lines
        }]
      };
    } catch (error) {
      console.warn(`Failed to get diff for ${filepath}:`, error);
      return null;
    }
  }

  private createSimpleDiff(oldContent: string, newContent: string): GitDiffLine[] {
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    const diffLines: GitDiffLine[] = [];

    // Simple line-by-line diff
    const maxLines = Math.max(oldLines.length, newLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i];
      const newLine = newLines[i];

      if (oldLine === undefined) {
        diffLines.push({
          type: 'added',
          content: `+${newLine}`,
          newLineNumber: i + 1
        });
      } else if (newLine === undefined) {
        diffLines.push({
          type: 'deleted',
          content: `-${oldLine}`,
          oldLineNumber: i + 1
        });
      } else if (oldLine !== newLine) {
        diffLines.push({
          type: 'deleted',
          content: `-${oldLine}`,
          oldLineNumber: i + 1
        });
        diffLines.push({
          type: 'added',
          content: `+${newLine}`,
          newLineNumber: i + 1
        });
      } else {
        diffLines.push({
          type: 'context',
          content: ` ${oldLine}`,
          oldLineNumber: i + 1,
          newLineNumber: i + 1
        });
      }
    }

    return diffLines;
  }

  // Getter for workdir
  getWorkdir(): string {
    return this.workdir;
  }

  setWorkdir(path: string): void {
    this.workdir = path;
  }
}

// Git Credential Manager
class GitCredentialManager {
  private credentials: Map<string, GitAuth> = new Map();

  async getAuthForUrl(url: string): Promise<GitAuth> {
    try {
      const parsedUrl = new URL(url);
      const hostname = parsedUrl.hostname;
      
      const auth = this.credentials.get(hostname);
      if (auth) {
        return auth;
      }

      // Return empty auth for public repositories
      return {};
    } catch (error) {
      return {};
    }
  }

  async getAuthForRemote(workdir: string, remote: string): Promise<GitAuth> {
    try {
      // Get remote URL
      const remoteUrl = await git.getConfig({
        fs: {}, // Will be replaced with actual fs
        dir: workdir,
        path: `remote.${remote}.url`
      });

      if (remoteUrl) {
        return await this.getAuthForUrl(remoteUrl);
      }

      return {};
    } catch (error) {
      return {};
    }
  }

  setCredentials(hostname: string, auth: GitAuth): void {
    this.credentials.set(hostname, auth);
  }

  removeCredentials(hostname: string): void {
    this.credentials.delete(hostname);
  }

  hasCredentials(hostname: string): boolean {
    return this.credentials.has(hostname);
  }
}

export default WebGitService;