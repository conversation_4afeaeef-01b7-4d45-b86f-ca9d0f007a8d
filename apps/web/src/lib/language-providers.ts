'use client';

import * as monaco from 'monaco-editor';
import { languageManager } from './language-manager';

export interface LanguageServiceProvider {
  provideDiagnostics(model: monaco.editor.ITextModel): Promise<monaco.editor.IMarkerData[]>;
  provideCompletionItems(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.CompletionItem[]>;
  provideHover(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Hover | null>;
  provideSignatureHelp(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.SignatureHelp | null>;
  provideDefinition(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Location[]>;
  provideReferences(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Location[]>;
  provideRename(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.WorkspaceEdit | null>;
  provideDocumentSymbols(model: monaco.editor.ITextModel): Promise<monaco.languages.DocumentSymbol[]>;
  provideColorPresentations(model: monaco.editor.ITextModel): Promise<monaco.languages.IColorPresentation[]>;
  provideCodeActions(model: monaco.editor.ITextModel, range: monaco.Range): Promise<monaco.languages.CodeAction[]>;
  provideFormattingEdits(model: monaco.editor.ITextModel, options: monaco.languages.FormattingOptions): Promise<monaco.editor.ITextEdit[]>;
}

class JavaScriptLanguageProvider implements LanguageServiceProvider {
  async provideDiagnostics(model: monaco.editor.ITextModel): Promise<monaco.editor.IMarkerData[]> {
    const markers: monaco.editor.IMarkerData[] = [];
    const value = model.getValue();
    
    // Basic syntax validation
    try {
      new Function(value); // Basic syntax check
    } catch (error) {
      markers.push({
        severity: monaco.MarkerSeverity.Error,
        message: `Syntax error: ${error.message}`,
        startLineNumber: 1,
        startColumn: 1,
        endLineNumber: 1,
        endColumn: 1,
      });
    }
    
    // Check for common issues
    const lines = value.split('\n');
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Check for unused variables
      if (line.includes('var ') && !line.includes('=')) {
        markers.push({
          severity: monaco.MarkerSeverity.Warning,
          message: 'Unused variable declaration',
          startLineNumber: lineNumber,
          startColumn: line.indexOf('var ') + 1,
          endLineNumber: lineNumber,
          endColumn: line.length,
        });
      }
      
      // Check for console.log statements
      if (line.includes('console.log')) {
        markers.push({
          severity: monaco.MarkerSeverity.Info,
          message: 'Console statement found - consider removing in production',
          startLineNumber: lineNumber,
          startColumn: line.indexOf('console.log') + 1,
          endLineNumber: lineNumber,
          endColumn: line.indexOf('console.log') + 11,
        });
      }
      
      // Check for missing semicolons
      if (line.trim() && !line.trim().endsWith(';') && !line.trim().endsWith('{') && !line.trim().endsWith('}')) {
        markers.push({
          severity: monaco.MarkerSeverity.Hint,
          message: 'Missing semicolon',
          startLineNumber: lineNumber,
          startColumn: line.length,
          endLineNumber: lineNumber,
          endColumn: line.length + 1,
        });
      }
    });
    
    return markers;
  }

  async provideCompletionItems(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.CompletionItem[]> {
    const word = model.getWordUntilPosition(position);
    const range = {
      startLineNumber: position.lineNumber,
      endLineNumber: position.lineNumber,
      startColumn: word.startColumn,
      endColumn: word.endColumn,
    };

    const suggestions: monaco.languages.CompletionItem[] = [
      {
        label: 'console.log',
        kind: monaco.languages.CompletionItemKind.Function,
        documentation: 'Log output to console',
        insertText: 'console.log(${1:message});',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'function',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'Function declaration',
        insertText: 'function ${1:name}(${2:params}) {\n\t${3:// body}\n}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'if',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'If statement',
        insertText: 'if (${1:condition}) {\n\t${2:// body}\n}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'for',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'For loop',
        insertText: 'for (${1:let i = 0}; ${2:i < length}; ${3:i++}) {\n\t${4:// body}\n}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'try',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'Try-catch block',
        insertText: 'try {\n\t${1:// try code}\n} catch (${2:error}) {\n\t${3:// handle error}\n}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'setTimeout',
        kind: monaco.languages.CompletionItemKind.Function,
        documentation: 'Execute code after delay',
        insertText: 'setTimeout(${1:callback}, ${2:delay});',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'addEventListener',
        kind: monaco.languages.CompletionItemKind.Function,
        documentation: 'Add event listener',
        insertText: 'addEventListener(\'${1:event}\', ${2:callback});',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'fetch',
        kind: monaco.languages.CompletionItemKind.Function,
        documentation: 'Fetch API call',
        insertText: 'fetch(\'${1:url}\')\n\t.then(response => response.json())\n\t.then(data => ${2:console.log(data)});',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
    ];

    return suggestions;
  }

  async provideHover(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Hover | null> {
    const word = model.getWordAtPosition(position);
    if (!word) return null;

    const hoverMap: Record<string, string> = {
      'console': 'The console object provides access to the browser\'s debugging console.',
      'log': 'Outputs a message to the web console.',
      'function': 'A function is a reusable block of code.',
      'const': 'Creates a constant that cannot be reassigned.',
      'let': 'Declares a block-scoped variable.',
      'var': 'Declares a variable (function-scoped or global).',
      'if': 'Executes a statement if a condition is true.',
      'for': 'Creates a loop that runs a specific number of times.',
      'while': 'Creates a loop that runs while a condition is true.',
      'try': 'Defines a block of code to test for errors.',
      'catch': 'Defines a block of code to handle errors.',
      'finally': 'Defines a block of code that always runs.',
      'return': 'Exits a function and returns a value.',
      'throw': 'Throws an error.',
      'typeof': 'Returns the type of a variable.',
      'instanceof': 'Tests whether an object is an instance of a constructor.',
      'new': 'Creates a new instance of an object.',
      'this': 'Refers to the current object.',
      'null': 'Represents a null value.',
      'undefined': 'Represents an undefined value.',
      'true': 'Boolean true value.',
      'false': 'Boolean false value.',
    };

    const documentation = hoverMap[word.word];
    if (!documentation) return null;

    return {
      range: new monaco.Range(position.lineNumber, word.startColumn, position.lineNumber, word.endColumn),
      contents: [
        { value: `**${word.word}**` },
        { value: documentation },
      ],
    };
  }

  async provideSignatureHelp(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.SignatureHelp | null> {
    const lineContent = model.getLineContent(position.lineNumber);
    const beforeCursor = lineContent.substring(0, position.column - 1);
    
    // Check if we're inside a function call
    const functionMatch = beforeCursor.match(/(\w+)\s*\(/);
    if (!functionMatch) return null;
    
    const functionName = functionMatch[1];
    
    const signatureMap: Record<string, monaco.languages.SignatureInformation> = {
      'console.log': {
        label: 'console.log(message: any, ...optionalParams: any[]): void',
        documentation: 'Outputs a message to the web console.',
        parameters: [
          {
            label: 'message',
            documentation: 'The message to log',
          },
          {
            label: '...optionalParams',
            documentation: 'Additional parameters to log',
          },
        ],
      },
      'setTimeout': {
        label: 'setTimeout(callback: Function, delay: number): number',
        documentation: 'Executes a function after a specified delay.',
        parameters: [
          {
            label: 'callback',
            documentation: 'The function to execute',
          },
          {
            label: 'delay',
            documentation: 'The delay in milliseconds',
          },
        ],
      },
      'fetch': {
        label: 'fetch(url: string, options?: RequestInit): Promise<Response>',
        documentation: 'Fetch a resource from the network.',
        parameters: [
          {
            label: 'url',
            documentation: 'The URL to fetch',
          },
          {
            label: 'options',
            documentation: 'Request options',
          },
        ],
      },
    };
    
    const signature = signatureMap[functionName];
    if (!signature) return null;
    
    return {
      signatures: [signature],
      activeSignature: 0,
      activeParameter: 0,
    };
  }

  async provideDefinition(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Location[]> {
    // Mock implementation - in a real scenario, this would parse the code and find definitions
    return [];
  }

  async provideReferences(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Location[]> {
    // Mock implementation - in a real scenario, this would find all references
    return [];
  }

  async provideRename(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.WorkspaceEdit | null> {
    // Mock implementation - in a real scenario, this would handle renaming
    return null;
  }

  async provideDocumentSymbols(model: monaco.editor.ITextModel): Promise<monaco.languages.DocumentSymbol[]> {
    const symbols: monaco.languages.DocumentSymbol[] = [];
    const value = model.getValue();
    const lines = value.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Find function declarations
      const functionMatch = line.match(/function\s+(\w+)\s*\(/);
      if (functionMatch) {
        symbols.push({
          name: functionMatch[1],
          kind: monaco.languages.SymbolKind.Function,
          range: new monaco.Range(lineNumber, 1, lineNumber, line.length),
          selectionRange: new monaco.Range(lineNumber, line.indexOf(functionMatch[1]) + 1, lineNumber, line.indexOf(functionMatch[1]) + functionMatch[1].length + 1),
          children: [],
        });
      }
      
      // Find variable declarations
      const varMatch = line.match(/(const|let|var)\s+(\w+)/);
      if (varMatch) {
        symbols.push({
          name: varMatch[2],
          kind: monaco.languages.SymbolKind.Variable,
          range: new monaco.Range(lineNumber, 1, lineNumber, line.length),
          selectionRange: new monaco.Range(lineNumber, line.indexOf(varMatch[2]) + 1, lineNumber, line.indexOf(varMatch[2]) + varMatch[2].length + 1),
          children: [],
        });
      }
    });
    
    return symbols;
  }

  async provideColorPresentations(model: monaco.editor.ITextModel): Promise<monaco.languages.IColorPresentation[]> {
    // Mock implementation - in a real scenario, this would find color values
    return [];
  }

  async provideCodeActions(model: monaco.editor.ITextModel, range: monaco.Range): Promise<monaco.languages.CodeAction[]> {
    const actions: monaco.languages.CodeAction[] = [];
    const text = model.getValueInRange(range);
    
    // Quick fix for console.log
    if (text.includes('console.log')) {
      actions.push({
        title: 'Remove console.log statement',
        kind: 'quickfix',
        edit: {
          edits: [
            {
              resource: model.uri,
              textEdit: {
                range: range,
                text: '',
              },
            },
          ],
        },
      });
    }
    
    return actions;
  }

  async provideFormattingEdits(model: monaco.editor.ITextModel, options: monaco.languages.FormattingOptions): Promise<monaco.editor.ITextEdit[]> {
    const edits: monaco.editor.ITextEdit[] = [];
    const value = model.getValue();
    const lines = value.split('\n');
    
    let formatted = '';
    let indentLevel = 0;
    const indentString = options.insertSpaces ? ' '.repeat(options.tabSize) : '\t';
    
    lines.forEach((line) => {
      const trimmedLine = line.trim();
      
      if (trimmedLine.includes('}')) {
        indentLevel = Math.max(0, indentLevel - 1);
      }
      
      formatted += indentString.repeat(indentLevel) + trimmedLine + '\n';
      
      if (trimmedLine.includes('{')) {
        indentLevel++;
      }
    });
    
    edits.push({
      range: model.getFullModelRange(),
      text: formatted.trim(),
    });
    
    return edits;
  }
}

class TypeScriptLanguageProvider extends JavaScriptLanguageProvider {
  async provideCompletionItems(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.CompletionItem[]> {
    const baseItems = await super.provideCompletionItems(model, position);
    
    const word = model.getWordUntilPosition(position);
    const range = {
      startLineNumber: position.lineNumber,
      endLineNumber: position.lineNumber,
      startColumn: word.startColumn,
      endColumn: word.endColumn,
    };

    const tsSpecificItems: monaco.languages.CompletionItem[] = [
      {
        label: 'interface',
        kind: monaco.languages.CompletionItemKind.Interface,
        documentation: 'TypeScript interface declaration',
        insertText: 'interface ${1:Name} {\n\t${2:// properties}\n}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'type',
        kind: monaco.languages.CompletionItemKind.TypeParameter,
        documentation: 'TypeScript type alias',
        insertText: 'type ${1:Name} = ${2:Type};',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'class',
        kind: monaco.languages.CompletionItemKind.Class,
        documentation: 'TypeScript class declaration',
        insertText: 'class ${1:Name} {\n\t${2:// class body}\n}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'enum',
        kind: monaco.languages.CompletionItemKind.Enum,
        documentation: 'TypeScript enum declaration',
        insertText: 'enum ${1:Name} {\n\t${2:// enum values}\n}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'async',
        kind: monaco.languages.CompletionItemKind.Keyword,
        documentation: 'Async function',
        insertText: 'async ${1:function}(${2:params}): Promise<${3:ReturnType}> {\n\t${4:// async body}\n}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
    ];

    return [...baseItems, ...tsSpecificItems];
  }
}

class PythonLanguageProvider implements LanguageServiceProvider {
  async provideDiagnostics(model: monaco.editor.ITextModel): Promise<monaco.editor.IMarkerData[]> {
    const markers: monaco.editor.IMarkerData[] = [];
    const lines = model.getValue().split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Check for print statements without parentheses (Python 2 style)
      if (line.includes('print ') && !line.includes('print(')) {
        markers.push({
          severity: monaco.MarkerSeverity.Error,
          message: 'print is a function in Python 3. Use print() instead.',
          startLineNumber: lineNumber,
          startColumn: line.indexOf('print ') + 1,
          endLineNumber: lineNumber,
          endColumn: line.indexOf('print ') + 5,
        });
      }
      
      // Check for missing colons
      if (line.trim().startsWith(('if ', 'for ', 'while ', 'def ', 'class ', 'try:', 'except', 'finally:', 'with ')) && !line.includes(':')) {
        markers.push({
          severity: monaco.MarkerSeverity.Error,
          message: 'Missing colon at end of statement',
          startLineNumber: lineNumber,
          startColumn: line.length,
          endLineNumber: lineNumber,
          endColumn: line.length + 1,
        });
      }
    });
    
    return markers;
  }

  async provideCompletionItems(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.CompletionItem[]> {
    const word = model.getWordUntilPosition(position);
    const range = {
      startLineNumber: position.lineNumber,
      endLineNumber: position.lineNumber,
      startColumn: word.startColumn,
      endColumn: word.endColumn,
    };

    const suggestions: monaco.languages.CompletionItem[] = [
      {
        label: 'print',
        kind: monaco.languages.CompletionItemKind.Function,
        documentation: 'Print output to console',
        insertText: 'print(${1:message})',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'def',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'Function definition',
        insertText: 'def ${1:function_name}(${2:parameters}):\n\t${3:# function body}\n\treturn ${4:result}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'class',
        kind: monaco.languages.CompletionItemKind.Class,
        documentation: 'Class definition',
        insertText: 'class ${1:ClassName}:\n\tdef __init__(self${2:, parameters}):\n\t\t${3:# constructor body}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'if',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'If statement',
        insertText: 'if ${1:condition}:\n\t${2:# if body}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'for',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'For loop',
        insertText: 'for ${1:item} in ${2:iterable}:\n\t${3:# loop body}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'while',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'While loop',
        insertText: 'while ${1:condition}:\n\t${2:# loop body}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'try',
        kind: monaco.languages.CompletionItemKind.Snippet,
        documentation: 'Try-except block',
        insertText: 'try:\n\t${1:# try block}\nexcept ${2:Exception} as ${3:e}:\n\t${4:# exception handling}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'import',
        kind: monaco.languages.CompletionItemKind.Module,
        documentation: 'Import statement',
        insertText: 'import ${1:module}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
      {
        label: 'from',
        kind: monaco.languages.CompletionItemKind.Module,
        documentation: 'From import statement',
        insertText: 'from ${1:module} import ${2:item}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        range: range,
      },
    ];

    return suggestions;
  }

  async provideHover(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Hover | null> {
    const word = model.getWordAtPosition(position);
    if (!word) return null;

    const hoverMap: Record<string, string> = {
      'print': 'Print objects to the text stream file.',
      'def': 'Define a function.',
      'class': 'Define a class.',
      'if': 'Execute a statement if a condition is true.',
      'for': 'Execute a loop over a sequence.',
      'while': 'Execute a loop while a condition is true.',
      'try': 'Define a block of code to test for errors.',
      'except': 'Define a block of code to handle errors.',
      'finally': 'Define a block of code that always runs.',
      'import': 'Import a module.',
      'from': 'Import specific items from a module.',
      'return': 'Return a value from a function.',
      'yield': 'Yield a value from a generator.',
      'lambda': 'Create an anonymous function.',
      'with': 'Use a context manager.',
      'as': 'Create an alias.',
      'pass': 'Do nothing (placeholder).',
      'break': 'Exit a loop.',
      'continue': 'Skip to the next iteration of a loop.',
      'None': 'Represents the absence of a value.',
      'True': 'Boolean true value.',
      'False': 'Boolean false value.',
      'self': 'Reference to the current instance.',
      'len': 'Return the length of an object.',
      'range': 'Generate a sequence of numbers.',
      'str': 'String type.',
      'int': 'Integer type.',
      'float': 'Floating point type.',
      'list': 'List type.',
      'dict': 'Dictionary type.',
      'tuple': 'Tuple type.',
      'set': 'Set type.',
    };

    const documentation = hoverMap[word.word];
    if (!documentation) return null;

    return {
      range: new monaco.Range(position.lineNumber, word.startColumn, position.lineNumber, word.endColumn),
      contents: [
        { value: `**${word.word}**` },
        { value: documentation },
      ],
    };
  }

  async provideSignatureHelp(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.SignatureHelp | null> {
    const lineContent = model.getLineContent(position.lineNumber);
    const beforeCursor = lineContent.substring(0, position.column - 1);
    
    const functionMatch = beforeCursor.match(/(\w+)\s*\(/);
    if (!functionMatch) return null;
    
    const functionName = functionMatch[1];
    
    const signatureMap: Record<string, monaco.languages.SignatureInformation> = {
      'print': {
        label: 'print(*values, sep=\' \', end=\'\\n\', file=sys.stdout, flush=False)',
        documentation: 'Print values to a stream, or to sys.stdout by default.',
        parameters: [
          { label: '*values', documentation: 'Values to print' },
          { label: 'sep', documentation: 'Separator between values' },
          { label: 'end', documentation: 'String appended after the last value' },
          { label: 'file', documentation: 'File object to write to' },
          { label: 'flush', documentation: 'Whether to forcibly flush the stream' },
        ],
      },
      'len': {
        label: 'len(obj)',
        documentation: 'Return the length of an object.',
        parameters: [
          { label: 'obj', documentation: 'Object to get length of' },
        ],
      },
      'range': {
        label: 'range(start, stop[, step])',
        documentation: 'Generate a sequence of numbers.',
        parameters: [
          { label: 'start', documentation: 'Start value' },
          { label: 'stop', documentation: 'Stop value (exclusive)' },
          { label: 'step', documentation: 'Step size' },
        ],
      },
    };
    
    const signature = signatureMap[functionName];
    if (!signature) return null;
    
    return {
      signatures: [signature],
      activeSignature: 0,
      activeParameter: 0,
    };
  }

  async provideDefinition(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Location[]> {
    return [];
  }

  async provideReferences(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.Location[]> {
    return [];
  }

  async provideRename(model: monaco.editor.ITextModel, position: monaco.Position): Promise<monaco.languages.WorkspaceEdit | null> {
    return null;
  }

  async provideDocumentSymbols(model: monaco.editor.ITextModel): Promise<monaco.languages.DocumentSymbol[]> {
    const symbols: monaco.languages.DocumentSymbol[] = [];
    const value = model.getValue();
    const lines = value.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Find function definitions
      const functionMatch = line.match(/def\s+(\w+)\s*\(/);
      if (functionMatch) {
        symbols.push({
          name: functionMatch[1],
          kind: monaco.languages.SymbolKind.Function,
          range: new monaco.Range(lineNumber, 1, lineNumber, line.length),
          selectionRange: new monaco.Range(lineNumber, line.indexOf(functionMatch[1]) + 1, lineNumber, line.indexOf(functionMatch[1]) + functionMatch[1].length + 1),
          children: [],
        });
      }
      
      // Find class definitions
      const classMatch = line.match(/class\s+(\w+)/);
      if (classMatch) {
        symbols.push({
          name: classMatch[1],
          kind: monaco.languages.SymbolKind.Class,
          range: new monaco.Range(lineNumber, 1, lineNumber, line.length),
          selectionRange: new monaco.Range(lineNumber, line.indexOf(classMatch[1]) + 1, lineNumber, line.indexOf(classMatch[1]) + classMatch[1].length + 1),
          children: [],
        });
      }
    });
    
    return symbols;
  }

  async provideColorPresentations(model: monaco.editor.ITextModel): Promise<monaco.languages.IColorPresentation[]> {
    return [];
  }

  async provideCodeActions(model: monaco.editor.ITextModel, range: monaco.Range): Promise<monaco.languages.CodeAction[]> {
    const actions: monaco.languages.CodeAction[] = [];
    const text = model.getValueInRange(range);
    
    // Quick fix for Python 2 style print
    if (text.includes('print ') && !text.includes('print(')) {
      actions.push({
        title: 'Convert to Python 3 print function',
        kind: 'quickfix',
        edit: {
          edits: [
            {
              resource: model.uri,
              textEdit: {
                range: range,
                text: text.replace(/print\s+(.+)/, 'print($1)'),
              },
            },
          ],
        },
      });
    }
    
    return actions;
  }

  async provideFormattingEdits(model: monaco.editor.ITextModel, options: monaco.languages.FormattingOptions): Promise<monaco.editor.ITextEdit[]> {
    const edits: monaco.editor.ITextEdit[] = [];
    const value = model.getValue();
    const lines = value.split('\n');
    
    let formatted = '';
    let indentLevel = 0;
    const indentString = options.insertSpaces ? ' '.repeat(options.tabSize) : '\t';
    
    lines.forEach((line) => {
      const trimmedLine = line.trim();
      
      // Adjust indent level for closing statements
      if (trimmedLine.startsWith(('except', 'finally', 'elif', 'else')) || 
          (trimmedLine === '' && lines[lines.indexOf(line) + 1]?.trim().startsWith(('except', 'finally', 'elif', 'else')))) {
        indentLevel = Math.max(0, indentLevel - 1);
      }
      
      if (trimmedLine) {
        formatted += indentString.repeat(indentLevel) + trimmedLine + '\n';
      } else {
        formatted += '\n';
      }
      
      // Adjust indent level for opening statements
      if (trimmedLine.endsWith(':') && !trimmedLine.startsWith(('except', 'finally', 'elif', 'else'))) {
        indentLevel++;
      }
    });
    
    edits.push({
      range: model.getFullModelRange(),
      text: formatted.trim(),
    });
    
    return edits;
  }
}

export class LanguageServiceManager {
  private providers: Map<string, LanguageServiceProvider> = new Map();
  private registeredLanguages: Set<string> = new Set();

  constructor() {
    this.registerDefaultProviders();
  }

  private registerDefaultProviders() {
    this.providers.set('javascript', new JavaScriptLanguageProvider());
    this.providers.set('typescript', new TypeScriptLanguageProvider());
    this.providers.set('python', new PythonLanguageProvider());
  }

  registerLanguageProvider(languageId: string, provider: LanguageServiceProvider) {
    this.providers.set(languageId, provider);
    this.registerMonacoLanguageFeatures(languageId);
  }

  private registerMonacoLanguageFeatures(languageId: string) {
    if (this.registeredLanguages.has(languageId)) return;
    
    const provider = this.providers.get(languageId);
    if (!provider) return;

    // Register completion provider
    monaco.languages.registerCompletionItemProvider(languageId, {
      provideCompletionItems: async (model, position) => {
        const items = await provider.provideCompletionItems(model, position);
        return { suggestions: items };
      },
    });

    // Register hover provider
    monaco.languages.registerHoverProvider(languageId, {
      provideHover: async (model, position) => {
        return await provider.provideHover(model, position);
      },
    });

    // Register signature help provider
    monaco.languages.registerSignatureHelpProvider(languageId, {
      signatureHelpTriggerCharacters: ['(', ','],
      provideSignatureHelp: async (model, position) => {
        return await provider.provideSignatureHelp(model, position);
      },
    });

    // Register definition provider
    monaco.languages.registerDefinitionProvider(languageId, {
      provideDefinition: async (model, position) => {
        return await provider.provideDefinition(model, position);
      },
    });

    // Register references provider
    monaco.languages.registerReferenceProvider(languageId, {
      provideReferences: async (model, position) => {
        return await provider.provideReferences(model, position);
      },
    });

    // Register rename provider
    monaco.languages.registerRenameProvider(languageId, {
      provideRenameEdits: async (model, position) => {
        return await provider.provideRename(model, position);
      },
    });

    // Register document symbol provider
    monaco.languages.registerDocumentSymbolProvider(languageId, {
      provideDocumentSymbols: async (model) => {
        return await provider.provideDocumentSymbols(model);
      },
    });

    // Register color provider
    monaco.languages.registerColorProvider(languageId, {
      provideColorPresentations: async (model) => {
        return await provider.provideColorPresentations(model);
      },
      provideDocumentColors: async (model) => {
        // Mock implementation
        return [];
      },
    });

    // Register code action provider
    monaco.languages.registerCodeActionProvider(languageId, {
      provideCodeActions: async (model, range) => {
        const actions = await provider.provideCodeActions(model, range);
        return { actions, dispose: () => {} };
      },
    });

    // Register formatting provider
    monaco.languages.registerDocumentFormattingEditProvider(languageId, {
      provideDocumentFormattingEdits: async (model, options) => {
        return await provider.provideFormattingEdits(model, options);
      },
    });

    this.registeredLanguages.add(languageId);
  }

  async provideDiagnostics(model: monaco.editor.ITextModel): Promise<monaco.editor.IMarkerData[]> {
    const languageId = model.getLanguageId();
    const provider = this.providers.get(languageId);
    
    if (!provider) return [];
    
    try {
      return await provider.provideDiagnostics(model);
    } catch (error) {
      console.error(`Error providing diagnostics for ${languageId}:`, error);
      return [];
    }
  }

  initializeAllLanguages() {
    // Register all language providers
    this.providers.forEach((provider, languageId) => {
      this.registerMonacoLanguageFeatures(languageId);
    });
  }

  getSupportedLanguages(): string[] {
    return Array.from(this.providers.keys());
  }
}

export const languageServiceManager = new LanguageServiceManager();