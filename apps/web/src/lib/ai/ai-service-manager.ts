'use client';

import { 
  BaseAIService,
  AIProvider,
  AIRequest,
  AIResponse,
  CompletionItem,
  CodeSuggestion,
  AnalysisResult,
  DebugAnalysis,
  TestGeneration,
  AIServiceFactory,
  AIConfigurationManager,
} from './ai-service-base';
import { OpenAIService } from './openai-service';

export interface AIServiceManager {
  // Service management
  initializeServices(): Promise<void>;
  getAvailableProviders(): string[];
  setDefaultProvider(name: string): void;
  
  // AI Development Assistant Features
  getCodeCompletions(request: AIRequest): Promise<AIResponse<CompletionItem[]>>;
  getCodeSuggestions(request: AIRequest): Promise<AIResponse<CodeSuggestion[]>>;
  analyzeCode(request: AIRequest): Promise<AIResponse<AnalysisResult>>;
  analyzeError(request: AIRequest): Promise<AIResponse<DebugAnalysis>>;
  generateTests(request: AIRequest): Promise<AIResponse<TestGeneration>>;
  
  // Health and monitoring
  getServiceHealth(): Promise<ServiceHealthStatus>;
  clearCache(): void;
}

export interface ServiceHealthStatus {
  [serviceName: string]: {
    available: boolean;
    responseTime?: number;
    lastError?: string;
    cacheStats?: {
      size: number;
      hitRate: number;
    };
  };
}

class AIServiceManagerImpl implements AIServiceManager {
  private configManager: AIConfigurationManager;
  private defaultProvider: string;
  private initialized: boolean = false;

  constructor() {
    this.configManager = AIConfigurationManager.getInstance();
    this.defaultProvider = this.configManager.getConfiguration().defaultProvider;
  }

  async initializeServices(): Promise<void> {
    if (this.initialized) return;

    const config = this.configManager.getConfiguration();
    
    // Initialize available AI services
    for (const provider of config.providers) {
      try {
        await this.initializeProvider(provider);
      } catch (error) {
        console.error(`Failed to initialize provider ${provider.name}:`, error);
      }
    }

    this.initialized = true;
  }

  private async initializeProvider(provider: AIProvider): Promise<void> {
    let service: BaseAIService;

    switch (provider.name.toLowerCase()) {
      case 'openai':
        service = new OpenAIService(provider);
        break;
      
      case 'anthropic':
        // TODO: Implement AnthropicService when needed
        console.warn('Anthropic service not yet implemented');
        return;
      
      case 'cohere':
        // TODO: Implement CohereService when needed
        console.warn('Cohere service not yet implemented');
        return;
      
      default:
        console.warn(`Unknown provider: ${provider.name}`);
        return;
    }

    // Test the service with a simple health check
    try {
      await this.testService(service);
      AIServiceFactory.registerService(provider.name, service);
      console.log(`Successfully initialized ${provider.name} service`);
    } catch (error) {
      console.error(`Health check failed for ${provider.name}:`, error);
      throw error;
    }
  }

  private async testService(service: BaseAIService): Promise<void> {
    const testRequest: AIRequest = {
      prompt: 'console.log("Hello")',
      language: 'javascript',
      context: 'Health check test',
    };

    // Simple test to verify service is working
    const response = await service.makeRequest(testRequest);
    if (!response.success) {
      throw new Error(response.error || 'Service health check failed');
    }
  }

  getAvailableProviders(): string[] {
    const services = AIServiceFactory.getAllServices();
    return Array.from(services.keys());
  }

  setDefaultProvider(name: string): void {
    const service = AIServiceFactory.getService(name);
    if (!service) {
      throw new Error(`Provider ${name} is not available`);
    }
    this.defaultProvider = name;
    this.configManager.updateConfiguration({ defaultProvider: name });
  }

  private getDefaultService(): BaseAIService {
    const service = AIServiceFactory.getService(this.defaultProvider);
    if (!service) {
      throw new Error(`Default provider ${this.defaultProvider} is not available`);
    }
    return service;
  }

  private getServiceForRequest(request: AIRequest): BaseAIService {
    // Allow request to specify preferred provider
    const preferredProvider = request.additionalContext?.provider as string;
    if (preferredProvider) {
      const service = AIServiceFactory.getService(preferredProvider);
      if (service) return service;
    }
    
    return this.getDefaultService();
  }

  // D3.1a: Code Completion
  async getCodeCompletions(request: AIRequest): Promise<AIResponse<CompletionItem[]>> {
    if (!this.configManager.isFeatureEnabled('codeCompletion')) {
      return {
        success: false,
        error: 'Code completion feature is disabled',
      };
    }

    try {
      const service = this.getServiceForRequest(request);
      
      // Validate service has completion method
      if ('getCodeCompletions' in service && typeof service.getCodeCompletions === 'function') {
        return await (service as any).getCodeCompletions(request);
      }
      
      return {
        success: false,
        error: 'Code completion not supported by selected provider',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Code completion failed',
      };
    }
  }

  // D3.1b: Code Suggestions
  async getCodeSuggestions(request: AIRequest): Promise<AIResponse<CodeSuggestion[]>> {
    if (!this.configManager.isFeatureEnabled('codeSuggestions')) {
      return {
        success: false,
        error: 'Code suggestions feature is disabled',
      };
    }

    try {
      const service = this.getServiceForRequest(request);
      
      if ('getCodeSuggestions' in service && typeof service.getCodeSuggestions === 'function') {
        return await (service as any).getCodeSuggestions(request);
      }
      
      return {
        success: false,
        error: 'Code suggestions not supported by selected provider',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Code suggestions failed',
      };
    }
  }

  // D3.2a: Code Analysis & Review
  async analyzeCode(request: AIRequest): Promise<AIResponse<AnalysisResult>> {
    if (!this.configManager.isFeatureEnabled('codeAnalysis')) {
      return {
        success: false,
        error: 'Code analysis feature is disabled',
      };
    }

    try {
      const service = this.getServiceForRequest(request);
      
      if ('analyzeCode' in service && typeof service.analyzeCode === 'function') {
        return await (service as any).analyzeCode(request);
      }
      
      return {
        success: false,
        error: 'Code analysis not supported by selected provider',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Code analysis failed',
      };
    }
  }

  // D3.3a: Debugging Assistant
  async analyzeError(request: AIRequest): Promise<AIResponse<DebugAnalysis>> {
    if (!this.configManager.isFeatureEnabled('debugging')) {
      return {
        success: false,
        error: 'Debugging feature is disabled',
      };
    }

    try {
      const service = this.getServiceForRequest(request);
      
      if ('analyzeError' in service && typeof service.analyzeError === 'function') {
        return await (service as any).analyzeError(request);
      }
      
      return {
        success: false,
        error: 'Error analysis not supported by selected provider',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error analysis failed',
      };
    }
  }

  // D3.4a: Test Generation
  async generateTests(request: AIRequest): Promise<AIResponse<TestGeneration>> {
    if (!this.configManager.isFeatureEnabled('testGeneration')) {
      return {
        success: false,
        error: 'Test generation feature is disabled',
      };
    }

    try {
      const service = this.getServiceForRequest(request);
      
      if ('generateTests' in service && typeof service.generateTests === 'function') {
        return await (service as any).generateTests(request);
      }
      
      return {
        success: false,
        error: 'Test generation not supported by selected provider',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Test generation failed',
      };
    }
  }

  // Health and monitoring
  async getServiceHealth(): Promise<ServiceHealthStatus> {
    const services = AIServiceFactory.getAllServices();
    const healthStatus: ServiceHealthStatus = {};

    for (const [name, service] of services) {
      try {
        const startTime = Date.now();
        
        // Simple health check
        const testResponse = await service.makeRequest({
          prompt: 'test',
          context: 'health check',
        });
        
        const responseTime = Date.now() - startTime;
        
        healthStatus[name] = {
          available: testResponse.success,
          responseTime,
          lastError: testResponse.success ? undefined : testResponse.error,
          cacheStats: service.getCacheStats ? service.getCacheStats() : undefined,
        };
      } catch (error) {
        healthStatus[name] = {
          available: false,
          lastError: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }

    return healthStatus;
  }

  clearCache(): void {
    const services = AIServiceFactory.getAllServices();
    for (const [name, service] of services) {
      try {
        service.clearCache();
        console.log(`Cache cleared for ${name} service`);
      } catch (error) {
        console.error(`Failed to clear cache for ${name}:`, error);
      }
    }
  }

  // Utility methods for Monaco Editor integration
  async getSmartCompletions(
    code: string,
    position: { line: number; column: number },
    language: string,
    fileName?: string
  ): Promise<CompletionItem[]> {
    // Extract relevant context around cursor position
    const lines = code.split('\n');
    const currentLine = lines[position.line - 1] || '';
    const beforeCursor = currentLine.substring(0, position.column - 1);
    const afterCursor = currentLine.substring(position.column - 1);
    
    // Get surrounding context (5 lines before and after)
    const contextStart = Math.max(0, position.line - 6);
    const contextEnd = Math.min(lines.length, position.line + 5);
    const context = lines.slice(contextStart, contextEnd).join('\n');

    const request: AIRequest = {
      prompt: beforeCursor,
      context,
      language,
      fileName,
      cursorPosition: position,
      additionalContext: {
        afterCursor,
        fullCode: code,
      },
    };

    const response = await this.getCodeCompletions(request);
    return response.success ? response.data || [] : [];
  }

  async getIntelligentSuggestions(
    code: string,
    selectedText: string,
    language: string,
    fileName?: string
  ): Promise<CodeSuggestion[]> {
    const request: AIRequest = {
      prompt: selectedText || code,
      context: code !== selectedText ? code : undefined,
      language,
      fileName,
    };

    const response = await this.getCodeSuggestions(request);
    return response.success ? response.data || [] : [];
  }

  async performCodeAnalysis(
    code: string,
    language: string,
    fileName?: string
  ): Promise<AnalysisResult | null> {
    const request: AIRequest = {
      prompt: code,
      language,
      fileName,
    };

    const response = await this.analyzeCode(request);
    return response.success ? response.data || null : null;
  }

  async getDebugHelp(
    error: string,
    code: string,
    language: string,
    fileName?: string
  ): Promise<DebugAnalysis | null> {
    const request: AIRequest = {
      prompt: error,
      context: code,
      language,
      fileName,
    };

    const response = await this.analyzeError(request);
    return response.success ? response.data || null : null;
  }

  async createTests(
    code: string,
    language: string,
    fileName?: string
  ): Promise<TestGeneration | null> {
    const request: AIRequest = {
      prompt: code,
      language,
      fileName,
    };

    const response = await this.generateTests(request);
    return response.success ? response.data || null : null;
  }
}

// Global AI service manager instance
export const aiServiceManager = new AIServiceManagerImpl();

// Initialize AI services on module load
if (typeof window !== 'undefined') {
  // Initialize services when in browser environment
  aiServiceManager.initializeServices().catch(console.error);
}