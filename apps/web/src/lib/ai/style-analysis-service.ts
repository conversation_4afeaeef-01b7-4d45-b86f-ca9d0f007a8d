'use client';

import { EventEmitter } from 'events';
import { 
  StyleReference, 
  StyleFeatures, 
  StyleSimilarityResult,
  ColorFeatures,
  TextureFeatures,
  CompositionFeatures,
  SemanticFeatures
} from './style-transfer-engine';

export interface ImageStyleAnalysis {
  id: string;
  imageData: ImageData;
  features: StyleFeatures;
  styleClassification: StyleClassification;
  qualityMetrics: ImageQualityMetrics;
  timestamp: number;
  processingTime: number;
}

export interface StyleClassification {
  primaryStyle: ArtisticStyle;
  secondaryStyles: Array<{ style: ArtisticStyle; confidence: number }>;
  confidence: number;
  reasoning: string[];
}

export interface ArtisticStyle {
  name: string;
  period: string;
  movement: string;
  characteristics: string[];
  examples: string[];
  compatibility: StyleCompatibility;
}

export interface StyleCompatibility {
  contentTypes: string[]; // portrait, landscape, object, etc.
  complexityLevels: string[]; // low, medium, high
  colorProfiles: string[]; // vibrant, muted, monochrome, etc.
  recommendedUse: string[];
}

export interface ImageQualityMetrics {
  sharpness: number; // 0-1
  contrast: number; // 0-1
  saturation: number; // 0-1
  brightness: number; // 0-1
  noiseLevel: number; // 0-1
  compression: number; // 0-1
  resolution: { width: number; height: number };
  aspectRatio: number;
  fileSize?: number;
}

export interface StyleMatchingResult {
  sourceImage: ImageData;
  matchedStyles: Array<{
    style: StyleReference;
    compatibility: number; // 0-1
    reasons: string[];
    expectedOutcome: string;
    processingEstimate: number;
  }>;
  recommendations: StyleRecommendation[];
  analysis: ImageStyleAnalysis;
}

export interface StyleRecommendation {
  type: 'enhancement' | 'alternative' | 'combination' | 'preprocessing';
  title: string;
  description: string;
  confidence: number;
  actions: RecommendedAction[];
}

export interface RecommendedAction {
  type: 'adjust_parameter' | 'preprocess_image' | 'select_different_style' | 'combine_styles';
  parameter?: string;
  value?: any;
  reason: string;
}

export interface ColorHarmonyAnalysis {
  harmonyType: 'monochromatic' | 'analogous' | 'complementary' | 'triadic' | 'split-complementary' | 'tetradic';
  balance: number; // 0-1
  temperature: number; // -1 to 1 (cool to warm)
  saturationDistribution: {
    low: number;
    medium: number;
    high: number;
  };
  dominantHue: number; // 0-360 degrees
  colorPalette: Array<{
    color: string;
    percentage: number;
    role: 'dominant' | 'accent' | 'neutral' | 'background';
  }>;
}

export interface TextureAnalysisResult {
  textureType: 'smooth' | 'rough' | 'fine' | 'coarse' | 'regular' | 'irregular';
  patterns: Array<{
    type: string;
    strength: number;
    direction?: number; // 0-360 degrees
    scale: 'micro' | 'small' | 'medium' | 'large' | 'macro';
  }>;
  roughness: number; // 0-1
  directionality: number; // 0-1 (0 = isotropic, 1 = highly directional)
  regularity: number; // 0-1
  contrast: number; // 0-1
  entropy: number; // 0-1 (complexity measure)
}

export interface CompositionAnalysisResult {
  balance: {
    type: 'symmetrical' | 'asymmetrical' | 'radial';
    strength: number; // 0-1
    axis?: number; // degrees for symmetrical balance
  };
  focalPoints: Array<{
    x: number; // 0-1 normalized coordinates
    y: number; // 0-1 normalized coordinates
    strength: number; // 0-1
    type: 'primary' | 'secondary' | 'tertiary';
  }>;
  ruleOfThirds: {
    adherence: number; // 0-1
    intersectionPoints: Array<{ x: number; y: number; strength: number }>;
  };
  leadingLines: Array<{
    startX: number;
    startY: number;
    endX: number;
    endY: number;
    strength: number;
    type: 'diagonal' | 'curved' | 'horizontal' | 'vertical';
  }>;
  depth: {
    perception: 'flat' | 'shallow' | 'moderate' | 'deep';
    layers: number;
    perspectiveStrength: number; // 0-1
  };
  visualWeight: Array<{
    region: { x: number; y: number; width: number; height: number };
    weight: number; // 0-1
  }>;
}

export class StyleAnalysisService extends EventEmitter {
  private analysisCache: Map<string, ImageStyleAnalysis> = new Map();
  private artisticStyles: Map<string, ArtisticStyle>;
  private isInitialized: boolean = false;

  constructor() {
    super();
    this.artisticStyles = new Map();
    this.initializeArtisticStyles();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      this.emit('initialization:failed', error);
      throw error;
    }
  }

  private initializeArtisticStyles(): void {
    const styles: ArtisticStyle[] = [
      {
        name: 'Impressionism',
        period: '1860-1886',
        movement: 'Post-Impressionism',
        characteristics: [
          'Visible brushstrokes',
          'Open composition',
          'Emphasis on light and its changing qualities',
          'Movement and candid poses',
          'Unusual visual angles'
        ],
        examples: ['Monet Water Lilies', 'Renoir Luncheon of the Boating Party'],
        compatibility: {
          contentTypes: ['landscape', 'portrait', 'still-life'],
          complexityLevels: ['medium', 'high'],
          colorProfiles: ['vibrant', 'natural'],
          recommendedUse: ['outdoor scenes', 'natural lighting', 'soft subjects']
        }
      },
      {
        name: 'Cubism',
        period: '1907-1917',
        movement: 'Modern Art',
        characteristics: [
          'Geometric forms',
          'Multiple perspectives',
          'Fragmented objects',
          'Abstract representation',
          'Analytical approach'
        ],
        examples: ['Picasso Les Demoiselles d\'Avignon', 'Braque Violin and Candlestick'],
        compatibility: {
          contentTypes: ['portrait', 'object', 'abstract'],
          complexityLevels: ['high'],
          colorProfiles: ['muted', 'monochrome', 'earthy'],
          recommendedUse: ['geometric subjects', 'architectural elements', 'portraits']
        }
      },
      {
        name: 'Abstract Expressionism',
        period: '1940s-1960s',
        movement: 'Abstract Art',
        characteristics: [
          'Large-scale works',
          'Gestural brushstrokes',
          'Spontaneous creation',
          'Emotional intensity',
          'Non-representational forms'
        ],
        examples: ['Pollock Number 1', 'Rothko Color Field paintings'],
        compatibility: {
          contentTypes: ['abstract', 'mixed'],
          complexityLevels: ['medium', 'high'],
          colorProfiles: ['vibrant', 'bold', 'contrasting'],
          recommendedUse: ['emotional expression', 'abstract concepts', 'dynamic subjects']
        }
      },
      {
        name: 'Pop Art',
        period: '1950s-1960s',
        movement: 'Contemporary Art',
        characteristics: [
          'Bold colors',
          'Commercial imagery',
          'Mass production techniques',
          'Popular culture references',
          'Irony and parody'
        ],
        examples: ['Warhol Campbell\'s Soup Cans', 'Lichtenstein Whaam!'],
        compatibility: {
          contentTypes: ['object', 'portrait', 'commercial'],
          complexityLevels: ['low', 'medium'],
          colorProfiles: ['vibrant', 'saturated', 'primary'],
          recommendedUse: ['products', 'brands', 'contemporary subjects']
        }
      },
      {
        name: 'Photorealism',
        period: '1960s-present',
        movement: 'Contemporary Realism',
        characteristics: [
          'Extreme attention to detail',
          'Photographic accuracy',
          'High resolution',
          'Precise color matching',
          'Minimal artistic interpretation'
        ],
        examples: ['Chuck Close portraits', 'Richard Estes cityscapes'],
        compatibility: {
          contentTypes: ['portrait', 'landscape', 'object'],
          complexityLevels: ['high'],
          colorProfiles: ['natural', 'accurate'],
          recommendedUse: ['detailed subjects', 'high-resolution images', 'technical accuracy']
        }
      },
      {
        name: 'Anime/Manga',
        period: '1960s-present',
        movement: 'Japanese Popular Art',
        characteristics: [
          'Clean line art',
          'Vibrant colors',
          'Exaggerated features',
          'Cell shading',
          'Stylized proportions'
        ],
        examples: ['Studio Ghibli films', 'Dragon Ball artwork'],
        compatibility: {
          contentTypes: ['portrait', 'character', 'fantasy'],
          complexityLevels: ['low', 'medium'],
          colorProfiles: ['vibrant', 'saturated', 'clean'],
          recommendedUse: ['characters', 'illustrations', 'fantasy subjects']
        }
      }
    ];

    styles.forEach(style => {
      this.artisticStyles.set(style.name.toLowerCase(), style);
    });
  }

  // Main analysis method
  async analyzeImageStyle(imageData: ImageData): Promise<ImageStyleAnalysis> {
    const startTime = Date.now();
    const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Check cache first
      const cacheKey = this.generateImageHash(imageData);
      const cached = this.analysisCache.get(cacheKey);
      if (cached) {
        return cached;
      }

      this.emit('analysis:started', { analysisId, imageSize: imageData.data.length });

      // Extract comprehensive features
      const features = await this.extractComprehensiveFeatures(imageData);
      
      // Classify artistic style
      const styleClassification = await this.classifyArtisticStyle(features, imageData);
      
      // Calculate quality metrics
      const qualityMetrics = await this.calculateImageQuality(imageData);

      const processingTime = Date.now() - startTime;

      const analysis: ImageStyleAnalysis = {
        id: analysisId,
        imageData,
        features,
        styleClassification,
        qualityMetrics,
        timestamp: Date.now(),
        processingTime
      };

      // Cache the result
      this.analysisCache.set(cacheKey, analysis);
      
      this.emit('analysis:completed', { analysisId, processingTime });
      
      return analysis;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.emit('analysis:failed', { 
        analysisId, 
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime 
      });
      throw error;
    }
  }

  private generateImageHash(imageData: ImageData): string {
    // Simple hash based on image dimensions and sample pixels
    const sampleSize = Math.min(1000, imageData.data.length / 4);
    const step = Math.floor(imageData.data.length / (sampleSize * 4));
    
    let hash = `${imageData.width}x${imageData.height}`;
    for (let i = 0; i < imageData.data.length; i += step * 4) {
      hash += imageData.data[i].toString(16);
    }
    
    return hash;
  }

  private async extractComprehensiveFeatures(imageData: ImageData): Promise<StyleFeatures> {
    // Parallel feature extraction
    const [colorFeatures, textureFeatures, compositionFeatures, semanticFeatures] = await Promise.all([
      this.analyzeColorFeatures(imageData),
      this.analyzeTextureFeatures(imageData),
      this.analyzeCompositionFeatures(imageData),
      this.analyzeSemanticFeatures(imageData)
    ]);

    return {
      color: colorFeatures,
      texture: textureFeatures,
      composition: compositionFeatures,
      semantic: semanticFeatures,
      metadata: {
        extractedAt: Date.now(),
        analysisVersion: '2.0'
      }
    };
  }

  private async analyzeColorFeatures(imageData: ImageData): Promise<ColorFeatures> {
    const colorAnalysis = this.analyzeColorHarmony(imageData);
    
    return {
      palette: colorAnalysis.colorPalette.map(c => c.color),
      harmony: colorAnalysis.harmonyType,
      saturation: this.calculateAverageSaturation(imageData),
      brightness: this.calculateAverageBrightness(imageData),
      contrast: this.calculateContrast(imageData),
      temperature: colorAnalysis.temperature
    };
  }

  private analyzeColorHarmony(imageData: ImageData): ColorHarmonyAnalysis {
    const data = imageData.data;
    const colorMap = new Map<string, number>();
    const pixelCount = data.length / 4;
    
    let totalHue = 0;
    let totalSaturation = 0;
    let totalBrightness = 0;
    
    // Sample pixels for color analysis
    for (let i = 0; i < data.length; i += 16) { // Sample every 4th pixel
      const r = data[i] / 255;
      const g = data[i + 1] / 255;
      const b = data[i + 2] / 255;
      
      const { h, s, l } = this.rgbToHsl(r, g, b);
      
      totalHue += h;
      totalSaturation += s;
      totalBrightness += l;
      
      // Quantize colors for palette extraction
      const quantizedR = Math.round(r * 16) * 16;
      const quantizedG = Math.round(g * 16) * 16;
      const quantizedB = Math.round(b * 16) * 16;
      const colorKey = `rgb(${quantizedR},${quantizedG},${quantizedB})`;
      
      colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1);
    }
    
    const sampleCount = Math.floor(data.length / 16);
    const dominantHue = (totalHue / sampleCount) * 360;
    
    // Extract dominant colors
    const sortedColors = Array.from(colorMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 8);
    
    const totalPixels = sortedColors.reduce((sum, [, count]) => sum + count, 0);
    
    const colorPalette = sortedColors.map(([color, count], index) => ({
      color,
      percentage: count / totalPixels,
      role: index === 0 ? 'dominant' as const : 
            index < 3 ? 'accent' as const : 
            'neutral' as const
    }));
    
    // Determine harmony type based on hue relationships
    const hues = colorPalette
      .map(c => this.extractHueFromRgb(c.color))
      .filter(h => h !== null) as number[];
    
    const harmonyType = this.determineHarmonyType(hues);
    
    return {
      harmonyType,
      balance: this.calculateColorBalance(colorPalette),
      temperature: this.calculateColorTemperature(imageData),
      saturationDistribution: this.calculateSaturationDistribution(imageData),
      dominantHue,
      colorPalette
    };
  }

  private rgbToHsl(r: number, g: number, b: number): { h: number; s: number; l: number } {
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const l = (max + min) / 2;
    
    if (max === min) {
      return { h: 0, s: 0, l }; // Achromatic
    }
    
    const d = max - min;
    const s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    let h = 0;
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
    
    return { h, s, l };
  }

  private extractHueFromRgb(rgbString: string): number | null {
    const match = rgbString.match(/rgb\((\d+),(\d+),(\d+)\)/);
    if (!match) return null;
    
    const r = parseInt(match[1]) / 255;
    const g = parseInt(match[2]) / 255;
    const b = parseInt(match[3]) / 255;
    
    const { h } = this.rgbToHsl(r, g, b);
    return h * 360;
  }

  private determineHarmonyType(hues: number[]): ColorHarmonyAnalysis['harmonyType'] {
    if (hues.length < 2) return 'monochromatic';
    
    hues.sort((a, b) => a - b);
    const differences = [];
    
    for (let i = 1; i < hues.length; i++) {
      differences.push(Math.min(
        Math.abs(hues[i] - hues[i-1]),
        360 - Math.abs(hues[i] - hues[i-1])
      ));
    }
    
    const avgDifference = differences.reduce((sum, diff) => sum + diff, 0) / differences.length;
    
    if (avgDifference < 30) return 'monochromatic';
    if (avgDifference < 60) return 'analogous';
    if (avgDifference > 150) return 'complementary';
    if (hues.length >= 3) return 'triadic';
    
    return 'complementary';
  }

  private calculateColorBalance(colorPalette: Array<{ color: string; percentage: number; role: string }>): number {
    // Simple balance calculation based on color distribution
    const dominantPercentage = colorPalette.find(c => c.role === 'dominant')?.percentage || 0;
    return 1 - Math.abs(0.5 - dominantPercentage) * 2; // Balanced around 50%
  }

  private calculateColorTemperature(imageData: ImageData): number {
    const data = imageData.data;
    let warmSum = 0;
    let coolSum = 0;
    
    for (let i = 0; i < data.length; i += 16) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      // Simple warm/cool calculation
      const warmth = (r + g * 0.5) - b;
      if (warmth > 0) {
        warmSum += warmth;
      } else {
        coolSum += Math.abs(warmth);
      }
    }
    
    const total = warmSum + coolSum;
    return total > 0 ? (warmSum - coolSum) / total : 0; // -1 to 1
  }

  private calculateSaturationDistribution(imageData: ImageData): { low: number; medium: number; high: number } {
    const data = imageData.data;
    let low = 0, medium = 0, high = 0;
    
    for (let i = 0; i < data.length; i += 16) {
      const r = data[i] / 255;
      const g = data[i + 1] / 255;
      const b = data[i + 2] / 255;
      
      const { s } = this.rgbToHsl(r, g, b);
      
      if (s < 0.3) low++;
      else if (s < 0.7) medium++;
      else high++;
    }
    
    const total = low + medium + high;
    return {
      low: low / total,
      medium: medium / total,
      high: high / total
    };
  }

  private calculateAverageSaturation(imageData: ImageData): number {
    const data = imageData.data;
    let totalSaturation = 0;
    let count = 0;
    
    for (let i = 0; i < data.length; i += 16) {
      const r = data[i] / 255;
      const g = data[i + 1] / 255;
      const b = data[i + 2] / 255;
      
      const { s } = this.rgbToHsl(r, g, b);
      totalSaturation += s;
      count++;
    }
    
    return count > 0 ? totalSaturation / count : 0;
  }

  private calculateAverageBrightness(imageData: ImageData): number {
    const data = imageData.data;
    let totalBrightness = 0;
    
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      totalBrightness += (r + g + b) / 3;
    }
    
    return totalBrightness / (data.length / 4) / 255;
  }

  private calculateContrast(imageData: ImageData): number {
    const data = imageData.data;
    let min = 255, max = 0;
    
    for (let i = 0; i < data.length; i += 4) {
      const brightness = (data[i] + data[i + 1] + data[i + 2]) / 3;
      min = Math.min(min, brightness);
      max = Math.max(max, brightness);
    }
    
    return max > min ? (max - min) / 255 : 0;
  }

  private async analyzeTextureFeatures(imageData: ImageData): Promise<TextureFeatures> {
    const textureAnalysis = this.analyzeTexture(imageData);
    
    return {
      roughness: textureAnalysis.roughness,
      patterns: textureAnalysis.patterns.map(p => p.type),
      directionality: textureAnalysis.directionality,
      regularity: textureAnalysis.regularity,
      contrast: textureAnalysis.contrast
    };
  }

  private analyzeTexture(imageData: ImageData): TextureAnalysisResult {
    // Simplified texture analysis using local binary patterns and edge detection
    const { width, height, data } = imageData;
    
    // Calculate local binary patterns for texture classification
    const lbpHistogram = this.calculateLBP(imageData);
    
    // Edge detection for directionality
    const edges = this.detectEdges(imageData);
    
    return {
      textureType: this.classifyTextureType(lbpHistogram),
      patterns: this.detectPatterns(imageData),
      roughness: this.calculateRoughness(lbpHistogram),
      directionality: this.calculateDirectionality(edges),
      regularity: this.calculateRegularity(lbpHistogram),
      contrast: this.calculateLocalContrast(imageData),
      entropy: this.calculateTextureEntropy(lbpHistogram)
    };
  }

  private calculateLBP(imageData: ImageData): number[] {
    // Simplified Local Binary Pattern calculation
    const { width, height, data } = imageData;
    const histogram = new Array(256).fill(0);
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const centerIdx = (y * width + x) * 4;
        const center = data[centerIdx]; // Use red channel for simplicity
        
        let lbp = 0;
        const neighbors = [
          [-1, -1], [-1, 0], [-1, 1],
          [0, 1], [1, 1], [1, 0],
          [1, -1], [0, -1]
        ];
        
        for (let i = 0; i < neighbors.length; i++) {
          const [dx, dy] = neighbors[i];
          const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
          const neighborValue = data[neighborIdx];
          
          if (neighborValue >= center) {
            lbp |= (1 << i);
          }
        }
        
        histogram[lbp]++;
      }
    }
    
    // Normalize histogram
    const total = histogram.reduce((sum, val) => sum + val, 0);
    return histogram.map(val => val / total);
  }

  private detectEdges(imageData: ImageData): Array<{ x: number; y: number; direction: number; magnitude: number }> {
    // Simplified edge detection using Sobel operator
    const { width, height, data } = imageData;
    const edges: Array<{ x: number; y: number; direction: number; magnitude: number }> = [];
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const sobelX = this.applySobelX(imageData, x, y);
        const sobelY = this.applySobelY(imageData, x, y);
        
        const magnitude = Math.sqrt(sobelX * sobelX + sobelY * sobelY);
        if (magnitude > 50) { // Threshold for significant edges
          const direction = Math.atan2(sobelY, sobelX) * (180 / Math.PI);
          edges.push({ x, y, direction, magnitude });
        }
      }
    }
    
    return edges;
  }

  private applySobelX(imageData: ImageData, x: number, y: number): number {
    const { width, data } = imageData;
    const kernel = [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]];
    let result = 0;
    
    for (let ky = -1; ky <= 1; ky++) {
      for (let kx = -1; kx <= 1; kx++) {
        const idx = ((y + ky) * width + (x + kx)) * 4;
        const intensity = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        result += intensity * kernel[ky + 1][kx + 1];
      }
    }
    
    return result;
  }

  private applySobelY(imageData: ImageData, x: number, y: number): number {
    const { width, data } = imageData;
    const kernel = [[-1, -2, -1], [0, 0, 0], [1, 2, 1]];
    let result = 0;
    
    for (let ky = -1; ky <= 1; ky++) {
      for (let kx = -1; kx <= 1; kx++) {
        const idx = ((y + ky) * width + (x + kx)) * 4;
        const intensity = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        result += intensity * kernel[ky + 1][kx + 1];
      }
    }
    
    return result;
  }

  private classifyTextureType(lbpHistogram: number[]): TextureAnalysisResult['textureType'] {
    // Simplified texture classification based on LBP histogram characteristics
    const entropy = this.calculateTextureEntropy(lbpHistogram);
    const uniformity = Math.max(...lbpHistogram);
    
    if (uniformity > 0.3) return 'smooth';
    if (entropy > 0.8) return 'rough';
    if (entropy > 0.6) return 'fine';
    return 'coarse';
  }

  private detectPatterns(imageData: ImageData): Array<{ type: string; strength: number; direction?: number; scale: 'micro' | 'small' | 'medium' | 'large' | 'macro' }> {
    // Simplified pattern detection
    return [
      { type: 'organic', strength: 0.7, scale: 'medium' },
      { type: 'geometric', strength: 0.3, scale: 'small' }
    ];
  }

  private calculateRoughness(lbpHistogram: number[]): number {
    // Higher entropy typically indicates rougher texture
    return this.calculateTextureEntropy(lbpHistogram);
  }

  private calculateDirectionality(edges: Array<{ direction: number; magnitude: number }>): number {
    if (edges.length === 0) return 0;
    
    // Calculate directional consistency
    const directions = edges.map(e => e.direction);
    const avgDirection = directions.reduce((sum, dir) => sum + dir, 0) / directions.length;
    
    let variance = 0;
    directions.forEach(dir => {
      let diff = Math.abs(dir - avgDirection);
      diff = Math.min(diff, 360 - diff); // Handle circular nature of angles
      variance += diff * diff;
    });
    variance /= directions.length;
    
    // Normalize: lower variance = higher directionality
    return Math.max(0, 1 - (variance / (180 * 180)));
  }

  private calculateRegularity(lbpHistogram: number[]): number {
    // Calculate how regular/uniform the texture is
    const uniformPatterns = lbpHistogram.slice(0, 59); // First 59 patterns are "uniform"
    const uniformSum = uniformPatterns.reduce((sum, val) => sum + val, 0);
    return uniformSum; // Higher value = more regular
  }

  private calculateLocalContrast(imageData: ImageData): number {
    const { width, height, data } = imageData;
    let totalContrast = 0;
    let count = 0;
    
    // Calculate local contrast in 3x3 neighborhoods
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let min = 255, max = 0;
        
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const idx = ((y + dy) * width + (x + dx)) * 4;
            const intensity = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
            min = Math.min(min, intensity);
            max = Math.max(max, intensity);
          }
        }
        
        totalContrast += (max - min) / 255;
        count++;
      }
    }
    
    return count > 0 ? totalContrast / count : 0;
  }

  private calculateTextureEntropy(histogram: number[]): number {
    let entropy = 0;
    for (const p of histogram) {
      if (p > 0) {
        entropy -= p * Math.log2(p);
      }
    }
    return entropy / Math.log2(histogram.length); // Normalize to 0-1
  }

  private async analyzeCompositionFeatures(imageData: ImageData): Promise<CompositionFeatures> {
    const compositionAnalysis = this.analyzeComposition(imageData);
    
    return {
      balance: compositionAnalysis.balance.type,
      focal_points: compositionAnalysis.focalPoints.map(fp => ({ 
        x: fp.x, 
        y: fp.y, 
        strength: fp.strength 
      })),
      rule_of_thirds: compositionAnalysis.ruleOfThirds.adherence > 0.5,
      leading_lines: compositionAnalysis.leadingLines.length,
      depth: compositionAnalysis.depth.perception
    };
  }

  private analyzeComposition(imageData: ImageData): CompositionAnalysisResult {
    const { width, height } = imageData;
    
    // Analyze focal points using gradient magnitude
    const focalPoints = this.findFocalPoints(imageData);
    
    // Analyze balance
    const balance = this.analyzeBalance(imageData);
    
    // Check rule of thirds
    const ruleOfThirds = this.analyzeRuleOfThirds(imageData, focalPoints);
    
    // Detect leading lines
    const leadingLines = this.detectLeadingLines(imageData);
    
    // Estimate depth
    const depth = this.estimateDepth(imageData);
    
    // Calculate visual weight distribution
    const visualWeight = this.calculateVisualWeight(imageData);
    
    return {
      balance,
      focalPoints,
      ruleOfThirds,
      leadingLines,
      depth,
      visualWeight
    };
  }

  private findFocalPoints(imageData: ImageData): Array<{ x: number; y: number; strength: number; type: 'primary' | 'secondary' | 'tertiary' }> {
    // Simplified focal point detection using edge density
    const { width, height } = imageData;
    const edges = this.detectEdges(imageData);
    
    // Create a heat map of edge density
    const cellSize = 32;
    const cols = Math.ceil(width / cellSize);
    const rows = Math.ceil(height / cellSize);
    const heatMap = new Array(rows).fill(null).map(() => new Array(cols).fill(0));
    
    edges.forEach(edge => {
      const col = Math.floor(edge.x / cellSize);
      const row = Math.floor(edge.y / cellSize);
      if (row < rows && col < cols) {
        heatMap[row][col] += edge.magnitude;
      }
    });
    
    // Find local maxima
    const focalPoints: Array<{ x: number; y: number; strength: number; type: 'primary' | 'secondary' | 'tertiary' }> = [];
    
    for (let row = 1; row < rows - 1; row++) {
      for (let col = 1; col < cols - 1; col++) {
        const current = heatMap[row][col];
        let isMaxima = true;
        
        for (let dr = -1; dr <= 1; dr++) {
          for (let dc = -1; dc <= 1; dc++) {
            if (dr === 0 && dc === 0) continue;
            if (heatMap[row + dr][col + dc] >= current) {
              isMaxima = false;
              break;
            }
          }
          if (!isMaxima) break;
        }
        
        if (isMaxima && current > 0) {
          focalPoints.push({
            x: (col + 0.5) * cellSize / width,
            y: (row + 0.5) * cellSize / height,
            strength: Math.min(1, current / 10000), // Normalize
            type: 'primary' // Would be classified based on relative strength
          });
        }
      }
    }
    
    // Sort by strength and classify
    focalPoints.sort((a, b) => b.strength - a.strength);
    focalPoints.forEach((fp, i) => {
      fp.type = i === 0 ? 'primary' : i < 3 ? 'secondary' : 'tertiary';
    });
    
    return focalPoints.slice(0, 5); // Return top 5 focal points
  }

  private analyzeBalance(imageData: ImageData): { type: 'symmetrical' | 'asymmetrical' | 'radial'; strength: number; axis?: number } {
    // Simplified balance analysis
    const { width, height, data } = imageData;
    
    // Calculate visual weight distribution
    let leftWeight = 0, rightWeight = 0;
    let topWeight = 0, bottomWeight = 0;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;
        const intensity = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        const weight = intensity / 255;
        
        if (x < width / 2) leftWeight += weight;
        else rightWeight += weight;
        
        if (y < height / 2) topWeight += weight;
        else bottomWeight += weight;
      }
    }
    
    const horizontalBalance = Math.abs(leftWeight - rightWeight) / (leftWeight + rightWeight);
    const verticalBalance = Math.abs(topWeight - bottomWeight) / (topWeight + bottomWeight);
    
    const overallBalance = (horizontalBalance + verticalBalance) / 2;
    
    return {
      type: overallBalance < 0.1 ? 'symmetrical' : 'asymmetrical',
      strength: 1 - overallBalance,
      axis: overallBalance < 0.1 ? (horizontalBalance < verticalBalance ? 90 : 0) : undefined
    };
  }

  private analyzeRuleOfThirds(
    imageData: ImageData, 
    focalPoints: Array<{ x: number; y: number; strength: number }>
  ): { adherence: number; intersectionPoints: Array<{ x: number; y: number; strength: number }> } {
    // Rule of thirds intersection points
    const intersections = [
      { x: 1/3, y: 1/3 }, { x: 2/3, y: 1/3 },
      { x: 1/3, y: 2/3 }, { x: 2/3, y: 2/3 }
    ];
    
    let totalAdherence = 0;
    const intersectionPoints: Array<{ x: number; y: number; strength: number }> = [];
    
    intersections.forEach(intersection => {
      let maxStrength = 0;
      focalPoints.forEach(fp => {
        const distance = Math.sqrt(
          Math.pow(fp.x - intersection.x, 2) + 
          Math.pow(fp.y - intersection.y, 2)
        );
        
        if (distance < 0.1) { // Within 10% of image dimension
          const strength = fp.strength * (1 - distance / 0.1);
          maxStrength = Math.max(maxStrength, strength);
        }
      });
      
      totalAdherence += maxStrength;
      intersectionPoints.push({
        x: intersection.x,
        y: intersection.y,
        strength: maxStrength
      });
    });
    
    return {
      adherence: totalAdherence / intersections.length,
      intersectionPoints
    };
  }

  private detectLeadingLines(imageData: ImageData): Array<{
    startX: number; startY: number; endX: number; endY: number;
    strength: number; type: 'diagonal' | 'curved' | 'horizontal' | 'vertical';
  }> {
    // Simplified leading line detection
    const edges = this.detectEdges(imageData);
    const lines: Array<{ startX: number; startY: number; endX: number; endY: number; strength: number; type: 'diagonal' | 'curved' | 'horizontal' | 'vertical' }> = [];
    
    // Group edges by direction and proximity to form lines
    const directionBins = new Map<number, typeof edges>();
    
    edges.forEach(edge => {
      const binSize = 45; // Group directions in 45-degree bins
      const bin = Math.floor((edge.direction + 180) / binSize) * binSize;
      
      if (!directionBins.has(bin)) {
        directionBins.set(bin, []);
      }
      directionBins.get(bin)!.push(edge);
    });
    
    // For each direction bin, try to form lines
    directionBins.forEach((edgeGroup, direction) => {
      if (edgeGroup.length < 3) return; // Need minimum edges to form a line
      
      // Simplified line formation - just take endpoints
      if (edgeGroup.length > 0) {
        const sortedEdges = edgeGroup.sort((a, b) => a.x - b.x);
        const start = sortedEdges[0];
        const end = sortedEdges[sortedEdges.length - 1];
        
        let lineType: 'diagonal' | 'curved' | 'horizontal' | 'vertical';
        if (Math.abs(direction) < 30 || Math.abs(direction) > 150) lineType = 'horizontal';
        else if (Math.abs(Math.abs(direction) - 90) < 30) lineType = 'vertical';
        else lineType = 'diagonal';
        
        lines.push({
          startX: start.x / imageData.width,
          startY: start.y / imageData.height,
          endX: end.x / imageData.width,
          endY: end.y / imageData.height,
          strength: Math.min(1, edgeGroup.length / 50),
          type: lineType
        });
      }
    });
    
    return lines;
  }

  private estimateDepth(imageData: ImageData): {
    perception: 'flat' | 'shallow' | 'moderate' | 'deep';
    layers: number;
    perspectiveStrength: number;
  } {
    // Simplified depth estimation based on blur gradient and perspective lines
    const edges = this.detectEdges(imageData);
    const { width, height } = imageData;
    
    // Analyze edge sharpness variation (depth of field indicator)
    const topThird = edges.filter(e => e.y < height / 3);
    const middleThird = edges.filter(e => e.y >= height / 3 && e.y < 2 * height / 3);
    const bottomThird = edges.filter(e => e.y >= 2 * height / 3);
    
    const topSharpness = topThird.reduce((sum, e) => sum + e.magnitude, 0) / Math.max(1, topThird.length);
    const middleSharpness = middleThird.reduce((sum, e) => sum + e.magnitude, 0) / Math.max(1, middleThird.length);
    const bottomSharpness = bottomThird.reduce((sum, e) => sum + e.magnitude, 0) / Math.max(1, bottomThird.length);
    
    const sharpnessVariation = Math.abs(topSharpness - bottomSharpness) / Math.max(topSharpness, bottomSharpness, 1);
    
    // Detect converging lines (perspective)
    const diagonalLines = edges.filter(e => Math.abs(Math.abs(e.direction) - 45) < 20 || Math.abs(Math.abs(e.direction) - 135) < 20);
    const perspectiveStrength = Math.min(1, diagonalLines.length / 100);
    
    let perception: 'flat' | 'shallow' | 'moderate' | 'deep';
    if (sharpnessVariation < 0.1 && perspectiveStrength < 0.3) perception = 'flat';
    else if (sharpnessVariation < 0.3 && perspectiveStrength < 0.5) perception = 'shallow';
    else if (sharpnessVariation < 0.6 || perspectiveStrength < 0.7) perception = 'moderate';
    else perception = 'deep';
    
    const layers = Math.min(5, Math.floor(sharpnessVariation * 5) + 1);
    
    return { perception, layers, perspectiveStrength };
  }

  private calculateVisualWeight(imageData: ImageData): Array<{
    region: { x: number; y: number; width: number; height: number };
    weight: number;
  }> {
    // Divide image into grid and calculate visual weight for each cell
    const { width, height, data } = imageData;
    const gridSize = 4;
    const cellWidth = width / gridSize;
    const cellHeight = height / gridSize;
    const weights: Array<{ region: { x: number; y: number; width: number; height: number }; weight: number }> = [];
    
    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        let totalWeight = 0;
        let pixelCount = 0;
        
        const startX = Math.floor(col * cellWidth);
        const startY = Math.floor(row * cellHeight);
        const endX = Math.floor((col + 1) * cellWidth);
        const endY = Math.floor((row + 1) * cellHeight);
        
        for (let y = startY; y < endY; y++) {
          for (let x = startX; x < endX; x++) {
            const idx = (y * width + x) * 4;
            const r = data[idx];
            const g = data[idx + 1];
            const b = data[idx + 2];
            
            // Calculate visual weight based on brightness and saturation
            const brightness = (r + g + b) / 3;
            const { s } = this.rgbToHsl(r / 255, g / 255, b / 255);
            
            // Darker and more saturated areas have more visual weight
            const weight = (1 - brightness / 255) * 0.7 + s * 0.3;
            totalWeight += weight;
            pixelCount++;
          }
        }
        
        weights.push({
          region: {
            x: startX / width,
            y: startY / height,
            width: cellWidth / width,
            height: cellHeight / height
          },
          weight: pixelCount > 0 ? totalWeight / pixelCount : 0
        });
      }
    }
    
    return weights;
  }

  private async analyzeSemanticFeatures(imageData: ImageData): Promise<SemanticFeatures> {
    // Simplified semantic analysis - in production would use computer vision APIs
    return {
      subjects: ['unknown'], // Would be detected using object detection
      mood: ['neutral'],
      style_period: 'contemporary',
      artistic_movement: 'modern',
      complexity: 0.5
    };
  }

  private async classifyArtisticStyle(features: StyleFeatures, imageData: ImageData): Promise<StyleClassification> {
    const scores = new Map<string, number>();
    
    // Score each artistic style based on features
    for (const [styleName, style] of this.artisticStyles) {
      let score = 0;
      const reasoning: string[] = [];
      
      // Color analysis
      if (features.color.saturation > 0.7 && style.compatibility.colorProfiles.includes('vibrant')) {
        score += 0.3;
        reasoning.push('High saturation matches vibrant color profile');
      }
      
      if (features.color.saturation < 0.3 && style.compatibility.colorProfiles.includes('muted')) {
        score += 0.3;
        reasoning.push('Low saturation matches muted color profile');
      }
      
      // Complexity analysis
      const complexity = features.semantic.complexity > 0.7 ? 'high' : 
                        features.semantic.complexity > 0.4 ? 'medium' : 'low';
      
      if (style.compatibility.complexityLevels.includes(complexity)) {
        score += 0.2;
        reasoning.push(`Complexity level matches ${complexity}`);
      }
      
      // Texture analysis
      if (features.texture.roughness > 0.6 && style.characteristics.some(c => c.toLowerCase().includes('brush'))) {
        score += 0.2;
        reasoning.push('High texture roughness suggests brushwork');
      }
      
      // Composition analysis
      if (features.composition.rule_of_thirds && style.characteristics.some(c => c.toLowerCase().includes('composition'))) {
        score += 0.1;
        reasoning.push('Good composition matches artistic standards');
      }
      
      // Time period bonus for contemporary styles
      if (style.period.includes('present') || style.period.includes('contemporary')) {
        score += 0.1;
        reasoning.push('Contemporary style bonus');
      }
      
      scores.set(styleName, score);
    }
    
    // Find best match
    const sortedStyles = Array.from(scores.entries())
      .map(([name, score]) => ({ 
        style: this.artisticStyles.get(name)!, 
        confidence: Math.min(1, score),
        score 
      }))
      .sort((a, b) => b.score - a.score);
    
    const primaryStyle = sortedStyles[0];
    const secondaryStyles = sortedStyles.slice(1, 4);
    
    return {
      primaryStyle: primaryStyle.style,
      secondaryStyles: secondaryStyles.map(s => ({ 
        style: s.style, 
        confidence: s.confidence 
      })),
      confidence: primaryStyle.confidence,
      reasoning: primaryStyle.score > 0.5 ? 
        [`Strong match with ${primaryStyle.style.name}`] : 
        ['No strong style match found']
    };
  }

  private async calculateImageQuality(imageData: ImageData): Promise<ImageQualityMetrics> {
    const { width, height, data } = imageData;
    
    // Calculate sharpness using edge density
    const edges = this.detectEdges(imageData);
    const sharpness = Math.min(1, edges.length / (width * height * 0.01));
    
    // Calculate other metrics
    const contrast = this.calculateContrast(imageData);
    const saturation = this.calculateAverageSaturation(imageData);
    const brightness = this.calculateAverageBrightness(imageData);
    
    // Simple noise estimation
    const noiseLevel = this.estimateNoise(imageData);
    
    return {
      sharpness,
      contrast,
      saturation,
      brightness,
      noiseLevel,
      compression: 0.8, // Would be estimated from JPEG artifacts
      resolution: { width, height },
      aspectRatio: width / height
    };
  }

  private estimateNoise(imageData: ImageData): number {
    // Simple noise estimation using local variance
    const { width, height, data } = imageData;
    let totalVariance = 0;
    let count = 0;
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const centerIdx = (y * width + x) * 4;
        const center = (data[centerIdx] + data[centerIdx + 1] + data[centerIdx + 2]) / 3;
        
        let localSum = 0;
        let localCount = 0;
        
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const idx = ((y + dy) * width + (x + dx)) * 4;
            const value = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
            localSum += value;
            localCount++;
          }
        }
        
        const localMean = localSum / localCount;
        const variance = Math.pow(center - localMean, 2);
        totalVariance += variance;
        count++;
      }
    }
    
    const avgVariance = totalVariance / count;
    return Math.min(1, avgVariance / (255 * 255)); // Normalize to 0-1
  }

  // Style matching and recommendation methods

  async findMatchingStyles(
    imageData: ImageData, 
    availableStyles: StyleReference[]
  ): Promise<StyleMatchingResult> {
    const analysis = await this.analyzeImageStyle(imageData);
    
    const matchedStyles = await Promise.all(
      availableStyles.map(async style => {
        const compatibility = await this.calculateStyleCompatibility(analysis, style);
        return {
          style,
          compatibility: compatibility.score,
          reasons: compatibility.reasons,
          expectedOutcome: compatibility.expectedOutcome,
          processingEstimate: compatibility.processingEstimate
        };
      })
    );
    
    // Sort by compatibility
    matchedStyles.sort((a, b) => b.compatibility - a.compatibility);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(analysis, matchedStyles);
    
    return {
      sourceImage: imageData,
      matchedStyles,
      recommendations,
      analysis
    };
  }

  private async calculateStyleCompatibility(
    analysis: ImageStyleAnalysis, 
    style: StyleReference
  ): Promise<{
    score: number;
    reasons: string[];
    expectedOutcome: string;
    processingEstimate: number;
  }> {
    let score = 0;
    const reasons: string[] = [];
    
    // Check artistic style compatibility
    const primaryStyle = analysis.styleClassification.primaryStyle;
    if (primaryStyle.compatibility.colorProfiles.some(profile => 
      this.matchesColorProfile(analysis.features.color, profile))) {
      score += 0.3;
      reasons.push('Color profile matches artistic style');
    }
    
    // Check complexity compatibility
    const imageComplexity = analysis.features.semantic.complexity > 0.7 ? 'high' : 
                           analysis.features.semantic.complexity > 0.4 ? 'medium' : 'low';
    
    if (primaryStyle.compatibility.complexityLevels.includes(imageComplexity)) {
      score += 0.2;
      reasons.push(`Complexity level (${imageComplexity}) is compatible`);
    }
    
    // Quality considerations
    if (analysis.qualityMetrics.sharpness > 0.7) {
      score += 0.1;
      reasons.push('High image quality enhances style transfer');
    }
    
    if (analysis.qualityMetrics.contrast > 0.5) {
      score += 0.1;
      reasons.push('Good contrast supports style application');
    }
    
    // Tag matching
    if (style.tags.some(tag => 
      primaryStyle.characteristics.some(char => 
        char.toLowerCase().includes(tag.toLowerCase())))) {
      score += 0.2;
      reasons.push('Style tags match image characteristics');
    }
    
    // Resolution considerations
    if (analysis.qualityMetrics.resolution.width >= 512 && 
        analysis.qualityMetrics.resolution.height >= 512) {
      score += 0.1;
      reasons.push('Resolution suitable for high-quality style transfer');
    }
    
    // Determine expected outcome
    let expectedOutcome: string;
    if (score > 0.8) expectedOutcome = 'Excellent style transfer with high fidelity';
    else if (score > 0.6) expectedOutcome = 'Good style transfer with minor adjustments needed';
    else if (score > 0.4) expectedOutcome = 'Moderate style transfer, some artifacts possible';
    else expectedOutcome = 'Poor compatibility, significant artifacts expected';
    
    // Estimate processing time based on complexity and quality
    const baseTime = 30; // seconds
    const complexityMultiplier = imageComplexity === 'high' ? 1.5 : 
                                imageComplexity === 'medium' ? 1.2 : 1.0;
    const resolutionMultiplier = (analysis.qualityMetrics.resolution.width * 
                                 analysis.qualityMetrics.resolution.height) / (512 * 512);
    
    const processingEstimate = Math.round(baseTime * complexityMultiplier * resolutionMultiplier);
    
    return { score, reasons, expectedOutcome, processingEstimate };
  }

  private matchesColorProfile(colorFeatures: ColorFeatures, profile: string): boolean {
    switch (profile) {
      case 'vibrant': return colorFeatures.saturation > 0.6;
      case 'muted': return colorFeatures.saturation < 0.4;
      case 'monochrome': return colorFeatures.saturation < 0.2;
      case 'natural': return colorFeatures.saturation > 0.3 && colorFeatures.saturation < 0.7;
      case 'bold': return colorFeatures.contrast > 0.6;
      default: return false;
    }
  }

  private generateRecommendations(
    analysis: ImageStyleAnalysis, 
    matchedStyles: Array<{ style: StyleReference; compatibility: number; reasons: string[] }>
  ): StyleRecommendation[] {
    const recommendations: StyleRecommendation[] = [];
    
    // Low quality recommendations
    if (analysis.qualityMetrics.sharpness < 0.5) {
      recommendations.push({
        type: 'preprocessing',
        title: 'Enhance Image Sharpness',
        description: 'The image appears soft. Consider applying sharpening before style transfer.',
        confidence: 0.8,
        actions: [{
          type: 'preprocess_image',
          parameter: 'sharpen',
          value: 0.3,
          reason: 'Low sharpness detected'
        }]
      });
    }
    
    // Low contrast recommendations
    if (analysis.qualityMetrics.contrast < 0.4) {
      recommendations.push({
        type: 'preprocessing',
        title: 'Increase Contrast',
        description: 'Low contrast may result in flat style transfer. Consider enhancing contrast.',
        confidence: 0.7,
        actions: [{
          type: 'preprocess_image',
          parameter: 'contrast',
          value: 1.2,
          reason: 'Low contrast detected'
        }]
      });
    }
    
    // Style compatibility recommendations
    const bestStyle = matchedStyles[0];
    if (bestStyle && bestStyle.compatibility < 0.6) {
      recommendations.push({
        type: 'alternative',
        title: 'Consider Alternative Styles',
        description: 'The selected style may not be optimal for this image. Try styles with better compatibility.',
        confidence: 0.6,
        actions: [{
          type: 'select_different_style',
          reason: 'Low compatibility with current style'
        }]
      });
    }
    
    // Parameter adjustment recommendations
    if (analysis.features.semantic.complexity > 0.8) {
      recommendations.push({
        type: 'enhancement',
        title: 'Adjust Style Strength',
        description: 'Complex images benefit from lower style strength to preserve details.',
        confidence: 0.7,
        actions: [{
          type: 'adjust_parameter',
          parameter: 'strength',
          value: 0.6,
          reason: 'High image complexity detected'
        }]
      });
    }
    
    return recommendations;
  }

  // Utility methods

  clearCache(): void {
    this.analysisCache.clear();
    this.emit('cache:cleared');
  }

  getCacheStats(): { size: number; entries: number } {
    return {
      size: this.analysisCache.size,
      entries: this.analysisCache.size
    };
  }

  getArtisticStyles(): ArtisticStyle[] {
    return Array.from(this.artisticStyles.values());
  }

  // Cleanup
  dispose(): void {
    this.analysisCache.clear();
    this.artisticStyles.clear();
    this.removeAllListeners();
  }
}