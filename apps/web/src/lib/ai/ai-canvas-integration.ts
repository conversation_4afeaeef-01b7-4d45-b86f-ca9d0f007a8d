'use client';

import { fabric } from 'fabric';
import { EventEmitter } from 'events';
import { EnhancedHybridCanvasManager } from '../canvas/enhanced-hybrid-canvas';
import { AdvancedLayerManager, LayerNode, LayerType } from '../canvas/advanced-layer-manager';
import { EnhancedReplicateService, GenerationResult, AIGenerationRequest } from './replicate-api-service';

// Types for AI-generated content integration
export interface AIImagePlacementOptions {
  x: number;
  y: number;
  width?: number;
  height?: number;
  scaleToFit?: boolean;
  maintainAspectRatio?: boolean;
  blendMode?: string;
  opacity?: number;
  layerName?: string;
  insertAbove?: string; // Layer ID to insert above
  insertBelow?: string; // Layer ID to insert below
}

export interface CanvasSelectionArea {
  x: number;
  y: number;
  width: number;
  height: number;
  objectIds?: string[]; // Selected objects within the area
}

export interface AIImageGenerationContext {
  canvasSize: { width: number; height: number };
  selectedObjects: Array<{ id: string; object: fabric.Object }>;
  selectedArea?: CanvasSelectionArea;
  backgroundImage?: string;
  currentLayers: LayerNode[];
  style?: {
    colorPalette?: string[];
    theme?: string;
    artStyle?: string;
  };
}

export interface InpaintingRegion {
  mask: ImageData | string; // Image data or base64 mask
  prompt: string;
  strength?: number;
  negativePrompt?: string;
}

export interface ImageToImageRequest {
  sourceImage: string; // Base64 or URL
  prompt: string;
  strength?: number;
  negativePrompt?: string;
  preserveAreas?: CanvasSelectionArea[]; // Areas to preserve
}

export interface StyleTransferRequest {
  sourceImage: string;
  styleReference?: string;
  prompt: string;
  intensity?: number;
}

export interface AICanvasOperation {
  id: string;
  type: 'text-to-image' | 'image-to-image' | 'inpainting' | 'style-transfer' | 'enhancement';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  request: any;
  result?: GenerationResult;
  error?: string;
  startTime: number;
  endTime?: number;
  canvasSnapshot?: string; // Base64 snapshot before operation
}

export interface CanvasAIMetrics {
  totalGenerations: number;
  successfulGenerations: number;
  averageProcessingTime: number;
  totalCost: number;
  cacheHitRate: number;
  popularWorkflows: string[];
  layerDistribution: { ai: number; manual: number };
}

// Main AI Canvas Integration Service
export class AICanvasIntegration extends EventEmitter {
  private canvasManager: EnhancedHybridCanvasManager;
  private layerManager: AdvancedLayerManager;
  private replicateService: EnhancedReplicateService;
  private operations: Map<string, AICanvasOperation> = new Map();
  private isInitialized: boolean = false;
  private metrics: CanvasAIMetrics = {
    totalGenerations: 0,
    successfulGenerations: 0,
    averageProcessingTime: 0,
    totalCost: 0,
    cacheHitRate: 0,
    popularWorkflows: [],
    layerDistribution: { ai: 0, manual: 0 }
  };

  constructor(
    canvasManager: EnhancedHybridCanvasManager,
    layerManager: AdvancedLayerManager,
    replicateService: EnhancedReplicateService
  ) {
    super();
    
    this.canvasManager = canvasManager;
    this.layerManager = layerManager;
    this.replicateService = replicateService;
    
    this.initialize();
  }

  private initialize(): void {
    // Set up event listeners
    this.setupEventListeners();
    
    // Initialize performance tracking
    this.startPerformanceTracking();
    
    this.isInitialized = true;
    this.emit('ai-canvas:initialized');
  }

  private setupEventListeners(): void {
    // Canvas events
    this.canvasManager.on('object:added', ({ id, object }) => {
      this.trackLayerDistribution();
    });

    this.canvasManager.on('object:removed', ({ id }) => {
      this.trackLayerDistribution();
    });

    // Replicate service events
    this.replicateService.on('budget:exceeded', (data) => {
      this.emit('ai-canvas:budget:exceeded', data);
    });

    // Layer management events
    this.layerManager.on('layer:created', (layer) => {
      this.trackLayerDistribution();
    });
  }

  private startPerformanceTracking(): void {
    // Update metrics every 30 seconds
    setInterval(() => {
      this.updateMetrics();
    }, 30000);
  }

  private trackLayerDistribution(): void {
    const allLayers = Array.from(this.layerManager.layers.values());
    const aiLayers = allLayers.filter(layer => 
      layer.metadata?.source === 'ai' || layer.metadata?.aiGenerated
    );
    
    this.metrics.layerDistribution = {
      ai: aiLayers.length,
      manual: allLayers.length - aiLayers.length
    };
  }

  private updateMetrics(): void {
    const completedOps = Array.from(this.operations.values())
      .filter(op => op.status === 'completed');
    
    const failedOps = Array.from(this.operations.values())
      .filter(op => op.status === 'failed');
    
    this.metrics.totalGenerations = this.operations.size;
    this.metrics.successfulGenerations = completedOps.length;
    
    if (completedOps.length > 0) {
      const totalTime = completedOps.reduce((sum, op) => {
        return sum + (op.endTime! - op.startTime);
      }, 0);
      this.metrics.averageProcessingTime = totalTime / completedOps.length;
      
      this.metrics.totalCost = completedOps.reduce((sum, op) => {
        return sum + (op.result?.cost || 0);
      }, 0);
    }
    
    // Get cache stats from Replicate service
    const cacheStats = this.replicateService.getCacheStats();
    this.metrics.cacheHitRate = cacheStats.hitRate;
    
    // Update popular workflows
    const workflowCounts = new Map<string, number>();
    Array.from(this.operations.values()).forEach(op => {
      workflowCounts.set(op.type, (workflowCounts.get(op.type) || 0) + 1);
    });
    
    this.metrics.popularWorkflows = Array.from(workflowCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([workflow]) => workflow);
    
    this.emit('ai-canvas:metrics:updated', this.metrics);
  }

  // Core AI Generation Methods

  async generateTextToImage(
    prompt: string,
    options: Partial<AIImagePlacementOptions> = {},
    generationOptions: {
      model?: string;
      quality?: 'fast' | 'balanced' | 'quality';
      width?: number;
      height?: number;
      negativePrompt?: string;
      seed?: number;
    } = {}
  ): Promise<string> {
    const operationId = this.createOperation('text-to-image', {
      prompt,
      options,
      generationOptions
    });

    try {
      // Get canvas context for better generation
      const context = this.getGenerationContext();
      
      // Select optimal model if not specified
      const modelId = generationOptions.model || 
        (await this.replicateService.findOptimalModel(
          'text-to-image', 
          'text', 
          generationOptions.quality || 'balanced'
        )).modelId;

      // Prepare generation parameters
      const parameters = {
        prompt: this.enhancePromptWithContext(prompt, context),
        negative_prompt: generationOptions.negativePrompt,
        width: generationOptions.width || context.canvasSize.width,
        height: generationOptions.height || context.canvasSize.height,
        seed: generationOptions.seed
      };

      this.updateOperationProgress(operationId, 25, 'Generating image...');

      // Generate image
      const result = await this.replicateService.generateWithCache(modelId, parameters);
      
      this.updateOperationProgress(operationId, 75, 'Placing image on canvas...');

      // Place image on canvas
      const layerId = await this.placeAIImageOnCanvas(result, options, operationId);
      
      this.completeOperation(operationId, result);
      
      this.emit('ai-canvas:generation:completed', {
        operationId,
        layerId,
        result,
        type: 'text-to-image'
      });

      return layerId;

    } catch (error) {
      this.failOperation(operationId, error);
      throw error;
    }
  }

  async generateImageToImage(
    request: ImageToImageRequest,
    options: Partial<AIImagePlacementOptions> = {}
  ): Promise<string> {
    const operationId = this.createOperation('image-to-image', request);

    try {
      // Get optimal model for image-to-image
      const model = await this.replicateService.findOptimalModel(
        'image-to-image',
        'image',
        'balanced'
      );

      const parameters = {
        image: request.sourceImage,
        prompt: request.prompt,
        negative_prompt: request.negativePrompt,
        strength: request.strength || 0.8
      };

      this.updateOperationProgress(operationId, 25, 'Processing image transformation...');

      const result = await this.replicateService.generateWithCache(model.modelId, parameters);
      
      this.updateOperationProgress(operationId, 75, 'Placing transformed image...');

      const layerId = await this.placeAIImageOnCanvas(result, options, operationId);
      
      this.completeOperation(operationId, result);
      
      this.emit('ai-canvas:generation:completed', {
        operationId,
        layerId,
        result,
        type: 'image-to-image'
      });

      return layerId;

    } catch (error) {
      this.failOperation(operationId, error);
      throw error;
    }
  }

  async performInpainting(
    region: InpaintingRegion,
    sourceImage: string,
    options: Partial<AIImagePlacementOptions> = {}
  ): Promise<string> {
    const operationId = this.createOperation('inpainting', { region, sourceImage });

    try {
      // Get inpainting model
      const model = await this.replicateService.findOptimalModel(
        'inpainting',
        'image',
        'balanced'
      );

      const parameters = {
        image: sourceImage,
        mask: region.mask,
        prompt: region.prompt,
        negative_prompt: region.negativePrompt,
        strength: region.strength || 0.9
      };

      this.updateOperationProgress(operationId, 25, 'Performing inpainting...');

      const result = await this.replicateService.generateWithCache(model.modelId, parameters);
      
      this.updateOperationProgress(operationId, 75, 'Applying inpainted result...');

      const layerId = await this.placeAIImageOnCanvas(result, options, operationId);
      
      this.completeOperation(operationId, result);
      
      this.emit('ai-canvas:generation:completed', {
        operationId,
        layerId,
        result,
        type: 'inpainting'
      });

      return layerId;

    } catch (error) {
      this.failOperation(operationId, error);
      throw error;
    }
  }

  async performStyleTransfer(
    request: StyleTransferRequest,
    options: Partial<AIImagePlacementOptions> = {}
  ): Promise<string> {
    const operationId = this.createOperation('style-transfer', request);

    try {
      // Get style transfer model
      const model = await this.replicateService.findOptimalModel(
        'style-transfer',
        'image',
        'quality'
      );

      const parameters = {
        image: request.sourceImage,
        style_image: request.styleReference,
        prompt: request.prompt,
        strength: request.intensity || 0.7
      };

      this.updateOperationProgress(operationId, 25, 'Applying style transfer...');

      const result = await this.replicateService.generateWithCache(model.modelId, parameters);
      
      this.updateOperationProgress(operationId, 75, 'Placing styled image...');

      const layerId = await this.placeAIImageOnCanvas(result, options, operationId);
      
      this.completeOperation(operationId, result);
      
      this.emit('ai-canvas:generation:completed', {
        operationId,
        layerId,
        result,
        type: 'style-transfer'
      });

      return layerId;

    } catch (error) {
      this.failOperation(operationId, error);
      throw error;
    }
  }

  async enhanceImage(
    imageData: string,
    enhancementType: 'upscale' | 'restore' | 'denoise' | 'enhance',
    options: Partial<AIImagePlacementOptions> = {}
  ): Promise<string> {
    const operationId = this.createOperation('enhancement', { imageData, enhancementType });

    try {
      // Select enhancement model based on type
      let specialization: string;
      switch (enhancementType) {
        case 'upscale':
          specialization = 'upscaling';
          break;
        case 'restore':
          specialization = 'face-restoration';
          break;
        case 'denoise':
          specialization = 'enhancement';
          break;
        default:
          specialization = 'enhancement';
      }

      const model = await this.replicateService.findOptimalModel(
        specialization,
        'image',
        'quality'
      );

      const parameters = {
        img: imageData,
        scale: enhancementType === 'upscale' ? 4 : 2
      };

      this.updateOperationProgress(operationId, 25, `Performing ${enhancementType}...`);

      const result = await this.replicateService.generateWithCache(model.modelId, parameters);
      
      this.updateOperationProgress(operationId, 75, 'Placing enhanced image...');

      const layerId = await this.placeAIImageOnCanvas(result, options, operationId);
      
      this.completeOperation(operationId, result);
      
      this.emit('ai-canvas:generation:completed', {
        operationId,
        layerId,
        result,
        type: 'enhancement'
      });

      return layerId;

    } catch (error) {
      this.failOperation(operationId, error);
      throw error;
    }
  }

  // Advanced Workflow Methods

  async executeAIWorkflow(request: AIGenerationRequest): Promise<string[]> {
    const operationId = this.createOperation('workflow', request);
    const layerIds: string[] = [];

    try {
      this.updateOperationProgress(operationId, 10, 'Starting AI workflow...');

      const result = await this.replicateService.executeWorkflow(request);
      
      // Process each step result
      let progress = 20;
      const progressStep = 60 / result.steps.length;

      for (const stepResult of result.steps) {
        if (stepResult.success && stepResult.output) {
          const generationResult: GenerationResult = {
            id: stepResult.stepId,
            modelId: stepResult.modelId,
            output: stepResult.output,
            parameters: stepResult.input,
            cost: stepResult.metadata.cost || 0,
            processingTime: stepResult.executionTime,
            fromCache: stepResult.metadata.cacheHit || false,
            metadata: stepResult.metadata
          };

          const layerId = await this.placeAIImageOnCanvas(generationResult, {
            layerName: `AI Workflow Step ${stepResult.stepId}`
          }, operationId);
          
          layerIds.push(layerId);
        }
        
        progress += progressStep;
        this.updateOperationProgress(operationId, progress, `Completed step ${stepResult.stepId}`);
      }
      
      this.completeOperation(operationId, { finalOutput: result.finalOutput, steps: result.steps });
      
      this.emit('ai-canvas:workflow:completed', {
        operationId,
        layerIds,
        result,
        workflowId: request.workflowId
      });

      return layerIds;

    } catch (error) {
      this.failOperation(operationId, error);
      throw error;
    }
  }

  // Canvas Integration Helper Methods

  private async placeAIImageOnCanvas(
    result: GenerationResult,
    options: Partial<AIImagePlacementOptions> = {},
    operationId?: string
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const imageUrl = Array.isArray(result.output) ? result.output[0] : result.output;
      
      fabric.Image.fromURL(imageUrl, (fabricImage) => {
        if (!fabricImage) {
          reject(new Error('Failed to load generated image'));
          return;
        }

        // Configure image placement
        const placement = {
          left: options.x || 0,
          top: options.y || 0,
          scaleX: 1,
          scaleY: 1,
          opacity: options.opacity || 1,
          ...options
        };

        // Handle scaling
        if (options.width || options.height) {
          if (options.maintainAspectRatio !== false) {
            const aspectRatio = fabricImage.width! / fabricImage.height!;
            if (options.width && !options.height) {
              placement.scaleX = options.width / fabricImage.width!;
              placement.scaleY = placement.scaleX;
            } else if (options.height && !options.width) {
              placement.scaleY = options.height / fabricImage.height!;
              placement.scaleX = placement.scaleY;
            } else if (options.width && options.height) {
              const scaleX = options.width / fabricImage.width!;
              const scaleY = options.height / fabricImage.height!;
              const scale = Math.min(scaleX, scaleY);
              placement.scaleX = scale;
              placement.scaleY = scale;
            }
          } else {
            if (options.width) placement.scaleX = options.width / fabricImage.width!;
            if (options.height) placement.scaleY = options.height / fabricImage.height!;
          }
        } else if (options.scaleToFit) {
          const canvasSize = this.getCanvasSize();
          const scaleX = canvasSize.width / fabricImage.width!;
          const scaleY = canvasSize.height / fabricImage.height!;
          const scale = Math.min(scaleX, scaleY);
          placement.scaleX = scale;
          placement.scaleY = scale;
        }

        // Apply placement settings
        fabricImage.set(placement);

        // Add metadata
        (fabricImage as any).metadata = {
          source: 'ai',
          aiGenerated: true,
          modelId: result.modelId,
          generationId: result.id,
          operationId,
          cost: result.cost,
          processingTime: result.processingTime,
          fromCache: result.fromCache,
          createdAt: Date.now(),
          parameters: result.parameters
        };

        // Create layer
        const layerName = options.layerName || `AI Generated - ${new Date().toLocaleTimeString()}`;
        const layerId = this.generateLayerId();

        try {
          // Add to canvas
          this.canvasManager.addObject(fabricImage, { id: layerId });

          // Create layer in layer manager
          const layer = this.layerManager.createLayer({
            name: layerName,
            type: LayerType.IMAGE,
            visible: true,
            locked: false,
            opacity: options.opacity || 1,
            blendMode: (options.blendMode as any) || 'normal',
            metadata: (fabricImage as any).metadata
          });

          // Handle layer positioning
          if (options.insertAbove) {
            this.layerManager.moveLayer(layer.id, undefined, 
              this.getLayerIndex(options.insertAbove) + 1);
          } else if (options.insertBelow) {
            this.layerManager.moveLayer(layer.id, undefined, 
              this.getLayerIndex(options.insertBelow));
          }

          resolve(layerId);

        } catch (error) {
          reject(error);
        }
      }, {
        crossOrigin: 'anonymous'
      });
    });
  }

  private getGenerationContext(): AIImageGenerationContext {
    const canvasSize = this.getCanvasSize();
    const selectedObjects = this.canvasManager.getSelectedObjects();
    const currentLayers = Array.from(this.layerManager.layers.values());
    
    // Extract color palette from existing objects
    const colorPalette = this.extractColorPalette(selectedObjects);
    
    return {
      canvasSize,
      selectedObjects,
      currentLayers,
      style: {
        colorPalette,
        theme: this.detectCanvasTheme(),
        artStyle: this.detectArtStyle()
      }
    };
  }

  private enhancePromptWithContext(prompt: string, context: AIImageGenerationContext): string {
    let enhancedPrompt = prompt;
    
    // Add style context if available
    if (context.style?.artStyle) {
      enhancedPrompt += `, ${context.style.artStyle} style`;
    }
    
    // Add color palette context
    if (context.style?.colorPalette && context.style.colorPalette.length > 0) {
      const colorDescription = context.style.colorPalette.slice(0, 3).join(', ');
      enhancedPrompt += `, color palette: ${colorDescription}`;
    }
    
    // Add composition context based on selected objects
    if (context.selectedObjects.length > 0) {
      enhancedPrompt += ', integrate with existing composition';
    }
    
    return enhancedPrompt;
  }

  private extractColorPalette(objects: Array<{ id: string; object: fabric.Object }>): string[] {
    const colors = new Set<string>();
    
    objects.forEach(({ object }) => {
      if (object.fill && typeof object.fill === 'string') {
        colors.add(object.fill);
      }
      if (object.stroke && typeof object.stroke === 'string') {
        colors.add(object.stroke);
      }
    });
    
    return Array.from(colors).slice(0, 5);
  }

  private detectCanvasTheme(): string {
    // Analyze canvas background and overall composition
    const bgColor = this.canvasManager.fabricCanvasManager.canvas?.backgroundColor;
    
    if (bgColor === '#000000' || bgColor === 'black') return 'dark';
    if (bgColor === '#ffffff' || bgColor === 'white') return 'light';
    
    return 'neutral';
  }

  private detectArtStyle(): string {
    // Analyze existing objects to detect art style
    const allObjects = this.canvasManager.fabricCanvasManager.objects;
    
    // This is a simplified analysis - in a real implementation,
    // this would use more sophisticated style detection
    if (allObjects.size > 10) return 'detailed';
    if (allObjects.size > 5) return 'modern';
    
    return 'minimalist';
  }

  private getCanvasSize(): { width: number; height: number } {
    const config = this.canvasManager.getConfig();
    return {
      width: config.fabricConfig.width,
      height: config.fabricConfig.height
    };
  }

  private getLayerIndex(layerId: string): number {
    const layer = this.layerManager.getLayer(layerId);
    if (!layer) return 0;
    
    const rootLayers = this.layerManager.rootLayers;
    return rootLayers.indexOf(layerId);
  }

  private generateLayerId(): string {
    return `ai_layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Operation Management

  private createOperation(type: AICanvasOperation['type'], request: any): string {
    const operationId = `ai_op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const operation: AICanvasOperation = {
      id: operationId,
      type,
      status: 'pending',
      progress: 0,
      request,
      startTime: Date.now(),
      canvasSnapshot: this.captureCanvasSnapshot()
    };
    
    this.operations.set(operationId, operation);
    this.emit('ai-canvas:operation:created', operation);
    
    return operationId;
  }

  private updateOperationProgress(operationId: string, progress: number, message?: string): void {
    const operation = this.operations.get(operationId);
    if (!operation) return;
    
    operation.status = 'processing';
    operation.progress = Math.min(100, Math.max(0, progress));
    
    this.emit('ai-canvas:operation:progress', {
      operationId,
      progress: operation.progress,
      message,
      operation
    });
  }

  private completeOperation(operationId: string, result: any): void {
    const operation = this.operations.get(operationId);
    if (!operation) return;
    
    operation.status = 'completed';
    operation.progress = 100;
    operation.result = result;
    operation.endTime = Date.now();
    
    this.emit('ai-canvas:operation:completed', operation);
  }

  private failOperation(operationId: string, error: any): void {
    const operation = this.operations.get(operationId);
    if (!operation) return;
    
    operation.status = 'failed';
    operation.error = error instanceof Error ? error.message : 'Unknown error';
    operation.endTime = Date.now();
    
    this.emit('ai-canvas:operation:failed', operation);
  }

  private captureCanvasSnapshot(): string {
    try {
      const canvas = this.canvasManager.fabricCanvasManager.canvas;
      return canvas?.toDataURL('image/png') || '';
    } catch (error) {
      console.warn('Failed to capture canvas snapshot:', error);
      return '';
    }
  }

  // Public API Methods

  // Selection and area methods
  getSelectedArea(): CanvasSelectionArea | null {
    const selectedObjects = this.canvasManager.getSelectedObjects();
    if (selectedObjects.length === 0) return null;
    
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    const objectIds: string[] = [];
    
    selectedObjects.forEach(({ id, object }) => {
      objectIds.push(id);
      
      const bounds = object.getBoundingRect();
      minX = Math.min(minX, bounds.left);
      minY = Math.min(minY, bounds.top);
      maxX = Math.max(maxX, bounds.left + bounds.width);
      maxY = Math.max(maxY, bounds.top + bounds.height);
    });
    
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY,
      objectIds
    };
  }

  createSelectionMask(area: CanvasSelectionArea): string {
    // Create a mask for inpainting based on selected area
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    
    const canvasSize = this.getCanvasSize();
    canvas.width = canvasSize.width;
    canvas.height = canvasSize.height;
    
    // Fill with black (masked area)
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Fill selection area with white (area to inpaint)
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(area.x, area.y, area.width, area.height);
    
    return canvas.toDataURL('image/png');
  }

  exportCanvasAsImage(): string {
    const canvas = this.canvasManager.fabricCanvasManager.canvas;
    return canvas?.toDataURL('image/png') || '';
  }

  // Operation management
  getOperation(operationId: string): AICanvasOperation | undefined {
    return this.operations.get(operationId);
  }

  getActiveOperations(): AICanvasOperation[] {
    return Array.from(this.operations.values())
      .filter(op => op.status === 'pending' || op.status === 'processing');
  }

  getOperationHistory(): AICanvasOperation[] {
    return Array.from(this.operations.values())
      .sort((a, b) => b.startTime - a.startTime);
  }

  cancelOperation(operationId: string): boolean {
    const operation = this.operations.get(operationId);
    if (!operation || operation.status === 'completed' || operation.status === 'failed') {
      return false;
    }
    
    this.failOperation(operationId, new Error('Operation cancelled by user'));
    return true;
  }

  // Metrics and analytics
  getMetrics(): CanvasAIMetrics {
    return { ...this.metrics };
  }

  getCostSummary(): {
    daily: number;
    monthly: number;
    perGeneration: number;
    remainingBudget: { daily: number; monthly: number };
  } {
    return {
      daily: this.replicateService.getDailyCost(),
      monthly: this.replicateService.getMonthlyCost(),
      perGeneration: this.metrics.totalCost / (this.metrics.totalGenerations || 1),
      remainingBudget: {
        daily: this.replicateService.getCostTracker().getRemainingDailyBudget(),
        monthly: this.replicateService.getCostTracker().getRemainingMonthlyBudget()
      }
    };
  }

  // Cleanup
  dispose(): void {
    this.operations.clear();
    this.removeAllListeners();
  }

  // Getters
  get isReady(): boolean {
    return this.isInitialized && 
           this.canvasManager.isReady && 
           this.layerManager !== null;
  }

  get fabricCanvas(): fabric.Canvas | null {
    return this.canvasManager.fabricCanvasManager.canvas || null;
  }
}