'use client';

import { EventEmitter } from 'events';
import {
  BaseProviderAdapter,
  AIImageModel,
  GenerationRequest,
  GenerationResponse,
  ProviderUsageStats
} from './providers/provider-base';

export interface ProviderSelectionCriteria {
  task: TaskType;
  priority: SelectionPriority;
  budget?: BudgetConstraints;
  quality?: QualityRequirements;
  speed?: SpeedRequirements;
  features?: FeatureRequirements;
  fallbackStrategy?: FallbackStrategy;
  customWeights?: SelectionWeights;
}

export interface BudgetConstraints {
  maxCostPerImage?: number;
  dailyBudgetRemaining?: number;
  monthlyBudgetRemaining?: number;
  costOptimization?: 'aggressive' | 'balanced' | 'quality-first';
}

export interface QualityRequirements {
  minQualityScore?: number;
  artisticStyle?: 'photorealistic' | 'artistic' | 'anime' | 'any';
  outputResolution?: 'low' | 'medium' | 'high' | 'ultra';
  consistency?: 'low' | 'medium' | 'high';
}

export interface SpeedRequirements {
  maxWaitTime?: number; // seconds
  realTimePreferred?: boolean;
  urgency?: 'low' | 'medium' | 'high' | 'critical';
}

export interface FeatureRequirements {
  requiredCapabilities?: string[];
  preferredCapabilities?: string[];
  excludedProviders?: string[];
  requiredProviders?: string[];
}

export interface SelectionWeights {
  cost: number;        // 0-1
  quality: number;     // 0-1
  speed: number;       // 0-1
  reliability: number; // 0-1
  features: number;    // 0-1
}

export type TaskType = 
  | 'text-to-image'
  | 'image-to-image'
  | 'inpainting'
  | 'upscaling'
  | 'style-transfer'
  | 'batch-generation'
  | 'real-time'
  | 'creative'
  | 'photorealistic'
  | 'anime'
  | 'logo-design'
  | 'concept-art';

export type SelectionPriority = 'cost' | 'quality' | 'speed' | 'balanced' | 'features';
export type FallbackStrategy = 'none' | 'cheaper' | 'faster' | 'higher-quality' | 'different-provider';

export interface ProviderScore {
  provider: string;
  model: string;
  score: number;
  breakdown: ScoreBreakdown;
  reasoning: string[];
  estimatedCost: number;
  estimatedTime: number;
  confidence: number;
}

export interface ScoreBreakdown {
  cost: number;
  quality: number;
  speed: number;
  reliability: number;
  features: number;
  availability: number;
  total: number;
}

export interface SelectionResult {
  primary: ProviderScore;
  alternatives: ProviderScore[];
  fallbacks: ProviderScore[];
  rejected: Array<{ provider: string; model: string; reason: string }>;
  confidence: number;
  recommendation: string;
}

export interface ProviderPerformanceMetrics {
  provider: string;
  successRate: number;
  averageResponseTime: number;
  averageQueueTime: number;
  uptime: number;
  costEfficiency: number;
  userSatisfaction: number;
  lastUpdated: Date;
}

export interface MarketConditions {
  timestamp: Date;
  globalLoad: Record<string, number>; // provider -> load percentage
  pricing: Record<string, number>; // provider -> current pricing multiplier
  availability: Record<string, boolean>; // provider -> available
  promotions: Record<string, { discount: number; validUntil: Date }>;
}

export class ProviderSelectionEngine extends EventEmitter {
  private providers: Map<string, BaseProviderAdapter> = new Map();
  private performanceHistory: Map<string, ProviderPerformanceMetrics> = new Map();
  private marketConditions: MarketConditions;
  private learningHistory: Array<{
    criteria: ProviderSelectionCriteria;
    selection: string;
    outcome: 'success' | 'failure' | 'timeout';
    userRating?: number;
    timestamp: Date;
  }> = [];

  private defaultWeights: SelectionWeights = {
    cost: 0.25,
    quality: 0.30,
    speed: 0.20,
    reliability: 0.15,
    features: 0.10
  };

  constructor() {
    super();
    
    this.marketConditions = {
      timestamp: new Date(),
      globalLoad: {},
      pricing: {},
      availability: {},
      promotions: {}
    };

    // Update market conditions periodically
    setInterval(() => this.updateMarketConditions(), 5 * 60 * 1000); // Every 5 minutes
  }

  // Provider management
  addProvider(provider: BaseProviderAdapter): void {
    this.providers.set(provider.name, provider);
    
    // Initialize performance metrics
    this.performanceHistory.set(provider.name, {
      provider: provider.name,
      successRate: 0.95, // Default optimistic
      averageResponseTime: 30,
      averageQueueTime: 5,
      uptime: 100,
      costEfficiency: 0.8,
      userSatisfaction: 4.0,
      lastUpdated: new Date()
    });

    // Set up event listeners
    provider.on('usage:updated', (stats: ProviderUsageStats) => {
      this.updatePerformanceMetrics(provider.name, stats);
    });

    provider.on('health:updated', (health) => {
      this.updateProviderHealth(provider.name, health);
    });

    this.emit('provider:added', provider.name);
  }

  removeProvider(providerName: string): boolean {
    const removed = this.providers.delete(providerName);
    if (removed) {
      this.performanceHistory.delete(providerName);
      this.emit('provider:removed', providerName);
    }
    return removed;
  }

  getProvider(name: string): BaseProviderAdapter | undefined {
    return this.providers.get(name);
  }

  getAllProviders(): BaseProviderAdapter[] {
    return Array.from(this.providers.values());
  }

  // Main selection method
  async selectOptimalProvider(
    criteria: ProviderSelectionCriteria
  ): Promise<SelectionResult> {
    console.log('Selecting optimal provider for criteria:', criteria);

    // Get all available candidates
    const candidates = await this.getCandidateProviders(criteria);
    
    if (candidates.length === 0) {
      throw new Error('No providers available for the specified criteria');
    }

    // Score each candidate
    const scoredCandidates = await Promise.all(
      candidates.map(candidate => this.scoreCandidate(candidate, criteria))
    );

    // Sort by score (highest first)
    scoredCandidates.sort((a, b) => b.score - a.score);

    // Determine primary, alternatives, and fallbacks
    const primary = scoredCandidates[0];
    const alternatives = scoredCandidates.slice(1, 4); // Top 3 alternatives
    const fallbacks = await this.generateFallbacks(criteria, scoredCandidates);

    // Calculate overall confidence
    const confidence = this.calculateSelectionConfidence(primary, alternatives, criteria);

    // Generate recommendation
    const recommendation = this.generateRecommendation(primary, criteria);

    const result: SelectionResult = {
      primary,
      alternatives,
      fallbacks,
      rejected: [], // TODO: Track rejected candidates
      confidence,
      recommendation
    };

    this.emit('selection:completed', { criteria, result });
    return result;
  }

  // Get candidate providers based on criteria
  private async getCandidateProviders(
    criteria: ProviderSelectionCriteria
  ): Promise<Array<{ provider: BaseProviderAdapter; model: AIImageModel }>> {
    const candidates: Array<{ provider: BaseProviderAdapter; model: AIImageModel }> = [];

    for (const provider of this.providers.values()) {
      // Skip if provider is excluded
      if (criteria.features?.excludedProviders?.includes(provider.name)) {
        continue;
      }

      // Skip if not in required providers list (if specified)
      if (criteria.features?.requiredProviders && 
          !criteria.features.requiredProviders.includes(provider.name)) {
        continue;
      }

      // Check provider health
      if (provider.healthStatus.status === 'unhealthy') {
        continue;
      }

      // Find suitable models for this task
      const suitableModels = this.findSuitableModels(provider, criteria);
      
      for (const model of suitableModels) {
        candidates.push({ provider, model });
      }
    }

    return candidates;
  }

  // Find models suitable for the task
  private findSuitableModels(
    provider: BaseProviderAdapter,
    criteria: ProviderSelectionCriteria
  ): AIImageModel[] {
    return provider.models.filter(model => {
      // Check task compatibility
      if (!this.isModelSuitableForTask(model, criteria.task)) {
        return false;
      }

      // Check required capabilities
      if (criteria.features?.requiredCapabilities) {
        for (const capability of criteria.features.requiredCapabilities) {
          if (!(model.capabilities as any)[capability]) {
            return false;
          }
        }
      }

      // Check budget constraints
      if (criteria.budget?.maxCostPerImage && 
          model.pricing.costPerImage > criteria.budget.maxCostPerImage) {
        return false;
      }

      // Check quality requirements
      if (criteria.quality?.minQualityScore && 
          model.performance.qualityScore < criteria.quality.minQualityScore) {
        return false;
      }

      // Check speed requirements
      if (criteria.speed?.maxWaitTime && 
          model.performance.averageTime > criteria.speed.maxWaitTime) {
        return false;
      }

      return true;
    });
  }

  // Check if model is suitable for task
  private isModelSuitableForTask(model: AIImageModel, task: TaskType): boolean {
    switch (task) {
      case 'text-to-image':
        return model.capabilities.textToImage;
      case 'image-to-image':
        return model.capabilities.imageToImage;
      case 'inpainting':
        return model.capabilities.inpainting;
      case 'creative':
      case 'concept-art':
        return model.tags.includes('artistic') || model.tags.includes('creative');
      case 'photorealistic':
        return model.tags.includes('photorealistic') || model.tags.includes('realistic');
      case 'anime':
        return model.tags.includes('anime') || model.tags.includes('manga');
      case 'batch-generation':
        return model.capabilities.batchGeneration;
      case 'real-time':
        return model.capabilities.fastGeneration;
      default:
        return true;
    }
  }

  // Score a candidate provider/model combination
  private async scoreCandidate(
    candidate: { provider: BaseProviderAdapter; model: AIImageModel },
    criteria: ProviderSelectionCriteria
  ): Promise<ProviderScore> {
    const { provider, model } = candidate;
    const weights = criteria.customWeights || this.getWeightsForPriority(criteria.priority);
    
    // Calculate individual scores
    const costScore = this.calculateCostScore(model, criteria.budget);
    const qualityScore = this.calculateQualityScore(model, criteria.quality);
    const speedScore = this.calculateSpeedScore(model, criteria.speed);
    const reliabilityScore = this.calculateReliabilityScore(provider);
    const featuresScore = this.calculateFeaturesScore(model, criteria.features);
    const availabilityScore = this.calculateAvailabilityScore(provider);

    // Calculate weighted total score
    const totalScore = (
      costScore * weights.cost +
      qualityScore * weights.quality +
      speedScore * weights.speed +
      reliabilityScore * weights.reliability +
      featuresScore * weights.features
    ) * availabilityScore; // Availability acts as a multiplier

    const breakdown: ScoreBreakdown = {
      cost: costScore,
      quality: qualityScore,
      speed: speedScore,
      reliability: reliabilityScore,
      features: featuresScore,
      availability: availabilityScore,
      total: totalScore
    };

    // Generate reasoning
    const reasoning = this.generateScoreReasoning(breakdown, model, provider, criteria);

    // Calculate confidence
    const confidence = this.calculateCandidateConfidence(breakdown, criteria);

    return {
      provider: provider.name,
      model: model.id,
      score: totalScore,
      breakdown,
      reasoning,
      estimatedCost: model.pricing.costPerImage,
      estimatedTime: model.performance.averageTime,
      confidence
    };
  }

  // Scoring methods
  private calculateCostScore(model: AIImageModel, budget?: BudgetConstraints): number {
    if (!budget) return 0.8; // Default score if no budget constraints

    const cost = model.pricing.costPerImage;
    
    if (budget.maxCostPerImage && cost > budget.maxCostPerImage) {
      return 0; // Hard constraint
    }

    // Score based on cost efficiency
    const maxReasonableCost = budget.maxCostPerImage || 1.0; // $1 default max
    const efficiency = Math.max(0, (maxReasonableCost - cost) / maxReasonableCost);
    
    // Apply optimization strategy
    if (budget.costOptimization === 'aggressive') {
      return Math.pow(efficiency, 2); // Heavily favor cheaper options
    } else if (budget.costOptimization === 'quality-first') {
      return Math.sqrt(efficiency); // Less penalty for higher cost
    }
    
    return efficiency; // Balanced
  }

  private calculateQualityScore(model: AIImageModel, quality?: QualityRequirements): number {
    let score = model.performance.qualityScore / 10; // Normalize to 0-1
    
    if (!quality) return score;

    // Check artistic style match
    if (quality.artisticStyle && quality.artisticStyle !== 'any') {
      const hasStyle = model.tags.includes(quality.artisticStyle) || 
                     model.tags.includes(quality.artisticStyle.replace('-', ''));
      if (!hasStyle) score *= 0.7; // Penalty for style mismatch
    }

    // Check resolution requirements
    if (quality.outputResolution) {
      const resolutionBonus = this.getResolutionBonus(model, quality.outputResolution);
      score *= resolutionBonus;
    }

    return Math.min(1, score);
  }

  private calculateSpeedScore(model: AIImageModel, speed?: SpeedRequirements): number {
    const baseTime = model.performance.averageTime;
    let score = Math.max(0, (120 - baseTime) / 120); // 2 minutes max reference
    
    if (!speed) return score;

    // Hard constraint for max wait time
    if (speed.maxWaitTime && baseTime > speed.maxWaitTime) {
      return 0;
    }

    // Boost for real-time requirements
    if (speed.realTimePreferred && model.capabilities.fastGeneration) {
      score *= 1.3;
    }

    // Urgency adjustments
    if (speed.urgency === 'critical' && baseTime < 30) {
      score *= 1.5;
    } else if (speed.urgency === 'low') {
      score = Math.sqrt(score); // Less penalty for slower models
    }

    return Math.min(1, score);
  }

  private calculateReliabilityScore(provider: BaseProviderAdapter): number {
    const metrics = this.performanceHistory.get(provider.name);
    if (!metrics) return 0.8; // Default score

    const healthScore = provider.healthStatus.status === 'healthy' ? 1 : 
                       provider.healthStatus.status === 'degraded' ? 0.6 : 0.2;
    
    return (metrics.successRate * 0.4 + 
            (metrics.uptime / 100) * 0.3 + 
            healthScore * 0.3);
  }

  private calculateFeaturesScore(model: AIImageModel, features?: FeatureRequirements): number {
    if (!features) return 0.8; // Default score

    let score = 0.5; // Base score

    // Required capabilities (hard constraint handled earlier)
    if (features.requiredCapabilities) {
      score += 0.3; // Bonus for meeting requirements
    }

    // Preferred capabilities
    if (features.preferredCapabilities) {
      const matchedPreferences = features.preferredCapabilities.filter(
        cap => (model.capabilities as any)[cap]
      ).length;
      score += (matchedPreferences / features.preferredCapabilities.length) * 0.2;
    }

    return Math.min(1, score);
  }

  private calculateAvailabilityScore(provider: BaseProviderAdapter): number {
    const marketLoad = this.marketConditions.globalLoad[provider.name] || 0;
    const isAvailable = this.marketConditions.availability[provider.name] !== false;
    
    if (!isAvailable) return 0;
    
    // Score based on current load (lower load = higher score)
    return Math.max(0.3, 1 - (marketLoad / 100));
  }

  // Helper methods
  private getWeightsForPriority(priority: SelectionPriority): SelectionWeights {
    switch (priority) {
      case 'cost':
        return { cost: 0.50, quality: 0.15, speed: 0.15, reliability: 0.15, features: 0.05 };
      case 'quality':
        return { cost: 0.10, quality: 0.50, speed: 0.15, reliability: 0.20, features: 0.05 };
      case 'speed':
        return { cost: 0.15, quality: 0.15, speed: 0.50, reliability: 0.15, features: 0.05 };
      case 'features':
        return { cost: 0.15, quality: 0.20, speed: 0.15, reliability: 0.15, features: 0.35 };
      case 'balanced':
      default:
        return this.defaultWeights;
    }
  }

  private getResolutionBonus(model: AIImageModel, requirement: string): number {
    const maxRes = model.maxResolution;
    const maxPixels = maxRes.width * maxRes.height;
    
    switch (requirement) {
      case 'ultra': return maxPixels >= 2048 * 2048 ? 1.2 : 0.8;
      case 'high': return maxPixels >= 1024 * 1024 ? 1.1 : 0.9;
      case 'medium': return maxPixels >= 512 * 512 ? 1.0 : 0.9;
      case 'low': return 1.0;
      default: return 1.0;
    }
  }

  private generateScoreReasoning(
    breakdown: ScoreBreakdown,
    model: AIImageModel,
    provider: BaseProviderAdapter,
    criteria: ProviderSelectionCriteria
  ): string[] {
    const reasoning: string[] = [];

    if (breakdown.cost > 0.8) {
      reasoning.push(`Excellent cost efficiency at $${model.pricing.costPerImage} per image`);
    } else if (breakdown.cost < 0.4) {
      reasoning.push(`Higher cost at $${model.pricing.costPerImage} per image`);
    }

    if (breakdown.quality > 0.9) {
      reasoning.push(`Exceptional quality (${model.performance.qualityScore}/10)`);
    } else if (breakdown.quality < 0.6) {
      reasoning.push(`Lower quality score (${model.performance.qualityScore}/10)`);
    }

    if (breakdown.speed > 0.8) {
      reasoning.push(`Fast generation (~${model.performance.averageTime}s)`);
    } else if (breakdown.speed < 0.4) {
      reasoning.push(`Slower generation (~${model.performance.averageTime}s)`);
    }

    if (breakdown.reliability > 0.9) {
      reasoning.push(`Highly reliable provider with excellent uptime`);
    }

    if (model.tags.length > 0) {
      reasoning.push(`Specialized for: ${model.tags.slice(0, 3).join(', ')}`);
    }

    return reasoning;
  }

  private calculateCandidateConfidence(
    breakdown: ScoreBreakdown,
    criteria: ProviderSelectionCriteria
  ): number {
    // Higher confidence when all scores are good or when critical requirements are well met
    const scores = [breakdown.cost, breakdown.quality, breakdown.speed, breakdown.reliability];
    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / scores.length;
    
    // Lower variance = higher confidence
    return Math.max(0.3, Math.min(1.0, average - variance));
  }

  private calculateSelectionConfidence(
    primary: ProviderScore,
    alternatives: ProviderScore[],
    criteria: ProviderSelectionCriteria
  ): number {
    // Higher confidence when primary clearly outperforms alternatives
    if (alternatives.length === 0) return primary.confidence;
    
    const scoreDifference = primary.score - alternatives[0].score;
    const relativeDifference = scoreDifference / primary.score;
    
    // Combine individual confidence with selection clarity
    return (primary.confidence + Math.min(1.0, relativeDifference * 2)) / 2;
  }

  private generateRecommendation(
    primary: ProviderScore,
    criteria: ProviderSelectionCriteria
  ): string {
    const provider = this.providers.get(primary.provider)!;
    const model = provider.getModel(primary.model)!;
    
    let recommendation = `${provider.displayName} ${model.displayName} is recommended`;
    
    if (primary.score > 0.9) {
      recommendation += ' as an excellent match';
    } else if (primary.score > 0.7) {
      recommendation += ' as a good fit';
    } else {
      recommendation += ' as the best available option';
    }
    
    recommendation += ` for ${criteria.task} with ${criteria.priority} priority.`;
    
    // Add specific highlights
    if (primary.breakdown.cost > 0.8) {
      recommendation += ' Great value for the cost.';
    }
    if (primary.breakdown.quality > 0.9) {
      recommendation += ' Exceptional output quality.';
    }
    if (primary.breakdown.speed > 0.8) {
      recommendation += ' Fast generation time.';
    }
    
    return recommendation;
  }

  private async generateFallbacks(
    criteria: ProviderSelectionCriteria,
    scoredCandidates: ProviderScore[]
  ): Promise<ProviderScore[]> {
    if (!criteria.fallbackStrategy || criteria.fallbackStrategy === 'none') {
      return [];
    }

    // Simple fallback strategy - return lower-scored candidates
    return scoredCandidates.slice(4, 7); // Next 3 options as fallbacks
  }

  // Performance tracking
  private updatePerformanceMetrics(providerName: string, stats: ProviderUsageStats): void {
    const current = this.performanceHistory.get(providerName);
    if (!current) return;

    const successRate = stats.totalGenerations > 0 ? 
      stats.successfulGenerations / stats.totalGenerations : current.successRate;

    this.performanceHistory.set(providerName, {
      ...current,
      successRate,
      averageResponseTime: stats.averageResponseTime,
      costEfficiency: this.calculateCostEfficiency(stats),
      lastUpdated: new Date()
    });
  }

  private updateProviderHealth(providerName: string, health: any): void {
    const current = this.performanceHistory.get(providerName);
    if (!current) return;

    const uptime = health.status === 'healthy' ? 100 : 
                  health.status === 'degraded' ? 75 : 
                  health.status === 'unhealthy' ? 25 : 0;

    this.performanceHistory.set(providerName, {
      ...current,
      uptime,
      lastUpdated: new Date()
    });
  }

  private calculateCostEfficiency(stats: ProviderUsageStats): number {
    // Simple cost efficiency calculation
    // This could be more sophisticated based on quality/cost ratios
    return Math.max(0, Math.min(1, 1 - (stats.totalCost / Math.max(1, stats.totalGenerations)) / 0.5));
  }

  private updateMarketConditions(): void {
    // In a real implementation, this would fetch real market data
    // For now, simulate market conditions
    for (const provider of this.providers.values()) {
      this.marketConditions.globalLoad[provider.name] = Math.random() * 80; // 0-80% load
      this.marketConditions.pricing[provider.name] = 0.8 + Math.random() * 0.4; // 0.8-1.2x multiplier
      this.marketConditions.availability[provider.name] = Math.random() > 0.05; // 95% uptime
    }
    
    this.marketConditions.timestamp = new Date();
    this.emit('market:updated', this.marketConditions);
  }

  // Learning and optimization
  recordSelectionOutcome(
    criteria: ProviderSelectionCriteria,
    selectedProvider: string,
    outcome: 'success' | 'failure' | 'timeout',
    userRating?: number
  ): void {
    this.learningHistory.push({
      criteria,
      selection: selectedProvider,
      outcome,
      userRating,
      timestamp: new Date()
    });

    // Keep only recent history
    if (this.learningHistory.length > 1000) {
      this.learningHistory = this.learningHistory.slice(-1000);
    }

    this.emit('learning:recorded', { criteria, selectedProvider, outcome, userRating });
  }

  // Analytics and insights
  getSelectionAnalytics(timeRange: { start: Date; end: Date }) {
    const relevantHistory = this.learningHistory.filter(
      entry => entry.timestamp >= timeRange.start && entry.timestamp <= timeRange.end
    );

    const providerSuccess = new Map<string, { total: number; success: number }>();
    const taskSuccess = new Map<TaskType, { total: number; success: number }>();
    
    for (const entry of relevantHistory) {
      // Provider success tracking
      const providerStats = providerSuccess.get(entry.selection) || { total: 0, success: 0 };
      providerStats.total++;
      if (entry.outcome === 'success') providerStats.success++;
      providerSuccess.set(entry.selection, providerStats);

      // Task success tracking
      const taskStats = taskSuccess.get(entry.criteria.task) || { total: 0, success: 0 };
      taskStats.total++;
      if (entry.outcome === 'success') taskStats.success++;
      taskSuccess.set(entry.criteria.task, taskStats);
    }

    return {
      totalSelections: relevantHistory.length,
      overallSuccessRate: relevantHistory.filter(e => e.outcome === 'success').length / relevantHistory.length,
      providerPerformance: Array.from(providerSuccess.entries()).map(([provider, stats]) => ({
        provider,
        successRate: stats.success / stats.total,
        totalSelections: stats.total
      })),
      taskPerformance: Array.from(taskSuccess.entries()).map(([task, stats]) => ({
        task,
        successRate: stats.success / stats.total,
        totalSelections: stats.total
      })),
      averageUserRating: relevantHistory
        .filter(e => e.userRating)
        .reduce((sum, e) => sum + e.userRating!, 0) / 
        relevantHistory.filter(e => e.userRating).length || 0
    };
  }

  // Cleanup
  dispose(): void {
    this.removeAllListeners();
    this.providers.clear();
    this.performanceHistory.clear();
    this.learningHistory.length = 0;
  }
}