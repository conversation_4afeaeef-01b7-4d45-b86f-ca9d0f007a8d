'use client';

import { z } from 'zod';
import { throttle, debounce } from 'throttle-debounce';
import LRU from 'lru-cache';

// Base types for AI services
export interface AIProvider {
  name: string;
  apiKey?: string;
  baseUrl?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface AIRequest {
  prompt: string;
  context?: string;
  language?: string;
  fileName?: string;
  cursorPosition?: { line: number; column: number };
  additionalContext?: Record<string, any>;
}

export interface AIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    model?: string;
    tokens?: number;
    responseTime?: number;
    cached?: boolean;
  };
}

export interface CacheConfig {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
}

// Response schemas
export const CompletionItemSchema = z.object({
  label: z.string(),
  insertText: z.string(),
  detail: z.string().optional(),
  documentation: z.string().optional(),
  kind: z.enum(['function', 'variable', 'class', 'interface', 'module', 'property', 'method', 'snippet']),
  priority: z.number().optional(),
});

export const CodeSuggestionSchema = z.object({
  type: z.enum(['refactor', 'optimize', 'fix', 'improve', 'security', 'performance']),
  title: z.string(),
  description: z.string(),
  codeChanges: z.array(z.object({
    startLine: z.number(),
    endLine: z.number(),
    originalCode: z.string(),
    suggestedCode: z.string(),
    explanation: z.string(),
  })),
  confidence: z.number().min(0).max(1),
});

export const AnalysisResultSchema = z.object({
  issues: z.array(z.object({
    type: z.enum(['error', 'warning', 'info', 'hint']),
    severity: z.enum(['critical', 'high', 'medium', 'low']),
    message: z.string(),
    line: z.number(),
    column: z.number(),
    endLine: z.number().optional(),
    endColumn: z.number().optional(),
    rule: z.string().optional(),
    suggestions: z.array(z.string()).optional(),
  })),
  metrics: z.object({
    complexity: z.number().optional(),
    maintainability: z.number().optional(),
    testCoverage: z.number().optional(),
    security: z.number().optional(),
  }).optional(),
  recommendations: z.array(z.string()).optional(),
});

export const DebugAnalysisSchema = z.object({
  errorType: z.string(),
  errorMessage: z.string(),
  possibleCauses: z.array(z.string()),
  solutions: z.array(z.object({
    title: z.string(),
    description: z.string(),
    codeExample: z.string().optional(),
    priority: z.enum(['high', 'medium', 'low']),
  })),
  relatedDocs: z.array(z.object({
    title: z.string(),
    url: z.string(),
    description: z.string(),
  })).optional(),
});

export const TestGenerationSchema = z.object({
  testFramework: z.string(),
  testCode: z.string(),
  description: z.string(),
  coverage: z.object({
    functions: z.array(z.string()),
    branches: z.array(z.string()),
    edgeCases: z.array(z.string()),
  }),
  dependencies: z.array(z.string()).optional(),
});

export type CompletionItem = z.infer<typeof CompletionItemSchema>;
export type CodeSuggestion = z.infer<typeof CodeSuggestionSchema>;
export type AnalysisResult = z.infer<typeof AnalysisResultSchema>;
export type DebugAnalysis = z.infer<typeof DebugAnalysisSchema>;
export type TestGeneration = z.infer<typeof TestGenerationSchema>;

// Base AI Service class
export abstract class BaseAIService {
  protected provider: AIProvider;
  protected cache: LRU<string, any>;
  protected requestThrottle: any;
  protected requestDebounce: any;

  constructor(provider: AIProvider, cacheConfig: CacheConfig = { maxSize: 1000, ttl: 5 * 60 * 1000 }) {
    this.provider = provider;
    this.cache = new LRU({
      max: cacheConfig.maxSize,
      ttl: cacheConfig.ttl,
    });

    // Throttle requests to prevent API rate limiting
    this.requestThrottle = throttle(500, this.makeRequest.bind(this));
    this.requestDebounce = debounce(300, this.makeRequest.bind(this));
  }

  // Abstract methods to be implemented by specific AI services
  abstract makeRequest(request: AIRequest): Promise<AIResponse>;
  
  // Cache management
  protected getCacheKey(request: AIRequest): string {
    return JSON.stringify({
      prompt: request.prompt,
      context: request.context,
      language: request.language,
      provider: this.provider.name,
      model: this.provider.model,
    });
  }

  protected getCachedResponse<T>(cacheKey: string): T | null {
    return this.cache.get(cacheKey) || null;
  }

  protected setCachedResponse<T>(cacheKey: string, response: T): void {
    this.cache.set(cacheKey, response);
  }

  // Request helpers
  protected async requestWithCache<T>(
    request: AIRequest,
    processor: (request: AIRequest) => Promise<AIResponse<T>>,
    useCache: boolean = true
  ): Promise<AIResponse<T>> {
    const cacheKey = this.getCacheKey(request);
    
    if (useCache) {
      const cached = this.getCachedResponse<AIResponse<T>>(cacheKey);
      if (cached) {
        return {
          ...cached,
          metadata: {
            ...cached.metadata,
            cached: true,
          },
        };
      }
    }

    const startTime = Date.now();
    try {
      const response = await processor(request);
      const responseTime = Date.now() - startTime;
      
      const enrichedResponse = {
        ...response,
        metadata: {
          ...response.metadata,
          responseTime,
          cached: false,
        },
      };

      if (useCache && response.success) {
        this.setCachedResponse(cacheKey, enrichedResponse);
      }

      return enrichedResponse;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          responseTime: Date.now() - startTime,
          cached: false,
        },
      };
    }
  }

  // Throttled request methods
  protected async throttledRequest(request: AIRequest): Promise<AIResponse> {
    return new Promise((resolve, reject) => {
      this.requestThrottle(request)
        .then(resolve)
        .catch(reject);
    });
  }

  protected async debouncedRequest(request: AIRequest): Promise<AIResponse> {
    return new Promise((resolve, reject) => {
      this.requestDebounce(request)
        .then(resolve)
        .catch(reject);
    });
  }

  // Utility methods
  protected validateRequest(request: AIRequest): boolean {
    return !!(request.prompt && request.prompt.trim());
  }

  protected sanitizeInput(input: string): string {
    // Remove potentially harmful content
    return input
      .replace(/\b(password|token|key|secret)\s*[:=]\s*\S+/gi, '$1: [REDACTED]')
      .trim();
  }

  protected buildPrompt(request: AIRequest, additionalContext?: string): string {
    const parts = [];
    
    if (request.context) {
      parts.push(`Context: ${request.context}`);
    }
    
    if (request.language) {
      parts.push(`Language: ${request.language}`);
    }
    
    if (request.fileName) {
      parts.push(`File: ${request.fileName}`);
    }
    
    if (additionalContext) {
      parts.push(additionalContext);
    }
    
    parts.push(`Request: ${request.prompt}`);
    
    return parts.join('\n\n');
  }

  // Configuration updates
  updateProvider(provider: Partial<AIProvider>): void {
    this.provider = { ...this.provider, ...provider };
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; max: number } {
    return {
      size: this.cache.size,
      max: this.cache.max,
    };
  }
}

// AI Service factory
export class AIServiceFactory {
  private static services: Map<string, BaseAIService> = new Map();

  static registerService(name: string, service: BaseAIService): void {
    this.services.set(name, service);
  }

  static getService(name: string): BaseAIService | null {
    return this.services.get(name) || null;
  }

  static getAllServices(): Map<string, BaseAIService> {
    return new Map(this.services);
  }

  static removeService(name: string): boolean {
    return this.services.delete(name);
  }

  static clearServices(): void {
    this.services.clear();
  }
}

// Configuration management
export interface AIConfiguration {
  providers: AIProvider[];
  defaultProvider: string;
  enableCaching: boolean;
  cacheConfig: CacheConfig;
  features: {
    codeCompletion: boolean;
    codeSuggestions: boolean;
    codeAnalysis: boolean;
    debugging: boolean;
    testGeneration: boolean;
  };
  requestLimits: {
    maxRequestsPerMinute: number;
    maxTokensPerRequest: number;
  };
}

export const defaultAIConfiguration: AIConfiguration = {
  providers: [
    {
      name: 'openai',
      model: 'gpt-4-turbo-preview',
      maxTokens: 4000,
      temperature: 0.1,
    },
    {
      name: 'anthropic',
      model: 'claude-3-sonnet-20240229',
      maxTokens: 4000,
      temperature: 0.1,
    },
  ],
  defaultProvider: 'openai',
  enableCaching: true,
  cacheConfig: {
    maxSize: 1000,
    ttl: 5 * 60 * 1000, // 5 minutes
  },
  features: {
    codeCompletion: true,
    codeSuggestions: true,
    codeAnalysis: true,
    debugging: true,
    testGeneration: true,
  },
  requestLimits: {
    maxRequestsPerMinute: 60,
    maxTokensPerRequest: 4000,
  },
};

// Configuration store
export class AIConfigurationManager {
  private static instance: AIConfigurationManager;
  private config: AIConfiguration;

  private constructor() {
    this.config = { ...defaultAIConfiguration };
  }

  static getInstance(): AIConfigurationManager {
    if (!this.instance) {
      this.instance = new AIConfigurationManager();
    }
    return this.instance;
  }

  getConfiguration(): AIConfiguration {
    return { ...this.config };
  }

  updateConfiguration(updates: Partial<AIConfiguration>): void {
    this.config = { ...this.config, ...updates };
  }

  getProvider(name: string): AIProvider | null {
    return this.config.providers.find(p => p.name === name) || null;
  }

  addProvider(provider: AIProvider): void {
    const existingIndex = this.config.providers.findIndex(p => p.name === provider.name);
    if (existingIndex >= 0) {
      this.config.providers[existingIndex] = provider;
    } else {
      this.config.providers.push(provider);
    }
  }

  removeProvider(name: string): boolean {
    const index = this.config.providers.findIndex(p => p.name === name);
    if (index >= 0) {
      this.config.providers.splice(index, 1);
      return true;
    }
    return false;
  }

  isFeatureEnabled(feature: keyof AIConfiguration['features']): boolean {
    return this.config.features[feature];
  }

  setFeatureEnabled(feature: keyof AIConfiguration['features'], enabled: boolean): void {
    this.config.features[feature] = enabled;
  }
}