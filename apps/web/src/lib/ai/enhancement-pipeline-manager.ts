'use client';

import { EventEmitter } from 'events';
import { ImageEnhancementEngine, EnhancementRequest, EnhancementResult, EnhancementType, EnhancementParameters } from './image-enhancement-engine';
import { MultiProviderService } from './multi-provider-service';

export interface PipelineStage {
  id: string;
  name: string;
  type: EnhancementType;
  parameters: EnhancementParameters;
  condition?: (image: ImageData | string, quality: any) => boolean;
  priority: number;
  dependencies?: string[];
  enabled: boolean;
  estimatedTime: number;
  estimatedCost: number;
}

export interface PipelineConfig {
  name: string;
  description: string;
  stages: PipelineStage[];
  autoOptimize: boolean;
  qualityThreshold: number;
  maxCost: number;
  maxTime: number;
  parallelExecution: boolean;
  continueOnFailure: boolean;
}

export interface PipelineExecution {
  id: string;
  config: PipelineConfig;
  input: ImageData | string;
  stages: PipelineStageResult[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: number;
  endTime?: number;
  totalCost: number;
  totalTime: number;
  finalResult?: EnhancementResult;
  error?: string;
  progress: number;
  currentStage?: string;
}

export interface PipelineStageResult {
  stageId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startTime?: number;
  endTime?: number;
  input?: ImageData | string;
  output?: ImageData | string;
  result?: EnhancementResult;
  error?: string;
  qualityImprovement?: number;
  cost: number;
  time: number;
}

export interface PipelinePreset {
  id: string;
  name: string;
  description: string;
  category: 'photography' | 'digital-art' | 'social-media' | 'print' | 'restoration' | 'general';
  config: PipelineConfig;
  preview?: string;
  tags: string[];
  popularity: number;
  avgRating: number;
  avgTime: number;
  avgCost: number;
}

export interface PipelineMetrics {
  totalExecutions: number;
  successRate: number;
  averageTime: number;
  averageCost: number;
  popularStages: { stage: EnhancementType; count: number }[];
  qualityImprovements: { avg: number; min: number; max: number };
  costDistribution: { low: number; medium: number; high: number };
  timeDistribution: { fast: number; medium: number; slow: number };
}

export interface QualityGate {
  id: string;
  name: string;
  condition: (result: EnhancementResult) => boolean;
  action: 'continue' | 'retry' | 'skip' | 'abort';
  retryCount: number;
  threshold: number;
}

export class EnhancementPipelineManager extends EventEmitter {
  private enhancementEngine: ImageEnhancementEngine;
  private multiProviderService: MultiProviderService;
  private executions: Map<string, PipelineExecution> = new Map();
  private presets: Map<string, PipelinePreset> = new Map();
  private qualityGates: Map<string, QualityGate> = new Map();
  private metrics: PipelineMetrics;
  private isInitialized: boolean = false;
  private maxConcurrentExecutions: number = 3;
  private executionQueue: string[] = [];

  constructor(
    enhancementEngine: ImageEnhancementEngine,
    multiProviderService: MultiProviderService
  ) {
    super();
    this.enhancementEngine = enhancementEngine;
    this.multiProviderService = multiProviderService;
    this.metrics = this.initializeMetrics();
    this.setupEventListeners();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load preset pipelines
      await this.loadPresetPipelines();
      
      // Initialize quality gates
      this.initializeQualityGates();
      
      // Start execution manager
      this.startExecutionManager();
      
      this.isInitialized = true;
      this.emit('initialized');
      
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  private initializeMetrics(): PipelineMetrics {
    return {
      totalExecutions: 0,
      successRate: 0,
      averageTime: 0,
      averageCost: 0,
      popularStages: [],
      qualityImprovements: { avg: 0, min: 0, max: 0 },
      costDistribution: { low: 0, medium: 0, high: 0 },
      timeDistribution: { fast: 0, medium: 0, slow: 0 }
    };
  }

  private async loadPresetPipelines(): Promise<void> {
    const presets: PipelinePreset[] = [
      // Photography Enhancement Pipeline
      {
        id: 'photography-pro',
        name: 'Professional Photography',
        description: 'Comprehensive enhancement for professional photography',
        category: 'photography',
        config: {
          name: 'Professional Photography Pipeline',
          description: 'Multi-stage enhancement for professional photos',
          stages: [
            {
              id: 'noise-reduction',
              name: 'Noise Reduction',
              type: EnhancementType.DENOISE,
              parameters: {
                strength: 0.7,
                preserveOriginal: 0.8,
                denoiseLevel: 0.6,
                preserveEdges: true,
                adaptiveEnhancement: true
              },
              condition: (image, quality) => quality.metrics.noise > 40,
              priority: 1,
              enabled: true,
              estimatedTime: 8,
              estimatedCost: 0.03
            },
            {
              id: 'sharpening',
              name: 'Detail Enhancement',
              type: EnhancementType.SHARPEN,
              parameters: {
                strength: 0.8,
                preserveOriginal: 0.9,
                sharpenLevel: 0.7,
                preserveEdges: true,
                adaptiveEnhancement: true
              },
              dependencies: ['noise-reduction'],
              priority: 2,
              enabled: true,
              estimatedTime: 3,
              estimatedCost: 0.01
            },
            {
              id: 'color-correction',
              name: 'Color Correction',
              type: EnhancementType.COLOR_CORRECT,
              parameters: {
                strength: 0.6,
                preserveOriginal: 0.8,
                colorCorrection: true,
                saturationAdjustment: 0.1,
                contrastBoost: 0.3
              },
              priority: 3,
              enabled: true,
              estimatedTime: 2,
              estimatedCost: 0.01
            },
            {
              id: 'exposure-fix',
              name: 'Exposure Optimization',
              type: EnhancementType.EXPOSURE_FIX,
              parameters: {
                strength: 0.7,
                preserveOriginal: 0.8,
                exposureAdjustment: 0,
                hdrMode: true
              },
              condition: (image, quality) => Math.abs(quality.metrics.exposure - 50) > 15,
              priority: 4,
              enabled: true,
              estimatedTime: 5,
              estimatedCost: 0.02
            }
          ],
          autoOptimize: true,
          qualityThreshold: 0.8,
          maxCost: 0.15,
          maxTime: 30,
          parallelExecution: false,
          continueOnFailure: true
        },
        preview: 'photography-pro-preview.jpg',
        tags: ['photography', 'professional', 'noise-reduction', 'sharpening'],
        popularity: 85,
        avgRating: 4.7,
        avgTime: 18,
        avgCost: 0.07
      },

      // Digital Art Enhancement Pipeline
      {
        id: 'digital-art-enhance',
        name: 'Digital Art Enhancement',
        description: 'Optimized for digital artwork and illustrations',
        category: 'digital-art',
        config: {
          name: 'Digital Art Enhancement Pipeline',
          description: 'Specialized enhancement for digital art',
          stages: [
            {
              id: 'artifact-removal',
              name: 'Artifact Removal',
              type: EnhancementType.ARTIFACT_REMOVAL,
              parameters: {
                strength: 0.8,
                preserveOriginal: 0.9,
                artifactReduction: 0.7,
                preserveEdges: true
              },
              priority: 1,
              enabled: true,
              estimatedTime: 6,
              estimatedCost: 0.03
            },
            {
              id: 'super-resolution',
              name: 'Super Resolution',
              type: EnhancementType.SUPER_RESOLUTION,
              parameters: {
                strength: 0.9,
                preserveOriginal: 0.7,
                scale: 2,
                preserveEdges: true,
                adaptiveEnhancement: true
              },
              dependencies: ['artifact-removal'],
              priority: 2,
              enabled: true,
              estimatedTime: 25,
              estimatedCost: 0.12
            },
            {
              id: 'detail-enhancement',
              name: 'Detail Enhancement',
              type: EnhancementType.DETAIL_ENHANCEMENT,
              parameters: {
                strength: 0.7,
                preserveOriginal: 0.8,
                contrastBoost: 0.4,
                adaptiveEnhancement: true
              },
              priority: 3,
              enabled: true,
              estimatedTime: 4,
              estimatedCost: 0.02
            }
          ],
          autoOptimize: true,
          qualityThreshold: 0.85,
          maxCost: 0.25,
          maxTime: 45,
          parallelExecution: false,
          continueOnFailure: true
        },
        preview: 'digital-art-preview.jpg',
        tags: ['digital-art', 'illustration', 'super-resolution', 'detail'],
        popularity: 72,
        avgRating: 4.5,
        avgTime: 35,
        avgCost: 0.17
      },

      // Social Media Optimization Pipeline
      {
        id: 'social-media-opt',
        name: 'Social Media Optimization',
        description: 'Quick enhancement for social media posts',
        category: 'social-media',
        config: {
          name: 'Social Media Optimization Pipeline',
          description: 'Fast enhancement for social media',
          stages: [
            {
              id: 'quick-enhance',
              name: 'Quick Enhancement',
              type: EnhancementType.DETAIL_ENHANCEMENT,
              parameters: {
                strength: 0.6,
                preserveOriginal: 0.9,
                contrastBoost: 0.3,
                colorCorrection: true,
                saturationAdjustment: 0.2
              },
              priority: 1,
              enabled: true,
              estimatedTime: 3,
              estimatedCost: 0.01
            },
            {
              id: 'color-pop',
              name: 'Color Enhancement',
              type: EnhancementType.COLOR_CORRECT,
              parameters: {
                strength: 0.7,
                preserveOriginal: 0.8,
                colorCorrection: true,
                saturationAdjustment: 0.3,
                contrastBoost: 0.2
              },
              priority: 2,
              enabled: true,
              estimatedTime: 2,
              estimatedCost: 0.01
            }
          ],
          autoOptimize: false,
          qualityThreshold: 0.7,
          maxCost: 0.05,
          maxTime: 10,
          parallelExecution: true,
          continueOnFailure: false
        },
        preview: 'social-media-preview.jpg',
        tags: ['social-media', 'quick', 'color-enhancement', 'fast'],
        popularity: 93,
        avgRating: 4.3,
        avgTime: 5,
        avgCost: 0.02
      },

      // Print Quality Pipeline
      {
        id: 'print-quality',
        name: 'Print Quality Enhancement',
        description: 'High-quality enhancement for print materials',
        category: 'print',
        config: {
          name: 'Print Quality Enhancement Pipeline',
          description: 'Professional enhancement for print media',
          stages: [
            {
              id: 'super-resolution',
              name: 'High Resolution Upscaling',
              type: EnhancementType.SUPER_RESOLUTION,
              parameters: {
                strength: 0.9,
                preserveOriginal: 0.8,
                scale: 4,
                preserveEdges: true,
                adaptiveEnhancement: true
              },
              priority: 1,
              enabled: true,
              estimatedTime: 30,
              estimatedCost: 0.15
            },
            {
              id: 'noise-reduction',
              name: 'Noise Reduction',
              type: EnhancementType.DENOISE,
              parameters: {
                strength: 0.8,
                preserveOriginal: 0.9,
                denoiseLevel: 0.7,
                preserveEdges: true
              },
              dependencies: ['super-resolution'],
              priority: 2,
              enabled: true,
              estimatedTime: 12,
              estimatedCost: 0.04
            },
            {
              id: 'sharpening',
              name: 'Print Sharpening',
              type: EnhancementType.SHARPEN,
              parameters: {
                strength: 0.9,
                preserveOriginal: 0.8,
                sharpenLevel: 0.8,
                preserveEdges: true,
                adaptiveEnhancement: true
              },
              dependencies: ['noise-reduction'],
              priority: 3,
              enabled: true,
              estimatedTime: 4,
              estimatedCost: 0.02
            }
          ],
          autoOptimize: true,
          qualityThreshold: 0.9,
          maxCost: 0.3,
          maxTime: 60,
          parallelExecution: false,
          continueOnFailure: true
        },
        preview: 'print-quality-preview.jpg',
        tags: ['print', 'high-resolution', 'professional', 'upscaling'],
        popularity: 68,
        avgRating: 4.8,
        avgTime: 46,
        avgCost: 0.21
      },

      // Restoration Pipeline
      {
        id: 'photo-restoration',
        name: 'Photo Restoration',
        description: 'Comprehensive restoration for old or damaged photos',
        category: 'restoration',
        config: {
          name: 'Photo Restoration Pipeline',
          description: 'Multi-stage restoration for damaged photos',
          stages: [
            {
              id: 'artifact-removal',
              name: 'Artifact Removal',
              type: EnhancementType.ARTIFACT_REMOVAL,
              parameters: {
                strength: 0.9,
                preserveOriginal: 0.7,
                artifactReduction: 0.8,
                preserveEdges: true
              },
              priority: 1,
              enabled: true,
              estimatedTime: 8,
              estimatedCost: 0.04
            },
            {
              id: 'noise-reduction',
              name: 'Noise Reduction',
              type: EnhancementType.DENOISE,
              parameters: {
                strength: 0.8,
                preserveOriginal: 0.8,
                denoiseLevel: 0.8,
                preserveEdges: true
              },
              dependencies: ['artifact-removal'],
              priority: 2,
              enabled: true,
              estimatedTime: 10,
              estimatedCost: 0.03
            },
            {
              id: 'restoration',
              name: 'Photo Restoration',
              type: EnhancementType.RESTORE,
              parameters: {
                strength: 0.8,
                preserveOriginal: 0.7,
                adaptiveEnhancement: true
              },
              dependencies: ['noise-reduction'],
              priority: 3,
              enabled: true,
              estimatedTime: 20,
              estimatedCost: 0.08
            },
            {
              id: 'color-restoration',
              name: 'Color Restoration',
              type: EnhancementType.COLOR_CORRECT,
              parameters: {
                strength: 0.7,
                preserveOriginal: 0.8,
                colorCorrection: true,
                saturationAdjustment: 0.2
              },
              dependencies: ['restoration'],
              priority: 4,
              enabled: true,
              estimatedTime: 3,
              estimatedCost: 0.02
            }
          ],
          autoOptimize: true,
          qualityThreshold: 0.8,
          maxCost: 0.25,
          maxTime: 60,
          parallelExecution: false,
          continueOnFailure: true
        },
        preview: 'restoration-preview.jpg',
        tags: ['restoration', 'vintage', 'repair', 'comprehensive'],
        popularity: 45,
        avgRating: 4.6,
        avgTime: 41,
        avgCost: 0.17
      }
    ];

    // Load presets into registry
    presets.forEach(preset => {
      this.presets.set(preset.id, preset);
    });

    this.emit('presets:loaded', presets.length);
  }

  private initializeQualityGates(): void {
    const gates: QualityGate[] = [
      {
        id: 'minimum-improvement',
        name: 'Minimum Quality Improvement',
        condition: (result) => (result.qualityImprovement?.overallScore || 0) >= 0.1,
        action: 'retry',
        retryCount: 2,
        threshold: 0.1
      },
      {
        id: 'cost-limit',
        name: 'Cost Limit Check',
        condition: (result) => (result.cost || 0) <= 0.5,
        action: 'abort',
        retryCount: 0,
        threshold: 0.5
      },
      {
        id: 'time-limit',
        name: 'Time Limit Check',
        condition: (result) => (result.processingTime || 0) <= 120000,
        action: 'skip',
        retryCount: 0,
        threshold: 120000
      },
      {
        id: 'success-rate',
        name: 'Success Rate Check',
        condition: (result) => result.success === true,
        action: 'retry',
        retryCount: 1,
        threshold: 1
      }
    ];

    gates.forEach(gate => {
      this.qualityGates.set(gate.id, gate);
    });
  }

  private setupEventListeners(): void {
    this.enhancementEngine.on('enhancement:started', (data) => {
      this.emit('stage:started', data);
    });

    this.enhancementEngine.on('enhancement:completed', (data) => {
      this.emit('stage:completed', data);
    });

    this.enhancementEngine.on('enhancement:failed', (data) => {
      this.emit('stage:failed', data);
    });
  }

  private startExecutionManager(): void {
    // Process execution queue
    setInterval(() => {
      this.processExecutionQueue();
    }, 1000);
  }

  private processExecutionQueue(): void {
    const runningExecutions = Array.from(this.executions.values())
      .filter(exec => exec.status === 'running').length;

    if (runningExecutions >= this.maxConcurrentExecutions) {
      return;
    }

    const pendingExecutionId = this.executionQueue.shift();
    if (pendingExecutionId) {
      const execution = this.executions.get(pendingExecutionId);
      if (execution && execution.status === 'pending') {
        this.executeInternal(execution);
      }
    }
  }

  // Main execution method
  async executePipeline(
    input: ImageData | string,
    config: PipelineConfig,
    options: {
      progressCallback?: (progress: number, stage?: string, message?: string) => void;
      enableCaching?: boolean;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ): Promise<PipelineExecution> {
    const executionId = `pipeline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const execution: PipelineExecution = {
      id: executionId,
      config,
      input,
      stages: config.stages.map(stage => ({
        stageId: stage.id,
        status: 'pending',
        cost: 0,
        time: 0
      })),
      status: 'pending',
      startTime: Date.now(),
      totalCost: 0,
      totalTime: 0,
      progress: 0
    };

    this.executions.set(executionId, execution);

    // Add to execution queue based on priority
    if (options.priority === 'high') {
      this.executionQueue.unshift(executionId);
    } else {
      this.executionQueue.push(executionId);
    }

    this.emit('execution:queued', { executionId, position: this.executionQueue.length });

    // Start execution if under concurrent limit
    setTimeout(() => this.processExecutionQueue(), 0);

    return execution;
  }

  private async executeInternal(execution: PipelineExecution): Promise<void> {
    execution.status = 'running';
    execution.startTime = Date.now();
    
    this.emit('execution:started', { executionId: execution.id });

    try {
      // Assess input quality for conditional stages
      const inputQuality = await this.enhancementEngine['qualityAssessor'].assessImageQuality(execution.input);
      
      // Filter stages based on conditions
      const enabledStages = execution.config.stages.filter(stage => 
        stage.enabled && (!stage.condition || stage.condition(execution.input, inputQuality))
      );

      // Sort stages by priority and dependencies
      const sortedStages = this.sortStagesByDependencies(enabledStages);

      let currentInput = execution.input;
      let totalCost = 0;
      let totalTime = 0;

      // Execute stages
      for (let i = 0; i < sortedStages.length; i++) {
        const stage = sortedStages[i];
        const stageResult = execution.stages.find(s => s.stageId === stage.id)!;

        // Check if we should continue
        if (execution.status === 'cancelled') {
          stageResult.status = 'skipped';
          continue;
        }

        // Check cost and time limits
        if (totalCost + stage.estimatedCost > execution.config.maxCost) {
          stageResult.status = 'skipped';
          this.emit('stage:skipped', { executionId: execution.id, stageId: stage.id, reason: 'cost-limit' });
          continue;
        }

        if (totalTime + stage.estimatedTime > execution.config.maxTime) {
          stageResult.status = 'skipped';
          this.emit('stage:skipped', { executionId: execution.id, stageId: stage.id, reason: 'time-limit' });
          continue;
        }

        // Update progress
        execution.progress = (i / sortedStages.length) * 100;
        execution.currentStage = stage.name;
        this.emit('execution:progress', { 
          executionId: execution.id, 
          progress: execution.progress, 
          stage: stage.name 
        });

        // Execute stage
        stageResult.status = 'running';
        stageResult.startTime = Date.now();
        stageResult.input = currentInput;

        try {
          const enhancementRequest: EnhancementRequest = {
            image: currentInput,
            enhancementType: stage.type,
            parameters: stage.parameters,
            options: {
              enableCaching: true,
              priorityMode: 'balanced',
              maxRetries: 2,
              timeout: stage.estimatedTime * 1000
            }
          };

          const enhancementResult = await this.enhancementEngine.enhanceImage(enhancementRequest);

          stageResult.endTime = Date.now();
          stageResult.time = stageResult.endTime - stageResult.startTime!;
          stageResult.result = enhancementResult;
          stageResult.cost = enhancementResult.cost || stage.estimatedCost;

          // Check quality gates
          const gatesPassed = await this.checkQualityGates(enhancementResult);
          if (!gatesPassed && !execution.config.continueOnFailure) {
            stageResult.status = 'failed';
            stageResult.error = 'Quality gates failed';
            execution.status = 'failed';
            execution.error = `Stage ${stage.name} failed quality gates`;
            break;
          }

          if (enhancementResult.success) {
            stageResult.status = 'completed';
            stageResult.output = enhancementResult.enhancedImage;
            stageResult.qualityImprovement = enhancementResult.qualityImprovement?.overallScore || 0;
            currentInput = enhancementResult.enhancedImage!;
          } else {
            stageResult.status = 'failed';
            stageResult.error = enhancementResult.error;
            
            if (!execution.config.continueOnFailure) {
              execution.status = 'failed';
              execution.error = `Stage ${stage.name} failed: ${enhancementResult.error}`;
              break;
            }
          }

          totalCost += stageResult.cost;
          totalTime += stageResult.time;

          this.emit('stage:completed', { 
            executionId: execution.id, 
            stageId: stage.id, 
            result: stageResult 
          });

        } catch (error) {
          stageResult.status = 'failed';
          stageResult.error = error instanceof Error ? error.message : 'Unknown error';
          stageResult.endTime = Date.now();
          stageResult.time = stageResult.endTime - stageResult.startTime!;

          this.emit('stage:failed', { 
            executionId: execution.id, 
            stageId: stage.id, 
            error: stageResult.error 
          });

          if (!execution.config.continueOnFailure) {
            execution.status = 'failed';
            execution.error = `Stage ${stage.name} failed: ${stageResult.error}`;
            break;
          }
        }
      }

      // Finalize execution
      execution.endTime = Date.now();
      execution.totalTime = execution.endTime - execution.startTime;
      execution.totalCost = totalCost;
      execution.progress = 100;

      if (execution.status === 'running') {
        execution.status = 'completed';
        execution.finalResult = {
          success: true,
          enhancedImage: currentInput,
          originalImage: execution.input,
          processingTime: execution.totalTime,
          cost: execution.totalCost,
          metadata: {
            originalQuality: inputQuality,
            enhancedQuality: await this.enhancementEngine['qualityAssessor'].assessImageQuality(currentInput),
            appliedEnhancements: sortedStages.map(s => s.type.toString()),
            processingSteps: execution.stages.map(s => ({
              step: s.stageId,
              duration: s.time,
              parameters: execution.config.stages.find(st => st.id === s.stageId)?.parameters || {},
              result: s.status
            })),
            timestamp: Date.now(),
            version: '1.0'
          }
        };
      }

      this.emit('execution:completed', { executionId: execution.id, result: execution.finalResult });
      this.updateMetrics(execution);

    } catch (error) {
      execution.status = 'failed';
      execution.error = error instanceof Error ? error.message : 'Unknown error';
      execution.endTime = Date.now();
      execution.totalTime = execution.endTime - execution.startTime;
      
      this.emit('execution:failed', { executionId: execution.id, error: execution.error });
    }
  }

  private sortStagesByDependencies(stages: PipelineStage[]): PipelineStage[] {
    const sorted: PipelineStage[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (stage: PipelineStage) => {
      if (visiting.has(stage.id)) {
        throw new Error(`Circular dependency detected: ${stage.id}`);
      }
      if (visited.has(stage.id)) {
        return;
      }

      visiting.add(stage.id);

      // Visit dependencies first
      if (stage.dependencies) {
        for (const depId of stage.dependencies) {
          const depStage = stages.find(s => s.id === depId);
          if (depStage) {
            visit(depStage);
          }
        }
      }

      visiting.delete(stage.id);
      visited.add(stage.id);
      sorted.push(stage);
    };

    // Sort by priority first, then resolve dependencies
    const prioritySorted = [...stages].sort((a, b) => a.priority - b.priority);
    for (const stage of prioritySorted) {
      visit(stage);
    }

    return sorted;
  }

  private async checkQualityGates(result: EnhancementResult): Promise<boolean> {
    for (const gate of this.qualityGates.values()) {
      if (!gate.condition(result)) {
        this.emit('quality:gate:failed', { gate: gate.id, result });
        return false;
      }
    }
    return true;
  }

  private updateMetrics(execution: PipelineExecution): void {
    this.metrics.totalExecutions++;
    
    if (execution.status === 'completed') {
      this.metrics.successRate = (this.metrics.successRate * (this.metrics.totalExecutions - 1) + 1) / this.metrics.totalExecutions;
    } else {
      this.metrics.successRate = (this.metrics.successRate * (this.metrics.totalExecutions - 1)) / this.metrics.totalExecutions;
    }

    this.metrics.averageTime = (this.metrics.averageTime * (this.metrics.totalExecutions - 1) + execution.totalTime) / this.metrics.totalExecutions;
    this.metrics.averageCost = (this.metrics.averageCost * (this.metrics.totalExecutions - 1) + execution.totalCost) / this.metrics.totalExecutions;

    // Update stage popularity
    for (const stage of execution.stages) {
      if (stage.status === 'completed') {
        const stageConfig = execution.config.stages.find(s => s.id === stage.stageId);
        if (stageConfig) {
          const existingPopular = this.metrics.popularStages.find(p => p.stage === stageConfig.type);
          if (existingPopular) {
            existingPopular.count++;
          } else {
            this.metrics.popularStages.push({ stage: stageConfig.type, count: 1 });
          }
        }
      }
    }

    // Sort popular stages
    this.metrics.popularStages.sort((a, b) => b.count - a.count);
    this.metrics.popularStages = this.metrics.popularStages.slice(0, 10);

    this.emit('metrics:updated', this.metrics);
  }

  // Public API methods

  getPresets(category?: string): PipelinePreset[] {
    const presets = Array.from(this.presets.values());
    return category ? presets.filter(p => p.category === category) : presets;
  }

  getPreset(id: string): PipelinePreset | undefined {
    return this.presets.get(id);
  }

  createCustomPreset(preset: Omit<PipelinePreset, 'id' | 'popularity' | 'avgRating' | 'avgTime' | 'avgCost'>): PipelinePreset {
    const id = `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const customPreset: PipelinePreset = {
      ...preset,
      id,
      popularity: 0,
      avgRating: 0,
      avgTime: 0,
      avgCost: 0
    };

    this.presets.set(id, customPreset);
    this.emit('preset:created', customPreset);
    return customPreset;
  }

  getExecution(id: string): PipelineExecution | undefined {
    return this.executions.get(id);
  }

  getAllExecutions(): PipelineExecution[] {
    return Array.from(this.executions.values());
  }

  getActiveExecutions(): PipelineExecution[] {
    return Array.from(this.executions.values())
      .filter(exec => exec.status === 'running' || exec.status === 'pending');
  }

  cancelExecution(id: string): boolean {
    const execution = this.executions.get(id);
    if (execution && (execution.status === 'pending' || execution.status === 'running')) {
      execution.status = 'cancelled';
      execution.endTime = Date.now();
      execution.totalTime = execution.endTime - execution.startTime;
      
      // Remove from queue if pending
      const queueIndex = this.executionQueue.indexOf(id);
      if (queueIndex > -1) {
        this.executionQueue.splice(queueIndex, 1);
      }

      this.emit('execution:cancelled', { executionId: id });
      return true;
    }
    return false;
  }

  getMetrics(): PipelineMetrics {
    return { ...this.metrics };
  }

  getQueueStatus(): { position: number; estimatedWait: number }[] {
    return this.executionQueue.map((id, index) => ({
      position: index + 1,
      estimatedWait: (index + 1) * 30 // Rough estimate
    }));
  }

  clearCompletedExecutions(): void {
    const toRemove: string[] = [];
    for (const [id, execution] of this.executions.entries()) {
      if (execution.status === 'completed' || execution.status === 'failed' || execution.status === 'cancelled') {
        toRemove.push(id);
      }
    }

    toRemove.forEach(id => this.executions.delete(id));
    this.emit('executions:cleared', toRemove.length);
  }

  dispose(): void {
    // Cancel all running executions
    for (const execution of this.executions.values()) {
      if (execution.status === 'running' || execution.status === 'pending') {
        this.cancelExecution(execution.id);
      }
    }

    this.executions.clear();
    this.executionQueue.length = 0;
    this.removeAllListeners();
  }
}