'use client';

import { EventEmitter } from 'events';
import { 
  StyleReference, 
  StyleFeatures, 
  StyleSimilarityResult,
  StyleMood 
} from './style-transfer-engine';

export interface StyleLibraryEntry extends StyleReference {
  id: string;
  features?: StyleFeatures;
  thumbnails?: StyleThumbnails;
  addedAt: number;
  usageCount: number;
  averageRating: number;
  ratings: StyleRating[];
  categories: string[];
  isPublic: boolean;
  authorId?: string;
  authorName?: string;
  downloads?: number;
  likes?: number;
}

export interface StyleThumbnails {
  small: string; // 128x128
  medium: string; // 256x256
  large: string; // 512x512
  examples?: string[]; // Example applications
}

export interface StyleRating {
  userId: string;
  rating: number; // 1-5
  comment?: string;
  timestamp: number;
}

export interface StyleSearchQuery {
  text?: string;
  tags?: string[];
  categories?: string[];
  colors?: string[];
  mood?: Partial<StyleMood>;
  artisticPeriod?: string;
  complexity?: 'low' | 'medium' | 'high';
  rating?: { min: number; max?: number };
  sortBy?: 'relevance' | 'rating' | 'usage' | 'recent' | 'downloads';
  limit?: number;
}

export interface StyleSearchResult {
  style: StyleLibraryEntry;
  relevanceScore: number;
  matchReasons: string[];
}

export interface StyleCollection {
  id: string;
  name: string;
  description: string;
  styleIds: string[];
  authorId: string;
  isPublic: boolean;
  createdAt: number;
  updatedAt: number;
  thumbnails: string[];
}

export interface ExtractedStyle {
  success: boolean;
  style?: StyleLibraryEntry;
  confidence?: number;
  error?: string;
}

export interface StyleAnalytics {
  totalStyles: number;
  publicStyles: number;
  privateStyles: number;
  topCategories: Array<{ category: string; count: number }>;
  topTags: Array<{ tag: string; count: number }>;
  averageRating: number;
  recentActivity: Array<{
    type: 'added' | 'used' | 'rated' | 'downloaded';
    styleId: string;
    timestamp: number;
    userId?: string;
  }>;
  popularStyles: StyleLibraryEntry[];
  trendingStyles: StyleLibraryEntry[];
}

export class StyleLibraryManager extends EventEmitter {
  private styles: Map<string, StyleLibraryEntry> = new Map();
  private collections: Map<string, StyleCollection> = new Map();
  private indexedFeatures: Map<string, StyleFeatures> = new Map();
  private searchIndex: Map<string, Set<string>> = new Map();
  private isInitialized: boolean = false;

  constructor() {
    super();
    this.initializeDefaultStyles();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load styles from local storage or API
      await this.loadStoredStyles();
      
      // Build search indexes
      this.rebuildSearchIndex();
      
      // Load default style collections
      await this.loadDefaultCollections();
      
      this.isInitialized = true;
      this.emit('initialized');
      
    } catch (error) {
      this.emit('initialization:failed', error);
      throw error;
    }
  }

  private initializeDefaultStyles(): void {
    const defaultStyles: StyleLibraryEntry[] = [
      {
        id: 'van-gogh-starry-night',
        type: 'preset',
        source: '/styles/presets/van-gogh-starry-night.jpg',
        name: 'Van Gogh - Starry Night',
        description: 'Classic post-impressionist style with swirling brushstrokes and vibrant colors',
        tags: ['van-gogh', 'post-impressionist', 'classic', 'swirls', 'blue', 'yellow'],
        mood: {
          energy: 'high',
          warmth: 'cool',
          mood: 'dramatic',
          era: 'classical'
        },
        artisticPeriod: 'post-impressionism',
        dominantColors: ['#1e3a8a', '#fbbf24', '#374151'],
        complexity: 'high',
        addedAt: Date.now() - 86400000, // Yesterday
        usageCount: 127,
        averageRating: 4.8,
        ratings: [],
        categories: ['classic', 'painting', 'famous'],
        isPublic: true,
        authorName: 'System',
        downloads: 1250,
        likes: 892
      },
      {
        id: 'picasso-cubist',
        type: 'preset',
        source: '/styles/presets/picasso-cubist.jpg',
        name: 'Picasso - Cubist',
        description: 'Abstract cubist style with geometric forms and multiple perspectives',
        tags: ['picasso', 'cubism', 'abstract', 'geometric', 'angular'],
        mood: {
          energy: 'medium',
          warmth: 'neutral',
          mood: 'serious',
          era: 'modern'
        },
        artisticPeriod: 'cubism',
        dominantColors: ['#8b5cf6', '#f59e0b', '#6b7280'],
        complexity: 'high',
        addedAt: Date.now() - 172800000, // 2 days ago
        usageCount: 89,
        averageRating: 4.6,
        ratings: [],
        categories: ['modern', 'abstract', 'famous'],
        isPublic: true,
        authorName: 'System',
        downloads: 756,
        likes: 623
      },
      {
        id: 'monet-impressionist',
        type: 'preset',
        source: '/styles/presets/monet-impressionist.jpg',
        name: 'Monet - Impressionist',
        description: 'Soft impressionist style with light brushstrokes and natural lighting',
        tags: ['monet', 'impressionist', 'soft', 'natural', 'light', 'pastel'],
        mood: {
          energy: 'low',
          warmth: 'warm',
          mood: 'calm',
          era: 'classical'
        },
        artisticPeriod: 'impressionism',
        dominantColors: ['#a7f3d0', '#fde68a', '#e0e7ff'],
        complexity: 'medium',
        addedAt: Date.now() - 259200000, // 3 days ago
        usageCount: 203,
        averageRating: 4.9,
        ratings: [],
        categories: ['classic', 'impressionist', 'nature'],
        isPublic: true,
        authorName: 'System',
        downloads: 1850,
        likes: 1420
      },
      {
        id: 'anime-style',
        type: 'preset',
        source: '/styles/presets/anime-style.jpg',
        name: 'Anime Style',
        description: 'Japanese anime/manga style with clean lines and vibrant colors',
        tags: ['anime', 'manga', 'japanese', 'clean', 'vibrant', 'cartoon'],
        mood: {
          energy: 'high',
          warmth: 'warm',
          mood: 'playful',
          era: 'contemporary'
        },
        artisticPeriod: 'contemporary',
        dominantColors: ['#ff6b6b', '#4ecdc4', '#45b7d1'],
        complexity: 'medium',
        addedAt: Date.now() - 432000000, // 5 days ago
        usageCount: 456,
        averageRating: 4.7,
        ratings: [],
        categories: ['anime', 'cartoon', 'contemporary'],
        isPublic: true,
        authorName: 'System',
        downloads: 3200,
        likes: 2890
      },
      {
        id: 'watercolor-soft',
        type: 'preset',
        source: '/styles/presets/watercolor-soft.jpg',
        name: 'Soft Watercolor',
        description: 'Gentle watercolor style with flowing colors and soft edges',
        tags: ['watercolor', 'soft', 'flowing', 'gentle', 'pastel', 'artistic'],
        mood: {
          energy: 'low',
          warmth: 'warm',
          mood: 'calm',
          era: 'contemporary'
        },
        artisticPeriod: 'contemporary',
        dominantColors: ['#fca5a5', '#a7f3d0', '#ddd6fe'],
        complexity: 'low',
        addedAt: Date.now() - 518400000, // 6 days ago
        usageCount: 334,
        averageRating: 4.5,
        ratings: [],
        categories: ['watercolor', 'soft', 'artistic'],
        isPublic: true,
        authorName: 'System',
        downloads: 2100,
        likes: 1680
      }
    ];

    defaultStyles.forEach(style => {
      this.styles.set(style.id, style);
    });
  }

  private async loadStoredStyles(): Promise<void> {
    try {
      // Load from localStorage or API
      const storedStyles = localStorage.getItem('styleLibrary');
      if (storedStyles) {
        const parsed = JSON.parse(storedStyles);
        parsed.forEach((style: StyleLibraryEntry) => {
          this.styles.set(style.id, style);
        });
      }
    } catch (error) {
      console.warn('Failed to load stored styles:', error);
    }
  }

  private async loadDefaultCollections(): Promise<void> {
    const defaultCollections: StyleCollection[] = [
      {
        id: 'classic-masters',
        name: 'Classic Masters',
        description: 'Timeless styles from famous artists',
        styleIds: ['van-gogh-starry-night', 'picasso-cubist', 'monet-impressionist'],
        authorId: 'system',
        isPublic: true,
        createdAt: Date.now() - 86400000,
        updatedAt: Date.now() - 86400000,
        thumbnails: [
          '/styles/presets/van-gogh-starry-night.jpg',
          '/styles/presets/picasso-cubist.jpg',
          '/styles/presets/monet-impressionist.jpg'
        ]
      },
      {
        id: 'contemporary-styles',
        name: 'Contemporary Styles',
        description: 'Modern and contemporary artistic styles',
        styleIds: ['anime-style', 'watercolor-soft'],
        authorId: 'system',
        isPublic: true,
        createdAt: Date.now() - 172800000,
        updatedAt: Date.now() - 172800000,
        thumbnails: [
          '/styles/presets/anime-style.jpg',
          '/styles/presets/watercolor-soft.jpg'
        ]
      }
    ];

    defaultCollections.forEach(collection => {
      this.collections.set(collection.id, collection);
    });
  }

  private rebuildSearchIndex(): void {
    this.searchIndex.clear();
    
    for (const [styleId, style] of this.styles) {
      // Index by name
      this.addToSearchIndex('name', style.name.toLowerCase(), styleId);
      
      // Index by tags
      style.tags.forEach(tag => {
        this.addToSearchIndex('tag', tag.toLowerCase(), styleId);
      });
      
      // Index by categories
      style.categories.forEach(category => {
        this.addToSearchIndex('category', category.toLowerCase(), styleId);
      });
      
      // Index by description
      if (style.description) {
        const words = style.description.toLowerCase().split(/\s+/);
        words.forEach(word => {
          if (word.length > 2) { // Skip very short words
            this.addToSearchIndex('description', word, styleId);
          }
        });
      }
      
      // Index by artistic period
      if (style.artisticPeriod) {
        this.addToSearchIndex('period', style.artisticPeriod.toLowerCase(), styleId);
      }
      
      // Index by mood
      this.addToSearchIndex('mood', style.mood.mood.toLowerCase(), styleId);
      this.addToSearchIndex('era', style.mood.era.toLowerCase(), styleId);
      this.addToSearchIndex('energy', style.mood.energy.toLowerCase(), styleId);
      this.addToSearchIndex('warmth', style.mood.warmth.toLowerCase(), styleId);
    }
  }

  private addToSearchIndex(type: string, term: string, styleId: string): void {
    const key = `${type}:${term}`;
    if (!this.searchIndex.has(key)) {
      this.searchIndex.set(key, new Set());
    }
    this.searchIndex.get(key)!.add(styleId);
  }

  // Main API methods

  async addStyle(style: StyleReference, options?: {
    extractFeatures?: boolean;
    generateThumbnails?: boolean;
    isPublic?: boolean;
    categories?: string[];
  }): Promise<string> {
    const styleId = `style_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const entry: StyleLibraryEntry = {
      ...style,
      id: styleId,
      addedAt: Date.now(),
      usageCount: 0,
      averageRating: 0,
      ratings: [],
      categories: options?.categories || ['custom'],
      isPublic: options?.isPublic || false
    };

    // Extract features if requested
    if (options?.extractFeatures !== false) {
      try {
        entry.features = await this.extractStyleFeatures(style);
        this.indexedFeatures.set(styleId, entry.features);
      } catch (error) {
        console.warn('Failed to extract features for style:', error);
      }
    }

    // Generate thumbnails if requested
    if (options?.generateThumbnails !== false) {
      try {
        entry.thumbnails = await this.generateStyleThumbnails(style);
      } catch (error) {
        console.warn('Failed to generate thumbnails for style:', error);
      }
    }

    this.styles.set(styleId, entry);
    this.updateSearchIndexForStyle(entry);
    
    // Persist to storage
    await this.saveToStorage();
    
    this.emit('style:added', entry);
    return styleId;
  }

  private updateSearchIndexForStyle(style: StyleLibraryEntry): void {
    // Add to search index
    this.addToSearchIndex('name', style.name.toLowerCase(), style.id);
    style.tags.forEach(tag => {
      this.addToSearchIndex('tag', tag.toLowerCase(), style.id);
    });
    style.categories.forEach(category => {
      this.addToSearchIndex('category', category.toLowerCase(), style.id);
    });
    
    if (style.description) {
      const words = style.description.toLowerCase().split(/\s+/);
      words.forEach(word => {
        if (word.length > 2) {
          this.addToSearchIndex('description', word, style.id);
        }
      });
    }
  }

  async extractStyleFromImage(image: ImageData | HTMLImageElement | string): Promise<ExtractedStyle> {
    try {
      // Convert image to consistent format
      const imageData = await this.prepareImage(image);
      
      // Extract style features
      const features = await this.extractStyleFeatures({
        type: 'image',
        source: imageData,
        tags: [],
        mood: {
          energy: 'medium',
          warmth: 'neutral',
          mood: 'serious',
          era: 'contemporary'
        },
        dominantColors: []
      });

      // Analyze image to determine style characteristics
      const analysis = await this.analyzeImageForStyle(imageData);
      
      // Create style entry
      const style: StyleLibraryEntry = {
        id: `extracted_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'custom',
        source: imageData,
        name: `Extracted Style ${new Date().toLocaleDateString()}`,
        description: `Style extracted from uploaded image`,
        tags: this.generateStyleTags(analysis),
        mood: this.inferMoodFromAnalysis(analysis),
        dominantColors: analysis.dominantColors,
        complexity: analysis.complexity,
        features,
        addedAt: Date.now(),
        usageCount: 0,
        averageRating: 0,
        ratings: [],
        categories: ['extracted', 'custom'],
        isPublic: false
      };

      return {
        success: true,
        style,
        confidence: analysis.confidence
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Style extraction failed'
      };
    }
  }

  private async prepareImage(image: ImageData | HTMLImageElement | string): Promise<ImageData> {
    if (image instanceof ImageData) {
      return image;
    }
    
    if (typeof image === 'string') {
      return this.loadImageFromString(image);
    }
    
    if (image instanceof HTMLImageElement) {
      return this.convertHTMLImageToImageData(image);
    }
    
    throw new Error('Unsupported image format');
  }

  private async loadImageFromString(src: string): Promise<ImageData> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        resolve(this.convertHTMLImageToImageData(img));
      };
      
      img.onerror = () => {
        reject(new Error('Failed to load image from string'));
      };
      
      img.src = src;
    });
  }

  private convertHTMLImageToImageData(img: HTMLImageElement): ImageData {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);
    
    return ctx.getImageData(0, 0, canvas.width, canvas.height);
  }

  private async extractStyleFeatures(style: StyleReference): Promise<StyleFeatures> {
    // Simplified feature extraction - in production would use advanced AI
    const colorFeatures = await this.analyzeColorPalette(style);
    const textureFeatures = await this.analyzeTexture(style);
    const compositionFeatures = await this.analyzeComposition(style);
    const semanticFeatures = await this.analyzeSemanticContent(style);
    
    return {
      color: colorFeatures,
      texture: textureFeatures,
      composition: compositionFeatures,
      semantic: semanticFeatures,
      metadata: {
        extractedAt: Date.now(),
        analysisVersion: '2.0'
      }
    };
  }

  private async analyzeColorPalette(style: StyleReference): Promise<any> {
    // Simplified color analysis
    return {
      palette: style.dominantColors,
      harmony: 'complementary',
      saturation: 0.7,
      brightness: 0.6,
      contrast: 0.8,
      temperature: 0.2
    };
  }

  private async analyzeTexture(style: StyleReference): Promise<any> {
    // Simplified texture analysis
    return {
      roughness: 0.5,
      patterns: ['brushstroke', 'organic'],
      directionality: 0.3,
      regularity: 0.4,
      contrast: 0.6
    };
  }

  private async analyzeComposition(style: StyleReference): Promise<any> {
    // Simplified composition analysis
    return {
      balance: 'asymmetrical',
      focal_points: [{ x: 0.6, y: 0.4, strength: 0.8 }],
      rule_of_thirds: true,
      leading_lines: 2,
      depth: 'deep'
    };
  }

  private async analyzeSemanticContent(style: StyleReference): Promise<any> {
    // Simplified semantic analysis
    return {
      subjects: style.tags.filter(tag => !['color', 'texture', 'style'].includes(tag)),
      mood: Object.values(style.mood),
      style_period: style.artisticPeriod || 'contemporary',
      artistic_movement: style.artisticPeriod || 'modern',
      complexity: 0.7
    };
  }

  private async generateStyleThumbnails(style: StyleReference): Promise<StyleThumbnails> {
    // In production, this would generate actual thumbnails from the style image
    const placeholder = '/placeholder-style-thumbnail.jpg';
    
    return {
      small: placeholder,
      medium: placeholder,
      large: placeholder,
      examples: [placeholder, placeholder]
    };
  }

  private async analyzeImageForStyle(imageData: ImageData): Promise<any> {
    // Simplified image analysis for style extraction
    const colors = this.extractDominantColors(imageData);
    
    return {
      dominantColors: colors,
      complexity: colors.length > 5 ? 'high' : colors.length > 3 ? 'medium' : 'low',
      confidence: 0.8,
      textureType: 'smooth',
      edgeStrength: 0.6
    };
  }

  private extractDominantColors(imageData: ImageData): string[] {
    // Simplified color extraction
    const data = imageData.data;
    const colorMap = new Map<string, number>();
    
    // Sample every 10th pixel for performance
    for (let i = 0; i < data.length; i += 40) {
      const r = Math.round(data[i] / 32) * 32;
      const g = Math.round(data[i + 1] / 32) * 32;
      const b = Math.round(data[i + 2] / 32) * 32;
      
      const color = `rgb(${r}, ${g}, ${b})`;
      colorMap.set(color, (colorMap.get(color) || 0) + 1);
    }
    
    // Return top 5 colors
    return Array.from(colorMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([color]) => color);
  }

  private generateStyleTags(analysis: any): string[] {
    const tags = ['extracted'];
    
    if (analysis.complexity === 'high') tags.push('complex');
    if (analysis.complexity === 'low') tags.push('simple');
    if (analysis.textureType) tags.push(analysis.textureType);
    
    return tags;
  }

  private inferMoodFromAnalysis(analysis: any): StyleMood {
    return {
      energy: analysis.complexity === 'high' ? 'high' : 'medium',
      warmth: 'neutral',
      mood: 'serious',
      era: 'contemporary'
    };
  }

  // Search and retrieval methods

  async searchStyles(query: StyleSearchQuery): Promise<StyleSearchResult[]> {
    const results: StyleSearchResult[] = [];
    const candidateIds = new Set<string>();

    // Text search
    if (query.text) {
      const textResults = this.searchByText(query.text);
      textResults.forEach(id => candidateIds.add(id));
    }

    // Tag search
    if (query.tags && query.tags.length > 0) {
      const tagResults = this.searchByTags(query.tags);
      tagResults.forEach(id => candidateIds.add(id));
    }

    // Category search
    if (query.categories && query.categories.length > 0) {
      const categoryResults = this.searchByCategories(query.categories);
      categoryResults.forEach(id => candidateIds.add(id));
    }

    // If no specific search criteria, get all styles
    if (!query.text && (!query.tags || query.tags.length === 0) && 
        (!query.categories || query.categories.length === 0)) {
      this.styles.forEach((_, id) => candidateIds.add(id));
    }

    // Score and filter candidates
    for (const styleId of candidateIds) {
      const style = this.styles.get(styleId);
      if (!style) continue;

      const score = this.calculateRelevanceScore(style, query);
      if (score.score > 0) {
        results.push({
          style,
          relevanceScore: score.score,
          matchReasons: score.reasons
        });
      }
    }

    // Sort results
    results.sort((a, b) => {
      switch (query.sortBy) {
        case 'rating': return b.style.averageRating - a.style.averageRating;
        case 'usage': return b.style.usageCount - a.style.usageCount;
        case 'recent': return b.style.addedAt - a.style.addedAt;
        case 'downloads': return (b.style.downloads || 0) - (a.style.downloads || 0);
        default: return b.relevanceScore - a.relevanceScore;
      }
    });

    // Apply limit
    return results.slice(0, query.limit || 50);
  }

  private searchByText(text: string): string[] {
    const terms = text.toLowerCase().split(/\s+/);
    const results = new Set<string>();

    terms.forEach(term => {
      // Search in names
      const nameKey = `name:${term}`;
      const nameResults = this.searchIndex.get(nameKey);
      if (nameResults) {
        nameResults.forEach(id => results.add(id));
      }

      // Search in descriptions
      const descKey = `description:${term}`;
      const descResults = this.searchIndex.get(descKey);
      if (descResults) {
        descResults.forEach(id => results.add(id));
      }

      // Partial matches
      for (const [key, ids] of this.searchIndex) {
        if (key.includes(term)) {
          ids.forEach(id => results.add(id));
        }
      }
    });

    return Array.from(results);
  }

  private searchByTags(tags: string[]): string[] {
    const results = new Set<string>();

    tags.forEach(tag => {
      const key = `tag:${tag.toLowerCase()}`;
      const tagResults = this.searchIndex.get(key);
      if (tagResults) {
        tagResults.forEach(id => results.add(id));
      }
    });

    return Array.from(results);
  }

  private searchByCategories(categories: string[]): string[] {
    const results = new Set<string>();

    categories.forEach(category => {
      const key = `category:${category.toLowerCase()}`;
      const categoryResults = this.searchIndex.get(key);
      if (categoryResults) {
        categoryResults.forEach(id => results.add(id));
      }
    });

    return Array.from(results);
  }

  private calculateRelevanceScore(style: StyleLibraryEntry, query: StyleSearchQuery): { score: number; reasons: string[] } {
    let score = 0;
    const reasons: string[] = [];

    // Text relevance
    if (query.text) {
      const textLower = query.text.toLowerCase();
      if (style.name.toLowerCase().includes(textLower)) {
        score += 3;
        reasons.push('Title match');
      }
      if (style.description?.toLowerCase().includes(textLower)) {
        score += 2;
        reasons.push('Description match');
      }
      if (style.tags.some(tag => tag.toLowerCase().includes(textLower))) {
        score += 1;
        reasons.push('Tag match');
      }
    }

    // Tag relevance
    if (query.tags && query.tags.length > 0) {
      const matchingTags = style.tags.filter(tag => 
        query.tags!.some(queryTag => tag.toLowerCase().includes(queryTag.toLowerCase()))
      );
      score += matchingTags.length;
      if (matchingTags.length > 0) {
        reasons.push(`${matchingTags.length} tag matches`);
      }
    }

    // Category relevance
    if (query.categories && query.categories.length > 0) {
      const matchingCategories = style.categories.filter(cat => 
        query.categories!.some(queryCat => cat.toLowerCase().includes(queryCat.toLowerCase()))
      );
      score += matchingCategories.length * 2;
      if (matchingCategories.length > 0) {
        reasons.push(`${matchingCategories.length} category matches`);
      }
    }

    // Mood relevance
    if (query.mood) {
      let moodMatches = 0;
      if (query.mood.energy && style.mood.energy === query.mood.energy) {
        moodMatches++;
      }
      if (query.mood.warmth && style.mood.warmth === query.mood.warmth) {
        moodMatches++;
      }
      if (query.mood.mood && style.mood.mood === query.mood.mood) {
        moodMatches++;
      }
      if (query.mood.era && style.mood.era === query.mood.era) {
        moodMatches++;
      }
      
      score += moodMatches * 0.5;
      if (moodMatches > 0) {
        reasons.push(`${moodMatches} mood matches`);
      }
    }

    // Rating filter
    if (query.rating && style.averageRating < query.rating.min) {
      score = 0; // Exclude low-rated styles
    }

    // Complexity filter
    if (query.complexity && style.complexity !== query.complexity) {
      score *= 0.5; // Reduce score for complexity mismatch
    }

    // Boost popular styles slightly
    score += (style.usageCount / 100) * 0.1;
    score += style.averageRating * 0.1;

    return { score, reasons };
  }

  async findSimilarStyles(
    referenceStyle: StyleReference | string,
    limit: number = 10
  ): Promise<StyleSimilarityResult[]> {
    let style: StyleLibraryEntry;
    
    if (typeof referenceStyle === 'string') {
      const found = this.styles.get(referenceStyle);
      if (!found) {
        throw new Error('Reference style not found');
      }
      style = found;
    } else {
      // Create temporary style entry for comparison
      style = {
        ...referenceStyle,
        id: 'temp',
        addedAt: Date.now(),
        usageCount: 0,
        averageRating: 0,
        ratings: [],
        categories: [],
        isPublic: false
      } as StyleLibraryEntry;
    }

    const similarities: StyleSimilarityResult[] = [];
    
    for (const [id, candidate] of this.styles) {
      if (id === style.id) continue; // Skip self
      
      const similarity = this.calculateStyleSimilarity(style, candidate);
      
      if (similarity.similarity > 0.3) { // Minimum similarity threshold
        similarities.push(similarity);
      }
    }
    
    // Sort by similarity and return top results
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit);
  }

  private calculateStyleSimilarity(
    styleA: StyleLibraryEntry,
    styleB: StyleLibraryEntry
  ): StyleSimilarityResult {
    let totalSimilarity = 0;
    let weightSum = 0;
    const matchingFeatures: string[] = [];
    const differences: string[] = [];

    // Tag similarity (30%)
    const tagSimilarity = this.calculateTagSimilarity(styleA.tags, styleB.tags);
    totalSimilarity += tagSimilarity * 0.3;
    weightSum += 0.3;
    if (tagSimilarity > 0.5) {
      matchingFeatures.push(`Similar tags: ${this.getCommonTags(styleA.tags, styleB.tags).join(', ')}`);
    }

    // Mood similarity (25%)
    const moodSimilarity = this.calculateMoodSimilarity(styleA.mood, styleB.mood);
    totalSimilarity += moodSimilarity * 0.25;
    weightSum += 0.25;
    if (moodSimilarity > 0.7) {
      matchingFeatures.push('Similar mood and energy');
    }

    // Color similarity (25%)
    const colorSimilarity = this.calculateColorSimilarity(styleA.dominantColors, styleB.dominantColors);
    totalSimilarity += colorSimilarity * 0.25;
    weightSum += 0.25;
    if (colorSimilarity > 0.6) {
      matchingFeatures.push('Similar color palette');
    }

    // Artistic period similarity (20%)
    const periodSimilarity = styleA.artisticPeriod === styleB.artisticPeriod ? 1 : 0;
    totalSimilarity += periodSimilarity * 0.2;
    weightSum += 0.2;
    if (periodSimilarity > 0) {
      matchingFeatures.push(`Same artistic period: ${styleA.artisticPeriod}`);
    }

    // Generate differences
    if (tagSimilarity < 0.3) {
      differences.push('Different style themes');
    }
    if (moodSimilarity < 0.5) {
      differences.push('Different mood and energy');
    }
    if (colorSimilarity < 0.4) {
      differences.push('Different color schemes');
    }

    const finalSimilarity = weightSum > 0 ? totalSimilarity / weightSum : 0;

    return {
      style: styleB,
      similarity: finalSimilarity,
      matchingFeatures,
      differences
    };
  }

  private calculateTagSimilarity(tagsA: string[], tagsB: string[]): number {
    if (tagsA.length === 0 && tagsB.length === 0) return 1;
    if (tagsA.length === 0 || tagsB.length === 0) return 0;

    const setA = new Set(tagsA.map(tag => tag.toLowerCase()));
    const setB = new Set(tagsB.map(tag => tag.toLowerCase()));
    
    const intersection = new Set([...setA].filter(tag => setB.has(tag)));
    const union = new Set([...setA, ...setB]);
    
    return intersection.size / union.size; // Jaccard similarity
  }

  private calculateMoodSimilarity(moodA: StyleMood, moodB: StyleMood): number {
    let matches = 0;
    let total = 0;
    
    if (moodA.energy === moodB.energy) matches++;
    total++;
    
    if (moodA.warmth === moodB.warmth) matches++;
    total++;
    
    if (moodA.mood === moodB.mood) matches++;
    total++;
    
    if (moodA.era === moodB.era) matches++;
    total++;
    
    return matches / total;
  }

  private calculateColorSimilarity(colorsA: string[], colorsB: string[]): number {
    if (colorsA.length === 0 && colorsB.length === 0) return 1;
    if (colorsA.length === 0 || colorsB.length === 0) return 0;

    // Simple color similarity based on common colors
    const setA = new Set(colorsA);
    const setB = new Set(colorsB);
    
    const intersection = new Set([...setA].filter(color => setB.has(color)));
    const union = new Set([...setA, ...setB]);
    
    return intersection.size / union.size;
  }

  private getCommonTags(tagsA: string[], tagsB: string[]): string[] {
    const setA = new Set(tagsA.map(tag => tag.toLowerCase()));
    const setB = new Set(tagsB.map(tag => tag.toLowerCase()));
    
    return [...setA].filter(tag => setB.has(tag));
  }

  // Collection management

  async createCollection(
    name: string,
    description: string,
    styleIds: string[],
    isPublic: boolean = false
  ): Promise<string> {
    const collectionId = `collection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Validate style IDs
    const validStyleIds = styleIds.filter(id => this.styles.has(id));
    
    const collection: StyleCollection = {
      id: collectionId,
      name,
      description,
      styleIds: validStyleIds,
      authorId: 'current-user', // Would come from auth
      isPublic,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      thumbnails: validStyleIds.slice(0, 3).map(id => {
        const style = this.styles.get(id);
        return style?.thumbnails?.medium || '/placeholder-style-thumbnail.jpg';
      })
    };

    this.collections.set(collectionId, collection);
    await this.saveToStorage();
    
    this.emit('collection:created', collection);
    return collectionId;
  }

  getCollections(): StyleCollection[] {
    return Array.from(this.collections.values());
  }

  getCollection(collectionId: string): StyleCollection | undefined {
    return this.collections.get(collectionId);
  }

  // Analytics and insights

  getAnalytics(): StyleAnalytics {
    const styles = Array.from(this.styles.values());
    
    // Category analysis
    const categoryCount = new Map<string, number>();
    styles.forEach(style => {
      style.categories.forEach(category => {
        categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
      });
    });
    
    // Tag analysis
    const tagCount = new Map<string, number>();
    styles.forEach(style => {
      style.tags.forEach(tag => {
        tagCount.set(tag, (tagCount.get(tag) || 0) + 1);
      });
    });

    // Popular and trending styles
    const popularStyles = styles
      .filter(s => s.isPublic)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 10);
    
    const recentStyles = styles
      .sort((a, b) => b.addedAt - a.addedAt)
      .slice(0, 20);
    
    const trendingStyles = recentStyles
      .filter(s => s.usageCount > 0)
      .sort((a, b) => (b.usageCount / Math.max(1, Date.now() - b.addedAt)) - 
                      (a.usageCount / Math.max(1, Date.now() - a.addedAt)))
      .slice(0, 10);

    return {
      totalStyles: styles.length,
      publicStyles: styles.filter(s => s.isPublic).length,
      privateStyles: styles.filter(s => !s.isPublic).length,
      topCategories: Array.from(categoryCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([category, count]) => ({ category, count })),
      topTags: Array.from(tagCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 15)
        .map(([tag, count]) => ({ tag, count })),
      averageRating: styles.reduce((sum, s) => sum + s.averageRating, 0) / styles.length,
      recentActivity: [], // Would be populated from activity log
      popularStyles,
      trendingStyles
    };
  }

  // Utility methods

  getStyleById(styleId: string): StyleLibraryEntry | undefined {
    return this.styles.get(styleId);
  }

  getAllStyles(): StyleLibraryEntry[] {
    return Array.from(this.styles.values());
  }

  getPublicStyles(): StyleLibraryEntry[] {
    return Array.from(this.styles.values()).filter(style => style.isPublic);
  }

  async rateStyle(styleId: string, rating: number, comment?: string): Promise<void> {
    const style = this.styles.get(styleId);
    if (!style) {
      throw new Error('Style not found');
    }

    const userId = 'current-user'; // Would come from auth
    
    // Remove existing rating from this user
    style.ratings = style.ratings.filter(r => r.userId !== userId);
    
    // Add new rating
    style.ratings.push({
      userId,
      rating,
      comment,
      timestamp: Date.now()
    });

    // Recalculate average rating
    style.averageRating = style.ratings.reduce((sum, r) => sum + r.rating, 0) / style.ratings.length;

    await this.saveToStorage();
    this.emit('style:rated', { styleId, rating, comment });
  }

  async incrementUsage(styleId: string): Promise<void> {
    const style = this.styles.get(styleId);
    if (style) {
      style.usageCount++;
      await this.saveToStorage();
      this.emit('style:used', { styleId });
    }
  }

  async deleteStyle(styleId: string): Promise<boolean> {
    const deleted = this.styles.delete(styleId);
    if (deleted) {
      this.indexedFeatures.delete(styleId);
      this.rebuildSearchIndex(); // Rebuild to remove references
      await this.saveToStorage();
      this.emit('style:deleted', { styleId });
    }
    return deleted;
  }

  private async saveToStorage(): Promise<void> {
    try {
      const stylesToSave = Array.from(this.styles.values())
        .filter(style => !style.isPublic || style.authorName !== 'System'); // Don't save system styles
      
      localStorage.setItem('styleLibrary', JSON.stringify(stylesToSave));
      
      const collectionsToSave = Array.from(this.collections.values())
        .filter(collection => collection.authorId !== 'system');
      
      localStorage.setItem('styleCollections', JSON.stringify(collectionsToSave));
    } catch (error) {
      console.warn('Failed to save to storage:', error);
    }
  }

  // Cleanup
  dispose(): void {
    this.styles.clear();
    this.collections.clear();
    this.indexedFeatures.clear();
    this.searchIndex.clear();
    this.removeAllListeners();
  }
}