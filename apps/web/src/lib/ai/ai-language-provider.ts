'use client';

import * as monaco from 'monaco-editor';
import { 
  LanguageServiceProvider,
  languageServiceManager 
} from '../language-providers';
import { aiServiceManager } from './ai-service-manager';
import { CompletionItem, CodeSuggestion, AnalysisResult } from './ai-service-base';
import { debounce } from 'throttle-debounce';

export class AIEnhancedLanguageProvider implements LanguageServiceProvider {
  private baseProvider: LanguageServiceProvider;
  private debouncedAnalysis: any;
  private analysisCache: Map<string, AnalysisResult> = new Map();

  constructor(baseProvider: LanguageServiceProvider) {
    this.baseProvider = baseProvider;
    
    // Debounce analysis to avoid too frequent AI calls
    this.debouncedAnalysis = debounce(1000, this.performAIAnalysis.bind(this));
  }

  // Enhanced diagnostics with AI analysis
  async provideDiagnostics(model: monaco.editor.ITextModel): Promise<monaco.editor.IMarkerData[]> {
    // Get base diagnostics first
    const baseMarkers = await this.baseProvider.provideDiagnostics(model);
    
    // Trigger AI analysis (debounced)
    this.debouncedAnalysis(model);
    
    // Check if we have cached AI analysis
    const cacheKey = this.getModelCacheKey(model);
    const aiAnalysis = this.analysisCache.get(cacheKey);
    
    if (aiAnalysis) {
      const aiMarkers = this.convertAIAnalysisToMarkers(aiAnalysis);
      return [...baseMarkers, ...aiMarkers];
    }
    
    return baseMarkers;
  }

  private async performAIAnalysis(model: monaco.editor.ITextModel): Promise<void> {
    try {
      const code = model.getValue();
      const language = model.getLanguageId();
      const fileName = model.uri.path;
      
      const analysis = await aiServiceManager.performCodeAnalysis(code, language, fileName);
      
      if (analysis) {
        const cacheKey = this.getModelCacheKey(model);
        this.analysisCache.set(cacheKey, analysis);
        
        // Trigger diagnostics update
        monaco.editor.setModelMarkers(
          model,
          'ai-analysis',
          this.convertAIAnalysisToMarkers(analysis)
        );
      }
    } catch (error) {
      console.error('AI analysis failed:', error);
    }
  }

  private convertAIAnalysisToMarkers(analysis: AnalysisResult): monaco.editor.IMarkerData[] {
    return analysis.issues.map(issue => ({
      severity: this.mapSeverityToMonaco(issue.severity),
      message: issue.message,
      startLineNumber: issue.line,
      startColumn: issue.column,
      endLineNumber: issue.endLine || issue.line,
      endColumn: issue.endColumn || issue.column + 1,
      source: 'AI Analysis',
      code: issue.rule,
    }));
  }

  private mapSeverityToMonaco(severity: string): monaco.MarkerSeverity {
    switch (severity) {
      case 'critical':
      case 'high':
        return monaco.MarkerSeverity.Error;
      case 'medium':
        return monaco.MarkerSeverity.Warning;
      case 'low':
      default:
        return monaco.MarkerSeverity.Info;
    }
  }

  private getModelCacheKey(model: monaco.editor.ITextModel): string {
    return `${model.uri.toString()}-${model.getVersionId()}`;
  }

  // Enhanced completion with AI suggestions
  async provideCompletionItems(
    model: monaco.editor.ITextModel, 
    position: monaco.Position
  ): Promise<monaco.languages.CompletionItem[]> {
    // Get base completions
    const baseCompletions = await this.baseProvider.provideCompletionItems(model, position);
    
    try {
      // Get AI-powered completions
      const code = model.getValue();
      const language = model.getLanguageId();
      const fileName = model.uri.path;
      
      const aiCompletions = await aiServiceManager.getSmartCompletions(
        code,
        { line: position.lineNumber, column: position.column },
        language,
        fileName
      );
      
      // Convert AI completions to Monaco format
      const monacoAICompletions = this.convertAICompletionsToMonaco(aiCompletions, position);
      
      // Merge and prioritize completions
      return this.mergeCompletions(baseCompletions, monacoAICompletions);
    } catch (error) {
      console.error('AI completion failed:', error);
      return baseCompletions;
    }
  }

  private convertAICompletionsToMonaco(
    aiCompletions: CompletionItem[],
    position: monaco.Position
  ): monaco.languages.CompletionItem[] {
    return aiCompletions.map((item, index) => ({
      label: item.label,
      kind: this.mapCompletionKindToMonaco(item.kind),
      insertText: item.insertText,
      detail: item.detail,
      documentation: item.documentation ? { value: item.documentation } : undefined,
      sortText: `ai-${String(item.priority || 5).padStart(2, '0')}-${String(index).padStart(3, '0')}`,
      filterText: item.label,
      range: {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: position.column,
        endColumn: position.column,
      },
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      tags: [monaco.languages.CompletionItemTag.Deprecated], // Mark as AI-generated
    }));
  }

  private mapCompletionKindToMonaco(kind: string): monaco.languages.CompletionItemKind {
    switch (kind) {
      case 'function':
        return monaco.languages.CompletionItemKind.Function;
      case 'variable':
        return monaco.languages.CompletionItemKind.Variable;
      case 'class':
        return monaco.languages.CompletionItemKind.Class;
      case 'interface':
        return monaco.languages.CompletionItemKind.Interface;
      case 'module':
        return monaco.languages.CompletionItemKind.Module;
      case 'property':
        return monaco.languages.CompletionItemKind.Property;
      case 'method':
        return monaco.languages.CompletionItemKind.Method;
      case 'snippet':
        return monaco.languages.CompletionItemKind.Snippet;
      default:
        return monaco.languages.CompletionItemKind.Text;
    }
  }

  private mergeCompletions(
    baseCompletions: monaco.languages.CompletionItem[],
    aiCompletions: monaco.languages.CompletionItem[]
  ): monaco.languages.CompletionItem[] {
    // Add base completions with lower priority
    const prioritizedBase = baseCompletions.map((item, index) => ({
      ...item,
      sortText: `base-${String(index).padStart(3, '0')}`,
    }));
    
    // Combine and sort by priority (AI first, then base)
    return [...aiCompletions, ...prioritizedBase];
  }

  // Enhanced hover with AI insights
  async provideHover(
    model: monaco.editor.ITextModel, 
    position: monaco.Position
  ): Promise<monaco.languages.Hover | null> {
    // Get base hover first
    const baseHover = await this.baseProvider.provideHover(model, position);
    
    // TODO: Add AI-enhanced hover information
    // For now, just return base hover
    return baseHover;
  }

  // Delegate other methods to base provider
  async provideSignatureHelp(
    model: monaco.editor.ITextModel, 
    position: monaco.Position
  ): Promise<monaco.languages.SignatureHelp | null> {
    return this.baseProvider.provideSignatureHelp(model, position);
  }

  async provideDefinition(
    model: monaco.editor.ITextModel, 
    position: monaco.Position
  ): Promise<monaco.languages.Location[]> {
    return this.baseProvider.provideDefinition(model, position);
  }

  async provideReferences(
    model: monaco.editor.ITextModel, 
    position: monaco.Position
  ): Promise<monaco.languages.Location[]> {
    return this.baseProvider.provideReferences(model, position);
  }

  async provideRename(
    model: monaco.editor.ITextModel, 
    position: monaco.Position
  ): Promise<monaco.languages.WorkspaceEdit | null> {
    return this.baseProvider.provideRename(model, position);
  }

  async provideDocumentSymbols(
    model: monaco.editor.ITextModel
  ): Promise<monaco.languages.DocumentSymbol[]> {
    return this.baseProvider.provideDocumentSymbols(model);
  }

  async provideColorPresentations(
    model: monaco.editor.ITextModel
  ): Promise<monaco.languages.IColorPresentation[]> {
    return this.baseProvider.provideColorPresentations(model);
  }

  // Enhanced code actions with AI suggestions
  async provideCodeActions(
    model: monaco.editor.ITextModel, 
    range: monaco.Range
  ): Promise<monaco.languages.CodeAction[]> {
    // Get base code actions
    const baseActions = await this.baseProvider.provideCodeActions(model, range);
    
    try {
      // Get AI suggestions for the selected code
      const selectedText = model.getValueInRange(range);
      const code = model.getValue();
      const language = model.getLanguageId();
      const fileName = model.uri.path;
      
      const suggestions = await aiServiceManager.getIntelligentSuggestions(
        code,
        selectedText,
        language,
        fileName
      );
      
      // Convert AI suggestions to code actions
      const aiActions = this.convertSuggestionsToCodeActions(suggestions, model, range);
      
      return [...baseActions, ...aiActions];
    } catch (error) {
      console.error('AI code actions failed:', error);
      return baseActions;
    }
  }

  private convertSuggestionsToCodeActions(
    suggestions: CodeSuggestion[],
    model: monaco.editor.ITextModel,
    range: monaco.Range
  ): monaco.languages.CodeAction[] {
    return suggestions.map(suggestion => ({
      title: `AI: ${suggestion.title}`,
      kind: this.mapSuggestionTypeToCodeActionKind(suggestion.type),
      edit: {
        edits: suggestion.codeChanges.map(change => ({
          resource: model.uri,
          textEdit: {
            range: new monaco.Range(
              change.startLine,
              1,
              change.endLine,
              Number.MAX_SAFE_INTEGER
            ),
            text: change.suggestedCode,
          },
        })),
      },
      diagnostics: [],
      isPreferred: suggestion.confidence > 0.8,
    }));
  }

  private mapSuggestionTypeToCodeActionKind(type: string): string {
    switch (type) {
      case 'refactor':
        return 'refactor';
      case 'optimize':
      case 'performance':
        return 'refactor.optimize';
      case 'fix':
        return 'quickfix';
      case 'security':
        return 'quickfix.security';
      case 'improve':
      default:
        return 'refactor.improve';
    }
  }

  async provideFormattingEdits(
    model: monaco.editor.ITextModel, 
    options: monaco.languages.FormattingOptions
  ): Promise<monaco.editor.ITextEdit[]> {
    return this.baseProvider.provideFormattingEdits(model, options);
  }
}

// Enhanced Language Service Manager with AI integration
export class AIEnhancedLanguageServiceManager {
  private aiProviders: Map<string, AIEnhancedLanguageProvider> = new Map();
  private registeredLanguages: Set<string> = new Set();

  registerAIEnhancedLanguage(languageId: string): void {
    if (this.registeredLanguages.has(languageId)) return;

    // Get base provider from existing language service manager
    const baseProvider = this.getBaseProvider(languageId);
    if (!baseProvider) {
      console.warn(`No base provider found for language: ${languageId}`);
      return;
    }

    // Create AI-enhanced provider
    const aiProvider = new AIEnhancedLanguageProvider(baseProvider);
    this.aiProviders.set(languageId, aiProvider);

    // Register with Monaco
    this.registerMonacoLanguageFeatures(languageId, aiProvider);
    this.registeredLanguages.add(languageId);

    console.log(`AI-enhanced language support registered for: ${languageId}`);
  }

  private getBaseProvider(languageId: string): LanguageServiceProvider | null {
    // Access the internal providers from the language service manager
    const providers = (languageServiceManager as any).providers;
    return providers.get(languageId) || null;
  }

  private registerMonacoLanguageFeatures(
    languageId: string, 
    provider: AIEnhancedLanguageProvider
  ): void {
    // Register completion provider with higher priority
    monaco.languages.registerCompletionItemProvider(languageId, {
      triggerCharacters: ['.', '(', '<', '"', "'", '/', '@', '#'],
      provideCompletionItems: async (model, position) => {
        const items = await provider.provideCompletionItems(model, position);
        return { suggestions: items };
      },
    });

    // Register code action provider
    monaco.languages.registerCodeActionProvider(languageId, {
      provideCodeActions: async (model, range) => {
        const actions = await provider.provideCodeActions(model, range);
        return { actions, dispose: () => {} };
      },
    });

    // Register hover provider
    monaco.languages.registerHoverProvider(languageId, {
      provideHover: async (model, position) => {
        return await provider.provideHover(model, position);
      },
    });
  }

  initializeAllAILanguages(): void {
    // Initialize AI-enhanced providers for all supported languages
    const supportedLanguages = ['javascript', 'typescript', 'python', 'java', 'csharp', 'go', 'rust'];
    
    for (const language of supportedLanguages) {
      this.registerAIEnhancedLanguage(language);
    }
  }

  getAIProvider(languageId: string): AIEnhancedLanguageProvider | null {
    return this.aiProviders.get(languageId) || null;
  }
}

// Global AI-enhanced language service manager
export const aiLanguageServiceManager = new AIEnhancedLanguageServiceManager();