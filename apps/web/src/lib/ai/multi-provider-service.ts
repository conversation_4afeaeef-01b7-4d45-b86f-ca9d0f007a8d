'use client';

import { EventEmitter } from 'events';
import { BaseProviderAdapter, GenerationRequest, GenerationResponse, ProviderUsageStats } from './providers/provider-base';
import { StabilityAIAdapter } from './providers/stability-ai-adapter';
import { DALLEAdapter } from './providers/dalle-adapter';
import { MidjourneyAdapter } from './providers/midjourney-adapter';
import { 
  ProviderSelectionEngine, 
  ProviderSelectionCriteria, 
  SelectionResult,
  TaskType,
  SelectionPriority
} from './provider-selection-engine';

export interface MultiProviderConfig {
  providers: {
    stabilityAI?: { apiKey: string; organizationId?: string };
    dalle?: { apiKey: string; organizationId?: string };
    midjourney?: { apiKey: string; discordToken?: string; applicationId?: string };
    replicate?: { apiKey: string }; // For existing integration
  };
  defaultStrategy?: {
    priority: SelectionPriority;
    fallbackEnabled: boolean;
    budgetLimits?: {
      dailyLimit: number;
      monthlyLimit: number;
    };
  };
  enableLearning?: boolean;
  enableCaching?: boolean;
}

export interface MultiProviderGenerationRequest extends GenerationRequest {
  providerPreference?: string;
  selectionCriteria?: Partial<ProviderSelectionCriteria>;
  enableFallback?: boolean;
  maxRetries?: number;
  timeoutMs?: number;
}

export interface MultiProviderGenerationResponse extends GenerationResponse {
  provider: string;
  alternativesUsed: string[];
  selectionReasoning: string;
  totalCost: number;
  totalTime: number;
  fallbacksAttempted: number;
}

export interface ProviderBudget {
  daily: { limit: number; spent: number; remaining: number };
  monthly: { limit: number; spent: number; remaining: number };
  lastReset: { daily: Date; monthly: Date };
}

export interface MultiProviderMetrics {
  totalGenerations: number;
  successRate: number;
  averageCost: number;
  averageTime: number;
  providerDistribution: Record<string, number>;
  taskDistribution: Record<TaskType, number>;
  costSavings: number; // vs always using most expensive
  qualityScore: number;
  uptime: number;
}

export interface BudgetAlert {
  type: 'warning' | 'limit' | 'exceeded';
  period: 'daily' | 'monthly';
  provider?: string;
  currentSpent: number;
  limit: number;
  timestamp: Date;
}

export class MultiProviderService extends EventEmitter {
  private providers: Map<string, BaseProviderAdapter> = new Map();
  private selectionEngine: ProviderSelectionEngine;
  private config: MultiProviderConfig;
  private budgetTracker: Map<string, ProviderBudget> = new Map();
  private generationHistory: Array<{
    request: MultiProviderGenerationRequest;
    response: MultiProviderGenerationResponse;
    timestamp: Date;
  }> = [];
  private cache: Map<string, { response: GenerationResponse; timestamp: number; ttl: number }> = new Map();
  private isInitialized: boolean = false;

  constructor(config: MultiProviderConfig) {
    super();
    this.config = config;
    this.selectionEngine = new ProviderSelectionEngine();
    
    this.setupEventListeners();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize provider adapters
      await this.initializeProviders();
      
      // Initialize budget tracking
      this.initializeBudgetTracking();
      
      // Start periodic tasks
      this.startPeriodicTasks();
      
      this.isInitialized = true;
      this.emit('initialized');
      
    } catch (error) {
      this.emit('initialization:failed', error);
      throw error;
    }
  }

  private async initializeProviders(): Promise<void> {
    const { providers } = this.config;
    
    // Initialize Stability AI
    if (providers.stabilityAI?.apiKey) {
      const stabilityAI = new StabilityAIAdapter(providers.stabilityAI);
      await stabilityAI.initialize();
      this.providers.set('stability-ai', stabilityAI);
      this.selectionEngine.addProvider(stabilityAI);
    }

    // Initialize DALL-E
    if (providers.dalle?.apiKey) {
      const dalle = new DALLEAdapter(providers.dalle);
      await dalle.initialize();
      this.providers.set('dalle', dalle);
      this.selectionEngine.addProvider(dalle);
    }

    // Initialize Midjourney
    if (providers.midjourney?.apiKey) {
      const midjourney = new MidjourneyAdapter(providers.midjourney);
      await midjourney.initialize();
      this.providers.set('midjourney', midjourney);
      this.selectionEngine.addProvider(midjourney);
    }

    // TODO: Integrate existing Replicate service
    // if (providers.replicate?.apiKey) {
    //   // Use existing EnhancedReplicateService
    // }

    console.log(`Initialized ${this.providers.size} AI image providers`);
  }

  private initializeBudgetTracking(): void {
    const defaultLimits = this.config.defaultStrategy?.budgetLimits || {
      dailyLimit: 50,
      monthlyLimit: 500
    };

    for (const providerName of this.providers.keys()) {
      this.budgetTracker.set(providerName, {
        daily: { limit: defaultLimits.dailyLimit, spent: 0, remaining: defaultLimits.dailyLimit },
        monthly: { limit: defaultLimits.monthlyLimit, spent: 0, remaining: defaultLimits.monthlyLimit },
        lastReset: { daily: new Date(), monthly: new Date() }
      });
    }

    // Overall budget
    this.budgetTracker.set('total', {
      daily: { limit: defaultLimits.dailyLimit * 2, spent: 0, remaining: defaultLimits.dailyLimit * 2 },
      monthly: { limit: defaultLimits.monthlyLimit * 2, spent: 0, remaining: defaultLimits.monthlyLimit * 2 },
      lastReset: { daily: new Date(), monthly: new Date() }
    });
  }

  private startPeriodicTasks(): void {
    // Reset daily budgets at midnight
    const resetDaily = () => {
      const now = new Date();
      for (const [providerName, budget] of this.budgetTracker.entries()) {
        if (now.getDate() !== budget.lastReset.daily.getDate()) {
          budget.daily.spent = 0;
          budget.daily.remaining = budget.daily.limit;
          budget.lastReset.daily = now;
          this.emit('budget:daily:reset', providerName);
        }
      }
    };

    // Reset monthly budgets at start of month
    const resetMonthly = () => {
      const now = new Date();
      for (const [providerName, budget] of this.budgetTracker.entries()) {
        if (now.getMonth() !== budget.lastReset.monthly.getMonth()) {
          budget.monthly.spent = 0;
          budget.monthly.remaining = budget.monthly.limit;
          budget.lastReset.monthly = now;
          this.emit('budget:monthly:reset', providerName);
        }
      }
    };

    // Clean old cache entries
    const cleanCache = () => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now - entry.timestamp > entry.ttl) {
          this.cache.delete(key);
        }
      }
    };

    // Run tasks every hour
    setInterval(() => {
      resetDaily();
      resetMonthly();
      cleanCache();
    }, 60 * 60 * 1000);

    // Initial run
    resetDaily();
    resetMonthly();
  }

  private setupEventListeners(): void {
    this.selectionEngine.on('selection:completed', (data) => {
      this.emit('provider:selected', data);
    });

    this.selectionEngine.on('learning:recorded', (data) => {
      this.emit('learning:updated', data);
    });
  }

  // Main generation method
  async generateImage(request: MultiProviderGenerationRequest): Promise<MultiProviderGenerationResponse> {
    if (!this.isInitialized) {
      throw new Error('MultiProviderService not initialized');
    }

    const startTime = Date.now();
    const requestId = `multi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Check cache first
      if (this.config.enableCaching !== false) {
        const cached = this.checkCache(request);
        if (cached) {
          return this.createCachedResponse(cached, requestId, startTime);
        }
      }

      // Build selection criteria
      const criteria = this.buildSelectionCriteria(request);
      
      // Select optimal provider
      const selection = await this.selectProvider(criteria, request.providerPreference);
      
      // Attempt generation with primary provider
      let result = await this.attemptGeneration(selection.primary, request);
      
      const alternativesUsed: string[] = [];
      let fallbacksAttempted = 0;

      // Try alternatives if primary fails and fallback is enabled
      if (!result.success && request.enableFallback !== false) {
        for (const alternative of [...selection.alternatives, ...selection.fallbacks]) {
          try {
            fallbacksAttempted++;
            result = await this.attemptGeneration(alternative, request);
            alternativesUsed.push(alternative.provider);
            
            if (result.success) {
              this.emit('fallback:success', { 
                original: selection.primary.provider, 
                fallback: alternative.provider 
              });
              break;
            }
          } catch (error) {
            this.emit('fallback:failed', { 
              provider: alternative.provider, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            });
          }
        }
      }

      const totalTime = Date.now() - startTime;
      const response: MultiProviderGenerationResponse = {
        ...result.response,
        provider: result.provider,
        alternativesUsed,
        selectionReasoning: selection.recommendation,
        totalCost: result.response.metadata.cost,
        totalTime,
        fallbacksAttempted
      };

      // Record outcome for learning
      if (this.config.enableLearning !== false) {
        this.recordOutcome(criteria, result.provider, result.success);
      }

      // Update budget tracking
      this.updateBudgetTracking(result.provider, result.response.metadata.cost);

      // Cache successful results
      if (result.success && this.config.enableCaching !== false) {
        this.cacheResponse(request, result.response);
      }

      // Record in history
      this.recordGeneration(request, response);

      return response;

    } catch (error) {
      const totalTime = Date.now() - startTime;
      
      // Create error response
      const errorResponse: MultiProviderGenerationResponse = {
        id: requestId,
        status: 'failed',
        images: [],
        metadata: {
          model: 'unknown',
          prompt: request.prompt,
          parameters: {},
          processingTime: totalTime,
          cost: 0,
          queue: { position: 0, estimatedWait: 0 },
          provider: 'multi-provider',
          timestamp: new Date()
        },
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: 'none',
        alternativesUsed: [],
        selectionReasoning: 'Generation failed before provider selection',
        totalCost: 0,
        totalTime,
        fallbacksAttempted: 0
      };

      this.emit('generation:failed', { request, error: errorResponse.error });
      return errorResponse;
    }
  }

  private buildSelectionCriteria(request: MultiProviderGenerationRequest): ProviderSelectionCriteria {
    const defaultCriteria: ProviderSelectionCriteria = {
      task: this.inferTaskType(request),
      priority: this.config.defaultStrategy?.priority || 'balanced',
      budget: this.buildBudgetConstraints(),
      features: {
        excludedProviders: [],
        requiredProviders: request.providerPreference ? [request.providerPreference] : undefined
      },
      fallbackStrategy: request.enableFallback !== false ? 'different-provider' : 'none'
    };

    // Merge with request-specific criteria
    return {
      ...defaultCriteria,
      ...request.selectionCriteria
    };
  }

  private inferTaskType(request: GenerationRequest): TaskType {
    // Simple task inference based on request properties
    if (request.imageInput && request.maskInput) return 'inpainting';
    if (request.imageInput) return 'image-to-image';
    
    // Analyze prompt for task hints
    const prompt = request.prompt.toLowerCase();
    if (prompt.includes('anime') || prompt.includes('manga')) return 'anime';
    if (prompt.includes('logo') || prompt.includes('brand')) return 'logo-design';
    if (prompt.includes('concept') || prompt.includes('art')) return 'concept-art';
    if (prompt.includes('photo') || prompt.includes('realistic')) return 'photorealistic';
    
    return 'text-to-image';
  }

  private buildBudgetConstraints() {
    const totalBudget = this.budgetTracker.get('total');
    if (!totalBudget) return undefined;

    return {
      dailyBudgetRemaining: totalBudget.daily.remaining,
      monthlyBudgetRemaining: totalBudget.monthly.remaining,
      maxCostPerImage: Math.min(2.0, totalBudget.daily.remaining / 10), // Conservative estimate
      costOptimization: 'balanced' as const
    };
  }

  private async selectProvider(
    criteria: ProviderSelectionCriteria, 
    preference?: string
  ): Promise<SelectionResult> {
    // If specific provider is preferred and available, try to honor it
    if (preference && this.providers.has(preference)) {
      const preferredProvider = this.providers.get(preference)!;
      if (preferredProvider.healthStatus.status !== 'unhealthy') {
        // Create modified criteria to strongly favor the preferred provider
        const modifiedCriteria = {
          ...criteria,
          features: {
            ...criteria.features,
            requiredProviders: [preference]
          }
        };
        return this.selectionEngine.selectOptimalProvider(modifiedCriteria);
      }
    }

    return this.selectionEngine.selectOptimalProvider(criteria);
  }

  private async attemptGeneration(
    providerScore: { provider: string; model: string },
    request: MultiProviderGenerationRequest
  ): Promise<{ success: boolean; provider: string; response: GenerationResponse }> {
    const provider = this.providers.get(providerScore.provider);
    if (!provider) {
      throw new Error(`Provider ${providerScore.provider} not found`);
    }

    // Check provider budget
    if (!this.checkProviderBudget(providerScore.provider, providerScore.estimatedCost || 0.2)) {
      throw new Error(`Budget limit reached for provider ${providerScore.provider}`);
    }

    const generationRequest: GenerationRequest = {
      ...request,
      model: providerScore.model
    };

    try {
      const response = await provider.generateImage(generationRequest);
      return {
        success: response.status === 'completed',
        provider: providerScore.provider,
        response
      };
    } catch (error) {
      return {
        success: false,
        provider: providerScore.provider,
        response: {
          id: `error_${Date.now()}`,
          status: 'failed',
          images: [],
          metadata: {
            model: providerScore.model,
            prompt: request.prompt,
            parameters: generationRequest,
            processingTime: 0,
            cost: 0,
            queue: { position: 0, estimatedWait: 0 },
            provider: providerScore.provider,
            timestamp: new Date()
          },
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  private checkProviderBudget(providerName: string, estimatedCost: number): boolean {
    const budget = this.budgetTracker.get(providerName);
    const totalBudget = this.budgetTracker.get('total');
    
    return (budget?.daily.remaining || 0) >= estimatedCost && 
           (totalBudget?.daily.remaining || 0) >= estimatedCost;
  }

  private updateBudgetTracking(providerName: string, cost: number): void {
    // Update provider budget
    const providerBudget = this.budgetTracker.get(providerName);
    if (providerBudget) {
      providerBudget.daily.spent += cost;
      providerBudget.daily.remaining = Math.max(0, providerBudget.daily.limit - providerBudget.daily.spent);
      providerBudget.monthly.spent += cost;
      providerBudget.monthly.remaining = Math.max(0, providerBudget.monthly.limit - providerBudget.monthly.spent);

      // Check for budget alerts
      this.checkBudgetAlerts(providerName, providerBudget);
    }

    // Update total budget
    const totalBudget = this.budgetTracker.get('total');
    if (totalBudget) {
      totalBudget.daily.spent += cost;
      totalBudget.daily.remaining = Math.max(0, totalBudget.daily.limit - totalBudget.daily.spent);
      totalBudget.monthly.spent += cost;
      totalBudget.monthly.remaining = Math.max(0, totalBudget.monthly.limit - totalBudget.monthly.spent);

      this.checkBudgetAlerts('total', totalBudget);
    }

    this.emit('budget:updated', { provider: providerName, cost });
  }

  private checkBudgetAlerts(providerName: string, budget: ProviderBudget): void {
    // Daily budget alerts
    const dailyUsagePercent = budget.daily.spent / budget.daily.limit;
    if (dailyUsagePercent >= 1.0) {
      this.emitBudgetAlert('exceeded', 'daily', providerName, budget.daily.spent, budget.daily.limit);
    } else if (dailyUsagePercent >= 0.9) {
      this.emitBudgetAlert('limit', 'daily', providerName, budget.daily.spent, budget.daily.limit);
    } else if (dailyUsagePercent >= 0.75) {
      this.emitBudgetAlert('warning', 'daily', providerName, budget.daily.spent, budget.daily.limit);
    }

    // Monthly budget alerts
    const monthlyUsagePercent = budget.monthly.spent / budget.monthly.limit;
    if (monthlyUsagePercent >= 1.0) {
      this.emitBudgetAlert('exceeded', 'monthly', providerName, budget.monthly.spent, budget.monthly.limit);
    } else if (monthlyUsagePercent >= 0.9) {
      this.emitBudgetAlert('limit', 'monthly', providerName, budget.monthly.spent, budget.monthly.limit);
    } else if (monthlyUsagePercent >= 0.75) {
      this.emitBudgetAlert('warning', 'monthly', providerName, budget.monthly.spent, budget.monthly.limit);
    }
  }

  private emitBudgetAlert(
    type: 'warning' | 'limit' | 'exceeded',
    period: 'daily' | 'monthly',
    provider: string,
    currentSpent: number,
    limit: number
  ): void {
    const alert: BudgetAlert = {
      type,
      period,
      provider: provider !== 'total' ? provider : undefined,
      currentSpent,
      limit,
      timestamp: new Date()
    };

    this.emit('budget:alert', alert);
  }

  // Cache management
  private checkCache(request: GenerationRequest): GenerationResponse | null {
    const cacheKey = this.generateCacheKey(request);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.response;
    }
    
    return null;
  }

  private cacheResponse(request: GenerationRequest, response: GenerationResponse): void {
    const cacheKey = this.generateCacheKey(request);
    const ttl = this.calculateCacheTTL(request);
    
    this.cache.set(cacheKey, {
      response,
      timestamp: Date.now(),
      ttl
    });
  }

  private generateCacheKey(request: GenerationRequest): string {
    const keyData = {
      prompt: request.prompt,
      negativePrompt: request.negativePrompt,
      width: request.width,
      height: request.height,
      seed: request.seed,
      // Include other relevant parameters
    };
    
    return `multi_${JSON.stringify(keyData)}`;
  }

  private calculateCacheTTL(request: GenerationRequest): number {
    // Deterministic results cache longer
    if (request.seed) return 30 * 24 * 60 * 60 * 1000; // 30 days
    return 24 * 60 * 60 * 1000; // 1 day
  }

  private createCachedResponse(
    cached: GenerationResponse,
    requestId: string,
    startTime: number
  ): MultiProviderGenerationResponse {
    return {
      ...cached,
      id: requestId,
      provider: 'cache',
      alternativesUsed: [],
      selectionReasoning: 'Retrieved from cache',
      totalCost: 0,
      totalTime: Date.now() - startTime,
      fallbacksAttempted: 0,
      metadata: {
        ...cached.metadata,
        cached: true
      }
    };
  }

  // Learning and analytics
  private recordOutcome(
    criteria: ProviderSelectionCriteria,
    provider: string,
    success: boolean
  ): void {
    this.selectionEngine.recordSelectionOutcome(
      criteria,
      provider,
      success ? 'success' : 'failure'
    );
  }

  private recordGeneration(
    request: MultiProviderGenerationRequest,
    response: MultiProviderGenerationResponse
  ): void {
    this.generationHistory.push({
      request,
      response,
      timestamp: new Date()
    });

    // Keep only recent history
    if (this.generationHistory.length > 1000) {
      this.generationHistory = this.generationHistory.slice(-1000);
    }
  }

  // Public API methods
  
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  getProviderStatus(providerName: string) {
    const provider = this.providers.get(providerName);
    if (!provider) return null;

    return {
      name: provider.name,
      displayName: provider.displayName,
      health: provider.healthStatus,
      models: provider.models.length,
      capabilities: provider.capabilities,
      budget: this.budgetTracker.get(providerName)
    };
  }

  getAllProviderStatuses() {
    return Array.from(this.providers.keys()).map(name => this.getProviderStatus(name));
  }

  getBudgetSummary() {
    const summary: Record<string, ProviderBudget> = {};
    for (const [provider, budget] of this.budgetTracker.entries()) {
      summary[provider] = { ...budget };
    }
    return summary;
  }

  getMetrics(timeRange?: { start: Date; end: Date }): MultiProviderMetrics {
    const relevantHistory = timeRange ? 
      this.generationHistory.filter(h => h.timestamp >= timeRange.start && h.timestamp <= timeRange.end) :
      this.generationHistory;

    if (relevantHistory.length === 0) {
      return {
        totalGenerations: 0,
        successRate: 0,
        averageCost: 0,
        averageTime: 0,
        providerDistribution: {},
        taskDistribution: {},
        costSavings: 0,
        qualityScore: 0,
        uptime: 100
      };
    }

    const successful = relevantHistory.filter(h => h.response.status === 'completed');
    const totalCost = relevantHistory.reduce((sum, h) => sum + h.response.totalCost, 0);
    const totalTime = relevantHistory.reduce((sum, h) => sum + h.response.totalTime, 0);

    // Provider distribution
    const providerDistribution: Record<string, number> = {};
    relevantHistory.forEach(h => {
      providerDistribution[h.response.provider] = (providerDistribution[h.response.provider] || 0) + 1;
    });

    // Task distribution (would need to be tracked in request)
    const taskDistribution: Record<TaskType, number> = {};

    return {
      totalGenerations: relevantHistory.length,
      successRate: successful.length / relevantHistory.length,
      averageCost: totalCost / relevantHistory.length,
      averageTime: totalTime / relevantHistory.length,
      providerDistribution,
      taskDistribution,
      costSavings: 0, // TODO: Calculate vs most expensive option
      qualityScore: 8.5, // TODO: Calculate based on user ratings
      uptime: 99.5 // TODO: Calculate based on provider health
    };
  }

  setBudgetLimit(provider: string, period: 'daily' | 'monthly', limit: number): void {
    const budget = this.budgetTracker.get(provider);
    if (budget) {
      budget[period].limit = limit;
      budget[period].remaining = Math.max(0, limit - budget[period].spent);
      this.emit('budget:limit:updated', { provider, period, limit });
    }
  }

  clearCache(): void {
    this.cache.clear();
    this.emit('cache:cleared');
  }

  // Cleanup
  dispose(): void {
    this.selectionEngine.dispose();
    
    for (const provider of this.providers.values()) {
      provider.dispose();
    }
    
    this.providers.clear();
    this.budgetTracker.clear();
    this.generationHistory.length = 0;
    this.cache.clear();
    this.removeAllListeners();
  }
}