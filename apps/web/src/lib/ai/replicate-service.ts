import { EventEmitter } from 'events';

export interface ReplicateConfig {
  apiToken: string;
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
}

export interface ReplicateModel {
  id: string;
  name: string;
  description: string;
  version: string;
  owner: string;
  visibility: 'public' | 'private';
  hardware: string;
  pricing: {
    compute: number;
    storage: number;
  };
  capabilities: string[];
  inputSchema: any;
  outputSchema: any;
}

export interface GenerationRequest {
  model: string;
  input: Record<string, any>;
  webhook?: string;
  stream?: boolean;
}

export interface GenerationResult {
  id: string;
  status: 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  input: Record<string, any>;
  output?: any;
  error?: string;
  logs?: string;
  metrics?: {
    predict_time?: number;
    total_time?: number;
  };
  urls?: {
    get: string;
    cancel: string;
  };
  created_at: string;
  completed_at?: string;
}

export class ReplicateService extends EventEmitter {
  private config: ReplicateConfig;
  private modelRegistry: Map<string, ReplicateModel> = new Map();
  private activeGenerations: Map<string, GenerationResult> = new Map();

  constructor(config: ReplicateConfig) {
    super();
    this.config = {
      baseUrl: 'https://api.replicate.com/v1',
      timeout: 300000, // 5 minutes
      retryAttempts: 3,
      ...config
    };
    
    this.initializeModelRegistry();
  }

  private async initializeModelRegistry(): Promise<void> {
    // Initialize with popular models for image generation
    const popularModels: ReplicateModel[] = [
      {
        id: 'stability-ai/stable-diffusion',
        name: 'Stable Diffusion',
        description: 'High-quality text-to-image generation',
        version: 'latest',
        owner: 'stability-ai',
        visibility: 'public',
        hardware: 'gpu-t4',
        pricing: { compute: 0.0023, storage: 0.000 },
        capabilities: ['text-to-image', 'img2img'],
        inputSchema: {
          prompt: { type: 'string', required: true },
          negative_prompt: { type: 'string', required: false },
          width: { type: 'integer', default: 512 },
          height: { type: 'integer', default: 512 },
          num_inference_steps: { type: 'integer', default: 50 },
          guidance_scale: { type: 'number', default: 7.5 },
          seed: { type: 'integer', required: false }
        },
        outputSchema: { type: 'array', items: { type: 'string', format: 'uri' } }
      },
      {
        id: 'stability-ai/sdxl',
        name: 'Stable Diffusion XL',
        description: 'Enhanced text-to-image with higher resolution',
        version: 'latest',
        owner: 'stability-ai',
        visibility: 'public',
        hardware: 'gpu-a40-large',
        pricing: { compute: 0.0055, storage: 0.000 },
        capabilities: ['text-to-image', 'img2img', 'inpainting'],
        inputSchema: {
          prompt: { type: 'string', required: true },
          negative_prompt: { type: 'string', required: false },
          width: { type: 'integer', default: 1024 },
          height: { type: 'integer', default: 1024 },
          num_inference_steps: { type: 'integer', default: 50 },
          guidance_scale: { type: 'number', default: 7.5 },
          seed: { type: 'integer', required: false }
        },
        outputSchema: { type: 'array', items: { type: 'string', format: 'uri' } }
      },
      {
        id: 'lucataco/realistic-vision-v5',
        name: 'Realistic Vision',
        description: 'Photorealistic image generation',
        version: 'latest',
        owner: 'lucataco',
        visibility: 'public',
        hardware: 'gpu-t4',
        pricing: { compute: 0.0023, storage: 0.000 },
        capabilities: ['text-to-image', 'img2img'],
        inputSchema: {
          prompt: { type: 'string', required: true },
          negative_prompt: { type: 'string', required: false },
          width: { type: 'integer', default: 512 },
          height: { type: 'integer', default: 512 },
          num_inference_steps: { type: 'integer', default: 20 },
          guidance_scale: { type: 'number', default: 7 },
          seed: { type: 'integer', required: false }
        },
        outputSchema: { type: 'array', items: { type: 'string', format: 'uri' } }
      }
    ];

    popularModels.forEach(model => {
      this.modelRegistry.set(model.id, model);
    });
  }

  public async generateImage(request: GenerationRequest): Promise<GenerationResult> {
    try {
      const response = await this.makeRequest('POST', '/predictions', {
        version: await this.getModelVersion(request.model),
        input: request.input,
        webhook: request.webhook,
        stream: request.stream
      });

      const result: GenerationResult = {
        id: response.id,
        status: response.status,
        input: response.input,
        output: response.output,
        error: response.error,
        logs: response.logs,
        metrics: response.metrics,
        urls: response.urls,
        created_at: response.created_at,
        completed_at: response.completed_at
      };

      this.activeGenerations.set(result.id, result);
      this.emit('generation:started', result);

      // Poll for completion if not using webhooks
      if (!request.webhook && result.status !== 'succeeded' && result.status !== 'failed') {
        this.pollGeneration(result.id);
      }

      return result;
    } catch (error) {
      this.emit('generation:error', error);
      throw error;
    }
  }

  public async getGeneration(id: string): Promise<GenerationResult> {
    try {
      const response = await this.makeRequest('GET', `/predictions/${id}`);
      
      const result: GenerationResult = {
        id: response.id,
        status: response.status,
        input: response.input,
        output: response.output,
        error: response.error,
        logs: response.logs,
        metrics: response.metrics,
        urls: response.urls,
        created_at: response.created_at,
        completed_at: response.completed_at
      };

      this.activeGenerations.set(id, result);
      return result;
    } catch (error) {
      this.emit('generation:error', error);
      throw error;
    }
  }

  public async cancelGeneration(id: string): Promise<void> {
    try {
      await this.makeRequest('POST', `/predictions/${id}/cancel`);
      
      const generation = this.activeGenerations.get(id);
      if (generation) {
        generation.status = 'canceled';
        this.emit('generation:canceled', generation);
      }
    } catch (error) {
      this.emit('generation:error', error);
      throw error;
    }
  }

  private async pollGeneration(id: string): Promise<void> {
    const poll = async () => {
      try {
        const result = await this.getGeneration(id);
        
        if (result.status === 'succeeded') {
          this.emit('generation:completed', result);
        } else if (result.status === 'failed') {
          this.emit('generation:failed', result);
        } else if (result.status === 'processing' || result.status === 'starting') {
          this.emit('generation:progress', result);
          setTimeout(poll, 1000); // Poll every second
        }
      } catch (error) {
        this.emit('generation:error', error);
      }
    };

    poll();
  }

  public getAvailableModels(): ReplicateModel[] {
    return Array.from(this.modelRegistry.values());
  }

  public getModel(id: string): ReplicateModel | null {
    return this.modelRegistry.get(id) || null;
  }

  public recommendModel(prompt: string, requirements: any = {}): ReplicateModel | null {
    const models = this.getAvailableModels();
    
    // Simple recommendation logic based on prompt and requirements
    if (requirements.photorealistic) {
      return models.find(m => m.id.includes('realistic-vision')) || models[0];
    }
    
    if (requirements.highResolution) {
      return models.find(m => m.id.includes('sdxl')) || models[0];
    }
    
    // Default to Stable Diffusion
    return models.find(m => m.id.includes('stable-diffusion')) || models[0];
  }

  public estimateCost(model: string, input: any): number {
    const modelInfo = this.getModel(model);
    if (!modelInfo) return 0;
    
    // Simple cost estimation based on model pricing
    const baseCompute = modelInfo.pricing.compute;
    const steps = input.num_inference_steps || 50;
    const resolution = (input.width || 512) * (input.height || 512);
    
    // Cost scales with steps and resolution
    const stepMultiplier = steps / 50;
    const resolutionMultiplier = resolution / (512 * 512);
    
    return baseCompute * stepMultiplier * resolutionMultiplier;
  }

  private async getModelVersion(modelId: string): Promise<string> {
    const model = this.getModel(modelId);
    return model?.version || 'latest';
  }

  private async makeRequest(method: string, endpoint: string, data?: any): Promise<any> {
    const url = `${this.config.baseUrl}${endpoint}`;
    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Token ${this.config.apiToken}`,
        'Content-Type': 'application/json'
      },
      signal: AbortSignal.timeout(this.config.timeout!)
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    let lastError: Error;
    
    for (let attempt = 0; attempt < this.config.retryAttempts!; attempt++) {
      try {
        const response = await fetch(url, options);
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`Replicate API error: ${response.status} - ${errorData.detail || response.statusText}`);
        }
        
        return await response.json();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.retryAttempts! - 1) {
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }
    
    throw lastError!;
  }

  public getActiveGenerations(): GenerationResult[] {
    return Array.from(this.activeGenerations.values());
  }

  public dispose(): void {
    this.activeGenerations.clear();
    this.removeAllListeners();
  }
}