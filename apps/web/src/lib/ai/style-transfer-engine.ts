'use client';

import { EventEmitter } from 'events';
import { MultiProviderService } from './multi-provider-service';

// Core Types for Style Transfer System
export interface StyleModel {
  id: string;
  name: string;
  provider: 'replicate' | 'huggingface' | 'openai' | 'local';
  modelId: string;
  version: string;
  capabilities: StyleCapability[];
  performance: ModelPerformance;
  supportedFormats: string[];
  maxResolution: { width: number; height: number };
  averageProcessingTime: number;
  qualityScore: number;
  costPerGeneration: number;
}

export interface StyleCapability {
  type: 'artistic' | 'photographic' | 'abstract' | 'texture' | 'color' | 'preview';
  subtype: string;
  description: string;
  examples: string[];
}

export interface ModelPerformance {
  handlesComplexity: boolean;
  speed: 'fast' | 'medium' | 'slow';
  reliability: number; // 0-1
  qualityConsistency: number; // 0-1
}

export interface StyleReference {
  type: 'image' | 'preset' | 'custom';
  source: string | ImageData | HTMLImageElement;
  name?: string;
  description?: string;
  tags: string[];
  mood: StyleMood;
  artisticPeriod?: string;
  dominantColors: string[];
  complexity?: 'low' | 'medium' | 'high';
}

export interface StyleMood {
  energy: 'low' | 'medium' | 'high';
  warmth: 'cool' | 'neutral' | 'warm';
  mood: 'calm' | 'dramatic' | 'playful' | 'serious' | 'mysterious';
  era: 'classical' | 'modern' | 'contemporary' | 'futuristic';
}

export interface StyleParameters {
  strength: number; // 0-1
  preserveContent: number; // 0-1
  colorTransfer: boolean;
  textureTransfer: boolean;
  edgePreservation: number; // 0-1
  detailLevel: 'low' | 'medium' | 'high';
  blendMode: BlendMode;
  iterations?: number;
}

export type BlendMode = 
  | 'normal' | 'multiply' | 'screen' | 'overlay' | 'soft-light' 
  | 'hard-light' | 'color-dodge' | 'color-burn' | 'darken' | 'lighten';

export interface StyleTransferRequest {
  contentImage: ImageData | HTMLImageElement | string;
  styleReference: StyleReference;
  model?: string;
  parameters: StyleParameters;
  options: TransferOptions;
}

export interface TransferOptions {
  priority: 'quality' | 'speed' | 'balanced';
  enablePreview: boolean;
  enableCache: boolean;
  outputFormat: 'jpeg' | 'png' | 'webp';
  outputQuality: number; // 0-1
  maxRetries: number;
  timeoutMs: number;
}

export interface StyleTransferResult {
  success: boolean;
  result?: ImageData;
  originalImage?: ImageData;
  model?: string;
  processingTime?: number;
  error?: string;
  metadata?: {
    contentAnalysis: ContentAnalysis;
    styleAnalysis: StyleAnalysis;
    qualityMetrics: QualityMetrics;
    cost?: number;
    provider?: string;
  };
}

export interface ContentAnalysis {
  resolution: { width: number; height: number };
  primaryContentType: 'portrait' | 'landscape' | 'object' | 'abstract' | 'mixed';
  complexity: 'low' | 'medium' | 'high';
  colorProfile: {
    dominant: string[];
    saturation: number;
    brightness: number;
    contrast: number;
  };
  edgeComplexity: number; // 0-1
  textureComplexity: number; // 0-1
}

export interface StyleAnalysis {
  styleTypes: Array<'artistic' | 'photographic' | 'abstract' | 'texture' | 'color'>;
  artisticPeriod?: string;
  colorComplexity: 'low' | 'medium' | 'high';
  textureComplexity: 'low' | 'medium' | 'high';
  confidence: number; // 0-1
}

export interface QualityMetrics {
  structuralSimilarity: number; // SSIM score
  colorAccuracy: number;
  texturePreservation: number;
  overallQuality: number;
}

export interface StylePreviewResult {
  success: boolean;
  preview?: ImageData;
  processingTime?: number;
  estimatedFullTime?: number;
  error?: string;
}

export interface StyleFeatures {
  color: ColorFeatures;
  texture: TextureFeatures;
  composition: CompositionFeatures;
  semantic: SemanticFeatures;
  metadata: {
    extractedAt: number;
    analysisVersion: string;
  };
}

export interface ColorFeatures {
  palette: string[];
  harmony: 'monochromatic' | 'analogous' | 'complementary' | 'triadic' | 'split-complementary';
  saturation: number;
  brightness: number;
  contrast: number;
  temperature: number; // -1 to 1 (cool to warm)
}

export interface TextureFeatures {
  roughness: number; // 0-1
  patterns: string[];
  directionality: number; // 0-1
  regularity: number; // 0-1
  contrast: number; // 0-1
}

export interface CompositionFeatures {
  balance: 'symmetrical' | 'asymmetrical' | 'radial';
  focal_points: Array<{ x: number; y: number; strength: number }>;
  rule_of_thirds: boolean;
  leading_lines: number;
  depth: 'flat' | 'shallow' | 'deep';
}

export interface SemanticFeatures {
  subjects: string[];
  mood: string[];
  style_period: string;
  artistic_movement: string;
  complexity: number; // 0-1
}

export interface StyleSimilarityResult {
  style: StyleReference;
  similarity: number; // 0-1
  matchingFeatures: string[];
  differences: string[];
}

// Advanced Style Transfer Service
export class AdvancedStyleTransferService extends EventEmitter {
  private modelRegistry: Map<string, StyleModel> = new Map();
  private multiProviderService: MultiProviderService;
  private previewModelId: string = 'fast-neural-style';
  private defaultModels: StyleModel[];
  
  constructor(multiProviderService: MultiProviderService) {
    super();
    this.multiProviderService = multiProviderService;
    this.defaultModels = this.getDefaultStyleModels();
    this.initializeModelRegistry();
  }

  private getDefaultStyleModels(): StyleModel[] {
    return [
      {
        id: 'neural-style-transfer',
        name: 'Neural Style Transfer',
        provider: 'replicate',
        modelId: 'cjwbw/style-transfer',
        version: 'latest',
        capabilities: [
          {
            type: 'artistic',
            subtype: 'classic',
            description: 'Classic artistic style transfer using VGG-based neural networks',
            examples: ['van-gogh', 'picasso', 'monet']
          },
          {
            type: 'texture',
            subtype: 'painterly',
            description: 'Excellent for painterly textures and brush strokes',
            examples: ['oil-painting', 'watercolor', 'acrylic']
          }
        ],
        performance: {
          handlesComplexity: true,
          speed: 'medium',
          reliability: 0.9,
          qualityConsistency: 0.85
        },
        supportedFormats: ['jpeg', 'png'],
        maxResolution: { width: 2048, height: 2048 },
        averageProcessingTime: 45000, // 45 seconds
        qualityScore: 8.5,
        costPerGeneration: 0.15
      },
      {
        id: 'fast-neural-style',
        name: 'Fast Neural Style',
        provider: 'replicate',
        modelId: 'cjwbw/fast-neural-style',
        version: 'latest',
        capabilities: [
          {
            type: 'preview',
            subtype: 'fast',
            description: 'Fast style transfer optimized for real-time preview',
            examples: ['quick-preview', 'real-time']
          },
          {
            type: 'artistic',
            subtype: 'simplified',
            description: 'Simplified artistic styles for fast processing',
            examples: ['cartoon', 'sketch', 'minimal']
          }
        ],
        performance: {
          handlesComplexity: false,
          speed: 'fast',
          reliability: 0.95,
          qualityConsistency: 0.75
        },
        supportedFormats: ['jpeg', 'png'],
        maxResolution: { width: 512, height: 512 },
        averageProcessingTime: 8000, // 8 seconds
        qualityScore: 7.0,
        costPerGeneration: 0.05
      },
      {
        id: 'adain-style-transfer',
        name: 'AdaIN Style Transfer',
        provider: 'replicate',
        modelId: 'cjwbw/adain-style-transfer',
        version: 'latest',
        capabilities: [
          {
            type: 'photographic',
            subtype: 'realistic',
            description: 'Adaptive Instance Normalization for photorealistic style transfer',
            examples: ['photo-style', 'realistic', 'subtle']
          },
          {
            type: 'color',
            subtype: 'adaptive',
            description: 'Excellent color and tone adaptation',
            examples: ['color-grading', 'tone-mapping']
          }
        ],
        performance: {
          handlesComplexity: true,
          speed: 'medium',
          reliability: 0.88,
          qualityConsistency: 0.9
        },
        supportedFormats: ['jpeg', 'png'],
        maxResolution: { width: 1024, height: 1024 },
        averageProcessingTime: 30000, // 30 seconds
        qualityScore: 9.0,
        costPerGeneration: 0.12
      },
      {
        id: 'wct-style-transfer',
        name: 'WCT Style Transfer',
        provider: 'replicate',
        modelId: 'cjwbw/wct-style-transfer',
        version: 'latest',
        capabilities: [
          {
            type: 'abstract',
            subtype: 'whitening-coloring',
            description: 'Whitening and Coloring Transform for abstract styles',
            examples: ['abstract', 'geometric', 'modern']
          },
          {
            type: 'texture',
            subtype: 'structural',
            description: 'Preserves structural elements while transferring style',
            examples: ['architecture', 'geometric', 'patterns']
          }
        ],
        performance: {
          handlesComplexity: true,
          speed: 'slow',
          reliability: 0.82,
          qualityConsistency: 0.88
        },
        supportedFormats: ['jpeg', 'png'],
        maxResolution: { width: 1024, height: 1024 },
        averageProcessingTime: 60000, // 60 seconds
        qualityScore: 8.8,
        costPerGeneration: 0.18
      }
    ];
  }

  private initializeModelRegistry(): void {
    this.defaultModels.forEach(model => {
      this.modelRegistry.set(model.id, model);
    });
  }

  // Main style transfer method
  async transferStyle(request: StyleTransferRequest): Promise<StyleTransferResult> {
    const startTime = Date.now();
    
    try {
      // Validate request
      this.validateTransferRequest(request);
      
      // Select optimal model if not specified
      let modelId = request.model;
      if (!modelId) {
        modelId = await this.selectOptimalModel(request.contentImage, request.styleReference);
      }

      const model = this.modelRegistry.get(modelId);
      if (!model) {
        throw new Error(`Model not found: ${modelId}`);
      }

      // Prepare content and style images
      const contentImageData = await this.prepareImage(request.contentImage);
      const styleImageData = await this.prepareStyleImage(request.styleReference);

      // Execute style transfer using multi-provider service
      const result = await this.executeStyleTransfer(
        model, 
        contentImageData, 
        styleImageData, 
        request.parameters,
        request.options
      );

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        result: result.imageData,
        originalImage: contentImageData,
        model: modelId,
        processingTime,
        metadata: {
          contentAnalysis: await this.analyzeImageContent(contentImageData),
          styleAnalysis: await this.analyzeStyleReference(request.styleReference),
          qualityMetrics: await this.calculateQualityMetrics(contentImageData, result.imageData),
          cost: result.cost,
          provider: result.provider
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.emit('style:transfer:failed', { 
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime 
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        model: request.model,
        processingTime
      };
    }
  }

  private validateTransferRequest(request: StyleTransferRequest): void {
    if (!request.contentImage) {
      throw new Error('Content image is required');
    }
    if (!request.styleReference) {
      throw new Error('Style reference is required');
    }
    if (!request.parameters) {
      throw new Error('Style parameters are required');
    }
    if (request.parameters.strength < 0 || request.parameters.strength > 1) {
      throw new Error('Style strength must be between 0 and 1');
    }
  }

  private async selectOptimalModel(
    contentImage: ImageData | HTMLImageElement | string,
    styleReference: StyleReference
  ): Promise<string> {
    // Analyze content image
    const contentImageData = await this.prepareImage(contentImage);
    const contentAnalysis = await this.analyzeImageContent(contentImageData);
    
    // Analyze style reference
    const styleAnalysis = await this.analyzeStyleReference(styleReference);
    
    // Score models based on compatibility
    const modelScores = new Map<string, number>();
    
    for (const [modelId, model] of this.modelRegistry) {
      let score = 0;
      
      // Content compatibility scoring (30%)
      score += this.scoreContentCompatibility(model, contentAnalysis) * 0.3;
      
      // Style compatibility scoring (40%)
      score += this.scoreStyleCompatibility(model, styleAnalysis) * 0.4;
      
      // Performance scoring (20%)
      score += this.scoreModelPerformance(model) * 0.2;
      
      // Quality scoring (10%)
      score += (model.qualityScore / 10) * 0.1;
      
      modelScores.set(modelId, score);
    }
    
    // Return highest scoring model
    const sortedModels = Array.from(modelScores.entries())
      .sort((a, b) => b[1] - a[1]);
    
    if (sortedModels.length === 0) {
      throw new Error('No compatible models found');
    }

    const selectedModel = sortedModels[0][0];
    this.emit('model:selected', { 
      model: selectedModel, 
      score: sortedModels[0][1],
      alternatives: sortedModels.slice(1, 3).map(([id, score]) => ({ id, score }))
    });

    return selectedModel;
  }

  private scoreContentCompatibility(model: StyleModel, contentAnalysis: ContentAnalysis): number {
    let score = 0;
    
    // Check resolution compatibility
    if (contentAnalysis.resolution.width <= model.maxResolution.width &&
        contentAnalysis.resolution.height <= model.maxResolution.height) {
      score += 3;
    } else {
      score += 1; // Partial credit for handling scaling
    }
    
    // Check content type compatibility
    if (model.capabilities.some(cap => 
      this.isContentTypeCompatible(cap.type, contentAnalysis.primaryContentType))) {
      score += 2;
    }
    
    // Check complexity compatibility
    if (contentAnalysis.complexity === 'high' && model.performance.handlesComplexity) {
      score += 1;
    } else if (contentAnalysis.complexity === 'low') {
      score += 0.5; // Any model can handle low complexity
    }
    
    return Math.min(score, 6); // Normalize to 0-6 range
  }

  private isContentTypeCompatible(capabilityType: string, contentType: string): boolean {
    const compatibilityMap: Record<string, string[]> = {
      'artistic': ['portrait', 'landscape', 'object', 'mixed'],
      'photographic': ['portrait', 'landscape'],
      'abstract': ['abstract', 'mixed'],
      'texture': ['object', 'mixed'],
      'color': ['portrait', 'landscape', 'object', 'mixed']
    };
    
    return compatibilityMap[capabilityType]?.includes(contentType) || false;
  }

  private scoreStyleCompatibility(model: StyleModel, styleAnalysis: StyleAnalysis): number {
    let score = 0;
    
    // Check style type compatibility
    const compatibleCapabilities = model.capabilities.filter(cap =>
      styleAnalysis.styleTypes.includes(cap.type)
    );
    score += compatibleCapabilities.length * 0.5;
    
    // Check artistic period compatibility
    if (styleAnalysis.artisticPeriod && model.capabilities.some(cap =>
      cap.subtype === styleAnalysis.artisticPeriod)) {
      score += 2;
    }
    
    // Check color complexity compatibility
    if (styleAnalysis.colorComplexity === 'high' && 
        model.capabilities.some(cap => cap.type === 'color')) {
      score += 1;
    }
    
    return Math.min(score, 6); // Normalize to 0-6 range
  }

  private scoreModelPerformance(model: StyleModel): number {
    let score = 0;
    
    // Speed scoring
    switch (model.performance.speed) {
      case 'fast': score += 2; break;
      case 'medium': score += 1.5; break;
      case 'slow': score += 1; break;
    }
    
    // Reliability scoring
    score += model.performance.reliability * 2;
    
    // Quality consistency scoring
    score += model.performance.qualityConsistency * 2;
    
    return Math.min(score, 6); // Normalize to 0-6 range
  }

  private async prepareImage(image: ImageData | HTMLImageElement | string): Promise<ImageData> {
    if (image instanceof ImageData) {
      return image;
    }
    
    if (typeof image === 'string') {
      // Load image from URL or base64
      return this.loadImageFromString(image);
    }
    
    if (image instanceof HTMLImageElement) {
      return this.convertHTMLImageToImageData(image);
    }
    
    throw new Error('Unsupported image format');
  }

  private async loadImageFromString(src: string): Promise<ImageData> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        resolve(this.convertHTMLImageToImageData(img));
      };
      
      img.onerror = () => {
        reject(new Error('Failed to load image from string'));
      };
      
      img.src = src;
    });
  }

  private convertHTMLImageToImageData(img: HTMLImageElement): ImageData {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);
    
    return ctx.getImageData(0, 0, canvas.width, canvas.height);
  }

  private async prepareStyleImage(styleReference: StyleReference): Promise<ImageData> {
    if (styleReference.type === 'image') {
      return this.prepareImage(styleReference.source);
    }
    
    if (styleReference.type === 'preset') {
      // Load preset style from a predefined collection
      return this.loadPresetStyle(styleReference.source as string);
    }
    
    // For custom styles, the source should be an image
    return this.prepareImage(styleReference.source);
  }

  private async loadPresetStyle(presetName: string): Promise<ImageData> {
    // This would load from a predefined style collection
    // For now, throw an error as presets aren't implemented
    throw new Error(`Preset styles not yet implemented: ${presetName}`);
  }

  private async executeStyleTransfer(
    model: StyleModel,
    contentImage: ImageData,
    styleImage: ImageData,
    parameters: StyleParameters,
    options: TransferOptions
  ): Promise<{ imageData: ImageData; cost: number; provider: string }> {
    
    // Convert ImageData to base64 for API transmission
    const contentBase64 = this.imageDataToBase64(contentImage);
    const styleBase64 = this.imageDataToBase64(styleImage);

    // Prepare generation request for multi-provider service
    const generationRequest = {
      prompt: `Style transfer with strength ${parameters.strength}, preserve content ${parameters.preserveContent}`,
      model: model.modelId,
      imageInput: contentBase64,
      styleInput: styleBase64, // Custom parameter for style transfer
      parameters: {
        strength: parameters.strength,
        preserve_content: parameters.preserveContent,
        color_transfer: parameters.colorTransfer,
        texture_transfer: parameters.textureTransfer,
        edge_preservation: parameters.edgePreservation,
        detail_level: parameters.detailLevel,
        blend_mode: parameters.blendMode,
        iterations: parameters.iterations
      },
      width: contentImage.width,
      height: contentImage.height,
      enableFallback: options.maxRetries > 0,
      maxRetries: options.maxRetries,
      timeoutMs: options.timeoutMs
    };

    // Execute through multi-provider service
    const response = await this.multiProviderService.generateImage(generationRequest);
    
    if (response.status !== 'completed' || !response.images.length) {
      throw new Error(response.error || 'Style transfer failed');
    }

    // Convert result back to ImageData
    const resultImageData = await this.base64ToImageData(response.images[0].url);
    
    return {
      imageData: resultImageData,
      cost: response.totalCost,
      provider: response.provider
    };
  }

  private imageDataToBase64(imageData: ImageData): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    canvas.width = imageData.width;
    canvas.height = imageData.height;
    ctx.putImageData(imageData, 0, 0);
    
    return canvas.toDataURL('image/png');
  }

  private async base64ToImageData(base64: string): Promise<ImageData> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve(this.convertHTMLImageToImageData(img));
      };
      img.onerror = () => reject(new Error('Failed to load result image'));
      img.src = base64;
    });
  }

  private async analyzeImageContent(imageData: ImageData): Promise<ContentAnalysis> {
    // Simple content analysis - in production this would use computer vision APIs
    const { width, height } = imageData;
    
    // Analyze color profile
    const colorProfile = this.analyzeColorProfile(imageData);
    
    // Determine content type based on aspect ratio and other heuristics
    const aspectRatio = width / height;
    let primaryContentType: ContentAnalysis['primaryContentType'];
    
    if (aspectRatio > 1.5) {
      primaryContentType = 'landscape';
    } else if (aspectRatio < 0.75) {
      primaryContentType = 'portrait';
    } else {
      primaryContentType = 'object';
    }
    
    // Estimate complexity based on color variance
    const complexity = colorProfile.contrast > 0.7 ? 'high' : 
                     colorProfile.contrast > 0.4 ? 'medium' : 'low';

    return {
      resolution: { width, height },
      primaryContentType,
      complexity,
      colorProfile,
      edgeComplexity: colorProfile.contrast, // Simplified
      textureComplexity: colorProfile.saturation // Simplified
    };
  }

  private analyzeColorProfile(imageData: ImageData): ContentAnalysis['colorProfile'] {
    const data = imageData.data;
    let totalR = 0, totalG = 0, totalB = 0;
    let minBrightness = 255, maxBrightness = 0;
    const pixelCount = data.length / 4;

    // Calculate averages and extremes
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      totalR += r;
      totalG += g;
      totalB += b;
      
      const brightness = (r + g + b) / 3;
      minBrightness = Math.min(minBrightness, brightness);
      maxBrightness = Math.max(maxBrightness, brightness);
    }

    const avgR = totalR / pixelCount;
    const avgG = totalG / pixelCount;
    const avgB = totalB / pixelCount;
    
    // Calculate saturation (simplified)
    const maxChannel = Math.max(avgR, avgG, avgB);
    const minChannel = Math.min(avgR, avgG, avgB);
    const saturation = maxChannel > 0 ? (maxChannel - minChannel) / maxChannel : 0;
    
    // Calculate brightness
    const brightness = (avgR + avgG + avgB) / (3 * 255);
    
    // Calculate contrast
    const contrast = (maxBrightness - minBrightness) / 255;
    
    // Determine dominant colors (simplified)
    const dominantColor = `rgb(${Math.round(avgR)}, ${Math.round(avgG)}, ${Math.round(avgB)})`;

    return {
      dominant: [dominantColor],
      saturation,
      brightness,
      contrast
    };
  }

  private async analyzeStyleReference(styleReference: StyleReference): Promise<StyleAnalysis> {
    // Simple style analysis - in production this would use specialized AI models
    const styleTypes: StyleAnalysis['styleTypes'] = [];
    
    // Determine style types based on mood and tags
    if (styleReference.mood.mood === 'dramatic' || styleReference.tags.includes('artistic')) {
      styleTypes.push('artistic');
    }
    if (styleReference.mood.era === 'classical' || styleReference.tags.includes('texture')) {
      styleTypes.push('texture');
    }
    if (styleReference.tags.includes('color') || styleReference.dominantColors.length > 3) {
      styleTypes.push('color');
    }
    if (styleReference.tags.includes('abstract')) {
      styleTypes.push('abstract');
    }
    if (styleReference.tags.includes('photo') || styleReference.mood.era === 'contemporary') {
      styleTypes.push('photographic');
    }

    // Default to artistic if no specific types detected
    if (styleTypes.length === 0) {
      styleTypes.push('artistic');
    }

    return {
      styleTypes,
      artisticPeriod: styleReference.artisticPeriod,
      colorComplexity: styleReference.dominantColors.length > 5 ? 'high' : 
                      styleReference.dominantColors.length > 2 ? 'medium' : 'low',
      textureComplexity: styleReference.complexity || 'medium',
      confidence: 0.8 // Simplified confidence score
    };
  }

  private async calculateQualityMetrics(
    original: ImageData, 
    result: ImageData
  ): Promise<QualityMetrics> {
    // Simplified quality metrics - in production would use advanced image comparison
    return {
      structuralSimilarity: 0.85, // SSIM would be calculated here
      colorAccuracy: 0.9,
      texturePreservation: 0.8,
      overallQuality: 0.85
    };
  }

  // Real-time preview generation
  async generatePreview(
    contentImage: ImageData | HTMLImageElement | string,
    styleReference: StyleReference,
    parameters: StyleParameters
  ): Promise<StylePreviewResult> {
    try {
      // Use fast preview model
      const previewModel = this.modelRegistry.get(this.previewModelId);
      if (!previewModel) {
        throw new Error('Preview model not available');
      }

      // Prepare reduced resolution content for faster processing
      const contentImageData = await this.prepareImage(contentImage);
      const previewContent = this.preparePreviewContent(contentImageData);
      
      // Execute quick style transfer
      const startTime = Date.now();
      const result = await this.executeStyleTransfer(
        previewModel,
        previewContent,
        await this.prepareStyleImage(styleReference),
        { ...parameters, detailLevel: 'low' }, // Reduce detail for speed
        {
          priority: 'speed',
          enablePreview: false,
          enableCache: true,
          outputFormat: 'jpeg',
          outputQuality: 0.8,
          maxRetries: 1,
          timeoutMs: 15000 // 15 second timeout for previews
        }
      );
      
      const processingTime = Date.now() - startTime;
      
      // Estimate full processing time
      const fullTimeEstimate = this.estimateFullProcessingTime(
        contentImageData, styleReference, parameters
      );

      return {
        success: true,
        preview: result.imageData,
        processingTime,
        estimatedFullTime: fullTimeEstimate
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Preview generation failed'
      };
    }
  }

  private preparePreviewContent(image: ImageData): ImageData {
    // Reduce resolution for faster processing
    const maxPreviewSize = 512;
    const aspectRatio = image.width / image.height;
    
    let newWidth, newHeight;
    if (aspectRatio > 1) {
      newWidth = Math.min(maxPreviewSize, image.width);
      newHeight = newWidth / aspectRatio;
    } else {
      newHeight = Math.min(maxPreviewSize, image.height);
      newWidth = newHeight * aspectRatio;
    }
    
    return this.resizeImageData(image, newWidth, newHeight);
  }

  private resizeImageData(imageData: ImageData, newWidth: number, newHeight: number): ImageData {
    // Simple resize using canvas - in production would use better algorithms
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    // Draw original image
    const originalCanvas = document.createElement('canvas');
    const originalCtx = originalCanvas.getContext('2d')!;
    originalCanvas.width = imageData.width;
    originalCanvas.height = imageData.height;
    originalCtx.putImageData(imageData, 0, 0);
    
    // Resize
    canvas.width = newWidth;
    canvas.height = newHeight;
    ctx.drawImage(originalCanvas, 0, 0, newWidth, newHeight);
    
    return ctx.getImageData(0, 0, newWidth, newHeight);
  }

  private estimateFullProcessingTime(
    contentImage: ImageData,
    styleReference: StyleReference,
    parameters: StyleParameters
  ): number {
    // Estimate based on image size, complexity, and parameters
    const pixelCount = contentImage.width * contentImage.height;
    const complexityMultiplier = parameters.detailLevel === 'high' ? 2 : 
                                parameters.detailLevel === 'medium' ? 1.5 : 1;
    
    // Base time: 30 seconds for 512x512, scale by pixel count and complexity
    const baseTime = 30000; // 30 seconds
    const sizeMultiplier = pixelCount / (512 * 512);
    
    return Math.round(baseTime * sizeMultiplier * complexityMultiplier);
  }

  // Public API methods
  getAvailableModels(): StyleModel[] {
    return Array.from(this.modelRegistry.values());
  }

  getModelById(modelId: string): StyleModel | undefined {
    return this.modelRegistry.get(modelId);
  }

  addCustomModel(model: StyleModel): void {
    this.modelRegistry.set(model.id, model);
    this.emit('model:added', model);
  }

  removeModel(modelId: string): boolean {
    const removed = this.modelRegistry.delete(modelId);
    if (removed) {
      this.emit('model:removed', modelId);
    }
    return removed;
  }

  // Cleanup
  dispose(): void {
    this.modelRegistry.clear();
    this.removeAllListeners();
  }
}