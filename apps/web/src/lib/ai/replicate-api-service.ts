'use client';

import { BaseAIService, AIProvider, AIRequest, AIResponse } from './ai-service-base';
import { EventEmitter } from 'events';

// Replicate-specific types and interfaces
export interface ReplicateModelConfig {
  modelId: string;
  version: string;
  parameters: Record<string, any>;
  priority: number;
  costPerGeneration: number;
  averageProcessingTime: number;
  qualityScore: number;
  specializations: string[];
  category: string;
  inputTypes: string[];
  outputTypes: string[];
  maxResolution?: { width: number; height: number };
}

export interface AIWorkflowStep {
  id: string;
  modelId: string;
  parameters: Record<string, any>;
  inputFrom?: string;
  conditions?: WorkflowCondition[];
  fallbackModel?: string;
  maxRetries: number;
  timeout: number;
  type: 'generation' | 'enhancement' | 'analysis' | 'style-transfer';
}

export interface WorkflowCondition {
  type: 'input-type' | 'file-size' | 'resolution' | 'custom';
  operator: 'equals' | 'contains' | 'greater-than' | 'less-than';
  value: any;
}

export interface AIGenerationRequest {
  workflowId?: string;
  steps: AIWorkflowStep[];
  inputs: Record<string, any>;
  options: GenerationOptions;
  callbacks?: WorkflowCallbacks;
  priority?: 'low' | 'normal' | 'high';
  batchId?: string;
}

export interface GenerationOptions {
  quality: 'fast' | 'balanced' | 'quality';
  budget?: number;
  timeout?: number;
  enableCaching?: boolean;
  enableRetry?: boolean;
  progressCallback?: (progress: GenerationProgress) => void;
}

export interface WorkflowCallbacks {
  onStepStart?: (step: AIWorkflowStep) => void;
  onStepComplete?: (step: AIWorkflowStep, result: StepResult) => void;
  onStepError?: (step: AIWorkflowStep, error: Error) => void;
  onWorkflowComplete?: (result: WorkflowResult) => void;
  onWorkflowError?: (error: Error) => void;
}

export interface GenerationProgress {
  step: number;
  totalSteps: number;
  status: 'starting' | 'processing' | 'completed' | 'failed';
  message: string;
  eta?: number;
  progress?: number; // 0-100
}

export interface StepResult {
  stepId: string;
  modelId: string;
  input: any;
  output: any;
  executionTime: number;
  success: boolean;
  error?: string;
  metadata: {
    cacheHit?: boolean;
    cost?: number;
    resolution?: { width: number; height: number };
    fileSize?: number;
    [key: string]: any;
  };
}

export interface WorkflowResult {
  workflowId: string;
  steps: StepResult[];
  finalOutput: any;
  metadata: {
    totalCost: number;
    totalTime: number;
    cacheHits: number;
    errors: string[];
  };
}

export interface GenerationResult {
  id: string;
  modelId: string;
  output: string | string[]; // URL(s) to generated image(s)
  parameters: Record<string, any>;
  cost: number;
  processingTime: number;
  fromCache: boolean;
  cacheAge?: number;
  metadata: {
    resolution?: { width: number; height: number };
    fileSize?: number;
    seed?: number;
    model_version?: string;
  };
}

export interface ModelAnalytics {
  popularModels: Array<{ modelId: string; usage: number; avgRating: number }>;
  performanceMetrics: Array<{ modelId: string; avgTime: number; successRate: number }>;
  costAnalysis: { 
    totalSpent: number; 
    avgCostPerGeneration: number; 
    topCostlyModels: string[] 
  };
  qualityTrends: Array<{ modelId: string; trend: 'improving' | 'stable' | 'declining' }>;
  recommendations: string[];
}

export interface TimeRange {
  start: Date;
  end: Date;
}

// Model registry for managing available models
export class ModelRegistry extends EventEmitter {
  private models: Map<string, ReplicateModelConfig> = new Map();
  private categories: Map<string, string[]> = new Map();
  private performanceHistory: Map<string, any> = new Map();

  constructor() {
    super();
    this.loadDefaultModels();
  }

  private loadDefaultModels(): void {
    const defaultModels: ReplicateModelConfig[] = [
      {
        modelId: 'stability-ai/sdxl',
        version: 'latest',
        parameters: {
          prompt: { type: 'string', required: true },
          negative_prompt: { type: 'string', required: false },
          width: { type: 'number', default: 1024, range: [512, 2048] },
          height: { type: 'number', default: 1024, range: [512, 2048] },
          num_inference_steps: { type: 'number', default: 30, range: [1, 100] },
          guidance_scale: { type: 'number', default: 7.5, range: [1, 20] },
          seed: { type: 'number', required: false }
        },
        specializations: ['text-to-image', 'high-quality', 'versatile'],
        costPerGeneration: 0.12,
        averageProcessingTime: 25,
        qualityScore: 9.2,
        priority: 10,
        category: 'text-to-image',
        inputTypes: ['text'],
        outputTypes: ['image'],
        maxResolution: { width: 2048, height: 2048 }
      },
      {
        modelId: 'stability-ai/sdxl-lightning',
        version: 'latest',
        parameters: {
          prompt: { type: 'string', required: true },
          negative_prompt: { type: 'string', required: false },
          width: { type: 'number', default: 1024, range: [512, 1536] },
          height: { type: 'number', default: 1024, range: [512, 1536] },
          num_inference_steps: { type: 'number', default: 4, range: [1, 8] },
          guidance_scale: { type: 'number', default: 1.0, range: [0, 2] },
          seed: { type: 'number', required: false }
        },
        specializations: ['text-to-image', 'fast', 'lightning'],
        costPerGeneration: 0.08,
        averageProcessingTime: 8,
        qualityScore: 8.7,
        priority: 8,
        category: 'text-to-image',
        inputTypes: ['text'],
        outputTypes: ['image'],
        maxResolution: { width: 1536, height: 1536 }
      },
      {
        modelId: 'playgroundai/playground-v2.5-1024px-aesthetic',
        version: 'latest',
        parameters: {
          prompt: { type: 'string', required: true },
          negative_prompt: { type: 'string', required: false },
          width: { type: 'number', default: 1024, range: [256, 1024] },
          height: { type: 'number', default: 1024, range: [256, 1024] },
          guidance_scale: { type: 'number', default: 3.0, range: [1, 20] },
          scheduler: { type: 'string', default: 'K_EULER_ANCESTRAL' },
          num_inference_steps: { type: 'number', default: 50, range: [1, 100] },
          seed: { type: 'number', required: false }
        },
        specializations: ['text-to-image', 'aesthetic', 'artistic'],
        costPerGeneration: 0.10,
        averageProcessingTime: 15,
        qualityScore: 9.0,
        priority: 9,
        category: 'text-to-image',
        inputTypes: ['text'],
        outputTypes: ['image'],
        maxResolution: { width: 1024, height: 1024 }
      },
      {
        modelId: 'tencentarc/gfpgan',
        version: 'latest',
        parameters: {
          img: { type: 'image', required: true },
          version: { type: 'string', default: 'v1.4', options: ['v1.2', 'v1.3', 'v1.4'] },
          scale: { type: 'number', default: 2, range: [1, 4] }
        },
        specializations: ['face-restoration', 'upscaling', 'enhancement'],
        costPerGeneration: 0.05,
        averageProcessingTime: 15,
        qualityScore: 8.7,
        priority: 7,
        category: 'image-enhancement',
        inputTypes: ['image'],
        outputTypes: ['image']
      },
      {
        modelId: 'pharmapsychotic/clip-interrogator',
        version: 'latest',
        parameters: {
          image: { type: 'image', required: true },
          clip_model_name: { 
            type: 'string', 
            default: 'ViT-L-14/openai',
            options: ['ViT-L-14/openai', 'ViT-H-14/laion2b_s32b_b79k']
          },
          mode: {
            type: 'string',
            default: 'best',
            options: ['best', 'classic', 'fast', 'negative']
          }
        },
        specializations: ['image-analysis', 'prompt-generation', 'style-detection'],
        costPerGeneration: 0.02,
        averageProcessingTime: 8,
        qualityScore: 8.9,
        priority: 6,
        category: 'image-analysis',
        inputTypes: ['image'],
        outputTypes: ['text']
      },
      {
        modelId: 'stability-ai/stable-diffusion-img2img',
        version: 'latest',
        parameters: {
          image: { type: 'image', required: true },
          prompt: { type: 'string', required: true },
          negative_prompt: { type: 'string', required: false },
          strength: { type: 'number', default: 0.8, range: [0.1, 1.0] },
          guidance_scale: { type: 'number', default: 7.5, range: [1, 20] },
          num_inference_steps: { type: 'number', default: 30, range: [1, 100] },
          seed: { type: 'number', required: false }
        },
        specializations: ['image-to-image', 'style-transfer', 'modification'],
        costPerGeneration: 0.09,
        averageProcessingTime: 20,
        qualityScore: 8.8,
        priority: 8,
        category: 'image-to-image',
        inputTypes: ['image', 'text'],
        outputTypes: ['image']
      },
      {
        modelId: 'stability-ai/stable-diffusion-inpainting',
        version: 'latest',
        parameters: {
          image: { type: 'image', required: true },
          mask: { type: 'image', required: true },
          prompt: { type: 'string', required: true },
          negative_prompt: { type: 'string', required: false },
          guidance_scale: { type: 'number', default: 7.5, range: [1, 20] },
          num_inference_steps: { type: 'number', default: 30, range: [1, 100] },
          seed: { type: 'number', required: false }
        },
        specializations: ['inpainting', 'object-removal', 'editing'],
        costPerGeneration: 0.11,
        averageProcessingTime: 22,
        qualityScore: 8.6,
        priority: 7,
        category: 'inpainting',
        inputTypes: ['image', 'text'],
        outputTypes: ['image']
      }
    ];

    // Register models
    defaultModels.forEach(model => {
      this.models.set(model.modelId, model);
      
      // Categorize models
      if (!this.categories.has(model.category)) {
        this.categories.set(model.category, []);
      }
      this.categories.get(model.category)!.push(model.modelId);
    });

    this.emit('models:loaded', defaultModels.length);
  }

  getModel(modelId: string): ReplicateModelConfig | undefined {
    return this.models.get(modelId);
  }

  getAllModels(): ReplicateModelConfig[] {
    return Array.from(this.models.values());
  }

  getModelsByCategory(category: string): ReplicateModelConfig[] {
    const modelIds = this.categories.get(category) || [];
    return modelIds.map(id => this.models.get(id)!).filter(Boolean);
  }

  findModelsBySpecialization(specialization: string): ReplicateModelConfig[] {
    return Array.from(this.models.values())
      .filter(model => model.specializations.includes(specialization));
  }

  getModelRecommendations(
    task: string, 
    budget: number, 
    qualityLevel: number
  ): ReplicateModelConfig[] {
    return Array.from(this.models.values())
      .filter(model => model.specializations.includes(task))
      .filter(model => model.costPerGeneration <= budget)
      .filter(model => model.qualityScore >= qualityLevel)
      .sort((a, b) => b.qualityScore - a.qualityScore);
  }

  addModel(model: ReplicateModelConfig): void {
    this.models.set(model.modelId, model);
    
    if (!this.categories.has(model.category)) {
      this.categories.set(model.category, []);
    }
    this.categories.get(model.category)!.push(model.modelId);
    
    this.emit('model:added', model);
  }

  removeModel(modelId: string): boolean {
    const model = this.models.get(modelId);
    if (!model) return false;

    this.models.delete(modelId);
    
    const categoryModels = this.categories.get(model.category);
    if (categoryModels) {
      const index = categoryModels.indexOf(modelId);
      if (index > -1) {
        categoryModels.splice(index, 1);
      }
    }
    
    this.emit('model:removed', modelId);
    return true;
  }

  getCategories(): string[] {
    return Array.from(this.categories.keys());
  }

  updateModelPerformance(modelId: string, metrics: any): void {
    this.performanceHistory.set(modelId, {
      ...this.performanceHistory.get(modelId),
      ...metrics,
      lastUpdated: Date.now()
    });
    
    this.emit('model:performance:updated', { modelId, metrics });
  }
}

// Cost tracking and budget management
export class CostTracker extends EventEmitter {
  private generations: Array<{
    modelId: string;
    parameters: any;
    cost: number;
    timestamp: number;
  }> = [];
  
  private dailyBudget: number = 10.0; // Default $10 daily budget
  private monthlyBudget: number = 300.0; // Default $300 monthly budget

  recordGeneration(generation: {
    modelId: string;
    parameters: any;
    cost: number;
    timestamp: number;
  }): void {
    this.generations.push(generation);
    
    // Keep only last 1000 generations to prevent memory issues
    if (this.generations.length > 1000) {
      this.generations = this.generations.slice(-1000);
    }
    
    this.emit('generation:recorded', generation);
    
    // Check budget limits
    const dailyCost = this.getDailyCost();
    const monthlyCost = this.getMonthlyCost();
    
    if (dailyCost > this.dailyBudget) {
      this.emit('budget:daily:exceeded', { cost: dailyCost, budget: this.dailyBudget });
    }
    
    if (monthlyCost > this.monthlyBudget) {
      this.emit('budget:monthly:exceeded', { cost: monthlyCost, budget: this.monthlyBudget });
    }
  }

  getDailyCost(): number {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();
    
    return this.generations
      .filter(gen => gen.timestamp >= todayTimestamp)
      .reduce((total, gen) => total + gen.cost, 0);
  }

  getMonthlyCost(): number {
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);
    const monthTimestamp = thisMonth.getTime();
    
    return this.generations
      .filter(gen => gen.timestamp >= monthTimestamp)
      .reduce((total, gen) => total + gen.cost, 0);
  }

  getCostAnalysis(timeRange: TimeRange): any {
    const filteredGenerations = this.generations.filter(
      gen => gen.timestamp >= timeRange.start.getTime() && 
             gen.timestamp <= timeRange.end.getTime()
    );
    
    const totalSpent = filteredGenerations.reduce((total, gen) => total + gen.cost, 0);
    const avgCostPerGeneration = filteredGenerations.length > 0 ? 
      totalSpent / filteredGenerations.length : 0;
    
    const modelCosts = new Map<string, number>();
    filteredGenerations.forEach(gen => {
      modelCosts.set(gen.modelId, (modelCosts.get(gen.modelId) || 0) + gen.cost);
    });
    
    const topCostlyModels = Array.from(modelCosts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([modelId]) => modelId);
    
    return {
      totalSpent,
      avgCostPerGeneration,
      topCostlyModels,
      generationCount: filteredGenerations.length
    };
  }

  setDailyBudget(budget: number): void {
    this.dailyBudget = budget;
    this.emit('budget:daily:updated', budget);
  }

  setMonthlyBudget(budget: number): void {
    this.monthlyBudget = budget;
    this.emit('budget:monthly:updated', budget);
  }

  getDailyBudget(): number {
    return this.dailyBudget;
  }

  getMonthlyBudget(): number {
    return this.monthlyBudget;
  }

  getRemainingDailyBudget(): number {
    return Math.max(0, this.dailyBudget - this.getDailyCost());
  }

  getRemainingMonthlyBudget(): number {
    return Math.max(0, this.monthlyBudget - this.getMonthlyCost());
  }
}

// Performance monitoring for models
export class ModelPerformanceMonitor extends EventEmitter {
  private metrics: Map<string, any> = new Map();

  recordModelPerformance(modelId: string, metrics: {
    processingTime: number;
    success: boolean;
    outputQuality?: number;
    userRating?: number;
  }): void {
    const currentMetrics = this.metrics.get(modelId) || {
      totalGenerations: 0,
      successfulGenerations: 0,
      totalProcessingTime: 0,
      qualityRatings: [],
      userRatings: [],
      lastUpdated: Date.now()
    };

    currentMetrics.totalGenerations++;
    currentMetrics.totalProcessingTime += metrics.processingTime;
    
    if (metrics.success) {
      currentMetrics.successfulGenerations++;
    }
    
    if (metrics.outputQuality !== undefined) {
      currentMetrics.qualityRatings.push(metrics.outputQuality);
      // Keep only last 100 ratings
      if (currentMetrics.qualityRatings.length > 100) {
        currentMetrics.qualityRatings = currentMetrics.qualityRatings.slice(-100);
      }
    }
    
    if (metrics.userRating !== undefined) {
      currentMetrics.userRatings.push(metrics.userRating);
      // Keep only last 100 ratings
      if (currentMetrics.userRatings.length > 100) {
        currentMetrics.userRatings = currentMetrics.userRatings.slice(-100);
      }
    }
    
    currentMetrics.lastUpdated = Date.now();
    this.metrics.set(modelId, currentMetrics);
    
    this.emit('model:performance:recorded', { modelId, metrics: currentMetrics });
  }

  getModelPerformance(modelId: string): any {
    const metrics = this.metrics.get(modelId);
    if (!metrics) return null;

    const successRate = metrics.totalGenerations > 0 ? 
      metrics.successfulGenerations / metrics.totalGenerations : 0;
    
    const avgProcessingTime = metrics.totalGenerations > 0 ? 
      metrics.totalProcessingTime / metrics.totalGenerations : 0;
    
    const avgQuality = metrics.qualityRatings.length > 0 ? 
      metrics.qualityRatings.reduce((sum: number, rating: number) => sum + rating, 0) / 
      metrics.qualityRatings.length : 0;
    
    const avgUserRating = metrics.userRatings.length > 0 ? 
      metrics.userRatings.reduce((sum: number, rating: number) => sum + rating, 0) / 
      metrics.userRatings.length : 0;

    return {
      successRate,
      avgProcessingTime,
      avgQuality,
      avgUserRating,
      totalGenerations: metrics.totalGenerations,
      lastUpdated: metrics.lastUpdated
    };
  }

  getPopularModels(timeRange: TimeRange): Array<{ modelId: string; usage: number; avgRating: number }> {
    const results: Array<{ modelId: string; usage: number; avgRating: number }> = [];
    
    for (const [modelId, metrics] of this.metrics.entries()) {
      if (metrics.lastUpdated >= timeRange.start.getTime() && 
          metrics.lastUpdated <= timeRange.end.getTime()) {
        
        const avgRating = metrics.userRatings.length > 0 ? 
          metrics.userRatings.reduce((sum: number, rating: number) => sum + rating, 0) / 
          metrics.userRatings.length : 0;
        
        results.push({
          modelId,
          usage: metrics.totalGenerations,
          avgRating
        });
      }
    }
    
    return results.sort((a, b) => b.usage - a.usage);
  }

  getPerformanceMetrics(timeRange: TimeRange): Array<{ modelId: string; avgTime: number; successRate: number }> {
    const results: Array<{ modelId: string; avgTime: number; successRate: number }> = [];
    
    for (const [modelId, metrics] of this.metrics.entries()) {
      if (metrics.lastUpdated >= timeRange.start.getTime() && 
          metrics.lastUpdated <= timeRange.end.getTime()) {
        
        const avgTime = metrics.totalGenerations > 0 ? 
          metrics.totalProcessingTime / metrics.totalGenerations : 0;
        
        const successRate = metrics.totalGenerations > 0 ? 
          metrics.successfulGenerations / metrics.totalGenerations : 0;
        
        results.push({
          modelId,
          avgTime,
          successRate
        });
      }
    }
    
    return results;
  }

  getQualityTrends(timeRange: TimeRange): Array<{ modelId: string; trend: 'improving' | 'stable' | 'declining' }> {
    // Simplified trend analysis - in a real implementation, this would analyze 
    // quality ratings over time to determine trends
    return Array.from(this.metrics.keys()).map(modelId => ({
      modelId,
      trend: 'stable' as const // Simplified for now
    }));
  }
}

// Intelligent cache manager
export class IntelligentCacheManager extends EventEmitter {
  private cache: Map<string, any> = new Map();
  private cacheTimestamps: Map<string, number> = new Map();
  private cacheAccess: Map<string, number> = new Map();
  private maxCacheSize: number = 1000;

  constructor(maxSize: number = 1000) {
    super();
    this.maxCacheSize = maxSize;
    
    // Cleanup cache every 5 minutes
    setInterval(() => this.cleanupCache(), 5 * 60 * 1000);
  }

  generateCacheKey(modelId: string, parameters: Record<string, any>): string {
    const paramString = JSON.stringify(parameters, Object.keys(parameters).sort());
    return `${modelId}:${Buffer.from(paramString).toString('base64')}`;
  }

  async get(key: string): Promise<any> {
    const cached = this.cache.get(key);
    if (cached) {
      // Update access count
      this.cacheAccess.set(key, (this.cacheAccess.get(key) || 0) + 1);
      this.emit('cache:hit', key);
      return cached;
    }
    
    this.emit('cache:miss', key);
    return null;
  }

  async set(key: string, value: any, ttl: number): Promise<void> {
    // Remove old entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastRecentlyUsed();
    }
    
    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
      ttl,
      accessed: 1
    });
    
    this.cacheTimestamps.set(key, Date.now());
    this.cacheAccess.set(key, 1);
    
    this.emit('cache:set', key);
  }

  isCacheValid(cached: any): boolean {
    const now = Date.now();
    return cached && (now - cached.timestamp) < cached.ttl;
  }

  private evictLeastRecentlyUsed(): void {
    let lruKey = '';
    let lruAccess = Infinity;
    
    for (const [key, accessCount] of this.cacheAccess.entries()) {
      if (accessCount < lruAccess) {
        lruAccess = accessCount;
        lruKey = key;
      }
    }
    
    if (lruKey) {
      this.cache.delete(lruKey);
      this.cacheTimestamps.delete(lruKey);
      this.cacheAccess.delete(lruKey);
      this.emit('cache:evicted', lruKey);
    }
  }

  private cleanupCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    for (const [key, cached] of this.cache.entries()) {
      if (!this.isCacheValid(cached)) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.cacheTimestamps.delete(key);
      this.cacheAccess.delete(key);
    });
    
    if (expiredKeys.length > 0) {
      this.emit('cache:cleanup', expiredKeys.length);
    }
  }

  getCacheStats(): { size: number; maxSize: number; hitRate: number; memoryUsage: number } {
    const totalRequests = Array.from(this.cacheAccess.values()).reduce((sum, count) => sum + count, 0);
    const cacheHits = Array.from(this.cacheAccess.values()).filter(count => count > 1).length;
    const hitRate = totalRequests > 0 ? cacheHits / totalRequests : 0;
    
    // Rough memory usage estimation
    const memoryUsage = this.cache.size * 1024; // Assume 1KB per cache entry on average
    
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate,
      memoryUsage
    };
  }

  clear(): void {
    this.cache.clear();
    this.cacheTimestamps.clear();
    this.cacheAccess.clear();
    this.emit('cache:cleared');
  }
}

// Main Enhanced Replicate Service
export class EnhancedReplicateService extends BaseAIService {
  private modelRegistry: ModelRegistry;
  private costTracker: CostTracker;
  private performanceMonitor: ModelPerformanceMonitor;
  private cacheManager: IntelligentCacheManager;
  private apiKey: string;
  private baseUrl: string = 'https://api.replicate.com/v1';
  private requestQueue: Map<string, Promise<any>> = new Map();

  constructor(provider: AIProvider & { apiKey: string }) {
    super(provider);
    
    this.apiKey = provider.apiKey;
    if (provider.baseUrl) {
      this.baseUrl = provider.baseUrl;
    }
    
    this.modelRegistry = new ModelRegistry();
    this.costTracker = new CostTracker();
    this.performanceMonitor = new ModelPerformanceMonitor();
    this.cacheManager = new IntelligentCacheManager();
    
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    this.costTracker.on('budget:daily:exceeded', (data) => {
      console.warn('Daily budget exceeded:', data);
      this.emit('budget:exceeded', { type: 'daily', ...data });
    });
    
    this.costTracker.on('budget:monthly:exceeded', (data) => {
      console.warn('Monthly budget exceeded:', data);
      this.emit('budget:exceeded', { type: 'monthly', ...data });
    });
  }

  // Base AI Service implementation
  async makeRequest(request: AIRequest): Promise<AIResponse> {
    // This is a generic request handler - specific generation methods should be used instead
    throw new Error('Use specific generation methods like generateWithCache instead of makeRequest');
  }

  // Intelligent model selection
  async selectOptimalModel(
    task: string, 
    inputType: string, 
    qualityRequirement: 'fast' | 'balanced' | 'quality',
    budget?: number
  ): Promise<ReplicateModelConfig> {
    const candidates = this.modelRegistry.getAllModels()
      .filter(model => model.specializations.includes(task))
      .filter(model => model.inputTypes.includes(inputType))
      .filter(model => !budget || model.costPerGeneration <= budget);

    if (candidates.length === 0) {
      throw new Error(`No suitable models found for task: ${task} with input type: ${inputType}`);
    }

    // Score models based on requirements
    const scoredModels = candidates.map(model => ({
      model,
      score: this.calculateModelScore(model, qualityRequirement, inputType)
    }));

    // Return highest scoring model
    return scoredModels
      .sort((a, b) => b.score - a.score)[0]
      .model;
  }

  private calculateModelScore(
    model: ReplicateModelConfig, 
    quality: string, 
    inputType: string
  ): number {
    let score = 0;

    // Quality preference scoring
    if (quality === 'fast') {
      score += (30 - model.averageProcessingTime) * 2; // Prioritize speed
      score += model.costPerGeneration < 0.1 ? 30 : 0;
    } else if (quality === 'quality') {
      score += model.qualityScore * 10; // Prioritize quality
      score += model.costPerGeneration < 0.5 ? 20 : 0;
    } else { // balanced
      score += model.qualityScore * 5;
      score += (30 - model.averageProcessingTime) * 1;
      score += model.costPerGeneration < 0.2 ? 25 : 0;
    }

    // Input type compatibility
    if (model.inputTypes.includes(inputType)) {
      score += 20;
    }

    // Performance history
    const performance = this.performanceMonitor.getModelPerformance(model.modelId);
    if (performance) {
      score += performance.successRate * 30;
      score += performance.avgUserRating * 5;
    }

    // Priority bonus
    score += model.priority * 2;

    return score;
  }

  // Main generation method with caching
  async generateWithCache(
    modelId: string, 
    parameters: Record<string, any>,
    options: Partial<GenerationOptions> = {}
  ): Promise<GenerationResult> {
    const cacheKey = this.cacheManager.generateCacheKey(modelId, parameters);
    
    // Check cache first if enabled
    if (options.enableCaching !== false) {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached && this.cacheManager.isCacheValid(cached)) {
        return {
          ...cached.data,
          fromCache: true,
          cacheAge: Date.now() - cached.timestamp
        };
      }
    }
    
    // Generate new result
    const startTime = Date.now();
    const result = await this.generateImage(modelId, parameters, options);
    const processingTime = Date.now() - startTime;
    
    // Cache result with intelligent TTL
    if (options.enableCaching !== false) {
      const ttl = this.calculateCacheTTL(modelId, parameters);
      await this.cacheManager.set(cacheKey, result, ttl);
    }
    
    // Record performance metrics
    this.performanceMonitor.recordModelPerformance(modelId, {
      processingTime,
      success: true
    });
    
    // Track cost
    this.costTracker.recordGeneration({
      modelId,
      parameters,
      cost: result.cost,
      timestamp: Date.now()
    });
    
    return {
      ...result,
      fromCache: false,
      processingTime
    };
  }

  private async generateImage(
    modelId: string, 
    parameters: Record<string, any>,
    options: Partial<GenerationOptions> = {}
  ): Promise<GenerationResult> {
    const model = this.modelRegistry.getModel(modelId);
    if (!model) {
      throw new Error(`Model ${modelId} not found in registry`);
    }

    // Validate parameters
    this.validateParameters(model, parameters);
    
    // Create prediction
    const predictionData = {
      version: model.version,
      input: parameters
    };

    try {
      const response = await fetch(`${this.baseUrl}/predictions`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(predictionData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Replicate API error: ${response.status} - ${errorData.detail || response.statusText}`);
      }

      const prediction = await response.json();
      
      // Poll for completion
      const result = await this.pollForCompletion(prediction.id, options);
      
      return {
        id: prediction.id,
        modelId,
        output: result.output,
        parameters,
        cost: model.costPerGeneration,
        processingTime: 0, // Will be set by caller
        fromCache: false,
        metadata: {
          model_version: model.version,
          ...result.metadata
        }
      };
      
    } catch (error) {
      // Record failed generation
      this.performanceMonitor.recordModelPerformance(modelId, {
        processingTime: Date.now() - Date.now(),
        success: false
      });
      
      throw error;
    }
  }

  private validateParameters(model: ReplicateModelConfig, parameters: Record<string, any>): void {
    const modelParams = model.parameters;
    
    for (const [paramName, paramConfig] of Object.entries(modelParams)) {
      const value = parameters[paramName];
      
      // Check required parameters
      if (paramConfig.required && (value === undefined || value === null)) {
        throw new Error(`Required parameter '${paramName}' is missing`);
      }
      
      // Check parameter ranges
      if (value !== undefined && paramConfig.range) {
        const [min, max] = paramConfig.range;
        if (typeof value === 'number' && (value < min || value > max)) {
          throw new Error(`Parameter '${paramName}' must be between ${min} and ${max}`);
        }
      }
      
      // Check parameter options
      if (value !== undefined && paramConfig.options) {
        if (!paramConfig.options.includes(value)) {
          throw new Error(`Parameter '${paramName}' must be one of: ${paramConfig.options.join(', ')}`);
        }
      }
    }
  }

  private async pollForCompletion(
    predictionId: string, 
    options: Partial<GenerationOptions> = {}
  ): Promise<any> {
    const timeout = options.timeout || 60000; // 60 seconds default
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const response = await fetch(`${this.baseUrl}/predictions/${predictionId}`, {
        headers: {
          'Authorization': `Token ${this.apiKey}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch prediction status: ${response.statusText}`);
      }
      
      const prediction = await response.json();
      
      if (options.progressCallback) {
        options.progressCallback({
          step: 1,
          totalSteps: 1,
          status: prediction.status,
          message: `Generation ${prediction.status}`,
          progress: prediction.status === 'succeeded' ? 100 : 
                   prediction.status === 'failed' ? 0 : 50
        });
      }
      
      if (prediction.status === 'succeeded') {
        return {
          output: prediction.output,
          metadata: {
            logs: prediction.logs,
            metrics: prediction.metrics
          }
        };
      } else if (prediction.status === 'failed') {
        throw new Error(`Generation failed: ${prediction.error || 'Unknown error'}`);
      } else if (prediction.status === 'canceled') {
        throw new Error('Generation was canceled');
      }
      
      // Wait 1 second before polling again
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error('Generation timed out');
  }

  private calculateCacheTTL(modelId: string, parameters: Record<string, any>): number {
    // Deterministic outputs cache longer
    if (parameters.seed && typeof parameters.seed === 'number') {
      return 30 * 24 * 60 * 60 * 1000; // 30 days
    }
    
    // Style transfer and similar operations cache for moderate time
    if (modelId.includes('style') || modelId.includes('transfer')) {
      return 7 * 24 * 60 * 60 * 1000; // 7 days
    }
    
    // Creative generation cache for short time
    return 24 * 60 * 60 * 1000; // 1 day
  }

  // Batch processing with optimization
  async processBatch(requests: AIGenerationRequest[]): Promise<WorkflowResult[]> {
    // Group requests by model for optimal batching
    const modelGroups = this.groupRequestsByModel(requests);
    const results: WorkflowResult[] = [];
    
    for (const [modelId, modelRequests] of modelGroups) {
      // Process each model group with rate limiting
      const batchSize = this.getBatchSizeForModel(modelId);
      const batches = this.chunkArray(modelRequests, batchSize);
      
      for (const batch of batches) {
        const batchPromises = batch.map(req => this.executeWorkflow(req));
        const batchResults = await Promise.allSettled(batchPromises);
        
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            console.error('Batch request failed:', result.reason);
            // Handle individual batch failures
            this.handleBatchFailure(batch[index], result.reason);
          }
        });
        
        // Respect rate limits between batches
        await this.waitBetweenBatches(modelId);
      }
    }
    
    return results;
  }

  private groupRequestsByModel(requests: AIGenerationRequest[]): Map<string, AIGenerationRequest[]> {
    const groups = new Map<string, AIGenerationRequest[]>();
    
    requests.forEach(request => {
      const primaryModelId = request.steps[0]?.modelId;
      if (primaryModelId) {
        if (!groups.has(primaryModelId)) {
          groups.set(primaryModelId, []);
        }
        groups.get(primaryModelId)!.push(request);
      }
    });
    
    return groups;
  }

  private getBatchSizeForModel(modelId: string): number {
    // Conservative batch sizes to respect rate limits
    const model = this.modelRegistry.getModel(modelId);
    if (!model) return 3;
    
    // Faster models can handle more concurrent requests
    if (model.averageProcessingTime < 10) return 5;
    if (model.averageProcessingTime < 20) return 3;
    return 2;
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private async waitBetweenBatches(modelId: string): Promise<void> {
    // Conservative rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
  }

  private handleBatchFailure(request: AIGenerationRequest, error: any): void {
    console.error('Batch request failed:', request.workflowId, error);
    this.emit('batch:request:failed', { request, error });
  }

  // Workflow execution (simplified for this implementation)
  async executeWorkflow(request: AIGenerationRequest): Promise<WorkflowResult> {
    const workflowId = request.workflowId || `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();
    const results: StepResult[] = [];
    let totalCost = 0;
    let cacheHits = 0;
    const errors: string[] = [];
    
    try {
      for (const step of request.steps) {
        try {
          const stepResult = await this.executeWorkflowStep(step, request.inputs);
          results.push(stepResult);
          totalCost += stepResult.metadata.cost || 0;
          if (stepResult.metadata.cacheHit) cacheHits++;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Step ${step.id}: ${errorMessage}`);
          
          // Try fallback model if available
          if (step.fallbackModel) {
            try {
              const fallbackStep = { ...step, modelId: step.fallbackModel };
              const stepResult = await this.executeWorkflowStep(fallbackStep, request.inputs);
              results.push(stepResult);
              totalCost += stepResult.metadata.cost || 0;
              if (stepResult.metadata.cacheHit) cacheHits++;
            } catch (fallbackError) {
              errors.push(`Fallback failed for step ${step.id}: ${fallbackError}`);
              throw error; // Rethrow original error
            }
          } else {
            throw error;
          }
        }
      }
      
      const finalOutput = results[results.length - 1]?.output;
      
      return {
        workflowId,
        steps: results,
        finalOutput,
        metadata: {
          totalCost,
          totalTime: Date.now() - startTime,
          cacheHits,
          errors
        }
      };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      
      return {
        workflowId,
        steps: results,
        finalOutput: null,
        metadata: {
          totalCost,
          totalTime: Date.now() - startTime,
          cacheHits,
          errors
        }
      };
    }
  }

  private async executeWorkflowStep(step: AIWorkflowStep, inputs: Record<string, any>): Promise<StepResult> {
    const startTime = Date.now();
    
    try {
      const parameters = this.prepareStepParameters(step.parameters, inputs);
      const result = await this.generateWithCache(step.modelId, parameters);
      
      return {
        stepId: step.id,
        modelId: step.modelId,
        input: parameters,
        output: result.output,
        executionTime: Date.now() - startTime,
        success: true,
        metadata: {
          cacheHit: result.fromCache,
          cost: result.cost,
          resolution: result.metadata.resolution,
          fileSize: result.metadata.fileSize
        }
      };
    } catch (error) {
      return {
        stepId: step.id,
        modelId: step.modelId,
        input: step.parameters,
        output: null,
        executionTime: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {}
      };
    }
  }

  private prepareStepParameters(parameters: Record<string, any>, inputs: Record<string, any>): Record<string, any> {
    const prepared = { ...parameters };
    
    // Replace input placeholders
    for (const [key, value] of Object.entries(prepared)) {
      if (typeof value === 'string' && value.startsWith('$input.')) {
        const inputKey = value.substring(7); // Remove '$input.'
        if (inputs[inputKey] !== undefined) {
          prepared[key] = inputs[inputKey];
        }
      }
    }
    
    return prepared;
  }

  // Public API methods

  // Model management
  getAvailableModels(): ReplicateModelConfig[] {
    return this.modelRegistry.getAllModels();
  }

  getModelsByCategory(category: string): ReplicateModelConfig[] {
    return this.modelRegistry.getModelsByCategory(category);
  }

  getModelCategories(): string[] {
    return this.modelRegistry.getCategories();
  }

  async findOptimalModel(
    task: string,
    inputType: string = 'text',
    quality: 'fast' | 'balanced' | 'quality' = 'balanced',
    budget?: number
  ): Promise<ReplicateModelConfig> {
    return this.selectOptimalModel(task, inputType, quality, budget);
  }

  // Cost management
  getCostTracker(): CostTracker {
    return this.costTracker;
  }

  getDailyCost(): number {
    return this.costTracker.getDailyCost();
  }

  getMonthlyCost(): number {
    return this.costTracker.getMonthlyCost();
  }

  setDailyBudget(budget: number): void {
    this.costTracker.setDailyBudget(budget);
  }

  setMonthlyBudget(budget: number): void {
    this.costTracker.setMonthlyBudget(budget);
  }

  // Performance analytics
  getModelAnalytics(timeRange: TimeRange): ModelAnalytics {
    return {
      popularModels: this.performanceMonitor.getPopularModels(timeRange),
      performanceMetrics: this.performanceMonitor.getPerformanceMetrics(timeRange),
      costAnalysis: this.costTracker.getCostAnalysis(timeRange),
      qualityTrends: this.performanceMonitor.getQualityTrends(timeRange),
      recommendations: this.generateModelRecommendations()
    };
  }

  private generateModelRecommendations(): string[] {
    // Generate intelligent recommendations based on usage patterns
    const recommendations: string[] = [];
    
    const dailyCost = this.costTracker.getDailyCost();
    const remainingBudget = this.costTracker.getRemainingDailyBudget();
    
    if (remainingBudget < dailyCost * 0.2) {
      recommendations.push('Consider using faster, more cost-effective models for remaining daily budget');
    }
    
    if (dailyCost > 5) {
      recommendations.push('High daily usage detected - consider batch processing for better cost efficiency');
    }
    
    recommendations.push('Try SDXL-Lightning for faster iterations during development');
    recommendations.push('Use deterministic seeds to improve cache hit rates');
    
    return recommendations;
  }

  // Cache management
  clearCache(): void {
    this.cacheManager.clear();
  }

  getCacheStats(): any {
    return this.cacheManager.getCacheStats();
  }

  // Cleanup
  dispose(): void {
    this.cacheManager.removeAllListeners();
    this.costTracker.removeAllListeners();
    this.performanceMonitor.removeAllListeners();
    this.modelRegistry.removeAllListeners();
    this.removeAllListeners();
  }
}