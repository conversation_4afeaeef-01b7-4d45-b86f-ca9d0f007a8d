'use client';

import OpenAI from 'openai';
import { 
  BaseAIService, 
  AIProvider, 
  AIRequest, 
  AIResponse,
  CompletionItem,
  CodeSuggestion,
  AnalysisResult,
  DebugAnalysis,
  TestGeneration,
  CompletionItemSchema,
  CodeSuggestionSchema,
  AnalysisResultSchema,
  DebugAnalysisSchema,
  TestGenerationSchema,
} from './ai-service-base';

export class OpenAIService extends BaseAIService {
  private client: OpenAI;

  constructor(provider: AIProvider) {
    super(provider);
    
    if (!provider.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.client = new OpenAI({
      apiKey: provider.apiKey,
      baseURL: provider.baseUrl,
      dangerouslyAllowBrowser: true, // Note: In production, API calls should go through your backend
    });
  }

  async makeRequest(request: AIRequest): Promise<AIResponse> {
    if (!this.validateRequest(request)) {
      return {
        success: false,
        error: 'Invalid request: prompt is required',
      };
    }

    try {
      const sanitizedPrompt = this.sanitizeInput(request.prompt);
      const fullPrompt = this.buildPrompt({ ...request, prompt: sanitizedPrompt });

      const completion = await this.client.chat.completions.create({
        model: this.provider.model || 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: 'You are an expert AI coding assistant. Provide helpful, accurate, and concise responses.',
          },
          {
            role: 'user',
            content: fullPrompt,
          },
        ],
        max_tokens: this.provider.maxTokens || 4000,
        temperature: this.provider.temperature || 0.1,
        stream: false,
      });

      const responseText = completion.choices[0]?.message?.content || '';
      
      return {
        success: true,
        data: responseText,
        metadata: {
          model: completion.model,
          tokens: completion.usage?.total_tokens,
        },
      };
    } catch (error) {
      console.error('OpenAI API error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'OpenAI API request failed',
      };
    }
  }

  // D3.1a: Code Completion Service
  async getCodeCompletions(request: AIRequest): Promise<AIResponse<CompletionItem[]>> {
    const completionPrompt = this.buildCompletionPrompt(request);
    
    return this.requestWithCache(
      { ...request, prompt: completionPrompt },
      async (req) => {
        const response = await this.makeRequest(req);
        if (!response.success) return response;

        try {
          const completions = this.parseCompletionsFromResponse(response.data);
          const validatedCompletions = completions
            .map(item => CompletionItemSchema.safeParse(item))
            .filter(result => result.success)
            .map(result => result.data!);

          return {
            success: true,
            data: validatedCompletions,
            metadata: response.metadata,
          };
        } catch (error) {
          return {
            success: false,
            error: 'Failed to parse completion response',
          };
        }
      }
    );
  }

  private buildCompletionPrompt(request: AIRequest): string {
    return `You are a code completion assistant. Given the following code context, provide intelligent code completions.

Language: ${request.language || 'unknown'}
File: ${request.fileName || 'unknown'}
${request.context ? `\nCode Context:\n${request.context}` : ''}

Current input: ${request.prompt}

Provide 5-10 relevant code completions in JSON format with this structure:
{
  "completions": [
    {
      "label": "completion text",
      "insertText": "text to insert",
      "detail": "brief description",
      "documentation": "detailed explanation",
      "kind": "function|variable|class|interface|module|property|method|snippet",
      "priority": 1-10
    }
  ]
}

Focus on:
1. Context-aware suggestions
2. Language-specific best practices
3. Common patterns and idioms
4. Error prevention
5. Performance considerations

Respond only with valid JSON.`;
  }

  private parseCompletionsFromResponse(response: string): CompletionItem[] {
    try {
      const parsed = JSON.parse(response);
      return parsed.completions || [];
    } catch {
      // Fallback: try to extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const parsed = JSON.parse(jsonMatch[0]);
          return parsed.completions || [];
        } catch {
          return [];
        }
      }
      return [];
    }
  }

  // D3.1b: AI Code Suggestions Service  
  async getCodeSuggestions(request: AIRequest): Promise<AIResponse<CodeSuggestion[]>> {
    const suggestionsPrompt = this.buildSuggestionsPrompt(request);
    
    return this.requestWithCache(
      { ...request, prompt: suggestionsPrompt },
      async (req) => {
        const response = await this.makeRequest(req);
        if (!response.success) return response;

        try {
          const suggestions = this.parseSuggestionsFromResponse(response.data);
          const validatedSuggestions = suggestions
            .map(item => CodeSuggestionSchema.safeParse(item))
            .filter(result => result.success)
            .map(result => result.data!);

          return {
            success: true,
            data: validatedSuggestions,
            metadata: response.metadata,
          };
        } catch (error) {
          return {
            success: false,
            error: 'Failed to parse suggestions response',
          };
        }
      }
    );
  }

  private buildSuggestionsPrompt(request: AIRequest): string {
    return `You are an expert code reviewer and refactoring assistant. Analyze the provided code and suggest improvements.

Language: ${request.language || 'unknown'}
File: ${request.fileName || 'unknown'}

Code to analyze:
${request.prompt}

${request.context ? `\nAdditional Context:\n${request.context}` : ''}

Provide code improvement suggestions in JSON format:
{
  "suggestions": [
    {
      "type": "refactor|optimize|fix|improve|security|performance",
      "title": "Short descriptive title",
      "description": "Detailed explanation of the suggestion",
      "codeChanges": [
        {
          "startLine": 1,
          "endLine": 5,
          "originalCode": "original code block",
          "suggestedCode": "improved code block",
          "explanation": "why this change is beneficial"
        }
      ],
      "confidence": 0.85
    }
  ]
}

Focus on:
1. Code quality improvements
2. Performance optimizations
3. Security vulnerabilities
4. Best practices
5. Maintainability enhancements
6. Bug fixes
7. Refactoring opportunities

Respond only with valid JSON.`;
  }

  private parseSuggestionsFromResponse(response: string): CodeSuggestion[] {
    try {
      const parsed = JSON.parse(response);
      return parsed.suggestions || [];
    } catch {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const parsed = JSON.parse(jsonMatch[0]);
          return parsed.suggestions || [];
        } catch {
          return [];
        }
      }
      return [];
    }
  }

  // D3.2a: Code Analysis & Review Service
  async analyzeCode(request: AIRequest): Promise<AIResponse<AnalysisResult>> {
    const analysisPrompt = this.buildAnalysisPrompt(request);
    
    return this.requestWithCache(
      { ...request, prompt: analysisPrompt },
      async (req) => {
        const response = await this.makeRequest(req);
        if (!response.success) return response;

        try {
          const analysis = this.parseAnalysisFromResponse(response.data);
          const validatedAnalysis = AnalysisResultSchema.safeParse(analysis);
          
          if (!validatedAnalysis.success) {
            return {
              success: false,
              error: 'Invalid analysis response format',
            };
          }

          return {
            success: true,
            data: validatedAnalysis.data,
            metadata: response.metadata,
          };
        } catch (error) {
          return {
            success: false,
            error: 'Failed to parse analysis response',
          };
        }
      }
    );
  }

  private buildAnalysisPrompt(request: AIRequest): string {
    return `You are a senior code reviewer performing a comprehensive code analysis. Analyze the provided code for issues, quality metrics, and provide recommendations.

Language: ${request.language || 'unknown'}
File: ${request.fileName || 'unknown'}

Code to analyze:
${request.prompt}

${request.context ? `\nProject Context:\n${request.context}` : ''}

Provide a comprehensive analysis in JSON format:
{
  "issues": [
    {
      "type": "error|warning|info|hint",
      "severity": "critical|high|medium|low",
      "message": "description of the issue",
      "line": 10,
      "column": 5,
      "endLine": 10,
      "endColumn": 20,
      "rule": "rule identifier",
      "suggestions": ["suggestion 1", "suggestion 2"]
    }
  ],
  "metrics": {
    "complexity": 7.5,
    "maintainability": 85.2,
    "testCoverage": 75.0,
    "security": 90.0
  },
  "recommendations": [
    "Overall recommendation 1",
    "Overall recommendation 2"
  ]
}

Analyze for:
1. Syntax and logical errors
2. Security vulnerabilities
3. Performance issues
4. Code smells
5. Best practice violations
6. Maintainability concerns
7. Testing opportunities
8. Documentation gaps

Respond only with valid JSON.`;
  }

  private parseAnalysisFromResponse(response: string): AnalysisResult {
    try {
      return JSON.parse(response);
    } catch {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch {
          // Fallback empty analysis
          return {
            issues: [],
            metrics: {},
            recommendations: [],
          };
        }
      }
      return {
        issues: [],
        metrics: {},
        recommendations: [],
      };
    }
  }

  // D3.3a: Debugging Assistant Service
  async analyzeError(request: AIRequest): Promise<AIResponse<DebugAnalysis>> {
    const debugPrompt = this.buildDebugPrompt(request);
    
    return this.requestWithCache(
      { ...request, prompt: debugPrompt },
      async (req) => {
        const response = await this.makeRequest(req);
        if (!response.success) return response;

        try {
          const debugAnalysis = this.parseDebugFromResponse(response.data);
          const validatedDebug = DebugAnalysisSchema.safeParse(debugAnalysis);
          
          if (!validatedDebug.success) {
            return {
              success: false,
              error: 'Invalid debug analysis response format',
            };
          }

          return {
            success: true,
            data: validatedDebug.data,
            metadata: response.metadata,
          };
        } catch (error) {
          return {
            success: false,
            error: 'Failed to parse debug analysis response',
          };
        }
      }
    );
  }

  private buildDebugPrompt(request: AIRequest): string {
    return `You are an expert debugging assistant. Analyze the provided error and code to provide comprehensive debugging assistance.

Language: ${request.language || 'unknown'}
File: ${request.fileName || 'unknown'}

Error/Issue Description:
${request.prompt}

${request.context ? `\nCode Context:\n${request.context}` : ''}

Provide debugging analysis in JSON format:
{
  "errorType": "Type of error (e.g., SyntaxError, TypeError, etc.)",
  "errorMessage": "Clear explanation of what the error means",
  "possibleCauses": [
    "Possible cause 1",
    "Possible cause 2",
    "Possible cause 3"
  ],
  "solutions": [
    {
      "title": "Solution title",
      "description": "Detailed solution description", 
      "codeExample": "Code example if applicable",
      "priority": "high|medium|low"
    }
  ],
  "relatedDocs": [
    {
      "title": "Documentation title",
      "url": "https://example.com/docs",
      "description": "Why this documentation is relevant"
    }
  ]
}

Focus on:
1. Root cause analysis
2. Step-by-step solutions
3. Prevention strategies
4. Related documentation
5. Common pitfalls
6. Best practices to avoid similar issues

Respond only with valid JSON.`;
  }

  private parseDebugFromResponse(response: string): DebugAnalysis {
    try {
      return JSON.parse(response);
    } catch {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch {
          // Fallback empty debug analysis
          return {
            errorType: 'Unknown',
            errorMessage: 'Unable to analyze error',
            possibleCauses: [],
            solutions: [],
            relatedDocs: [],
          };
        }
      }
      return {
        errorType: 'Unknown',
        errorMessage: 'Unable to analyze error',
        possibleCauses: [],
        solutions: [],
        relatedDocs: [],
      };
    }
  }

  // D3.4a: Test Generation Service
  async generateTests(request: AIRequest): Promise<AIResponse<TestGeneration>> {
    const testPrompt = this.buildTestPrompt(request);
    
    return this.requestWithCache(
      { ...request, prompt: testPrompt },
      async (req) => {
        const response = await this.makeRequest(req);
        if (!response.success) return response;

        try {
          const testGeneration = this.parseTestFromResponse(response.data);
          const validatedTest = TestGenerationSchema.safeParse(testGeneration);
          
          if (!validatedTest.success) {
            return {
              success: false,
              error: 'Invalid test generation response format',
            };
          }

          return {
            success: true,
            data: validatedTest.data,
            metadata: response.metadata,
          };
        } catch (error) {
          return {
            success: false,
            error: 'Failed to parse test generation response',
          };
        }
      }
    );
  }

  private buildTestPrompt(request: AIRequest): string {
    return `You are an expert test engineer. Generate comprehensive tests for the provided code.

Language: ${request.language || 'unknown'}
File: ${request.fileName || 'unknown'}

Code to test:
${request.prompt}

${request.context ? `\nProject Context:\n${request.context}` : ''}

Generate tests in JSON format:
{
  "testFramework": "Jest|Mocha|PyTest|etc",
  "testCode": "Complete test code with imports and setup",
  "description": "Description of what the tests cover",
  "coverage": {
    "functions": ["function1", "function2"],
    "branches": ["if condition", "else condition"],
    "edgeCases": ["null input", "empty array", "large numbers"]
  },
  "dependencies": ["jest", "@testing-library/react"]
}

Generate tests that cover:
1. Happy path scenarios
2. Edge cases
3. Error conditions
4. Boundary values
5. Integration scenarios
6. Performance considerations
7. Security aspects

Focus on:
- Comprehensive coverage
- Clear test names
- Proper setup/teardown
- Mocking when appropriate
- Assertion best practices

Respond only with valid JSON.`;
  }

  private parseTestFromResponse(response: string): TestGeneration {
    try {
      return JSON.parse(response);
    } catch {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch {
          // Fallback empty test generation
          return {
            testFramework: 'Jest',
            testCode: '// Unable to generate tests',
            description: 'Test generation failed',
            coverage: {
              functions: [],
              branches: [],
              edgeCases: [],
            },
            dependencies: [],
          };
        }
      }
      return {
        testFramework: 'Jest',
        testCode: '// Unable to generate tests',
        description: 'Test generation failed',
        coverage: {
          functions: [],
          branches: [],
          edgeCases: [],
        },
        dependencies: [],
      };
    }
  }
}