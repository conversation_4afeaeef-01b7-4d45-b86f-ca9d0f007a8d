'use client';

import { 
  BaseProviderAdapter, 
  AIImageModel, 
  GenerationRequest, 
  GenerationResponse,
  ProviderCapabilities,
  RateLimits
} from './provider-base';

export interface MidjourneyConfig {
  apiKey: string;
  baseUrl?: string;
  discordToken?: string;
  applicationId?: string;
}

export class MidjourneyAdapter extends BaseProviderAdapter {
  private discordToken?: string;
  private applicationId?: string;
  private pollInterval: number = 2000; // 2 seconds

  constructor(config: MidjourneyConfig) {
    super({
      name: 'midjourney',
      displayName: 'Midjourney',
      apiKey: config.apiKey,
      baseUrl: config.baseUrl || 'https://api.midjourney.com/v1',
      capabilities: {
        textToImage: true,
        imageToImage: true,
        inpainting: false,
        outpainting: true,
        upscaling: true,
        styleTransfer: true,
        controlNet: false,
        customModels: false,
        batchGeneration: false,
        realTimeGeneration: false
      },
      costPer1000: 150, // Mid-range pricing
      rateLimits: {
        requestsPerMinute: 20,
        requestsPerHour: 200,
        requestsPerDay: 2000,
        concurrent: 3
      }
    });

    this.discordToken = config.discordToken;
    this.applicationId = config.applicationId;
  }

  async initialize(): Promise<void> {
    await this.validateApiKey();
    this.models = await this.loadModels();
    this.emit('initialized', this.name);
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/account`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Midjourney API key validation failed:', error);
      return false;
    }
  }

  async loadModels(): Promise<AIImageModel[]> {
    // Midjourney models are predefined versions
    return [
      {
        id: 'mj-6.1',
        name: 'mj-6.1',
        displayName: 'Midjourney v6.1',
        description: 'Latest Midjourney model with improved coherence and text rendering',
        type: 'diffusion',
        capabilities: {
          textToImage: true,
          imageToImage: true,
          inpainting: false,
          controlNet: false,
          negativePrompts: true,
          seedControl: true,
          batchGeneration: false,
          highResolution: true,
          fastGeneration: false
        },
        parameters: {
          prompt: {
            type: 'string',
            required: true,
            description: 'Text description of the image to generate',
            example: 'A majestic eagle soaring over snow-capped mountains --ar 16:9 --v 6.1'
          },
          aspect_ratio: {
            type: 'select',
            required: false,
            default: '1:1',
            options: ['1:1', '4:3', '3:2', '16:9', '9:16', '2:3', '3:4', '5:4', '4:5'],
            description: 'Aspect ratio of the generated image'
          },
          chaos: {
            type: 'number',
            required: false,
            default: 0,
            min: 0,
            max: 100,
            description: 'Chaos value for more varied results (0-100)'
          },
          quality: {
            type: 'select',
            required: false,
            default: '1',
            options: ['0.25', '0.5', '1', '2'],
            description: 'Quality setting (higher = more detail, slower)'
          },
          stylize: {
            type: 'number',
            required: false,
            default: 100,
            min: 0,
            max: 1000,
            description: 'Stylization strength (0-1000)'
          },
          seed: {
            type: 'number',
            required: false,
            min: 0,
            max: 4294967295,
            description: 'Seed for reproducible results'
          },
          weird: {
            type: 'number',
            required: false,
            default: 0,
            min: 0,
            max: 3000,
            description: 'Weirdness factor for unusual results'
          }
        },
        pricing: {
          costPerImage: 0.15
        },
        performance: {
          averageTime: 60,
          successRate: 0.92,
          qualityScore: 9.8,
          speedScore: 4.5,
          lastUpdated: new Date()
        },
        tags: ['artistic', 'high-quality', 'creative', 'latest'],
        maxResolution: { width: 2048, height: 2048 },
        aspectRatios: ['1:1', '4:3', '3:2', '16:9', '9:16', '2:3', '3:4', '5:4', '4:5'],
        supportedFormats: ['png', 'jpeg']
      },
      {
        id: 'mj-6.0',
        name: 'mj-6.0',
        displayName: 'Midjourney v6.0',
        description: 'Previous version with excellent artistic quality and style consistency',
        type: 'diffusion',
        capabilities: {
          textToImage: true,
          imageToImage: true,
          inpainting: false,
          controlNet: false,
          negativePrompts: true,
          seedControl: true,
          batchGeneration: false,
          highResolution: true,
          fastGeneration: false
        },
        parameters: {
          prompt: {
            type: 'string',
            required: true,
            description: 'Text description of the image to generate',
            example: 'A beautiful landscape painting in impressionist style --ar 16:9 --v 6'
          },
          aspect_ratio: {
            type: 'select',
            required: false,
            default: '1:1',
            options: ['1:1', '4:3', '3:2', '16:9', '9:16', '2:3', '3:4'],
            description: 'Aspect ratio of the generated image'
          },
          chaos: {
            type: 'number',
            required: false,
            default: 0,
            min: 0,
            max: 100,
            description: 'Chaos value for more varied results'
          },
          quality: {
            type: 'select',
            required: false,
            default: '1',
            options: ['0.25', '0.5', '1', '2'],
            description: 'Quality setting'
          },
          stylize: {
            type: 'number',
            required: false,
            default: 100,
            min: 0,
            max: 1000,
            description: 'Stylization strength'
          },
          seed: {
            type: 'number',
            required: false,
            min: 0,
            max: 4294967295,
            description: 'Seed for reproducible results'
          }
        },
        pricing: {
          costPerImage: 0.12
        },
        performance: {
          averageTime: 55,
          successRate: 0.94,
          qualityScore: 9.5,
          speedScore: 5.0,
          lastUpdated: new Date()
        },
        tags: ['artistic', 'creative', 'stable'],
        maxResolution: { width: 2048, height: 2048 },
        aspectRatios: ['1:1', '4:3', '3:2', '16:9', '9:16', '2:3', '3:4'],
        supportedFormats: ['png', 'jpeg']
      },
      {
        id: 'mj-niji-6',
        name: 'mj-niji-6',
        displayName: 'Niji v6',
        description: 'Specialized model for anime and manga style illustrations',
        type: 'diffusion',
        capabilities: {
          textToImage: true,
          imageToImage: true,
          inpainting: false,
          controlNet: false,
          negativePrompts: true,
          seedControl: true,
          batchGeneration: false,
          highResolution: true,
          fastGeneration: false
        },
        parameters: {
          prompt: {
            type: 'string',
            required: true,
            description: 'Text description with anime/manga style focus',
            example: 'Anime girl with blue hair in school uniform --niji 6 --ar 2:3'
          },
          aspect_ratio: {
            type: 'select',
            required: false,
            default: '1:1',
            options: ['1:1', '4:3', '3:2', '16:9', '9:16', '2:3', '3:4'],
            description: 'Aspect ratio of the generated image'
          },
          style: {
            type: 'select',
            required: false,
            options: ['default', 'expressive', 'cute', 'scenic'],
            description: 'Niji style variant'
          },
          chaos: {
            type: 'number',
            required: false,
            default: 0,
            min: 0,
            max: 100,
            description: 'Chaos value for variation'
          },
          stylize: {
            type: 'number',
            required: false,
            default: 100,
            min: 0,
            max: 1000,
            description: 'Stylization strength'
          },
          seed: {
            type: 'number',
            required: false,
            min: 0,
            max: 4294967295,
            description: 'Seed for reproducible results'
          }
        },
        pricing: {
          costPerImage: 0.12
        },
        performance: {
          averageTime: 50,
          successRate: 0.95,
          qualityScore: 9.3,
          speedScore: 5.5,
          lastUpdated: new Date()
        },
        tags: ['anime', 'manga', 'illustration', 'specialized'],
        maxResolution: { width: 2048, height: 2048 },
        aspectRatios: ['1:1', '4:3', '3:2', '16:9', '9:16', '2:3', '3:4'],
        supportedFormats: ['png', 'jpeg']
      }
    ];
  }

  async generateImage(request: GenerationRequest): Promise<GenerationResponse> {
    await this.checkRateLimit();
    this.validateRequest(request);

    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      const model = this.getModel(request.model);
      if (!model) {
        throw new Error(`Model ${request.model} not found`);
      }

      const midjourneyRequest = this.mapToMidjourneyRequest(request);
      
      // Submit the generation request
      const response = await fetch(`${this.baseUrl}/imagine`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(midjourneyRequest)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Midjourney API error: ${response.status} - ${errorData.error || response.statusText}`);
      }

      const submitResult = await response.json();
      const jobId = submitResult.id;

      // Poll for completion
      const result = await this.pollForCompletion(jobId);
      const processingTime = Date.now() - startTime;
      const cost = model.pricing.costPerImage;

      this.recordGeneration(true, cost, processingTime, request.model);

      return {
        id: requestId,
        status: 'completed',
        images: result.images.map((imageUrl: string, index: number) => ({
          url: imageUrl,
          width: this.getImageDimensions(request).width,
          height: this.getImageDimensions(request).height,
          format: 'png',
          size: 0, // Midjourney doesn't provide file size
          seed: result.seed
        })),
        metadata: {
          model: request.model,
          prompt: request.prompt,
          parameters: midjourneyRequest,
          processingTime,
          cost,
          queue: result.queue || { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.recordGeneration(false, 0, processingTime, request.model);

      return {
        id: requestId,
        status: 'failed',
        images: [],
        metadata: {
          model: request.model,
          prompt: request.prompt,
          parameters: {},
          processingTime,
          cost: 0,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async pollForCompletion(jobId: string): Promise<any> {
    const maxAttempts = 60; // 2 minutes max
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(`${this.baseUrl}/imagine/${jobId}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to check status: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.status === 'completed') {
          return result;
        } else if (result.status === 'failed') {
          throw new Error(`Generation failed: ${result.error || 'Unknown error'}`);
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, this.pollInterval));
        attempts++;

      } catch (error) {
        if (attempts >= maxAttempts - 1) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, this.pollInterval));
        attempts++;
      }
    }

    throw new Error('Generation timed out');
  }

  private mapToMidjourneyRequest(request: GenerationRequest): any {
    let prompt = request.prompt;
    
    // Add model version to prompt
    if (request.model === 'mj-6.1') {
      prompt += ' --v 6.1';
    } else if (request.model === 'mj-6.0') {
      prompt += ' --v 6';
    } else if (request.model === 'mj-niji-6') {
      prompt += ' --niji 6';
    }

    // Add aspect ratio
    if (request.aspectRatio) {
      prompt += ` --ar ${request.aspectRatio}`;
    } else if (request.width && request.height) {
      const ratio = this.calculateAspectRatio(request.width, request.height);
      prompt += ` --ar ${ratio}`;
    }

    // Add other parameters
    if (request.seed) {
      prompt += ` --seed ${request.seed}`;
    }

    if (request.customParameters) {
      const params = request.customParameters;
      
      if (params.chaos) prompt += ` --chaos ${params.chaos}`;
      if (params.quality) prompt += ` --quality ${params.quality}`;
      if (params.stylize) prompt += ` --stylize ${params.stylize}`;
      if (params.weird) prompt += ` --weird ${params.weird}`;
      if (params.style && request.model === 'mj-niji-6') {
        prompt += ` --style ${params.style}`;
      }
    }

    // Add negative prompt if provided
    if (request.negativePrompt) {
      prompt += ` --no ${request.negativePrompt}`;
    }

    return {
      prompt: prompt.trim(),
      model: request.model
    };
  }

  private calculateAspectRatio(width: number, height: number): string {
    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);
    const divisor = gcd(width, height);
    return `${width / divisor}:${height / divisor}`;
  }

  private getImageDimensions(request: GenerationRequest): { width: number; height: number } {
    // Midjourney generates various sizes based on aspect ratio
    const aspectRatio = request.aspectRatio || '1:1';
    
    switch (aspectRatio) {
      case '1:1': return { width: 1024, height: 1024 };
      case '4:3': return { width: 1024, height: 768 };
      case '3:2': return { width: 1024, height: 683 };
      case '16:9': return { width: 1024, height: 576 };
      case '9:16': return { width: 576, height: 1024 };
      case '2:3': return { width: 683, height: 1024 };
      case '3:4': return { width: 768, height: 1024 };
      default: return { width: 1024, height: 1024 };
    }
  }

  async getGenerationStatus(id: string): Promise<GenerationResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/imagine/${id}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to get status: ${response.statusText}`);
      }

      const result = await response.json();
      
      return {
        id,
        status: result.status,
        images: result.images || [],
        metadata: {
          model: result.model || 'unknown',
          prompt: result.prompt || '',
          parameters: {},
          processingTime: 0,
          cost: 0,
          queue: result.queue || { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date(result.created_at || Date.now())
        },
        error: result.error
      };

    } catch (error) {
      return {
        id,
        status: 'failed',
        images: [],
        metadata: {
          model: 'unknown',
          prompt: '',
          parameters: {},
          processingTime: 0,
          cost: 0,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async cancelGeneration(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/imagine/${id}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to cancel Midjourney generation:', error);
      return false;
    }
  }

  // Midjourney-specific methods
  async upscaleImage(
    originalJobId: string, 
    upscaleIndex: number = 1
  ): Promise<GenerationResponse> {
    const requestId = this.generateRequestId();
    
    try {
      const response = await fetch(`${this.baseUrl}/upscale`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          jobId: originalJobId,
          index: upscaleIndex
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Upscale error: ${response.status} - ${errorData.error || response.statusText}`);
      }

      const submitResult = await response.json();
      const result = await this.pollForCompletion(submitResult.id);

      return {
        id: requestId,
        status: 'completed',
        images: result.images.map((imageUrl: string) => ({
          url: imageUrl,
          width: 2048,
          height: 2048,
          format: 'png',
          size: 0
        })),
        metadata: {
          model: 'upscale',
          prompt: 'upscale',
          parameters: { originalJobId, upscaleIndex },
          processingTime: 0,
          cost: 0.05, // Upscale cost
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        }
      };

    } catch (error) {
      return {
        id: requestId,
        status: 'failed',
        images: [],
        metadata: {
          model: 'upscale',
          prompt: 'upscale',
          parameters: { originalJobId, upscaleIndex },
          processingTime: 0,
          cost: 0,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}