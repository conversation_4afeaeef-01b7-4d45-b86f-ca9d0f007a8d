'use client';

import { 
  Base<PERSON>roviderAdapter, 
  AIImageModel, 
  GenerationRequest, 
  GenerationResponse,
  ProviderCapabilities,
  RateLimits
} from './provider-base';

export interface DALLEConfig {
  apiKey: string;
  organizationId?: string;
  baseUrl?: string;
}

export class D<PERSON><PERSON><PERSON>dapter extends BaseProviderAdapter {
  private organizationId?: string;

  constructor(config: DALLEConfig) {
    super({
      name: 'dalle',
      displayName: 'DALL-E',
      apiKey: config.apiKey,
      baseUrl: config.baseUrl || 'https://api.openai.com/v1',
      capabilities: {
        textToImage: true,
        imageToImage: true,
        inpainting: true,
        outpainting: true,
        upscaling: false,
        styleTransfer: false,
        controlNet: false,
        customModels: false,
        batchGeneration: false,
        realTimeGeneration: false
      },
      costPer1000: 200, // Higher cost than others
      rateLimits: {
        requestsPerMinute: 50,
        requestsPerHour: 1000,
        requestsPerDay: 5000,
        concurrent: 5
      }
    });

    this.organizationId = config.organizationId;
  }

  async initialize(): Promise<void> {
    await this.validateApiKey();
    this.models = await this.loadModels();
    this.emit('initialized', this.name);
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          ...(this.organizationId && { 'OpenAI-Organization': this.organizationId })
        }
      });

      return response.ok;
    } catch (error) {
      console.error('DALL-E API key validation failed:', error);
      return false;
    }
  }

  async loadModels(): Promise<AIImageModel[]> {
    // DALL-E models are predefined
    return [
      {
        id: 'dall-e-3',
        name: 'dall-e-3',
        displayName: 'DALL-E 3',
        description: 'OpenAI\'s most advanced image generation model with superior prompt understanding',
        type: 'diffusion',
        capabilities: {
          textToImage: true,
          imageToImage: false,
          inpainting: false,
          controlNet: false,
          negativePrompts: false,
          seedControl: false,
          batchGeneration: false,
          highResolution: true,
          fastGeneration: false
        },
        parameters: {
          prompt: {
            type: 'string',
            required: true,
            description: 'Text description of the image to generate',
            example: 'A beautiful sunset over mountains'
          },
          size: {
            type: 'select',
            required: false,
            default: '1024x1024',
            options: ['1024x1024', '1792x1024', '1024x1792'],
            description: 'Size of the generated image'
          },
          quality: {
            type: 'select',
            required: false,
            default: 'standard',
            options: ['standard', 'hd'],
            description: 'Quality of the generated image'
          },
          style: {
            type: 'select',
            required: false,
            default: 'vivid',
            options: ['vivid', 'natural'],
            description: 'Style of the generated image'
          },
          n: {
            type: 'number',
            required: false,
            default: 1,
            min: 1,
            max: 1,
            description: 'Number of images to generate (always 1 for DALL-E 3)'
          }
        },
        pricing: {
          costPerImage: 0.20, // Standard quality
          costPerMegapixel: 0.25 // HD quality
        },
        performance: {
          averageTime: 45,
          successRate: 0.98,
          qualityScore: 9.5,
          speedScore: 5.0,
          lastUpdated: new Date()
        },
        tags: ['high-quality', 'commercial', 'openai', 'premium'],
        maxResolution: { width: 1792, height: 1792 },
        aspectRatios: ['1:1', '16:9', '9:16'],
        supportedFormats: ['png']
      },
      {
        id: 'dall-e-2',
        name: 'dall-e-2',
        displayName: 'DALL-E 2',
        description: 'OpenAI\'s previous generation model with good quality and editing capabilities',
        type: 'diffusion',
        capabilities: {
          textToImage: true,
          imageToImage: true,
          inpainting: true,
          controlNet: false,
          negativePrompts: false,
          seedControl: false,
          batchGeneration: true,
          highResolution: false,
          fastGeneration: true
        },
        parameters: {
          prompt: {
            type: 'string',
            required: true,
            description: 'Text description of the image to generate',
            example: 'A beautiful sunset over mountains'
          },
          size: {
            type: 'select',
            required: false,
            default: '1024x1024',
            options: ['256x256', '512x512', '1024x1024'],
            description: 'Size of the generated image'
          },
          n: {
            type: 'number',
            required: false,
            default: 1,
            min: 1,
            max: 10,
            description: 'Number of images to generate'
          }
        },
        pricing: {
          costPerImage: 0.08 // 1024x1024 pricing
        },
        performance: {
          averageTime: 30,
          successRate: 0.95,
          qualityScore: 8.5,
          speedScore: 7.0,
          lastUpdated: new Date()
        },
        tags: ['versatile', 'editing', 'openai', 'batch'],
        maxResolution: { width: 1024, height: 1024 },
        aspectRatios: ['1:1'],
        supportedFormats: ['png']
      }
    ];
  }

  async generateImage(request: GenerationRequest): Promise<GenerationResponse> {
    await this.checkRateLimit();
    this.validateRequest(request);

    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      const model = this.getModel(request.model);
      if (!model) {
        throw new Error(`Model ${request.model} not found`);
      }

      const dalleRequest = this.mapToDALLERequest(request);
      
      const response = await fetch(`${this.baseUrl}/images/generations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          ...(this.organizationId && { 'OpenAI-Organization': this.organizationId })
        },
        body: JSON.stringify(dalleRequest)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`DALL-E API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const result = await response.json();
      const processingTime = Date.now() - startTime;
      const cost = this.calculateCost(request, model);

      this.recordGeneration(true, cost, processingTime, request.model);

      return {
        id: requestId,
        status: 'completed',
        images: result.data.map((image: any) => {
          const dimensions = this.parseDimensions(request.customParameters?.size || '1024x1024');
          return {
            url: image.url,
            width: dimensions.width,
            height: dimensions.height,
            format: 'png',
            size: 0, // OpenAI doesn't provide file size
            nsfw: false // OpenAI has built-in content filtering
          };
        }),
        metadata: {
          model: request.model,
          prompt: request.prompt,
          parameters: dalleRequest,
          processingTime,
          cost,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.recordGeneration(false, 0, processingTime, request.model);

      return {
        id: requestId,
        status: 'failed',
        images: [],
        metadata: {
          model: request.model,
          prompt: request.prompt,
          parameters: {},
          processingTime,
          cost: 0,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async generateImageEdit(
    image: string,
    mask: string,
    prompt: string,
    request: Partial<GenerationRequest> = {}
  ): Promise<GenerationResponse> {
    await this.checkRateLimit();

    if (request.model !== 'dall-e-2') {
      throw new Error('Image editing is only supported with DALL-E 2');
    }

    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      const formData = new FormData();
      
      // Convert base64 to blob if needed
      const imageBlob = this.base64ToBlob(image);
      const maskBlob = this.base64ToBlob(mask);
      
      formData.append('image', imageBlob, 'image.png');
      formData.append('mask', maskBlob, 'mask.png');
      formData.append('prompt', prompt);
      
      if (request.customParameters?.n) {
        formData.append('n', request.customParameters.n.toString());
      }
      
      if (request.customParameters?.size) {
        formData.append('size', request.customParameters.size);
      }

      const response = await fetch(`${this.baseUrl}/images/edits`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          ...(this.organizationId && { 'OpenAI-Organization': this.organizationId })
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`DALL-E API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const result = await response.json();
      const processingTime = Date.now() - startTime;
      const model = this.getModel('dall-e-2')!;
      const cost = this.calculateCost({ ...request, model: 'dall-e-2' } as GenerationRequest, model);

      this.recordGeneration(true, cost, processingTime, 'dall-e-2');

      return {
        id: requestId,
        status: 'completed',
        images: result.data.map((image: any) => {
          const dimensions = this.parseDimensions(request.customParameters?.size || '1024x1024');
          return {
            url: image.url,
            width: dimensions.width,
            height: dimensions.height,
            format: 'png',
            size: 0,
            nsfw: false
          };
        }),
        metadata: {
          model: 'dall-e-2',
          prompt,
          parameters: { prompt, n: request.customParameters?.n || 1 },
          processingTime,
          cost,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.recordGeneration(false, 0, processingTime, 'dall-e-2');

      return {
        id: requestId,
        status: 'failed',
        images: [],
        metadata: {
          model: 'dall-e-2',
          prompt,
          parameters: {},
          processingTime,
          cost: 0,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private mapToDALLERequest(request: GenerationRequest): any {
    const dalleRequest: any = {
      model: request.model,
      prompt: request.prompt
    };

    // Handle size parameter
    if (request.width && request.height) {
      const size = `${request.width}x${request.height}`;
      const model = this.getModel(request.model);
      if (model?.parameters.size?.options?.includes(size)) {
        dalleRequest.size = size;
      }
    }

    // Map custom parameters
    if (request.customParameters) {
      if (request.customParameters.size) dalleRequest.size = request.customParameters.size;
      if (request.customParameters.quality) dalleRequest.quality = request.customParameters.quality;
      if (request.customParameters.style) dalleRequest.style = request.customParameters.style;
      if (request.customParameters.n) dalleRequest.n = request.customParameters.n;
    }

    return dalleRequest;
  }

  private calculateCost(request: GenerationRequest, model: AIImageModel): number {
    const baseCost = model.pricing.costPerImage;
    
    // Adjust for quality (DALL-E 3)
    if (request.model === 'dall-e-3' && request.customParameters?.quality === 'hd') {
      return baseCost * 2; // HD costs double
    }
    
    // Adjust for size (DALL-E 2)
    if (request.model === 'dall-e-2') {
      const size = request.customParameters?.size || '1024x1024';
      switch (size) {
        case '256x256': return 0.016;
        case '512x512': return 0.018;
        case '1024x1024': return 0.020;
        default: return baseCost;
      }
    }
    
    return baseCost;
  }

  private parseDimensions(sizeString: string): { width: number; height: number } {
    const [width, height] = sizeString.split('x').map(Number);
    return { width, height };
  }

  private base64ToBlob(base64: string): Blob {
    // Remove data URL prefix if present
    const base64Data = base64.replace(/^data:image\/[a-z]+;base64,/, '');
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: 'image/png' });
  }

  async getGenerationStatus(id: string): Promise<GenerationResponse> {
    // DALL-E returns results immediately, so this is mainly for error handling
    throw new Error('Generation status not supported for DALL-E (synchronous API)');
  }

  async cancelGeneration(id: string): Promise<boolean> {
    // DALL-E doesn't support cancellation for synchronous requests
    return false;
  }
}