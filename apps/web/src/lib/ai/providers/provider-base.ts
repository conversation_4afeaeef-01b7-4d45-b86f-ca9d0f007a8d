'use client';

import { EventEmitter } from 'events';

// Base provider interface for unified AI image generation
export interface AIImageProvider {
  name: string;
  displayName: string;
  apiKey: string;
  baseUrl?: string;
  models: AIImageModel[];
  capabilities: ProviderCapabilities;
  costPer1000: number; // Cost per 1000 images generated
  rateLimits: RateLimits;
  healthStatus: ProviderHealth;
}

export interface AIImageModel {
  id: string;
  name: string;
  displayName: string;
  description: string;
  type: ModelType;
  capabilities: ModelCapabilities;
  parameters: ModelParameters;
  pricing: ModelPricing;
  performance: ModelPerformance;
  tags: string[];
  maxResolution: { width: number; height: number };
  aspectRatios: string[];
  supportedFormats: string[];
}

export interface ProviderCapabilities {
  textToImage: boolean;
  imageToImage: boolean;
  inpainting: boolean;
  outpainting: boolean;
  upscaling: boolean;
  styleTransfer: boolean;
  controlNet: boolean;
  customModels: boolean;
  batchGeneration: boolean;
  realTimeGeneration: boolean;
}

export interface ModelCapabilities {
  textToImage: boolean;
  imageToImage: boolean;
  inpainting: boolean;
  controlNet: boolean;
  negativePrompts: boolean;
  seedControl: boolean;
  batchGeneration: boolean;
  highResolution: boolean;
  fastGeneration: boolean;
}

export interface ModelParameters {
  [key: string]: ParameterDefinition;
}

export interface ParameterDefinition {
  type: 'string' | 'number' | 'boolean' | 'select' | 'image' | 'array';
  required: boolean;
  default?: any;
  min?: number;
  max?: number;
  step?: number;
  options?: string[] | number[];
  description: string;
  example?: any;
}

export interface ModelPricing {
  costPerImage: number;
  costPerMegapixel?: number;
  freeQuota?: number;
  subscriptionTiers?: {
    name: string;
    price: number;
    imagesIncluded: number;
    additionalCost: number;
  }[];
}

export interface ModelPerformance {
  averageTime: number; // seconds
  successRate: number; // 0-1
  qualityScore: number; // 0-10
  speedScore: number; // 0-10
  lastUpdated: Date;
}

export interface RateLimits {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  concurrent: number;
  burstLimit?: number;
}

export interface ProviderHealth {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'maintenance';
  responseTime: number; // ms
  uptime: number; // percentage
  lastCheck: Date;
  issues: string[];
}

export type ModelType = 
  | 'diffusion' 
  | 'gan' 
  | 'transformer' 
  | 'hybrid' 
  | 'custom';

export interface GenerationRequest {
  model: string;
  prompt: string;
  negativePrompt?: string;
  width?: number;
  height?: number;
  aspectRatio?: string;
  seed?: number;
  steps?: number;
  guidance?: number;
  strength?: number;
  imageInput?: string;
  maskInput?: string;
  style?: string;
  quality?: 'draft' | 'standard' | 'premium';
  format?: 'png' | 'jpg' | 'webp';
  customParameters?: Record<string, any>;
}

export interface GenerationResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  images: GeneratedImage[];
  metadata: GenerationMetadata;
  error?: string;
}

export interface GeneratedImage {
  url: string;
  width: number;
  height: number;
  format: string;
  size: number; // bytes
  seed?: number;
  nsfw?: boolean;
}

export interface GenerationMetadata {
  model: string;
  prompt: string;
  parameters: Record<string, any>;
  processingTime: number;
  cost: number;
  queue: {
    position?: number;
    estimatedWait?: number;
  };
  provider: string;
  timestamp: Date;
}

export interface ProviderUsageStats {
  totalGenerations: number;
  successfulGenerations: number;
  failedGenerations: number;
  totalCost: number;
  averageResponseTime: number;
  averageQueueTime: number;
  popularModels: Array<{ model: string; usage: number }>;
  timeRange: { start: Date; end: Date };
}

// Base provider adapter class
export abstract class BaseProviderAdapter extends EventEmitter implements AIImageProvider {
  public name: string;
  public displayName: string;
  public apiKey: string;
  public baseUrl?: string;
  public models: AIImageModel[] = [];
  public capabilities: ProviderCapabilities;
  public costPer1000: number;
  public rateLimits: RateLimits;
  public healthStatus: ProviderHealth;

  protected requestQueue: Map<string, Promise<any>> = new Map();
  protected usageStats: ProviderUsageStats;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor(config: Partial<AIImageProvider>) {
    super();
    
    this.name = config.name || 'unknown';
    this.displayName = config.displayName || this.name;
    this.apiKey = config.apiKey || '';
    this.baseUrl = config.baseUrl;
    this.capabilities = config.capabilities || this.getDefaultCapabilities();
    this.costPer1000 = config.costPer1000 || 0;
    this.rateLimits = config.rateLimits || this.getDefaultRateLimits();
    this.healthStatus = config.healthStatus || this.getDefaultHealth();
    
    this.usageStats = {
      totalGenerations: 0,
      successfulGenerations: 0,
      failedGenerations: 0,
      totalCost: 0,
      averageResponseTime: 0,
      averageQueueTime: 0,
      popularModels: [],
      timeRange: { start: new Date(), end: new Date() }
    };

    this.startHealthMonitoring();
  }

  // Abstract methods to be implemented by specific providers
  abstract initialize(): Promise<void>;
  abstract loadModels(): Promise<AIImageModel[]>;
  abstract generateImage(request: GenerationRequest): Promise<GenerationResponse>;
  abstract getGenerationStatus(id: string): Promise<GenerationResponse>;
  abstract cancelGeneration(id: string): Promise<boolean>;
  abstract validateApiKey(): Promise<boolean>;

  // Common utility methods
  protected getDefaultCapabilities(): ProviderCapabilities {
    return {
      textToImage: true,
      imageToImage: false,
      inpainting: false,
      outpainting: false,
      upscaling: false,
      styleTransfer: false,
      controlNet: false,
      customModels: false,
      batchGeneration: false,
      realTimeGeneration: false
    };
  }

  protected getDefaultRateLimits(): RateLimits {
    return {
      requestsPerMinute: 10,
      requestsPerHour: 200,
      requestsPerDay: 1000,
      concurrent: 3
    };
  }

  protected getDefaultHealth(): ProviderHealth {
    return {
      status: 'healthy',
      responseTime: 0,
      uptime: 100,
      lastCheck: new Date(),
      issues: []
    };
  }

  // Health monitoring
  private startHealthMonitoring(): void {
    // Check health every 5 minutes
    this.healthCheckInterval = setInterval(() => {
      this.checkHealth();
    }, 5 * 60 * 1000);
  }

  protected async checkHealth(): Promise<void> {
    try {
      const startTime = Date.now();
      const isHealthy = await this.performHealthCheck();
      const responseTime = Date.now() - startTime;

      this.healthStatus = {
        ...this.healthStatus,
        status: isHealthy ? 'healthy' : 'degraded',
        responseTime,
        lastCheck: new Date(),
        issues: isHealthy ? [] : ['Health check failed']
      };

      this.emit('health:updated', this.healthStatus);
    } catch (error) {
      this.healthStatus = {
        ...this.healthStatus,
        status: 'unhealthy',
        lastCheck: new Date(),
        issues: [error instanceof Error ? error.message : 'Health check error']
      };

      this.emit('health:error', error);
    }
  }

  protected async performHealthCheck(): Promise<boolean> {
    try {
      return await this.validateApiKey();
    } catch {
      return false;
    }
  }

  // Usage tracking
  protected recordGeneration(
    success: boolean, 
    cost: number, 
    responseTime: number, 
    model: string
  ): void {
    this.usageStats.totalGenerations++;
    
    if (success) {
      this.usageStats.successfulGenerations++;
    } else {
      this.usageStats.failedGenerations++;
    }
    
    this.usageStats.totalCost += cost;
    
    // Update rolling average
    const total = this.usageStats.totalGenerations;
    this.usageStats.averageResponseTime = 
      (this.usageStats.averageResponseTime * (total - 1) + responseTime) / total;
    
    // Update popular models
    const existingModel = this.usageStats.popularModels.find(m => m.model === model);
    if (existingModel) {
      existingModel.usage++;
    } else {
      this.usageStats.popularModels.push({ model, usage: 1 });
    }
    
    // Sort by usage
    this.usageStats.popularModels.sort((a, b) => b.usage - a.usage);
    
    this.emit('usage:updated', this.usageStats);
  }

  // Model management
  public getModel(modelId: string): AIImageModel | undefined {
    return this.models.find(m => m.id === modelId);
  }

  public getModelsByType(type: ModelType): AIImageModel[] {
    return this.models.filter(m => m.type === type);
  }

  public getModelsByCapability(capability: keyof ModelCapabilities): AIImageModel[] {
    return this.models.filter(m => m.capabilities[capability]);
  }

  public getOptimalModel(
    requirements: {
      type?: ModelType;
      capability?: keyof ModelCapabilities;
      maxCost?: number;
      minQuality?: number;
      maxTime?: number;
    }
  ): AIImageModel | null {
    let candidates = [...this.models];
    
    if (requirements.type) {
      candidates = candidates.filter(m => m.type === requirements.type);
    }
    
    if (requirements.capability) {
      candidates = candidates.filter(m => m.capabilities[requirements.capability]);
    }
    
    if (requirements.maxCost) {
      candidates = candidates.filter(m => m.pricing.costPerImage <= requirements.maxCost);
    }
    
    if (requirements.minQuality) {
      candidates = candidates.filter(m => m.performance.qualityScore >= requirements.minQuality);
    }
    
    if (requirements.maxTime) {
      candidates = candidates.filter(m => m.performance.averageTime <= requirements.maxTime);
    }
    
    if (candidates.length === 0) return null;
    
    // Score candidates
    const scored = candidates.map(model => ({
      model,
      score: this.calculateModelScore(model, requirements)
    }));
    
    scored.sort((a, b) => b.score - a.score);
    return scored[0].model;
  }

  private calculateModelScore(
    model: AIImageModel, 
    requirements: any
  ): number {
    let score = 0;
    
    // Quality score (weighted heavily)
    score += model.performance.qualityScore * 3;
    
    // Speed score
    score += model.performance.speedScore * 2;
    
    // Cost efficiency (inverse cost)
    score += (1 - Math.min(model.pricing.costPerImage / 10, 1)) * 2;
    
    // Success rate
    score += model.performance.successRate * 2;
    
    return score;
  }

  // Request management
  protected async queueRequest<T>(
    requestId: string,
    requestFn: () => Promise<T>
  ): Promise<T> {
    // Check if request is already queued
    if (this.requestQueue.has(requestId)) {
      return this.requestQueue.get(requestId);
    }
    
    // Execute request
    const promise = requestFn()
      .finally(() => {
        this.requestQueue.delete(requestId);
      });
    
    this.requestQueue.set(requestId, promise);
    return promise;
  }

  // Rate limiting
  protected async checkRateLimit(): Promise<void> {
    // Simple rate limiting - providers can override for more sophisticated logic
    if (this.requestQueue.size >= this.rateLimits.concurrent) {
      throw new Error('Rate limit exceeded: too many concurrent requests');
    }
  }

  // Utility methods
  protected generateRequestId(): string {
    return `${this.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  protected validateRequest(request: GenerationRequest): void {
    if (!request.prompt?.trim()) {
      throw new Error('Prompt is required');
    }
    
    if (!request.model) {
      throw new Error('Model is required');
    }
    
    const model = this.getModel(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }
    
    // Validate parameters against model requirements
    this.validateModelParameters(model, request);
  }

  private validateModelParameters(model: AIImageModel, request: GenerationRequest): void {
    for (const [paramName, paramDef] of Object.entries(model.parameters)) {
      const value = (request as any)[paramName] || request.customParameters?.[paramName];
      
      if (paramDef.required && (value === undefined || value === null)) {
        throw new Error(`Required parameter '${paramName}' is missing`);
      }
      
      if (value !== undefined) {
        if (paramDef.type === 'number' && typeof value !== 'number') {
          throw new Error(`Parameter '${paramName}' must be a number`);
        }
        
        if (paramDef.min !== undefined && value < paramDef.min) {
          throw new Error(`Parameter '${paramName}' must be >= ${paramDef.min}`);
        }
        
        if (paramDef.max !== undefined && value > paramDef.max) {
          throw new Error(`Parameter '${paramName}' must be <= ${paramDef.max}`);
        }
        
        if (paramDef.options && !paramDef.options.includes(value)) {
          throw new Error(`Parameter '${paramName}' must be one of: ${paramDef.options.join(', ')}`);
        }
      }
    }
  }

  // Cleanup
  public dispose(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    this.removeAllListeners();
    this.requestQueue.clear();
  }

  // Statistics
  public getUsageStats(): ProviderUsageStats {
    return { ...this.usageStats };
  }

  public resetUsageStats(): void {
    this.usageStats = {
      totalGenerations: 0,
      successfulGenerations: 0,
      failedGenerations: 0,
      totalCost: 0,
      averageResponseTime: 0,
      averageQueueTime: 0,
      popularModels: [],
      timeRange: { start: new Date(), end: new Date() }
    };
  }
}