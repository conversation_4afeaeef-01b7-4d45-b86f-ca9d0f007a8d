'use client';

import { 
  BaseProviderAdapter, 
  AIImageModel, 
  GenerationRequest, 
  GenerationResponse,
  ProviderCapabilities,
  RateLimits
} from './provider-base';

export interface StabilityAIConfig {
  apiKey: string;
  baseUrl?: string;
  organizationId?: string;
}

export class StabilityAIAdapter extends BaseProviderAdapter {
  private organizationId?: string;

  constructor(config: StabilityAIConfig) {
    super({
      name: 'stability-ai',
      displayName: 'Stability AI',
      apiKey: config.apiKey,
      baseUrl: config.baseUrl || 'https://api.stability.ai',
      capabilities: {
        textToImage: true,
        imageToImage: true,
        inpainting: true,
        outpainting: true,
        upscaling: true,
        styleTransfer: false,
        controlNet: true,
        customModels: false,
        batchGeneration: true,
        realTimeGeneration: false
      },
      costPer1000: 120, // Approximate cost per 1000 images
      rateLimits: {
        requestsPerMinute: 150,
        requestsPerHour: 5000,
        requestsPerDay: 20000,
        concurrent: 10
      }
    });

    this.organizationId = config.organizationId;
  }

  async initialize(): Promise<void> {
    await this.validateApiKey();
    this.models = await this.loadModels();
    this.emit('initialized', this.name);
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/user/account`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Accept': 'application/json',
          ...(this.organizationId && { 'Stability-Client-ID': this.organizationId })
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Stability AI API key validation failed:', error);
      return false;
    }
  }

  async loadModels(): Promise<AIImageModel[]> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/engines/list`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Accept': 'application/json',
          ...(this.organizationId && { 'Stability-Client-ID': this.organizationId })
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to load models: ${response.statusText}`);
      }

      const data = await response.json();
      return this.mapStabilityModels(data.engines || []);
    } catch (error) {
      console.error('Failed to load Stability AI models:', error);
      return this.getDefaultModels();
    }
  }

  private mapStabilityModels(engines: any[]): AIImageModel[] {
    return engines.map(engine => ({
      id: engine.id,
      name: engine.id,
      displayName: engine.name || engine.id,
      description: engine.description || `Stability AI ${engine.id} model`,
      type: 'diffusion' as const,
      capabilities: {
        textToImage: true,
        imageToImage: engine.id.includes('img2img') || engine.id.includes('stable-diffusion'),
        inpainting: engine.id.includes('inpaint') || engine.id.includes('stable-diffusion'),
        controlNet: engine.id.includes('control'),
        negativePrompts: true,
        seedControl: true,
        batchGeneration: false,
        highResolution: engine.id.includes('xl') || engine.id.includes('2048'),
        fastGeneration: engine.id.includes('turbo') || engine.id.includes('lightning')
      },
      parameters: this.getModelParameters(engine.id),
      pricing: {
        costPerImage: this.getModelCost(engine.id),
        costPerMegapixel: 0.01
      },
      performance: {
        averageTime: this.getEstimatedTime(engine.id),
        successRate: 0.95,
        qualityScore: this.getQualityScore(engine.id),
        speedScore: this.getSpeedScore(engine.id),
        lastUpdated: new Date()
      },
      tags: this.getModelTags(engine.id),
      maxResolution: this.getMaxResolution(engine.id),
      aspectRatios: ['1:1', '4:3', '3:2', '16:9', '9:16', '2:3', '3:4'],
      supportedFormats: ['png', 'jpeg', 'webp']
    }));
  }

  private getDefaultModels(): AIImageModel[] {
    return [
      {
        id: 'stable-diffusion-xl-1024-v1-0',
        name: 'stable-diffusion-xl-1024-v1-0',
        displayName: 'Stable Diffusion XL 1.0',
        description: 'High-quality 1024x1024 image generation',
        type: 'diffusion',
        capabilities: {
          textToImage: true,
          imageToImage: true,
          inpainting: true,
          controlNet: false,
          negativePrompts: true,
          seedControl: true,
          batchGeneration: false,
          highResolution: true,
          fastGeneration: false
        },
        parameters: {
          text_prompts: {
            type: 'array',
            required: true,
            description: 'Text prompts for generation',
            example: [{ text: 'A beautiful landscape', weight: 1 }]
          },
          cfg_scale: {
            type: 'number',
            required: false,
            default: 7,
            min: 1,
            max: 35,
            description: 'Classifier-free guidance scale'
          },
          steps: {
            type: 'number',
            required: false,
            default: 30,
            min: 10,
            max: 150,
            description: 'Number of diffusion steps'
          },
          seed: {
            type: 'number',
            required: false,
            min: 0,
            max: 4294967295,
            description: 'Random seed for reproducible results'
          },
          samples: {
            type: 'number',
            required: false,
            default: 1,
            min: 1,
            max: 10,
            description: 'Number of images to generate'
          },
          style_preset: {
            type: 'select',
            required: false,
            options: ['3d-model', 'analog-film', 'anime', 'cinematic', 'comic-book', 'digital-art', 'enhance', 'fantasy-art', 'isometric', 'line-art', 'low-poly', 'modeling-compound', 'neon-punk', 'origami', 'photographic', 'pixel-art', 'tile-texture'],
            description: 'Style preset to apply'
          }
        },
        pricing: {
          costPerImage: 0.12
        },
        performance: {
          averageTime: 25,
          successRate: 0.95,
          qualityScore: 9.2,
          speedScore: 6.5,
          lastUpdated: new Date()
        },
        tags: ['high-quality', 'versatile', 'stable-diffusion'],
        maxResolution: { width: 1024, height: 1024 },
        aspectRatios: ['1:1', '4:3', '3:2', '16:9', '9:16', '2:3', '3:4'],
        supportedFormats: ['png', 'jpeg', 'webp']
      },
      {
        id: 'stable-diffusion-xl-beta-v2-2-2',
        name: 'stable-diffusion-xl-beta-v2-2-2',
        displayName: 'Stable Diffusion XL Lightning',
        description: 'Fast 4-step generation with good quality',
        type: 'diffusion',
        capabilities: {
          textToImage: true,
          imageToImage: true,
          inpainting: false,
          controlNet: false,
          negativePrompts: true,
          seedControl: true,
          batchGeneration: false,
          highResolution: true,
          fastGeneration: true
        },
        parameters: {
          text_prompts: {
            type: 'array',
            required: true,
            description: 'Text prompts for generation',
            example: [{ text: 'A beautiful landscape', weight: 1 }]
          },
          cfg_scale: {
            type: 'number',
            required: false,
            default: 1.5,
            min: 1,
            max: 8,
            description: 'Classifier-free guidance scale'
          },
          steps: {
            type: 'number',
            required: false,
            default: 4,
            min: 1,
            max: 8,
            description: 'Number of diffusion steps'
          },
          seed: {
            type: 'number',
            required: false,
            min: 0,
            max: 4294967295,
            description: 'Random seed for reproducible results'
          }
        },
        pricing: {
          costPerImage: 0.08
        },
        performance: {
          averageTime: 8,
          successRate: 0.92,
          qualityScore: 8.7,
          speedScore: 9.5,
          lastUpdated: new Date()
        },
        tags: ['fast', 'lightning', 'stable-diffusion'],
        maxResolution: { width: 1024, height: 1024 },
        aspectRatios: ['1:1', '4:3', '3:2', '16:9', '9:16'],
        supportedFormats: ['png', 'jpeg']
      }
    ];
  }

  private getModelParameters(modelId: string): any {
    const baseParams = {
      text_prompts: {
        type: 'array',
        required: true,
        description: 'Text prompts for generation',
        example: [{ text: 'A beautiful landscape', weight: 1 }]
      },
      cfg_scale: {
        type: 'number',
        required: false,
        default: 7,
        min: 1,
        max: 35,
        description: 'Classifier-free guidance scale'
      },
      steps: {
        type: 'number',
        required: false,
        default: 30,
        min: 10,
        max: 150,
        description: 'Number of diffusion steps'
      },
      seed: {
        type: 'number',
        required: false,
        min: 0,
        max: 4294967295,
        description: 'Random seed for reproducible results'
      }
    };

    if (modelId.includes('lightning') || modelId.includes('turbo')) {
      baseParams.steps.default = 4;
      baseParams.steps.max = 8;
      baseParams.cfg_scale.default = 1.5;
      baseParams.cfg_scale.max = 8;
    }

    return baseParams;
  }

  private getModelCost(modelId: string): number {
    if (modelId.includes('lightning') || modelId.includes('turbo')) return 0.08;
    if (modelId.includes('xl') || modelId.includes('2048')) return 0.12;
    return 0.10;
  }

  private getEstimatedTime(modelId: string): number {
    if (modelId.includes('lightning') || modelId.includes('turbo')) return 8;
    if (modelId.includes('xl')) return 25;
    return 20;
  }

  private getQualityScore(modelId: string): number {
    if (modelId.includes('xl')) return 9.2;
    if (modelId.includes('lightning')) return 8.7;
    return 8.5;
  }

  private getSpeedScore(modelId: string): number {
    if (modelId.includes('lightning') || modelId.includes('turbo')) return 9.5;
    if (modelId.includes('xl')) return 6.5;
    return 7.0;
  }

  private getModelTags(modelId: string): string[] {
    const tags = [];
    if (modelId.includes('xl')) tags.push('high-quality', 'xl');
    if (modelId.includes('lightning')) tags.push('fast', 'lightning');
    if (modelId.includes('turbo')) tags.push('fast', 'turbo');
    if (modelId.includes('inpaint')) tags.push('inpainting');
    if (modelId.includes('control')) tags.push('controlnet');
    tags.push('stable-diffusion');
    return tags;
  }

  private getMaxResolution(modelId: string): { width: number; height: number } {
    if (modelId.includes('xl') || modelId.includes('2048')) return { width: 2048, height: 2048 };
    return { width: 1024, height: 1024 };
  }

  async generateImage(request: GenerationRequest): Promise<GenerationResponse> {
    await this.checkRateLimit();
    this.validateRequest(request);

    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      const model = this.getModel(request.model);
      if (!model) {
        throw new Error(`Model ${request.model} not found`);
      }

      const stabilityRequest = this.mapToStabilityRequest(request);
      
      const response = await fetch(`${this.baseUrl}/v1/generation/${request.model}/text-to-image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'Accept': 'application/json',
          ...(this.organizationId && { 'Stability-Client-ID': this.organizationId })
        },
        body: JSON.stringify(stabilityRequest)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Stability AI API error: ${response.status} - ${errorData.message || response.statusText}`);
      }

      const result = await response.json();
      const processingTime = Date.now() - startTime;
      const cost = model.pricing.costPerImage;

      this.recordGeneration(true, cost, processingTime, request.model);

      return {
        id: requestId,
        status: 'completed',
        images: result.artifacts.map((artifact: any) => ({
          url: `data:image/png;base64,${artifact.base64}`,
          width: request.width || 1024,
          height: request.height || 1024,
          format: 'png',
          size: Math.floor(artifact.base64.length * 0.75), // Approximate file size
          seed: artifact.seed,
          nsfw: artifact.finishReason === 'CONTENT_FILTERED'
        })),
        metadata: {
          model: request.model,
          prompt: request.prompt,
          parameters: stabilityRequest,
          processingTime,
          cost,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.recordGeneration(false, 0, processingTime, request.model);

      return {
        id: requestId,
        status: 'failed',
        images: [],
        metadata: {
          model: request.model,
          prompt: request.prompt,
          parameters: {},
          processingTime,
          cost: 0,
          queue: { position: 0, estimatedWait: 0 },
          provider: this.name,
          timestamp: new Date()
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private mapToStabilityRequest(request: GenerationRequest): any {
    const stabilityRequest: any = {
      text_prompts: [
        { text: request.prompt, weight: 1 }
      ]
    };

    if (request.negativePrompt) {
      stabilityRequest.text_prompts.push({
        text: request.negativePrompt,
        weight: -1
      });
    }

    if (request.width) stabilityRequest.width = request.width;
    if (request.height) stabilityRequest.height = request.height;
    if (request.seed) stabilityRequest.seed = request.seed;
    if (request.steps) stabilityRequest.steps = request.steps;
    if (request.guidance) stabilityRequest.cfg_scale = request.guidance;

    // Map custom parameters
    if (request.customParameters) {
      Object.assign(stabilityRequest, request.customParameters);
    }

    return stabilityRequest;
  }

  async getGenerationStatus(id: string): Promise<GenerationResponse> {
    // Stability AI returns results immediately, so this is mainly for error handling
    throw new Error('Generation status not supported for Stability AI (synchronous API)');
  }

  async cancelGeneration(id: string): Promise<boolean> {
    // Stability AI doesn't support cancellation for synchronous requests
    return false;
  }
}