import { EventEmitter } from 'events';
import { TerminalService } from './terminal-backend-service';
import { TerminalConfiguration } from './terminal-service';

interface WebSocketMessage {
  type: string;
  sessionId?: string;
  data?: any;
  config?: TerminalConfiguration;
  cols?: number;
  rows?: number;
}

interface TerminalConnection {
  userId: string;
  sessionId?: string;
  lastActivity: Date;
}

export class TerminalWebSocketHandler extends EventEmitter {
  private connections: Map<WebSocket, TerminalConnection> = new Map();
  private terminalService: TerminalService;
  private heartbeatInterval: NodeJS.Timeout;

  constructor() {
    super();
    this.terminalService = new TerminalService();
    this.setupTerminalEventHandlers();
    this.startHeartbeat();
  }

  handleConnection(ws: WebSocket, userId: string): void {
    console.log(`Terminal WebSocket connection established for user: ${userId}`);
    
    // Store connection info
    this.connections.set(ws, {
      userId,
      lastActivity: new Date()
    });

    // Setup message handler
    ws.addEventListener('message', async (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data.toString());
        await this.handleMessage(ws, userId, message);
      } catch (error) {
        console.error('WebSocket message error:', error);
        this.sendError(ws, 'Invalid message format');
      }
    });

    // Handle connection close
    ws.addEventListener('close', () => {
      this.handleDisconnection(ws, userId);
    });

    // Handle connection error
    ws.addEventListener('error', (error) => {
      console.error(`WebSocket error for user ${userId}:`, error);
      this.handleDisconnection(ws, userId);
    });

    // Send welcome message
    this.sendMessage(ws, {
      type: 'connected',
      data: { message: 'Terminal WebSocket connected successfully' }
    });
  }

  private async handleMessage(
    ws: WebSocket,
    userId: string,
    message: WebSocketMessage
  ): Promise<void> {
    const connection = this.connections.get(ws);
    if (!connection) return;

    // Update last activity
    connection.lastActivity = new Date();

    switch (message.type) {
      case 'create_session':
        await this.handleCreateSession(ws, userId, message.config);
        break;

      case 'send_input':
        await this.handleSendInput(message.sessionId!, message.data);
        break;

      case 'resize_terminal':
        await this.handleResize(message.sessionId!, message.cols!, message.rows!);
        break;

      case 'list_sessions':
        await this.handleListSessions(ws, userId);
        break;

      case 'get_session':
        await this.handleGetSession(ws, message.sessionId!);
        break;

      case 'delete_session':
        await this.handleDeleteSession(ws, message.sessionId!);
        break;

      case 'ping':
        this.sendMessage(ws, { type: 'pong', data: { timestamp: Date.now() } });
        break;

      default:
        this.sendError(ws, `Unknown message type: ${message.type}`);
    }
  }

  private async handleCreateSession(
    ws: WebSocket,
    userId: string,
    config?: TerminalConfiguration
  ): Promise<void> {
    try {
      const defaultConfig: TerminalConfiguration = {
        shell: 'bash',
        theme: {
          name: 'dark',
          background: '#1e1e1e',
          foreground: '#d4d4d4',
          cursor: '#aeafad',
          selection: '#264f78',
          black: '#000000',
          red: '#cd3131',
          green: '#0dbc79',
          yellow: '#e5e510',
          blue: '#2472c8',
          magenta: '#bc3fbc',
          cyan: '#11a8cd',
          white: '#e5e5e5',
          brightBlack: '#666666',
          brightRed: '#f14c4c',
          brightGreen: '#23d18b',
          brightYellow: '#f5f543',
          brightBlue: '#3b8eea',
          brightMagenta: '#d670d6',
          brightCyan: '#29b8db',
          brightWhite: '#ffffff'
        },
        fontSize: 14,
        fontFamily: 'Monaco, "Cascadia Code", "Roboto Mono", monospace',
        scrollback: 1000,
        bellStyle: 'none',
        cursorBlink: true,
        cursorStyle: 'block',
        allowTransparency: false
      };

      const sessionConfig = { ...defaultConfig, ...config };
      const session = await this.terminalService.createSession(sessionConfig);

      // Associate session with connection
      const connection = this.connections.get(ws);
      if (connection) {
        connection.sessionId = session.id;
      }

      this.sendMessage(ws, {
        type: 'session_created',
        data: { session }
      });
    } catch (error) {
      this.sendError(ws, `Failed to create session: ${error}`);
    }
  }

  async handleSendInput(sessionId: string, input: string): Promise<void> {
    try {
      await this.terminalService.executeCommand(sessionId, input);
    } catch (error) {
      console.error(`Failed to send input to session ${sessionId}:`, error);
    }
  }

  async handleResize(sessionId: string, cols: number, rows: number): Promise<void> {
    try {
      await this.terminalService.resizeTerminal(sessionId, cols, rows);
    } catch (error) {
      console.error(`Failed to resize terminal ${sessionId}:`, error);
    }
  }

  private async handleListSessions(ws: WebSocket, userId: string): Promise<void> {
    try {
      const sessions = await this.terminalService.listSessions();
      this.sendMessage(ws, {
        type: 'sessions_list',
        data: { sessions }
      });
    } catch (error) {
      this.sendError(ws, `Failed to list sessions: ${error}`);
    }
  }

  private async handleGetSession(ws: WebSocket, sessionId: string): Promise<void> {
    try {
      const session = await this.terminalService.getSession(sessionId);
      if (session) {
        this.sendMessage(ws, {
          type: 'session_data',
          data: { session }
        });
      } else {
        this.sendError(ws, `Session not found: ${sessionId}`);
      }
    } catch (error) {
      this.sendError(ws, `Failed to get session: ${error}`);
    }
  }

  private async handleDeleteSession(ws: WebSocket, sessionId: string): Promise<void> {
    try {
      await this.terminalService.deleteSession(sessionId);
      
      // Clear session from connection
      const connection = this.connections.get(ws);
      if (connection && connection.sessionId === sessionId) {
        connection.sessionId = undefined;
      }

      this.sendMessage(ws, {
        type: 'session_deleted',
        data: { sessionId }
      });
    } catch (error) {
      this.sendError(ws, `Failed to delete session: ${error}`);
    }
  }

  private setupTerminalEventHandlers(): void {
    this.terminalService.on('processOutput', ({ sessionId, data }) => {
      this.broadcastToSession(sessionId, {
        type: 'output',
        data: { output: data }
      });
    });

    this.terminalService.on('processExit', ({ sessionId, exitCode }) => {
      this.broadcastToSession(sessionId, {
        type: 'session_terminated',
        data: { exitCode }
      });
    });

    this.terminalService.on('processError', ({ sessionId, error }) => {
      this.broadcastToSession(sessionId, {
        type: 'error',
        data: { message: error }
      });
    });

    this.terminalService.on('commandExecuted', ({ sessionId, command }) => {
      this.broadcastToSession(sessionId, {
        type: 'command_executed',
        data: { command }
      });
    });
  }

  private broadcastToSession(sessionId: string, message: any): void {
    for (const [ws, connection] of this.connections.entries()) {
      if (connection.sessionId === sessionId && ws.readyState === WebSocket.OPEN) {
        this.sendMessage(ws, message);
      }
    }
  }

  private sendMessage(ws: WebSocket, message: any): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, error: string): void {
    this.sendMessage(ws, {
      type: 'error',
      data: { message: error }
    });
  }

  private handleDisconnection(ws: WebSocket, userId: string): void {
    console.log(`Terminal WebSocket disconnection for user: ${userId}`);
    this.connections.delete(ws);
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = new Date();
      const timeout = 5 * 60 * 1000; // 5 minutes

      for (const [ws, connection] of this.connections.entries()) {
        const timeSinceActivity = now.getTime() - connection.lastActivity.getTime();
        
        if (timeSinceActivity > timeout) {
          console.log(`Closing inactive WebSocket connection for user: ${connection.userId}`);
          ws.close();
          this.connections.delete(ws);
        } else if (ws.readyState === WebSocket.OPEN) {
          // Send ping to keep connection alive
          this.sendMessage(ws, { type: 'ping', data: { timestamp: now.getTime() } });
        }
      }
    }, 30000); // Check every 30 seconds
  }

  destroy(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    // Close all connections
    for (const [ws] of this.connections.entries()) {
      ws.close();
    }
    
    this.connections.clear();
  }
}