import TerminalService, { TerminalSession, TerminalConfiguration } from './terminal-service';
import { WebSocket } from 'ws';

export interface TerminalWebSocketMessage {
  type: 'create_session' | 'send_input' | 'resize_terminal' | 'list_sessions' | 'terminate_session';
  sessionId?: string;
  data?: any;
  config?: Partial<TerminalConfiguration>;
  cols?: number;
  rows?: number;
}

export interface TerminalWebSocketResponse {
  type: 'session_created' | 'output' | 'error' | 'session_list' | 'session_terminated';
  sessionId?: string;
  session?: TerminalSession;
  sessions?: TerminalSession[];
  data?: string;
  message?: string;
}

export class TerminalWebSocketHandler {
  private connections: Map<string, WebSocket> = new Map();
  private terminalService: TerminalService;
  private userSessions: Map<string, Set<string>> = new Map();

  constructor() {
    this.terminalService = new TerminalService();
    this.setupTerminalEventHandlers();
  }

  handleConnection(ws: WebSocket, userId: string): void {
    const connectionId = this.generateConnectionId();
    this.connections.set(connectionId, ws);

    // Initialize user session tracking
    if (!this.userSessions.has(userId)) {
      this.userSessions.set(userId, new Set());
    }

    ws.on('message', async (message: string) => {
      try {
        const data: TerminalWebSocketMessage = JSON.parse(message);
        await this.handleMessage(ws, userId, data);
      } catch (error) {
        this.sendError(ws, 'Invalid message format', error.message);
      }
    });

    ws.on('close', () => {
      this.handleDisconnection(connectionId, userId);
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.handleDisconnection(connectionId, userId);
    });

    // Send initial session list
    this.sendSessionList(ws, userId);
  }

  private async handleMessage(
    ws: WebSocket,
    userId: string,
    data: TerminalWebSocketMessage
  ): Promise<void> {
    try {
      switch (data.type) {
        case 'create_session':
          await this.handleCreateSession(ws, userId, data.config);
          break;
          
        case 'send_input':
          await this.handleSendInput(ws, userId, data.sessionId!, data.data);
          break;
          
        case 'resize_terminal':
          await this.handleResize(ws, userId, data.sessionId!, data.cols!, data.rows!);
          break;
          
        case 'list_sessions':
          await this.handleListSessions(ws, userId);
          break;
          
        case 'terminate_session':
          await this.handleTerminateSession(ws, userId, data.sessionId!);
          break;
          
        default:
          this.sendError(ws, `Unknown message type: ${data.type}`);
      }
    } catch (error) {
      this.sendError(ws, `Failed to handle message: ${error.message}`);
    }
  }

  private async handleCreateSession(
    ws: WebSocket,
    userId: string,
    config: Partial<TerminalConfiguration> = {}
  ): Promise<void> {
    try {
      const session = await this.terminalService.createSession(userId, config);
      
      // Track session for user
      const userSessionSet = this.userSessions.get(userId);
      if (userSessionSet) {
        userSessionSet.add(session.id);
      }

      // Attach WebSocket to session
      this.terminalService.attachWebSocket(session.id, ws);
      
      this.sendResponse(ws, {
        type: 'session_created',
        sessionId: session.id,
        session
      });
    } catch (error) {
      this.sendError(ws, `Failed to create session: ${error.message}`);
    }
  }

  private async handleSendInput(
    ws: WebSocket,
    userId: string,
    sessionId: string,
    input: string
  ): Promise<void> {
    try {
      // Verify user owns this session
      if (!this.verifySessionOwnership(userId, sessionId)) {
        this.sendError(ws, 'Unauthorized access to session');
        return;
      }

      await this.terminalService.executeCommand(sessionId, input);
    } catch (error) {
      this.sendError(ws, `Failed to send input: ${error.message}`);
    }
  }

  private async handleResize(
    ws: WebSocket,
    userId: string,
    sessionId: string,
    cols: number,
    rows: number
  ): Promise<void> {
    try {
      // Verify user owns this session
      if (!this.verifySessionOwnership(userId, sessionId)) {
        this.sendError(ws, 'Unauthorized access to session');
        return;
      }

      // Terminal resize is handled internally by the service
      // This is a placeholder for future implementation
      console.log(`Resize terminal ${sessionId} to ${cols}x${rows}`);
    } catch (error) {
      this.sendError(ws, `Failed to resize terminal: ${error.message}`);
    }
  }

  private async handleListSessions(ws: WebSocket, userId: string): Promise<void> {
    try {
      const sessions = this.terminalService.getUserSessions(userId);
      this.sendResponse(ws, {
        type: 'session_list',
        sessions
      });
    } catch (error) {
      this.sendError(ws, `Failed to list sessions: ${error.message}`);
    }
  }

  private async handleTerminateSession(
    ws: WebSocket,
    userId: string,
    sessionId: string
  ): Promise<void> {
    try {
      // Verify user owns this session
      if (!this.verifySessionOwnership(userId, sessionId)) {
        this.sendError(ws, 'Unauthorized access to session');
        return;
      }

      await this.terminalService.terminateSession(sessionId);
      
      // Remove from user session tracking
      const userSessionSet = this.userSessions.get(userId);
      if (userSessionSet) {
        userSessionSet.delete(sessionId);
      }

      this.sendResponse(ws, {
        type: 'session_terminated',
        sessionId
      });
    } catch (error) {
      this.sendError(ws, `Failed to terminate session: ${error.message}`);
    }
  }

  private handleDisconnection(connectionId: string, userId: string): void {
    this.connections.delete(connectionId);
    
    // Clean up user sessions if no more connections
    const hasActiveConnections = Array.from(this.connections.values()).some(ws => {
      // In a real implementation, we would track userId per connection
      return true; // Placeholder
    });

    if (!hasActiveConnections) {
      // Terminate all user sessions
      const userSessionSet = this.userSessions.get(userId);
      if (userSessionSet) {
        for (const sessionId of userSessionSet) {
          this.terminalService.terminateSession(sessionId);
        }
        this.userSessions.delete(userId);
      }
    }
  }

  private setupTerminalEventHandlers(): void {
    this.terminalService.on('output', (sessionId: string, data: string) => {
      // Broadcast output to all connected clients for this session
      this.broadcastToSessionClients(sessionId, {
        type: 'output',
        sessionId,
        data
      });
    });

    this.terminalService.on('sessionTerminated', (sessionId: string) => {
      this.broadcastToSessionClients(sessionId, {
        type: 'session_terminated',
        sessionId
      });
    });
  }

  private broadcastToSessionClients(sessionId: string, message: TerminalWebSocketResponse): void {
    // In a real implementation, we would track which clients are connected to which sessions
    for (const [, ws] of this.connections) {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    }
  }

  private sendResponse(ws: WebSocket, response: TerminalWebSocketResponse): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(response));
    }
  }

  private sendError(ws: WebSocket, message: string, details?: string): void {
    const errorResponse: TerminalWebSocketResponse = {
      type: 'error',
      message,
      data: details
    };
    this.sendResponse(ws, errorResponse);
  }

  private sendSessionList(ws: WebSocket, userId: string): void {
    const sessions = this.terminalService.getUserSessions(userId);
    this.sendResponse(ws, {
      type: 'session_list',
      sessions
    });
  }

  private verifySessionOwnership(userId: string, sessionId: string): boolean {
    const session = this.terminalService.getSession(sessionId);
    return session ? session.userId === userId : false;
  }

  private generateConnectionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
}

export default TerminalWebSocketHandler;