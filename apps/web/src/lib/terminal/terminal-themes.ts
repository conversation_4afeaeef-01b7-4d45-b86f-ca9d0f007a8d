import { TerminalTheme } from './terminal-service';

export class TerminalThemeManager {
  private themes: Map<string, TerminalTheme> = new Map();
  private defaultTheme: string = 'dark';

  constructor() {
    this.loadDefaultThemes();
  }

  private loadDefaultThemes(): void {
    // Dark theme
    this.themes.set('dark', {
      name: 'Dark',
      background: '#1e1e1e',
      foreground: '#d4d4d4',
      cursor: '#aeafad',
      selection: '#264f78',
      black: '#000000',
      red: '#cd3131',
      green: '#0dbc79',
      yellow: '#e5e510',
      blue: '#2472c8',
      magenta: '#bc3fbc',
      cyan: '#11a8cd',
      white: '#e5e5e5',
      brightBlack: '#666666',
      brightRed: '#f14c4c',
      brightGreen: '#23d18b',
      brightYellow: '#f5f543',
      brightBlue: '#3b8eea',
      brightMagenta: '#d670d6',
      brightCyan: '#29b8db',
      brightWhite: '#ffffff'
    });

    // Light theme
    this.themes.set('light', {
      name: 'Light',
      background: '#ffffff',
      foreground: '#383a42',
      cursor: '#526fff',
      selection: '#c9d1d9',
      black: '#383a42',
      red: '#e45649',
      green: '#50a14f',
      yellow: '#c18401',
      blue: '#4078f2',
      magenta: '#a626a4',
      cyan: '#0184bc',
      white: '#fafafa',
      brightBlack: '#4f525d',
      brightRed: '#e06c75',
      brightGreen: '#98c379',
      brightYellow: '#e5c07b',
      brightBlue: '#61afef',
      brightMagenta: '#c678dd',
      brightCyan: '#56b6c2',
      brightWhite: '#ffffff'
    });

    // Monokai theme
    this.themes.set('monokai', {
      name: 'Monokai',
      background: '#272822',
      foreground: '#f8f8f2',
      cursor: '#f8f8f0',
      selection: '#49483e',
      black: '#272822',
      red: '#f92672',
      green: '#a6e22e',
      yellow: '#f4bf75',
      blue: '#66d9ef',
      magenta: '#ae81ff',
      cyan: '#a1efe4',
      white: '#f8f8f2',
      brightBlack: '#75715e',
      brightRed: '#f92672',
      brightGreen: '#a6e22e',
      brightYellow: '#f4bf75',
      brightBlue: '#66d9ef',
      brightMagenta: '#ae81ff',
      brightCyan: '#a1efe4',
      brightWhite: '#f9f8f5'
    });

    // Solarized Dark theme
    this.themes.set('solarized-dark', {
      name: 'Solarized Dark',
      background: '#002b36',
      foreground: '#839496',
      cursor: '#93a1a1',
      selection: '#073642',
      black: '#073642',
      red: '#dc322f',
      green: '#859900',
      yellow: '#b58900',
      blue: '#268bd2',
      magenta: '#d33682',
      cyan: '#2aa198',
      white: '#eee8d5',
      brightBlack: '#002b36',
      brightRed: '#cb4b16',
      brightGreen: '#586e75',
      brightYellow: '#657b83',
      brightBlue: '#839496',
      brightMagenta: '#6c71c4',
      brightCyan: '#93a1a1',
      brightWhite: '#fdf6e3'
    });

    // Solarized Light theme
    this.themes.set('solarized-light', {
      name: 'Solarized Light',
      background: '#fdf6e3',
      foreground: '#657b83',
      cursor: '#586e75',
      selection: '#eee8d5',
      black: '#073642',
      red: '#dc322f',
      green: '#859900',
      yellow: '#b58900',
      blue: '#268bd2',
      magenta: '#d33682',
      cyan: '#2aa198',
      white: '#eee8d5',
      brightBlack: '#002b36',
      brightRed: '#cb4b16',
      brightGreen: '#586e75',
      brightYellow: '#657b83',
      brightBlue: '#839496',
      brightMagenta: '#6c71c4',
      brightCyan: '#93a1a1',
      brightWhite: '#fdf6e3'
    });

    // Dracula theme
    this.themes.set('dracula', {
      name: 'Dracula',
      background: '#282a36',
      foreground: '#f8f8f2',
      cursor: '#f8f8f0',
      selection: '#44475a',
      black: '#000000',
      red: '#ff5555',
      green: '#50fa7b',
      yellow: '#f1fa8c',
      blue: '#bd93f9',
      magenta: '#ff79c6',
      cyan: '#8be9fd',
      white: '#bfbfbf',
      brightBlack: '#4d4d4d',
      brightRed: '#ff6e67',
      brightGreen: '#5af78e',
      brightYellow: '#f4f99d',
      brightBlue: '#caa9fa',
      brightMagenta: '#ff92d0',
      brightCyan: '#9aedfe',
      brightWhite: '#e6e6e6'
    });

    // One Dark theme
    this.themes.set('one-dark', {
      name: 'One Dark',
      background: '#282c34',
      foreground: '#abb2bf',
      cursor: '#528bff',
      selection: '#3e4451',
      black: '#282c34',
      red: '#e06c75',
      green: '#98c379',
      yellow: '#e5c07b',
      blue: '#61afef',
      magenta: '#c678dd',
      cyan: '#56b6c2',
      white: '#abb2bf',
      brightBlack: '#545862',
      brightRed: '#e06c75',
      brightGreen: '#98c379',
      brightYellow: '#e5c07b',
      brightBlue: '#61afef',
      brightMagenta: '#c678dd',
      brightCyan: '#56b6c2',
      brightWhite: '#c8ccd4'
    });

    // Material theme
    this.themes.set('material', {
      name: 'Material',
      background: '#263238',
      foreground: '#eeffff',
      cursor: '#ffcc00',
      selection: '#314549',
      black: '#000000',
      red: '#f07178',
      green: '#c3e88d',
      yellow: '#ffcb6b',
      blue: '#82aaff',
      magenta: '#c792ea',
      cyan: '#89ddff',
      white: '#ffffff',
      brightBlack: '#546e7a',
      brightRed: '#f07178',
      brightGreen: '#c3e88d',
      brightYellow: '#ffcb6b',
      brightBlue: '#82aaff',
      brightMagenta: '#c792ea',
      brightCyan: '#89ddff',
      brightWhite: '#ffffff'
    });
  }

  getTheme(name: string): TerminalTheme | undefined {
    return this.themes.get(name);
  }

  getDefaultTheme(): TerminalTheme {
    return this.themes.get(this.defaultTheme)!;
  }

  getAllThemes(): TerminalTheme[] {
    return Array.from(this.themes.values());
  }

  addTheme(name: string, theme: TerminalTheme): void {
    this.themes.set(name, theme);
  }

  removeTheme(name: string): boolean {
    return this.themes.delete(name);
  }

  setDefaultTheme(name: string): void {
    if (this.themes.has(name)) {
      this.defaultTheme = name;
    }
  }

  getThemeNames(): string[] {
    return Array.from(this.themes.keys());
  }

  validateTheme(theme: TerminalTheme): boolean {
    const requiredProperties = [
      'name', 'background', 'foreground', 'cursor', 'selection',
      'black', 'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white',
      'brightBlack', 'brightRed', 'brightGreen', 'brightYellow', 
      'brightBlue', 'brightMagenta', 'brightCyan', 'brightWhite'
    ];

    return requiredProperties.every(prop => 
      prop in theme && typeof theme[prop as keyof TerminalTheme] === 'string'
    );
  }

  createCustomTheme(baseTheme: string, customizations: Partial<TerminalTheme>): TerminalTheme {
    const base = this.getTheme(baseTheme) || this.getDefaultTheme();
    return {
      ...base,
      ...customizations
    };
  }

  exportTheme(name: string): string | null {
    const theme = this.getTheme(name);
    if (!theme) return null;
    
    return JSON.stringify(theme, null, 2);
  }

  importTheme(themeJson: string): TerminalTheme | null {
    try {
      const theme = JSON.parse(themeJson);
      if (this.validateTheme(theme)) {
        return theme;
      }
    } catch (error) {
      console.error('Failed to import theme:', error);
    }
    return null;
  }

  // Generate theme preview
  generateThemePreview(theme: TerminalTheme): string {
    return `
      <div style="background: ${theme.background}; color: ${theme.foreground}; padding: 10px; font-family: monospace;">
        <div style="margin-bottom: 5px;">
          <span style="color: ${theme.red};">user</span>@<span style="color: ${theme.green};">host</span>:<span style="color: ${theme.blue};">~</span>$ <span style="color: ${theme.yellow};">ls -la</span>
        </div>
        <div style="margin-bottom: 5px;">
          <span style="color: ${theme.cyan};">drwxr-xr-x</span> <span style="color: ${theme.white};">5</span> <span style="color: ${theme.green};">user</span> <span style="color: ${theme.green};">user</span> <span style="color: ${theme.white};">4096</span> <span style="color: ${theme.magenta};">Dec 15 10:30</span> <span style="color: ${theme.blue};">.</span>
        </div>
        <div style="margin-bottom: 5px;">
          <span style="color: ${theme.cyan};">drwxr-xr-x</span> <span style="color: ${theme.white};">3</span> <span style="color: ${theme.green};">user</span> <span style="color: ${theme.green};">user</span> <span style="color: ${theme.white};">4096</span> <span style="color: ${theme.magenta};">Dec 15 10:29</span> <span style="color: ${theme.blue};">..</span>
        </div>
        <div style="margin-bottom: 5px;">
          <span style="color: ${theme.green};">-rw-r--r--</span> <span style="color: ${theme.white};">1</span> <span style="color: ${theme.green};">user</span> <span style="color: ${theme.green};">user</span> <span style="color: ${theme.white};">1234</span> <span style="color: ${theme.magenta};">Dec 15 10:28</span> <span style="color: ${theme.white};">file.txt</span>
        </div>
      </div>
    `;
  }
}

export const terminalThemeManager = new TerminalThemeManager();
export default terminalThemeManager;