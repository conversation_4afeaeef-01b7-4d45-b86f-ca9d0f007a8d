import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import crypto from 'crypto';
import { TerminalSession, TerminalConfiguration, ShellType } from './terminal-service';

interface ProcessInfo {
  process: ChildProcess;
  isActive: boolean;
  createdAt: Date;
  lastActivity: Date;
}

export class TerminalService extends EventEmitter {
  private sessions: Map<string, TerminalSession> = new Map();
  private processes: Map<string, ProcessInfo> = new Map();
  private websockets: Map<string, WebSocket> = new Map();
  private readonly maxSessions = 10;
  private readonly sessionTimeout = 30 * 60 * 1000; // 30 minutes

  constructor() {
    super();
    this.setupCleanupTimer();
  }

  async createSession(config: TerminalConfiguration): Promise<TerminalSession> {
    // Check session limit
    if (this.sessions.size >= this.maxSessions) {
      await this.cleanupInactiveSessions();
      if (this.sessions.size >= this.maxSessions) {
        throw new Error('Maximum number of sessions reached');
      }
    }

    const sessionId = this.generateSessionId();
    
    try {
      // Create PTY process based on platform
      const ptyProcess = await this.createPtyProcess(config);
      
      // Create session object
      const session: TerminalSession = {
        id: sessionId,
        name: config.name || `Terminal ${sessionId.slice(-4)}`,
        shell: config.shell || this.getDefaultShell(),
        cwd: config.cwd || process.cwd(),
        environment: config.environment || {},
        state: {
          isActive: true,
          pid: ptyProcess.pid,
          scrollPosition: 0,
          buffer: ''
        },
        history: [],
        createdAt: new Date(),
        lastActivity: new Date()
      };

      // Store session and process
      this.sessions.set(sessionId, session);
      this.processes.set(sessionId, {
        process: ptyProcess,
        isActive: true,
        createdAt: new Date(),
        lastActivity: new Date()
      });

      // Setup process event handlers
      this.setupProcessHandlers(sessionId, ptyProcess);

      return session;
    } catch (error) {
      throw new Error(`Failed to create terminal session: ${error}`);
    }
  }

  async executeCommand(sessionId: string, command: string): Promise<void> {
    const processInfo = this.processes.get(sessionId);
    const session = this.sessions.get(sessionId);

    if (!processInfo || !session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    if (!processInfo.isActive) {
      throw new Error(`Session ${sessionId} is not active`);
    }

    try {
      // Validate command for security
      await this.validateCommand(command);

      // Add to history
      session.history.push({
        command,
        timestamp: new Date(),
        cwd: session.cwd
      });

      // Send command to process
      processInfo.process.stdin?.write(command + '\r\n');

      // Update last activity
      session.lastActivity = new Date();
      processInfo.lastActivity = new Date();

      this.emit('commandExecuted', { sessionId, command });
    } catch (error) {
      throw new Error(`Failed to execute command: ${error}`);
    }
  }

  async resizeTerminal(sessionId: string, cols: number, rows: number): Promise<void> {
    const processInfo = this.processes.get(sessionId);
    
    if (!processInfo) {
      throw new Error(`Session ${sessionId} not found`);
    }

    try {
      // Resize the PTY
      if (processInfo.process.resize) {
        processInfo.process.resize(cols, rows);
      }
    } catch (error) {
      console.error(`Failed to resize terminal ${sessionId}:`, error);
    }
  }

  async getSession(sessionId: string): Promise<TerminalSession | null> {
    return this.sessions.get(sessionId) || null;
  }

  async listSessions(): Promise<TerminalSession[]> {
    return Array.from(this.sessions.values());
  }

  async deleteSession(sessionId: string): Promise<void> {
    const processInfo = this.processes.get(sessionId);
    
    if (processInfo) {
      // Kill the process
      try {
        processInfo.process.kill('SIGTERM');
        setTimeout(() => {
          if (!processInfo.process.killed) {
            processInfo.process.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        console.error(`Failed to kill process for session ${sessionId}:`, error);
      }
    }

    // Remove from maps
    this.sessions.delete(sessionId);
    this.processes.delete(sessionId);
    this.websockets.delete(sessionId);

    this.emit('sessionDeleted', { sessionId });
  }

  private async createPtyProcess(config: TerminalConfiguration): Promise<ChildProcess> {
    const shell = config.shell || this.getDefaultShell();
    const cwd = config.cwd || process.cwd();
    
    // For security, use a restricted environment
    const env = {
      ...process.env,
      ...config.environment,
      TERM: 'xterm-256color',
      COLORTERM: 'truecolor',
      PATH: this.getRestrictedPath()
    };

    try {
      const ptyProcess = spawn(shell, [], {
        cwd,
        env,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: false
      });

      return ptyProcess;
    } catch (error) {
      throw new Error(`Failed to spawn shell process: ${error}`);
    }
  }

  private setupProcessHandlers(sessionId: string, process: ChildProcess): void {
    const session = this.sessions.get(sessionId);
    const processInfo = this.processes.get(sessionId);
    
    if (!session || !processInfo) return;

    process.stdout?.on('data', (data: Buffer) => {
      const output = data.toString();
      session.state.buffer += output;
      session.lastActivity = new Date();
      processInfo.lastActivity = new Date();
      
      this.emit('processOutput', { sessionId, data: output });
    });

    process.stderr?.on('data', (data: Buffer) => {
      const output = data.toString();
      session.state.buffer += output;
      session.lastActivity = new Date();
      processInfo.lastActivity = new Date();
      
      this.emit('processOutput', { sessionId, data: output });
    });

    process.on('exit', (code: number | null) => {
      session.state.isActive = false;
      session.state.exitCode = code || undefined;
      processInfo.isActive = false;
      
      this.emit('processExit', { sessionId, exitCode: code });
    });

    process.on('error', (error: Error) => {
      console.error(`Process error for session ${sessionId}:`, error);
      session.state.isActive = false;
      processInfo.isActive = false;
      
      this.emit('processError', { sessionId, error: error.message });
    });
  }

  private async validateCommand(command: string): Promise<void> {
    // Basic security validation
    const dangerousCommands = [
      'rm -rf /',
      'sudo rm',
      'mkfs',
      'dd if=',
      'chmod 777 /',
      'curl | sh',
      'wget | sh',
      '> /dev/sda',
      'format',
      'fdisk'
    ];

    const commandLower = command.toLowerCase();
    for (const dangerous of dangerousCommands) {
      if (commandLower.includes(dangerous.toLowerCase())) {
        throw new Error(`Dangerous command blocked: ${dangerous}`);
      }
    }

    // Check for command injection attempts
    if (command.includes('$(') || command.includes('`') || command.includes('&&') || command.includes('||')) {
      // Allow some safe uses but be cautious
      console.warn(`Potentially dangerous command: ${command}`);
    }
  }

  private getDefaultShell(): ShellType {
    if (process.platform === 'win32') {
      return 'powershell';
    }
    return process.env.SHELL?.includes('zsh') ? 'zsh' : 'bash';
  }

  private getRestrictedPath(): string {
    // Provide a restricted PATH for security
    const safePaths = [
      '/usr/local/bin',
      '/usr/bin',
      '/bin',
      '/usr/local/sbin',
      '/usr/sbin',
      '/sbin'
    ];
    
    if (process.platform === 'win32') {
      return process.env.PATH || '';
    }
    
    return safePaths.join(':');
  }

  private generateSessionId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  private setupCleanupTimer(): void {
    setInterval(async () => {
      await this.cleanupInactiveSessions();
    }, 5 * 60 * 1000); // Run every 5 minutes
  }

  private async cleanupInactiveSessions(): Promise<void> {
    const now = new Date();
    const sessionsToRemove: string[] = [];

    for (const [sessionId, session] of this.sessions.entries()) {
      const timeSinceActivity = now.getTime() - session.lastActivity.getTime();
      
      if (timeSinceActivity > this.sessionTimeout || !session.state.isActive) {
        sessionsToRemove.push(sessionId);
      }
    }

    for (const sessionId of sessionsToRemove) {
      await this.deleteSession(sessionId);
    }

    if (sessionsToRemove.length > 0) {
      console.log(`Cleaned up ${sessionsToRemove.length} inactive terminal sessions`);
    }
  }
}