import { spawn } from 'child_process';
import { WebSocket } from 'ws';
import { EventEmitter } from 'events';
import { createHash } from 'crypto';

// Terminal Session Types
export interface TerminalSession {
  id: string;
  name: string;
  shell: ShellType;
  cwd: string;
  environment: Record<string, string>;
  state: TerminalState;
  history: CommandHistory[];
  createdAt: Date;
  lastActivity: Date;
  userId: string;
}

export interface TerminalState {
  isActive: boolean;
  pid?: number;
  exitCode?: number;
  scrollPosition: number;
  selection?: SelectionRange;
  buffer: string;
}

export interface CommandHistory {
  command: string;
  timestamp: Date;
  cwd: string;
  exitCode?: number;
  duration?: number;
}

export interface SelectionRange {
  start: { row: number; col: number };
  end: { row: number; col: number };
}

export type ShellType = 'bash' | 'zsh' | 'sh' | 'fish' | 'powershell' | 'cmd';

export interface TerminalConfiguration {
  shell: ShellType;
  theme: TerminalTheme;
  fontSize: number;
  fontFamily: string;
  scrollback: number;
  bellStyle: 'none' | 'sound' | 'visual';
  cursorBlink: boolean;
  cursorStyle: 'block' | 'underline' | 'bar';
  allowTransparency: boolean;
}

export interface TerminalTheme {
  name: string;
  background: string;
  foreground: string;
  cursor: string;
  selection: string;
  black: string;
  red: string;
  green: string;
  yellow: string;
  blue: string;
  magenta: string;
  cyan: string;
  white: string;
  brightBlack: string;
  brightRed: string;
  brightGreen: string;
  brightYellow: string;
  brightBlue: string;
  brightMagenta: string;
  brightCyan: string;
  brightWhite: string;
}

class TerminalSecurityError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TerminalSecurityError';
  }
}

class TerminalSessionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TerminalSessionError';
  }
}

export class TerminalService extends EventEmitter {
  private sessions: Map<string, TerminalSession> = new Map();
  private processes: Map<string, any> = new Map();
  private websockets: Map<string, WebSocket> = new Map();
  private dangerousCommands: string[] = [
    'rm -rf /',
    'sudo rm',
    'mkfs',
    'dd if=',
    'chmod 777 /',
    'curl | sh',
    'wget | sh',
    '> /dev/sda',
    'format c:',
    'del /f /s /q',
    'shutdown',
    'reboot',
    'halt'
  ];

  constructor() {
    super();
    this.setupCleanupHandlers();
  }

  async createSession(
    userId: string,
    config: Partial<TerminalConfiguration> & { name?: string; cwd?: string }
  ): Promise<TerminalSession> {
    const sessionId = this.generateSessionId();
    
    try {
      // Create session object
      const session: TerminalSession = {
        id: sessionId,
        name: config.name || `Terminal ${sessionId.slice(-4)}`,
        shell: config.shell || this.getDefaultShell(),
        cwd: config.cwd || process.cwd(),
        environment: {
          ...process.env,
          TERM: 'xterm-256color',
          COLORTERM: 'truecolor'
        },
        state: {
          isActive: true,
          scrollPosition: 0,
          buffer: ''
        },
        history: [],
        createdAt: new Date(),
        lastActivity: new Date(),
        userId
      };

      // Create PTY process for supported platforms
      if (typeof window === 'undefined' && process.platform !== 'browser') {
        const ptyProcess = await this.createPtyProcess(session);
        this.processes.set(sessionId, ptyProcess);
        session.state.pid = ptyProcess.pid;
      }

      // Store session
      this.sessions.set(sessionId, session);
      
      // Setup session handlers
      this.setupSessionHandlers(sessionId);
      
      this.emit('sessionCreated', session);
      return session;
    } catch (error) {
      throw new TerminalSessionError(`Failed to create session: ${error.message}`);
    }
  }

  async executeCommand(sessionId: string, command: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new TerminalSessionError(`Session ${sessionId} not found`);
    }

    try {
      // Validate command for security
      await this.validateCommand(command, session.userId);
      
      // Add to history
      const historyEntry: CommandHistory = {
        command,
        timestamp: new Date(),
        cwd: session.cwd
      };
      session.history.push(historyEntry);
      
      // Execute command
      const process = this.processes.get(sessionId);
      if (process && process.write) {
        process.write(command + '\r');
      } else {
        // Fallback for browser environment
        await this.executeCommandFallback(sessionId, command);
      }
      
      // Update last activity
      session.lastActivity = new Date();
      
      this.emit('commandExecuted', sessionId, command);
    } catch (error) {
      throw new TerminalSessionError(`Failed to execute command: ${error.message}`);
    }
  }

  async terminateSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new TerminalSessionError(`Session ${sessionId} not found`);
    }

    try {
      // Terminate process
      const process = this.processes.get(sessionId);
      if (process) {
        process.kill();
        this.processes.delete(sessionId);
      }

      // Close WebSocket
      const ws = this.websockets.get(sessionId);
      if (ws) {
        ws.close();
        this.websockets.delete(sessionId);
      }

      // Update session state
      session.state.isActive = false;
      session.state.exitCode = 0;
      
      // Remove from active sessions
      this.sessions.delete(sessionId);
      
      this.emit('sessionTerminated', sessionId);
    } catch (error) {
      throw new TerminalSessionError(`Failed to terminate session: ${error.message}`);
    }
  }

  getSession(sessionId: string): TerminalSession | undefined {
    return this.sessions.get(sessionId);
  }

  getUserSessions(userId: string): TerminalSession[] {
    return Array.from(this.sessions.values()).filter(session => session.userId === userId);
  }

  private async createPtyProcess(session: TerminalSession): Promise<any> {
    try {
      // For server-side environments, we would use node-pty
      // For browser environments, we'll use a WebSocket connection
      if (typeof window !== 'undefined') {
        return this.createWebSocketProcess(session);
      }

      // Server-side PTY creation (commented out for browser compatibility)
      /*
      const pty = spawn(session.shell, [], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        cwd: session.cwd,
        env: session.environment
      });
      
      return pty;
      */
      
      return this.createWebSocketProcess(session);
    } catch (error) {
      throw new TerminalSessionError(`Failed to create PTY process: ${error.message}`);
    }
  }

  private createWebSocketProcess(session: TerminalSession): any {
    // Create a mock process for WebSocket communication
    return {
      pid: Math.floor(Math.random() * 10000),
      write: (data: string) => {
        const ws = this.websockets.get(session.id);
        if (ws && ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({
            type: 'input',
            sessionId: session.id,
            data
          }));
        }
      },
      kill: () => {
        const ws = this.websockets.get(session.id);
        if (ws) {
          ws.close();
        }
      }
    };
  }

  private async executeCommandFallback(sessionId: string, command: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // Simple command execution for demonstration
    // In a real implementation, this would connect to a backend service
    let output = '';
    
    try {
      if (command.startsWith('echo ')) {
        output = command.substring(5) + '\n';
      } else if (command === 'pwd') {
        output = session.cwd + '\n';
      } else if (command === 'date') {
        output = new Date().toString() + '\n';
      } else if (command === 'whoami') {
        output = session.userId + '\n';
      } else if (command.startsWith('cd ')) {
        const newDir = command.substring(3).trim();
        // Simple directory change simulation
        session.cwd = newDir.startsWith('/') ? newDir : `${session.cwd}/${newDir}`;
        output = '';
      } else {
        output = `Command not found: ${command}\n`;
      }
      
      // Add output to buffer
      session.state.buffer += output;
      
      // Emit output event
      this.emit('output', sessionId, output);
    } catch (error) {
      const errorOutput = `Error executing command: ${error.message}\n`;
      session.state.buffer += errorOutput;
      this.emit('output', sessionId, errorOutput);
    }
  }

  private async validateCommand(command: string, userId: string): Promise<void> {
    // Check for dangerous commands
    for (const dangerous of this.dangerousCommands) {
      if (command.includes(dangerous)) {
        throw new TerminalSecurityError(`Dangerous command blocked: ${dangerous}`);
      }
    }

    // Check for suspicious patterns
    if (command.includes('$(') || command.includes('`') || command.includes(';;')) {
      throw new TerminalSecurityError('Suspicious command pattern detected');
    }

    // Rate limiting check
    const sessionHistory = Array.from(this.sessions.values())
      .filter(session => session.userId === userId)
      .flatMap(session => session.history)
      .filter(entry => Date.now() - entry.timestamp.getTime() < 60000); // Last minute

    if (sessionHistory.length > 100) {
      throw new TerminalSecurityError('Command rate limit exceeded');
    }
  }

  private setupSessionHandlers(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // Setup periodic cleanup
    const cleanupInterval = setInterval(() => {
      const currentSession = this.sessions.get(sessionId);
      if (!currentSession || !currentSession.state.isActive) {
        clearInterval(cleanupInterval);
        return;
      }

      // Check for inactive sessions
      const inactiveTime = Date.now() - currentSession.lastActivity.getTime();
      if (inactiveTime > 30 * 60 * 1000) { // 30 minutes
        this.terminateSession(sessionId);
      }
    }, 60000); // Check every minute
  }

  private setupCleanupHandlers(): void {
    // Cleanup on process exit
    const cleanup = () => {
      for (const [sessionId] of this.sessions) {
        this.terminateSession(sessionId);
      }
    };

    process.on('exit', cleanup);
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
  }

  private generateSessionId(): string {
    return createHash('sha256')
      .update(Date.now().toString() + Math.random().toString())
      .digest('hex')
      .substring(0, 16);
  }

  private getDefaultShell(): ShellType {
    if (process.platform === 'win32') {
      return 'cmd';
    } else if (process.platform === 'darwin') {
      return 'zsh';
    } else {
      return 'bash';
    }
  }

  // WebSocket connection management
  attachWebSocket(sessionId: string, ws: WebSocket): void {
    this.websockets.set(sessionId, ws);
    
    ws.on('message', async (message: string) => {
      try {
        const data = JSON.parse(message);
        await this.handleWebSocketMessage(sessionId, data);
      } catch (error) {
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Invalid message format'
        }));
      }
    });

    ws.on('close', () => {
      this.websockets.delete(sessionId);
    });
  }

  private async handleWebSocketMessage(sessionId: string, data: any): Promise<void> {
    switch (data.type) {
      case 'input':
        await this.executeCommand(sessionId, data.data);
        break;
      case 'resize':
        await this.resizeTerminal(sessionId, data.cols, data.rows);
        break;
      default:
        console.warn(`Unknown message type: ${data.type}`);
    }
  }

  private async resizeTerminal(sessionId: string, cols: number, rows: number): Promise<void> {
    const process = this.processes.get(sessionId);
    if (process && process.resize) {
      process.resize(cols, rows);
    }
  }
}

export default TerminalService;