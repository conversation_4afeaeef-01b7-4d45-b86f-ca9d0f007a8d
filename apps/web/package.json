{"name": "unified-code-editor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.525.0", "monaco-editor": "^0.52.2", "next": "14.2.13", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-resizable-panels": "^3.0.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.6", "ai": "^4.0.15", "openai": "^4.75.1", "@anthropic-ai/sdk": "^0.27.3", "cohere-ai": "^7.14.0", "llamaindex": "^0.9.18", "zod": "^3.24.1", "@types/throttle-debounce": "^5.0.2", "throttle-debounce": "^5.0.2", "lru-cache": "^11.0.2", "fabric": "^6.5.0", "pixi.js": "^8.5.2", "gl-matrix": "^3.4.3", "canvas-to-blob": "^1.0.0", "file-saver": "^2.0.5"}, "devDependencies": {"@types/node": "^20.17.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "14.2.13", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "@types/fabric": "^1.5.45", "@types/file-saver": "^2.0.7"}}