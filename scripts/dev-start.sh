#!/bin/bash

# Unified Development Start Script
# 
# LEVER approach implementation:
# L - Leverage existing module start scripts
# E - Extend with unified development orchestration
# V - Verify through health checks and status monitoring
# E - Eliminate manual module startup complexity
# R - Reduce development friction through automation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
MODULES=("agent-inbox" "foto-fun" "vibe-kanban" "open-canvas" "vcode" "gen-ui-computer-use" "social-media-agent")
PACKAGES=("types" "ai" "events" "ui" "foundation" "state")
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_INTERVAL=2

# PID tracking
declare -A MODULE_PIDS
declare -A PACKAGE_PIDS

# Function to print status messages
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_module() {
    echo -e "${PURPLE}📦 $1${NC}"
}

print_health() {
    echo -e "${CYAN}🏥 $1${NC}"
}

# Function to cleanup on exit
cleanup() {
    echo ""
    print_info "Shutting down development environment..."
    
    # Kill all module processes
    for module in "${!MODULE_PIDS[@]}"; do
        local pid=${MODULE_PIDS[$module]}
        if kill -0 $pid 2>/dev/null; then
            print_info "Stopping $module (PID: $pid)"
            kill $pid 2>/dev/null || true
        fi
    done
    
    # Kill all package watch processes
    for package in "${!PACKAGE_PIDS[@]}"; do
        local pid=${PACKAGE_PIDS[$package]}
        if kill -0 $pid 2>/dev/null; then
            print_info "Stopping $package watch (PID: $pid)"
            kill $pid 2>/dev/null || true
        fi
    done
    
    print_status "Development environment stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    else
        return 0
    fi
}

# Function to wait for port to be available
wait_for_port() {
    local port=$1
    local timeout=$2
    local elapsed=0
    
    while [ $elapsed -lt $timeout ]; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            return 0
        fi
        sleep $HEALTH_CHECK_INTERVAL
        elapsed=$((elapsed + HEALTH_CHECK_INTERVAL))
    done
    
    return 1
}

# Function to check module health
check_module_health() {
    local module=$1
    local port=$2
    
    if [ -n "$port" ]; then
        if curl -s "http://localhost:$port/health" >/dev/null 2>&1; then
            return 0
        elif curl -s "http://localhost:$port" >/dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    else
        # For modules without HTTP endpoints, check if process is running
        local pid=${MODULE_PIDS[$module]}
        if [ -n "$pid" ] && kill -0 $pid 2>/dev/null; then
            return 0
        else
            return 1
        fi
    fi
}

# Function to start package watch mode
start_package_watch() {
    local package=$1
    
    print_info "Starting $package package in watch mode..."
    
    cd "packages/$package"
    pnpm run dev > "../../logs/$package.log" 2>&1 &
    local pid=$!
    PACKAGE_PIDS[$package]=$pid
    cd - > /dev/null
    
    print_status "$package package watch started (PID: $pid)"
}

# Function to start module
start_module() {
    local module=$1
    local port=$2
    
    print_module "Starting $module module..."
    
    # Check if module directory exists
    if [ ! -d "$module" ]; then
        print_warning "$module directory not found, skipping..."
        return
    fi
    
    # Check if port is available
    if [ -n "$port" ] && ! check_port $port; then
        print_warning "Port $port is already in use, $module may conflict"
    fi
    
    # Start module
    cd "$module"
    if [ -f "package.json" ]; then
        pnpm run dev > "../logs/$module.log" 2>&1 &
        local pid=$!
        MODULE_PIDS[$module]=$pid
        cd - > /dev/null
        
        print_status "$module started (PID: $pid)"
        
        # Wait for module to be ready
        if [ -n "$port" ]; then
            print_info "Waiting for $module to be ready on port $port..."
            if wait_for_port $port $HEALTH_CHECK_TIMEOUT; then
                print_status "$module is ready on port $port"
            else
                print_warning "$module may not be ready (port $port not responding)"
            fi
        fi
    else
        print_warning "$module does not have package.json, skipping..."
        cd - > /dev/null
    fi
}

# Function to display module status
show_module_status() {
    echo ""
    print_info "Module Status:"
    echo "=============="
    
    for module in "${MODULES[@]}"; do
        if [ -n "${MODULE_PIDS[$module]}" ]; then
            local pid=${MODULE_PIDS[$module]}
            if kill -0 $pid 2>/dev/null; then
                local port=""
                case $module in
                    "agent-inbox") port="3000" ;;
                    "foto-fun") port="3001" ;;
                    "vibe-kanban") port="3002" ;;
                    "open-canvas") port="3003" ;;
                    "vcode") port="3004" ;;
                    "gen-ui-computer-use") port="3005" ;;
                    "social-media-agent") port="3006" ;;
                esac
                
                if check_module_health $module $port; then
                    print_status "$module: Running (PID: $pid, Port: $port)"
                else
                    print_warning "$module: Started but not responding (PID: $pid)"
                fi
            else
                print_error "$module: Process died (PID: $pid)"
            fi
        else
            print_warning "$module: Not started"
        fi
    done
    
    echo ""
    print_info "Package Watch Status:"
    echo "===================="
    
    for package in "${PACKAGES[@]}"; do
        if [ -n "${PACKAGE_PIDS[$package]}" ]; then
            local pid=${PACKAGE_PIDS[$package]}
            if kill -0 $pid 2>/dev/null; then
                print_status "$package: Watching (PID: $pid)"
            else
                print_error "$package: Watch process died (PID: $pid)"
            fi
        else
            print_warning "$package: Not watching"
        fi
    done
}

# Function to show logs
show_logs() {
    local target=$1
    
    if [ -z "$target" ]; then
        print_info "Available logs:"
        ls -la logs/ 2>/dev/null || print_warning "No logs directory found"
        return
    fi
    
    local log_file="logs/$target.log"
    if [ -f "$log_file" ]; then
        print_info "Showing logs for $target (press Ctrl+C to stop):"
        tail -f "$log_file"
    else
        print_error "Log file not found: $log_file"
    fi
}

# Function to restart module
restart_module() {
    local module=$1
    
    print_info "Restarting $module..."
    
    # Stop module if running
    if [ -n "${MODULE_PIDS[$module]}" ]; then
        local pid=${MODULE_PIDS[$module]}
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            sleep 2
        fi
        unset MODULE_PIDS[$module]
    fi
    
    # Start module
    local port=""
    case $module in
        "agent-inbox") port="3000" ;;
        "foto-fun") port="3001" ;;
        "vibe-kanban") port="3002" ;;
        "open-canvas") port="3003" ;;
        "vcode") port="3004" ;;
        "gen-ui-computer-use") port="3005" ;;
        "social-media-agent") port="3006" ;;
    esac
    
    start_module $module $port
}

# Function to show help
show_help() {
    echo -e "${BLUE}Unified AI Assistant Platform - Development Environment${NC}"
    echo "======================================================"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  start                 Start all modules and packages"
    echo "  stop                  Stop all modules and packages"
    echo "  status                Show status of all modules and packages"
    echo "  logs [module]         Show logs for specific module or list all"
    echo "  restart [module]      Restart specific module"
    echo "  health                Run health checks on all modules"
    echo "  help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start              # Start everything"
    echo "  $0 logs agent-inbox   # Show agent-inbox logs"
    echo "  $0 restart foto-fun   # Restart foto-fun module"
    echo "  $0 status             # Show status of all components"
    echo ""
}

# Function to run health checks
run_health_checks() {
    print_health "Running health checks..."
    
    local healthy_count=0
    local total_count=0
    
    for module in "${MODULES[@]}"; do
        if [ -n "${MODULE_PIDS[$module]}" ]; then
            total_count=$((total_count + 1))
            local port=""
            case $module in
                "agent-inbox") port="3000" ;;
                "foto-fun") port="3001" ;;
                "vibe-kanban") port="3002" ;;
                "open-canvas") port="3003" ;;
                "vcode") port="3004" ;;
                "gen-ui-computer-use") port="3005" ;;
                "social-media-agent") port="3006" ;;
            esac
            
            if check_module_health $module $port; then
                print_status "$module: Healthy"
                healthy_count=$((healthy_count + 1))
            else
                print_error "$module: Unhealthy"
            fi
        fi
    done
    
    echo ""
    print_health "Health Check Summary: $healthy_count/$total_count modules healthy"
    
    if [ $healthy_count -eq $total_count ] && [ $total_count -gt 0 ]; then
        print_status "All modules are healthy!"
        return 0
    else
        print_warning "Some modules are unhealthy"
        return 1
    fi
}

# Main function to start development environment
start_development() {
    echo -e "${BLUE}🚀 Starting Unified AI Assistant Platform Development Environment${NC}"
    echo -e "${BLUE}================================================================${NC}"
    
    # Create logs directory
    mkdir -p logs
    
    # Start package watch processes
    print_info "Starting shared packages in watch mode..."
    for package in "${PACKAGES[@]}"; do
        if [ -d "packages/$package" ]; then
            start_package_watch $package
        else
            print_warning "Package $package not found, skipping..."
        fi
    done
    
    # Wait a bit for packages to start
    sleep 3
    
    # Start modules
    print_info "Starting modules..."
    start_module "agent-inbox" "3000"
    start_module "foto-fun" "3001"
    start_module "vibe-kanban" "3002"
    start_module "open-canvas" "3003"
    start_module "vcode" "3004"
    start_module "gen-ui-computer-use" "3005"
    start_module "social-media-agent" "3006"
    
    # Show initial status
    show_module_status
    
    # Run health checks
    sleep 5
    run_health_checks
    
    echo ""
    print_status "Development environment is running!"
    print_info "Press Ctrl+C to stop all services"
    print_info "Use '$0 status' in another terminal to check status"
    print_info "Use '$0 logs [module]' to view logs"
    
    # Keep script running
    while true; do
        sleep 10
        # Optional: periodic health checks
        # run_health_checks > /dev/null 2>&1
    done
}

# Main execution
case "${1:-start}" in
    "start")
        start_development
        ;;
    "stop")
        cleanup
        ;;
    "status")
        show_module_status
        ;;
    "logs")
        show_logs "$2"
        ;;
    "restart")
        if [ -z "$2" ]; then
            print_error "Please specify a module to restart"
            exit 1
        fi
        restart_module "$2"
        ;;
    "health")
        run_health_checks
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
