# Continue Existing Session

## Description
Resume a previously started session that was interrupted (crash, network issue, etc.) without losing context or progress.

## Usage
```
/session-continue [session-name]
```

## Execution

### 1. Find Most Recent Session
If no session-name provided, find the most recent session:
```bash
echo "Looking for sessions to continue..."

# Check if there's an active session first
if [ -f ".claude/sessions/.current-session" ] && [ -s ".claude/sessions/.current-session" ]; then
    CURRENT_SESSION=$(cat .claude/sessions/.current-session)
    echo "Found active session: $CURRENT_SESSION"
    echo "Do you want to continue this session? (y/n)"
    # Wait for user confirmation
else
    echo "No active session found. Looking for recent sessions..."
    
    # Find most recent session file
    RECENT_SESSION=$(ls -t .claude/sessions/*.md 2>/dev/null | head -1)
    
    if [ -n "$RECENT_SESSION" ]; then
        SESSION_NAME=$(basename "$RECENT_SESSION" .md)
        echo "Most recent session found: $SESSION_NAME"
        echo "Session file: $RECENT_SESSION"
        
        # Show session info
        echo ""
        echo "Session Overview:"
        head -20 "$RECENT_SESSION" | sed 's/^/  /'
        echo ""
        echo "Continue this session? (y/n)"
    else
        echo "No previous sessions found in .claude/sessions/"
        echo "Use /session-start to create a new session"
        exit 1
    fi
fi
```

### 2. Resume Session Context
```bash
# Set as current session
echo "$SESSION_NAME" > .claude/sessions/.current-session

echo "Resuming session: $SESSION_NAME"
echo ""

# Load session context
SESSION_FILE=".claude/sessions/${SESSION_NAME}.md"

# Extract goals from session file
echo "📋 SESSION GOALS:"
sed -n '/## Goals/,/##/p' "$SESSION_FILE" | grep -v "^##" | sed 's/^/  /'
echo ""

# Show current progress
echo "📈 CURRENT PROGRESS:"
sed -n '/## Progress/,/##/p' "$SESSION_FILE" | grep -v "^##" | sed 's/^/  /'
echo ""

# Check git status since session start
echo "🔄 GIT STATUS SINCE SESSION START:"
git status --porcelain | head -10
if [ $(git status --porcelain | wc -l) -gt 10 ]; then
    echo "  ... and $(( $(git status --porcelain | wc -l) - 10 )) more files"
fi
echo ""

# Check for todos
if command -v todo >/dev/null 2>&1; then
    echo "✅ CURRENT TODOS:"
    todo list 2>/dev/null | head -5 || echo "  No todos found"
    echo ""
fi
```

### 3. Restore PIB Context
```bash
echo "🤖 RESTORING PIB CONTEXT..."

# Load PIB orchestrator config
if [ -f "pib-agent/ide-pib-orchestrator.md" ]; then
    echo "  ✓ PIB orchestrator configuration loaded"
else
    echo "  ⚠ PIB orchestrator config not found"
fi

# Check knowledge base status
if [ -d ".ai" ]; then
    KNOWLEDGE_FILES=$(find .ai -name "*.md" 2>/dev/null | wc -l)
    echo "  ✓ Knowledge base found ($KNOWLEDGE_FILES files)"
else
    echo "  ⚠ No knowledge base found - run /update-knowledge"
fi

# Check for workflow state
if [ -f ".claude/current-workflow-state.json" ]; then
    CURRENT_STAGE=$(jq -r '.currentStage // "unknown"' .claude/current-workflow-state.json 2>/dev/null)
    echo "  ✓ Workflow state: $CURRENT_STAGE"
else
    echo "  ⚠ No workflow state found"
fi
```

### 4. Session Continuation Summary
```bash
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "                    🔄 SESSION CONTINUATION READY"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "📝 SESSION: $SESSION_NAME"
echo "⏰ RESUMED: $(date +'%Y-%m-%d %H:%M:%S')"
echo ""
echo "🎯 NEXT STEPS:"
echo "   • Review session goals and current progress above"
echo "   • Use /session-update to add new progress"
echo "   • Continue with PIB workflows (*agents, /project-init, etc.)"
echo "   • Use /session-end when ready to finish"
echo ""
echo "🤖 PIB ORCHESTRATOR READY:"
echo "   • Use *agents to see available personas"
echo "   • Use *help for PIB commands"
echo "   • All auto-knowledge updates active"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Update session with continuation info
echo "" >> "$SESSION_FILE"
echo "### Session Continued - $(date +'%Y-%m-%d %H:%M:%S')" >> "$SESSION_FILE"
echo "Session resumed after interruption" >> "$SESSION_FILE"
echo "" >> "$SESSION_FILE"
```

### 5. Load PIB Orchestrator Context
After session context is restored, load the PIB orchestrator configuration:

```bash
# MUST read PIB orchestrator config (matching session-start behavior)
echo "🔧 Loading PIB Orchestrator configuration..."

if [ -f "pib-agent/ide-pib-orchestrator.md" ]; then
    echo "PIB Orchestrator ready. Configuration loaded."
    echo ""
    echo "Available commands:"
    echo "  *agents - List all available PIB personas"
    echo "  *help - Get PIB command help"
    echo "  /workflow-help - Quick workflow guidance"
    echo ""
    echo "Session continuation complete. Ready to continue your work!"
else
    echo "⚠ Warning: PIB configuration not found. Operating in limited mode."
fi
```

## Smart Session Detection

The command intelligently handles different scenarios:

1. **Active Session Exists**: Confirms continuation of current session
2. **Recent Session Available**: Shows most recent session and asks to continue
3. **Multiple Sessions**: Lists recent sessions for user selection
4. **No Sessions**: Guides user to start a new session

## Recovery Features

- **Context Restoration**: Loads goals, progress, and workflow state
- **Git Status Check**: Shows changes since session start
- **PIB Configuration**: Reloads orchestrator and personas
- **Knowledge Base**: Verifies and restores knowledge context
- **Auto-Documentation**: Records continuation in session file

## Integration with PIB System

- **Workflow State**: Preserves workflow transitions and stage information
- **Knowledge Updates**: All auto-update triggers remain active
- **Agent Context**: Full access to all 16 PIB personas
- **Quality Gates**: All analysis and review capabilities restored

## Example Usage

```bash
# Simple continuation (uses most recent)
/session-continue

# Continue specific session
/session-continue 2024-01-15-1430-auth-feature

# After continuation, resume normal PIB work
*dev "continue implementing login validation"
/codereview
```

This ensures you never lose progress and can seamlessly pick up where you left off, even after unexpected interruptions!