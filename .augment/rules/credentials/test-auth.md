# Test Authentication

Validate stored credentials against the target authentication system.

## Usage
```bash
/project:test-auth {site-name}
```

## Arguments
- `site-name`: Name identifier of the credentials to test

## Implementation
Test stored credentials to verify they are still valid and working:

1. **Credential Retrieval**: Load stored credentials from secure storage
2. **Authentication Test**: Attempt login using stored credentials
3. **Validation**: Verify successful authentication and access levels
4. **Status Update**: Update credential status based on test results
5. **Report**: Provide detailed authentication test results

Authentication validation includes:
- Login form submission with stored credentials
- Session establishment verification
- Access token validation (for API credentials)
- Permission level verification
- Multi-factor authentication handling
- Session persistence testing

Test scenarios:
- Standard username/password authentication
- API key validation against endpoints
- OAuth token refresh and validation
- SSO and enterprise authentication
- Two-factor authentication flows
- Session timeout and renewal

Validation checks:
- Successful login to dashboard/home page
- Access to protected resources
- API endpoint accessibility (for API credentials)
- User role and permission verification
- Session token validity and expiration
- Multi-factor authentication requirements

Test results reported:
```
Testing credentials for: staging-app
User: test***@company.com
Test: Login form authentication

✓ Credentials valid
✓ Login successful
✓ Dashboard accessible
✓ User permissions verified
✓ Session established

Status: All tests passed
Last tested: 2024-01-20 14:30:00
Next test recommended: 2024-01-27
```

Failed authentication handling:
- Password expiration detection
- Account lockout identification
- Two-factor requirement changes
- Permission revocation detection
- API key expiration alerts

The command helps maintain credential reliability for automated testing workflows.

Arguments: $ARGUMENTS