# List Credentials

Display available stored credentials for testing.

## Usage
```bash
/project:list-credentials
```

## Implementation
Display a secure list of all stored testing credentials:

1. **Credential Enumeration**: List all stored credential identifiers
2. **Metadata Display**: Show non-sensitive information about each credential
3. **Status Validation**: Check credential validity and expiration
4. **Usage Statistics**: Display last used dates and frequency
5. **Security Status**: Show encryption and storage status

Information displayed for each credential:
- Site/service name identifier
- Username or email (partially masked for security)
- Credential type (password, API key, OAuth, etc.)
- Creation date and last modified
- Last used date and usage count
- Expiration date (if applicable)
- Validation status (valid, expired, needs refresh)

Security features:
- Passwords and sensitive data are never displayed
- Only credential metadata is shown
- Masked usernames (e.g., "user***@example.com")
- No credential values are exposed
- Access requires authentication

Output format:
```
Stored Testing Credentials:
========================

1. Site: github-enterprise
   User: dev-***@company.com
   Type: Username/Password
   Created: 2024-01-15
   Last Used: 2024-01-20
   Status: ✓ Valid

2. Site: staging-app
   User: test***
   Type: API Key
   Created: 2024-01-10
   Last Used: Never
   Status: ⚠ Expires in 30 days

3. Site: production-api
   User: service***@company.com
   Type: OAuth Token
   Created: 2024-01-01
   Last Used: 2024-01-20
   Status: ✓ Valid
```

The command helps manage and audit stored credentials for testing workflows.

Arguments: $ARGUMENTS