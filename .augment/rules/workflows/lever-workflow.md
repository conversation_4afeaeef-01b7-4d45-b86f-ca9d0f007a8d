# LEVER Workflow - Architecture-First Development

Execute development workflows with mandatory LEVER architecture validation at every phase.

## Usage
```bash
/project:lever-workflow "implement user authentication" --target-score=5
```

## Arguments
- Main argument: Development task description
- `--target-score`: Required LEVER score (3-5, default: 4)
- `--phase`: Run specific phase only (analysis, design, implement, review)
- `--strict`: Fail workflow if LEVER target not met (default: true)

## Implementation
Comprehensive development workflow with LEVER principles enforced at every stage:

### Phase 1: LEVER Analysis & Planning
```
Task → LEVER Discovery → Reuse Assessment → Extension Analysis → Complexity Check → Planning
```

1. **LEVER Discovery Phase**:
   - **L**everage: Scan codebase for existing similar functionality
   - **E**xtend: Identify components that could be extended
   - **V**erify: Find reactive patterns for validation
   - **E**liminate: Detect potential duplications before creation
   - **R**educe: Assess simplification opportunities

2. **Reuse Assessment**:
   - Generate reuse score (0-5) for each LEVER principle
   - Document found patterns and extension points
   - Identify libraries and frameworks to leverage
   - Map existing reactive validation patterns
   - Calculate complexity reduction potential

3. **LEVER Planning**:
   - Create LEVER-compliant implementation plan
   - Set specific reuse targets and metrics
   - Define extension strategies over new creation
   - Plan reactive validation integration
   - Document complexity reduction approach

### Phase 2: LEVER-Guided Design
```
Requirements → Architecture Assessment → LEVER Design → Pattern Integration → Validation Strategy
```

1. **Architecture Assessment**:
   - Evaluate existing architecture for extension points
   - Identify reusable architectural patterns
   - Assess current reactive mechanisms
   - Find opportunities for simplification

2. **LEVER Design Process**:
   - Design extension-first approach
   - Integrate existing reactive patterns
   - Minimize new code creation
   - Eliminate architectural duplication
   - Optimize for simplicity and maintainability

3. **Pattern Integration Planning**:
   - Map integration with existing components
   - Plan reactive validation workflows
   - Design clean extension interfaces
   - Minimize system complexity

### Phase 3: LEVER Implementation
```
Design → Extension Implementation → Reuse Integration → Reactive Validation → Complexity Check
```

1. **Extension-First Implementation**:
   - Implement by extending existing components
   - Leverage existing libraries and utilities
   - Integrate with reactive validation patterns
   - Maintain architectural simplicity

2. **LEVER Compliance Monitoring**:
   - Real-time LEVER score tracking during implementation
   - Continuous assessment of reuse opportunities
   - Extension validation against design
   - Complexity metrics monitoring

3. **Implementation Validation**:
   - Verify extension integrity
   - Test reactive validation patterns
   - Validate duplication elimination
   - Confirm complexity reduction

### Phase 4: LEVER Quality Assurance
```
Implementation → LEVER Audit → Compliance Check → Score Validation → Optimization → Approval
```

1. **Comprehensive LEVER Audit**:
   - **L**everage Score: Evaluate reuse effectiveness (0-5)
   - **E**xtend Score: Assess extension vs new creation (0-5)
   - **V**erify Score: Validate reactive pattern usage (0-5)
   - **E**liminate Score: Confirm duplication elimination (0-5)
   - **R**educe Score: Measure complexity reduction (0-5)

2. **Compliance Validation**:
   - Calculate overall LEVER score
   - Compare against target score
   - Generate improvement recommendations
   - Identify optimization opportunities

3. **Quality Gates**:
   - **Mandatory**: LEVER score ≥ target score
   - **Code Review**: LEVER-focused review process
   - **Architecture Review**: Extension and integration validation
   - **Performance Review**: Complexity and efficiency validation

### LEVER Score Calculation
```
Leverage Score (0-5):
- 5: Extensive reuse of existing patterns and libraries
- 4: Good reuse with minimal new code
- 3: Moderate reuse with some new implementation
- 2: Limited reuse, mostly new code
- 1: Minimal reuse, significant new development
- 0: No reuse, everything built from scratch

Extend Score (0-5):
- 5: All functionality through extension of existing components
- 4: Primary functionality through extension
- 3: Mixed extension and new implementation
- 2: Limited extension, mostly new components
- 1: Minimal extension usage
- 0: No extension, all new components

Verify Score (0-5):
- 5: Comprehensive reactive validation throughout
- 4: Good reactive pattern usage
- 3: Moderate reactive validation
- 2: Basic reactive patterns
- 1: Minimal reactive validation
- 0: No reactive patterns used

Eliminate Score (0-5):
- 5: Zero duplication, perfect code reuse
- 4: Minimal duplication, good consolidation
- 3: Some duplication eliminated
- 2: Limited duplication reduction
- 1: Minimal effort to eliminate duplication
- 0: No duplication elimination

Reduce Score (0-5):
- 5: Maximum simplicity, minimal complexity
- 4: Good complexity reduction
- 3: Moderate simplification
- 2: Limited complexity reduction
- 1: Minimal simplification effort
- 0: No complexity reduction
```

### Real-Time LEVER Tracking
```
LEVER Workflow Progress: User Authentication
===========================================

Phase 1: LEVER Analysis & Planning ✓ Complete
- Leverage Score: 4/5 (Found existing auth patterns)
- Extend Score: 5/5 (Can extend BaseAuth component)  
- Verify Score: 4/5 (Existing reactive validation available)
- Eliminate Score: 5/5 (No duplication detected)
- Reduce Score: 4/5 (Simple extension approach)
Current LEVER Score: 4.4/5 ✓ Target: 4.0

Phase 2: LEVER-Guided Design ⏳ In Progress
- Architecture assessment complete
- Extension design validated
- Pattern integration planned
- Reactive validation mapped

Next: Begin LEVER Implementation Phase
```

### Success Criteria
- **LEVER Score**: Must meet or exceed target score
- **Code Quality**: Standard PIB quality requirements
- **Architecture**: Clean extension without architectural debt
- **Documentation**: LEVER compliance documentation
- **Testing**: Comprehensive testing of extensions

### Failure Handling
- **Score Below Target**: Automatic refactoring recommendations
- **Extension Failures**: Fallback to guided new implementation
- **Complexity Issues**: Mandatory simplification phase
- **Duplication Detected**: Automatic consolidation requirements

The workflow ensures every development task follows LEVER principles religiously.

Arguments: $ARGUMENTS