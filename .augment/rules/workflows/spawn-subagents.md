---
description: Spawn multiple sub-agents for parallel execution with focused domain contexts
---

# Spawn Sub-Agents - Automated Parallel Agent Creation

**Usage**: `/spawn-subagents $ARGUMENTS`

Create parallel sub-agents from orchestration plan: $ARGUMENTS

*chat model=gemini-2.5-pro prompt="I need to spawn sub-agents for parallel execution based on orchestration plan: $ARGUMENTS. Please analyze the plan and create focused context packages for frontend, backend, devops, and testing sub-agents. Each sub-agent should receive domain-specific context filtered from the planning documentation and coordination requirements. Use the context engineering system to create isolated context packages for each sub-agent type."

## What This Command Does

### 1. Orchestration Plan Analysis
- **Parse Orchestration Plan**: Extract parallel execution groups from orchestration
- **Sub-Agent Identification**: Identify optimal sub-agent assignments
- **Context Isolation**: Create isolated context packages for each sub-agent
- **Dependency Mapping**: Map dependencies and coordination points

### 2. Automated Sub-Agent Creation
- **Frontend Sub-Agent**: Spawn with UI/UX context and tasks
- **Backend Sub-Agent**: Spawn with API/data context and tasks  
- **DevOps Sub-Agent**: Spawn with infrastructure context and tasks
- **Testing Sub-Agent**: Spawn with QA context and tasks

### 3. Context Engineering per Sub-Agent
- **Focused Context Packages**: Each sub-agent gets domain-specific context
- **Planning Context Integration**: Relevant planning decisions per domain
- **Coordination Context**: Dependencies and integration points
- **Quality Standards**: Domain-specific quality gates and standards

### 4. Coordination Infrastructure
- **Communication Channels**: Inter-sub-agent communication setup
- **Progress Tracking**: Individual and collective progress monitoring
- **Dependency Management**: Automatic dependency resolution and blocking
- **Integration Synchronization**: Coordination of integration points

## Implementation Process

### Phase 1: Orchestration Analysis
```
Orchestration Plan → Task Categorization → Sub-Agent Mapping → Dependency Analysis → Context Preparation
```

1. **Parse Orchestration Plan**: Extract parallel execution groups and dependencies
2. **Task Categorization**: Categorize tasks by domain (frontend, backend, devops, testing)
3. **Sub-Agent Mapping**: Map tasks to optimal sub-agents based on expertise
4. **Dependency Analysis**: Identify inter-task dependencies and sequencing
5. **Context Preparation**: Prepare domain-specific context packages

### Phase 2: Sub-Agent Spawning
```
Context Packages → Sub-Agent Creation → Context Injection → Task Assignment → Coordination Setup
```

1. **Sub-Agent Creation**: Spawn sub-agents with domain-specific personas
2. **Context Injection**: Inject focused context packages per sub-agent
3. **Task Assignment**: Assign specific tasks with clear acceptance criteria
4. **Coordination Setup**: Establish communication and dependency tracking
5. **Quality Gate Setup**: Configure domain-specific quality validation

### Phase 3: Parallel Execution Management
```
Sub-Agent Monitoring → Progress Tracking → Dependency Coordination → Integration Management → Quality Validation
```

1. **Real-time Monitoring**: Track sub-agent progress and status
2. **Dependency Coordination**: Manage inter-task dependencies automatically
3. **Communication Facilitation**: Enable efficient sub-agent communication
4. **Integration Management**: Coordinate integration points and handoffs
5. **Quality Gate Enforcement**: Ensure quality standards per domain

### Phase 4: Synchronization and Integration
```
Task Completion → Integration Testing → Quality Validation → Final Coordination → Epic Completion
```

1. **Completion Validation**: Validate individual task completion
2. **Integration Testing**: Test integration between sub-agent deliverables
3. **Quality Validation**: Ensure overall epic quality standards
4. **Final Coordination**: Complete epic integration and delivery
5. **Retrospective**: Capture lessons learned for future orchestration

## Sub-Agent Context Engineering

### Frontend Sub-Agent Context Package
```markdown
# Frontend Sub-Agent Context Package

## Assigned Tasks
- {UI_COMPONENT_TASKS}
- {USER_INTERACTION_TASKS}
- {FRONTEND_INTEGRATION_TASKS}

## Planning Context
- Epic Vision: {EPIC_VISION_FRONTEND_RELEVANT}
- UI/UX Decisions: {UI_ARCHITECTURAL_DECISIONS}
- Design System Requirements: {DESIGN_SYSTEM_CONTEXT}

## Technical Context
- Frontend Architecture: {FRONTEND_ARCHITECTURE_CONTEXT}
- Component Library: {COMPONENT_LIBRARY_CONTEXT}
- State Management: {STATE_MANAGEMENT_CONTEXT}

## Coordination Context  
- Backend Dependencies: {BACKEND_API_DEPENDENCIES}
- Integration Points: {FRONTEND_INTEGRATION_POINTS}
- Testing Coordination: {FRONTEND_TESTING_COORDINATION}

## Quality Standards
- Performance Requirements: {FRONTEND_PERFORMANCE_STANDARDS}
- Accessibility Standards: {ACCESSIBILITY_REQUIREMENTS}
- Browser Compatibility: {BROWSER_COMPATIBILITY_REQUIREMENTS}
```

### Backend Sub-Agent Context Package
```markdown
# Backend Sub-Agent Context Package

## Assigned Tasks
- {API_ENDPOINT_TASKS}
- {DATABASE_TASKS}
- {BUSINESS_LOGIC_TASKS}

## Planning Context
- Epic Vision: {EPIC_VISION_BACKEND_RELEVANT}
- Architecture Decisions: {BACKEND_ARCHITECTURAL_DECISIONS}
- Integration Requirements: {BACKEND_INTEGRATION_CONTEXT}

## Technical Context
- API Architecture: {API_ARCHITECTURE_CONTEXT}
- Database Schema: {DATABASE_SCHEMA_CONTEXT}
- Business Logic Patterns: {BUSINESS_LOGIC_CONTEXT}

## Coordination Context
- Frontend Integration: {FRONTEND_API_REQUIREMENTS}
- DevOps Dependencies: {INFRASTRUCTURE_DEPENDENCIES}
- Testing Requirements: {BACKEND_TESTING_COORDINATION}

## Quality Standards
- API Performance: {API_PERFORMANCE_STANDARDS}
- Security Requirements: {BACKEND_SECURITY_STANDARDS}
- Data Integrity: {DATA_INTEGRITY_REQUIREMENTS}
```

### DevOps Sub-Agent Context Package
```markdown
# DevOps Sub-Agent Context Package

## Assigned Tasks
- {INFRASTRUCTURE_TASKS}
- {DEPLOYMENT_TASKS}
- {MONITORING_TASKS}

## Planning Context
- Epic Vision: {EPIC_VISION_INFRASTRUCTURE_RELEVANT}
- Infrastructure Decisions: {INFRASTRUCTURE_ARCHITECTURAL_DECISIONS}
- Scalability Requirements: {SCALABILITY_CONTEXT}

## Technical Context
- Infrastructure Architecture: {INFRASTRUCTURE_ARCHITECTURE_CONTEXT}
- Deployment Pipeline: {DEPLOYMENT_PIPELINE_CONTEXT}
- Monitoring Strategy: {MONITORING_STRATEGY_CONTEXT}

## Coordination Context
- Application Dependencies: {APPLICATION_INFRASTRUCTURE_DEPENDENCIES}
- Security Coordination: {SECURITY_COORDINATION_REQUIREMENTS}
- Testing Infrastructure: {TESTING_INFRASTRUCTURE_COORDINATION}

## Quality Standards
- Performance Requirements: {INFRASTRUCTURE_PERFORMANCE_STANDARDS}
- Security Standards: {INFRASTRUCTURE_SECURITY_STANDARDS}
- Reliability Requirements: {RELIABILITY_REQUIREMENTS}
```

### Testing Sub-Agent Context Package
```markdown
# Testing Sub-Agent Context Package

## Assigned Tasks
- {TEST_STRATEGY_TASKS}
- {AUTOMATION_TASKS}
- {QUALITY_VALIDATION_TASKS}

## Planning Context
- Epic Vision: {EPIC_VISION_TESTING_RELEVANT}
- Quality Requirements: {QUALITY_ARCHITECTURAL_DECISIONS}
- Testing Strategy: {TESTING_STRATEGY_CONTEXT}

## Technical Context
- Testing Architecture: {TESTING_ARCHITECTURE_CONTEXT}
- Automation Framework: {AUTOMATION_FRAMEWORK_CONTEXT}
- Quality Metrics: {QUALITY_METRICS_CONTEXT}

## Coordination Context
- Feature Testing: {FEATURE_TESTING_COORDINATION}
- Integration Testing: {INTEGRATION_TESTING_COORDINATION}
- Performance Testing: {PERFORMANCE_TESTING_COORDINATION}

## Quality Standards
- Test Coverage: {TEST_COVERAGE_REQUIREMENTS}
- Quality Gates: {QUALITY_GATE_STANDARDS}
- Performance Validation: {PERFORMANCE_VALIDATION_STANDARDS}
```

## Coordination Mechanisms

### Inter-Sub-Agent Communication
```bash
# Sub-agent status updates
/subagent-status <subagent-id> --status=<status> --progress=<percentage>

# Dependency completion notifications  
/subagent-notify <dependency-id> --completed --notify=<dependent-subagents>

# Integration readiness
/subagent-integration <subagent-id> --ready --integration-point=<point>
```

### Dependency Management
- **Automatic Blocking**: Sub-agents automatically block on unmet dependencies
- **Notification System**: Automatic notifications when dependencies are ready
- **Progress Tracking**: Real-time progress tracking across all sub-agents
- **Bottleneck Detection**: Automatic detection and escalation of bottlenecks

### Quality Gate Coordination
- **Domain-Specific Reviews**: Each sub-agent has domain-expert reviewers
- **Integration Testing**: Coordinated integration testing across sub-agents
- **Epic-Level Validation**: Final epic-level quality validation
- **Continuous Quality**: Quality gates throughout parallel execution

## Advanced Features

### Dynamic Load Balancing
- **Task Redistribution**: Automatically redistribute tasks based on sub-agent capacity
- **Bottleneck Resolution**: Identify and resolve bottlenecks through task reallocation
- **Capacity Optimization**: Optimize sub-agent utilization throughout execution

### Real-time Coordination
- **Live Progress Tracking**: Real-time visualization of sub-agent progress
- **Dynamic Dependency Updates**: Real-time dependency status updates
- **Communication Facilitation**: Efficient inter-sub-agent communication
- **Integration Synchronization**: Real-time coordination of integration points

### Context Evolution
- **Learning Integration**: Capture and apply lessons learned across sub-agents
- **Context Refinement**: Continuously refine context packages based on execution
- **Pattern Recognition**: Identify successful coordination patterns
- **Process Optimization**: Optimize coordination processes based on outcomes

## Examples

### Spawn Sub-Agents from Orchestration Plan
```bash
/spawn-subagents .claude/plans/active/user-dashboard-orchestration-plan.md --max-agents=3
```
- Analyzes orchestration plan for parallel opportunities
- Creates 3 sub-agents: Frontend, Backend, Testing
- Injects domain-specific context packages
- Establishes coordination and dependency tracking

### Auto-Assign with Maximum Parallelization
```bash
/spawn-subagents planning-session-result --auto-assign --max-agents=4
```
- Automatically assigns all available sub-agent types
- Maximizes parallel execution with 4 concurrent sub-agents
- Automatically manages dependencies and coordination
- Provides real-time progress tracking and coordination

### Focused Sub-Agent Spawning
```bash
/spawn-subagents orchestration-plan.md --focus=frontend,backend
```
- Spawns only Frontend and Backend sub-agents
- Optimized for scenarios where DevOps/Testing are not needed
- Simplified coordination for focused development

## Integration with Existing Workflows

### Context Engineering Integration
- **Enhanced Context Packages**: Sub-agents receive focused, domain-specific context
- **Planning Integration**: Planning context automatically included per domain
- **Quality Standards**: Domain-specific quality requirements included
- **Coordination Context**: Inter-sub-agent coordination requirements included

### Orchestration Hook Integration
- **Automatic Triggering**: Sub-agent spawning triggered by orchestration plans
- **Progress Integration**: Sub-agent progress integrated with existing hooks
- **Quality Gate Integration**: Sub-agent quality gates integrated with existing validation
- **Context Evolution**: Sub-agent learnings integrated with context evolution

### Planning System Integration
- **Orchestration Plan Source**: Uses existing orchestration plans as input
- **Context Bridge Integration**: Leverages context bridges for cross-session continuity
- **Planning Index Updates**: Updates planning index with sub-agent execution status
- **Quality Metrics**: Tracks sub-agent quality metrics in planning system

## Quality Assurance

### Sub-Agent Quality Gates
- **Domain Expertise**: Each sub-agent has domain-expert reviewers
- **Focused Reviews**: Reviews focused on domain-specific quality criteria
- **Integration Validation**: Validation of integration points between sub-agents
- **Epic-Level Quality**: Final epic-level quality validation

### Coordination Quality
- **Communication Efficiency**: Efficient inter-sub-agent communication
- **Dependency Management**: Effective dependency tracking and resolution
- **Progress Transparency**: Clear visibility into sub-agent progress
- **Integration Success**: Successful integration of sub-agent deliverables

## Related Commands
- `/orchestration-plan` - Create orchestration plan for sub-agent spawning
- `/subagent-status` - Check sub-agent status and progress
- `/subagent-coordination` - Manage sub-agent coordination and dependencies
- `/integration-test` - Test integration between sub-agent deliverables

---
*Part of the PIB Method's automated parallel execution system*