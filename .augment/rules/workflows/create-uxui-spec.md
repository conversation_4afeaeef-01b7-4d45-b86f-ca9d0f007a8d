---
description: Create detailed UX/UI specifications with Design Architect expertise
---

# PIB Create UX/UI Specification Command

**Usage**: `*create-uxui-spec [project-name] [options]`

## Purpose

Creates comprehensive UX/UI specifications using Design Architect expertise, following PIB principles of leveraging existing design patterns while extending capabilities for user experience excellence.

### Key Benefits
- Leverages existing design systems and UI patterns in the project
- Extends current frontend architecture with detailed interaction specifications
- Reduces design-development disconnect through comprehensive specifications
- Integrates seamlessly with PIB architectural workflows

### When to Use
- After frontend architecture has been established
- When detailed UI/UX specifications are needed before development
- As part of complete project initialization workflow
- When enhancing or redesigning existing user interfaces

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing design systems, UI patterns, brand guidelines
- **E** - Extend: Current frontend architecture with detailed interaction specs
- **V** - Verify: Design decisions against user research and best practices
- **E** - Eliminate: Inconsistent design patterns and redundant UI elements
- **R** - Reduce: Complexity in user flows while maximizing usability

#### 2. Context Gathering
```bash
# Load project and design context
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "design-architect"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
FRONTEND_ARCH=$(cat docs/frontend-architecture.md 2>/dev/null || echo "No frontend architecture found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If Design Architect Active
- Focus on comprehensive UX/UI design specifications
- Detailed interaction design and user flow documentation
- Component library and design system specifications

##### If PM Active  
- Emphasize business requirements alignment in UX specifications
- User story integration with UI specifications
- Success metrics and conversion optimization focus

#### 4. Core Processing
- Load Design Architect persona and UX/UI specification task
- Analyze existing frontend architecture and design constraints
- Create comprehensive UX/UI specifications using established template
- Document interaction patterns, component specifications, and user flows
- Generate design system guidelines and accessibility requirements

#### 5. Output Generation
- Structured UX/UI specification document saved to `docs/uxui-spec.md`
- Component specifications with design tokens and guidelines
- User flow diagrams and interaction specifications
- Accessibility compliance documentation

#### 6. Workflow Integration
- Updates workflow state to indicate UX/UI specs completion
- Prepares context for development phase handoff
- Integrates with existing frontend architecture documentation

## Quality Features / Validation Checklist

### PIB Compliance Validation
- [ ] **LEVER Principles**: Leverages existing design patterns, extends frontend architecture
- [ ] **Agent Alignment**: Uses Design Architect expertise and specialization
- [ ] **Workflow Integration**: Properly follows frontend design workflow sequence
- [ ] **Knowledge Capture**: Documents design decisions and rationale
- [ ] **Context Awareness**: Builds upon existing project and architecture context

### Technical Quality Gates
- [ ] **Input Validation**: Handles missing architecture or incomplete project context
- [ ] **Error Handling**: Provides guidance when design assets are missing
- [ ] **Performance**: Efficient specification generation process
- [ ] **Consistency**: Maintains consistent design language throughout
- [ ] **Documentation**: Clear, implementable design specifications

### Output Quality Assurance
- [ ] **Completeness**: Covers all major UI components and user flows
- [ ] **Accuracy**: Design specifications are technically feasible
- [ ] **Relevance**: Specifications align with project goals and user needs
- [ ] **Actionability**: Provides specific implementation guidance for developers
- [ ] **Integration**: Seamlessly connects with frontend architecture

### Knowledge Management Standards
- [ ] **Persistence**: Saves specifications to `docs/uxui-spec.md`
- [ ] **Versioning**: Maintains design decision history and iterations
- [ ] **Cross-Reference**: Links to frontend architecture and user stories
- [ ] **Searchability**: Structured for easy developer and designer reference
- [ ] **Agent Context**: Updates Design Architect and development team knowledge

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for design phase completion
- **Notifications**: `notification-hook.sh` for specification completion alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: UX/UI specifications to `docs/uxui-spec.md`
- **Updates**: Project context with design specifications in `.ai/project-context.md`
- **Creates**: Design asset tracking in `.ai/design/` directory
- **Links**: Cross-references with frontend architecture and user stories

### Agent Context Updates
- **Design Architect**: Updates design knowledge and specification templates
- **All Agents**: Updates shared project design context
- **Development**: Prepares detailed implementation specifications
- **QA**: Provides UI testing criteria and acceptance standards

### Follow-up Command Preparation
Results prepare context for:
- `*create-next-story` - Development stories based on UX/UI specifications
- `*create-ai-frontend-prompt` - AI-powered frontend generation prompts
- `*update-knowledge` - Distribute design specifications to all agents
- `*dev` - Begin implementation with detailed design specifications

## Related Commands

### Core Design Commands
- `*create-frontend-architecture` - Prerequisites for UX/UI specifications
- `*analyze-competitor-ux` - Competitive UX analysis for specification input
- `*create-ai-frontend-prompt` - AI generation based on specifications

### Workflow Commands
- `*project-init` - Complete project initialization workflow
- `*create-next-story` - Development stories from specifications
- `*dev` - Implementation phase with design specifications

### Agent Commands
- `/switch-agent design-architect` - Optimal agent for UX/UI specification creation
- `/load-design-architect` - Direct access to Design Architect capabilities

### Knowledge Commands
- `/update-knowledge` - Distribute design specifications to all agents
- `/memory-extract` - Extract design insights and decisions

## Example Usage

### Basic Usage
```bash
# Create UX/UI specifications for current project
*create-uxui-spec
```

### Advanced Usage
```bash
# Create specifications with specific focus areas
*create-uxui-spec --focus=mobile --accessibility=wcag-aa
```

### Agent-Specific Usage
```bash
# When Design Architect agent is active:
*create-uxui-spec --detailed --components

# When PM agent is active:
*create-uxui-spec --business-focused --metrics
```

### Workflow Integration Example
```bash
# Part of project initialization:
/analyst-brief
/pm-prd  
/architect-design
/create-frontend-architecture
*create-uxui-spec  # ← Detailed UX/UI specifications
/update-knowledge
/create-next-story
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **Design Architect**: Comprehensive design specifications with interaction details
- **PM**: Business-aligned UX requirements with success metrics
- **Developer**: Implementation-focused specifications with technical details
- **QA**: Testing criteria and user acceptance standards
- **Analyst**: User research integration and validation criteria

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Existing design systems, brand guidelines, UI pattern libraries
- **Extend**: Frontend architecture with detailed interaction specifications
- **Verify**: Design decisions against user research and accessibility standards
- **Eliminate**: Inconsistent design patterns and redundant UI components
- **Reduce**: Design-development friction through comprehensive specifications

### Best Practices
- Run after frontend architecture is established for optimal context
- Integrate with existing design systems and component libraries
- Include accessibility requirements and responsive design specifications
- Document design rationale and decision-making process for future reference