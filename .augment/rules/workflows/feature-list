#!/bin/bash

# Feature List Command Handler
# Shows all active feature worktrees with their status

set -euo pipefail

# Find project root dynamically
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

# Get optional format parameter
FORMAT="${1:-table}"

# Call the worktree manager with dynamic path
"$PROJECT_ROOT/.claude/hooks/worktree-manager.sh" list "$FORMAT"