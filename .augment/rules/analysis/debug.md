---
description: Systematic debugging and root cause analysis using PIB principles
---

# PIB Debug Command

**Usage**: `*debug [issue-description] [files/scope]`

## Purpose

Perform systematic debugging and root cause analysis following PIB workflows, LEVER framework validation, and agent-specific investigation approaches.

## LEVER Framework Pre-Investigation

Before debugging, validate these principles:
- **L** - Leverage: What existing debug patterns, logs, or documentation can we use?
- **E** - Extend: Can we extend existing monitoring/logging rather than adding new debug code?
- **V** - Verify: How can we verify our hypothesis through existing test patterns?
- **E** - Eliminate: Are we duplicating existing debugging efforts or tools?
- **R** - Reduce: What's the simplest debugging approach that isolates the issue?

## Agent-Specific Debug Focus

### Current Agent Context
Adapt debugging approach based on active agent persona:

#### If Developer Agent Active
- Focus on implementation bugs and logic errors
- Analyze code flow and data transformations
- Check variable states and function calls
- Investigate integration points and dependencies

#### If Architect Agent Active
- Focus on system-level issues and architectural flaws
- Analyze component interactions and design patterns
- Investigate scalability and performance bottlenecks
- Assess architectural decision implications

#### If QA Agent Active
- Focus on test coverage gaps and edge cases
- Analyze error conditions and boundary scenarios
- Investigate user workflow failures
- Check test environment vs production differences

#### If Analyst Agent Active
- Focus on business logic discrepancies
- Analyze data flow and processing errors
- Investigate requirement interpretation issues
- Check business rule implementation

## Systematic Debug Process

### 1. Issue Characterization
```bash
# Capture current context
echo "## Debug Session: $(date)" >> .ai/debug-log.md
echo "### Issue: $ISSUE_DESCRIPTION" >> .ai/debug-log.md
echo "### Agent Context: $CURRENT_AGENT" >> .ai/debug-log.md
echo "### Scope: $FILES_SCOPE" >> .ai/debug-log.md
```

### 2. LEVER Compliance Investigation
- **Leverage**: Check existing logs, error reports, and debug documentation
- **Extend**: Identify if existing monitoring can be enhanced vs new debug code
- **Verify**: Use existing test patterns to reproduce and verify issues
- **Eliminate**: Avoid duplicating existing debug efforts or tools
- **Reduce**: Find the minimal reproduction case and focused investigation

### 3. Evidence Gathering
- Collect relevant logs and error messages
- Identify reproduction steps and conditions
- Gather related code sections and dependencies
- Document environmental factors and configuration

### 4. Hypothesis Formation
Based on agent perspective and evidence:
- Form initial hypotheses about root cause
- Identify assumptions that need validation
- Plan investigation steps to test each hypothesis
- Document expected vs actual behavior

### 5. Investigation Execution
- Systematic testing of each hypothesis
- Code analysis of suspected problem areas
- Environment and configuration verification
- Integration point and dependency analysis

## Debug Categories

### Code Logic Issues
- Variable state analysis
- Function flow investigation
- Conditional logic verification
- Data transformation checking

### Integration Problems
- API communication failures
- Database connection issues
- Service dependency problems
- Configuration mismatches

### Performance Issues
- Resource usage analysis
- Query performance investigation
- Memory leak detection
- Scalability bottleneck identification

### Environmental Issues
- Configuration differences
- Dependency version conflicts
- Infrastructure problems
- Deployment environment issues

## Output Format

### Debug Summary
- Issue characterization and scope
- Agent perspective and focus area
- LEVER compliance assessment
- Investigation methodology used

### Evidence Analysis
- Collected symptoms and error patterns
- Relevant code sections and dependencies
- Environmental factors and configuration
- Reproduction steps and conditions

### Root Cause Analysis
- Validated hypothesis and evidence
- Contributing factors and conditions
- Impact assessment and scope
- Risk evaluation and urgency

### Resolution Plan
- Specific fix recommendations prioritized by impact
- LEVER-compliant solution approaches
- Testing and validation requirements
- Rollback plans and risk mitigation

### Workflow Integration
- Update workflow state with debug findings
- Document resolution in knowledge base
- **Auto-trigger knowledge update** on debug resolution
- Prepare context for implementation commands
- Schedule follow-up validation

## Integration with PIB System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles reminder
- Updates workflow state through `workflow-transition.sh`
- Logs debug session through `notification-hook.sh`

### Knowledge Management
- Saves debug session to `.ai/debug-[timestamp].md`
- Updates issue tracking in `.ai/issues/`
- Creates resolution documentation
- Updates project troubleshooting guide

### Follow-up Commands
Debug results prepare context for:
- `*analyze` - Deeper code analysis of problem areas
- `*codereview` - Quality assessment of fix implementation
- `*testgen` - Test cases to prevent regression
- `*refactor` - Structural improvements to prevent similar issues

## Example Usage

```bash
# Debug specific error with file context
*debug "User authentication failing on login" src/auth/

# Debug performance issue across system
*debug "Slow response times" . performance

# Debug integration problem
*debug "API returning 500 errors" src/api/ src/services/
```

## Quality Gates

All debugging must meet PIB standards:
- **LEVER Compliance**: Demonstrates leverage, extension, verification, elimination, reduction
- **Agent Alignment**: Respects current agent persona and debugging expertise
- **Systematic Approach**: Follows structured investigation methodology
- **Evidence-Based**: All conclusions supported by concrete evidence
- **Knowledge Capture**: Documents findings for future reference and learning
