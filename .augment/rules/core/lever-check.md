# LEVER Architecture Check

Validate current implementation against LEVER architecture principles.

## Usage
```bash
/project:lever-check --scope={file|module|project} --file={filepath}
```

## Arguments
- `--scope`: Scope of analysis (file, module, or project-wide)
- `--file`: Specific file path to analyze (when scope=file)

## Implementation
Comprehensive LEVER compliance analysis across different scopes:

### LEVER Principles Assessment:
1. **L**everage - Are we reusing existing patterns, libraries, and code?
2. **E**xtend - Are we extending rather than creating from scratch?
3. **V**erify - Are we using reactivity to validate and self-correct?
4. **E**liminate - Have we removed duplication and unnecessary code?
5. **R**educe - Is this the simplest possible solution?

### Analysis Process:
1. **Code Scanning**: Analyze implementation for LEVER compliance
2. **Pattern Detection**: Identify reuse opportunities and duplications
3. **Complexity Assessment**: Evaluate if simpler solutions exist
4. **Reactivity Check**: Verify reactive patterns are utilized
5. **Score Calculation**: Generate LEVER compliance score (0-5)

### File-Level Analysis:
```bash
/project:lever-check --scope=file --file=src/components/UserForm.tsx
```

Analyzes:
- Component reuse opportunities
- Hook utilization vs custom implementation
- Duplicate logic within file
- Reactive pattern usage
- Code complexity metrics

### Module-Level Analysis:
```bash
/project:lever-check --scope=module --file=src/modules/authentication
```

Analyzes:
- Cross-file code duplication
- Shared utility opportunities
- Module interdependencies
- Architecture pattern consistency
- Service layer reuse

### Project-Wide Analysis:
```bash
/project:lever-check --scope=project
```

Analyzes:
- Global code duplication patterns
- Unused dependencies and dead code
- Architecture pattern violations
- Framework utilization efficiency
- Overall complexity score

### LEVER Score Report:
```
LEVER Architecture Analysis Report
=================================
Scope: src/components/UserForm.tsx
Analysis Date: 2024-01-20 14:30:00

LEVER Scores:
============
L - Leverage:    4/5  ✓ Good use of existing UI library
E - Extend:      3/5  ⚠ Could extend BaseForm component
V - Verify:      5/5  ✓ Excellent reactive validation
E - Eliminate:   2/5  ⚠ Duplicate validation logic found
R - Reduce:      4/5  ✓ Reasonably simple implementation

Overall LEVER Score: 3.6/5 (Good)

Recommendations:
===============
1. [Extend] Refactor to extend BaseForm component instead of duplicating form logic
2. [Eliminate] Extract validation logic to shared validators module
3. [Leverage] Consider using form library for complex validation scenarios

Code Examples:
=============
❌ Current Implementation:
  const validateEmail = (email) => {
    // Custom validation logic (duplicated elsewhere)
  }

✅ LEVER-Compliant Approach:
  import { validateEmail } from '@/utils/validators';
  // Reuse existing validation logic

Duplication Detected:
====================
- Email validation: Also found in LoginForm.tsx, RegisterForm.tsx
- Form submission handling: Similar logic in 4 other components
- Error display: Pattern repeated across 8 components

Architecture Improvements:
=========================
1. Create shared FormValidation hook
2. Extract common form patterns to BaseForm
3. Implement reactive validation with existing form library
4. Consolidate error handling patterns
```

### Integration Points:
- Automatically triggered during code reviews
- Integrated with git hooks for pre-commit checks
- Available as standalone analysis command
- Generates improvement suggestions
- Tracks LEVER compliance trends over time

The command ensures consistent application of LEVER principles throughout development.

Arguments: $ARGUMENTS