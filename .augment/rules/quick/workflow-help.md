# Quick Workflow Help

## Description
Provides instant workflow guidance based on what you want to accomplish.

## Usage
```
/workflow-help
```

## Execution
Display workflow decision tree and commands for common scenarios:

```
🎯 PIB-METHOD Quick Workflow Guide

What do you want to do?

🆕 NEW PROJECT
Command: /project-init
What: Complete guided workflow from conception to deployment

🔧 ADD FEATURE
Command: /module-dev
What: Add new functionality to existing project

🚨 FIX SOMETHING BROKEN
Command: /legacy-fix
What: Systematic approach to fixing issues

🔍 UNDERSTAND CODEBASE
Command: /analyze
What: Comprehensive code analysis

🐛 DEBUG A PROBLEM
Command: /debug
What: Systematic debugging workflow

👥 PARALLEL DEVELOPMENT
Command: /feature-start <name>
What: Multiple people working on different features

🔒 SECURITY REVIEW
Command: /secaudit
What: Comprehensive security assessment

📝 CODE REVIEW
Command: /codereview
What: Quality and compliance review

🧪 GENERATE TESTS
Command: /testgen
What: Create comprehensive test suites

📋 FULL COMMAND LIST
Command: /help
What: See all available commands

🤖 SWITCH PERSONAS
Command: /agents (list) or /switch-agent <name>
What: Change to specialized agent for specific tasks

💡 LEVER CHECK
Command: /lever-check
What: Validate you're following LEVER principles

❓ WHEN STUCK
1. /help - Find the right command
2. /agents - See available specialists
3. /switch-agent pib-orchestrator - Get guidance
```

## Parameters
None - displays the quick reference guide.

## Output
Interactive decision tree showing the most appropriate workflow and commands for different scenarios.