# Record Test Workflow

Record a Playwright test workflow by capturing user interactions for automated test generation.

## Usage
```bash
/record-workflow {workflow-name} --output={path}
```

## Arguments
- `workflow-name`: Name for the recorded test workflow
- `--output`: Optional output directory (defaults to ./tests)

## Implementation
Record complete test workflows with Playwright codegen session:

1. **Start Recording**: Initialize Playwright codegen session
2. **Capture Actions**: Record all user interactions (clicks, fills, navigation)
3. **Generate Test**: Create reusable test file with recorded actions
4. **Save Workflow**: Store test file in specified output directory

Interactive recording flow:
```
Starting workflow recording: user-login-flow
Recording session started...

Perform your test actions in the browser:
- Navigate to pages
- Fill forms
- Click buttons
- Verify content

When finished, the test will be saved automatically.
```

Generated test file structure:
```javascript
// user-login-flow.spec.js
import { test, expect } from '@playwright/test';

test('user-login-flow', async ({ page }) => {
  // Navigate to login page
  await page.goto('http://localhost:3000/login');
  
  // Fill login form
  await page.fill('#email', '<EMAIL>');
  await page.fill('#password', 'password123');
  
  // Submit form
  await page.click('#login-button');
  
  // Verify redirect to dashboard
  await expect(page).toHaveURL('http://localhost:3000/dashboard');
});
```

Features:
- **Auto-generated Comments**: Descriptive comments for each action
- **Assertions**: Automatic verification points
- **Reusable Tests**: Clean, maintainable test code
- **Session Management**: Proper setup and teardown

Storage location: `{output-path}/{workflow-name}.spec.js`

Arguments: $ARGUMENTS