# Test Port

Set the default port for local application testing.

## Usage
```bash
/project:test-port {port}
```

## Arguments
- `port`: Port number to use as default for testing (e.g., 3000, 8080, 5173)

## Implementation
Configure the default port for local development server testing:

1. **Port Configuration**: Set the specified port as default for local testing
2. **Validation**: Verify port availability and accessibility
3. **Service Detection**: Attempt to detect running services on the port
4. **Configuration Storage**: Save port preference to local configuration
5. **Testing Integration**: Update all testing commands to use the new default port

Port configuration process:
- Validate port number (1-65535 range)
- Check if port is currently in use
- Test connectivity to localhost on specified port
- Update default port in testing configuration
- Verify configuration persistence

Service detection attempts:
- HTTP service detection (web applications)
- HTTPS service detection (secure applications)
- WebSocket service detection (real-time applications)
- API endpoint discovery
- Health check endpoint validation

Common development ports:
- 3000: Create React App, Next.js default
- 5173: Vite development server
- 8080: Common development server port
- 4200: Angular CLI development server
- 8000: Django development server
- 5000: Flask development server

Configuration updates:
- Default testing port preference
- Local application base URL
- Health check endpoint configuration
- Service discovery settings
- Timeout and retry configurations

After setting the port:
```
✓ Default testing port set to: {port}
✓ Port accessibility verified
✓ Service detection completed
✓ Configuration saved
✓ Testing commands updated

Default Port: {port}
Base URL: http://localhost:{port}
Service Type: Detected automatically
Health Check: /health (if available)
Status: Ready for testing
```

All testing commands will now use this port as the default when targeting local applications.

Arguments: $ARGUMENTS