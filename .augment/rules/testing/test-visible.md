# Test Visible E2E

Execute end-to-end testing with visible browser for debugging and development.

## Usage
```bash
/project:test-visible {target} --port={port}
```

## Arguments
- `target`: Application URL or service to test
- `--port`: Optional port number (default: 3000)

## Implementation
Run interactive browser tests using <PERSON>wright with visible browser window:
- Browser window displayed for real-time observation
- Slower execution for debugging purposes
- Interactive debugging capabilities
- Step-by-step test execution
- Console logging and error capture

The target should be either a URL (e.g., https://app.example.com) or a service name that resolves to localhost with the specified port.

Test execution includes:
1. Manual breakpoint support
2. Visual validation of UI elements
3. Interactive debugging sessions
4. Real-time error observation
5. Screenshot and video recording
6. Network traffic monitoring

This mode is ideal for:
- Test development and debugging
- Visual regression investigation
- UI behavior validation
- Manual intervention during automated tests

Arguments: $ARGUMENTS