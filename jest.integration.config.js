/**
 * Jest Configuration for Integration Tests
 * 
 * LEVER approach implementation:
 * L - Leverage existing Jest patterns from individual modules
 * E - Extend with comprehensive integration test configuration
 * V - Verify through proper test environment setup
 * E - Eliminate test configuration duplication
 * R - Reduce testing complexity through unified configuration
 */

const { pathsToModuleNameMapper } = require('ts-jest');
const { compilerOptions } = require('./tsconfig.json');

module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Test file patterns
  testMatch: [
    '<rootDir>/tests/integration/**/*.test.ts',
    '<rootDir>/tests/integration/**/*.test.tsx'
  ],
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup/integration-setup.ts'
  ],
  
  // TypeScript configuration
  preset: 'ts-jest',
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: {
        ...compilerOptions,
        esModuleInterop: true,
        allowSyntheticDefaultImports: true
      }
    }]
  },
  
  // Module name mapping for path aliases
  moduleNameMapper: {
    ...pathsToModuleNameMapper(compilerOptions.paths || {}, {
      prefix: '<rootDir>/'
    }),
    // Additional mappings for test utilities
    '^@test-utils/(.*)$': '<rootDir>/tests/utils/$1',
    '^@test-fixtures/(.*)$': '<rootDir>/tests/fixtures/$1'
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage/integration',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  collectCoverageFrom: [
    'packages/*/src/**/*.{ts,tsx}',
    '!packages/*/src/**/*.d.ts',
    '!packages/*/src/**/*.test.{ts,tsx}',
    '!packages/*/src/**/*.spec.{ts,tsx}',
    '!packages/*/src/**/index.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Test timeout (integration tests may take longer)
  testTimeout: 30000,
  
  // Global variables
  globals: {
    'ts-jest': {
      isolatedModules: true
    }
  },
  
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  
  // Verbose output for integration tests
  verbose: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Watch mode configuration
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/coverage/',
    '<rootDir>/.next/',
    '<rootDir>/logs/'
  ],
  
  // Test result processor
  testResultsProcessor: '<rootDir>/tests/utils/test-results-processor.js',
  
  // Custom reporters
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: '<rootDir>/test-results/integration',
      outputName: 'junit.xml',
      suiteName: 'Integration Tests'
    }],
    ['jest-html-reporters', {
      publicPath: '<rootDir>/test-results/integration',
      filename: 'integration-report.html',
      expand: true
    }]
  ],
  
  // Environment variables for tests
  setupFiles: ['<rootDir>/tests/setup/env-setup.js'],
  
  // Module resolution
  resolver: '<rootDir>/tests/utils/jest-resolver.js',
  
  // Transform ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$|@unified-assistant))'
  ],
  
  // Maximum worker processes for parallel execution
  maxWorkers: '50%',
  
  // Cache configuration
  cacheDirectory: '<rootDir>/.jest-cache/integration',
  
  // Snapshot configuration
  snapshotSerializers: [
    '<rootDir>/tests/utils/snapshot-serializers/event-serializer.js',
    '<rootDir>/tests/utils/snapshot-serializers/date-serializer.js'
  ]
};
