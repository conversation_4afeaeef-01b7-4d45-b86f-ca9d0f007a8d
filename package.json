{"name": "unified-ai-assistant", "version": "0.1.0", "description": "Unified AI Assistant Platform - Integrating 8 specialized modules into a cohesive ecosystem", "private": true, "scripts": {"build": "turbo run build", "dev": "./scripts/dev-start.sh start", "dev:setup": "./scripts/dev-setup.sh", "dev:status": "./scripts/dev-start.sh status", "dev:stop": "./scripts/dev-start.sh stop", "dev:health": "./scripts/dev-start.sh health", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "type-check": "turbo run type-check", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build --filter=@unified-assistant/* && changeset publish", "dev:foundation": "turbo run dev --filter=@unified-assistant/foundation", "dev:agent-inbox": "turbo run dev --filter=agent-inbox", "dev:foto-fun": "turbo run dev --filter=foto-fun", "dev:vibe-kanban": "turbo run dev --filter=vibe-kanban-frontend", "dev:open-canvas": "turbo run dev --filter=open-canvas", "dev:vcode": "turbo run dev --filter=vcode", "dev:gen-ui": "turbo run dev --filter=gen-ui-computer-use", "dev:social-media": "turbo run dev --filter=social-media-agent", "dev:langflow": "turbo run dev --filter=langflow", "build:packages": "turbo run build --filter=@unified-assistant/*", "test:integration": "jest --config=jest.integration.config.js", "test:e2e": "playwright test", "storybook": "turbo run storybook --filter=@unified-assistant/ui", "docs:build": "turbo run build --filter=docs", "docs:dev": "turbo run dev --filter=docs", "logs": "./scripts/dev-start.sh logs", "restart": "./scripts/dev-start.sh restart", "analyze": "ANALYZE=true turbo run build", "analyze:packages": "turbo run analyze --filter=@unified-assistant/*"}, "devDependencies": {"@changesets/cli": "^2.27.9", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.4.2", "turbo": "^2.3.3", "typescript": "^5.7.2"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@10.13.1", "repository": {"type": "git", "url": "https://github.com/PMStander/assistant.git"}, "keywords": ["ai", "assistant", "unified-platform", "modular-architecture", "typescript", "react", "nextjs"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "turbo": {"pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"]}, "clean": {"cache": false}, "type-check": {"dependsOn": ["^build"]}}}}