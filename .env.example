# Unified AI Assistant Platform - Environment Configuration
# Copy this file to .env.local and update with your values

# =============================================================================
# ENVIRONMENT
# =============================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development

# =============================================================================
# DATABASE
# =============================================================================
# PostgreSQL connection string
DATABASE_URL=postgresql://username:password@localhost:5432/unified_assistant

# Database pool settings
DATABASE_MAX_CONNECTIONS=20
DATABASE_IDLE_TIMEOUT=30000

# =============================================================================
# AI PROVIDERS
# =============================================================================
# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_ORG_ID=org-your-organization-id
OPENAI_DEFAULT_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7

# Anthropic Configuration
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here
ANTHROPIC_DEFAULT_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4000

# Google AI Configuration
GOOGLE_AI_API_KEY=your-google-ai-api-key-here
GOOGLE_AI_DEFAULT_MODEL=gemini-pro

# Azure OpenAI (optional)
AZURE_OPENAI_API_KEY=your-azure-openai-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# =============================================================================
# AUTHENTICATION
# =============================================================================
# NextAuth.js configuration
NEXTAUTH_SECRET=your-super-secret-jwt-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# OAuth providers (optional)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# =============================================================================
# SUPABASE (if using)
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# =============================================================================
# REDIS (for caching and sessions)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# =============================================================================
# FILE STORAGE
# =============================================================================
# Local storage path
UPLOAD_DIR=./uploads

# AWS S3 (optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# Cloudinary (optional)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Core platform features
NEXT_PUBLIC_ENABLE_AI_ORCHESTRATION=true
NEXT_PUBLIC_ENABLE_CROSS_MODULE_COMMUNICATION=true
NEXT_PUBLIC_ENABLE_SHARED_STATE=true
NEXT_PUBLIC_ENABLE_REAL_TIME_COLLABORATION=true

# Module-specific features
NEXT_PUBLIC_ENABLE_AGENT_INBOX=true
NEXT_PUBLIC_ENABLE_FOTO_FUN=true
NEXT_PUBLIC_ENABLE_VIBE_KANBAN=true
NEXT_PUBLIC_ENABLE_OPEN_CANVAS=true
NEXT_PUBLIC_ENABLE_VCODE=true
NEXT_PUBLIC_ENABLE_GEN_UI_COMPUTER_USE=true
NEXT_PUBLIC_ENABLE_SOCIAL_MEDIA_AGENT=true
NEXT_PUBLIC_ENABLE_LANGFLOW=true

# Advanced features
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================
# Vercel Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS=false

# PostHog (optional)
NEXT_PUBLIC_POSTHOG_KEY=your-posthog-key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# Sentry (optional)
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project

# =============================================================================
# EMAIL (for notifications)
# =============================================================================
# SMTP configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# SendGrid (alternative)
SENDGRID_API_KEY=your-sendgrid-api-key

# =============================================================================
# WEBHOOKS & INTEGRATIONS
# =============================================================================
# Webhook secret for external integrations
WEBHOOK_SECRET=your-webhook-secret

# Slack integration (optional)
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret

# Discord integration (optional)
DISCORD_BOT_TOKEN=your-discord-bot-token
DISCORD_CLIENT_ID=your-discord-client-id

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Logging level (debug, info, warn, error)
LOG_LEVEL=info

# Enable debug mode
DEBUG=false

# API rate limiting
API_RATE_LIMIT_REQUESTS=100
API_RATE_LIMIT_WINDOW_MS=60000

# Development ports (for multi-module setup)
AGENT_INBOX_PORT=3000
FOTO_FUN_PORT=3001
VIBE_KANBAN_PORT=3002
OPEN_CANVAS_PORT=3003
VCODE_PORT=3004
GEN_UI_COMPUTER_USE_PORT=3005
SOCIAL_MEDIA_AGENT_PORT=3006
LANGFLOW_PORT=3007

# =============================================================================
# SECURITY
# =============================================================================
# CORS origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002

# Content Security Policy
CSP_ENABLED=true

# Rate limiting
RATE_LIMIT_ENABLED=true

# =============================================================================
# PERFORMANCE
# =============================================================================
# Bundle analyzer
ANALYZE=false

# Image optimization
NEXT_PUBLIC_ENABLE_IMAGE_OPTIMIZATION=true

# Caching
ENABLE_REDIS_CACHE=true
CACHE_TTL=3600

# =============================================================================
# TESTING
# =============================================================================
# Test database
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/unified_assistant_test

# Test environment
TEST_ENV=test

# Mock external services in tests
MOCK_AI_PROVIDERS=true
MOCK_EXTERNAL_APIS=true
