/**
 * Integration Test Setup
 * 
 * LEVER approach implementation:
 * L - Leverage existing test setup patterns
 * E - Extend with comprehensive integration test environment
 * V - Verify through proper test isolation and cleanup
 * E - Eliminate test environment inconsistencies
 * R - Reduce setup complexity through automation
 */

import { jest } from '@jest/globals';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.REDIS_URL = 'redis://localhost:6379/1';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

// Mock external services
jest.mock('openai', () => ({
  OpenAI: jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn().mockResolvedValue({
          choices: [{
            message: {
              content: 'Mocked AI response',
              role: 'assistant'
            },
            finish_reason: 'stop'
          }],
          usage: {
            prompt_tokens: 10,
            completion_tokens: 5,
            total_tokens: 15
          }
        })
      }
    },
    embeddings: {
      create: jest.fn().mockResolvedValue({
        data: [{
          embedding: new Array(1536).fill(0.1)
        }],
        usage: {
          prompt_tokens: 10,
          total_tokens: 10
        }
      })
    }
  }))
}));

jest.mock('@anthropic-ai/sdk', () => ({
  Anthropic: jest.fn().mockImplementation(() => ({
    messages: {
      create: jest.fn().mockResolvedValue({
        content: [{
          type: 'text',
          text: 'Mocked Anthropic response'
        }],
        usage: {
          input_tokens: 10,
          output_tokens: 5
        }
      })
    }
  }))
}));

// Mock Google AI
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: jest.fn().mockResolvedValue({
        response: {
          text: () => 'Mocked Google AI response'
        }
      })
    })
  }))
}));

// Mock file system operations
jest.mock('fs/promises', () => ({
  readFile: jest.fn().mockResolvedValue('mocked file content'),
  writeFile: jest.fn().mockResolvedValue(undefined),
  mkdir: jest.fn().mockResolvedValue(undefined),
  access: jest.fn().mockResolvedValue(undefined)
}));

// Mock network requests
jest.mock('node-fetch', () => jest.fn().mockResolvedValue({
  ok: true,
  status: 200,
  json: () => Promise.resolve({ success: true }),
  text: () => Promise.resolve('mocked response')
}));

// Global test utilities
global.testUtils = {
  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Create mock event
  createMockEvent: (type: string, payload: any = {}) => ({
    id: `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type,
    source: 'test',
    payload,
    timestamp: new Date(),
    priority: 'normal' as const,
    persistent: false
  }),
  
  // Create mock AI request
  createMockAIRequest: (type: 'chat' | 'completion' | 'embedding' = 'chat') => ({
    id: `ai-request-${Date.now()}`,
    type,
    messages: type === 'chat' ? [{ role: 'user' as const, content: 'Test message' }] : undefined,
    prompt: type !== 'chat' ? 'Test prompt' : undefined,
    context: {
      conversationId: 'test-conversation',
      moduleId: 'test-module'
    }
  }),
  
  // Create mock module registration
  createMockModuleRegistration: (id: string, capabilities: string[] = []) => ({
    id,
    name: `Test ${id}`,
    version: '1.0.0',
    description: `Test module ${id}`,
    capabilities,
    dependencies: [],
    routes: [{ path: `/${id}`, component: `${id}App` }],
    api: { endpoints: [`/api/${id}`] }
  }),
  
  // Mock provider for AI testing
  createMockAIProvider: (id: string) => ({
    id,
    name: `Mock ${id}`,
    config: {
      id,
      name: `Mock ${id}`,
      type: 'openai' as const,
      enabled: true,
      priority: 1,
      config: { apiKey: 'mock-key' },
      capabilities: ['chat' as const, 'completion' as const]
    },
    initialize: jest.fn().mockResolvedValue(undefined),
    healthCheck: jest.fn().mockResolvedValue(true),
    processRequest: jest.fn().mockResolvedValue({
      id: `mock-response-${Date.now()}`,
      requestId: 'test-request',
      provider: id,
      model: 'mock-model',
      content: 'Mock AI response',
      finishReason: 'stop' as const,
      usage: { promptTokens: 10, completionTokens: 5, totalTokens: 15 },
      timestamp: new Date(),
      latency: 100
    }),
    streamRequest: jest.fn(),
    supports: jest.fn().mockReturnValue(true),
    getModels: jest.fn().mockResolvedValue(['mock-model']),
    getMetrics: jest.fn().mockResolvedValue({
      requests: 1,
      errors: 0,
      averageLatency: 100,
      tokensUsed: 15,
      cost: 0.001,
      uptime: 1000
    })
  })
};

// Console override for cleaner test output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

// Restore console for specific tests if needed
global.restoreConsole = () => {
  global.console = originalConsole;
};

// Global test timeout
jest.setTimeout(30000);

// Setup and teardown hooks
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
  
  // Reset timers
  jest.clearAllTimers();
  
  // Reset modules
  jest.resetModules();
});

afterEach(() => {
  // Clean up any remaining timers
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
});

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process in tests, just log the error
});

// Extend Jest matchers
expect.extend({
  toBeValidEvent(received) {
    const pass = received &&
      typeof received.id === 'string' &&
      typeof received.type === 'string' &&
      typeof received.source === 'string' &&
      received.timestamp instanceof Date &&
      ['low', 'normal', 'high', 'critical'].includes(received.priority) &&
      typeof received.persistent === 'boolean';

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid event`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid event`,
        pass: false
      };
    }
  },

  toBeValidAIRequest(received) {
    const pass = received &&
      typeof received.id === 'string' &&
      ['chat', 'completion', 'embedding', 'image'].includes(received.type) &&
      (received.messages || received.prompt);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid AI request`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid AI request`,
        pass: false
      };
    }
  },

  toBeValidModuleRegistration(received) {
    const pass = received &&
      typeof received.id === 'string' &&
      typeof received.name === 'string' &&
      typeof received.version === 'string' &&
      Array.isArray(received.capabilities);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid module registration`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid module registration`,
        pass: false
      };
    }
  }
});

// Type declarations for global utilities
declare global {
  var testUtils: {
    waitFor: (ms: number) => Promise<void>;
    createMockEvent: (type: string, payload?: any) => any;
    createMockAIRequest: (type?: 'chat' | 'completion' | 'embedding') => any;
    createMockModuleRegistration: (id: string, capabilities?: string[]) => any;
    createMockAIProvider: (id: string) => any;
  };
  
  var restoreConsole: () => void;
  
  namespace jest {
    interface Matchers<R> {
      toBeValidEvent(): R;
      toBeValidAIRequest(): R;
      toBeValidModuleRegistration(): R;
    }
  }
}

export {};
