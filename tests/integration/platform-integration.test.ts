/**
 * Platform Integration Tests
 * 
 * LEVER approach implementation:
 * L - Leverage existing test patterns from individual modules
 * E - Extend with comprehensive cross-module integration testing
 * V - Verify through end-to-end workflow validation
 * E - Eliminate integration gaps and edge cases
 * R - Reduce testing complexity through standardized test utilities
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { 
  UnifiedPlatform,
  AgentInboxIntegration,
  PlatformConfig
} from '@unified-assistant/foundation';
import { PlatformEventBus } from '@unified-assistant/events';
import { AIOrchestrator, ProviderManager, ContextManager } from '@unified-assistant/ai';
import { 
  usePlatformStore, 
  useAIStore, 
  useModuleStore,
  setupPlatformStoreEventSync,
  setupAIStoreEventSync
} from '@unified-assistant/state';

describe('Platform Integration Tests', () => {
  let platform: UnifiedPlatform;
  let eventBus: PlatformEventBus;
  let aiOrchestrator: AIOrchestrator;

  const testConfig: PlatformConfig = {
    name: 'Test Platform',
    version: '0.1.0',
    environment: 'test',
    modules: ['agent-inbox', 'foto-fun', 'vibe-kanban'],
    features: {
      'cross-module-communication': true,
      'unified-ai-orchestration': true,
      'shared-state-management': true
    },
    ai: {
      providers: ['openai'],
      defaultProvider: 'openai',
      fallbackEnabled: true
    },
    database: {
      url: 'postgresql://test:test@localhost:5432/test_db'
    },
    auth: {
      enabled: false
    }
  };

  beforeAll(async () => {
    // Initialize platform for testing
    platform = new UnifiedPlatform(testConfig);
    eventBus = platform.getEventBus() as PlatformEventBus;
    
    // Setup AI orchestration
    const providerManager = new ProviderManager();
    const contextManager = new ContextManager(eventBus);
    aiOrchestrator = new AIOrchestrator(providerManager, contextManager, eventBus);
    
    // Initialize platform
    await platform.initialize();
    
    // Setup state synchronization
    setupPlatformStoreEventSync(eventBus);
    setupAIStoreEventSync(eventBus);
  });

  afterAll(async () => {
    await platform.shutdown();
  });

  beforeEach(() => {
    // Reset stores before each test
    usePlatformStore.getState().reset();
    useAIStore.getState().reset();
    useModuleStore.getState().reset();
  });

  describe('Platform Initialization', () => {
    test('should initialize platform with correct configuration', () => {
      const platformStore = usePlatformStore.getState();
      
      expect(platformStore.config).toEqual(testConfig);
      expect(platformStore.status.status).toBe('ready');
      expect(platformStore.eventBus).toBe(eventBus);
    });

    test('should have all required features enabled', () => {
      const platformStore = usePlatformStore.getState();
      
      expect(platformStore.features['cross-module-communication']).toBe(true);
      expect(platformStore.features['unified-ai-orchestration']).toBe(true);
      expect(platformStore.features['shared-state-management']).toBe(true);
    });

    test('should register event bus with all stores', () => {
      const platformStore = usePlatformStore.getState();
      const aiStore = useAIStore.getState();
      
      expect(platformStore.eventBus).toBeDefined();
      expect(platformStore.eventBus).toBe(eventBus);
    });
  });

  describe('Module Registration and Communication', () => {
    test('should register agent-inbox module successfully', async () => {
      const agentInboxRegistration = {
        id: 'agent-inbox',
        name: 'Agent Inbox',
        version: '1.0.0',
        description: 'Communication hub',
        capabilities: ['ai', 'communication'],
        dependencies: [],
        routes: [{ path: '/inbox', component: 'AgentInboxApp' }],
        api: { endpoints: ['/api/messages'] }
      };

      await platform.registerModule(agentInboxRegistration);
      
      const moduleStore = useModuleStore.getState();
      expect(moduleStore.registrations['agent-inbox']).toEqual(agentInboxRegistration);
      expect(moduleStore.statuses['agent-inbox'].status).toBe('loading');
    });

    test('should handle cross-module communication', async () => {
      // Register modules
      await platform.registerModule({
        id: 'agent-inbox',
        name: 'Agent Inbox',
        version: '1.0.0',
        capabilities: ['communication']
      });

      await platform.registerModule({
        id: 'foto-fun',
        name: 'FotoFun',
        version: '1.0.0',
        capabilities: ['image-generation']
      });

      const moduleStore = useModuleStore.getState();
      
      // Send message between modules
      const messageId = moduleStore.sendMessage(
        'agent-inbox',
        'foto-fun',
        'generate-image',
        { prompt: 'test image', style: 'modern' }
      );

      expect(messageId).toBeDefined();
      
      const messages = moduleStore.messages;
      const sentMessage = messages.find(msg => msg.id === messageId);
      
      expect(sentMessage).toBeDefined();
      expect(sentMessage?.from).toBe('agent-inbox');
      expect(sentMessage?.to).toBe('foto-fun');
      expect(sentMessage?.type).toBe('generate-image');
      expect(sentMessage?.status).toBe('pending');
    });

    test('should route events correctly between modules', async () => {
      let receivedEvent: any = null;

      // Setup event listener
      eventBus.on('module:message', {
        id: 'test-listener',
        eventType: 'module:message',
        handler: (event) => {
          receivedEvent = event;
        }
      });

      // Emit cross-module event
      await eventBus.emit({
        id: 'test-event',
        type: 'module:message',
        source: 'agent-inbox',
        target: 'foto-fun',
        payload: { action: 'generate-image', data: { prompt: 'test' } },
        timestamp: new Date(),
        priority: 'normal',
        persistent: false
      });

      // Wait for event processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(receivedEvent).toBeDefined();
      expect(receivedEvent.source).toBe('agent-inbox');
      expect(receivedEvent.target).toBe('foto-fun');
      expect(receivedEvent.payload.action).toBe('generate-image');
    });
  });

  describe('AI Orchestration Integration', () => {
    test('should process AI requests through unified orchestration', async () => {
      // Mock provider for testing
      const mockProvider = {
        id: 'test-provider',
        name: 'Test Provider',
        config: {
          id: 'test-provider',
          name: 'Test Provider',
          type: 'openai' as const,
          enabled: true,
          priority: 1,
          config: { apiKey: 'test-key' },
          capabilities: ['chat' as const]
        },
        initialize: jest.fn().mockResolvedValue(undefined),
        healthCheck: jest.fn().mockResolvedValue(true),
        processRequest: jest.fn().mockResolvedValue({
          id: 'test-response',
          requestId: 'test-request',
          provider: 'test-provider',
          model: 'test-model',
          content: 'Test response',
          finishReason: 'stop' as const,
          usage: { promptTokens: 10, completionTokens: 5, totalTokens: 15 },
          timestamp: new Date(),
          latency: 100
        }),
        streamRequest: jest.fn(),
        supports: jest.fn().mockReturnValue(true),
        getModels: jest.fn().mockResolvedValue(['test-model']),
        getMetrics: jest.fn().mockResolvedValue({
          requests: 1,
          errors: 0,
          averageLatency: 100,
          tokensUsed: 15,
          cost: 0.001,
          uptime: 1000
        })
      };

      // Register mock provider
      const providerManager = new ProviderManager();
      (providerManager as any).providers.set('test-provider', mockProvider);

      const aiRequest = {
        id: 'test-request',
        type: 'chat' as const,
        messages: [{ role: 'user' as const, content: 'Hello, world!' }],
        context: {
          conversationId: 'test-conversation',
          moduleId: 'agent-inbox'
        }
      };

      const result = await aiOrchestrator.processRequest(aiRequest);

      expect(result.primary.content).toBe('Test response');
      expect(result.primary.provider).toBe('test-provider');
      expect(mockProvider.processRequest).toHaveBeenCalledWith(aiRequest);
    });

    test('should update AI store with request/response data', async () => {
      const aiStore = useAIStore.getState();

      // Add a test request
      const testRequest = {
        id: 'test-request-2',
        type: 'chat' as const,
        messages: [{ role: 'user' as const, content: 'Test message' }]
      };

      aiStore.addRequest(testRequest);
      expect(aiStore.activeRequests['test-request-2']).toEqual(testRequest);

      // Add a test response
      const testResponse = {
        id: 'test-response-2',
        requestId: 'test-request-2',
        provider: 'test-provider',
        model: 'test-model',
        content: 'Test response',
        finishReason: 'stop' as const,
        usage: { promptTokens: 10, completionTokens: 5, totalTokens: 15 },
        timestamp: new Date(),
        latency: 100
      };

      aiStore.addResponse(testResponse);
      expect(aiStore.requestHistory[0]).toEqual(testResponse);
      expect(aiStore.activeRequests['test-request-2']).toBeUndefined();
    });
  });

  describe('State Synchronization', () => {
    test('should synchronize platform events with stores', async () => {
      const platformStore = usePlatformStore.getState();

      // Emit platform initialization event
      await eventBus.emit({
        id: 'platform-init-test',
        type: 'platform:initialized',
        source: 'platform',
        payload: { config: testConfig },
        timestamp: new Date(),
        priority: 'normal',
        persistent: false
      });

      // Wait for event processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(platformStore.config).toEqual(testConfig);
      expect(platformStore.status.status).toBe('ready');
    });

    test('should handle module registration events', async () => {
      const moduleStore = useModuleStore.getState();

      // Emit module registration event
      await eventBus.emit({
        id: 'module-reg-test',
        type: 'module:registered',
        source: 'platform',
        payload: {
          id: 'test-module',
          name: 'Test Module',
          version: '1.0.0',
          capabilities: ['test']
        },
        timestamp: new Date(),
        priority: 'normal',
        persistent: false
      });

      // Wait for event processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(moduleStore.registrations['test-module']).toBeDefined();
      expect(moduleStore.registrations['test-module'].name).toBe('Test Module');
    });

    test('should handle AI events in AI store', async () => {
      const aiStore = useAIStore.getState();

      // Emit AI request start event
      await eventBus.emit({
        id: 'ai-request-start-test',
        type: 'ai:request:start',
        source: 'ai-orchestrator',
        payload: {
          requestId: 'test-ai-request',
          request: {
            id: 'test-ai-request',
            type: 'chat',
            messages: [{ role: 'user', content: 'Test' }]
          }
        },
        timestamp: new Date(),
        priority: 'normal',
        persistent: false
      });

      // Wait for event processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(aiStore.activeRequests['test-ai-request']).toBeDefined();
      expect(aiStore.loading.request).toBe(true);
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should handle module registration errors gracefully', async () => {
      const platformStore = usePlatformStore.getState();

      // Try to register invalid module
      try {
        await platform.registerModule({
          id: '',
          name: '',
          version: '',
          capabilities: []
        });
      } catch (error) {
        expect(error).toBeDefined();
      }

      // Platform should still be operational
      expect(platformStore.status.status).toBe('ready');
    });

    test('should track errors across all stores', async () => {
      const platformStore = usePlatformStore.getState();
      const aiStore = useAIStore.getState();
      const moduleStore = useModuleStore.getState();

      // Add errors to different stores
      platformStore.addError({
        message: 'Platform error',
        severity: 'high'
      });

      aiStore.addError({
        type: 'request',
        message: 'AI request failed'
      });

      moduleStore.addError({
        moduleId: 'test-module',
        type: 'runtime',
        message: 'Module runtime error',
        severity: 'medium'
      });

      expect(platformStore.errors).toHaveLength(1);
      expect(aiStore.errors).toHaveLength(1);
      expect(moduleStore.errors).toHaveLength(1);

      // Test error clearing
      platformStore.clearErrors();
      aiStore.clearErrors();
      moduleStore.clearErrors();

      expect(platformStore.errors).toHaveLength(0);
      expect(aiStore.errors).toHaveLength(0);
      expect(moduleStore.errors).toHaveLength(0);
    });
  });

  describe('Performance and Metrics', () => {
    test('should track platform metrics', () => {
      const platformStore = usePlatformStore.getState();

      // Update metrics
      platformStore.updateMetrics({
        uptime: 1000,
        activeUsers: 5,
        requestsPerMinute: 100,
        errorRate: 0.01,
        memoryUsage: 512
      });

      expect(platformStore.metrics.uptime).toBe(1000);
      expect(platformStore.metrics.activeUsers).toBe(5);
      expect(platformStore.metrics.requestsPerMinute).toBe(100);
      expect(platformStore.metrics.errorRate).toBe(0.01);
      expect(platformStore.metrics.memoryUsage).toBe(512);
    });

    test('should track AI provider metrics', () => {
      const aiStore = useAIStore.getState();

      // Update provider metrics
      aiStore.updateProviderMetrics('test-provider', {
        requests: 100,
        errors: 2,
        averageLatency: 150,
        tokensUsed: 5000,
        cost: 0.50,
        uptime: 3600
      });

      const metrics = aiStore.providerMetrics['test-provider'];
      expect(metrics.requests).toBe(100);
      expect(metrics.errors).toBe(2);
      expect(metrics.averageLatency).toBe(150);
      expect(metrics.tokensUsed).toBe(5000);
      expect(metrics.cost).toBe(0.50);
      expect(metrics.uptime).toBe(3600);
    });
  });
});
