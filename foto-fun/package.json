{"name": "foto-fun", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "bun run lib/db/migrate.ts", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@ai-sdk/react": "^0.0.66", "@pixi/react": "^8.0.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "ai": "^3.4.33", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.2", "fabric": "^6.0.0", "file-saver": "^2.0.5", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "pixi.js": "^8.11.0", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "replicate": "^1.0.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.75", "zustand": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/fabric": "^5.3.10", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/pixi.js": "^5.0.0", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.6.0", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "vitest": "^3.2.4"}}