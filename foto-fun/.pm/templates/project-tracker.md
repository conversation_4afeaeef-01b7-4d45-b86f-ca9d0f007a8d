# [Project Name] - Project Tracker

## Overview
This document tracks high-level progress across all epics, maintains key architectural decisions, and serves as persistent memory for the project.

## Epic Status Overview

| Epic # | Epic Name | Status | Start Date | End Date | Key Outcome |
|--------|-----------|--------|------------|----------|-------------|
| 01 | [Name] | NOT STARTED | - | - | [Main deliverable] |
| 02 | [Name] | NOT STARTED | - | - | [Main deliverable] |
| 03 | [Name] | NOT STARTED | - | - | [Main deliverable] |

**Statuses**: NOT STARTED | IN PROGRESS | COMPLETED | BLOCKED

## Completed Epics Summary

### Epic 01: [Name]
**Completed**: [Date]
**Key Features Delivered**:
- [Feature 1]
- [Feature 2]

**Major Architectural Decisions**:
- [Decision 1 with brief rationale]
- [Decision 2 with brief rationale]

**Important Learnings**:
- [What worked well]
- [What was challenging]

**Dependencies Created**:
- [What future epics depend on this]

---

[Repeat for each completed epic]

## Cross-Epic Architectural Decisions

### Decision Log
| Date | Decision | Rationale | Impacts |
|------|----------|-----------|---------|
| [Date] | [What was decided] | [Why] | [Which epics affected] |

### Established Patterns
- **Authentication**: [Pattern used across the app]
- **Error Handling**: [Consistent approach]
- **Data Fetching**: [Pattern for API calls]
- **State Management**: [How state is handled]
- **Component Structure**: [Organizational pattern]

## Technology Stack Evolution

### Core Stack (from PRD)
- Frontend: [Framework]
- Backend: [Framework]
- Database: [Type]

### Additional Libraries Added
| Library | Purpose | Added In Epic | Rationale |
|---------|---------|---------------|-----------|
| [Library] | [Why needed] | Epic # | [Reason for choice] |

## Critical Gotchas & Learnings

### Things That Tripped Us Up
1. **[Issue Name]**
   - Epic: [Where encountered]
   - Problem: [What happened]
   - Solution: [How we fixed it]
   - Future Prevention: [How to avoid]

### Performance Optimizations
- [Optimization made] - Epic # - [Impact]

### Security Considerations Implemented
- [Security measure] - Epic # - [What it protects]

## User Story Completion Status

| User Story | Status | Fully Enabled By Epics | Notes |
|------------|--------|------------------------|-------|
| Story 1: [Name] | COMPLETE | Epic 1, 2 | [Any limitations] |
| Story 2: [Name] | PARTIAL | Epic 1 | [What's missing] |
| Story 3: [Name] | NOT STARTED | - | [Planned for Epic X] |

## Refactoring & Technical Debt

### Completed Refactoring
- [What was refactored] - Epic # - [Benefit]

### Identified Technical Debt
| Issue | Severity | Identified In | Planned Resolution |
|-------|----------|---------------|-------------------|
| [Debt item] | HIGH/MED/LOW | Epic # | Epic # or "Post-MVP" |

## Production Readiness Checklist

- [ ] All user stories for MVP completed
- [ ] Error handling comprehensive
- [ ] Performance acceptable
- [ ] Security measures in place
- [ ] Documentation updated
- [ ] Deployment pipeline ready
- [ ] Monitoring configured

## Feature Backlog

### Prioritized Features (Post-MVP)
| Priority | Feature | User Story | Estimated Epics | Notes |
|----------|---------|------------|-----------------|-------|
| P0 (Critical) | [Feature name] | Story X | 1-2 | [Why important] |
| P1 (High) | [Feature name] | Story Y | 1 | [Dependencies] |
| P1 (High) | [Feature name] | Story Z | 2-3 | [Complexity notes] |
| P2 (Medium) | [Feature name] | New Story | 1 | [Nice to have] |
| P3 (Low) | [Feature name] | Story X enhancement | 1 | [Future consideration] |

**Priority Definitions**:
- **P0**: Critical for post-MVP launch
- **P1**: High value, implement soon after MVP
- **P2**: Medium value, clear benefit but not urgent
- **P3**: Nice to have, implement if time permits

### Ideas & Future Considerations
- [Feature idea] - [Brief description, no commitment]
- [Feature idea] - [Brief description, no commitment]

## Bug Tracker

### Active Bugs
| ID | Description | Severity | Found In | Status | Assigned To |
|----|-------------|----------|----------|--------|-------------|
| B001 | [Bug description] | CRITICAL | Epic 1/Sprint 1.2 | OPEN | Epic X |
| B002 | [Bug description] | HIGH | Epic 2/Sprint 2.1 | IN PROGRESS | Current Sprint |
| B003 | [Bug description] | MEDIUM | Epic 1/Sprint 1.3 | OPEN | Backlog |
| B004 | [Bug description] | LOW | Epic 2/Sprint 2.2 | OPEN | Backlog |

**Severity Levels**:
- **CRITICAL**: Breaks core functionality, fix immediately
- **HIGH**: Significant issue, fix in next sprint
- **MEDIUM**: Noticeable issue, fix when possible
- **LOW**: Minor issue, fix if time permits

### Resolved Bugs
| ID | Description | Fixed In | Resolution |
|----|-------------|----------|------------|
| B000 | [Bug description] | Epic 2/Sprint 2.3 | [What was fixed] |

## Open Questions & Decisions Needed

1. [Question] - Blocks: [Which epic/feature]
2. [Question] - Blocks: [Which epic/feature]

## Next Steps

**Current Epic**: [Epic # and name]
**Next Planned Epic**: [Epic # and name]
**Blocked Items**: [Any blockers across the project]
**P0 Items in Backlog**: [Count of critical items waiting]

---

*Last Updated: [Date]*
*Updated By: [Epic # that triggered update]*