# Cross-Module Integration Patterns

## Overview

This document defines the standardized patterns for integrating 8 independent modules into the unified AI assistant platform. These patterns ensure consistent communication, data sharing, and user experience across all modules.

## Core Integration Principles

### 1. **Event-Driven Architecture**
All modules communicate through a centralized event bus, enabling loose coupling and scalable integration.

### 2. **Shared Context Management**
Conversation and workflow context flows seamlessly between modules, maintaining user intent and session state.

### 3. **Unified UI Framework**
All modules use Radix UI + Tailwind CSS 4 for consistent design and interaction patterns.

### 4. **AI Orchestration**
A unified AI layer coordinates multiple providers and agents across modules.

### 5. **Progressive Enhancement**
Modules can be loaded and unloaded dynamically based on user needs and feature flags.

## Module Communication Patterns

### 1. Event Bus Communication
```typescript
// Centralized event bus for module communication
interface ModuleEvent {
  source: string                    // Source module identifier
  target?: string                   // Target module (broadcast if empty)
  type: string                      // Event type (message-sent, data-updated, etc.)
  data: any                        // Event payload
  timestamp: number                // Event timestamp
  correlation?: string             // Request correlation ID
  priority?: 'low' | 'normal' | 'high' // Event priority
}

class ModuleEventBus {
  private listeners = new Map<string, EventListener[]>()
  private middleware: EventMiddleware[] = []
  private eventHistory: ModuleEvent[] = []
  
  // Subscribe to events with optional filtering
  subscribe(
    eventType: string, 
    callback: EventListener,
    filter?: EventFilter
  ): UnsubscribeFunction {
    const listener: EventListener = {
      callback,
      filter,
      id: generateId()
    }
    
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, [])
    }
    this.listeners.get(eventType)!.push(listener)
    
    return () => this.unsubscribe(eventType, listener.id)
  }
  
  // Emit events with middleware processing
  async emit(event: ModuleEvent): Promise<void> {
    // Process through middleware pipeline
    let processedEvent = event
    for (const middleware of this.middleware) {
      processedEvent = await middleware.process(processedEvent)
    }
    
    // Store in history for debugging
    this.eventHistory.push(processedEvent)
    
    // Deliver to subscribers
    const listeners = this.listeners.get(processedEvent.type) || []
    const promises = listeners
      .filter(listener => this.matchesFilter(processedEvent, listener.filter))
      .map(listener => listener.callback(processedEvent))
    
    await Promise.all(promises)
  }
  
  // Middleware for logging, validation, transformation
  use(middleware: EventMiddleware) {
    this.middleware.push(middleware)
  }
}

// Example middleware implementations
class LoggingMiddleware implements EventMiddleware {
  async process(event: ModuleEvent): Promise<ModuleEvent> {
    console.log(`[EventBus] ${event.source} → ${event.target || 'broadcast'}: ${event.type}`)
    return event
  }
}

class ValidationMiddleware implements EventMiddleware {
  async process(event: ModuleEvent): Promise<ModuleEvent> {
    this.validateEventStructure(event)
    this.validatePermissions(event)
    return event
  }
}
```

### 2. Request-Response Pattern
```typescript
// Structured request-response for module interactions
interface ModuleRequest {
  id: string
  source: string
  target: string
  action: string
  parameters: any
  timeout?: number
  priority?: 'low' | 'normal' | 'high'
}

interface ModuleResponse {
  requestId: string
  success: boolean
  data?: any
  error?: ModuleError
  metadata?: ResponseMetadata
}

class ModuleRequestHandler {
  private pendingRequests = new Map<string, PendingRequest>()
  
  async sendRequest(request: ModuleRequest): Promise<ModuleResponse> {
    const requestId = generateId()
    const fullRequest = { ...request, id: requestId }
    
    // Set up response handler
    const responsePromise = new Promise<ModuleResponse>((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new Error(`Request timeout: ${request.action}`))
      }, request.timeout || 30000)
      
      this.pendingRequests.set(requestId, { resolve, reject, timeout })
    })
    
    // Send request via event bus
    await this.eventBus.emit({
      source: request.source,
      target: request.target,
      type: 'module-request',
      data: fullRequest,
      timestamp: Date.now()
    })
    
    return responsePromise
  }
  
  handleResponse(response: ModuleResponse) {
    const pending = this.pendingRequests.get(response.requestId)
    if (pending) {
      clearTimeout(pending.timeout)
      this.pendingRequests.delete(response.requestId)
      pending.resolve(response)
    }
  }
}
```

### 3. Pub-Sub Pattern for State Updates
```typescript
// State synchronization across modules
interface StateUpdate {
  module: string
  resource: string
  operation: 'create' | 'update' | 'delete'
  data: any
  version: number
  timestamp: number
}

class StateSync {
  private subscriptions = new Map<string, StateSubscription[]>()
  
  // Subscribe to state changes
  subscribeToState(
    resource: string,
    callback: (update: StateUpdate) => void
  ): UnsubscribeFunction {
    const subscription: StateSubscription = {
      id: generateId(),
      callback,
      resource
    }
    
    if (!this.subscriptions.has(resource)) {
      this.subscriptions.set(resource, [])
    }
    this.subscriptions.get(resource)!.push(subscription)
    
    return () => this.unsubscribe(resource, subscription.id)
  }
  
  // Publish state changes
  async publishStateUpdate(update: StateUpdate) {
    const subscribers = this.subscriptions.get(update.resource) || []
    
    // Notify all subscribers
    const notifications = subscribers.map(sub => 
      this.safeCallback(sub.callback, update)
    )
    
    await Promise.allSettled(notifications)
    
    // Also emit via event bus for logging/debugging
    await this.eventBus.emit({
      source: update.module,
      type: 'state-updated',
      data: update,
      timestamp: Date.now()
    })
  }
}
```

## Context Sharing Patterns

### 1. Conversation Context Flow
```typescript
// Shared conversation context across modules
interface ConversationContext {
  id: string
  userId: string
  sessionId: string
  topic?: string
  intent?: UserIntent
  entities: ExtractedEntity[]
  history: ContextHistory[]
  moduleStates: Record<string, any>
  aiProviders: ActiveProvider[]
  createdAt: Date
  updatedAt: Date
}

interface UserIntent {
  primary: string                   // Main user goal
  secondary?: string[]              // Additional goals
  confidence: number                // Intent confidence score
  parameters: Record<string, any>   // Intent parameters
}

class ContextManager {
  private contexts = new Map<string, ConversationContext>()
  private contextStore: ContextStore
  
  // Create or retrieve context
  async getContext(conversationId: string): Promise<ConversationContext> {
    if (this.contexts.has(conversationId)) {
      return this.contexts.get(conversationId)!
    }
    
    // Load from persistent store
    const context = await this.contextStore.load(conversationId)
    if (context) {
      this.contexts.set(conversationId, context)
      return context
    }
    
    // Create new context
    const newContext = this.createNewContext(conversationId)
    this.contexts.set(conversationId, newContext)
    await this.contextStore.save(newContext)
    
    return newContext
  }
  
  // Update context when module state changes
  async updateModuleState(
    conversationId: string,
    moduleId: string,
    state: any
  ): Promise<void> {
    const context = await this.getContext(conversationId)
    
    context.moduleStates[moduleId] = state
    context.updatedAt = new Date()
    
    // Persist changes
    await this.contextStore.save(context)
    
    // Notify other modules of context update
    await this.eventBus.emit({
      source: 'context-manager',
      type: 'context-updated',
      data: { conversationId, moduleId, state },
      timestamp: Date.now()
    })
  }
}
```

### 2. Workflow Context Passing
```typescript
// Context passing for cross-module workflows
interface WorkflowContext {
  workflowId: string
  userId: string
  currentStep: WorkflowStep
  completedSteps: WorkflowStep[]
  data: WorkflowData
  metadata: WorkflowMetadata
}

interface WorkflowStep {
  id: string
  moduleId: string
  action: string
  parameters: any
  status: 'pending' | 'running' | 'completed' | 'failed'
  result?: any
  error?: string
  startTime?: Date
  endTime?: Date
}

class WorkflowOrchestrator {
  async executeWorkflow(workflow: WorkflowDefinition): Promise<WorkflowResult> {
    const context = this.initializeWorkflowContext(workflow)
    
    for (const step of workflow.steps) {
      await this.executeStep(step, context)
      
      if (step.status === 'failed' && !step.optional) {
        throw new WorkflowError(`Step ${step.id} failed`, context)
      }
    }
    
    return this.finalizeWorkflow(context)
  }
  
  private async executeStep(
    step: WorkflowStep, 
    context: WorkflowContext
  ): Promise<void> {
    step.status = 'running'
    step.startTime = new Date()
    
    try {
      // Send request to target module
      const response = await this.requestHandler.sendRequest({
        id: generateId(),
        source: 'workflow-orchestrator',
        target: step.moduleId,
        action: step.action,
        parameters: {
          ...step.parameters,
          workflowContext: context
        }
      })
      
      step.result = response.data
      step.status = 'completed'
      
      // Update workflow context with step result
      context.data = this.mergeStepResult(context.data, step.result)
      
    } catch (error) {
      step.error = error.message
      step.status = 'failed'
    } finally {
      step.endTime = new Date()
    }
  }
}
```

## Data Flow Patterns

### 1. Reactive Data Flow
```typescript
// Reactive data updates across modules
class ReactiveDataFlow {
  private observables = new Map<string, Observable<any>>()
  private subscriptions = new Map<string, Subscription[]>()
  
  // Create observable data source
  createObservable<T>(key: string, initialValue: T): Observable<T> {
    const subject = new BehaviorSubject<T>(initialValue)
    this.observables.set(key, subject)
    return subject.asObservable()
  }
  
  // Update observable data
  updateData<T>(key: string, value: T): void {
    const subject = this.observables.get(key) as BehaviorSubject<T>
    if (subject) {
      subject.next(value)
      
      // Emit event for non-reactive subscribers
      this.eventBus.emit({
        source: 'data-flow',
        type: 'data-updated',
        data: { key, value },
        timestamp: Date.now()
      })
    }
  }
  
  // Subscribe to data changes
  subscribeToData<T>(
    key: string,
    callback: (value: T) => void
  ): UnsubscribeFunction {
    const observable = this.observables.get(key)
    if (!observable) {
      throw new Error(`Observable ${key} not found`)
    }
    
    const subscription = observable.subscribe(callback)
    
    if (!this.subscriptions.has(key)) {
      this.subscriptions.set(key, [])
    }
    this.subscriptions.get(key)!.push(subscription)
    
    return () => {
      subscription.unsubscribe()
      this.removeSubscription(key, subscription)
    }
  }
}
```

### 2. Data Transformation Pipeline
```typescript
// Transform data between modules with different schemas
interface DataTransformer {
  transform(data: any, sourceModule: string, targetModule: string): any
  validate(data: any, schema: DataSchema): boolean
}

class ModuleDataTransformer implements DataTransformer {
  private transformers = new Map<string, TransformFunction>()
  private schemas = new Map<string, DataSchema>()
  
  // Register transformation functions
  registerTransformer(
    route: string, // "sourceModule:targetModule"
    transformer: TransformFunction
  ): void {
    this.transformers.set(route, transformer)
  }
  
  transform(data: any, sourceModule: string, targetModule: string): any {
    const route = `${sourceModule}:${targetModule}`
    const transformer = this.transformers.get(route)
    
    if (!transformer) {
      // Default: pass through if no transformer registered
      return data
    }
    
    try {
      const transformed = transformer(data)
      
      // Validate against target schema
      const targetSchema = this.schemas.get(targetModule)
      if (targetSchema && !this.validate(transformed, targetSchema)) {
        throw new Error(`Transformed data doesn't match ${targetModule} schema`)
      }
      
      return transformed
    } catch (error) {
      console.error(`Data transformation failed: ${route}`, error)
      throw error
    }
  }
  
  validate(data: any, schema: DataSchema): boolean {
    // Implementation depends on schema validation library (e.g., Zod, Joi)
    return schema.safeParse(data).success
  }
}

// Example transformers
const agentInboxToVCodeTransformer: TransformFunction = (data) => ({
  code: data.content.code,
  language: data.metadata.language || 'typescript',
  filename: data.metadata.filename,
  context: {
    conversation: data.conversationId,
    userIntent: data.intent
  }
})

const fotoFunToSocialMediaTransformer: TransformFunction = (data) => ({
  imageUrl: data.generatedImage.url,
  alt: data.generatedImage.description,
  caption: data.userPrompt,
  tags: data.generatedImage.tags,
  metadata: {
    generator: 'foto-fun',
    style: data.style,
    dimensions: data.generatedImage.dimensions
  }
})
```

## UI Integration Patterns

### 1. Unified Layout System
```typescript
// Consistent layout across all modules
interface ModuleLayoutProps {
  moduleId: string
  title: string
  children: React.ReactNode
  actions?: ActionItem[]
  sidebar?: React.ReactNode
  footer?: React.ReactNode
}

export function UnifiedModuleLayout({
  moduleId,
  title,
  children,
  actions,
  sidebar,
  footer
}: ModuleLayoutProps) {
  const { user } = useAuth()
  const { notifications } = useNotifications()
  const { activeModules } = useModules()
  
  return (
    <div className="h-screen flex flex-col">
      {/* Global navigation */}
      <GlobalNav 
        user={user}
        activeModule={moduleId}
        modules={activeModules}
        notifications={notifications}
      />
      
      <div className="flex-1 flex">
        {/* Module navigation */}
        <ModuleNavigation 
          moduleId={moduleId}
          actions={actions}
        />
        
        {/* Main content area */}
        <main className="flex-1 flex">
          <div className="flex-1 p-6">
            <ModuleHeader title={title} />
            {children}
          </div>
          
          {/* Sidebar if provided */}
          {sidebar && (
            <aside className="w-80 border-l border-border">
              {sidebar}
            </aside>
          )}
        </main>
      </div>
      
      {/* Footer if provided */}
      {footer && (
        <footer className="border-t border-border">
          {footer}
        </footer>
      )}
    </div>
  )
}
```

### 2. Cross-Module Navigation
```typescript
// Navigation between modules with context preservation
interface ModuleNavigation {
  navigateToModule(
    targetModule: string,
    action?: string,
    context?: any
  ): Promise<void>
  
  openModuleInSidebar(
    moduleId: string,
    props?: any
  ): void
  
  openModuleInDialog(
    moduleId: string,
    props?: any
  ): Promise<any>
}

class ModuleNavigationManager implements ModuleNavigation {
  constructor(
    private router: NextRouter,
    private contextManager: ContextManager,
    private eventBus: ModuleEventBus
  ) {}
  
  async navigateToModule(
    targetModule: string,
    action?: string,
    context?: any
  ): Promise<void> {
    // Preserve current context
    const currentContext = await this.contextManager.getCurrentContext()
    
    // Build navigation URL
    const url = this.buildModuleUrl(targetModule, action)
    
    // Emit navigation event for cleanup
    await this.eventBus.emit({
      source: 'navigation-manager',
      type: 'module-navigation',
      data: { 
        from: this.getCurrentModule(),
        to: targetModule,
        action,
        context: { ...currentContext, ...context }
      },
      timestamp: Date.now()
    })
    
    // Navigate
    await this.router.push(url)
  }
  
  openModuleInSidebar(moduleId: string, props?: any): void {
    // Implementation for sidebar module loading
    this.eventBus.emit({
      source: 'navigation-manager',
      type: 'open-sidebar-module',
      data: { moduleId, props },
      timestamp: Date.now()
    })
  }
  
  async openModuleInDialog(moduleId: string, props?: any): Promise<any> {
    // Implementation for modal module loading
    return new Promise((resolve) => {
      this.eventBus.emit({
        source: 'navigation-manager',
        type: 'open-dialog-module',
        data: { 
          moduleId, 
          props, 
          onClose: resolve 
        },
        timestamp: Date.now()
      })
    })
  }
}
```

### 3. Shared Component Integration
```typescript
// Shared components that work across all modules
interface SharedComponentProps {
  moduleContext?: string
  variant?: ComponentVariant
  onAction?: (action: string, data?: any) => void
}

// AI Chat interface usable in any module
export function UniversalAIChat({ 
  moduleContext,
  variant = 'full',
  onAction 
}: SharedComponentProps) {
  const { aiOrchestrator } = useAI()
  const { contextManager } = useContext()
  
  const handleMessage = async (message: string) => {
    const context = await contextManager.getCurrentContext()
    const response = await aiOrchestrator.processMessage(message, {
      ...context,
      moduleContext
    })
    
    onAction?.('message-sent', { message, response })
  }
  
  return (
    <div className={cn(
      "ai-chat",
      variant === 'compact' && "compact-mode",
      variant === 'sidebar' && "sidebar-mode"
    )}>
      <ChatMessages />
      <ChatInput onSend={handleMessage} />
    </div>
  )
}

// File picker that works with all modules
export function UniversalFilePicker({
  accept,
  multiple = false,
  onSelect
}: {
  accept?: string
  multiple?: boolean
  onSelect: (files: File[]) => void
}) {
  const { fileManager } = useFiles()
  
  return (
    <div className="file-picker">
      <input
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={(e) => {
          const files = Array.from(e.target.files || [])
          onSelect(files)
        }}
        className="hidden"
        id="file-input"
      />
      <label htmlFor="file-input" className="cursor-pointer">
        <Button variant="outline">
          <Upload className="w-4 h-4 mr-2" />
          Select Files
        </Button>
      </label>
    </div>
  )
}
```

## Error Handling Patterns

### 1. Centralized Error Management
```typescript
// Unified error handling across modules
interface ModuleError {
  code: string
  message: string
  module: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  context?: any
  stack?: string
  timestamp: Date
}

class ErrorManager {
  private errorHandlers = new Map<string, ErrorHandler>()
  private errorLog: ModuleError[] = []
  
  // Register module-specific error handlers
  registerErrorHandler(moduleId: string, handler: ErrorHandler): void {
    this.errorHandlers.set(moduleId, handler)
  }
  
  // Handle errors with appropriate response
  async handleError(error: ModuleError): Promise<void> {
    // Log error
    this.errorLog.push(error)
    console.error(`[${error.module}] ${error.code}: ${error.message}`, error)
    
    // Try module-specific handler first
    const moduleHandler = this.errorHandlers.get(error.module)
    if (moduleHandler) {
      try {
        await moduleHandler.handle(error)
        return
      } catch (handlerError) {
        console.error('Error handler failed:', handlerError)
      }
    }
    
    // Fallback to default handling
    await this.defaultErrorHandling(error)
    
    // Emit error event for monitoring
    this.eventBus.emit({
      source: 'error-manager',
      type: 'error-occurred',
      data: error,
      timestamp: Date.now()
    })
  }
  
  private async defaultErrorHandling(error: ModuleError): Promise<void> {
    switch (error.severity) {
      case 'critical':
        // Show error dialog and potentially reload module
        this.showErrorDialog(error)
        break
      case 'high':
        // Show toast notification
        this.showErrorToast(error)
        break
      case 'medium':
      case 'low':
        // Log only, no user notification
        break
    }
  }
}
```

### 2. Retry and Fallback Patterns
```typescript
// Automatic retry with exponential backoff
class RetryManager {
  async withRetry<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    const {
      maxAttempts = 3,
      baseDelay = 1000,
      maxDelay = 10000,
      backoffFactor = 2
    } = options
    
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxAttempts) {
          break
        }
        
        // Calculate delay with exponential backoff
        const delay = Math.min(
          baseDelay * Math.pow(backoffFactor, attempt - 1),
          maxDelay
        )
        
        await this.sleep(delay)
      }
    }
    
    throw lastError!
  }
  
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Fallback patterns for module failures
class FallbackManager {
  private fallbacks = new Map<string, FallbackStrategy>()
  
  registerFallback(moduleId: string, strategy: FallbackStrategy): void {
    this.fallbacks.set(moduleId, strategy)
  }
  
  async handleModuleFailure(moduleId: string, error: Error): Promise<any> {
    const strategy = this.fallbacks.get(moduleId)
    
    if (!strategy) {
      throw new Error(`No fallback strategy for module: ${moduleId}`)
    }
    
    switch (strategy.type) {
      case 'graceful-degradation':
        return strategy.fallbackFunction()
      
      case 'alternative-module':
        return this.routeToAlternativeModule(strategy.alternativeModule, error)
      
      case 'cached-response':
        return this.getCachedResponse(moduleId, strategy.cacheKey)
      
      default:
        throw error
    }
  }
}
```

---

*These integration patterns provide the foundation for seamlessly connecting 8 independent modules into a unified, intelligent platform that delivers exponentially more value than the sum of its parts.*