# Development Standards & Technical Guidelines

## Core Development Principles

### LEVER Framework - MANDATORY for ALL Development
Before ANY implementation, ALWAYS follow the LEVER principles:
- **L** - Leverage existing patterns
- **E** - Extend before creating
- **V** - Verify through reactivity
- **E** - Eliminate duplication
- **R** - Reduce complexity

**Philosophy**: "The best code is no code. The second best code is code that already exists and works."

### Pre-Implementation Checklist (MANDATORY)
Before writing ANY code:
- [ ] What similar functionality already exists?
- [ ] Can I extend an existing table/query/hook/component?
- [ ] What's the minimal code needed?
- [ ] Have I eliminated all duplication?

## Technology Standards

### Core Technology Stack
```typescript
// Standardized across all modules
const standardStack = {
  framework: "Next.js 15",           // App Router, React 19
  runtime: "Bun",                    // Package manager and runtime
  ui: "Radix UI + Tailwind CSS 4",   // Consistent UI framework
  state: "Zustand + Immer",          // State management
  database: "Supabase + Drizzle ORM", // Database and ORM
  package_manager: "pnpm",           // Package management
  build_tool: "Turbo",               // Monorepo orchestration
  testing: "Vitest + Playwright",    // Testing framework
  ai_primary: "LangChain",           // AI orchestration
  ai_secondary: "Vercel AI SDK",     // Real-time AI streaming
}
```

### File Structure Standards
```
module-name/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   └── features/       # Feature-specific components
├── hooks/              # Custom React hooks
├── stores/             # Zustand stores
├── api/                # API routes and utilities
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── __tests__/          # Test files
```

## Code Style Standards

### TypeScript Guidelines
```typescript
// Use strict TypeScript configuration
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true
  }
}

// Prefer interfaces over types for objects
interface UserProfile {
  id: string
  name: string
  email: string
  preferences?: UserPreferences
}

// Use const assertions for literal types
const themes = ['light', 'dark', 'system'] as const
type Theme = typeof themes[number]

// Prefer async/await over Promises
async function fetchUserData(id: string): Promise<UserProfile> {
  const response = await fetch(`/api/users/${id}`)
  if (!response.ok) {
    throw new Error(`Failed to fetch user: ${response.statusText}`)
  }
  return response.json()
}
```

### React Component Standards
```typescript
// Use function components with TypeScript
interface ComponentProps {
  title: string
  children: React.ReactNode
  onAction?: () => void
}

export function MyComponent({ title, children, onAction }: ComponentProps) {
  // Use descriptive variable names
  const [isLoading, setIsLoading] = useState(false)
  
  // Handle effects properly
  useEffect(() => {
    // Cleanup function for subscriptions
    const cleanup = subscribeToUpdates()
    return cleanup
  }, [])
  
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">{title}</h2>
      {children}
      {onAction && (
        <Button onClick={onAction}>
          Take Action
        </Button>
      )}
    </div>
  )
}
```

### State Management Standards
```typescript
// Zustand with Immer for state management
interface AppState {
  user: User | null
  modules: ModuleState
  ui: UIState
}

interface AppActions {
  setUser: (user: User) => void
  updateModule: (moduleId: string, updates: Partial<ModuleState>) => void
  resetState: () => void
}

export const useAppStore = create<AppState & AppActions>()(
  immer((set) => ({
    // Initial state
    user: null,
    modules: {},
    ui: { theme: 'system' },
    
    // Actions
    setUser: (user) => {
      set((state) => {
        state.user = user
      })
    },
    
    updateModule: (moduleId, updates) => {
      set((state) => {
        Object.assign(state.modules[moduleId], updates)
      })
    },
    
    resetState: () => {
      set(() => ({
        user: null,
        modules: {},
        ui: { theme: 'system' }
      }))
    }
  }))
)
```

## Database Standards

### Schema Design Principles
```sql
-- Use consistent naming conventions
-- Tables: snake_case, plural
-- Columns: snake_case, descriptive
-- Indexes: table_column_idx format

CREATE TABLE user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Always include created_at and updated_at
-- Use UUID for primary keys
-- Use meaningful constraints
```

### Drizzle ORM Patterns
```typescript
// Define schemas with proper types
export const userProfiles = pgTable('user_profiles', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull().unique(),
  displayName: text('display_name').notNull(),
  avatarUrl: text('avatar_url'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
})

// Use proper relations
export const userProfilesRelations = relations(userProfiles, ({ many }) => ({
  workspaces: many(workspaces)
}))

// Type-safe queries
export async function getUserProfile(id: string) {
  return await db
    .select()
    .from(userProfiles)
    .where(eq(userProfiles.id, id))
    .limit(1)
}
```

## AI Integration Standards

### LangChain Integration Patterns
```typescript
// Standardized AI tool interface
interface AITool {
  name: string
  description: string
  schema: z.ZodSchema
  execute: (input: any, context?: any) => Promise<any>
}

// Example tool implementation
export const codeGenerationTool: AITool = {
  name: "generate_code",
  description: "Generate code based on requirements",
  schema: z.object({
    language: z.string(),
    requirements: z.string(),
    context: z.string().optional()
  }),
  execute: async (input) => {
    const { language, requirements, context } = input
    // Implementation logic
    return generatedCode
  }
}

// Standardized agent configuration
export function createModuleAgent(moduleId: string, tools: AITool[]) {
  return new ChatOpenAI({
    modelName: "gpt-4",
    temperature: 0.1,
  }).bind({
    tools: tools.map(tool => ({
      type: "function",
      function: {
        name: tool.name,
        description: tool.description,
        parameters: zodToJsonSchema(tool.schema)
      }
    }))
  })
}
```

### Vercel AI SDK Patterns
```typescript
// Standardized streaming implementation
export async function streamAIResponse(
  messages: Message[],
  tools?: Tool[]
) {
  const result = await streamText({
    model: openai('gpt-4'),
    messages,
    tools,
    maxTokens: 1000,
  })

  return result.toDataStreamResponse()
}

// Error handling for AI operations
export async function safeAIOperation<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T | null> {
  try {
    return await operation()
  } catch (error) {
    console.error('AI operation failed:', error)
    return fallback ?? null
  }
}
```

## Testing Standards

### Unit Testing with Vitest
```typescript
// Test file naming: *.test.ts or *.spec.ts
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { MyComponent } from './MyComponent'

describe('MyComponent', () => {
  it('renders with required props', () => {
    render(<MyComponent title="Test Title">Content</MyComponent>)
    
    expect(screen.getByText('Test Title')).toBeInTheDocument()
    expect(screen.getByText('Content')).toBeInTheDocument()
  })
  
  it('calls onAction when button is clicked', async () => {
    const mockAction = vi.fn()
    render(
      <MyComponent title="Test" onAction={mockAction}>
        Content
      </MyComponent>
    )
    
    await user.click(screen.getByRole('button'))
    expect(mockAction).toHaveBeenCalledOnce()
  })
})
```

### E2E Testing with Playwright
```typescript
// e2e/module-integration.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Module Integration', () => {
  test('cross-module workflow works correctly', async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
    
    // Test agent inbox to vcode workflow
    await page.click('[data-testid="agent-inbox"]')
    await page.fill('[data-testid="message-input"]', 'Create a React component')
    await page.click('[data-testid="send-button"]')
    
    // Verify response and integration
    await expect(page.locator('[data-testid="ai-response"]')).toBeVisible()
    
    // Test transition to vcode module
    await page.click('[data-testid="open-in-vcode"]')
    await expect(page.locator('[data-testid="vcode-editor"]')).toBeVisible()
  })
})
```

## Performance Standards

### Bundle Size Optimization
```typescript
// Use dynamic imports for code splitting
const LazyComponent = lazy(() => import('./HeavyComponent'))

// Optimize bundle size with proper imports
import { Button } from '@radix-ui/react-button' // ✅ Specific import
import * as RadixUI from '@radix-ui/react-*'   // ❌ Avoid namespace imports

// Use React.memo for expensive components
export const ExpensiveComponent = memo(function ExpensiveComponent({ data }) {
  const processedData = useMemo(() => processData(data), [data])
  return <div>{processedData}</div>
})
```

### Performance Monitoring
```typescript
// Monitor performance metrics
export function trackPerformance(operation: string) {
  const start = performance.now()
  
  return {
    end: () => {
      const duration = performance.now() - start
      // Send metrics to monitoring service
      analytics.track('performance', {
        operation,
        duration,
        timestamp: Date.now()
      })
    }
  }
}

// Use in components
function MyComponent() {
  useEffect(() => {
    const metric = trackPerformance('component-mount')
    return metric.end
  }, [])
}
```

## Security Standards

### Input Validation
```typescript
// Use Zod for runtime validation
import { z } from 'zod'

const UserInputSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1).max(100),
  age: z.number().int().min(0).max(150)
})

export function validateUserInput(input: unknown) {
  return UserInputSchema.safeParse(input)
}

// Sanitize user input
import DOMPurify from 'isomorphic-dompurify'

export function sanitizeHTML(html: string): string {
  return DOMPurify.sanitize(html)
}
```

### Authentication & Authorization
```typescript
// Standardized auth utilities
export async function requireAuth(
  request: Request
): Promise<User | Response> {
  const token = request.headers.get('Authorization')?.replace('Bearer ', '')
  
  if (!token) {
    return new Response('Unauthorized', { status: 401 })
  }
  
  const user = await validateToken(token)
  if (!user) {
    return new Response('Invalid token', { status: 401 })
  }
  
  return user
}

// Permission checking
export function hasPermission(
  user: User,
  resource: string,
  action: string
): boolean {
  return user.permissions.some(p => 
    p.resource === resource && p.actions.includes(action)
  )
}
```

## Documentation Standards

### Code Documentation
```typescript
/**
 * Generates code based on natural language requirements
 * 
 * @param requirements - Natural language description of the code to generate
 * @param language - Programming language for the generated code
 * @param context - Optional context to improve code generation
 * @returns Promise resolving to generated code string
 * 
 * @example
 * ```typescript
 * const code = await generateCode(
 *   "Create a React component that displays user profile",
 *   "typescript",
 *   { framework: "react", ui: "tailwind" }
 * )
 * ```
 */
export async function generateCode(
  requirements: string,
  language: string,
  context?: Record<string, any>
): Promise<string> {
  // Implementation
}
```

### API Documentation
```typescript
// Use OpenAPI/Swagger for API documentation
/**
 * @openapi
 * /api/modules/{moduleId}/execute:
 *   post:
 *     summary: Execute module functionality
 *     parameters:
 *       - in: path
 *         name: moduleId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               action:
 *                 type: string
 *               parameters:
 *                 type: object
 *     responses:
 *       200:
 *         description: Successful execution
 */
```

## Git Standards

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

**Types**: feat, fix, docs, style, refactor, test, chore
**Scope**: module name or component area
**Examples**:
- `feat(agent-inbox): add multi-provider AI support`
- `fix(vcode): resolve Monaco editor integration issue`
- `docs(integration): update cross-module API documentation`

### Branch Naming
- `feature/module-name/feature-description`
- `fix/module-name/bug-description`
- `chore/module-name/task-description`

### Pull Request Standards
- **Title**: Clear, descriptive summary
- **Description**: What, why, and how
- **Testing**: How to test the changes
- **Documentation**: Updated docs if needed
- **Breaking Changes**: Clearly marked

## CI/CD Standards

### GitHub Actions Workflow
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  quality-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
      - run: bun install --frozen-lockfile
      - run: bun run type-check
      - run: bun run lint
      - run: bun run test
      - run: bun run build
      
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run security audit
        run: bun audit
```

---

*These development standards ensure consistency, quality, and maintainability across all modules in the unified AI assistant platform.*