# Unified AI Assistant Platform - Knowledge Index

## Quick Navigation

### 🎯 **Start Here**
- [Project Overview](./knowledge/project-overview.md) - Executive summary and strategic vision
- [PIB Master Knowledge](./agents/pib-master.md) - Master orchestrator guidance
- [Technical Architecture](./knowledge/technical-architecture.md) - System design and implementation

### 📋 **For Project Leaders**
- [PIB Master Agent](./agents/pib-master.md) - Strategic oversight and coordination
- [Product Manager Knowledge](./agents/pm.md) - Requirements and business focus
- [Development Process](./workflows/development-process.md) - End-to-end development workflow

### 🏗️ **For Technical Teams**
- [Architect Knowledge](./agents/architect.md) - System design and patterns
- [Developer Knowledge](./agents/dev.md) - Implementation guidelines  
- [DevOps Knowledge](./agents/devops.md) - Infrastructure and deployment
- [Development Standards](./knowledge/development-standards.md) - Code quality and conventions

### 🔗 **For Integration Work**
- [Module Integration](./knowledge/module-integration.md) - How 8 modules work together
- [Integration Patterns](./cross-cutting/integration-patterns.md) - Communication and data flow
- [Shared Components](./cross-cutting/shared-components.md) - Reusable UI and logic

### 📦 **Module-Specific Knowledge**
- [Agent Inbox](./modules/agent-inbox/overview.md) - Chat foundation module
- [FotoFun Studio](./modules/foto-fun/overview.md) - Visual design module
- [vCode IDE](./modules/vcode/overview.md) - Development environment
- [Open Canvas](./modules/open-canvas/overview.md) - Content creation
- [Gen-UI Computer Use](./modules/gen-ui-computer-use/overview.md) - Automation
- [Vibe Kanban](./modules/vibe-kanban/overview.md) - Orchestration
- [Langflow](./modules/langflow/overview.md) - Visual workflows
- [Social Media Agent](./modules/social-media-agent/overview.md) - Distribution

## Knowledge Organization

### 📚 **Core Knowledge** (`/knowledge/`)
Foundation documents that every team member should understand:

| Document | Purpose | Key Audience |
|----------|---------|--------------|
| [project-overview.md](./knowledge/project-overview.md) | Strategic vision and business case | All stakeholders |
| [technical-architecture.md](./knowledge/technical-architecture.md) | System design and technology decisions | Technical teams |
| [module-integration.md](./knowledge/module-integration.md) | How modules work together | Integration teams |
| [development-standards.md](./knowledge/development-standards.md) | Code quality and conventions | Developers |
| [decision-rationale.md](./knowledge/decision-rationale.md) | Why architectural choices were made | Architects, leads |

### 👥 **Agent Knowledge** (`/agents/`)
Tailored knowledge for each specialized agent role:

| Agent | Focus Area | Key Responsibilities |
|-------|------------|---------------------|
| [pib-master.md](./agents/pib-master.md) | Strategic orchestration | Overall coordination and quality |
| [architect.md](./agents/architect.md) | System design | Technical architecture and patterns |
| [dev.md](./agents/dev.md) | Implementation | Code development and testing |
| [pm.md](./agents/pm.md) | Product management | Requirements and user focus |
| [designer.md](./agents/designer.md) | User experience | UI/UX and design systems |
| [devops.md](./agents/devops.md) | Infrastructure | Deployment and operations |
| [qa.md](./agents/qa.md) | Quality assurance | Testing and validation |
| [analyst.md](./agents/analyst.md) | Business analysis | Requirements and metrics |

### 📦 **Module Knowledge** (`/modules/`)
Detailed information for each of the 8 modules:

#### Foundation Modules (Low Integration Complexity)
- **agent-inbox**: Next.js 14 → 15, LangChain integration, chat foundation
- **foto-fun**: Next.js 15, Fabric.js + PIXI.js, visual design studio
- **open-canvas**: Next.js 14, BlockNote, content creation hub

#### Standard Modules (Medium Integration Complexity)  
- **gen-ui-computer-use**: Next.js 15, LangGraph, automation engine
- **social-media-agent**: LangGraph service, social media management

#### Complex Modules (High Integration Complexity)
- **vcode**: Electron + React 19, Monaco editor, development environment
- **vibe-kanban**: Rust backend + React frontend, orchestration platform
- **langflow**: FastAPI + React, visual workflow builder

### 🔗 **Cross-Cutting Concerns** (`/cross-cutting/`)
Knowledge that spans multiple modules:

| Document | Content | Impact |
|----------|---------|--------|
| [integration-patterns.md](./cross-cutting/integration-patterns.md) | Communication protocols | All modules |
| [shared-components.md](./cross-cutting/shared-components.md) | Reusable UI and logic | Development efficiency |
| [data-flow.md](./cross-cutting/data-flow.md) | Data movement patterns | System consistency |
| [security-model.md](./cross-cutting/security-model.md) | Security architecture | Enterprise compliance |

### 🌐 **Context & Background** (`/context/`)
Supporting information for decision making:

| Document | Purpose | Usage |
|----------|---------|-------|
| [technology-choices.md](./context/technology-choices.md) | Technology evaluation criteria | Architecture decisions |
| [performance-requirements.md](./context/performance-requirements.md) | Performance targets and metrics | System optimization |
| [user-personas.md](./context/user-personas.md) | Target users and needs | Product decisions |
| [competitive-landscape.md](./context/competitive-landscape.md) | Market positioning | Strategic planning |

### 🔄 **Workflows & Processes** (`/workflows/`)
Operational procedures and methodologies:

| Document | Scope | Team Usage |
|----------|-------|------------|
| [development-process.md](./workflows/development-process.md) | End-to-end development | All development teams |
| [deployment-pipeline.md](./workflows/deployment-pipeline.md) | CI/CD and release | DevOps and release management |
| [testing-strategy.md](./workflows/testing-strategy.md) | Quality assurance approach | QA and development |
| [maintenance-procedures.md](./workflows/maintenance-procedures.md) | Ongoing operations | Operations and support |

## Knowledge Usage Patterns

### 🚀 **For New Team Members**
1. Start with [Project Overview](./knowledge/project-overview.md)
2. Read your [Agent-Specific Knowledge](./agents/)
3. Review [Development Standards](./knowledge/development-standards.md)
4. Study [Integration Patterns](./cross-cutting/integration-patterns.md)
5. Deep dive into relevant [Module Documentation](./modules/)

### 🔧 **For Development Work**
1. Check [Development Process](./workflows/development-process.md)
2. Review [Technical Architecture](./knowledge/technical-architecture.md)
3. Understand [Module Integration](./knowledge/module-integration.md)
4. Apply [LEVER Framework](./knowledge/development-standards.md#lever-framework)
5. Follow [Testing Strategy](./workflows/testing-strategy.md)

### 🎯 **For Strategic Decisions**
1. Reference [Decision Rationale](./knowledge/decision-rationale.md)
2. Review [Technology Choices](./context/technology-choices.md)
3. Consider [User Personas](./context/user-personas.md)
4. Analyze [Competitive Landscape](./context/competitive-landscape.md)
5. Align with [Project Overview](./knowledge/project-overview.md)

### 🔍 **For Troubleshooting**
1. Check [Integration Patterns](./cross-cutting/integration-patterns.md)
2. Review [Module-Specific Documentation](./modules/)
3. Examine [Data Flow Patterns](./cross-cutting/data-flow.md)
4. Consult [Security Model](./cross-cutting/security-model.md)
5. Follow [Maintenance Procedures](./workflows/maintenance-procedures.md)

## Knowledge Maintenance

### 📅 **Update Schedule**
- **Weekly**: Agent knowledge summaries based on progress
- **Sprint**: Module-specific updates after integration milestones
- **Monthly**: Cross-cutting concerns and process improvements
- **Quarterly**: Strategic vision and competitive landscape review

### ✅ **Quality Assurance**
- All knowledge documents validated against actual implementation
- Cross-references checked for accuracy and consistency
- Agent knowledge tested through practical application
- User feedback incorporated into content improvements

### 🔄 **Version Control**
- Knowledge versioned with main project repository
- Change history tracked through Git commits
- Major updates communicated to all agents
- Deprecation notices for outdated information

## Integration Success Metrics

### 📊 **Knowledge Effectiveness**
- **Agent Onboarding Time**: <2 hours to productive contribution
- **Decision Speed**: <1 day for technical architecture decisions
- **Knowledge Accuracy**: >95% alignment with implementation
- **Cross-Module Understanding**: >90% of agents understand integration patterns

### 🎯 **Project Success Indicators**
- **Technical Performance**: 99.9% uptime, <3s AI responses
- **User Adoption**: 100K MAU by Month 12, 80% cross-module usage
- **Business Impact**: $10M ARR by Month 18, 4.5+ star rating
- **Development Velocity**: 40% faster feature delivery

## Quick Reference

### 🔗 **Key Links**
- **Project Repository**: `/Users/<USER>/Projects/own/assistant/`
- **Documentation Root**: `/Users/<USER>/Projects/own/assistant/docs/`
- **Knowledge Base**: `/Users/<USER>/Projects/own/assistant/.ai/`
- **PIB Agent Config**: `/Users/<USER>/Projects/own/assistant/pib-agent/`

### 📞 **Contact & Support**
- **PIB Master**: Strategic oversight and coordination
- **Architect**: Technical architecture and design patterns
- **DevOps**: Infrastructure and deployment support
- **QA**: Testing strategy and quality assurance

### 🆘 **Emergency Procedures**
- **Integration Failures**: Contact Architect + PIB Master
- **Performance Issues**: Contact DevOps + Architect
- **User Impact**: Contact PIB Master + Product Manager
- **Security Concerns**: Contact DevOps + PIB Master

---

*This knowledge index serves as the central navigation system for all project information, ensuring that every team member and AI agent can quickly access the information they need to contribute effectively to the unified AI assistant platform.*