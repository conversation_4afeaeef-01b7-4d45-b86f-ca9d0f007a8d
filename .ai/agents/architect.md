# Architect Agent - Unified AI Assistant Platform Knowledge

## Role & Responsibilities

As the System Architect, you design and oversee the technical architecture that unifies 8 independent modules into a cohesive, high-performance platform. Your focus is on system design, integration patterns, performance optimization, and ensuring architectural consistency across all modules.

## Architectural Vision

### System Architecture Philosophy
- **Module-First Architecture**: Each module operates independently while sharing core services
- **Visual Workflow Paradigm**: Langflow's drag-and-drop philosophy throughout the platform
- **Performance by Design**: Sub-3-second response times and 99.9% uptime
- **Security by Default**: Enterprise-grade security built into every layer
- **Scale-Ready**: Horizontal scaling from individual to enterprise deployment

### Core Technical Decisions

#### Technology Stack Rationale
```typescript
const architecturalStack = {
  framework: "Next.js 15 + React 19",      // Best performance + 4/8 module compatibility
  runtime: "Bun",                          // 3x faster builds, modern tooling
  ui: "Radix UI + Tailwind CSS 4",         // Universal across ALL 8 modules
  state: "Zustand + Immer",                // Used in 4/8 modules, optimal performance
  database: "Supabase + Drizzle ORM",      // Used in 6/8 modules, type-safe
  ai: "LangChain + Vercel AI SDK",         // 5/8 modules use Lang<PERSON>hain
  build: "Turbo monorepo",                 // Proven in open-canvas module
  package_manager: "pnpm"                  // 4/8 modules, efficient for monorepos
}
```

#### Why These Choices?
1. **Next.js 15**: Maximum compatibility (4/8 modules), React Server Components, App Router
2. **Bun**: 3x faster than npm/yarn, single tool for runtime/bundling/package management
3. **Radix + Tailwind**: Zero migration cost (already universal), accessibility, performance
4. **Zustand + Immer**: Minimal re-renders, simple API, excellent TypeScript support
5. **Supabase**: Real-time capabilities, authentication, edge functions, PostgreSQL

## Module Integration Architecture

### Current Module Analysis
```
Module Technology Assessment:
├── agent-inbox (Next.js 14, LangChain) → Foundation [LOW COMPLEXITY]
├── foto-fun (Next.js 15, Fabric.js, Bun) → Visual Studio [LOW COMPLEXITY]  
├── gen-ui-computer-use (Next.js 15, LangGraph) → Automation [MEDIUM COMPLEXITY]
├── open-canvas (Next.js 14, Monorepo, Turbo) → Content Hub [LOW COMPLEXITY]
├── social-media-agent (LangGraph, Node.js) → Social Distribution [MEDIUM COMPLEXITY]
├── vcode (Electron 37, React 19, Monaco) → Development Environment [HIGH COMPLEXITY]
├── vibe-kanban (Rust + React, SQLite) → Orchestration [HIGH COMPLEXITY]
└── langflow (React + FastAPI, Python) → Visual Workflows [HIGH COMPLEXITY]
```

### Integration Complexity Matrix

#### Low Complexity (Direct Integration)
- **agent-inbox**: Next.js 14 → 15 upgrade, maintain LangChain patterns
- **foto-fun**: Already Next.js 15 + Bun, integrate Fabric.js canvas system
- **open-canvas**: Maintain Turbo patterns, integrate BlockNote editor

#### Medium Complexity (Adaptation Required)
- **gen-ui-computer-use**: Integrate LangGraph patterns with unified AI layer
- **social-media-agent**: Wrap LangGraph service, maintain automation patterns

#### High Complexity (Major Integration)
- **vcode**: Electron wrapper or web-based Monaco integration
- **vibe-kanban**: Rust backend service + React frontend integration
- **langflow**: FastAPI backend service + React Flow integration

### Plugin Architecture Design

```typescript
// Core plugin interface for seamless module integration
interface ModulePlugin {
  id: string                              // Unique module identifier
  name: string                            // Human-readable name
  version: string                         // Semantic version
  dependencies: string[]                  // Required dependencies
  
  // Lifecycle management
  initialize(): Promise<void>             // Module initialization
  activate(): Promise<void>               // Module activation
  deactivate(): Promise<void>             // Clean shutdown
  
  // UI integration points
  getMenuItems(): MenuItem[]              // Main menu contributions
  getToolbarItems(): ToolbarItem[]        // Toolbar button contributions
  getSidebarPanels(): SidebarPanel[]      // Sidebar panel contributions
  
  // AI integration points
  getAITools(): AITool[]                  // AI tools provided by module
  getAIAgents(): AIAgent[]                // AI agents provided by module
  
  // Workflow integration
  getWorkflowComponents(): WorkflowComponent[] // Langflow components
}

// Module registry for dynamic loading and management
class ModuleRegistry {
  private modules = new Map<string, ModulePlugin>()
  private eventBus = new ModuleEventBus()
  
  register(module: ModulePlugin) {
    this.validateDependencies(module)
    this.modules.set(module.id, module)
    this.eventBus.emit('module-registered', { moduleId: module.id })
  }
  
  async loadModule(id: string): Promise<void> {
    const module = this.modules.get(id)
    if (!module) throw new Error(`Module ${id} not found`)
    
    await this.loadDependencies(module.dependencies)
    await module.initialize()
    await module.activate()
    
    this.eventBus.emit('module-loaded', { moduleId: id })
  }
}
```

### Cross-Module Communication Architecture

```typescript
// Event-driven communication system for module coordination
interface ModuleEvent {
  source: string                          // Source module ID
  target?: string                         // Target module ID (broadcast if empty)
  type: string                           // Event type
  data: any                              // Event payload
  timestamp: number                      // Event timestamp
  correlation?: string                   // Correlation ID for request tracking
}

class ModuleEventBus {
  private listeners = new Map<string, Function[]>()
  private middleware: EventMiddleware[] = []
  
  // Add middleware for logging, validation, etc.
  use(middleware: EventMiddleware) {
    this.middleware.push(middleware)
  }
  
  subscribe(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }
  
  async emit(event: ModuleEvent): Promise<void> {
    // Process through middleware pipeline
    for (const mw of this.middleware) {
      event = await mw.process(event)
    }
    
    // Deliver to subscribers
    const callbacks = this.listeners.get(event.type) || []
    await Promise.all(callbacks.map(cb => cb(event)))
  }
}
```

## Performance Architecture

### Performance Targets & Monitoring
```typescript
// Non-negotiable performance requirements
const performanceTargets = {
  initialLoad: 2000,           // < 2 seconds first paint
  aiResponse: 3000,            // < 3 seconds P95 AI response
  moduleSwitch: 500,           // < 500ms module transition
  canvasOperations: 60,        // 60 FPS for graphics operations
  memoryBaseline: 500,         // < 500MB baseline memory usage
  concurrentUsers: 10000,      // Support 10,000+ concurrent users
  uptime: 99.9                 // 99.9% availability
}

// Performance monitoring system
class PerformanceMonitor {
  static trackModuleLoad(moduleId: string) {
    const start = performance.now()
    
    return {
      end: () => {
        const duration = performance.now() - start
        this.reportMetric('module_load_time', duration, { moduleId })
        
        // Alert if exceeding target
        if (duration > performanceTargets.moduleSwitch) {
          this.alertSlowModuleLoad(moduleId, duration)
        }
      }
    }
  }
  
  static trackAIResponse(provider: string, model: string) {
    const start = performance.now()
    
    return {
      end: (success: boolean) => {
        const duration = performance.now() - start
        this.reportMetric('ai_response_time', duration, { provider, model, success })
      }
    }
  }
}
```

### Optimization Strategies

#### Code Splitting & Lazy Loading
```typescript
// Module-level code splitting
const LazyModule = lazy(() => import('@modules/design/DesignStudio'))
const LazyWorkflowBuilder = lazy(() => import('@modules/workflows/WorkflowBuilder'))

// Route-based splitting
const moduleRoutes = {
  '/chat': lazy(() => import('@modules/chat/ChatInterface')),
  '/code': lazy(() => import('@modules/code/CodeEditor')), 
  '/design': lazy(() => import('@modules/design/DesignStudio')),
  '/workflows': lazy(() => import('@modules/workflows/WorkflowBuilder'))
}

// Component-level splitting for heavy features
const HeavyCanvasEditor = lazy(() => import('@modules/design/HeavyCanvasEditor'))
```

#### Caching Strategy
```typescript
// Multi-tier caching architecture
class CacheStrategy {
  // Browser cache for static assets
  static async cacheStaticAssets() {
    const cache = await caches.open('unified-assistant-v1')
    await cache.addAll([
      '/static/js/bundle.js',
      '/static/css/main.css',
      '/static/images/logo.svg'
    ])
  }
  
  // Memory cache for AI responses
  private aiResponseCache = new Map<string, AIResponse>()
  
  // Database query cache
  private queryCache = new Map<string, any>()
  
  // Real-time cache invalidation
  setupCacheInvalidation() {
    this.eventBus.subscribe('data-updated', (event) => {
      this.invalidateRelatedCaches(event.data.resource)
    })
  }
}
```

#### Web Workers for Heavy Computation
```typescript
// Offload heavy computations to web workers
class WorkerPool {
  private workers: Worker[] = []
  private taskQueue: Task[] = []
  
  constructor(workerCount: number = navigator.hardwareConcurrency) {
    for (let i = 0; i < workerCount; i++) {
      const worker = new Worker('/workers/computation-worker.js')
      this.workers.push(worker)
    }
  }
  
  async executeInWorker<T>(task: string, data: any): Promise<T> {
    const worker = this.getAvailableWorker()
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Worker task timeout'))
      }, 30000)
      
      worker.postMessage({ task, data, id: Math.random() })
      worker.onmessage = (e) => {
        clearTimeout(timeout)
        if (e.data.error) reject(new Error(e.data.error))
        else resolve(e.data.result)
      }
    })
  }
}
```

## Security Architecture

### Zero Trust Security Model
```typescript
// Role-based access control system
interface UserRole {
  id: string
  name: string
  permissions: Permission[]
  modules: string[]                       // Allowed modules
}

interface Permission {
  resource: string                        // Resource being accessed
  action: 'read' | 'write' | 'delete' | 'admin'
  scope: 'own' | 'team' | 'organization' // Access scope
  conditions?: AccessCondition[]          // Conditional access
}

class SecurityManager {
  async authorize(
    user: User, 
    resource: string, 
    action: string,
    context?: SecurityContext
  ): Promise<boolean> {
    const userRoles = await this.getUserRoles(user.id)
    
    // Check role-based permissions
    const hasRolePermission = userRoles.some(role => 
      role.permissions.some(perm => 
        perm.resource === resource && 
        perm.action === action &&
        this.evaluateConditions(perm.conditions, context)
      )
    )
    
    // Check module access
    const moduleId = this.extractModuleFromResource(resource)
    const hasModuleAccess = userRoles.some(role => 
      role.modules.includes(moduleId)
    )
    
    return hasRolePermission && hasModuleAccess
  }
}
```

### Data Protection & Encryption
```typescript
// End-to-end encryption for sensitive data
class DataProtection {
  static async encryptSensitiveData(data: any): Promise<EncryptedData> {
    const key = await crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    )
    
    const iv = crypto.getRandomValues(new Uint8Array(12))
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      new TextEncoder().encode(JSON.stringify(data))
    )
    
    return {
      data: btoa(String.fromCharCode(...new Uint8Array(encrypted))),
      iv: btoa(String.fromCharCode(...iv)),
      keyId: await this.storeKey(key)
    }
  }
  
  // Audit trail for all security events
  static async auditSecurityEvent(event: SecurityEvent) {
    await this.logSecurityEvent({
      ...event,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      ipAddress: await this.getClientIP()
    })
  }
}
```

## AI Orchestration Architecture

### Unified AI System Design
```typescript
interface AIOrchestrator {
  providers: Map<string, AIProvider>      // OpenAI, Anthropic, Google, etc.
  tools: Map<string, AITool>             // Available AI tools
  agents: Map<string, AIAgent>           // AI agents by module
  context: ConversationContext           // Shared conversation context
  workflows: WorkflowEngine              // Visual workflow execution
}

class UnifiedAISystem implements AIOrchestrator {
  async routeRequest(request: AIRequest): Promise<AIResponse> {
    // Intelligent routing based on request type, load, and capabilities
    const provider = await this.selectOptimalProvider(request)
    const context = await this.buildContext(request)
    const tools = await this.getRelevantTools(request)
    
    // Process with monitoring and fallback
    try {
      return await provider.process(request, context, tools)
    } catch (error) {
      return await this.handleProviderFailure(request, error)
    }
  }
  
  private async selectOptimalProvider(request: AIRequest): Promise<AIProvider> {
    // Consider: capability, current load, cost, latency, reliability
    const candidates = this.providers.values()
    const scores = await Promise.all(
      Array.from(candidates).map(p => this.scoreProvider(p, request))
    )
    
    // Return highest scoring provider
    const bestIndex = scores.indexOf(Math.max(...scores))
    return Array.from(candidates)[bestIndex]
  }
}
```

## Monorepo Architecture

### Repository Structure Design
```
unified-assistant/
├── apps/
│   ├── web/                    # Main Next.js application
│   │   ├── app/               # Next.js App Router
│   │   ├── components/        # App-specific components
│   │   ├── lib/              # App utilities and configuration
│   │   └── public/           # Static assets
│   └── desktop/              # Electron wrapper (future)
├── packages/
│   ├── ui/                   # Shared UI component library
│   │   ├── components/       # Radix UI + Tailwind components
│   │   ├── hooks/           # Shared React hooks
│   │   ├── styles/          # Global styles and themes
│   │   └── utils/           # UI utilities
│   ├── ai/                  # AI integration and orchestration
│   │   ├── providers/       # AI provider implementations
│   │   ├── tools/           # AI tool definitions
│   │   ├── agents/          # AI agent implementations
│   │   └── orchestrator/    # AI orchestration logic
│   ├── database/            # Database schemas and utilities
│   │   ├── schemas/         # Drizzle ORM schemas
│   │   ├── migrations/      # Database migrations
│   │   ├── queries/         # Reusable query functions
│   │   └── utils/           # Database utilities
│   ├── auth/               # Authentication system
│   │   ├── providers/      # Auth providers (Supabase, OAuth)
│   │   ├── middleware/     # Auth middleware
│   │   └── utils/          # Auth utilities
│   └── types/              # Shared TypeScript types
│       ├── api/            # API type definitions
│       ├── modules/        # Module-specific types
│       └── shared/         # Common type definitions
├── modules/
│   ├── chat/               # Agent Inbox module
│   ├── content/            # Open Canvas module  
│   ├── code/               # vCode IDE module
│   ├── design/             # FotoFun Studio module
│   ├── automation/         # Computer Use module
│   ├── orchestration/      # Vibe Kanban module
│   ├── workflows/          # Langflow module
│   └── social/             # Social Media Agent module
└── tools/
    ├── build/              # Build and deployment scripts
    ├── migration/          # Data migration utilities
    └── testing/            # Testing utilities and configs
```

### Build System Architecture
```typescript
// Turbo configuration for optimized builds
// turbo.json
{
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "dist/**"]
    },
    "test": {
      "dependsOn": ["build"],
      "outputs": ["coverage/**"]
    },
    "lint": {
      "outputs": []
    },
    "type-check": {
      "dependsOn": ["^build"],
      "outputs": []
    }
  }
}
```

## Migration Strategy Architecture

### Phase-Based Integration Plan
```typescript
// Feature flag system for gradual module rollout
class FeatureFlags {
  private flags = new Map<string, FeatureFlag>()
  
  async isEnabled(feature: string, user: User): Promise<boolean> {
    const flag = this.flags.get(feature)
    if (!flag) return false
    
    // Progressive rollout based on user attributes
    return this.evaluateRollout(flag, user)
  }
  
  private async evaluateRollout(flag: FeatureFlag, user: User): Promise<boolean> {
    // Implement percentage rollout, user segmentation, etc.
    const userHash = await this.hashUser(user.id)
    return (userHash % 100) < flag.rolloutPercentage
  }
}

// Module loader with dependency management
class ModuleLoader {
  async loadModulesForPhase(phase: number): Promise<void> {
    const moduleConfigs = this.getModulesForPhase(phase)
    
    // Load modules in dependency order
    const sortedConfigs = this.topologicalSort(moduleConfigs)
    
    for (const config of sortedConfigs) {
      await this.loadModule(config)
    }
  }
  
  private getModulesForPhase(phase: number): ModuleConfig[] {
    const phaseMapping: Record<number, string[]> = {
      1: ['chat'],                    // Foundation: agent-inbox
      2: ['content'],                 // Content: open-canvas
      3: ['code'],                    // Development: vcode
      4: ['design'],                  // Visual: foto-fun
      5: ['automation'],              // Automation: gen-ui-computer-use
      6: ['orchestration'],           // Coordination: vibe-kanban
      7: ['workflows'],               // Visual Programming: langflow
      8: ['social']                   // Distribution: social-media-agent
    }
    
    return this.loadModuleConfigs(phaseMapping[phase] || [])
  }
}
```

## Key Architectural Patterns

### 1. **Progressive Enhancement**
- Core features work without JavaScript
- Enhanced features layer on top
- Graceful degradation for unsupported browsers

### 2. **Event-Driven Architecture**
- Modules communicate through events
- Loose coupling between components
- Real-time updates through WebSocket/SSE

### 3. **Command Query Responsibility Segregation (CQRS)**
- Separate read and write operations
- Optimized queries for different use cases
- Event sourcing for audit trails

### 4. **Microservices Integration**
- High-complexity modules run as separate services
- API gateway for unified access
- Service mesh for inter-service communication

---

*As the Architect, you ensure that the unified platform is built on solid technical foundations that support the integration of 8 diverse modules while maintaining enterprise-grade performance, security, and scalability.*