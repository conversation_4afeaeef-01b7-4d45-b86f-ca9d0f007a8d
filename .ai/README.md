# Unified AI Assistant Platform - Knowledge Organization System

## Overview

This `.ai/` directory contains the comprehensive knowledge organization system for the unified AI assistant platform project. It serves as the central repository for all project knowledge, ensuring consistent understanding across all agent personas and development teams.

## Directory Structure

```
.ai/
├── README.md                         # This file - system overview
├── knowledge/                        # Core knowledge extraction
│   ├── project-overview.md          # Executive summary and vision
│   ├── technical-architecture.md    # System architecture knowledge
│   ├── module-integration.md        # How 8 modules work together
│   ├── development-standards.md     # Technical standards and practices
│   └── decision-rationale.md        # Why architectural choices were made
├── agents/                          # Agent-specific knowledge summaries
│   ├── pib-master.md               # PIB orchestrator knowledge
│   ├── architect.md                # Architecture-focused knowledge
│   ├── dev.md                      # Developer-focused knowledge
│   ├── pm.md                       # Product manager knowledge
│   ├── designer.md                 # Design and UX knowledge
│   ├── devops.md                   # DevOps and infrastructure
│   ├── qa.md                       # Quality assurance knowledge
│   └── analyst.md                  # Business analysis knowledge
├── modules/                         # Module-specific knowledge
│   ├── agent-inbox/                # Chat and communication module
│   ├── foto-fun/                   # Visual design studio module
│   ├── gen-ui-computer-use/        # Automation module
│   ├── open-canvas/                # Content creation module
│   ├── social-media-agent/         # Social media management
│   ├── vcode/                      # IDE and coding module
│   ├── vibe-kanban/                # Orchestration module
│   └── langflow/                   # Visual workflow module
├── cross-cutting/                   # Cross-module concerns
│   ├── integration-patterns.md     # How modules integrate
│   ├── shared-components.md        # Reusable components
│   ├── data-flow.md                # Data flow across modules
│   └── security-model.md           # Security architecture
├── context/                         # Contextual information
│   ├── technology-choices.md       # Why specific tech was chosen
│   ├── performance-requirements.md # Performance targets and metrics
│   ├── user-personas.md            # Target users and their needs
│   └── competitive-landscape.md    # Market positioning
└── workflows/                       # Development and operational workflows
    ├── development-process.md       # How development works
    ├── deployment-pipeline.md       # CI/CD and deployment
    ├── testing-strategy.md          # Quality assurance approach
    └── maintenance-procedures.md    # Ongoing maintenance
```

## Knowledge Organization Principles

### 1. **Hierarchical Structure**
- Project-level knowledge at the top
- Module-specific knowledge organized by functional area
- Cross-cutting concerns clearly identified

### 2. **Agent-Specific Views**
- Each agent persona gets a tailored knowledge summary
- Focused on their specific responsibilities and needs
- Links to detailed knowledge where appropriate

### 3. **Module Integration Focus**
- Emphasis on how 8 modules work together
- Clear integration patterns and dependencies
- Shared component identification

### 4. **Decision Context**
- Rationale for architectural and technical decisions
- Trade-offs and alternatives considered
- Future implications and flexibility

## Usage Guidelines

### For Agent Personas
1. Start with your agent-specific knowledge file (e.g., `agents/architect.md`)
2. Reference cross-cutting concerns relevant to your role
3. Dive into module-specific details as needed
4. Use decision rationale to understand context

### For Development Teams
1. Review `knowledge/project-overview.md` for high-level understanding
2. Study `knowledge/technical-architecture.md` for implementation details
3. Follow `workflows/development-process.md` for procedures
4. Reference module-specific knowledge for detailed implementation

### For Stakeholders
1. Start with `knowledge/project-overview.md`
2. Review `context/user-personas.md` and `context/competitive-landscape.md`
3. Understand decision rationale from `knowledge/decision-rationale.md`
4. Track progress through workflow documentation

## Maintenance

This knowledge system should be:
- **Updated regularly** as the project evolves
- **Validated** against actual implementation
- **Versioned** with the main project
- **Accessible** to all team members and agents

## Knowledge Sources

This knowledge is extracted and synthesized from:
- Project Brief and PRD documentation
- Technical architecture specifications
- Module-specific documentation
- Implementation analysis of 8 existing modules
- Team decisions and architectural discussions

---

*Last Updated: 2025-07-14*
*Knowledge Version: 1.0*
*Project Phase: Knowledge Organization Setup*