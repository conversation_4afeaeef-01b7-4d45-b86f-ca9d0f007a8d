# Agent Inbox Module - Overview & Integration Knowledge

## Module Purpose & Role

Agent Inbox serves as the **communication foundation** for the unified AI assistant platform. It provides the central chat interface through which users interact with multiple AI agents and coordinate cross-module workflows.

### Core Functionality
- Multi-provider AI chat interface (OpenAI, Anthropic, Google, etc.)
- Agent conversation management and context preservation
- Real-time collaboration and team messaging
- Message threading and conversation organization
- File sharing and multimedia support

### Integration Role in Unified Platform
- **Central Communication Hub**: All modules send/receive messages through agent inbox
- **Context Sharing Engine**: Maintains conversation context across module switches
- **AI Provider Gateway**: Manages connections to multiple AI providers for all modules
- **Notification Center**: Receives status updates and alerts from other modules

## Current Technology Stack

### Framework & Infrastructure
```typescript
const agentInboxStack = {
  framework: "Next.js 14",                    // App Router, React 18
  runtime: "Node.js",                         // Standard runtime (migrate to Bun)
  ui: "Radix UI + Tailwind CSS",              // Consistent with platform
  package_manager: "Yarn",                    // Migrate to pnpm
  database: "Supabase",                       // Already platform-standard
  
  // AI Integration
  ai_primary: "Lang<PERSON>hain",                    // @langchain/anthropic, @langchain/openai
  ai_framework: "@langchain/langgraph",       // Workflow orchestration
  ai_community: "@langchain/community",       // Extended integrations
  ai_google: "@langchain/google-genai",       // Google AI integration
  
  // UI Components
  chat_interface: "@assistant-ui/react",      // Specialized chat components
  rich_text: "@blocknote/core",               // Rich text editing
  code_editor: "CodeMirror",                  // Multi-language code support
  animations: "Framer Motion",                // UI animations
  
  // Additional Features
  caching: "@vercel/kv",                      // Redis-compatible caching
  syntax_highlighting: "React Syntax Highlighter",
  markdown: "React Markdown + KaTeX",         // Math support
}
```

### Key Dependencies Analysis
```json
{
  "next": "14.2.5",                    // ✅ Compatible with platform Next.js 15
  "@langchain/anthropic": "^0.1.21",  // ✅ Core AI provider
  "@langchain/openai": "^0.0.34",     // ✅ Core AI provider  
  "@langchain/langgraph": "^0.0.26",  // ✅ Workflow integration
  "@supabase/supabase-js": "^2.39.3", // ✅ Platform database
  "@radix-ui/react-*": "latest",      // ✅ Platform UI components
  "tailwindcss": "^3.4.1"             // ✅ Platform CSS framework (upgrade to v4)
}
```

## Integration Architecture

### Migration Plan to Unified Platform

#### Phase 1: Foundation Integration (Month 1-3)
```typescript
// Migration tasks for agent-inbox module
const migrationTasks = {
  framework: {
    current: "Next.js 14",
    target: "Next.js 15",
    effort: "LOW",
    changes: ["App Router already implemented", "React 18 → 19 upgrade"]
  },
  
  package_manager: {
    current: "Yarn",
    target: "pnpm",
    effort: "LOW", 
    changes: ["Update package.json", "Update CI/CD", "Lock file migration"]
  },
  
  ai_integration: {
    current: "LangChain direct",
    target: "Unified AI Orchestrator",
    effort: "MEDIUM",
    changes: ["Wrap existing LangChain", "Add provider abstraction", "Event integration"]
  },
  
  state_management: {
    current: "React built-in",
    target: "Zustand + Immer",
    effort: "MEDIUM",
    changes: ["Create chat store", "Migrate useState calls", "Add persistence"]
  }
}
```

#### Integration APIs
```typescript
// Agent Inbox integration interface for unified platform
interface AgentInboxIntegration {
  // Message handling
  sendMessage(
    moduleId: string, 
    message: string, 
    context?: ConversationContext
  ): Promise<void>
  
  receiveMessage(
    callback: (message: ModuleMessage) => void
  ): void
  
  // Context management
  shareContext(
    moduleId: string, 
    context: ConversationContext
  ): Promise<void>
  
  getSharedContext(
    moduleId: string
  ): Promise<ConversationContext | null>
  
  // AI provider management
  routeAIRequest(
    request: AIRequest,
    preferredProvider?: string
  ): Promise<AIResponse>
  
  // Notification handling
  sendNotification(
    type: NotificationType,
    content: string,
    targetModules?: string[]
  ): Promise<void>
}

// Implementation example
class AgentInboxModule implements AgentInboxIntegration {
  constructor(
    private eventBus: ModuleEventBus,
    private aiOrchestrator: AIOrchestrator,
    private contextStore: ConversationStore
  ) {}
  
  async sendMessage(moduleId: string, message: string, context?: ConversationContext) {
    // Process message through AI if needed
    const response = await this.aiOrchestrator.processMessage(message, context)
    
    // Emit event for target module
    this.eventBus.emit({
      source: 'agent-inbox',
      target: moduleId,
      type: 'message-sent',
      data: { message, response, context },
      timestamp: Date.now()
    })
  }
  
  receiveMessage(callback: (message: ModuleMessage) => void) {
    this.eventBus.subscribe('message-received', callback)
  }
}
```

### Cross-Module Integration Points

#### 1. Open Canvas Integration
```typescript
// Content creation workflow
const contentCreationFlow = async () => {
  // User starts in agent-inbox
  const chatResponse = await agentInbox.sendMessage(
    'open-canvas',
    'Create a technical blog post about AI integration',
    { topic: 'AI', format: 'blog', audience: 'developers' }
  )
  
  // Context automatically shared with open-canvas
  const document = await openCanvas.createDocument(
    'blog-post',
    chatResponse.context
  )
  
  // Collaboration notifications back to agent-inbox
  agentInbox.receiveMessage((message) => {
    if (message.type === 'document-ready') {
      // Show document preview in chat
    }
  })
}
```

#### 2. vCode Integration
```typescript
// Code generation workflow
const codeGenerationFlow = async () => {
  // AI coding assistance request
  const codeRequest = await agentInbox.sendMessage(
    'vcode',
    'Generate a React component for user authentication',
    { language: 'typescript', framework: 'react', ui: 'radix' }
  )
  
  // vCode generates code and sends back
  vcode.generateCode(codeRequest.context).then(result => {
    agentInbox.receiveMessage({
      source: 'vcode',
      type: 'code-generated',
      data: { code: result.code, preview: result.preview }
    })
  })
}
```

#### 3. FotoFun Integration
```typescript
// Visual asset creation workflow
const visualAssetFlow = async () => {
  // Request visual content
  const designRequest = await agentInbox.sendMessage(
    'foto-fun',
    'Create a hero image for our AI platform',
    { style: 'modern', colors: ['blue', 'purple'], size: '1920x1080' }
  )
  
  // FotoFun AI generates image
  const image = await fotoFun.generateImage(designRequest.context)
  
  // Send result back to agent-inbox
  agentInbox.receiveMessage({
    source: 'foto-fun',
    type: 'image-generated',
    data: { imageUrl: image.url, metadata: image.metadata }
  })
}
```

## Data Models & State

### Conversation Management
```typescript
interface ConversationContext {
  id: string
  userId: string
  participants: Participant[]
  messages: Message[]
  metadata: ConversationMetadata
  moduleContext: Record<string, any>    // Context from other modules
  aiProviders: AIProviderUsage[]        // Provider usage history
  createdAt: Date
  updatedAt: Date
}

interface Message {
  id: string
  conversationId: string
  senderId: string
  senderType: 'user' | 'ai' | 'module'
  content: MessageContent
  parentMessageId?: string              // For threading
  reactions: MessageReaction[]
  attachments: Attachment[]
  moduleSource?: string                 // Source module if applicable
  aiProvider?: string                   // AI provider used
  timestamp: Date
}

interface MessageContent {
  text?: string
  markdown?: string
  code?: CodeBlock[]
  images?: string[]
  files?: FileReference[]
  embeds?: EmbedData[]
}
```

### AI Provider State
```typescript
interface AIProviderState {
  activeProviders: AIProvider[]
  defaultProvider: string
  fallbackChain: string[]               // Fallback order
  usage: ProviderUsageStats
  rateLimits: RateLimitStatus[]
  errors: ProviderError[]
}

interface AIProvider {
  id: string
  name: string
  apiKey: string                        // Encrypted
  models: AIModel[]
  capabilities: AICapability[]
  status: 'active' | 'inactive' | 'error'
  lastUsed: Date
}
```

### Module Integration State
```typescript
interface ModuleIntegrationState {
  connectedModules: ConnectedModule[]
  activeWorkflows: CrossModuleWorkflow[]
  sharedContexts: SharedContext[]
  notifications: ModuleNotification[]
}

interface ConnectedModule {
  moduleId: string
  status: 'connected' | 'disconnected' | 'error'
  capabilities: ModuleCapability[]
  lastCommunication: Date
  messageQueue: QueuedMessage[]
}
```

## User Interface Components

### Chat Interface Enhancement
```typescript
// Enhanced chat interface for unified platform
interface ChatInterfaceProps {
  conversationId: string
  moduleIntegrations: ModuleIntegration[]
  aiProviders: AIProvider[]
  onModuleAction: (moduleId: string, action: string) => void
  onProviderSwitch: (providerId: string) => void
}

export function EnhancedChatInterface({ 
  conversationId, 
  moduleIntegrations,
  aiProviders,
  onModuleAction,
  onProviderSwitch 
}: ChatInterfaceProps) {
  return (
    <div className="flex h-full">
      {/* Main chat area */}
      <div className="flex-1 flex flex-col">
        <ChatHeader 
          aiProviders={aiProviders}
          onProviderSwitch={onProviderSwitch}
        />
        <MessageList 
          conversationId={conversationId}
          onModuleAction={onModuleAction}
        />
        <MessageInput 
          onSend={handleMessageSend}
          moduleIntegrations={moduleIntegrations}
        />
      </div>
      
      {/* Module integration sidebar */}
      <ModuleIntegrationSidebar 
        modules={moduleIntegrations}
        onModuleSelect={onModuleAction}
      />
    </div>
  )
}
```

### Module Action Components
```typescript
// Action buttons for module integrations
export function ModuleActionButtons({ message, onAction }: {
  message: Message
  onAction: (moduleId: string, action: string) => void
}) {
  const availableActions = getAvailableActionsForMessage(message)
  
  return (
    <div className="flex gap-2 mt-2">
      {availableActions.map(action => (
        <Button
          key={action.id}
          variant="outline"
          size="sm"
          onClick={() => onAction(action.moduleId, action.action)}
        >
          <action.icon className="w-4 h-4 mr-2" />
          {action.label}
        </Button>
      ))}
    </div>
  )
}

// Example: Code message with vCode integration
function getAvailableActionsForMessage(message: Message): ModuleAction[] {
  const actions: ModuleAction[] = []
  
  if (message.content.code?.length > 0) {
    actions.push({
      id: 'open-in-vcode',
      moduleId: 'vcode',
      action: 'open-code',
      label: 'Open in vCode',
      icon: CodeIcon
    })
  }
  
  if (message.content.images?.length > 0) {
    actions.push({
      id: 'edit-in-fotofun',
      moduleId: 'foto-fun',
      action: 'edit-image',
      label: 'Edit in FotoFun',
      icon: ImageIcon
    })
  }
  
  return actions
}
```

## Performance Considerations

### Chat Performance Optimization
```typescript
// Virtual scrolling for large conversations
import { FixedSizeList as List } from 'react-window'

export function VirtualizedMessageList({ messages }: { messages: Message[] }) {
  const Row = ({ index, style }: { index: number, style: React.CSSProperties }) => (
    <div style={style}>
      <MessageComponent message={messages[index]} />
    </div>
  )
  
  return (
    <List
      height={600}
      itemCount={messages.length}
      itemSize={100}
      overscanCount={5}
    >
      {Row}
    </List>
  )
}

// Message caching and lazy loading
class MessageCache {
  private cache = new Map<string, Message[]>()
  private loadingStates = new Map<string, boolean>()
  
  async getMessages(conversationId: string, offset = 0, limit = 50): Promise<Message[]> {
    const cacheKey = `${conversationId}:${offset}:${limit}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }
    
    if (this.loadingStates.get(cacheKey)) {
      // Return cached partial data while loading
      return this.cache.get(conversationId) || []
    }
    
    this.loadingStates.set(cacheKey, true)
    const messages = await this.fetchMessages(conversationId, offset, limit)
    this.cache.set(cacheKey, messages)
    this.loadingStates.set(cacheKey, false)
    
    return messages
  }
}
```

### Real-time Performance
```typescript
// Optimized real-time updates
export function useRealtimeMessages(conversationId: string) {
  const [messages, setMessages] = useState<Message[]>([])
  
  useEffect(() => {
    const channel = supabase
      .channel(`conversation:${conversationId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: `conversation_id=eq.${conversationId}`
      }, (payload) => {
        // Optimistic update
        setMessages(prev => [...prev, payload.new as Message])
      })
      .subscribe()
    
    return () => {
      supabase.removeChannel(channel)
    }
  }, [conversationId])
  
  return messages
}
```

## Testing Strategy

### Integration Testing
```typescript
// Test cross-module communication
describe('Agent Inbox Module Integration', () => {
  test('should route messages to vcode module', async () => {
    const agentInbox = new AgentInboxModule(eventBus, aiOrchestrator, contextStore)
    const mockVCodeModule = jest.fn()
    
    eventBus.subscribe('message-sent', mockVCodeModule)
    
    await agentInbox.sendMessage(
      'vcode',
      'Generate a React component',
      { language: 'typescript' }
    )
    
    expect(mockVCodeModule).toHaveBeenCalledWith({
      source: 'agent-inbox',
      target: 'vcode',
      type: 'message-sent',
      data: expect.objectContaining({
        message: 'Generate a React component'
      })
    })
  })
  
  test('should maintain context across module switches', async () => {
    const context = { topic: 'AI', format: 'blog' }
    
    await agentInbox.shareContext('open-canvas', context)
    const retrievedContext = await agentInbox.getSharedContext('open-canvas')
    
    expect(retrievedContext).toEqual(context)
  })
})
```

---

*Agent Inbox serves as the communication backbone of the unified platform, enabling seamless coordination between all 8 modules while providing a familiar chat interface for users.*