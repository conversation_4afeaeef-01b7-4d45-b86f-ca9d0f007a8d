"use client";

import React from 'react';
import { Users, Plus, Settings, Activity, MessageSquare, Brain, Network } from 'lucide-react';

export default function OrchestrationPage() {
  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-red-50 to-pink-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white/80 backdrop-blur-sm border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <Users className="w-6 h-6 text-red-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Agent Orchestration</h1>
            <p className="text-sm text-gray-600">Multi-agent coordination and task management</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
            <Plus className="w-4 h-4" />
            New Agent
          </button>
          <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Settings className="w-4 h-4" />
            Configure
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Agent Panel */}
        <div className="w-80 bg-white/80 backdrop-blur-sm border-r p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Active Agents</h3>
          
          <div className="space-y-3">
            {[
              { name: 'Research Agent', status: 'active', tasks: 3, color: 'bg-blue-100 text-blue-600' },
              { name: 'Writing Agent', status: 'idle', tasks: 0, color: 'bg-green-100 text-green-600' },
              { name: 'Analysis Agent', status: 'busy', tasks: 1, color: 'bg-yellow-100 text-yellow-600' },
            ].map((agent, idx) => (
              <div key={idx} className="p-3 border rounded-lg hover:bg-red-50 cursor-pointer transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium text-sm">{agent.name}</div>
                  <div className={`px-2 py-1 rounded-full text-xs ${agent.color}`}>
                    {agent.status}
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {agent.tasks} active tasks
                </div>
              </div>
            ))}
          </div>

          <button className="w-full mt-4 p-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-red-300 hover:bg-red-50 transition-colors text-sm text-gray-600">
            + Add New Agent
          </button>
        </div>

        {/* Main Orchestration Area */}
        <div className="flex-1 flex flex-col">
          {/* Control Bar */}
          <div className="flex items-center gap-4 p-4 bg-white/80 backdrop-blur-sm border-b">
            <div className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-red-600" />
              <span className="font-medium">Orchestration Dashboard</span>
            </div>
            <div className="flex-1" />
            <div className="text-sm text-gray-600">3 agents • 4 tasks • 2 workflows</div>
          </div>

          {/* Dashboard Content */}
          <div className="flex-1 p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Task Queue */}
              <div className="bg-white/80 backdrop-blur-sm rounded-lg border p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Task Queue</h3>
                <div className="space-y-3">
                  {[
                    { task: 'Research market trends', agent: 'Research Agent', priority: 'high' },
                    { task: 'Generate content outline', agent: 'Writing Agent', priority: 'medium' },
                    { task: 'Analyze user feedback', agent: 'Analysis Agent', priority: 'low' },
                  ].map((item, idx) => (
                    <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{item.task}</div>
                        <div className="text-xs text-gray-500">{item.agent}</div>
                      </div>
                      <div className={`px-2 py-1 rounded text-xs ${
                        item.priority === 'high' ? 'bg-red-100 text-red-600' :
                        item.priority === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        {item.priority}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Agent Network */}
              <div className="bg-white/80 backdrop-blur-sm rounded-lg border p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Agent Network</h3>
                <div className="flex items-center justify-center h-40">
                  <div className="text-center">
                    <Network className="w-12 h-12 text-red-600 mx-auto mb-2" />
                    <div className="text-sm text-gray-600">Agent network visualization</div>
                    <div className="text-xs text-gray-500">3 connected agents</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Communication Hub */}
            <div className="bg-white/80 backdrop-blur-sm rounded-lg border p-6">
              <div className="flex items-center gap-2 mb-4">
                <MessageSquare className="w-5 h-5 text-red-600" />
                <h3 className="font-semibold text-gray-900">Inter-Agent Communication</h3>
              </div>
              <div className="space-y-3">
                <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Brain className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Research Agent → Writing Agent</div>
                    <div className="text-sm text-gray-600">Shared market research data for content creation</div>
                    <div className="text-xs text-gray-500">2 minutes ago</div>
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Brain className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Analysis Agent → Research Agent</div>
                    <div className="text-sm text-gray-600">Requested additional data points for analysis</div>
                    <div className="text-xs text-gray-500">5 minutes ago</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Monitoring Panel */}
        <div className="w-64 bg-white/80 backdrop-blur-sm border-l p-4">
          <h3 className="font-semibold text-gray-900 mb-4">System Monitor</h3>
          
          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Performance</div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Task Completion</span>
                  <span className="text-green-600">94%</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Response Time</span>
                  <span className="text-blue-600">1.2s</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Success Rate</span>
                  <span className="text-green-600">98%</span>
                </div>
              </div>
            </div>

            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Resource Usage</div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>CPU</span>
                  <span className="text-yellow-600">45%</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Memory</span>
                  <span className="text-blue-600">3.2GB</span>
                </div>
              </div>
            </div>

            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Alerts</div>
              <div className="text-sm text-gray-500">No active alerts</div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-6 py-2 bg-white/80 backdrop-blur-sm border-t text-sm text-gray-600">
        <div>Orchestration System v1.5</div>
        <div className="flex items-center gap-4">
          <div>Agents: 3 active</div>
          <div>Tasks: 4 queued</div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Connected to Vibe Kanban</span>
          </div>
        </div>
      </div>
    </div>
  );
}
