"use client";

import React from 'react';
import { Zap, Play, Pause, Setting<PERSON>, <PERSON>, Mouse, Keyboard, Eye } from 'lucide-react';

export default function AutomationPage() {
  const [isRecording, setIsRecording] = React.useState(false);
  const [isRunning, setIsRunning] = React.useState(false);

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-yellow-50 to-orange-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white/80 backdrop-blur-sm border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-yellow-100 rounded-lg">
            <Zap className="w-6 h-6 text-yellow-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Automation Engine</h1>
            <p className="text-sm text-gray-600">Computer use automation and task recording</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button 
            onClick={() => setIsRecording(!isRecording)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
              isRecording 
                ? 'bg-red-600 text-white hover:bg-red-700' 
                : 'bg-yellow-600 text-white hover:bg-yellow-700'
            }`}
          >
            <Eye className="w-4 h-4" />
            {isRecording ? 'Stop Recording' : 'Start Recording'}
          </button>
          <button 
            onClick={() => setIsRunning(!isRunning)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
              isRunning 
                ? 'bg-red-600 text-white hover:bg-red-700' 
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            {isRunning ? 'Stop' : 'Run'}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Control Panel */}
        <div className="w-80 bg-white/80 backdrop-blur-sm border-r p-6">
          <h3 className="font-semibold text-gray-900 mb-4">Automation Controls</h3>
          
          {/* Recording Status */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <div className={`w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`}></div>
              <span className="font-medium">{isRecording ? 'Recording' : 'Idle'}</span>
            </div>
            <div className="text-sm text-gray-600">
              {isRecording ? 'Capturing screen actions...' : 'Ready to record automation'}
            </div>
          </div>

          {/* Action Types */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Available Actions</h4>
            <div className="space-y-2">
              {[
                { icon: Mouse, name: 'Click', desc: 'Mouse clicks and drags' },
                { icon: Keyboard, name: 'Type', desc: 'Keyboard input' },
                { icon: Eye, name: 'Wait', desc: 'Wait for elements' },
                { icon: Monitor, name: 'Screenshot', desc: 'Capture screen' },
              ].map((action, idx) => (
                <div key={idx} className="flex items-center gap-3 p-3 rounded-lg border hover:bg-yellow-50 cursor-pointer transition-colors">
                  <action.icon className="w-5 h-5 text-yellow-600" />
                  <div>
                    <div className="font-medium text-sm">{action.name}</div>
                    <div className="text-xs text-gray-500">{action.desc}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Settings */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Settings</h4>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Delay (ms)</label>
                <input type="number" className="w-full px-3 py-2 border rounded-lg" defaultValue="1000" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Timeout (s)</label>
                <input type="number" className="w-full px-3 py-2 border rounded-lg" defaultValue="30" />
              </div>
              <div className="flex items-center gap-2">
                <input type="checkbox" id="screenshot" className="rounded" />
                <label htmlFor="screenshot" className="text-sm text-gray-700">Take screenshots</label>
              </div>
            </div>
          </div>
        </div>

        {/* Main Workspace */}
        <div className="flex-1 flex flex-col">
          {/* Toolbar */}
          <div className="flex items-center gap-4 p-4 bg-white/80 backdrop-blur-sm border-b">
            <button className="flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
              <Settings className="w-4 h-4" />
              Configure
            </button>
            <div className="flex-1" />
            <div className="text-sm text-gray-600">
              Status: {isRunning ? 'Running automation...' : 'Ready'}
            </div>
          </div>

          {/* Automation Sequence */}
          <div className="flex-1 p-6">
            <div className="bg-white rounded-lg shadow-sm border h-full">
              <div className="p-4 border-b">
                <h3 className="font-semibold text-gray-900">Automation Sequence</h3>
                <p className="text-sm text-gray-600">Recorded actions will appear here</p>
              </div>
              
              <div className="p-4 space-y-3">
                {[
                  { id: 1, type: 'click', target: 'Button "Submit"', x: 450, y: 300, status: 'completed' },
                  { id: 2, type: 'type', target: 'Input field', text: 'Hello World', status: 'completed' },
                  { id: 3, type: 'wait', target: 'Element ".loading"', timeout: 5000, status: 'running' },
                  { id: 4, type: 'screenshot', target: 'Full screen', status: 'pending' },
                ].map((action) => (
                  <div key={action.id} className={`flex items-center gap-4 p-4 rounded-lg border ${
                    action.status === 'completed' ? 'bg-green-50 border-green-200' :
                    action.status === 'running' ? 'bg-yellow-50 border-yellow-200' :
                    'bg-gray-50 border-gray-200'
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      action.status === 'completed' ? 'bg-green-500 text-white' :
                      action.status === 'running' ? 'bg-yellow-500 text-white' :
                      'bg-gray-400 text-white'
                    }`}>
                      {action.id}
                    </div>
                    
                    <div className="flex-1">
                      <div className="font-medium text-sm capitalize">{action.type}</div>
                      <div className="text-sm text-gray-600">{action.target}</div>
                      {action.text && <div className="text-xs text-gray-500">Text: "{action.text}"</div>}
                      {action.x && action.y && <div className="text-xs text-gray-500">Position: ({action.x}, {action.y})</div>}
                      {action.timeout && <div className="text-xs text-gray-500">Timeout: {action.timeout}ms</div>}
                    </div>
                    
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      action.status === 'completed' ? 'bg-green-100 text-green-800' :
                      action.status === 'running' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {action.status}
                    </div>
                  </div>
                ))}
                
                {isRecording && (
                  <div className="flex items-center gap-4 p-4 rounded-lg border border-dashed border-yellow-300 bg-yellow-50">
                    <div className="w-8 h-8 rounded-full bg-yellow-500 flex items-center justify-center">
                      <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm text-yellow-800">Recording...</div>
                      <div className="text-sm text-yellow-600">Perform actions to record them</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Screen Preview */}
        <div className="w-96 bg-white/80 backdrop-blur-sm border-l p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Screen Preview</h3>
          <div className="aspect-video bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center mb-4">
            <div className="text-center">
              <Monitor className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <div className="text-sm text-gray-600">Screen capture will appear here</div>
            </div>
          </div>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Target Application</label>
              <select className="w-full px-3 py-2 border rounded-lg">
                <option>Chrome Browser</option>
                <option>VS Code</option>
                <option>Terminal</option>
                <option>Any Application</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Resolution</label>
              <select className="w-full px-3 py-2 border rounded-lg">
                <option>1920x1080</option>
                <option>1366x768</option>
                <option>1280x720</option>
                <option>Auto Detect</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-6 py-2 bg-white/80 backdrop-blur-sm border-t text-sm text-gray-600">
        <div>Actions recorded: 4</div>
        <div className="flex items-center gap-4">
          <div>Last action: 2s ago</div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Connected to Computer Use Engine</span>
          </div>
        </div>
      </div>
    </div>
  );
}
