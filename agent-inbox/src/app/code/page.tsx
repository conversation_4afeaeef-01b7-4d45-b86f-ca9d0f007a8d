"use client";

import React from 'react';
import { Code, Terminal, FileText, Play, Save, GitBranch, Zap } from 'lucide-react';

export default function CodeEditorPage() {
  return (
    <div className="flex flex-col h-full bg-gray-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <Code className="w-6 h-6 text-green-600" />
          </div>
          <div>
            <h1 className="text-xl font-bold">Development Environment</h1>
            <p className="text-sm text-gray-400">AI-assisted code editor with terminal</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Zap className="w-4 h-4" />
            AI Assist
          </button>
          <button className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Play className="w-4 h-4" />
            Run
          </button>
          <button className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            <Save className="w-4 h-4" />
            Save
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* File Explorer */}
        <div className="w-64 bg-gray-800 border-r border-gray-700 p-4">
          <div className="flex items-center gap-2 mb-4">
            <FileText className="w-5 h-5 text-gray-400" />
            <h3 className="font-semibold">Explorer</h3>
          </div>
          <div className="space-y-1">
            {[
              { name: 'src/', type: 'folder', expanded: true },
              { name: '  components/', type: 'folder', expanded: true },
              { name: '    Button.tsx', type: 'file', active: true },
              { name: '    Input.tsx', type: 'file' },
              { name: '  pages/', type: 'folder' },
              { name: '    index.tsx', type: 'file' },
              { name: 'package.json', type: 'file' },
              { name: 'README.md', type: 'file' },
            ].map((item, idx) => (
              <div 
                key={idx} 
                className={`text-sm py-1 px-2 rounded cursor-pointer hover:bg-gray-700 ${
                  item.active ? 'bg-gray-700 text-white' : 'text-gray-300'
                }`}
              >
                {item.name}
              </div>
            ))}
          </div>
        </div>

        {/* Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Editor Tabs */}
          <div className="flex items-center bg-gray-800 border-b border-gray-700">
            <div className="flex items-center gap-2 px-4 py-2 bg-gray-700 border-r border-gray-600">
              <Code className="w-4 h-4 text-blue-400" />
              <span className="text-sm">Button.tsx</span>
              <button className="text-gray-400 hover:text-white">×</button>
            </div>
          </div>

          {/* Code Editor */}
          <div className="flex-1 bg-gray-900 p-4 font-mono text-sm">
            <div className="space-y-1">
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">1</span>
                <span className="text-purple-400">import</span>
                <span className="text-white"> React </span>
                <span className="text-purple-400">from</span>
                <span className="text-green-400"> 'react'</span>
                <span className="text-white">;</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">2</span>
                <span className="text-purple-400">import</span>
                <span className="text-white"> { cn } </span>
                <span className="text-purple-400">from</span>
                <span className="text-green-400"> '@/lib/utils'</span>
                <span className="text-white">;</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">3</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">4</span>
                <span className="text-purple-400">interface</span>
                <span className="text-yellow-400"> ButtonProps </span>
                <span className="text-white">{</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">5</span>
                <span className="text-white">  children: React.ReactNode;</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">6</span>
                <span className="text-white">  className?: </span>
                <span className="text-blue-400">string</span>
                <span className="text-white">;</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">7</span>
                <span className="text-white">  onClick?: () => </span>
                <span className="text-blue-400">void</span>
                <span className="text-white">;</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">8</span>
                <span className="text-white">}</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">9</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">10</span>
                <span className="text-purple-400">export</span>
                <span className="text-purple-400"> function</span>
                <span className="text-yellow-400"> Button</span>
                <span className="text-white">({ children, className, onClick }: ButtonProps) {</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">11</span>
                <span className="text-white">  </span>
                <span className="text-purple-400">return</span>
                <span className="text-white"> (</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">12</span>
                <span className="text-white">    &lt;</span>
                <span className="text-red-400">button</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">13</span>
                <span className="text-white">      </span>
                <span className="text-blue-400">className</span>
                <span className="text-white">={cn(</span>
                <span className="text-green-400">"px-4 py-2 rounded-lg"</span>
                <span className="text-white">, className)}</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">14</span>
                <span className="text-white">      </span>
                <span className="text-blue-400">onClick</span>
                <span className="text-white">={onClick}</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">15</span>
                <span className="text-white">    &gt;</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">16</span>
                <span className="text-white">      {children}</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">17</span>
                <span className="text-white">    &lt;/</span>
                <span className="text-red-400">button</span>
                <span className="text-white">&gt;</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">18</span>
                <span className="text-white">  );</span>
              </div>
              <div className="flex">
                <span className="w-8 text-gray-500 text-right mr-4">19</span>
                <span className="text-white">}</span>
              </div>
            </div>
          </div>

          {/* Terminal */}
          <div className="h-48 bg-black border-t border-gray-700">
            <div className="flex items-center gap-2 px-4 py-2 bg-gray-800 border-b border-gray-700">
              <Terminal className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-medium">Terminal</span>
            </div>
            <div className="p-4 font-mono text-sm">
              <div className="text-green-400">$ npm run dev</div>
              <div className="text-gray-300">Starting development server...</div>
              <div className="text-gray-300">✓ Ready on http://localhost:3000</div>
              <div className="flex items-center">
                <span className="text-green-400">$ </span>
                <span className="bg-gray-700 w-2 h-4 ml-1 animate-pulse"></span>
              </div>
            </div>
          </div>
        </div>

        {/* AI Assistant Panel */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 p-4">
          <div className="flex items-center gap-2 mb-4">
            <Zap className="w-5 h-5 text-yellow-400" />
            <h3 className="font-semibold">AI Assistant</h3>
          </div>
          <div className="space-y-3">
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Suggestion:</div>
              <div className="text-sm">Add TypeScript interface for better type safety</div>
            </div>
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Code Review:</div>
              <div className="text-sm">Consider adding error handling for onClick</div>
            </div>
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Optimization:</div>
              <div className="text-sm">Use React.memo for performance</div>
            </div>
          </div>
          
          <div className="mt-6">
            <input 
              type="text" 
              placeholder="Ask AI about your code..." 
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400"
            />
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800 border-t border-gray-700 text-sm text-gray-400">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <GitBranch className="w-4 h-4" />
            <span>main</span>
          </div>
          <div>TypeScript</div>
        </div>
        <div className="flex items-center gap-4">
          <div>Ln 10, Col 25</div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Connected to vCode</span>
          </div>
        </div>
      </div>
    </div>
  );
}
