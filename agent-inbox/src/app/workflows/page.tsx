"use client";

import React from 'react';
import { Workflow, Play, Save, Share2, Grid, Layers, Zap, ArrowRight } from 'lucide-react';

export default function WorkflowsPage() {
  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-indigo-50 to-blue-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white/80 backdrop-blur-sm border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-indigo-100 rounded-lg">
            <Workflow className="w-6 h-6 text-indigo-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Visual Workflows</h1>
            <p className="text-sm text-gray-600">Drag & drop workflow builder with 400+ components</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
            <Play className="w-4 h-4" />
            Run Workflow
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Save className="w-4 h-4" />
            Save
          </button>
          <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Share2 className="w-4 h-4" />
            Share
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Component Library */}
        <div className="w-80 bg-white/80 backdrop-blur-sm border-r p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Component Library</h3>
          
          <div className="space-y-4">
            {/* Categories */}
            {[
              { name: 'Input/Output', icon: Grid, count: 45, color: 'text-blue-600' },
              { name: 'Data Processing', icon: Layers, count: 78, color: 'text-green-600' },
              { name: 'AI/ML', icon: Zap, count: 92, color: 'text-purple-600' },
              { name: 'Integrations', icon: ArrowRight, count: 156, color: 'text-orange-600' },
            ].map((category, idx) => (
              <div key={idx} className="p-3 border rounded-lg hover:bg-indigo-50 cursor-pointer transition-colors">
                <div className="flex items-center gap-3">
                  <category.icon className={`w-5 h-5 ${category.color}`} />
                  <div className="flex-1">
                    <div className="font-medium text-sm">{category.name}</div>
                    <div className="text-xs text-gray-500">{category.count} components</div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Recent Components */}
          <div className="mt-6">
            <h4 className="font-medium text-gray-900 mb-3">Recently Used</h4>
            <div className="space-y-2">
              {[
                { name: 'HTTP Request', type: 'Integration' },
                { name: 'Text Processor', type: 'Data' },
                { name: 'OpenAI Chat', type: 'AI/ML' },
              ].map((comp, idx) => (
                <div key={idx} className="p-2 bg-gray-50 rounded text-sm hover:bg-indigo-50 cursor-pointer transition-colors">
                  <div className="font-medium">{comp.name}</div>
                  <div className="text-xs text-gray-500">{comp.type}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Workflow Canvas */}
        <div className="flex-1 flex flex-col">
          {/* Canvas Toolbar */}
          <div className="flex items-center gap-2 p-4 bg-white/80 backdrop-blur-sm border-b">
            <button className="p-2 rounded-lg border hover:bg-gray-50">
              <Play className="w-4 h-4 text-green-600" />
            </button>
            <button className="p-2 rounded-lg border hover:bg-gray-50">
              <Save className="w-4 h-4 text-blue-600" />
            </button>
            <div className="flex-1" />
            <div className="text-sm text-gray-600">Zoom: 100%</div>
          </div>

          {/* Canvas */}
          <div className="flex-1 bg-gray-50 relative overflow-hidden">
            {/* Grid Background */}
            <div className="absolute inset-0 opacity-20" style={{
              backgroundImage: `
                linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }} />
            
            {/* Canvas Content */}
            <div className="relative h-full flex items-center justify-center">
              <div className="text-center">
                <div className="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Workflow className="w-10 h-10 text-indigo-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Visual Workflow Builder</h3>
                <p className="text-gray-600 mb-6 max-w-md">
                  Drag and drop components to create powerful workflows. Connect data sources, 
                  AI models, and integrations with visual flow programming.
                </p>
                <div className="flex gap-3 justify-center">
                  <button className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                    Start Building
                  </button>
                  <button className="px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Browse Templates
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Properties Panel */}
        <div className="w-64 bg-white/80 backdrop-blur-sm border-l p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Properties</h3>
          
          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Workflow Info</div>
              <div className="space-y-2">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Name</label>
                  <input type="text" className="w-full px-2 py-1 border rounded text-sm" placeholder="My Workflow" />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Description</label>
                  <textarea className="w-full px-2 py-1 border rounded text-sm h-16" placeholder="Workflow description..."></textarea>
                </div>
              </div>
            </div>

            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Execution</div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Status</span>
                  <span className="text-gray-500">Ready</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Components</span>
                  <span className="text-gray-500">0</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Connections</span>
                  <span className="text-gray-500">0</span>
                </div>
              </div>
            </div>

            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Settings</div>
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm">
                  <input type="checkbox" className="rounded" />
                  <span>Auto-save</span>
                </label>
                <label className="flex items-center gap-2 text-sm">
                  <input type="checkbox" className="rounded" />
                  <span>Debug mode</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-6 py-2 bg-white/80 backdrop-blur-sm border-t text-sm text-gray-600">
        <div>Workflow Builder v3.0</div>
        <div className="flex items-center gap-4">
          <div>Components: 0</div>
          <div>Connections: 0</div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Connected to Langflow</span>
          </div>
        </div>
      </div>
    </div>
  );
}
