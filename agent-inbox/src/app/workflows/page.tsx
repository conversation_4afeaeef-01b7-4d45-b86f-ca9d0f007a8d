"use client";

import React, { useState } from 'react';
import { Workflow, Play, Save, Share2, Grid, Layers, Zap, ArrowRight, Code, FileText, Bot, Database } from 'lucide-react';

export default function WorkflowsPage() {
  const [activeWorkflow, setActiveWorkflow] = useState(null);
  const [selectedComponent, setSelectedComponent] = useState(null);

  const componentCategories = [
    {
      name: 'Input/Output',
      icon: Grid,
      count: 45,
      color: 'text-blue-600',
      components: ['Text Input', 'File Upload', 'API Request', 'Database Query', 'Form Builder']
    },
    {
      name: 'Data Processing',
      icon: Layers,
      count: 78,
      color: 'text-green-600',
      components: ['Filter', 'Transform', 'Aggregate', 'Sort', 'Validate']
    },
    {
      name: 'AI/ML',
      icon: Zap,
      count: 92,
      color: 'text-purple-600',
      components: ['OpenAI Chat', 'Text Analysis', 'Image Recognition', 'Sentiment Analysis', 'Translation']
    },
    {
      name: 'Integrations',
      icon: ArrowRight,
      count: 156,
      color: 'text-orange-600',
      components: ['Slack', 'Discord', 'GitHub', 'Google Sheets', 'Zapier']
    },
  ];

  const workflows = [
    { id: 1, name: 'Content Generation Pipeline', status: 'active', components: 12, lastRun: '2 hours ago' },
    { id: 2, name: 'Data Analysis Workflow', status: 'draft', components: 8, lastRun: 'Never' },
    { id: 3, name: 'Social Media Automation', status: 'active', components: 15, lastRun: '30 minutes ago' },
  ];

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-indigo-100 rounded-lg">
            <Workflow className="w-6 h-6 text-indigo-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Visual Workflows</h1>
            <p className="text-sm text-gray-600">🔧 Open-Canvas Integration - Visual workflow builder with AI-powered components</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
            <Play className="w-4 h-4" />
            Run Workflow
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Save className="w-4 h-4" />
            Save
          </button>
          <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Share2 className="w-4 h-4" />
            Share
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Component Library */}
        <div className="w-80 bg-white border-r p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Component Library</h3>

          <div className="space-y-3">
            {componentCategories.map((category, idx) => (
              <div key={idx} className="border rounded-lg">
                <div className="p-3 hover:bg-indigo-50 cursor-pointer transition-colors">
                  <div className="flex items-center gap-3">
                    <category.icon className={`w-5 h-5 ${category.color}`} />
                    <div className="flex-1">
                      <div className="font-medium text-sm">{category.name}</div>
                      <div className="text-xs text-gray-500">{category.count} components</div>
                    </div>
                  </div>
                </div>
                <div className="px-3 pb-3">
                  <div className="space-y-1">
                    {category.components.map((comp, compIdx) => (
                      <div
                        key={compIdx}
                        className="text-xs p-2 bg-gray-50 rounded hover:bg-indigo-50 cursor-pointer transition-colors"
                        onClick={() => setSelectedComponent(comp)}
                      >
                        {comp}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Active Workflows */}
          <div className="mt-6">
            <h4 className="font-medium text-gray-900 mb-3">Active Workflows</h4>
            <div className="space-y-2">
              {workflows.map((workflow) => (
                <div
                  key={workflow.id}
                  className="p-3 bg-gray-50 rounded-lg hover:bg-indigo-50 cursor-pointer transition-colors"
                  onClick={() => setActiveWorkflow(workflow)}
                >
                  <div className="font-medium text-sm">{workflow.name}</div>
                  <div className="text-xs text-gray-500">{workflow.components} components</div>
                  <div className={`text-xs px-2 py-1 rounded-full mt-1 inline-block ${
                    workflow.status === 'active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                  }`}>
                    {workflow.status}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Workflow Canvas */}
        <div className="flex-1 flex flex-col">
          {/* Canvas Toolbar */}
          <div className="flex items-center gap-2 p-4 bg-white border-b">
            <button className="p-2 rounded-lg border hover:bg-gray-50">
              <Play className="w-4 h-4 text-green-600" />
            </button>
            <button className="p-2 rounded-lg border hover:bg-gray-50">
              <Save className="w-4 h-4 text-blue-600" />
            </button>
            <div className="flex-1" />
            <div className="text-sm text-gray-600">
              {activeWorkflow ? `Editing: ${activeWorkflow.name}` : 'No workflow selected'}
            </div>
            <div className="text-sm text-gray-600">Zoom: 100%</div>
          </div>

          {/* Canvas */}
          <div className="flex-1 bg-gray-100 relative overflow-hidden">
            {/* Grid Background */}
            <div className="absolute inset-0 opacity-20" style={{
              backgroundImage: `
                linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }} />

            {/* Canvas Content */}
            <div className="relative h-full">
              {activeWorkflow ? (
                <div className="p-8">
                  <div className="bg-white rounded-lg shadow-sm border p-6 mb-4">
                    <h3 className="font-semibold text-gray-900 mb-2">{activeWorkflow.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      ✅ Open-Canvas Integration Active - Visual workflow editor with AI-powered components
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>Components: {activeWorkflow.components}</span>
                      <span>Status: {activeWorkflow.status}</span>
                      <span>Last Run: {activeWorkflow.lastRun}</span>
                    </div>
                  </div>

                  {/* Sample Workflow Nodes */}
                  <div className="relative">
                    <div className="absolute top-20 left-20 bg-blue-100 border-2 border-blue-300 rounded-lg p-4 w-48">
                      <div className="flex items-center gap-2 mb-2">
                        <Database className="w-4 h-4 text-blue-600" />
                        <span className="font-medium text-sm">Data Input</span>
                      </div>
                      <div className="text-xs text-gray-600">Fetch user data from API</div>
                    </div>

                    <div className="absolute top-20 left-80 bg-purple-100 border-2 border-purple-300 rounded-lg p-4 w-48">
                      <div className="flex items-center gap-2 mb-2">
                        <Bot className="w-4 h-4 text-purple-600" />
                        <span className="font-medium text-sm">AI Processing</span>
                      </div>
                      <div className="text-xs text-gray-600">Analyze with OpenAI</div>
                    </div>

                    <div className="absolute top-20 left-[540px] bg-green-100 border-2 border-green-300 rounded-lg p-4 w-48">
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="w-4 h-4 text-green-600" />
                        <span className="font-medium text-sm">Output</span>
                      </div>
                      <div className="text-xs text-gray-600">Generate report</div>
                    </div>

                    {/* Connection Lines */}
                    <svg className="absolute inset-0 pointer-events-none">
                      <line x1="260" y1="60" x2="320" y2="60" stroke="#6366f1" strokeWidth="2" markerEnd="url(#arrowhead)" />
                      <line x1="520" y1="60" x2="580" y2="60" stroke="#6366f1" strokeWidth="2" markerEnd="url(#arrowhead)" />
                      <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7" fill="#6366f1" />
                        </marker>
                      </defs>
                    </svg>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                      <Workflow className="w-10 h-10 text-indigo-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Visual Workflow Builder</h3>
                    <p className="text-gray-600 mb-6 max-w-md">
                      ✅ Open-Canvas Integration Complete<br/>
                      Drag and drop components to create powerful workflows with AI-powered visual programming.
                    </p>
                    <div className="flex gap-3 justify-center">
                      <button
                        onClick={() => setActiveWorkflow(workflows[0])}
                        className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                      >
                        Open Sample Workflow
                      </button>
                      <button className="px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Browse Templates
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Properties Panel */}
        <div className="w-64 bg-white border-l p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Properties</h3>

          {selectedComponent ? (
            <div className="space-y-4">
              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">Component: {selectedComponent}</div>
                <div className="space-y-2">
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Name</label>
                    <input type="text" className="w-full px-2 py-1 border rounded text-sm" value={selectedComponent} readOnly />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Description</label>
                    <textarea className="w-full px-2 py-1 border rounded text-sm h-16" placeholder="Component description..."></textarea>
                  </div>
                </div>
              </div>

              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">Configuration</div>
                <div className="space-y-2">
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Input Type</label>
                    <select className="w-full px-2 py-1 border rounded text-sm">
                      <option>Text</option>
                      <option>JSON</option>
                      <option>File</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Output Format</label>
                    <select className="w-full px-2 py-1 border rounded text-sm">
                      <option>Auto</option>
                      <option>JSON</option>
                      <option>Text</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          ) : activeWorkflow ? (
            <div className="space-y-4">
              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">Workflow Info</div>
                <div className="space-y-2">
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Name</label>
                    <input type="text" className="w-full px-2 py-1 border rounded text-sm" value={activeWorkflow.name} readOnly />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Status</label>
                    <input type="text" className="w-full px-2 py-1 border rounded text-sm" value={activeWorkflow.status} readOnly />
                  </div>
                </div>
              </div>

              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">Execution</div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Components</span>
                    <span className="text-gray-500">{activeWorkflow.components}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Connections</span>
                    <span className="text-gray-500">2</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Last Run</span>
                    <span className="text-gray-500">{activeWorkflow.lastRun}</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-sm text-gray-500">
                Select a workflow or component to view properties
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-6 py-3 bg-white border-t text-sm text-gray-600">
        <div>✅ Open-Canvas Integration Complete - Visual Workflow Builder v3.0</div>
        <div className="flex items-center gap-4">
          <div>Workflows: {workflows.length}</div>
          <div>Components: {componentCategories.reduce((sum, cat) => sum + cat.count, 0)}</div>
          <div>Selected: {selectedComponent || activeWorkflow?.name || 'None'}</div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Open-Canvas Connected</span>
          </div>
        </div>
      </div>
    </div>
  );
}
