"use client";

import React from 'react';
import { Share2, Calendar, BarChart3, Setting<PERSON>, Plus, Twitter, Facebook, Instagram, Linkedin } from 'lucide-react';

export default function SocialMediaPage() {
  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-pink-50 to-rose-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white/80 backdrop-blur-sm border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-pink-100 rounded-lg">
            <Share2 className="w-6 h-6 text-pink-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Social Media Hub</h1>
            <p className="text-sm text-gray-600">Multi-platform social media management & analytics</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors">
            <Plus className="w-4 h-4" />
            Create Post
          </button>
          <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Calendar className="w-4 h-4" />
            Schedule
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Platform Panel */}
        <div className="w-80 bg-white/80 backdrop-blur-sm border-r p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Connected Platforms</h3>
          
          <div className="space-y-3">
            {[
              { name: 'Twitter', icon: Twitter, connected: true, followers: '12.5K', color: 'text-blue-500' },
              { name: 'Facebook', icon: Facebook, connected: true, followers: '8.2K', color: 'text-blue-600' },
              { name: 'Instagram', icon: Instagram, connected: false, followers: '0', color: 'text-pink-500' },
              { name: 'LinkedIn', icon: Linkedin, connected: true, followers: '3.1K', color: 'text-blue-700' },
            ].map((platform, idx) => (
              <div key={idx} className="p-3 border rounded-lg hover:bg-pink-50 cursor-pointer transition-colors">
                <div className="flex items-center gap-3">
                  <platform.icon className={`w-5 h-5 ${platform.color}`} />
                  <div className="flex-1">
                    <div className="font-medium text-sm">{platform.name}</div>
                    <div className="text-xs text-gray-500">
                      {platform.connected ? `${platform.followers} followers` : 'Not connected'}
                    </div>
                  </div>
                  <div className={`w-2 h-2 rounded-full ${
                    platform.connected ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                </div>
              </div>
            ))}
          </div>

          <button className="w-full mt-4 p-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-pink-300 hover:bg-pink-50 transition-colors text-sm text-gray-600">
            + Connect Platform
          </button>

          {/* Quick Stats */}
          <div className="mt-6 p-4 bg-pink-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3">Today's Stats</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Posts Published</span>
                <span className="font-medium">3</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Total Reach</span>
                <span className="font-medium">2.4K</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Engagement</span>
                <span className="font-medium">156</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Dashboard */}
        <div className="flex-1 flex flex-col">
          {/* Dashboard Tabs */}
          <div className="flex items-center gap-4 p-4 bg-white/80 backdrop-blur-sm border-b">
            <button className="px-4 py-2 bg-pink-100 text-pink-700 rounded-lg font-medium">
              Dashboard
            </button>
            <button className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
              Content Calendar
            </button>
            <button className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
              Analytics
            </button>
            <div className="flex-1" />
            <div className="text-sm text-gray-600">Last updated: 2 min ago</div>
          </div>

          {/* Dashboard Content */}
          <div className="flex-1 p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Recent Posts */}
              <div className="bg-white/80 backdrop-blur-sm rounded-lg border p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Recent Posts</h3>
                <div className="space-y-4">
                  {[
                    { platform: 'Twitter', content: 'Just launched our new AI feature! 🚀', time: '2h ago', engagement: '24 likes, 5 retweets' },
                    { platform: 'Facebook', content: 'Behind the scenes of our development process...', time: '4h ago', engagement: '12 likes, 3 comments' },
                    { platform: 'LinkedIn', content: 'Thoughts on the future of AI in productivity...', time: '1d ago', engagement: '45 likes, 8 comments' },
                  ].map((post, idx) => (
                    <div key={idx} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium text-sm">{post.platform}</div>
                        <div className="text-xs text-gray-500">{post.time}</div>
                      </div>
                      <div className="text-sm text-gray-700 mb-2">{post.content}</div>
                      <div className="text-xs text-gray-500">{post.engagement}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Analytics Overview */}
              <div className="bg-white/80 backdrop-blur-sm rounded-lg border p-6">
                <div className="flex items-center gap-2 mb-4">
                  <BarChart3 className="w-5 h-5 text-pink-600" />
                  <h3 className="font-semibold text-gray-900">Analytics Overview</h3>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Followers</span>
                    <span className="font-semibold">23.8K</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">This Week's Reach</span>
                    <span className="font-semibold text-green-600">+15.2K</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Engagement Rate</span>
                    <span className="font-semibold text-blue-600">4.7%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Best Performing</span>
                    <span className="font-semibold">Twitter</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Scheduled Posts */}
            <div className="bg-white/80 backdrop-blur-sm rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">Scheduled Posts</h3>
                <button className="text-sm text-pink-600 hover:text-pink-700">View All</button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { time: 'Today 3:00 PM', platform: 'Twitter', content: 'Weekly product update thread...' },
                  { time: 'Tomorrow 9:00 AM', platform: 'LinkedIn', content: 'Industry insights article...' },
                  { time: 'Tomorrow 2:00 PM', platform: 'Facebook', content: 'Community spotlight post...' },
                ].map((post, idx) => (
                  <div key={idx} className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-500 mb-1">{post.time}</div>
                    <div className="font-medium text-sm mb-1">{post.platform}</div>
                    <div className="text-sm text-gray-700">{post.content}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Tools Panel */}
        <div className="w-64 bg-white/80 backdrop-blur-sm border-l p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Tools</h3>
          
          <div className="space-y-3">
            <button className="w-full p-3 bg-pink-100 text-pink-700 rounded-lg hover:bg-pink-200 transition-colors text-sm font-medium">
              AI Content Generator
            </button>
            <button className="w-full p-3 border rounded-lg hover:bg-gray-50 transition-colors text-sm">
              Hashtag Suggestions
            </button>
            <button className="w-full p-3 border rounded-lg hover:bg-gray-50 transition-colors text-sm">
              Best Time to Post
            </button>
            <button className="w-full p-3 border rounded-lg hover:bg-gray-50 transition-colors text-sm">
              Competitor Analysis
            </button>
          </div>

          <div className="mt-6">
            <h4 className="font-medium text-gray-900 mb-3">Quick Actions</h4>
            <div className="space-y-2">
              <button className="w-full p-2 text-left text-sm hover:bg-gray-50 rounded">
                Bulk Upload Images
              </button>
              <button className="w-full p-2 text-left text-sm hover:bg-gray-50 rounded">
                Export Analytics
              </button>
              <button className="w-full p-2 text-left text-sm hover:bg-gray-50 rounded">
                Team Collaboration
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-6 py-2 bg-white/80 backdrop-blur-sm border-t text-sm text-gray-600">
        <div>Social Media Hub v2.1</div>
        <div className="flex items-center gap-4">
          <div>Platforms: 3 connected</div>
          <div>Scheduled: 12 posts</div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>All platforms synced</span>
          </div>
        </div>
      </div>
    </div>
  );
}
