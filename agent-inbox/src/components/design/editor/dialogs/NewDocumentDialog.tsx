"use client";

import React, { useState } from 'react';
import { X, FileText, Image, Smartphone, Monitor } from 'lucide-react';

interface NewDocumentDialogProps {
  onClose: () => void;
}

const DOCUMENT_PRESETS = [
  { id: 'web', name: 'Web', width: 1920, height: 1080, icon: Monitor },
  { id: 'mobile', name: 'Mobile', width: 375, height: 812, icon: Smartphone },
  { id: 'photo', name: 'Photo', width: 3000, height: 2000, icon: Image },
  { id: 'document', name: 'Document', width: 2550, height: 3300, icon: FileText },
];

export function NewDocumentDialog({ onClose }: NewDocumentDialogProps) {
  const [selectedPreset, setSelectedPreset] = useState('web');
  const [customWidth, setCustomWidth] = useState(1920);
  const [customHeight, setCustomHeight] = useState(1080);

  const handleCreate = () => {
    const preset = DOCUMENT_PRESETS.find(p => p.id === selectedPreset);
    if (preset) {
      // Here you would create a new document with the selected dimensions
      console.log('Creating document:', preset);
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-semibold">New Document</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Document Type
              </label>
              <div className="grid grid-cols-2 gap-2">
                {DOCUMENT_PRESETS.map((preset) => {
                  const IconComponent = preset.icon;
                  return (
                    <button
                      key={preset.id}
                      onClick={() => setSelectedPreset(preset.id)}
                      className={`p-3 border rounded-lg flex flex-col items-center gap-2 transition-colors ${
                        selectedPreset === preset.id
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <IconComponent className="w-6 h-6 text-gray-600" />
                      <span className="text-sm font-medium">{preset.name}</span>
                      <span className="text-xs text-gray-500">
                        {preset.width} × {preset.height}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Width
                </label>
                <input
                  type="number"
                  value={customWidth}
                  onChange={(e) => setCustomWidth(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Height
                </label>
                <input
                  type="number"
                  value={customHeight}
                  onChange={(e) => setCustomHeight(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleCreate}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Create Document
          </button>
        </div>
      </div>
    </div>
  );
}
